<?php

use App\Enums\AccessExternalApiType;
use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\CloudMockupApis;
use App\Enums\ColorSpaceEnum;
use App\Enums\DateRangeEnum;
use App\Enums\EnvironmentEnum;
use App\Enums\HomeListingEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\PaymentMethodEnum;
use App\Enums\ProductOptionEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\SellerTeamStatusEnum;
use App\Enums\ShippingMethodEnum;
use App\Enums\StorageDisksEnum;
use App\Enums\StoreDomainStatusEnum;
use App\Enums\StoreStatusEnum;
use App\Enums\UserRoleEnum;
use App\Http\Controllers\Admin\SyncController;
use App\Http\Controllers\ApiKeyController;
use App\Http\Requests\ApiKey\GenerateRequest;
use App\Library\SPHash;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\SellerTeam;
use App\Models\Sharding;
use App\Models\Store;
use App\Models\StoreDomain;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Models\TempEventLog;
use App\Models\User;
use App\Services\SenPrintsAuth;
use App\Services\StoreService;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use DateTime as DT;
use DateTimeZone as DTZ;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;
use NumberFormatter as NumFormat;
use Tests\Feature\Checkout\OrderTest;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Facades\JWTFactory;

if (!function_exists('str_starts_with')) {
    function str_starts_with($haystack, $needles): bool
    {
        return Str::startsWith($haystack, $needles);
    }
}

if (!function_exists('get_auth_guards')) {
    function get_auth_guards(): array
    {
        $configAuth = config('auth.guards');
        return array_keys($configAuth);
    }
}

if (!function_exists('check_access_auth_guard')) {
    function check_access_auth_guard($guardName): bool
    {
        return in_array($guardName, get_auth_guards(), true);
    }
}

if (!function_exists('get_app_services')) {
    function get_app_services()
    {
        return config('app.services');
    }
}

if (!function_exists('check_access_app_service')) {
    function check_access_app_service($serviceName): bool
    {
        return in_array($serviceName, get_app_services(), true);
    }
}

if (!function_exists('upload_file_aws')) {
    function upload_file_aws($file, string $path): array
    {
        $url = Storage::disk(StorageDisksEnum::DEFAULT)->put($path, $file, 'public');

        return [
            'file_url' => $url,
            'file_name' => $file->getClientOriginalName(),
            'file_size' => $file->getSize(),
        ];
    }
}

if (!function_exists('delete_s3_files')) {
    function delete_s3_files(array $files)
    {
        if (count($files) > 0) {
            $store = Storage::disk(StorageDisksEnum::DEFAULT);

            foreach ($files as $file) {
                $store->delete($file['file_url']);
            }
        }
    }
}

if (!function_exists('get_payload_current_token')) {
    function get_payload_current_token()
    {
        return JWTAuth::parseToken()->getPayload();
    }
}

if (!function_exists('jwtauth_encode')) {
    function jwtauth_encode($payload): string
    {
        return (string)JWTAuth::encode($payload);
    }
}

if (!function_exists('currentUser')) {
    function currentUser($userId = null, $payload = []): SenPrintsAuth
    {
        $instance = SenPrintsAuth::instance($userId);
        if ($instance && !empty($payload)) {
            $instance::setRequestData($payload, request());
        }
        return $instance;
    }
}

if (!function_exists('time_utc_offset')) {
    /**
     * @param int $offset
     * @return int
     */
    function time_utc_offset($offset = 0)
    {
        return $offset * 3600;
    }
}

if (!function_exists('convert_time_by_offset')) {
    /**
     * @param string $oldTime
     * @param int $offset
     * @param false $tzId
     * @param string $dateFormat
     * @return string
     * @throws DateInvalidTimeZoneException
     */
    function convert_time_by_offset(string $oldTime, $offset = 0, $tzId = false, $dateFormat = 'Y-m-d H:i:s'): string
    {
        $userTimezone = get_timezone_name_with_offset($offset, $tzId);
        $dt = new DT();
        $dt->setTimestamp(strtotime($oldTime));
        $dt->setTimezone(new DTZ($userTimezone));
        return $dt->format($dateFormat);
    }
}

if (!function_exists('round_timestamp')) {
    /**
     * @param string $timeNow
     * @return int
     */
    function round_timestamp(string $timeNow): int
    {
        $timeNowToTime = strtotime($timeNow);
        return (int)(floor($timeNowToTime / 3600) * 3600);
    }
}

if (!function_exists('get_utc_offset_by_user_or_store')) {
    /**
     * @param int|null $sellerId
     * @param int|null $storeId
     * @return int|null
     */
    function get_utc_offset_by_user_or_store(int $sellerId = null, int $storeId = null)
    {
        if ($sellerId === 0 && $storeId === 0) {
            return 0;
        }

        $tz = null;

        // if (!is_null($storeId) && $storeId != 1) {
        //     $tz = Store::get_utc_offset($storeId);
        // }

        if (is_null($tz) && !is_null($sellerId)) {
            $tz = User::get_utc_offset($sellerId);
        }

        $tzOffset = 0;

        if (!is_null($tz)) {
            $tzOffset = $tz[1];
        }

        return $tzOffset;
    }
}

if (!function_exists('get_list_product_for_stats_order')) {

    /**
     * @param $objectBuilder
     * @return array[]
     */
    function get_list_product_for_stats_order($objectBuilder): array
    {
        if (($objectBuilder instanceof Collection) === false) {
            $objectBuilder = [$objectBuilder];
        }

        $orderProducts = [];
        $orderIds = [];
        $orderWithProductIds = [];
        $productIds = [];

        foreach ($objectBuilder as $orderIndex => $order) {
            $timezoneOffset = get_utc_offset_by_user_or_store($order->seller_id, $order->store_id);
            $dateStamp = $order->paid_at->addRealHours($timezoneOffset)->startOfDay();

            $products = !is_null($order->products) ? $order->products : false;

            if (!$products) {
                continue;
            }

            foreach ($products as $orderProductIndex => $orderProduct) {
                $productIds[] = $orderProduct->product_id;
                $orderProducts[] = [
                    'seller_id' => $order->seller_id,
                    'ref_id' => $order->ref_id,
                    'store_id' => $order->store_id,
                    'campaign_id' => $orderProduct->campaign_id,
                    'template_id' => $orderProduct->template_id,
                    'order_id' => $order->id,
                    'product_id' => $orderProduct->product_id,
                    'items' => $orderProduct->quantity,
                    'sales' => $orderProduct->total_amount,
                    'seller_profit' => $orderProduct->seller_profit,
                    'country' => $order->country,
                    'device' => $order->device,
                    'device_detail' => $order->device_detail,
                    'ad_source' => $order->ad_source ?? 'N/A',
                    'ad_campaign' => $order->ad_campaign ?? 'N/A',
                    'ad_medium' => $order->ad_medium ?? 'N/A',
                    'timestamp' => $order->paid_at->toDateTimeString(),
                    'datestamp' => $dateStamp,
                    'timezone' => $timezoneOffset,
                ];

                if (!in_array($order->id, $orderIds)) {
                    $orderIds[] = $order->id;
                }

                if (isset($orderWithProductIds[$order->id], $orderWithProductIds[$order->id]['product_id'])) {
                    if (!in_array($orderProduct->id, $orderWithProductIds[$order->id])) {
                        $orderWithProductIds[$order->id]['product_id'][] = $orderProduct->product_id;
                        $orderWithProductIds[$order->id]['template_id'][] = $orderProduct->template_id;
                        $orderWithProductIds[$order->id]['campaign_id'][] = $orderProduct->campaign_id;
                    }
                } else {
                    $orderWithProductIds[$order->id] = [
                        'product_id' => [$orderProduct->product_id],
                        'template_id' => [$orderProduct->template_id],
                        'campaign_id' => [$orderProduct->campaign_id],
                        'customer_id' => !is_null($order->customer_id) ? $order->customer_id : 0,
                        'seller_id' => !is_null($order->seller_id) ? $order->seller_id : 0,
                        'store_id' => !is_null($order->store_id) ? $order->store_id : 0,
                        'paid_at' => $order->paid_at->toDateTimeString()
                    ];
                }
                unset($products[$orderProductIndex]);
            }
            unset($objectBuilder[$orderIndex]);
        }
        return [$orderProducts, $orderIds, $orderWithProductIds, $productIds];
    }
}

if (!function_exists('bought_together_product_ids')) {
    /**
     * @param array $productIds
     * @return array
     */
    function bought_together_product_ids(array $productIds = []): array
    {
        $result = [];

        foreach ($productIds as $index => $item) {
            $temp = $productIds;
            unset($temp[$index]); // (remove current item from array)

            // prevent null to collection => empty
            if ($item === null) {
                $item = [null];
            }
            $res = collect($item)->crossJoin($temp)->toArray();
            $result = array_merge($result, $res);
        }

        return $result;
    }
}

if (!function_exists('getQuery')) {
    /**
     * @param Builder $builder
     * @return string
     */
    function getQuery(Builder $builder): string
    {
        $addSlashes = str_replace('?', "'?'", $builder->toSql());
        return vsprintf(str_replace('?', '%s', $addSlashes), $builder->getBindings());
    }
}

if (!function_exists('get_timezone_name_with_offset')) {
    /**
     * @param int $offset
     * @param bool $timezoneId
     * @return bool|string
     */
    function get_timezone_name_with_offset($offset = 0, $timezoneId = false)
    {
        $gmtOffset = time_utc_offset($offset);
        $userTimezone = timezone_name_from_abbr("", $gmtOffset, 0);

        if (!$userTimezone) {
            $userTimezone = $timezoneId;

            if (!$userTimezone) {
                $userTimezone = timezone_name_from_abbr("", $gmtOffset, 1);
            }
        }

        return $userTimezone;
    }
}

if (!function_exists('fileTokenGenerator')) {
    function fileTokenGenerator(): string
    {
        $random = Str::orderedUuid();
        $date = microtime(true);
        return md5($random . '-senprint-' . $date);
    }
}

if (!function_exists('replaceArrayKey')) {
    function replaceArrayKey(array &$item, $oldKey, $newKey): void
    {
        $item[$newKey] = $item[$oldKey];
        unset($item[$oldKey]);
    }
}


if (!function_exists('getFullPath')) {
    function getFullPath($path): string
    {
        if (empty($path)) {
            return '';
        }

        if (strpos($path, '/') !== 0) {
            $path = '/' . $path;
        }

        return config('senprints.image_proxy_url') . $path;
    }
}

if (!function_exists('exportColumns')) {
    function exportColumns(Model $newCampaign, array $columns): array
    {
        $newArrayCampaign = [];
        $arrayCampaign = $newCampaign->toArray();

        foreach ($columns as $column) {
            $newArrayCampaign[$column] = $arrayCampaign[$column];
        }

        return $newArrayCampaign;
    }
}

if (!function_exists('sendEmailOld')) {
    function sendEmailOld(array $config): bool
    {
        try {
            // Sent success after create account
            $emailQueueBaseUrl = config('senprints.email_queue_base_url');
            $emailQueueApiKey = config('senprints.email_queue_api_key');
            $url = $emailQueueBaseUrl . '/api/email';
            $request = Http::withHeaders([
                'x-senprints-key' => $emailQueueApiKey
            ])->withoutVerifying();
            $config['from'] = getSenderEmail();
            $config['backup_email'] = getBackupEmail();
            $config['support_email'] = getSupportEmail();
            $config['unsubscribe_key'] = Crypt::encryptString($config['to']);
            $response = $request->put($url, $config);
            return $response->status() === 200;
        } catch (Exception $ex) {
            logException($ex);
            return false;
        }
    }
}

if (!function_exists('getSenderEmail')) {
    function getSenderEmail(): string
    {
        return SystemConfig::getConfig('sender_email', '<EMAIL>');
    }
}

if (!function_exists('getBackupEmail')) {
    function getBackupEmail(): string
    {
        return SystemConfig::getConfig('backup_email', '<EMAIL>');
    }
}

if (!function_exists('getSupportEmail')) {
    function getSupportEmail(): string
    {
        return SystemConfig::getConfig('support_email', '<EMAIL>');
    }
}

if (!function_exists('getSellerCampaignUrl')) {
    function getSellerCampaignUrl(int $campaignId): string
    {
        return config('senprints.base_url_seller') . 'campaigns/' . $campaignId;
    }
}

if (!function_exists('getSellerOrderDetailUrl')) {
    function getSellerOrderDetailUrl(int $orderId): string
    {
        return config('senprints.base_url_seller') . 'orders/detail/' . $orderId;
    }
}

if (!function_exists('getSellerResetPasswordUrl')) {
    function getSellerResetPasswordUrl(string $token): string
    {
        return config('senprints.base_url_seller') . "auth/reset-password?token={$token}";
    }
}

if (!function_exists('getSupplierResetPasswordUrl')) {
    function getSupplierResetPasswordUrl(string $token): string
    {
        return config('senprints.base_url_supplier') . "auth/reset-password?token={$token}";
    }
}

if (!function_exists('color2hex')) {
    function color2hex($colorName): string
    {
        $colors = StoreService::systemColors();

        if (count($colors) > 0) {
            foreach ($colors as $color) {
                if (
                    isset($color['name'], $color['hex_code'])
                    && strtolower($color['name']) === strtolower($colorName)
                ) {
                    return $color['hex_code'];
                }
            }
        }

        return '#ffffff';
    }
}

if (!function_exists('getLocationByCode')) {
    function getLocationByCode($countryCode): ?object
    {
        return SystemLocation::findByCountryCode($countryCode);
    }
}
if (!function_exists('getLocationByName')) {
    function getLocationByName($countryCode)
    {
        $locations = SystemLocation::systemLocations();

        if (count($locations) > 0) {
            foreach ($locations as $location) {
                if ($location->name === $countryCode) {
                    return $location;
                }
            }
        }

        return null;
    }
}

if (!function_exists('getProcessingFee')) {
    function getProcessingFee(): float
    {
        return (float)SystemConfig::getConfig('processing_fee', 5) / 100;
    }
}

if (!function_exists('getProcessingFeeMin')) {
    function getProcessingFeeMin(): float
    {
        return (float)SystemConfig::getConfig('processing_fee_min', 0.975);
    }
}

if (!function_exists('getTipShareRate')) {
    function getTipShareRate(): float
    {
        return (float)SystemConfig::getConfig('tip_share_rate', 50) / 100;
    }
}

if (!function_exists('getFulfillProcessingFee')) {
    function getFulfillProcessingFee(): float
    {
        return (float)SystemConfig::getConfig('fulfill_processing_fee', 3) / 100;
    }
}

if (!function_exists('getTransactionFee')) {
    function getTransactionFee(): float
    {
        return (float)SystemConfig::getConfig('transaction_fee', 2) / 100;
    }
}

if (!function_exists('getTransactionFeeMin')) {
    function getTransactionFeeMin(): float
    {
        return (float)SystemConfig::getConfig('transaction_fee_min', 0.3);
    }
}

if (!function_exists('getPaymentDiscountRate')) {
    function getPaymentDiscountRate(): float
    {
        return (float)SystemConfig::getConfig('payment_discount', 2) / 100;
    }
}

if (!function_exists('secondsToDays')) {
    function secondsToDays($seconds): float
    {
        return CarbonInterval::seconds($seconds)->totalDays;
    }
}

if (!function_exists('daysToSeconds')) {
    function daysToSeconds($days): float
    {
        return CarbonInterval::days($days)->totalSeconds;
    }
}

if (!function_exists('hoursToSeconds')) {
    function hoursToSeconds($hours): float
    {
        return CarbonInterval::hours($hours)->totalSeconds;
    }
}

if (!function_exists('mapProductForListing')) {
    function mapProductForListing($each)
    {
        if ($each->product_type === ProductType::PRODUCT && !empty($each->campaign_id)) {
            $each->campaign_name = $each->campaign->name;
            $each->slug = $each->campaign->slug;
        }

        unset($each->campaign);
        return $each;
    }
}

if (!function_exists('removeDuplicateInMultidimensionalArray')) {
    function removeDuplicateInMultidimensionalArray($array): array
    {
        return array_map('unserialize', array_unique(array_map('serialize', $array)));
    }
}

if (!function_exists('clearStoreCache')) {
    /**
     * @throws Exception
     */
    function clearStoreCache($storeIds = null)
    {
        try {
            $stores = Store::query()
                ->addFieldCache()
                ->where(
                    function ($query) use ($storeIds) {
                        if (empty($storeIds)) {
                            return $query->where('seller_id', currentUser()->getUserId());
                        }

                        if (is_array($storeIds)) {
                            return $query->whereIn('id', $storeIds);
                        }

                        return $query->where('id', $storeIds);
                    }
                )
                ->get();

            if ($stores->isEmpty()) {
                return;
            }

            $cacheKeys = [];
            $htmlCacheParams = [];

            foreach ($stores as $store) {
                if ($store->id) {
                    $cacheKeys['tags'][] = CacheKeys::getStoreId($store->id);
                }

                if (!empty($store->domain)) {
                    $cacheKeys[] = CacheKeys::getStoreDomainKey($store->domain);
                    $htmlCacheParams[] = [
                        "type" => "domain",
                        "value" => $store->domain
                    ];
                }

                $cacheKeys[] = CacheKeys::getStoreDomainKey($store->sub_domain);
                foreach (HomeListingEnum::asArray() as $type) {
                    $cacheKeys[] = CacheKeys::getHomeListingType($type, $store->id);
                }

                $htmlCacheParams[] = [
                    "type" => "domain",
                    "value" => $store->sub_domain . '.' . getStoreBaseDomain()
                ];
            }

            $storeDomains = StoreDomain::query()
                ->where([
                    'seller_id' => currentUser()->getUserId(),
                    'status' => StoreDomainStatusEnum::ACTIVATED
                ])
                ->when(!empty($storeIds), function ($query) use ($storeIds) {
                    if (is_array($storeIds)) {
                        return $query->whereIn('id', $storeIds);
                    }

                    $query->where('store_id', $storeIds);
                })
                ->pluck('domain');

            // clear cache additional domains
            foreach ($storeDomains as $domain) {
                $htmlCacheParams[] = [
                    'type' => 'domain',
                    'value' => $domain
                ];
            }

            syncClearCache($cacheKeys);
            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
            clearHtmlCache($htmlCacheParams);
        } catch (Throwable $ex) {
            logException($ex);
        }
    }
}

if (!function_exists('syncClearCache')) {
    function syncClearCache($cacheKeys, string $type = 'default'): void
    {
        if (empty($cacheKeys)) {
            return;
        }

        if (is_string($cacheKeys)) {
            $cacheKeys = [$cacheKeys];
        }
        graylogInfo('Sync clear cache', [
            'category' => 'sync_clear_cache',
            'type' => $type,
            'user_id' => optional(currentUser())->getUserId(),
            'cache_keys' => json_encode($cacheKeys),
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
        ]);
        $controller = new SyncController('clear_cache'); // name route
        $controller->postClearCache($cacheKeys, $type);
    }
}

if (!function_exists('clearCacheSeller')) {
    function clearCacheSeller($sellerId)
    {
        $stores = Store::query()
            ->select(['id', 'domain', 'sub_domain'])
            ->where('seller_id', $sellerId)
            ->whereIn('status', [StoreStatusEnum::ACTIVE, StoreStatusEnum::VERIFIED, StoreStatusEnum::BLOCKED])
            ->get();

        $cacheKeys = [];

        if ($stores->isNotEmpty()) {
            foreach ($stores as $store) {
                if (!empty($store->domain)) {
                    $cacheKeys[] = CacheKeys::getStoreDomainKey($store->domain);
                }
                $cacheKeys[] = CacheKeys::getStoreDomainKey($store->sub_domain);
            }
        }

        $cacheKeys[] = CacheKeys::SYSTEM_PAYMENT_GATEWAYS . $sellerId;

        syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
    }
}

if (!function_exists('clearHtmlCacheSeller')) {
    function clearHtmlCacheSeller($sellerId, $campaignIds = [])
    {
        $stores = Store::query()
            ->select(['domain', 'sub_domain', 'domain_status'])
            ->where('seller_id', $sellerId)
            ->whereIn('status', [StoreStatusEnum::ACTIVE, StoreStatusEnum::VERIFIED, StoreStatusEnum::BLOCKED])
            ->get();

        if ($stores->isEmpty()) {
            return;
        }

        $htmlCacheParams = [];
        $cacheKeys = [];
        $visitCacheLimit = 100;

        foreach ($stores as $store) {
            if ($store->id) {
                $cacheKeys['tags'][] = CacheKeys::getStoreId($store->id);
            }

            if (!empty($store->domain)) {
                $cacheKeys[] = CacheKeys::getStoreDomainKey($store->domain);
                $htmlCacheParams[] = [
                    "type" => "domain",
                    "value" => $store->domain
                ];
            }

            $cacheKeys[] = CacheKeys::getStoreDomainKey($store->sub_domain);
            foreach (HomeListingEnum::asArray() as $type) {
                $cacheKeys[] = CacheKeys::getHomeListingType($type, $store->id);
            }

            $visits24h = visitByStoreId($store->id);
            if ($visits24h < $visitCacheLimit) {
                $htmlCacheParams[] = [
                    "type" => "domain",
                    "value" => $store->sub_domain . '.' . getStoreBaseDomain()
                ];
            }
        }

        $storeDomains = StoreDomain::query()->select(['store_id', 'domain'])
            ->where([
                'seller_id' => $sellerId,
                'status' => StoreDomainStatusEnum::ACTIVATED
            ])->get();

        // clear cache additional domains
        foreach ($storeDomains as $storeDomain) {
            $visits24h = visitByStoreId($storeDomain->store_id);

            if ($visits24h < $visitCacheLimit) {
                $htmlCacheParams[] = [
                    'type' => 'domain',
                    'value' => $storeDomain->domain
                ];
            }
        }

        foreach ($campaignIds as $campaignId) {
            $htmlCacheParams[] = [
                'type' => 'tag',
                'name' => 'campaign',
                'value' => $campaignId
            ];
        }

        syncClearCache($cacheKeys);
        clearHtmlCache($htmlCacheParams);
    }
}

if (!function_exists('clearHtmlCache')) {
    /**
     * @param $params
     * @param $async
     * @return array|bool
     * @throws Throwable
     */
    function clearHtmlCache($params = [], $async = true): bool|array
    {
        $cacheDomain = config('senprints.html_cache_domain');
        $cacheToken = config('senprints.html_cache_token');

        if (!$cacheDomain || !$cacheToken) {
            if (!$async) {
                return [
                    'success' => false,
                    'message' => 'Cache domain or token not found'
                ];
            }
            return false;
        }

        $apiUrl = 'https://' . $cacheDomain . '/delete';
        $clearCacheFunction = function () use ($apiUrl, $cacheToken, $params, $async) {
            try {
                $res = Http::withToken($cacheToken)
                    ->withoutVerifying()
                    ->timeout(30)
                    ->post($apiUrl, $params);

                if ($res->failed() || !$res->json('success', false)) {
                    graylogInfo('Clear HTML cache failed', [
                        'category' => 'clear_cache_html',
                        'success_response' => $res->json('success'),
                        'result' => json_encode($res, JSON_THROW_ON_ERROR),
                        'params' => json_encode($params, JSON_THROW_ON_ERROR),
                    ]);
                    if (!$async) {
                        return $res->json();
                    }
                    return false;
                }
                if (!$async) {
                    return $res->json();
                }
                return true;
            } catch (ConnectionException $e) {
                graylogInfo("Clear HTML cache failed - ConnectionException" . $e->getMessage(), [
                    'category' => 'clear_cache_html',
                    'params' => json_encode($params, JSON_THROW_ON_ERROR),
                ]);
                if (!$async) {
                    return [
                        'success' => false,
                        'message' => $e->getMessage()
                    ];
                }
                return false;
            } catch (Exception $ex) {
                graylogInfo('Clear HTML cache failed - ' . $ex->getMessage(), [
                    'category' => 'clear_cache_html',
                    'params' => json_encode($params, JSON_THROW_ON_ERROR),
                ]);
                if (!$async) {
                    return [
                        'success' => false,
                        'message' => $ex->getMessage()
                    ];
                }
                return false;
            }
        };
        if ($async) {
            dispatch($clearCacheFunction)->onQueue('clear-cache');
            return true;
        }
        return $clearCacheFunction();
    }
}

if (!function_exists('clearHtmlCacheByTag')) {
    function clearHtmlCacheByTag($tagName, $value)
    {
        clearHtmlCache([[
            'type' => 'tag',
            'name' => $tagName,
            'value' => $value
        ]]);
    }
}

if (!function_exists('roundAmountPayment')) {
    function roundAmountPayment($amount, $numberPrecision = 2): float
    {
        return round($amount, $numberPrecision, PHP_ROUND_HALF_UP);
    }
}

if (!function_exists('createCustomAccessToken')) {
    /**
     * Create a custom access token with secret & ttl
     * @param int $subId
     * @param array $extendData
     * @param string $secret
     * @param null $ttl
     * @return string|null
     */
    function createCustomAccessToken($subId = 0, array $extendData = [], string $secret = '', $ttl = null)
    {
        if ($subId === 0) {
            return null;
        }

        if (empty($secret)) {
            $secret = config('senprints.external.jwt.secret');
        }

        if (is_null($ttl)) {
            $ttl = config('senprints.external.jwt.ttl');
        }

        $extendData = collect($extendData);

        // Set custom secret & ttl
        try {
            JWTAuth::getJWTProvider()->setSecret($secret);
            JWTAuth::factory()->setTTL($ttl);

            // Generate access token
            $payload = JWTFactory::sub($subId);

            if (!empty($extendData)) {
                $extendData->map(function ($value, $key) use (&$payload, &$extendData) {
                    $payload->{$key}($value);
                    unset($extendData[$key]);
                });
            }

            return (string)JWTAuth::encode($payload->make());
        } catch (Exception $e) {
            return null;
        }
    }
}
if (!function_exists('productOptionsParser')) {
    function productOptionsParser($optionsString): string
    {
        try {
            $arrayOptions = [];
            $options = json_decode($optionsString, true);
            if (empty($options)) {
                return '';
            }
            $keys = array_keys($options);

            foreach ($keys as $key) {
                $optionKey = preg_replace("/[^a-z0-9A-Z]+/", " ", $key);
                $optionValue = trim($options[$key]);
                if (!str_starts_with($optionValue, '__')) {
                    $arrayOptions[] = strtoupper($optionKey) . ": " . strtoupper($optionValue);
                }
            }

            return implode(' - ', $arrayOptions);
        } catch (Exception $ex) {
            return '';
        }
    }
}

if (!function_exists('getVariantKey')) {
    function getVariantKey($options, $useDefaultColor = false): string
    {
        if (!is_array($options)) {
            if (is_object($options)) {
                $options = (array)$options;
            } elseif (is_string($options)) {
                $options = json_decode($options, true);

                // validate output: https://stackoverflow.com/a/59849065
                if ($options === null) {
                    return '';
                }
            } else {
                return ''; // invalid input, just return empty variant key
            }
        }

        // replace default color for full printed product
        if (
            isset($options['color'])
            &&
            ($useDefaultColor
                ||
                $options['color'] === 'multicolors'
            )
        ) {
            $options['color'] = 'white';
        }

        $arr = [];
        array_map(
            static function ($each) use (&$arr) {
                if (!empty($each)) {
                    $arr[] = strtolower(
                        preg_replace(
                            [
                                '/\s+/',
                                '/-/',
                            ],
                            '_',
                            correctOptionValue($each)
                        )
                    );
                }
            },
            $options
        );
        // options: {"size": "S", "color": "White / Light blue "}
        // variant key: white/light_blue-s
        return !empty($arr) ? implode('-', $arr) : 'none';
    }
}

if (!function_exists('correctOptionValue')) {
    function correctOptionValue($optionValue)
    {
        if (empty($optionValue)) {
            return $optionValue;
        }
        return trim(
            str_replace(
                ' / ',
                '/',
                str_replace(
                    '__',
                    '',
                    $optionValue
                )
            )
        );
    }
}

if (!function_exists('getHourOffsetBySeller')) {
    function getHourOffsetBySeller($sellerId = null): float
    {
        // ignore: system
        if ($sellerId === -1) {
            return 0;
        }

        $user = currentUser();
        if ($sellerId && $user->getUserId() !== $sellerId) {
            $utcOffset = (float)User::query()
                ->where('id', $sellerId)
                ->value('utc_offset');
        }

        if ($user->isSeller()) {
            $utcOffset ??= (float)$user->getUTCOffset();
        }

        $utcOffset ??= 7;
        return $utcOffset;
    }
}

if (!function_exists('escapeSpecialCharacter')) {
    function escapeSpecialCharacter(?string $string, $keepCharacter = true): ?string
    {
        if ($keepCharacter) {
            $replacement = '\\\\$0';
        } else {
            $replacement = '';
        }
        return preg_replace(
            [
                '_[<>]+_',
                '_[-+=!(){}[\]^"\'~*?:\\/\\\\]|&(?=&)|\|(?=\|)_',
            ],
            [
                '',
                $replacement,
            ],
            $string
        );
    }
}

if (!function_exists('getSearchTextElastic')) {
    function getSearchTextElastic(string &$string): void
    {
        $string  = strip_tags($string);
        $string  = strtolower($string);
        $pattern = '/[^A-Za-z0-9 ,]/';
        $string  = preg_replace($pattern, ' ', $string);
    }
}

if (!function_exists('getTodayTimeZone')) {
    function getTodayTimeZone($sellerId = null, $exactHour = true): \Illuminate\Support\Carbon
    {
        $hourOffset = getHourOffsetBySeller($sellerId);
        // utc + hour = time user -> get day of user -> sub hour -> get start of day
        $today = now()->addRealHours($hourOffset)->startOfDay();
        if ($exactHour) {
            $today->subRealHours($hourOffset);
        }

        return $today;
    }
}

if (!function_exists('filterQueryByDateRange')) {
    function filterQueryByDateRange(array $dateRange, $query = null, $sellerId = null, $submonths = null)
    {

        // if don't filter by date range. ex: lifetime
        if (empty($dateRange['type'])) {
            return $query;
        }
        $hourOffset = getHourOffsetBySeller($sellerId);
        $today = now()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset);

        // method copy() to clone variable
        switch ($dateRange['type']) {
            case DateRangeEnum::LIFE_TIME:
                return $query;
            case DateRangeEnum::CUSTOM:
                try {
                    $dateRange['range'] = array_filter($dateRange['range'] ?? []);
                    if (count($dateRange['range']) !== 2) {
                        return $query;
                    }

                    $startDate = Carbon::parse($dateRange['range'][0]);
                    $endDate   = Carbon::parse($dateRange['range'][1])->endOfDay();

                    if (empty($dateRange['is_timezone'])) {
                        $startDate->startOfDay()->subRealHours($hourOffset);
                        $endDate->endOfDay()->subRealHours($hourOffset);
                    }
                    $startDate = !$submonths ? $startDate : $startDate->copy()->subMonths($submonths)->toDateTimeString();
                    $endDate = !$submonths ? $endDate : $startDate->copy()->toDateTimeString();

                    $time = [$startDate, $endDate];
                    if (is_null($query)) {
                        return $time;
                    }

                    return $query->whereBetween($dateRange['column'], $time);
                } catch (Throwable $e) {
                    $message = "Parse date range failed\nDate Range: " . json_encode($dateRange)
                        . "\nError: " . $e->getMessage();

                    // trace for debug
                    $data = debug_backtrace(DEBUG_BACKTRACE_PROVIDE_OBJECT, 10);
                    if (count($data) > 0) {
                        $message .= PHP_EOL . ':detective: **Stacktrace:**' . PHP_EOL;

                        foreach ($data as $key => $line) {
                            $file = data_get($line, 'file', 'unknown');

                            if (Str::contains($file, '/app/')) {
                                $file = '/app/' . Str::after($file, '/app/');
                            }

                            // #0 /var/www/app/app/Console/Commands/AbandonedRunCommand.php(97): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError()
                            $newLine = $file . ':' . data_get($line, 'line', 'unknown');

                            // append if it has function (or method)
                            if (isset($line['function'])) {
                                $newLine .= ' - Function: ' . anyToString($line['function']) . '()';
                            }

                            // append if it has class
                            if (isset($line['class'])) {
                                $newLine .= ' - Class: ' . anyToString($line['class']);
                            }

                            // append if it has object
                            if (isset($line['object']) && !isset($line['class'])) {
                                $newLine .= ' - Object: ' . anyToString($line['object']);
                            }

                            $message .= '> #' . $key . '. ' . $newLine . PHP_EOL;
                        }
                    }
                    logToDiscord(
                        $message,
                        'log_request'
                    );
                    return $query;
                }
            default:
            case DateRangeEnum::TODAY:
                if (is_null($query)) {
                    return $today;
                }


                $today = !$submonths ? $today : $today->copy()->subMonths($submonths)->toDateTimeString();

                return $query->where($dateRange['column'], '>=', $today); // first hour of this day
            case DateRangeEnum::YESTERDAY:
                $time = [
                    $today->copy()->subDays(),
                    $today->copy()->subSecond()
                ];
                $time = !$submonths ? $time : [
                    $time[0]->copy()->subMonths($submonths)->toDateTimeString(),
                    $time[0]->copy()->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_HOUR:
                $time = now()->subHour()->subRealHours($hourOffset);
                $time  = !$submonths ? $time :  $time->copy()->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::LAST_24_HOURS:
                $time = now()->subHours(24)->subRealHours($hourOffset);
                $time = !$submonths ? $time : $time->copy()->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::LAST_3_DAYS:
                $time = now()->subDays(3)->subRealHours($hourOffset);
                $time = !$submonths ? $time : $time->copy()->subMonths($submonths)->toDateTimeString();

                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::LAST_7_DAYS:
                $time = now()->subDays(7)->subRealHours($hourOffset);
                $time = !$submonths ? $time : $time->copy()->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::LAST_14_DAYS:
                $time = now()->subDays(14)->subRealHours($hourOffset);
                $time = !$submonths ? $time : $time->copy()->subMonths($submonths)->toDateTimeString();

                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::LAST_30_DAYS:
                $time = now()->subDays(30)->subRealHours($hourOffset);
                $time = !$submonths ? $time : $time->copy()->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::THIS_WEEK:
                $time = $today->startOfWeek()->subRealHours($hourOffset);
                $time = !$submonths ? $time : $time->copy()->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::LAST_WEEK:
                $time = [
                    $today->copy()->subWeeks()->startOfWeek()->subRealHours($hourOffset),
                    $today->copy()->subWeeks()->endOfWeek()->subRealHours($hourOffset)
                ];
                $time = !$submonths ? $time : [
                    $time[0]->copy()->subMonths($submonths)->toDateTimeString(),
                    $time[0]->copy()->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_MONTH:
                $startDate = now()->copy()->startOfMonth()->subRealHours($hourOffset);
                $endDate = now()->copy()->endOfMonth()->subRealHours($hourOffset);
                $time = [$startDate, $endDate];
                $time = !$submonths ? $time : [
                    $time[0]->copy()->subMonths($submonths)->toDateTimeString(),
                    $time[1]->copy()->subMonths($submonths)->toDateTimeString()
                ];
                if (is_null($query)) {
                    return $time;
                }
                // start day of this month
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_MONTH:
                $time = [
                    now()->copy()->startOfMonth()->subMonthsWithNoOverflow()->subRealHours($hourOffset),
                    now()->copy()->subMonthsWithNoOverflow()->endOfMonth()->subRealHours($hourOffset)
                ];
                $time = !$submonths ? $time : [
                    $time[0]->copy()->subMonths($submonths),
                    $time[0]->copy()
                ];

                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_QUARTER:
                $time = [
                    $today->copy()->startOfQuarter()->subQuarterNoOverflow()->subRealHours($hourOffset),
                    $today->copy()->subQuarterNoOverflow()->endOfQuarter()->subRealHours($hourOffset)
                ];
                $time = !$submonths ? $time : [
                    $time[0]->copy()->subMonths($submonths)->toDateTimeString(),
                    $time[0]->copy()->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_6_MONTHS:
                $time = [
                    $today->copy()->subMonths(6)->startOfMonth()->subRealHours($hourOffset),
                    $today->copy()->subMonth()->endOfMonth()->subRealHours($hourOffset)
                ];

                $time = !$submonths ? $time : [
                    $time[0]->copy()->subMonths($submonths),
                    $time[0]->copy()
                ];

                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_YEAR:
                $time = $today->startOfYear()->subRealHours($hourOffset);
                $time = !$submonths ? $time : $time->copy()->subMonths($submonths)->toDateTimeString();

                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::THIS_QUARTER:
                $time = $today->startOfQuarter()->subRealHours($hourOffset);
                $time = !$submonths ? $time : $time->copy()->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->where($dateRange['column'], '>=', $time);
            case DateRangeEnum::LAST_YEAR:
                $time = [
                    $today->copy()->subYearsWithNoOverflow()->startOfYear()->subRealHours($hourOffset),
                    $today->copy()->subYearsWithNoOverflow()->endOfYear()->subRealHours($hourOffset)
                ];
                $time = !$submonths ? $time : [
                    $time[0]->copy()->subMonths($submonths)->toDateTimeString(),
                    $time[0]->copy()->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
        }
    }
}

if (!function_exists('getShippingMethodName')) {
    function getShippingMethodName($name)
    {
        $methods = config('senprints.shipping_methods');

        if ($methods) {
            foreach ($methods as $method) {
                if ($method['name'] === $name) {
                    return $method['description'];
                }
            }
        }

        return '';
    }
}

if (!function_exists('getPaymentMethodName')) {
    function getPaymentMethodName($name): string
    {
        if ($name === PaymentMethodEnum::STRIPE) {
            return 'Stripe';
        }

        // stripe-card -> Stripe (Card)
        // stripe-apple_pay -> Stripe (Apple Pay)
        if (Str::startsWith($name, 'stripe-')) {
            $name = Str::after($name, 'stripe-');
            $name = Str::of($name)->replace('_', ' ')->ucfirst();
            return 'Stripe (' . $name . ')';
        }

        if ($name === PaymentMethodEnum::PAYPAL) {
            return 'Paypal';
        }

        return ucfirst($name);
    }
}

if (!function_exists('convertImageToWebpAndUploadToS3')) {

    /**
     * Convert image file to webp & upload to s3
     * @param $file
     * @param $folder
     * @return string|null
     */
    function convertImageToWebpAndUploadToS3($file, $folder): ?string
    {
        // check supported types
        if (
            !function_exists('tempnam') ||
            !in_array(strtolower($file->extension()), ['jpg', 'jpeg', 'png'])
        ) {
            return null;
        }

        $fileName = substr(md5_file($file), 0, 6) . ".{$file->extension()}.webp";
        $image = Image::make($file);

        // todo: use a fallback method to generate temp name
        $tmpFilename = tempnam(sys_get_temp_dir(), 'SP');

        file_put_contents($tmpFilename, $image->stream('webp', 80));

        try {
            $newFile = \Illuminate\Http\UploadedFile::createFromBase((new \Symfony\Component\HttpFoundation\File\UploadedFile(
                $tmpFilename,
                $fileName,
                'image/webp'
            )));

            $response = $newFile->storePubliclyAs(
                $folder,
                $fileName,
                StorageDisksEnum::DEFAULT
            );

            if ($response) {
                $image->destroy();
                @unlink($tmpFilename);
                return $response;
            }

            return null;
        } catch (Exception $e) {
            return null;
        }
    }
}

if (!function_exists('isCollection')) {
    function isCollection($param): bool
    {
        return ($param instanceof Collection)
            || ($param instanceof LengthAwarePaginator);
    }
}

if (!function_exists('cartesian')) {
    // input: ["arm":["A","B","C"],"gender":["Female","Male"],"location":["Vancouver","Calgary"]]
    // output: [["arm":"A","gender":"Female","location":"Vancouver"],["arm":"B","gender":"Female","location":"Vancouver"],["arm":"C","gender":"Female","location":"Vancouver"],["arm":"A","gender":"Male","location":"Vancouver"],["arm":"B","gender":"Male","location":"Vancouver"],["arm":"C","gender":"Male","location":"Vancouver"],["arm":"A","gender":"Female","location":"Calgary"],["arm":"B","gender":"Female","location":"Calgary"],["arm":"C","gender":"Female","location":"Calgary"],["arm":"A","gender":"Male","location":"Calgary"],["arm":"B","gender":"Male","location":"Calgary"],["arm":"C","gender":"Male","location":"Calgary"]]
    function cartesian($input)
    {
        $result = array();

        foreach ($input as $key => $values) {
            // If a sub-array is empty, it doesn't affect the cartesian product
            if (empty($values)) {
                continue;
            }

            // Seeding the product array with the values from the first sub-array
            if (empty($result)) {
                foreach ($values as $value) {
                    $result[] = array($key => $value);
                }
            } else {
                // Second and subsequent input sub-arrays work like this:
                //   1. In each existing array inside $product, add an item with
                //      key == $key and value == first item in input sub-array
                //   2. Then, for each remaining item in current input sub-array,
                //      add a copy of each existing array inside $product with
                //      key == $key and value == first item of input sub-array

                // Store all items to be added to $product here; adding them
                // inside the foreach will result in an infinite loop
                $append = array();

                foreach ($result as &$product) {
                    // Do step 1 above. array_shift is not the most efficient, but
                    // it allows us to iterate over the rest of the items with a
                    // simple foreach, making the code short and easy to read.
                    $product[$key] = array_shift($values);

                    // $product is by reference (that's why the key we added above
                    // will appear in the end result), so make a copy of it here
                    $copy = $product;

                    // Do step 2 above.
                    foreach ($values as $item) {
                        $copy[$key] = $item;
                        $append[] = $copy;
                    }

                    // Undo the side effect of array_shift
                    array_unshift($values, $product[$key]);
                }

                // Out of the foreach, we can add to $results now
                $result = array_merge($result, $append);
            }
        }

        return $result;
    }
}

if (!function_exists('generateVariantKeysByProductOptions')) {
    function generateVariantKeysByProductOptions($options, $showOptionsName = false): array
    {
        $options = Str::isJson($options) ? json_decode($options, true, 512, JSON_THROW_ON_ERROR) : $options;
        $variants = [];
        if (empty($options)) {
            return ['none'];
        }
        if (is_object($options)) {
            $options = (array) $options;
        }
        if (!empty($options['custom_options'])) {
            unset($options['custom_options']);
        }
        if (!empty($options['common_options'])) {
            unset($options['common_options']);
        }
        // ex: options=['size' => ['s','x'], 'color'=>['black','white','green']]
        // => [['s','black'],['s','white'],['s','green'],['x','black'],['x','white'],['x','green']]
        $options = cartesian($options);
        array_map(
            static function ($option) use (&$variants, $showOptionsName) {
                if (!$showOptionsName) {
                    $variants[] = getVariantKey($option);
                } else {
                    $arr        = [
                        'variant_key' => getVariantKey($option)
                    ];
                    $variants[] = array_merge($arr, $option);
                }
            },
            $options
        );
        // => ['s-black','s-white','s-green','x-black','x-white','x-green']

        return $variants;
    }
}

if (!function_exists('generateShardId')) {
    function generateShardId(Model $model, int $startNumber = 1000, $isReset = false, $regionPrefix = null): string
    {
        $shardId = config('senprints.shard_id');
        if (!empty($regionPrefix)) {
            $overrideShardId = config('region.region_order_shard_id');
            $shardId = $overrideShardId . $regionPrefix;
        }
        $modelName      = class_basename($model);
        $cacheKey       = CacheKeys::getShardCount($shardId, $modelName);
        $numberAttempts = 3;
        $count          = $startNumber;

        for ($currentAttempt = 0; $currentAttempt < $numberAttempts; $currentAttempt++) {
            DB::beginTransaction();
            try {
                $value = Sharding::query()
                    ->whereKey($cacheKey)
                    ->lockForUpdate()
                    ->value('value');
                if (empty($value) || $isReset) {
                    $maxId = $model::query()
                        ->newQuery()
                        ->where('shard_id', $shardId)
                        ->when(
                            in_array('Illuminate\Database\Eloquent\SoftDeletes', class_uses($model)),
                            function ($query) {
                                $query->withTrashed();
                                return $query;
                            }
                        )
                        ->max('id');

                    if (!is_null($maxId)) {
                        if (strpos($maxId, $shardId) === 0) {
                            $value = substr($maxId, strlen($shardId));
                        } else {
                            $value = $maxId;
                        }
                    }
                }
                if (!empty($value)) {
                    $count = $value + 1;
                }

                if (!empty($regionPrefix)) {
                    $count = (string)$count;
                    $length = strlen($count);
                    if ($length < 6) {
                        $count = str_pad($count, 6, '0', STR_PAD_RIGHT);
                    }
                }

                Sharding::query()
                    ->updateOrCreate(
                        ['key' => $cacheKey],
                        ['value' => $count]
                    );
                DB::commit();
                break;
            } catch (Throwable $e) {
                DB::rollBack();
                // if DeadLock then retry!!
                if ($e->getCode() === 1205) {
                    logException($e, __FUNCTION__, 'error', true);
                    continue;
                }
                throw $e;
            }
        }

        return $shardId . $count;
    }
}

if (!function_exists('generateUUID')) {
    function generateUUID(): string
    {
        return Str::orderedUuid()->toString();
    }
}

if (!function_exists('cdnUrl')) {
    function cdnUrl(): string
    {
        $cdnUrl = env('CDN_URL', 'https://cdn.cloudimgs.net');
        return rtrim($cdnUrl, '/');
    }
}

if (!function_exists('s3Url')) {
    function s3Url(?string $path, $noCache = null): ?string
    {
        if (!$path || str_starts_with($path, 'http')) {
            return $path;
        }
        $url = cdnUrl() . '/' . $path;
        if (!empty($noCache)) {
            $url .= '?nocache=' . $noCache;
        }
        return $url;
    }
}

if (!function_exists('imgColorUrl')) {
    function imgColorUrl(?string $path, $color): ?string
    {
        if ($path && $color) {
            $colorHex = str_replace('#', '', color2hex($color));
            $path = preg_replace('/co_rgb:.{6}/', 'co_rgb:' . $colorHex, $path);
        }
        return $path;
    }
}

if (!function_exists('jpgUrl')) {
    function jpgUrl(?string $path): string
    {
        $imagesExt = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        if (!empty($extension) && !in_array($extension, $imagesExt)) {
            return s3Url($path);
        }
        $imgUrl = config('senprints.base_img_url') . '/rx/-,ofmt_jpeg';

        if (str_starts_with($path, '/s')) {
            $imgUrl .= $path;
        } else {
            $imgUrl .= '/s2/' . $path;
        }

        $imgUrl .= '/t/' . time() . '.jpg';
        return $imgUrl;
    }
}

if (!function_exists('scaleDesignUrl')) {
    function scaleDesignUrl($config, $path): string
    {
        if (!$path || str_starts_with($path, 'http')) {
            return $path;
        }
        $imagesExt = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        if (!empty($extension) && !in_array($extension, $imagesExt)) {
            return s3Url($path);
        }
        $noCacheRunningNumber = (int) SystemConfig::getConfig('no_cache_running_number');
        $imgUrl = config('senprints.base_img_url') . '/rx/';

        $imgUrl .= $config->width . 'x' . $config->height;
        if (!empty($config->color_space) && in_array($config->color_space, ColorSpaceEnum::getValues(), true)) {
            $imgUrl .= ',' . $config->color_space;
        }
        if (!empty($config->dpi) && (int) $config->dpi >= 150 && (int) $config->dpi <= 300) {
            $imgUrl .= ',dp_' . $config->dpi;
        }
        if (!empty($config->noxmp)) {
            $imgUrl .= ',noxmp';
        }
        if ($config->crop_transparent) {
            $imgUrl .= ',ofmt_png,c_3/s2/';
        } else {
            $imgUrl .= ',ofmt_png,c_2/s2/';
        }
        $imgUrl .= $path;
        if (!empty($noCacheRunningNumber)) {
            $imgUrl .= '?nocache=' . $noCacheRunningNumber;
        }
        return $imgUrl;
    }
}

if (!function_exists('imgUrl')) {
    function imgUrl(?string $path, $type = null, $color = null, $imgUrl = null): ?string
    {
        if (!$path) {
            return $path;
        }

        if (Str::contains($path, 'renders.cloudmockups.com')) {
            $path = str_replace('renders.cloudmockups.com', 'renders.cloudmockupsservices.com', $path);
        }

        if (Str::contains($path, 'renders.cloudmockupsservices.com')) {
            $url = parse_url($path);
            if (!$url) {
                return $path;
            }

            if ($color) {
                $colorHex = Str::replace('#', '', color2hex($color));
            }

            parse_str($url['query'], $urlParams);
            if (isset($colorHex)) {
                $urlParams['c'] = Str::lower($colorHex);
            }

            if (!isset($urlParams['expectedWidth'])) {
                $urlParams['expectedWidth'] = 1000;
            }

            $url['query'] = http_build_query($urlParams);

            return $url['scheme'] . '://' . $url['host'] . $url['path'] . '?' . $url['query'];
        }

        if (Str::startsWith($path, 'http')) {
            return $path;
        }
        $imagesExt = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        if (!empty($extension) && !in_array($extension, $imagesExt)) {
            return s3Url($path);
        }
        if ($color) {
            $colorHex = Str::replace('#', '', color2hex($color));
            $path = preg_replace('/co_rgb:.{6}/', 'co_rgb:' . $colorHex, $path);
        }

        $imgUrl ??= config('senprints.base_img_url');

        $imgUrl .= match ($type) {
            'full_hd' => '/rx/1000x1250',
            'preview' => '/rx/400x500,c_2',
            'preview_design' => '/rx/500',
            'download_design' => '/rx/800x800',
            'list' => '/rx/600x750,c_2',
            'mobile' => '/rx/640x800,c_2',
            'list_mb' => '/rx/320x400,c_2',
            'logo' => '/rx/256x256',
            'avatar' => '/rx/160x160',
            'thumb' => '/rx/160x200,c_2',
            'favicon' => '/rx/32x32',
            'validate' => '/rx/500,co_808080',
            'render' => '/rx/3000',
            'original' => '/rx/-',
            default => '/rx/1000',
        };

        if (Str::startsWith($path, '/s')) {
            $imgUrl .= $path;
        } else {
            $imgUrl .= '/s2/' . $path;
        }

        return $imgUrl;
    }
}

if (!function_exists('getSupplier')) {
    function getSupplier(string $supplierId)
    {
        $suppliers = config('supplier');
        foreach ($suppliers as $name => $supplier) {
            if ($supplier['supplier_id'] === $supplierId) {
                return $name;
            }
        }
        return "";
    }
}

if (!function_exists('formatCurrency')) {
    /**
     * @param mixed $number
     * @param $code
     * @param $locale
     * @param bool $withSymbol
     * @return string
     */
    function formatCurrency($number, $code, $locale, bool $withSymbol = false): string
    {
        $code ??= 'USD';
        $locale ??= 'en-US';
        $fmt = new NumFormat($locale, NumFormat::CURRENCY);
        if (!$withSymbol) {
            $fmt->setSymbol(NumFormat::CURRENCY_SYMBOL, '');
        }
        return $fmt->formatCurrency($number, Str::upper($code));
    }
}

if (!function_exists('formatPrice')) {
    /**
     * @param mixed $value
     * @param $currency
     * @return string
     */
    function formatPrice($value, $currency): string
    {
        return formatCurrency(convertCurrency($value, $currency['rate']), $currency['code'], $currency['locale'], true);
    }
}

if (!function_exists('convertCurrency')) {
    /**
     * @param mixed $number
     * @param mixed $currency_rate
     *
     * @return float|int
     */
    function convertCurrency($number, $currency_rate)
    {
        $currency_rate ??= 1;
        return $number * $currency_rate;
    }
}

if (!function_exists('parseElementToArray')) {
    /**
     * @param array $element
     * @param string $key
     * @return array|array[]
     */
    function parseElementToArray(array &$element, string $key = 'id'): array
    {
        if (Arr::exists($element, $key)) {
            $element = [$element];
        }
        return $element;
    }
}


if (!function_exists('getBaseCostsByLocation')) {

    function getBaseCostsByLocation(object $product, $location): float
    {
        if (is_string($product->base_costs)) {
            $product->base_costs = json_decode($product->base_costs, true);
        }
        // if (is_null($locationCode)) {
        //     $locationCode = '*';
        // }

        // $location = getLocationByCode($locationCode);

        if (isset($product->base_costs, $location)) {
            if (isset($product->base_costs[$location->code])) {
                return $product->base_costs[$location->code];
            }

            if (isset($product->base_costs[$location->sub_region_code])) {
                return $product->base_costs[$location->sub_region_code];
            }

            if (isset($product->base_costs[$location->intermediate_region_code])) {
                return $product->base_costs[$location->intermediate_region_code];
            }

            if (isset($product->base_costs[$location->region_code])) {
                return $product->base_costs[$location->region_code];
            }
        }

        return $product->base_cost;
    }
}


if (!function_exists('getSenPointsByLocation')) {

    function getSenPointsByLocation(array $points, $locationCode, $defaultPoint = 0): ?float
    {
        if (empty($locationCode)) {
            return $defaultPoint;
        }

        $location = getLocationByCode($locationCode);
        if (empty($location)) {
            return $defaultPoint;
        }

        if (isset($points[$location->code])) {
            return $points[$location->code];
        }

        if (isset($points[$location->sub_region_code])) {
            return $points[$location->sub_region_code];
        }

        if (isset($points[$location->intermediate_region_code])) {
            return $points[$location->intermediate_region_code];
        }

        if (isset($points[$location->region_code])) {
            return $points[$location->region_code];
        }

        return $defaultPoint;
    }
}

if (!function_exists('lookupArrayMapping')) {
    function lookupArrayMapping(array $map, string $status)
    {
        foreach ($map as $mapStatus => $supStatusList) {
            if (in_array($status, $supStatusList)) {
                return $mapStatus;
            }
        }
        return null;
    }
}

if (!function_exists('cleanSpecialCharacters')) {
    function cleanSpecialCharacters($string): string
    {
        $string = addslashes(strip_tags($string));
        $string = filter_var($string, FILTER_SANITIZE_STRING);
        $string = Str::remove([
            '!',
            '@',
            '#',
            '$',
            '%',
            '^',
            '&',
            '*',
            '(',
            ')',
            '+',
            '=',
            '~',
            '<',
            '>',
        ], $string);
        $string = Str::replace(
            [
                '-',
                '_',
            ],
            ' ',
            $string
        );

        return $string;
    }
}

if (!function_exists('isBotDetected')) {
    /**
     * Ref: https://newbedev.com/how-can-one-detect-a-crawler-spider-using-php
     *
     * @param $userAgent
     * @return boolean
     */
    function isBotDetected($userAgent): bool
    {
        if (preg_match('/abacho|accona|AddThis|AdsBot|ahoy|AhrefsBot|AISearchBot|alexa|altavista|anthill|appie|applebot|arale|araneo|AraybOt|ariadne|arks|aspseek|ATN_Worldwide|Atomz|baiduspider|baidu|bbot|bingbot|bing|Bjaaland|BlackWidow|BotLink|bot|boxseabot|bspider|calif|CCBot|ChinaClaw|christcrawler|CMC\/0\.01|combine|confuzzledbot|contaxe|CoolBot|cosmos|crawler|crawlpaper|crawl|curl|cusco|cyberspyder|cydralspider|dataprovider|digger|DIIbot|DotBot|downloadexpress|DragonBot|DuckDuckBot|dwcp|EasouSpider|ebiness|ecollector|elfinbot|esculapio|ESI|esther|eStyle|Ezooms|facebookexternalhit|facebook|facebot|fastcrawler|FatBot|FDSE|FELIX IDE|fetch|fido|find|Firefly|fouineur|Freecrawl|froogle|gammaSpider|gazz|gcreep|geona|Getterrobo-Plus|get|girafabot|golem|googlebot|\-google|grabber|GrabNet|griffon|Gromit|gulliver|gulper|hambot|havIndex|hotwired|htdig|HTTrack|ia_archiver|iajabot|IDBot|Informant|InfoSeek|InfoSpiders|INGRID\/0\.1|inktomi|inspectorwww|Internet Cruiser Robot|irobot|Iron33|JBot|jcrawler|Jeeves|jobo|KDD\-Explorer|KIT\-Fireball|ko_yappo_robot|label\-grabber|larbin|legs|libwww-perl|linkedin|Linkidator|linkwalker|Lockon|logo_gif_crawler|Lycos|m2e|majesticsEO|marvin|mattie|mediafox|mediapartners|MerzScope|MindCrawler|MJ12bot|mod_pagespeed|moget|Motor|msnbot|muncher|muninn|MuscatFerret|MwdSearch|NationalDirectory|naverbot|NEC\-MeshExplorer|NetcraftSurveyAgent|NetScoop|NetSeer|newscan\-online|nil|none|Nutch|ObjectsSearch|Occam|openstat.ru\/Bot|packrat|pageboy|ParaSite|patric|pegasus|perlcrawler|phpdig|piltdownman|Pimptrain|pingdom|pinterest|pjspider|PlumtreeWebAccessor|PortalBSpider|psbot|rambler|Raven|RHCS|RixBot|roadrunner|Robbie|robi|RoboCrawl|robofox|Scooter|Scrubby|Search\-AU|searchprocess|SenprintsTestBot|search|SemrushBot|Senrigan|seznambot|Shagseeker|sharp\-info\-agent|sift|SimBot|Site Valet|SiteSucker|skymob|SLCrawler\/2\.0|slurp|snooper|solbot|speedy|spider_monkey|SpiderBot\/1\.0|spiderline|spider|suke|tach_bw|TechBOT|TechnoratiSnoop|templeton|teoma|titin|topiclink|twitterbot|twitter|UdmSearch|Ukonline|UnwindFetchor|URL_Spider_SQL|urlck|urlresolver|Valkyrie libwww\-perl|verticrawl|Victoria|void\-bot|Voyager|VWbot_K|wapspider|WebBandit\/1\.0|webcatcher|WebCopier|WebFindBot|WebLeacher|WebMechanic|WebMoose|webquest|webreaper|webspider|webs|WebWalker|WebZip|wget|whowhere|winona|wlm|WOLP|woriobot|WWWC|XGET|xing|yahoo|YandexBot|YandexMobileBot|yandex|yeti|Zeus/i', $userAgent)) {
            return true; // 'Above given bots detected'
        }
        return false;
    }
}

if (!function_exists('xml_encode')) {
    // https://stackoverflow.com/a/18308071/9029340
    function xml_encode(
        $mixed,
        $domElement = null,
        $DOMDocument = null,
        array $exceptIndexes = ['root']
    ) {
        if (is_null($DOMDocument)) {
            $DOMDocument = new DOMDocument();
            $DOMDocument->formatOutput = true;
            xml_encode($mixed, $DOMDocument, $DOMDocument, $exceptIndexes);
            return $DOMDocument->saveXML();
        }

        // To cope with embedded objects
        if (is_object($mixed)) {
            $mixed = get_object_vars($mixed);
        }
        if (is_array($mixed)) {
            foreach ($mixed as $index => $mixedElement) {
                if (is_int($index)) {
                    if ($index === 0) {
                        $node = $domElement;
                    } else {
                        $node = $DOMDocument->createElement($domElement->tagName);
                        $domElement->parentNode->appendChild($node);
                    }
                } else {
                    $plural = $DOMDocument->createElement($index);
                    $domElement->appendChild($plural);
                    $node = $plural;
                    if (is_array($mixedElement) && !in_array($index, $exceptIndexes)) {
                        $singular = $DOMDocument->createElement(rtrim($index, 's'));
                        $plural->appendChild($singular);
                        $node = $singular;
                    }
                }

                xml_encode($mixedElement, $node, $DOMDocument, $exceptIndexes);
            }
        } else {
            $mixed = is_bool($mixed) ? ($mixed ? 'true' : 'false') : $mixed;
            $domElement->appendChild($DOMDocument->createTextNode($mixed));
        }
    }
}

if (!function_exists('cleanString')) {
    function cleanString($string)
    {
        $string = str_replace(' ', '_', $string); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9\-]/', '', $string); // Removes special chars.
    }
}

if (!function_exists('getScoreProductBySales')) {
    function getScoreProductBySales($sales = 0): array
    {
        $timestamp = time() / 3600;
        $power = 0.1;
        $multiply = 24 * 30;
        $score = $timestamp + ($sales ** $power) * $multiply;

        return compact('timestamp', 'score');
    }
}


if (!function_exists('getStringDefinitionFromEnums')) {
    function getStringDefinitionFromEnums($enum): string
    {
        $string = '';
        foreach ($enum::getInstances() as $key => $value) {
            $string .= strtolower($key) . ':' . $value . '|';
        }

        return $string;
    }
}

if (!function_exists('checkAndConvertToArray')) {
    function checkAndConvertToArray($value): array
    {
        if (empty($value)) {
            return [];
        }

        return is_array($value) ? $value : explode(',', $value);
    }
}

if (!function_exists('sortArrayOptions')) {
    function sortArrayOptions(array &$arr): void
    {
        $maxIndexSorted = count($arr);
        $arr            = collect($arr)->sortBy(function ($each, $key) use ($maxIndexSorted) {
            $searchKey = array_search($key, ProductOptionEnum::getValues());
            return ($searchKey === false) ? $maxIndexSorted++ : $searchKey;
        })->toArray();
    }
}

if (!function_exists('saveTempFileAws')) {
    /**
     * @param $tempPath
     * @param $newPath
     * @return mixed|string
     */
    function saveTempFileAws($tempPath, $newPath): ?string
    {
        if (isEnvTesting()) {
            return $tempPath;
        }

        $result = '';
        if (!$tempPath || !$newPath) {
            return $result;
        }
        if (str_starts_with($tempPath, 'tmp/') || str_contains($tempPath, '/tmp/')) {
            $filePaths = explode('/', $tempPath);
            $fileName = end($filePaths);
            if (!str_contains($newPath, $fileName)) {
                $newPath .= '/' . $fileName;
            }
            $maxRetried = 1;
            while (true) {
                if ($maxRetried > 3) {
                    break;
                }
                $maxRetried++;
                if (move_file_on_storage($tempPath, $newPath)) {
                    $result = $newPath;
                    break;
                }
            }
        } else {
            $result = $tempPath;
        }
        return $result;
    }
}

if (!function_exists('moveFileAws')) {
    /**
     * @param $tempPath
     * @param $newPath
     * @return mixed|string
     */
    function moveFileAws($tempPath, $newPath)
    {
        $result = '';
        if (!$tempPath || !$newPath) {
            return $result;
        }
        $filePaths = explode('/', $tempPath);
        $fileName = end($filePaths);
        if (!str_contains($newPath, $fileName)) {
            $newPath .= '/' . $fileName;
        }
        $maxRetried = 1;
        while (true) {
            if ($maxRetried > 3) {
                break;
            }
            $maxRetried++;
            if (move_file_on_storage($tempPath, $newPath)) {
                $result = $newPath;
                break;
            }
        }
        return $result;
    }
}

if (!function_exists('copyS3TempToMain')) {
    function copyS3TempToMain($tempPath, $newPath, string $disk = StorageDisksEnum::DEFAULT): string
    {
        if (!$tempPath || !$newPath) {
            return '';
        }
        $filePaths = explode('/', $tempPath);
        $fileName = end($filePaths);
        if (!str_contains($newPath, $fileName)) {
            $newPath .= '/' . $fileName;
        }
        $maxRetried = 1;
        while (true) {
            if ($maxRetried > 3) {
                break;
            }
            $maxRetried++;
            if (copy_file_on_storage($tempPath, $newPath)) {
                $result = $newPath;
                break;
            }
        }
        return $newPath;
    }
}

if (!function_exists('calculateCompareAtPrice')) {
    function calculateCompareAtPrice(float $price): float
    {
        $comparePriceRate = (float) SystemConfig::getConfig('compare_price_rate', 20) / 100;
        $decimal = $price - (int) $price;
        return (int) ($price * (1 + $comparePriceRate) - $decimal) + $decimal;
    }
}

if (!function_exists('currentTime')) {
    function currentTime(): string
    {
        return date('Y-m-d H:i:s');
    }
}

if (!function_exists('getLinkProductOnStoreByOptions')) {
    function getLinkProductOnStoreByOptions(
        string $domain,
        string $campaignSlug,
        string $productName,
        array $options
    ): string {
        $options = array_map(function ($value) {
            return str_replace(' ', '-', strtolower($value));
        }, $options);

        $data = array_merge([
            'product' => Str::slug($productName)
        ], $options);

        return 'https://' . $domain . '/' . $campaignSlug . '?' . http_build_query($data);
    }
}

if (!function_exists('nameSpaceK8sByAppEnv')) {

    function nameSpaceK8sByAppEnv(): ?string
    {
        switch (app()->environment()) {
            case 'production':
                $namespace = 'production';
                break;
            case 'development':
                $namespace = 'development';
                break;
            case 'local':
                $namespace = null;
                break;
            default:
                $namespace = 'default';
        }
        return $namespace;
    }
}

if (!function_exists('cacheGet')) {
    function cacheGet($key, $time, $callback, $tags = [], $type = 'default')
    {
        if (!is_callable($callback)) {
            return null;
        }

        try {
            $cache = cache();

            if ($type === CacheKeys::CACHE_TYPE_ALTERNATIVE) {
                $cache = cacheAlt();
            }

            if (!empty($tags)) {
                $cache = $cache->tags($tags);
            }

            return $cache->remember($key, $time, $callback);
        } catch (Throwable $e) {
            return $callback();
        }
    }
}

//https://gist.github.com/alexcorvi/df8faecb59e86bee93411f6a7967df2c#gistcomment-2722664
if (!function_exists('mime2ext')) {
    function mime2ext($mime)
    {
        $mime_map = [
            'video/3gpp2'                                                               => '3g2',
            'video/3gp'                                                                 => '3gp',
            'video/3gpp'                                                                => '3gp',
            'application/x-compressed'                                                  => '7zip',
            'audio/x-acc'                                                               => 'aac',
            'audio/ac3'                                                                 => 'ac3',
            'application/postscript'                                                    => 'ai',
            'audio/x-aiff'                                                              => 'aif',
            'audio/aiff'                                                                => 'aif',
            'audio/x-au'                                                                => 'au',
            'video/x-msvideo'                                                           => 'avi',
            'video/msvideo'                                                             => 'avi',
            'video/avi'                                                                 => 'avi',
            'application/x-troff-msvideo'                                               => 'avi',
            'application/macbinary'                                                     => 'bin',
            'application/mac-binary'                                                    => 'bin',
            'application/x-binary'                                                      => 'bin',
            'application/x-macbinary'                                                   => 'bin',
            'image/bmp'                                                                 => 'bmp',
            'image/x-bmp'                                                               => 'bmp',
            'image/x-bitmap'                                                            => 'bmp',
            'image/x-xbitmap'                                                           => 'bmp',
            'image/x-win-bitmap'                                                        => 'bmp',
            'image/x-windows-bmp'                                                       => 'bmp',
            'image/ms-bmp'                                                              => 'bmp',
            'image/x-ms-bmp'                                                            => 'bmp',
            'application/bmp'                                                           => 'bmp',
            'application/x-bmp'                                                         => 'bmp',
            'application/x-win-bitmap'                                                  => 'bmp',
            'application/cdr'                                                           => 'cdr',
            'application/coreldraw'                                                     => 'cdr',
            'application/x-cdr'                                                         => 'cdr',
            'application/x-coreldraw'                                                   => 'cdr',
            'image/cdr'                                                                 => 'cdr',
            'image/x-cdr'                                                               => 'cdr',
            'zz-application/zz-winassoc-cdr'                                            => 'cdr',
            'application/mac-compactpro'                                                => 'cpt',
            'application/pkix-crl'                                                      => 'crl',
            'application/pkcs-crl'                                                      => 'crl',
            'application/x-x509-ca-cert'                                                => 'crt',
            'application/pkix-cert'                                                     => 'crt',
            'text/css'                                                                  => 'css',
            'text/x-comma-separated-values'                                             => 'csv',
            'text/comma-separated-values'                                               => 'csv',
            'application/vnd.msexcel'                                                   => 'csv',
            'application/x-director'                                                    => 'dcr',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'   => 'docx',
            'application/x-dvi'                                                         => 'dvi',
            'message/rfc822'                                                            => 'eml',
            'application/x-msdownload'                                                  => 'exe',
            'video/x-f4v'                                                               => 'f4v',
            'audio/x-flac'                                                              => 'flac',
            'video/x-flv'                                                               => 'flv',
            'image/gif'                                                                 => 'gif',
            'application/gpg-keys'                                                      => 'gpg',
            'application/x-gtar'                                                        => 'gtar',
            'application/x-gzip'                                                        => 'gzip',
            'application/mac-binhex40'                                                  => 'hqx',
            'application/mac-binhex'                                                    => 'hqx',
            'application/x-binhex40'                                                    => 'hqx',
            'application/x-mac-binhex40'                                                => 'hqx',
            'text/html'                                                                 => 'html',
            'image/x-icon'                                                              => 'ico',
            'image/x-ico'                                                               => 'ico',
            'image/vnd.microsoft.icon'                                                  => 'ico',
            'text/calendar'                                                             => 'ics',
            'application/java-archive'                                                  => 'jar',
            'application/x-java-application'                                            => 'jar',
            'application/x-jar'                                                         => 'jar',
            'image/jp2'                                                                 => 'jp2',
            'video/mj2'                                                                 => 'jp2',
            'image/jpx'                                                                 => 'jp2',
            'image/jpm'                                                                 => 'jp2',
            'image/jpeg'                                                                => 'jpeg',
            'image/pjpeg'                                                               => 'jpeg',
            'application/x-javascript'                                                  => 'js',
            'application/json'                                                          => 'json',
            'text/json'                                                                 => 'json',
            'application/vnd.google-earth.kml+xml'                                      => 'kml',
            'application/vnd.google-earth.kmz'                                          => 'kmz',
            'text/x-log'                                                                => 'log',
            'audio/x-m4a'                                                               => 'm4a',
            'application/vnd.mpegurl'                                                   => 'm4u',
            'audio/midi'                                                                => 'mid',
            'application/vnd.mif'                                                       => 'mif',
            'video/quicktime'                                                           => 'mov',
            'video/x-sgi-movie'                                                         => 'movie',
            'audio/mpeg'                                                                => 'mp3',
            'audio/mpg'                                                                 => 'mp3',
            'audio/mpeg3'                                                               => 'mp3',
            'audio/mp3'                                                                 => 'mp3',
            'video/mp4'                                                                 => 'mp4',
            'video/mpeg'                                                                => 'mpeg',
            'application/oda'                                                           => 'oda',
            'audio/ogg'                                                                 => 'ogg',
            'video/ogg'                                                                 => 'ogg',
            'application/ogg'                                                           => 'ogg',
            'application/x-pkcs10'                                                      => 'p10',
            'application/pkcs10'                                                        => 'p10',
            'application/x-pkcs12'                                                      => 'p12',
            'application/x-pkcs7-signature'                                             => 'p7a',
            'application/pkcs7-mime'                                                    => 'p7c',
            'application/x-pkcs7-mime'                                                  => 'p7c',
            'application/x-pkcs7-certreqresp'                                           => 'p7r',
            'application/pkcs7-signature'                                               => 'p7s',
            'application/pdf'                                                           => 'pdf',
            'application/octet-stream'                                                  => 'pdf',
            'application/x-x509-user-cert'                                              => 'pem',
            'application/x-pem-file'                                                    => 'pem',
            'application/pgp'                                                           => 'pgp',
            'application/x-httpd-php'                                                   => 'php',
            'application/php'                                                           => 'php',
            'application/x-php'                                                         => 'php',
            'text/php'                                                                  => 'php',
            'text/x-php'                                                                => 'php',
            'application/x-httpd-php-source'                                            => 'php',
            'image/png'                                                                 => 'png',
            'image/x-png'                                                               => 'png',
            'application/powerpoint'                                                    => 'ppt',
            'application/vnd.ms-powerpoint'                                             => 'ppt',
            'application/vnd.ms-office'                                                 => 'ppt',
            'application/msword'                                                        => 'doc',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
            'application/x-photoshop'                                                   => 'psd',
            'image/vnd.adobe.photoshop'                                                 => 'psd',
            'audio/x-realaudio'                                                         => 'ra',
            'audio/x-pn-realaudio'                                                      => 'ram',
            'application/x-rar'                                                         => 'rar',
            'application/rar'                                                           => 'rar',
            'application/x-rar-compressed'                                              => 'rar',
            'audio/x-pn-realaudio-plugin'                                               => 'rpm',
            'application/x-pkcs7'                                                       => 'rsa',
            'text/rtf'                                                                  => 'rtf',
            'text/richtext'                                                             => 'rtx',
            'video/vnd.rn-realvideo'                                                    => 'rv',
            'application/x-stuffit'                                                     => 'sit',
            'application/smil'                                                          => 'smil',
            'text/srt'                                                                  => 'srt',
            'image/svg+xml'                                                             => 'svg',
            'application/x-shockwave-flash'                                             => 'swf',
            'application/x-tar'                                                         => 'tar',
            'application/x-gzip-compressed'                                             => 'tgz',
            'image/tiff'                                                                => 'tiff',
            'text/plain'                                                                => 'txt',
            'text/x-vcard'                                                              => 'vcf',
            'application/videolan'                                                      => 'vlc',
            'text/vtt'                                                                  => 'vtt',
            'audio/x-wav'                                                               => 'wav',
            'audio/wave'                                                                => 'wav',
            'audio/wav'                                                                 => 'wav',
            'application/wbxml'                                                         => 'wbxml',
            'video/webm'                                                                => 'webm',
            'audio/x-ms-wma'                                                            => 'wma',
            'application/wmlc'                                                          => 'wmlc',
            'video/x-ms-wmv'                                                            => 'wmv',
            'video/x-ms-asf'                                                            => 'wmv',
            'application/xhtml+xml'                                                     => 'xhtml',
            'application/excel'                                                         => 'xl',
            'application/msexcel'                                                       => 'xls',
            'application/x-msexcel'                                                     => 'xls',
            'application/x-ms-excel'                                                    => 'xls',
            'application/x-excel'                                                       => 'xls',
            'application/x-dos_ms_excel'                                                => 'xls',
            'application/xls'                                                           => 'xls',
            'application/x-xls'                                                         => 'xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'         => 'xlsx',
            'application/vnd.ms-excel'                                                  => 'xlsx',
            'application/xml'                                                           => 'xml',
            'text/xml'                                                                  => 'xml',
            'text/xsl'                                                                  => 'xsl',
            'application/xspf+xml'                                                      => 'xspf',
            'application/x-compress'                                                    => 'z',
            'application/x-zip'                                                         => 'zip',
            'application/zip'                                                           => 'zip',
            'application/x-zip-compressed'                                              => 'zip',
            'application/s-compressed'                                                  => 'zip',
            'multipart/x-zip'                                                           => 'zip',
            'text/x-scriptzsh'                                                          => 'zsh',
            'image/webp'                                                                => 'webp',
        ];

        return isset($mime_map[$mime]) ? $mime_map[$mime] : false;
    }
}

if (!function_exists('array_values_recursive')) {
    // remove key from array, work for multidimensional array
    function array_values_recursive($arr): array
    {
        $arr = array_values($arr);
        foreach ($arr as $key => $val) {
            if (array_values($val) === $val) {
                $arr[$key] = array_values_recursive($val);
            }
        }

        return $arr;
    }
}

if (!function_exists('generate_csrf_token')) {
    /**
     * @param array $params
     * @return string
     */
    function generate_csrf_token(array $params): string
    {
        $encryptString = '';
        if (empty($params)) {
            $encryptString .= '::';
        }
        $encryptString .= implode('::', $params);
        $hash = new SPHash(config('app.key'));
        return base64_encode($hash->encrypt($encryptString));
    }
}

if (!function_exists('verifyCsrfToken')) {
    function verifyCsrfToken(?string $csrfToken = '', ?string $sessionId = '', ?string $clientIp = ''): bool
    {
        if (empty($csrfToken) || empty($sessionId) || empty($clientIp)) {
            return false;
        }

        $hashToken = base64_decode($csrfToken);
        $hash = new SPHash(config('app.key'));
        $decryptToken = $hash->decrypt($hashToken);

        if (str_contains($decryptToken, '::')) {
            [$_sessionId, $_clientIp] = explode('::', $decryptToken);
            return $_sessionId === $sessionId && $_clientIp === $clientIp;
        }

        return false;
    }
}

if (!function_exists('hash_file_name')) {
    function hash_file_name($file): string
    {
        $hash = md5_file($file);
        return substr($hash, 0, 6) . '.' . $file->extension();
    }
}

if (!function_exists('diffProcessingDay')) {
    function diffProcessingDay($startDate, $endDate = null): float
    {
        $dt = Carbon::parse($startDate);
        $dt2 = Carbon::parse($endDate);

        return $dt->diffInHoursFiltered(function (Carbon $date) {
            return !$date->isWeekend();
        }, $dt2) / 24;
    }
}

if (!function_exists('requestEmailValidation')) {
    /**
     * Email validation with socket tcp
     * @param $email
     * @return bool
     */
    function requestEmailValidation($email = null): bool
    {
        if (empty($email)) {
            return false;
        }
        try {
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return false;
            }
            $hash = md5(strtolower(trim($email)));
            $headers = @get_headers("https://www.gravatar.com/avatar/$hash?d=404");
            if (str_contains($headers[0], '200')) {
                return true;
            }
            $domain = strtolower(substr(strrchr($email, "@"), 1));
            if (in_array($domain, config('senprints.whitelist_registered_domains', []), true)) {
                return true;
            }
            if (!checkdnsrr($domain, 'MX') || !checkdnsrr($domain, 'A')) {
                return false;
            }
            // https://unwrap.email/settings/general -> API Token
            $response = Http::withHeaders([
                'UNWRAP-EMAIL-API-TOKEN' => env('UNWRAP_EMAIL_API_TOKEN', 'ue_live_8ee3dc1a6e9d47ce82668bbae12c96af_mJSwbhaPH5CA2jmwl1NpjO_gFYODt/KLFmDpwsEwnn7BISMbDmItesBZSeDtYvZea8='),
            ])
            ->get("https://api.unwrap.email/v1/emails/validate?email=$email");
            if ($response->failed()) {
                logToDiscord('Unwrap email validation failed: ' . $response->json(), 'dev_logs');
                return true;
            }
            $response = $response->json();
            return data_get($response, 'validations.safe_to_send') !== false;
        } catch (Throwable $e) {
            return true;
        }
    }
}

if (!function_exists('POMassPayOutIsEnabled')) {
    function POMassPayOutIsEnabled()
    {
        $POConfig = config('senprints.payoneer');
        if (is_null($POConfig) || !isset($POConfig['is_enabled'])) {
            return false;
        }
        return $POConfig['is_enabled'];
    }
}

if (!function_exists('POMassPayOutQuickTestingEnabled')) {
    function POMassPayOutQuickTestingEnabled()
    {
        $POConfig = config('senprints.payoneer');
        if (is_null($POConfig) || !isset($POConfig['quick_testing_enabled'])) {
            return false;
        }
        return $POConfig['quick_testing_enabled'];
    }
}

if (!function_exists('POMassPayOutQuickTestingFiveMinutesEnabled')) {
    function POMassPayOutQuickTestingFiveMinutesEnabled()
    {
        $POConfig = config('senprints.payoneer');
        if (is_null($POConfig) || !isset($POConfig['quick_testing_5m_enabled'])) {
            return false;
        }
        return $POConfig['quick_testing_5m_enabled'];
    }
}

if (!function_exists('POMassPayOutProdTesting')) {
    function POMassPayOutProdTesting()
    {
        $POConfig = config('senprints.payoneer');
        if (is_null($POConfig) || !isset($POConfig['production_testing'])) {
            return false;
        }
        return $POConfig['production_testing'];
    }
}

if (!function_exists('POMassPayOutProdTestingByIds')) {
    function POMassPayOutProdTestingByIds($userId = '')
    {
        $POConfig = config('senprints.payoneer');
        if (is_null($POConfig) || !isset($POConfig['production_testing_by_ids'])) {
            return false;
        }
        $testingUserIds = explode(',', $POConfig['production_testing_by_ids']);
        $testingUserIds = array_map('trim', $testingUserIds);
        $testingUserIds = array_filter($testingUserIds, function ($entry) {
            return $entry;
        });
        if (count($testingUserIds) < 1) {
            return false;
        }
        return (in_array($userId, $testingUserIds));
    }
}

if (!function_exists('POMassPayOutScheduleEnabled')) {
    function POMassPayOutScheduleEnabled()
    {
        $POConfig = config('senprints.payoneer');
        if (is_null($POConfig) || !isset($POConfig['production_schedule_enabled'])) {
            return false;
        }
        return $POConfig['production_schedule_enabled'];
    }
}

if (!function_exists('xml_entities')) {
    // https://www.php.net/manual/en/function.htmlentities.php#97215
    function xml_entities($strIn)
    {
        if (is_numeric($strIn)) {
            return $strIn;
        }
        $strOut = null;

        $arrStr = mb_str_split($strIn);
        foreach ($arrStr as $char) {
            $ord = mb_ord($char);

            if (($ord > 0 && $ord < 32) || ($ord >= 127)) {
                $strOut .= "&amp;#{$ord};";
            } else {
                switch ($char) {
                    case '<':
                        $strOut .= '&lt;';
                        break;
                    case '>':
                        $strOut .= '&gt;';
                        break;
                    case '&':
                        $strOut .= '&amp;';
                        break;
                    case '"':
                        $strOut .= '&quot;';
                        break;
                    default:
                        $strOut .= $char;
                }
            }
        }

        return $strOut;
    }
}

if (!function_exists('getPositionOfKeyInOptions')) {
    function getPositionOfKeyInOptions(string $key, array $options)
    {
        return array_search($key, array_keys($options));
    }
}

if (!function_exists('topTemplateIds')) {
    function topTemplateIds($seller_id = null, $daysAgo = 30): array
    {
        return cacheAlt()->remember(CacheKeys::TOP_PRODUCT_PREFIX . '-' . $seller_id . '-all', CacheKeys::CACHE_24H, static function () use ($seller_id, $daysAgo) {
            return OrderProduct::query()
                ->select(['order_product.template_id', DB::raw('count(order_product.template_id) as total_used')])
                ->join('order', 'order.id', '=', 'order_product.order_id')
                ->where('order.paid_at', '>=', today()->subDays($daysAgo))
                ->when(!empty($seller_id), function ($query) use ($seller_id) {
                    $query->where('order.seller_id', (int) $seller_id);
                })
                ->whereHas('template', function ($query) {
                    $query->where('status', ProductStatus::ACTIVE);
                })
                ->whereNull('order.deleted_at')
                ->whereNull('order_product.deleted_at')
                ->groupBy('order_product.template_id')
                ->orderByRaw('total_used DESC')
                ->pluck('order_product.template_id')
                ->toArray();
        });
    }
}

if (!function_exists('topSellerTemplateIds')) {
    function topSellerTemplateIds($seller_id, $allPlatform = true)
    {
        $topTemplateIds = topTemplateIds();
        if ($allPlatform) {
            return $topTemplateIds;
        }
        $topSellerTemplateIds = topTemplateIds($seller_id);
        return array_unique(array_merge($topSellerTemplateIds, $topTemplateIds));
    }
}

if (!function_exists('getCallBackUrlForFulfill')) {
    function getCallBackUrlForFulfill($supplierId): string
    {
        $domain  = SystemConfig::getConfig('webhook_domain', 'apis.dev.senprints.net');
        $request = new GenerateRequest(['referenceId' => $supplierId]);
        $token   = (new ApiKeyController())->generate($request, AccessExternalApiType::SUPPLIER, true);
        if (empty($token)) {
            throw new RuntimeException('Can not generate token for supplier: ' . $supplierId);
        }

        return 'https://' . $domain . '/fulfill/order?token=' . $token;
    }
}

if (!function_exists('getChangesDetailForModel')) {
    function getChangesDetailForModel($model): string
    {
        $detail = '';
        foreach ($model->getChanges() as $key => $value) {
            if ($key !== $model->getUpdatedAtColumn()) {
                $detail .= $key . ':' . $value . "|";
            }
        }

        return $detail;
    }
}

if (!function_exists('getChangesDetailForOrder')) {
    function getChangesDetailForOrder($model, array $oldOrder): string
    {
        $details = [];
        // must compare with old order
        foreach ($model->getChanges() as $key => $value) {
            if ($key !== $model->getUpdatedAtColumn()) {
                $details[] = '<li>' . $key . ': "' . $oldOrder[$key] . '" changed to => "' . $value . '"</li>';
            }
        }

        // Join the details array into a string wrapped in <ul> tag
        return '<ul>' . implode("", $details) . '</ul>';
    }

}

if (!function_exists('calcTopupAmountWithFee')) {
    function calcTopupAmountWithFee($amount = 0, $topupFee = 0)
    {
        return (($amount * (float)$topupFee) / 100) + $amount;
    }
}

if (!function_exists('shuffle_assoc')) {
    function shuffle_assoc($list)
    {
        if (!is_array($list)) {
            return $list;
        }

        $keys = array_keys($list);
        shuffle($keys);
        $random = [];
        foreach ($keys as $key) {
            $random[] = $list[$key];
        }

        return $random;
    }
}

if (!function_exists('getIp')) {
    function getIp($request = null): ?string
    {
        if (!$request) {
            $request = request();
        }

        return $request->header(
            'X-Client-IP',
            $request->header(
                'sp-connecting-ip',
                $request->header('cf-connecting-ip', $request->ip())
            )
        );
    }
}

if (!function_exists('model_map')) {
    function model_map(&$model, callable $function): object
    {
        foreach ($model->toArray() as $key => $val) {
            $model->$key = $function($val);
        }

        return $model;
    }
}

if (!function_exists('array_map_func')) {
    /**
     * This function is same as array_map,
     * but we don't need to assign to variable
     */
    function array_map_func(array &$arr, callable $function): array
    {
        // note: don't return array_map($function, $arr)
        // we need to assign to $arr first to make sure it's reference
        $arr = array_map($function, $arr);
        return $arr;
    }
}

if (!function_exists('get_config_pb')) {
    function get_config_pb()
    {
        $configPBJson = SystemConfig::getCustomConfig('config_pb');

        if (is_null($configPBJson)) {
            return null;
        }

        return json_decode($configPBJson->json_data, true);
    }
}

if (!function_exists('generateDirectCloudMockupLink')) {
    function generateDirectCloudMockupLink($templateId, $designId, $color = 'FFFFFF'): string
    {
        $params = ['template' => $templateId, 'c' => $color,];

        if ($designId) {
            $params['id'] = $designId;
        }

        return CloudMockupApis::RENDER_DIRECT . '?' . http_build_query($params);
    }
}

if (!function_exists('implodeWithKeys')) {
    // only for array one dimensional
    function implodeWithKeys(
        array $array,
        string $glue = ':',
        string $separate = '|',
        array $onlyKeys = []
    ): string {
        $flattened = [];

        foreach ($array as $key => $val) {
            if (is_array($val)) {
                continue;
            }
            if (!empty($onlyKeys) && !in_array($key, $onlyKeys)) {
                continue;
            }
            $flattened[] = $key . $glue . $val;
        }

        return implode($separate, $flattened);
    }
}

if (!function_exists('cloneQueryWhere')) {
    function cloneQueryWhere(&$q, $query): void
    {
        foreach ($query->clone()->getQuery()->wheres as $eachWhere) {
            if ($eachWhere['boolean'] === 'or') {
                throw new RuntimeException('Unsupported orWhere');
            }
            switch ($eachWhere['type']) {
                default:
                    throw new RuntimeException('Unsupported where type: ' . $eachWhere['type']);
                case 'Basic':
                    $q->where($eachWhere['column'], $eachWhere['operator'], $eachWhere['value']);
                    break;
                case 'NotIn':
                    $q->whereNotIn($eachWhere['column'], $eachWhere['values']);
                    break;
                case 'NotNull':
                    $q->whereNotNull($eachWhere['column']);
                    break;
                case 'between':
                    $q->whereBetween($eachWhere['column'], $eachWhere['values']);
                    break;
                case 'In':
                    $q->whereIn($eachWhere['column'], $eachWhere['values']);
                    break;
                case 'Null':
                    $q->whereNull($eachWhere['column']);
                    break;
            }
        }
    }
}

if (!function_exists('checkLoadMarketPlace')) {
    function checkLoadMarketPlace($store): bool
    {
        return $store->id === Store::SENPRINTS_STORE_ID || $store->market_place_upsell;
    }
}

if (!function_exists('visitByStoreId')) {
    function visitByStoreId($storeId): int
    {
        if (empty($storeId)) {
            return 0;
        }
        return Cache::remember(CacheKeys::STORE_VISITS_PREFIX . $storeId, CacheKeys::CACHE_1H, static function () use ($storeId) {
            $filters            = [
                'store_id' => $storeId,
            ];
            $dateRanges['type'] = DateRangeEnum::LAST_24_HOURS;

            return TempEventLog::query()
                ->calculateCount()
                ->filterVisit()
                ->addFilterAnalytic($filters, $dateRanges)
                ->value('count');
        });
    }
}

if (!function_exists('generateHeadersCacheHTMLTagByCampaignIds')) {
    function generateHeadersCacheHTMLTagByCampaignIds(array $campaignIds, $cacheTime = CacheTime::CACHE_24H, string $tags = null): array
    {
        $tags ??= '';
        $campaignIds = array_unique($campaignIds);
        foreach ($campaignIds as $campaignId) {
            if ($tags !== '') {
                $tags .= ',';
            }
            $tags .= 'campaign=' . $campaignId;
        }

        return [
            'Cache-Tags'        => $tags,
            'Cache-Expire-Time' => $cacheTime,
        ];
    }
}


if (!function_exists('array_filter_empty')) {
    function array_filter_empty(array $arr): array
    {
        return array_values(array_filter($arr, static function ($each) {
            return $each && $each !== 'null';
        }));
    }
}

if (!function_exists('get_currency_by_code')) {
    /**
     * @param $currencyCode
     * @return float
     */
    function get_currency_by_code($currencyCode): float
    {
        $currencyRate = 1;
        $currencies = StoreService::systemCurrencies();
        if ($currencies) {
            $findCode = $currencies->firstWhere('code', $currencyCode);
            if ($findCode) {
                $currencyRate = (float) $findCode->rate;
            }
        }
        return $currencyRate;
    }
}

if (!function_exists('get_base_min_max_price_of_product')) {
    function get_base_min_max_price_of_product($productTemplate, $marketLocation, $currencyCode = 'USD'): array
    {
        $rate = get_currency_by_code(Str::upper($currencyCode));
        $location = getLocationByCode($marketLocation);
        $baseCost = getBaseCostsByLocation($productTemplate, $location);
        $minPrice = floor(min($baseCost * 1.5, $productTemplate->price) * $rate);
        $maxPrice = floor(max($baseCost * 1.5 + $productTemplate->extra_print_cost, $productTemplate->price + $productTemplate->extra_print_cost) * $rate) * 3;

        return [
            'base_cost' => $baseCost,
            'min_price' => $minPrice,
            'max_price' => $maxPrice,
        ];
    }
}

if (!function_exists('get_team_seller_stores')) {
    function get_team_seller_stores($sellerId, $memberId, $currentUser = null, $getOriginalStoreIds = false): array|null
    {
        $storeIds = [];

        if (empty($sellerId) || empty($memberId)) {
            return $storeIds;
        }

        $sellerTeam = SellerTeam::query()
            ->select(['seller_id', 'store_ids'])
            ->where([
                'member_id' => $memberId,
                'seller_id' => $sellerId,
                'status' => SellerTeamStatusEnum::ACCEPTED,
            ])
            ->first();

        if (empty($sellerTeam)) {
            return $storeIds;
        }

        if ($getOriginalStoreIds && $sellerTeam->store_ids === null) {
            return $sellerTeam->store_ids;
        }
        if (!empty($currentUser) &&
            $currentUser->role == UserRoleEnum::SELLER &&
            empty($sellerTeam->store_ids)
        ) {
            $sellerStoreIds = Store::query()
                ->select('id')
                ->where('seller_id', $sellerId)
                ->get()
                ->pluck('id')
                ->toArray();
        } else {
            if (empty($sellerTeam->store_ids)) {
                return $storeIds;
            }
            $sellerStoreIds = explode(',', $sellerTeam->store_ids);
        }

        foreach ($sellerStoreIds as $store_id) {
            $storeIds[] = (int) $store_id;
        }

        return array_unique($storeIds);
    }
}

// https://stackoverflow.com/a/42037557/9029340
if (!function_exists('validateEmail')) {
    function validateEmail(string $email): bool
    {
        if (!Str::contains($email, '@')) {
            return false;
        }

        $arr = explode('@', $email);
        [$user, $domain] = $arr;

        return count($arr) === 2 &&
            !empty($user) &&
            !empty($domain);
    }
}

if (!function_exists('getAverageProcessingDay')) {
    function getAverageProcessingDay($templateId, $supplierId = null): int
    {
        $tags = CacheKeys::getProductTemplateTags($templateId);
        return cacheAlt()->tags($tags)->remember(
            CacheKeys::getAverageProcessingDay($templateId, $supplierId),
            CacheKeys::CACHE_24H,
            function () use ($templateId, $supplierId) {
                return (int)
                round(
                    OrderProduct::query()
                        ->selectRaw('AVG(processing_day) as average')
                        ->where('fulfill_status', OrderProductFulfillStatus::FULFILLED)
                        ->where('processing_day', '>', 0)
                        ->where('delivered_at', '>=', now()->subDays(30))
                        ->where('template_id', $templateId)
                        ->when($supplierId, fn ($query) => $query->where('supplier_id', $supplierId))
                        ->havingRaw('count(order_id) > 10')
                        ->value('average')
                );
            }
        );
    }
}

if (!function_exists('getShippingTime')) {
    function getShippingTime($location, $shippingMethod): array
    {
        $obj = cache()->remember(
            CacheKeys::getShippingTime($location, $shippingMethod),
            CacheKeys::CACHE_24H,
            function () use ($location, $shippingMethod) {
                $orderSub = Order::query()
                    ->select('id')
                    ->where('shipping_method', $shippingMethod)
                    ->where('delivered_at', '>=', now()->subDays(30));

                switch ($location) {
                    case '*':
                        break;
                    case '150':
                        $systemCountries = SystemLocation::systemCountries();
                        $countries       = $systemCountries
                            ->where('region_code', $location)
                            ->pluck('code');
                        $orderSub->whereIn('country', $countries);
                        break;
                    default:
                        $orderSub->where('country', $location);
                        break;
                }

                return OrderProduct::query()
                    ->selectRaw('MIN(shipping_day) as min')
                    ->selectRaw('AVG(shipping_day) as avg')
                    ->joinSub($orderSub, 'o', 'o.id', 'order_product.order_id')
                    ->where('fulfill_status', OrderProductFulfillStatus::FULFILLED)
                    ->where('shipping_day', '>', 0)
                    ->where('delivered_at', '>=', now()->subDays(30))
                    ->havingRaw('count(order_id) >= 3')
                    ->first() ?? false;
            }
        );

        $rate = SystemConfig::getConfig('shipping_time_rate', 1.5);
        if ($obj === false) {
            if ($shippingMethod === ShippingMethodEnum::STANDARD) {
                return [];
            }

            $arr = getShippingTime($location, ShippingMethodEnum::STANDARD);
            if (empty($arr)) {
                return [];
            }

            return [
                $arr[0],
                (int)round($arr[1] / $rate),
            ];
        }

        return [
            $obj->min,
            (int)round($obj->avg * $rate),
        ];
    }
}


if (!function_exists('replacePBArtworkURL')) {
    function replacePBArtworkURL(string $url, $hash): string
    {
        return preg_replace('/([^\/]+)\.(\w+)$/', $hash . ".$2", $url);
    }
}

if (!function_exists('calcPercentOfAmount')) {
    /**
     * @param $percent
     * @param $amount
     * @return float|int
     */
    function calcPercentOfAmount($percent, $amount)
    {
        return ($percent / 100) * abs($amount);
    }
}

if (!function_exists('getSlugByCampaignUrls')) {
    function getSlugByCampaignUrls(array $urls): array
    {
        $campaignUrls = [];
        foreach ($urls as $urlIndex => $url) {
            $urlInfo = parse_url($url);
            $originDomain = sprintf('%s://%s/', $urlInfo['scheme'], $urlInfo['host']);
            $pathCount = count(explode('/', $urlInfo['path']));
            $domainPattern = '.' . getStoreBaseDomain();
            if ($pathCount == 2) {
                $host = $urlInfo['host'];
                $isSubdomain = false;
                if (strpos($host, $domainPattern) > 0) {
                    preg_match("/^([a-zA-Z0-9-]+)\./", $host, $matches);
                    if (count($matches) > 0) {
                        $isSubdomain = true;
                        $host = strtolower(trim($matches[1]));
                    }
                }
                $campaignSlug = str_replace('/', '', $urlInfo['path']);
                if (!empty($campaignSlug)) {
                    if (!empty($campaignUrls[$host]['campaigns']) && in_array($campaignSlug, $campaignUrls[$host]['campaigns'])) {
                        continue;
                    }
                    $campaignUrls[$host]['subdomain'] = $isSubdomain;
                    $campaignUrls[$host]['originDomain'] = $originDomain;
                    $campaignUrls[$host]['campaigns'][] = $campaignSlug;
                }
            }
            unset($urls[$urlIndex]);
        }
        return $campaignUrls;
    }
}

if (!function_exists('removeCurrency')) {
    function removeCurrency($str)
    {
        if (empty($str)) {
            return 0;
        }

        return (float)Str::substr($str, 1);
    }
}

if (!function_exists('isEnvLocalOrDev')) {
    function isEnvLocalOrDev(): bool
    {
        // need check env not app()->environment() cause using it in config
        return in_array(env('APP_ENV'), [
            EnvironmentEnum::TESTING,
            EnvironmentEnum::LOCAL,
            EnvironmentEnum::DEVELOPMENT,
        ], true);
    }
}

if (!function_exists('isEnvTesting')) {
    function isEnvTesting(): bool
    {
        return app()->environment(EnvironmentEnum::TESTING);
    }
}

if (!function_exists('gen_unique_hash')) {
    function gen_unique_hash(): string
    {
        return md5(Str::uuid() . Str::random(10));
    }
}

if (!function_exists('md5_array')) {
    function md5_array(array $data): string
    {
        return md5(implode('', $data));
    }
}


if (!function_exists('get_env')) {
    function get_env($envKey, $default = null)
    {
        $envValue = env($envKey, $default);
        if (empty($envValue)) {
            return $default;
        }
        return $envValue;
    }
}

if (!function_exists('filterQueryDateBeforeRange')) {
    function filterQueryDateBeforeRange(array $dateRange, $query = null, $sellerId = null)
    {
        // if don't filter by date range. ex: lifetime
        if (empty($dateRange['type'])) {
            return $query;
        }
        $hourOffset = getHourOffsetBySeller($sellerId);
        $today = now()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset);

        // method copy() to clone variable
        switch ($dateRange['type']) {
            case DateRangeEnum::LIFE_TIME:
                return $query->where($dateRange['column'], '<', now());
            case DateRangeEnum::CUSTOM:
                try {
                    $dateRange['range'] = array_filter($dateRange['range'] ?? []);
                    if (count($dateRange['range']) !== 2) {
                        return $query;
                    }

                    $startDate = Carbon::parse($dateRange['range'][0]);
                    $endDate   = Carbon::parse($dateRange['range'][1]);

                    if (empty($dateRange['is_timezone'])) {
                        $startDate->startOfDay()->subRealHours($hourOffset);
                        $endDate->endOfDay()->subRealHours($hourOffset);
                    }

                    $time = [$startDate, $endDate];
                    if (is_null($query)) {
                        return $time;
                    }

                    return $query->where($dateRange['column'], '<', $time[0]);
                } catch (Throwable $e) {
                    graylogError('filterQueryDateBeforeRange error with custom daterange: ', [
                        'category' => 'filterQueryDateBeforeRange_error_log',
                        'message' => $e,
                    ]);
                    return $query;
                }
            default:
            case DateRangeEnum::TODAY:
                if (is_null($query)) {
                    return $today;
                }

                return $query->where($dateRange['column'], '<', $today); // first hour of this day
            case DateRangeEnum::YESTERDAY:
                $time = [
                    $today->copy()->subDays(),
                    $today->copy()->subSecond()
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
            case DateRangeEnum::LAST_HOUR:
                $time = now()->subHour()->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_24_HOURS:
                $time = now()->subHours(24)->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_3_DAYS:
                $time = now()->subDays(3)->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_7_DAYS:
                $time = now()->subDays(7)->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_14_DAYS:
                $time = now()->subDays(14)->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_30_DAYS:
                $time = now()->subDays(30)->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::THIS_WEEK:
                $time = $today->startOfWeek()->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_WEEK:
                $time = [
                    $today->copy()->subWeeks()->startOfWeek()->subRealHours($hourOffset),
                    $today->copy()->subWeeks()->endOfWeek()->subRealHours($hourOffset)
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
            case DateRangeEnum::THIS_MONTH:
                $time = $today->startOfMonth()->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                // start day of this month
                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_MONTH:
                $time = [
                    $today->copy()->startOfMonth()->subMonthsWithNoOverflow()->subRealHours($hourOffset),
                    $today->copy()->subMonthsWithNoOverflow()->endOfMonth()->subRealHours($hourOffset)
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
            case DateRangeEnum::LAST_QUARTER:
                $time = [
                    $today->copy()->startOfQuarter()->subQuarterNoOverflow()->subRealHours($hourOffset),
                    $today->copy()->subQuarterNoOverflow()->endOfQuarter()->subRealHours($hourOffset)
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
            case DateRangeEnum::THIS_YEAR:
                $time = $today->startOfYear()->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::THIS_QUARTER:
                $time = $today->startOfQuarter()->subRealHours($hourOffset);
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_YEAR:
                $time = [
                    $today->copy()->subYearsWithNoOverflow()->startOfYear()->subRealHours($hourOffset),
                    $today->copy()->subYearsWithNoOverflow()->endOfYear()->subRealHours($hourOffset)
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
        }
    }
}

if (!function_exists('make_order_prefix')) {
    /**
     * @param string|null $suffix
     * @param array       $default
     *
     * @return string
     */
    function make_order_prefix(?string $suffix = null, array $default = ['SP']): string
    {
        if ($suffix) {
            $default = [$suffix];
        }

        return implode('-', [...$default, '']);
    }
}

if (!function_exists('filterQueryDateBeforeRangeWithSubMonth')) {
    function filterQueryDateBeforeRangeWithSubMonth(array $dateRange, $query = null, $sellerId = null, $submonth = 0)
    {
        // if don't filter by date range. ex: lifetime
        if (empty($dateRange['type'])) {
            return $query;
        }
        $hourOffset = getHourOffsetBySeller($sellerId);
        $today = now()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset);

        // method copy() to clone variable
        switch ($dateRange['type']) {
            case DateRangeEnum::LIFE_TIME:
                return $query->where($dateRange['column'], '<', now()->subMonths($submonth)->firstOfMonth()->toDateTimeString());
            case DateRangeEnum::CUSTOM:
                try {
                    $dateRange['range'] = array_filter($dateRange['range'] ?? []);
                    if (count($dateRange['range']) !== 2) {
                        return $query;
                    }

                    $startDate = Carbon::parse($dateRange['range'][0]);
                    $endDate   = Carbon::parse($dateRange['range'][1]);

                    if (empty($dateRange['is_timezone'])) {
                        $startDate->startOfDay()->subRealHours($hourOffset);
                        $endDate->endOfDay()->subRealHours($hourOffset);
                    }

                    $startDate = $startDate->copy()->subMonths($submonth)->toDateTimeString();
                    $endDate = $endDate->copy()->subMonths($submonth)->toDateTimeString();

                    $time = [$startDate, $endDate];
                    if (is_null($query)) {
                        return $time;
                    }

                    return $query->where($dateRange['column'], '<', $time[0]);
                } catch (Throwable $e) {
                    graylogError('filterQueryDateBeforeRange error with custom daterange: ', [
                        'category' => 'filterQueryDateBeforeRange_error_log',
                        'message' => $e,
                    ]);
                    return $query;
                }
            default:
            case DateRangeEnum::TODAY:
                if (is_null($query)) {
                    return $today;
                }

                return $query->where($dateRange['column'], '<', Carbon::parse($today)->subMonths($submonth)->firstOfMonth()->toDateString()); // first hour of this day
            case DateRangeEnum::YESTERDAY:
                $time = [
                    $today->copy()->subDays()->copy()->subMonths($submonth)->toDateTimeString(),
                    $today->copy()->subSecond()->copy()->subMonths($submonth)->toDateTimeString()
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
            case DateRangeEnum::LAST_HOUR:
                $time = now()->subHour()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_24_HOURS:
                $time = now()->subHours(24)->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_3_DAYS:
                $time = now()->subDays(3)->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_7_DAYS:
                $time = now()->subDays(7)->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_14_DAYS:
                $time = now()->subDays(14)->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_30_DAYS:
                $time = now()->subDays(30)->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::THIS_WEEK:
                $time = $today->startOfWeek()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_WEEK:
                $time = [
                    $today->copy()->subWeeks()->startOfWeek()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString(),
                    $today->copy()->subWeeks()->endOfWeek()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString()
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
            case DateRangeEnum::THIS_MONTH:
                $time = $today->startOfMonth()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                // start day of this month
                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_MONTH:
                $time = [
                    $today->copy()->startOfMonth()->subMonthsWithNoOverflow()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString(),
                    $today->copy()->subMonthsWithNoOverflow()->endOfMonth()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString()
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
            case DateRangeEnum::LAST_QUARTER:
                $time = [
                    $today->copy()->startOfQuarter()->subQuarterNoOverflow()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString(),
                    $today->copy()->subQuarterNoOverflow()->endOfQuarter()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString()
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
            case DateRangeEnum::THIS_YEAR:
                $time = $today->startOfYear()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::THIS_QUARTER:
                $time = $today->startOfQuarter()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->where($dateRange['column'], '<', $time);
            case DateRangeEnum::LAST_YEAR:
                $time = [
                    $today->copy()->subYearsWithNoOverflow()->startOfYear()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString(),
                    $today->copy()->subYearsWithNoOverflow()->endOfYear()->subRealHours($hourOffset)->copy()->subMonths($submonth)->toDateTimeString()
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->where($dateRange['column'], '<', $time[0]);
        }
    }
}


if (!function_exists('filterQueryDateInRangeWithRound')) {
    function filterQueryDateInRangeWithRound(array $dateRange, $query = null, $sellerId = null, $dayRange = 1, $submonths = null)
    {
        // if don't filter by date range. ex: lifetime
        if (empty($dateRange['type'])) {
            return $query;
        }
        $hourOffset = getHourOffsetBySeller($sellerId);
        $today = now()->daysInMonth == 31 ? now()->subDay()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset) : now()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset);
        // method copy() to clone variable

        switch ($dateRange['type']) {
            case DateRangeEnum::LIFE_TIME:
                $time = now()->subDays($dayRange);
                $time = !$submonths ? $time->toDateTimeString() :  $time->subMonths($submonths)->toDateTimeString();
                return $query->where($dateRange['column'], '>', $time);
            case DateRangeEnum::CUSTOM:
                try {
                    $dateRange['range'] = array_filter($dateRange['range'] ?? []);
                    if (count($dateRange['range']) !== 2) {
                        return $query;
                    }

                    $startTime = Carbon::parse($dateRange['range'][0])->startOfDay()->subRealHours($hourOffset);

                    $startDate = $startTime->copy()->subDays($dayRange);

                    $endDate   = $startTime->copy()->endOfDay();
                    $time = [$startDate, $endDate];

                    if (is_null($query)) {
                        return $time;
                    }

                    return $query->whereBetween($dateRange['column'], $time);
                } catch (Throwable $e) {
                    graylogError('filterQueryDateBeforeRange error with custom daterange: ', [
                        'category' => 'filterQueryDateBeforeRange_error_log',
                        'message' => $e,
                    ]);
                    return $query;
                }
            default:
            case DateRangeEnum::TODAY:
                if (is_null($query)) {
                    return $today;
                }

                $startDate = $today->copy()->subDays($dayRange);
                $endDate = $today;
                $startDate = !$submonths ? $startDate->toDateTimeString() : $startDate->subMonths($submonths)->toDateTimeString();
                $endDate = !$submonths ? $endDate->toDateTimeString() : $endDate->subMonths($submonths)->toDateTimeString();
                $time = [
                    $startDate,
                    $endDate
                ];
                return $query->whereBetween($dateRange['column'], $time); // first hour of this day
            case DateRangeEnum::YESTERDAY:
                $time = [
                    $today->copy()->subDays()->subDays($dayRange),
                    $today->copy()->subDays()
                ];
                $time =  !$submonths ? [
                    $time[0]->toDateTimeString(),
                    $time[1]->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_HOUR:
                $time = now()->subHour();
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = $time->copy();
                $startDate = !$submonths ? $startDate->toDateTimeString() : $startDate->subMonths($submonths)->toDateTimeString();
                $endDate = !$submonths ? $endDate->toDateTimeString() : $endDate->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }

                $time = [$startDate, $endDate];

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_24_HOURS:
                $time = now()->subHours(24);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = $time->copy();
                $startDate = !$submonths ? $startDate->toDateTimeString() : $startDate->subMonths($submonths)->toDateTimeString();
                $endDate = !$submonths ? $endDate->toDateTimeString() : $endDate->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];

                return $query->whereBetween($dateRange['column'],  $time);
            case DateRangeEnum::LAST_3_DAYS:
                $time = now()->subDays(3);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = $time->copy();
                $startDate = !$submonths ? $startDate->toDateTimeString() : $startDate->subMonths($submonths)->toDateTimeString();
                $endDate = !$submonths ? $endDate->toDateTimeString() : $endDate->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];
                return $query->whereBetween($dateRange['column'],  $time);
            case DateRangeEnum::LAST_7_DAYS:
                $time = now()->subDays(7)->subRealHours($hourOffset);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = $time->copy();
                $startDate = !$submonths ? $startDate->toDateTimeString() : $startDate->subMonths($submonths)->toDateTimeString();
                $endDate = !$submonths ? $endDate->toDateTimeString() : $endDate->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_14_DAYS:
                $time = now()->subDays(14);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = $time->copy();
                $startDate = !$submonths ? $startDate->toDateTimeString() : $startDate->subMonths($submonths)->toDateTimeString();
                $endDate = !$submonths ? $endDate->toDateTimeString() : $endDate->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_30_DAYS:
                $time = now()->subDays(30)->subRealHours($hourOffset);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = $time->copy();
                $startDate = !$submonths ? $startDate->toDateTimeString() : $startDate->subMonths($submonths)->toDateTimeString();
                $endDate = !$submonths ? $endDate->toDateTimeString() : $endDate->subMonths($submonths)->toDateTimeString();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_WEEK:
                $today = $today->startOfWeek()->subRealHours($hourOffset);
                $time = [
                    $today->copy()->subDays($dayRange),
                    $today->copy(),
                ];

                $time =  !$submonths ? [
                    $time[0]->toDateTimeString(),
                    $time[1]->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];

                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_WEEK:
                $lastWeek = $today->subWeek();
                $time = [
                    $lastWeek->copy()->startOfWeek()->subDays($dayRange)->subRealHours($hourOffset),
                    $lastWeek->copy()->endOfWeek()->subWeek()->subRealHours($hourOffset),
                ];
                $time =  !$submonths ? [
                    $time[0]->toDateTimeString(),
                    $time[1]->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_MONTH:
                $time = [
                    $today->copy()->startOfMonth()->subRealHours($hourOffset)->subDays($dayRange),
                    $today->copy()->startOfMonth()->subRealHours($hourOffset),
                ];
                $time =  !$submonths ? [
                    $time[0]->toDateTimeString(),
                    $time[1]->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }
                // start day of this month
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_MONTH:
                $time = [
                    $today->copy()->startOfMonth()->subMonth()->subRealHours($hourOffset)->subDays($dayRange),
                    $today->copy()->startOfMonth()->subMonth()->subRealHours($hourOffset),
                ];
                $time =  !$submonths ? [
                    $time[0]->toDateTimeString(),
                    $time[1]->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_QUARTER:
                $time = [
                    $today->copy()->startOfQuarter()->subQuarterNoOverflow()->subRealHours($hourOffset)->subDays($dayRange),
                    $today->copy()->subQuarterNoOverflow()->endOfQuarter()->subRealHours($hourOffset)->subDays($dayRange)
                ];
                $time =  !$submonths ? [
                    $time[0]->toDateTimeString(),
                    $time[1]->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }


                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_YEAR:
                $thisYear = $today->startOfYear();
                $time = [
                    $thisYear->copy()->subYear()->startOfYear()->subRealHours($hourOffset),
                    $thisYear->copy()->startOfYear()->subRealHours($hourOffset)
                ];

                $time =  !$submonths ? [
                    $time[1]->copy()->subDays($dayRange)->toDateTimeString(),
                    $time[1]->copy()->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_QUARTER:
                $thisQuarter = $today->startOfQuarter();
                $time = [
                    $thisQuarter->copy()->subDays($dayRange)->startOfQuarter()->subRealHours($hourOffset),
                    $thisQuarter->copy()->startOfQuarter()->subRealHours($hourOffset)
                ];
                $time =  !$submonths ? [
                    $time[0]->toDateTimeString(),
                    $time[1]->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_YEAR:
                $time = [
                    $today->copy()->subYears(2)->startOfYear()->subRealHours($hourOffset),
                    $today->copy()->subYears(2)->endOfYear()->subRealHours($hourOffset)
                ];
                $time =  !$submonths ? [
                    $time[1]->copy()->subDays($dayRange)->toDateTimeString(),
                    $time[1]->copy()->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_6_MONTHS:
                $time = [
                    $today->copy()->subMonths(6)->startOfMonth()->subRealHours($hourOffset),
                    $today->copy()->subMonth()->endOfMonth()->subRealHours($hourOffset)
                ];
                $time =  !$submonths ? [
                    $time[1]->copy()->subDays($dayRange)->toDateTimeString(),
                    $time[1]->copy()->toDateTimeString()
                ] : [
                    $time[0]->subMonths($submonths)->toDateTimeString(),
                    $time[1]->subMonths($submonths)->toDateTimeString(),
                ];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
        }
    }
}

if (!function_exists('adjustPrintSpace')) {
    function adjustPrintSpace($printSpace)
    {
        if (empty($printSpace)) {
            return $printSpace;
        }
        $size                     = 500;
        $scale                    = $size / max($printSpace['width'], $printSpace['height']);
        $printSpace['width']      = round($printSpace['width'] * $scale, 1);
        $printSpace['height']     = round($printSpace['height'] * $scale, 1);
        $printSpace['viewWidth']  = !empty($printSpace['viewWidth'])
            ? round($printSpace['viewWidth'] * $scale, 1)
            : 0;
        $printSpace['viewHeight'] = !empty($printSpace['viewHeight'])
            ? round($printSpace['viewHeight'] * $scale, 1)
            : 0;
        $printSpace['left']       = $printSpace['viewWidth'] > 0 ? round(
            ($printSpace['width'] - $printSpace['viewWidth']) / 2,
            1
        ) : 0;
        $printSpace['top']        = $printSpace['viewHeight'] > 0 ? round(
            ($printSpace['height'] - $printSpace['viewHeight']) / 2,
            1
        ) : 0;
        return $printSpace;
    }
}

if (! function_exists('suppliers')) {
    /**
     * @return Collection extends {
     *     validateBillingImporter(int $supplierId): string|null
     * }
     */
    function suppliers(): Collection
    {
        return tap(
            collect(config('supplier'))->values(),
            function(Collection $configs) {
                // Register macro to get validate billing importer by supplier id
                $configs->macro('validateBillingImporterClass', function(int $supplierId) {
                    return data_get($this->firstWhere('supplier_id', $supplierId),'validate_billing_importer');
                });
            }
        );
    }
}

if (! function_exists('is_supplier_support_api')) {
    /**
     * @param $supplier
     * @return bool
     */
    function is_supplier_support_api($supplier): bool
    {
        if (is_numeric($supplier)) {
            $supplier = suppliers()->firstWhere('supplier_id', $supplier);
        }

        if (! $supplier || empty(data_get($supplier, 'supplier_id'))) {
            return false;
        }
        return data_get($supplier, 'api') || data_get($supplier, 'production.api') || data_get($supplier, 'dev.api');
    }
}

if (!function_exists('filterQueryDateInRangeWithSameDiffInPass')) {
    function filterQueryDateInRangeWithSameDiffInPass(array $dateRange, $query = null)
    {
        // if don't filter by date range. ex: lifetime
        if (empty($dateRange['type'])) {
            return $query;
        }
        $hourOffset = getHourOffsetBySeller(null);
        $today = now()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset);
        // method copy() to clone variable
        switch ($dateRange['type']) {
            case DateRangeEnum::CUSTOM:
                try {
                    $dateRange['range'] = array_filter($dateRange['range'] ?? []);
                    if (count($dateRange['range']) !== 2) {
                        return $query;
                    }

                    $startDate = Carbon::parse($dateRange['range'][0]);
                    $endDate   = Carbon::parse($dateRange['range'][1]);

                    $diffDate = $startDate->diffInDays($endDate);
                    $time = [$startDate->copy()->subDay()->subDays($diffDate)->startOfDay()->subRealHours($hourOffset)->toDateTimeString(), $startDate->copy()->subDay()->endOfDay()->subRealHours($hourOffset)->toDateTimeString()];
                    // dd($time);
                    if (is_null($query)) {
                        return $time;
                    }
                    return $query->whereBetween($dateRange['column'], $time);
                } catch (Throwable $e) {
                    graylogError('filterQueryDateBeforeRange error with custom daterange: ', [
                        'category' => 'filterQueryDateBeforeRange_error_log',
                        'message' => $e,
                    ]);
                    return $query;
                }
            default:
            case DateRangeEnum::TODAY:
                if (is_null($query)) {
                    return $today;
                }

                $yesterday = $today->copy()->subDay();
                $time = [$yesterday, $today];
                return $query->whereBetween($dateRange['column'], $time); // first hour of this day
            case DateRangeEnum::YESTERDAY:
                $dayBeforeYesterday = Carbon::parse($today)->subDays(2);
                $time = [
                    $dayBeforeYesterday->copy()->startOfDay()->subRealHours($hourOffset)->toDateTimeString(),
                    $dayBeforeYesterday->copy()->endOfDay()->subRealHours($hourOffset)->toDateTimeString()
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_HOUR:
                $hourBeforeLastHour = now()->subHours(2);
                $startDate = $hourBeforeLastHour->copy()->startOfHour()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $hourBeforeLastHour->copy()->endOfHour()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_24_HOURS:
                $time = now()->subHours(24);
                $startDate = $time->copy()->subHours(24)->startOfHour()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $time->copy()->subHour()->endOfHour()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];

                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'],  $time);
            case DateRangeEnum::LAST_3_DAYS:
                $time = now()->subDays(3);
                $startDate = $time->copy()->subDays(3)->startOfDay()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $time->copy()->subDay()->endOfDay()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];

                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'],  $time);
            case DateRangeEnum::LAST_7_DAYS:
                $time = now()->subDays(7);
                $startDate = $time->copy()->subDays(7)->startOfDay()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $time->copy()->subDay()->endOfDay()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];

                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_14_DAYS:
                $time = now()->subDays(14);
                $startDate = $time->copy()->subDays(14)->startOfDay()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $time->copy()->subDay()->endOfDay()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];

                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_30_DAYS:
                $time = now()->subDays(30);
                $startDate = $time->copy()->subDays(30)->startOfDay()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $time->copy()->subDay()->endOfDay()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_WEEK:
                $lastWeek = $today->subWeek();
                $startDate = $lastWeek->copy()->startOfWeek()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $lastWeek->copy()->endOfWeek()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_WEEK:
                $weekBeforeLastWeek  = $today->subWeeks(2);
                $startDate = $weekBeforeLastWeek->copy()->startOfWeek()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $weekBeforeLastWeek->copy()->endOfWeek()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];

                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_MONTH:
                $lastMonth = $today->subMonth();
                $startDate = $lastMonth->copy()->startOfMonth()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $lastMonth->copy()->endOfMonth()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_MONTH:
                $lastMonth = $today->subMonths(2);
                $startDate = $lastMonth->copy()->startOfMonth()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $lastMonth->copy()->endOfMonth()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_QUARTER:
                $lastQuarter = $today->subQuarter();
                $startDate = $lastQuarter->copy()->startOfQuarter()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $lastQuarter->copy()->endOfQuarter()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_QUARTER:
                $quarterBeforeLastQuarter = $today->subQuarters(2);
                $startDate = $quarterBeforeLastQuarter->copy()->startOfQuarter()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $quarterBeforeLastQuarter->copy()->endOfQuarter()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_YEAR:
                $lastYear = $today->subYear();
                $startDate = $lastYear->copy()->startOfYear()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $lastYear->copy()->endOfYear()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);

            case DateRangeEnum::LAST_YEAR:
                $yearBeforeLastYear = $today->subYears(2);
                $startDate = $yearBeforeLastYear->copy()->startOfYear()->subRealHours($hourOffset)->toDateTimeString();
                $endDate = $yearBeforeLastYear->copy()->endOfYear()->subRealHours($hourOffset)->toDateTimeString();
                $time = [$startDate, $endDate];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
        }
    }
}

if (!function_exists('filterQueryDateInPreviousRangeToCurrentEndDate')) {
    function filterQueryDateInPreviousRangeToCurrentEndDate(array $dateRange, $query = null, $sellerId = null, $dayRange = 1)
    {
        // if don't filter by date range. ex: lifetime
        if (empty($dateRange['type'])) {
            return $query;
        }
        $hourOffset = getHourOffsetBySeller($sellerId);
        $today = now()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset);
        // method copy() to clone variable

        switch ($dateRange['type']) {
            case DateRangeEnum::LIFE_TIME:
                $time = now()->subDays($dayRange);
                return $query->where($dateRange['column'], '>', $time);
            case DateRangeEnum::CUSTOM:
                try {
                    $dateRange['range'] = array_filter($dateRange['range'] ?? []);
                    if (count($dateRange['range']) !== 2) {
                        return $query;
                    }

                    $startTime = Carbon::parse($dateRange['range'][0])->startOfDay()->subRealHours($hourOffset);

                    $startDate = $startTime->copy()->subDays($dayRange);
                    $endDate   = Carbon::parse($dateRange['range'][1])->endOfDay();
                    $time = [$startDate, $endDate];

                    if (is_null($query)) {
                        return $time;
                    }

                    return $query->whereBetween($dateRange['column'], $time);
                } catch (Throwable $e) {
                    graylogError('filterQueryDateBeforeRange error with custom daterange: ', [
                        'category' => 'filterQueryDateBeforeRange_error_log',
                        'message' => $e,
                    ]);
                    return $query;
                }
            default:
            case DateRangeEnum::TODAY:
                if (is_null($query)) {
                    return $today;
                }

                $startDate = $today->copy()->subDays($dayRange);
                $endDate = now()->addRealHours($hourOffset)->endOfDay()->subRealHours($hourOffset);
                $time = [
                    $startDate,
                    $endDate
                ];
                return $query->whereBetween($dateRange['column'], $time); // first hour of this day
            case DateRangeEnum::YESTERDAY:
                $time = [
                    $today->copy()->subDays()->subDays($dayRange),
                    now()->addRealHours($hourOffset)->endOfDay()->subRealHours($hourOffset)
                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_HOUR:
                $time = now()->subHour();
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = now()->copy();
                if (is_null($query)) {
                    return $time;
                }

                $time = [$startDate, $endDate];

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_24_HOURS:
                $time = now()->subHours(24);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = now()->copy();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];

                return $query->whereBetween($dateRange['column'],  $time);
            case DateRangeEnum::LAST_3_DAYS:
                $time = now()->subDays(3);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = now()->copy();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];
                return $query->whereBetween($dateRange['column'],  $time);
            case DateRangeEnum::LAST_7_DAYS:
                $time = now()->subDays(7)->subRealHours($hourOffset);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = now()->copy();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_14_DAYS:
                $time = now()->subDays(14);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = now()->copy();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_30_DAYS:
                $time = now()->subDays(30)->subRealHours($hourOffset);
                $startDate = $time->copy()->subDays($dayRange);
                $endDate = now()->copy();
                if (is_null($query)) {
                    return $time;
                }
                $time = [$startDate, $endDate];

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_WEEK:
                $today = $today->startOfWeek()->subRealHours($hourOffset);
                $time = [
                    $today->copy()->subDays($dayRange),
                    now()->addRealHours($hourOffset)->endOfWeek()->subRealHours($hourOffset)
                ];

                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_WEEK:
                $lastWeek = $today->subWeek();
                $time = [
                    $lastWeek->copy()->startOfWeek()->subDays($dayRange)->subRealHours($hourOffset),
                    now()->addRealHours($hourOffset)->subWeek()->endOfWeek()->subRealHours($hourOffset)

                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_MONTH:
                $time = [
                    $today->copy()->startOfMonth()->subRealHours($hourOffset)->subDays($dayRange),
                    now()->addRealHours($hourOffset)->endOfMonth()->subRealHours($hourOffset)
                ];
                if (is_null($query)) {
                    return $time;
                }
                // start day of this month
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_MONTH:
                $time = [
                    $today->copy()->startOfMonth()->subMonth()->subRealHours($hourOffset)->subDays($dayRange),
                    now()->addRealHours($hourOffset)->subMonthNoOverflow()->endOfMonth()->subRealHours($hourOffset)

                ];
                if (is_null($query)) {
                    return $time;
                }

                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_QUARTER:
                $time = [
                    $today->copy()->startOfQuarter()->subQuarterNoOverflow()->subRealHours($hourOffset)->subDays($dayRange),
                    now()->addRealHours($hourOffset)->subQuarterNoOverflow()->endOfQuarter()->subRealHours($hourOffset)
                ];
                if (is_null($query)) {
                    return $time;
                }


                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_YEAR:
                $thisYear = $today->startOfYear();
                $time = [
                    $thisYear->copy()->subYear()->startOfYear()->subRealHours($hourOffset),
                    now()->addRealHours($hourOffset)->endOfYear()->subRealHours($hourOffset)

                ];
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::THIS_QUARTER:
                $thisQuarter = $today->startOfQuarter();
                $time = [
                    $thisQuarter->copy()->subDays($dayRange)->startOfQuarter()->subRealHours($hourOffset),
                    now()->addRealHours($hourOffset)->endOfQuarter()->subRealHours($hourOffset)

                ];
                if (is_null($query)) {
                    return $time;
                }

                // first day of this year
                return $query->whereBetween($dateRange['column'], $time);
            case DateRangeEnum::LAST_YEAR:
                $time = [
                    $today->copy()->subYears(2)->startOfYear()->subRealHours($hourOffset),
                    now()->addRealHours($hourOffset)->subYear()->endOfYear()->subRealHours($hourOffset)

                ];
                if (is_null($query)) {
                    return $time;
                }
                return $query->whereBetween($dateRange['column'], $time);
        }
    }
}

const FILE_UPLOADED_SIZE_LIMIT = 100; //MB
if (!function_exists('validateFileUploadedSize')) {
    function validateFileUploadedSize($filePath, $bucketType = StorageDisksEnum::DEFAULT): array
    {
        try{
            $disk = Storage::disk($bucketType);
            if ($disk->size($filePath) > FILE_UPLOADED_SIZE_LIMIT*1024*1024) {
                return [
                    'accept' => false,
                    'message' => "File size should be less than ".FILE_UPLOADED_SIZE_LIMIT.' MB'
                ];
            }
            return [
                'accept' => true
            ];
        }catch(Exception $e) {
            return [
                'accept' => true
            ];
        }

    }
}

if (!function_exists('getStoreBaseUrl')) {
    function getStoreBaseUrl($subDomain = ''): string
    {
        if (strlen($subDomain) == 0) {
            return 'http://localhost';
        }
        switch (app()->environment()) {
            case 'production':
                return 'https://'.$subDomain.'.mysenprints.com';
            case 'development':
                return 'https://'.$subDomain.'.senprints.xyz';
            case 'staging':
                return 'https://'.$subDomain.'.store.staging.senprints.net';
            case 'testing':
                return 'https://'.$subDomain.'.tstore.senprints.net';
            case 'local':
            default:
                $baseUrl = env('STORE_APP_URL') ?? 'http://localhost';
                $parsedUrl = parse_url($baseUrl);
                $scheme = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] . '://' : '';
                $host = isset($parsedUrl['host']) ? $parsedUrl['host'] : '';
                $path = isset($parsedUrl['path']) ? $parsedUrl['path'] : '';
                $port = isset($parsedUrl['port']) ? ':' . $parsedUrl['port'] : '';
                return $scheme . $subDomain . '.' . $host . ':' . $port . $path;
        }
    }
}

if (! function_exists('silent')) {
    /**
     * @param     callable       $cb
     * @param     mixed|null     $defaultReturn
     * @param                    ...$params
     *
     * @return mixed
     */
    function silent(callable $cb, mixed $defaultReturn = null, ...$params): mixed
    {
        try {
            return value($cb, ...$params);
        } catch (Throwable $e) {
            return $defaultReturn;
        }
    }
}

if (! function_exists('google_client')) {
    /**
     * @param     string|null         $serviceAccountKeyPath     The path to the service account key file
     * @param     string|string[]     $scopes
     *
     * @return \Google_Client
     * @throws \Google\Exception
     */
    function google_client(mixed $scopes, ?string $serviceAccountKeyPath = null): Google_Client
    {
        $serviceAccountKeyPath ??= storage_path('google-service-account-credentials.json');
        $client = new \Google_Client();
        $client->setAuthConfig($serviceAccountKeyPath);
        $client->addScope($scopes);

        return $client;
    }
}

if (! function_exists('google_drive_service')) {
    /**
     * @param     string|null     $serviceAccountKeyPath     The path to the service account key file
     *
     * @return \Google_Service_Drive
     * @throws \Google\Exception
     */
    function google_drive_service(?string $serviceAccountKeyPath = null): Google_Service_Drive
    {
        return new Google_Service_Drive(
            google_client(Google_Service_Drive::DRIVE, $serviceAccountKeyPath)
        );
    }
}

if (! function_exists('parse_design_file_name')) {
    /**
     * Parse the design file name to get the design collection and print space
     *
     * @param     string     $fileName
     *
     * @return array
     */
    function parse_design_file_name(string $fileName): array
    {
        $pattern = '/^(?:\[(.*?)])?(?:\[(.*?)])?\s*(.+?)\.[^.]+$/';
        preg_match($pattern, $fileName, $matches);

        return [
            trim($matches[1] ?? ''), // collection
            trim($matches[2] ?? ''), // print space
            trim($matches[3] ?? '')  // name
        ];
    }
}

if (! function_exists('extract_google_drive_folder_id')) {
    /**
     * Extract the Google Drive folder ID from the URL
     *
     * @param     string     $url
     *
     * @return string|null
     */
    function extract_google_drive_folder_id(string $url): ?string
    {
        $pattern = '/drive\.google\.com\/drive\/folders\/([\w-]+)/';
        preg_match($pattern, $url, $matches);

        return $matches[1] ?? null;
    }
}

if (! function_exists('extract_google_driver_file_id')) {
    /**
     * Extract the Google Drive file ID from the URL
     *
     * @param     string     $url
     *
     * @return string|null
     * @throws Throwable
     */
    function extract_google_driver_file_id(string $url): ?string
    {
        if (preg_match('/\?id=([^&]+)/', $url, $matches)) {
            return $matches[1];
        }

        if (preg_match('%drive\.google\.com/file/[a-z]/([^/]+)/?(?:edit|view|preview)?%', $url, $matches)) {
            return $matches[1];
        }

        throw new RuntimeException('Invalid Google Drive file URL');
    }
}

if (! function_exists('to_list')) {
    /**
     * @param     mixed             $values
     * @param     callable|null     $caseTransform
     * @param     string            $delimiter
     *
     * @return string[]
     */
    function to_list(mixed $values, callable $caseTransform = null, string $delimiter = ','): array
    {
        if (is_array($values)) {
            return $values;
        }

        return Str::of($values)
            ->explode($delimiter)
            ->when(is_callable($caseTransform), fn($c) => $c->map($caseTransform))
            ->filter()
            ->all();
    }
}

if (! function_exists('to_list_int')) {
    /**
     * @param     mixed             $values
     * @param     callable|null     $caseTransform
     * @param     string            $delimiter
     *
     * @return string[]
     */
    function to_list_int(mixed $values, callable $caseTransform = null, string $delimiter = ','): array
    {
        return collect(to_list($values, $caseTransform, $delimiter))
            ->map(fn($v) => (int) $v)
            ->all();
    }
}

if (! function_exists('is_tmp_url')) {
    /**
     * Check if the URL is a temporary URL
     *
     * @param     string     $url
     *
     * @return bool
     */
    function is_tmp_url(string $url): bool
    {
        return preg_match('#/tmp/[^/]+\.[A-Za-z0-9]#i', $url);
    }
}

if (! function_exists('is_google_drive_url')) {
    /**
     * Get the design URL type
     *
     * @param     string     $url
     *
     * @return string
     */
    function is_google_drive_url(string $url): string
    {
        return preg_match('#^https://drive\.google\.com#i', $url);
    }
}

if (! function_exists('to_byte')) {
    /**
     * Convert the given size to bytes
     *
     * @param     int        $size
     * @param     string     $unit
     *
     * @return int
     */
    function to_byte(int $size, string $unit = 'B'): int
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        $unit = strtoupper($unit);

        return $size * (1024 ** array_search($unit, $units));
    }
}

if (! function_exists('default_disk')) {
    /**
     * Get the default disk name
     *
     * @return \Illuminate\Contracts\Filesystem\Filesystem
     */
    function default_disk(): \Illuminate\Contracts\Filesystem\Filesystem
    {
        return Storage::disk(StorageDisksEnum::DEFAULT);
    }
}

if (! function_exists('is_file_exists_on_storage')) {
    function is_file_exists_on_storage($path)
    {
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            $client = Storage::disk($disk);
            if ($client->exists($path)) {
                return $disk;
            }
        }
        return null;
    }
}

if (! function_exists('copy_file_on_storage')) {
    function copy_file_on_storage($oldPath, $newPath)
    {
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            $client = Storage::disk($disk);

            if ($client->exists($newPath)) {
                break;
            }

            $copied = $client->exists($oldPath) && $client->copy($oldPath, $newPath);

            if ($copied) {
                break;
            }

            if ($client->exists($oldPath)) {
                logToDiscord("Can not copy file on $disk with old path {$oldPath} to new path {$newPath} failed.");
                return false;
            }
        }

        return $newPath;
    }
}

if (! function_exists('move_file_on_storage')) {
    function move_file_on_storage($oldPath, $newPath)
    {
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            $client = Storage::disk($disk);

            if ($client->exists($newPath)) {
                break;
            }

            $moved = $client->exists($oldPath) && $client->move($oldPath, $newPath);

            if ($moved) {
                break;
            }

            if ($client->exists($oldPath)) {
                logToDiscord("Can not move file on $disk with old path {$oldPath} to new path {$newPath} failed.");
                return false;
            }
        }

        return $newPath;
    }
}

if (!function_exists('delete_directory_on_storage')) {
    function delete_directory_on_storage($path): bool
    {
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            $client = Storage::disk($disk);

            if ($client->exists($path)) {
                return $client->deleteDirectory($path);
            } else {
                return true;
            }
        }

        return false;
    }
}

if (! function_exists('file_exists_on_storage')) {
    function file_exists_on_storage($path): string
    {
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            $client = Storage::disk($disk);

            if ($client->exists($path)) {
                return $disk;
            }
        }

        return '';
    }
}

if (! function_exists('file_size_on_storage')) {
    function file_size_on_storage($path): int
    {
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            $client = Storage::disk($disk);

            if ($client->exists($path)) {
                return $client->size($path);
            }
        }

        return 0;
    }
}

if (! function_exists('delete_file_on_storage')) {
    function delete_file_on_storage($path): bool
    {
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            $client = Storage::disk($disk);

            if ($client->exists($path)) {
                return $client->delete($path);
            }
        }

        return false;
    }
}

if (! function_exists('opts_to_variants')) {
    /**
     * Get the temporary disk name
     *
     * @param string|array|\stdClass $opts
     *
     * @return string
     * @throws JsonException
     */
    function opts_to_variants(string|array|stdClass $opts): string
    {
        return silent(static function() use($opts): string {
            $opts = match(true) {
                is_string($opts) => json_decode($opts, true, 512, JSON_THROW_ON_ERROR),
                is_array($opts) => $opts,
                $opts instanceof stdClass => (array) $opts
            };

            return str_replace(' ', '-', collect($opts)->join('_'));
        }, '');
    }
}
if (!function_exists('roundToHalf')) {
    function roundToHalf ($value) {
        $reponse = ceil(abs($value) * 2) / 2;
        if ($value < 0) {
            return -1 * $reponse;
        }
        return $reponse;
    }
}

if (!function_exists('getBaseVariantFromOptions')) {
    function getBaseVariantFromOptions ($options, $optionsColor = 'white'): string
    {
        try {
            if (!empty($options['custom_options'])) {
                unset($options['custom_options']);
            }
            if (!empty($options['common_options'])) {
                unset($options['common_options']);
            }
            if (!is_array($options)) {
                if (is_object($options)) {
                    $options = (array)$options;
                } elseif (is_string($options)) {
                    $options = json_decode($options, true);

                    if ($options === null) {
                        return '';
                    }
                } else {
                    return ''; // invalid input, just return empty variant key
                }
            }

            $options['color'] = $optionsColor;
            $options['size'] = isset($options['size']) || !empty($options['size']) ? $options['size'][0] : 's';
            if (isset($options['pack'])) {
                $options['size'] = null;
            }
            $arr = [];
            array_map(
                static function ($each) use (&$arr, $options) {
                    if (!empty($each)) {
                        $type = gettype($each);
                        if (in_array($type, ['string', 'array'])) {
                            if ($type === 'array') {
                                $each = $each[0];
                            }
                            $arr[] = strtolower(
                                preg_replace(
                                    [
                                        '/\s+/',
                                        '/-/',
                                    ],
                                    '_',
                                    correctOptionValue($each)
                                )
                            );
                        } else {
                            logToDiscord('Error in getBaseVariantFromOptions type is not String. Detail : '. print_r($each, true).
                            '. Total options : ' . print_r($options, true)
                            , 'error');
                        }
                    }
                },
                $options
            );
            // options: {"size": "S", "color": "White / Light blue "}
            // variant key: white/light_blue-s
            return !empty($arr) ? implode('-', $arr) : 'none';
        } catch (\Exception $e) {
            logException($e);
            return 'none';
        }
    }
}

if (!function_exists('ttlDiffForHumans')) {
    function ttlDiffForHumans($ttl): bool|string
    {
        if (is_int($ttl) || is_float($ttl)) {
            $now = Carbon::now();
            $times = $now->copy()->addSeconds($ttl);
            return $now->diffForHumans($times, true);
        }

        return false;
    }
}

// Alternative cache store - different from the default cache store
if (!function_exists('cacheAlt')) {
    function cacheAlt()
    {
        return cache()->store(CacheKeys::CACHE_TYPE_ALTERNATIVE);
    }
}

if (!function_exists('hasCached')) {
    function hasCached($key, $store = null, $tags = []): bool
    {
        if (!empty($tags)) {
            return  Cache::hasCached($key, $store, $tags);
        }

        return Cache::hasCached($key, $store);
    }
}

if (!function_exists('getDataByDefaultCache')) {
    function getDataByDefaultCache($cacheKey, $tags = [])
    {
        $data = null;

        // store: null means default cache
        if (!empty($tags) && hasCached($cacheKey, null, $tags)) {
            $data = cache()->tags($tags)->get($cacheKey);
        } elseif (hasCached($cacheKey)) {
            $data = cache()->get($cacheKey);
        }

        return $data;
    }
}

if (!function_exists('ensureUrlHasProtocol')) {
    function ensureUrlHasProtocol($url, $defaultProtocol = 'https')
    {
        if (!in_array($defaultProtocol, ['http', 'https'])) {
            $defaultProtocol = 'https';
        }

        if (!Str::startsWith($url, ['http://', 'https://'])) {
            $url = $defaultProtocol . '://' . $url;
        }

        return $url;
    }
}

if (!function_exists('storageUrl')) {
    /**
     * @param null $path
     * @return string
     */
    function storageUrl($path = null): string
    {
        $baseStorageUrl = null;
        if (StorageDisksEnum::DEFAULT === StorageDisksEnum::S3) {
            $storageUrl = config('filesystems.disks.' . StorageDisksEnum::S3 . '.url', 'senasia.s3-ap-southeast-1.amazonaws.com');
            $baseStorageUrl = ensureUrlHasProtocol($storageUrl);
        }
        if (StorageDisksEnum::DEFAULT === StorageDisksEnum::IDRIVE) {
            $storageUrl = config('filesystems.disks.' . StorageDisksEnum::IDRIVE . '.url', 'p1d0.c12.e2-5.dev');
            $storageUrl = rtrim($storageUrl, '/') . '/' . config('filesystems.disks.' . StorageDisksEnum::IDRIVE . '.bucket', 'senasia');
            $baseStorageUrl = ensureUrlHasProtocol($storageUrl);
        }
        if (empty($path)) {
            if (!is_null($baseStorageUrl)) {
                return $baseStorageUrl;
            }
            throw new \RuntimeException('Base storage url not found');
        }
        $fileExists = Storage::disk(StorageDisksEnum::DEFAULT)->exists($path);
        if ($fileExists) {
            return rtrim($baseStorageUrl, '/') . '/' . $path;
        }
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            $fileExists = Storage::disk($disk)->exists($path);
            if ($fileExists) {
                if ($disk === StorageDisksEnum::S3) {
                    $storageUrl = config('filesystems.disks.' . StorageDisksEnum::S3 . '.url', 'senasia.s3-ap-southeast-1.amazonaws.com');
                    $baseStorageUrl = ensureUrlHasProtocol($storageUrl) . '/' . $path;
                    break;
                }
                if ($disk === StorageDisksEnum::IDRIVE) {
                    $storageUrl = config('filesystems.disks.' . StorageDisksEnum::IDRIVE . '.url', 'p1d0.c12.e2-5.dev');
                    $storageUrl = rtrim($storageUrl, '/') . '/' . config('filesystems.disks.' . StorageDisksEnum::IDRIVE . '.bucket', 'senasia');
                    $baseStorageUrl = ensureUrlHasProtocol($storageUrl) . '/' . $path;
                    break;
                }
            }
        }
        if (!is_null($baseStorageUrl)) {
            return $baseStorageUrl;
        }
        throw new \RuntimeException('File not found in storage');
    }
}

if (!function_exists('convertBytes')) {
    function convertBytes($bytes, $unit = 'B', $precision = 2)
    {
        $unit = strtoupper($unit);
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        if (!in_array($unit, $units, true)) {
            return $bytes;
        }
        if ($unit === 'B') {
            return $bytes;
        }
        $unitIndex = array_search($unit, $units);
        $value = $bytes / (1024 ** $unitIndex);
        return round($value, $precision);
    }
}


if (!function_exists('currentStoreInfo')) {
    function currentStoreInfo() {
        if (Request::hasMacro('currentStoreInfo')) {
            return request()?->currentStoreInfo();
        }
        return null;
    }
}

if (!function_exists('isImage')) {
    function isImage($path): bool
    {
        try {
            $imagesExt = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
            $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
            if (empty($extension)) {
                return false;
            }
            if (!in_array($extension, $imagesExt)) {
                return false;
            }
            return true;
        } catch (Throwable $e) {
            return false;
        }
    }
}

if (!function_exists('convertPhoneNumber')) {
    function convertPhoneNumber($number, $country = null): string
    {
        $phoneNumberUtil = PhoneNumberUtil::getInstance();
        try {
            $number = $phoneNumberUtil->parse($number, $country);
        } catch (Throwable $e) {
            return $number;
        }
        return $phoneNumberUtil->format($number, PhoneNumberFormat::E164);
    }
}

if (!function_exists('getDateRangeOfPreviousMonth')) {
    function getDateRangeOfPreviousMonth(Carbon $currentMonth, $numberPreviousMonths = 0): array
    {
        $response = [];
        foreach (range(0, $numberPreviousMonths) as $index) {
            $month = $currentMonth->copy()->subMonths($index);
            $response [] = [
                'sub_month_index' => $index,
                'start_date' => $month->copy()->startOfMonth()->toDateTimeString(),
                'end_date' => $month->copy()->endOfMonth()->toDateTimeString()
            ];
        }

        return $response;
    }
}

if (!function_exists('getComboSets')) {
    function getComboSets(array $products, $currency): array
    {
        return collect($products)->groupBy('combo_id')->map(function ($comboProducts) use ($currency) {
            $comboPrice = $comboProducts->sum(function ($product) {
                return (float) $product['raw_price'];
            });

            $campaignId = $comboProducts[0]['campaign_id'];
            $seller = \App\Models\User::query()->find($comboProducts[0]['seller_id']);
            $campaign = \App\Models\Campaign::query()->onSellerConnection($seller)->find($campaignId);

            return [
                'combo_id' => $comboProducts[0]['combo_id'],
                'products' => $comboProducts->all(),
                'combo_price' => formatPrice($comboPrice, $currency),
                'thumb_url' => $campaign['thumb_url'],
                'title' => $campaign['name'],
            ];
        })->values()->all();
    }
}

if (!function_exists('artisanCommand')) {
    /**
     * Get the current artisan command being executed.
     *
     * @return string|null
     */
    function artisanCommand(): ?string
    {
        return $_SERVER['argv'][1] ?? null;
    }
}

if (!function_exists('getDatabaseSecondBehindMaster')) {
    /**
     * @param $connection
     * @return int
     */
    function getDatabaseSecondBehindMaster($connection = null)
    {
        try {
            if ($connection) {
                $result = DB::connection($connection)->select('SHOW SLAVE STATUS');
            } else {
                $result = DB::select('SHOW SLAVE STATUS');
            }
            if (empty($result) || Str::lower(data_get($result, '0.Slave_IO_Running', 'yes')) === 'no' || Str::lower(data_get($result, '0.Slave_SQL_Running', 'yes')) === 'no') {
                return -1;
            }
            return (int)data_get($result, '0.Seconds_Behind_Master', 0);
        } catch (Throwable $e) {
            return -1;
        }
    }
}

if (!function_exists('generateCustomerId')) {
    /**
     * @param $payload
     * @return int
     */
    function generateCustomerId($payload)
    {
        try {
            $hash = md5($payload, true);
            return unpack("N", substr($hash, 0, 4))[1];
        } catch (Throwable $e) {
            return 0;
        }
    }
}

if (!function_exists('isValidOrderToken')) {
    /**
     * @param $string
     * @return boolean
     */
    function isValidOrderToken($string): bool
    {
        if ($string === OrderTest::ORDER_TOKEN) {
            return true;
        }
        return preg_match('/^[a-f0-9]{32}$/', $string);
    }
}

if (!function_exists('isOnDevDatabase')) {
    /**
     * @return bool
     */
    function isOnDevDatabase(): bool
    {
        return env('DB_DATABASE') === 'sp_dev';
    }
}

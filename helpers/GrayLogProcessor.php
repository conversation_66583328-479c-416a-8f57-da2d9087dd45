<?php

use App\Services\StoreService;
use Monolog\LogRecord;
use Monolog\Processor\ProcessorInterface;

/**
 * Description of GrayLogProcesser
 *
 * <AUTHOR>
 */
class GrayLogProcessor implements ProcessorInterface
{
    /**
     * Add more fields: server_info, application_name
     * @param LogRecord $record
     * @return LogRecord
     * @throws Exception
     */
    public function __invoke(LogRecord $record): LogRecord
    {
        $request = request();
        $context = $record->context;
        $context['server_info'] = getenv('SERVER_INFO');
        $context['application_name'] = "backend-api";
        $context['request_params'] = json_encode($request ? $request->all() : [], JSON_THROW_ON_ERROR);
        $context['request_route_params'] = json_encode($request ? $request->route()?->parameters() : [], JSON_THROW_ON_ERROR);
        $context['request_path'] = $request->path();
        $context['request_user_agent'] = $request->userAgent();
        $context['request_ip_address'] = getIp($request);
        $context['store_domain'] = StoreService::getDomain();
        return $record->with(context: $context);
    }
}

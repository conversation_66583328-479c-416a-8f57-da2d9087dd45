#!/bin/sh
cd /home/<USER>/www/services/api
git pull
docker-compose build --force-rm
docker service update --force senprints_apis_app
docker exec -i senprints_apis_app.1.$(docker service ps -f 'name=senprints_apis_app.1' senprints_apis_app -q --no-trunc | head -n1) /bin/sh -c 'php artisan migrate --force && sleep 2 && php artisan queue:restart && php artisan config:clear && php artisan cache:clear'
exit

version: '3.7'
services:
  app:
    image: app
    build:
      context: .
      dockerfile: Dockerfile.local
    container_name: app
    environment:
      - XDG_CONFIG_HOME=/var/www/.config
    restart: unless-stopped
    volumes:
      - ./:/var/www/app
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: "2g"
    networks:
      - senprints
  server:
    image: nginx:alpine
    container_name: server
    restart: unless-stopped
    working_dir: /var/www/app
    ports:
      - "6868:80"
    volumes:
      - ./:/var/www/app
      - ./docker/nginx/nginx.conf:/etc/nginx/conf.d/default.conf
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: "1g"
    depends_on:
      - app
      - redis
      - postgres
      - mariadb
    links:
      - app
      - redis
      - postgres
      - mariadb
    networks:
      - senprints
  redis:
    image: 'bitnami/redis:latest'
    container_name: redis
    restart: always
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    networks:
      - senprints
  postgres:
    image: postgres:14-alpine
    container_name: postgres
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: "512m"
    ports:
      - '54321:5432'
    volumes:
      - ./docker/db/resource/postgres:/var/lib/postgresql/data
    networks:
      - senprints
  mariadb:
    container_name: mariadb
    image: mariadb:10.3.36
    restart: always
    ports:
      - "3307:3306"
    expose:
      - "3307"
    volumes:
      - ./docker/db/resource/mariadb:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD:
      MYSQL_DATABASE: sp_dev
      MYSQL_ALLOW_EMPTY_PASSWORD: 'yes'
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: "2g"
    networks:
      - senprints
networks:
  senprints:
    driver: bridge

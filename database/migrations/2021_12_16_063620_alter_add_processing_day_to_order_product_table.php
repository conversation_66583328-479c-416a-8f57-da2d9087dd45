<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddProcessingDayToOrderProductTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'processing_day')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->integer('processing_day')->default(0)->after('delivered_at');
            });
        }
    }

    public function down(): void {}
}

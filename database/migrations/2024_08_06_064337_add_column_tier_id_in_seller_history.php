<?php

use App\Enums\SellerHistoryActionEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('seller_history', function (Blueprint $table) {
            $table->unsignedTinyInteger('tier_id')->after('order_id')->nullable();
            $table->enum('action', SellerHistoryActionEnum::asArray())->default(SellerHistoryActionEnum::UPDATE_ACCOUNT)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class qDropAllFkAssociateWithOrderAndOrderProduct extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm = Schema::getConnection()->getDoctrineSchemaManager();
        $doctrineTable = $sm->introspectTable('order_cancel_request');
        if ($doctrineTable->hasForeignKey("order_cancel_request_order_id_foreign")) {
            Schema::table('order_cancel_request', function (Blueprint $table) {
                $table->dropForeign('order_cancel_request_order_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('order_product');
        if ($doctrineTable->hasForeignKey("order_product_order_id_foreign")) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->dropForeign('order_product_order_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('order');
        Schema::table('order', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("order_customer_id_foreign")) {
                $table->dropForeign('order_customer_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("order_seller_id_foreign")) {
                $table->dropForeign('order_seller_id_foreign');
            }
        });
        $doctrineTable = $sm->introspectTable('product_variant');
        if ($doctrineTable->hasForeignKey("product_variant_product_id_foreign")) {
            Schema::table('product_variant', function (Blueprint $table) {
                $table->dropForeign('product_variant_product_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('file');
        if ($doctrineTable->hasForeignKey("file_seller_id_foreign")) {
            Schema::table('file', function (Blueprint $table) {
                $table->dropForeign('file_seller_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('product_size_guide');
        if ($doctrineTable->hasForeignKey("product_size_guide_product_id_foreign")) {
            Schema::table('product_size_guide', function (Blueprint $table) {
                $table->dropForeign('product_size_guide_product_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('product_points');
        if ($doctrineTable->hasForeignKey("product_points_product_id_foreign")) {
            Schema::table('product_points', function (Blueprint $table) {
                $table->dropForeign('product_points_product_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('product_fulfill_mapping');
        Schema::table('product_fulfill_mapping', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("product_fulfill_mapping_fulfill_product_id_foreign")) {
                $table->dropForeign('product_fulfill_mapping_fulfill_product_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("product_fulfill_mapping_product_id_foreign")) {
                $table->dropForeign('product_fulfill_mapping_product_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("product_fulfill_mapping_supplier_id_foreign")) {
                $table->dropForeign('product_fulfill_mapping_supplier_id_foreign');
            }
        });
        $doctrineTable = $sm->introspectTable('product_reviews');
        if ($doctrineTable->hasForeignKey("product_reviews_order_id_foreign")) {
            Schema::table('product_reviews', function (Blueprint $table) {
                $table->dropForeign('product_reviews_order_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('product_collection');
        if ($doctrineTable->hasForeignKey("product_collection_seller_id_foreign")) {
            Schema::table('product_collection', function (Blueprint $table) {
                $table->dropForeign('product_collection_seller_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('product_design_mapping');
        if ($doctrineTable->hasForeignKey("product_design_mapping_supplier_id_foreign")) {
            Schema::table('product_design_mapping', function (Blueprint $table) {
                $table->dropForeign('product_design_mapping_supplier_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('product_category');
        if ($doctrineTable->hasForeignKey("product_category_ibfk_1")) {
            Schema::table('product_category', function (Blueprint $table) {
                $table->dropForeign('product_category_ibfk_1');
            });
        }
        $doctrineTable = $sm->introspectTable('email_logs');
        Schema::table('email_logs', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("email_logs_email_campaign_id_foreign")) {
                $table->dropForeign('email_logs_email_campaign_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("email_logs_order_id_foreign")) {
                $table->dropForeign('email_logs_order_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("email_logs_subscriber_id_foreign")) {
                $table->dropForeign('email_logs_subscriber_id_foreign');
            }
        });
        $doctrineTable = $sm->introspectTable('subscribers');
        if ($doctrineTable->hasForeignKey("subscribers_seller_id_foreign")) {
            Schema::table('subscribers', function (Blueprint $table) {
                $table->dropForeign('subscribers_seller_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('upsell');
        Schema::table('upsell', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("upsell_product_id_foreign")) {
                $table->dropForeign('upsell_product_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("upsell_upsell_collection_id_foreign")) {
                $table->dropForeign('upsell_upsell_collection_id_foreign');
            }
        });
        $doctrineTable = $sm->introspectTable('taggables');
        if ($doctrineTable->hasForeignKey("taggables_tag_id_foreign")) {
            Schema::table('taggables', function (Blueprint $table) {
                $table->dropForeign('taggables_tag_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('email_campaigns');
        Schema::table('email_campaigns', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("email_campaigns_seller_id_foreign")) {
                $table->dropForeign('email_campaigns_seller_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("email_campaigns_store_id_foreign")) {
                $table->dropForeign('email_campaigns_store_id_foreign');
            }
        });
        $doctrineTable = $sm->introspectTable('email_settings');
        if ($doctrineTable->hasForeignKey("email_settings_seller_id_foreign")) {
            Schema::table('email_settings', function (Blueprint $table) {
                $table->dropForeign('email_settings_seller_id_foreign');
            });
        }
        $doctrineTable = $sm->introspectTable('seller_log');
        Schema::table('seller_log', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("seller_log_campaign_id_foreign")) {
                $table->dropForeign('seller_log_campaign_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("seller_log_order_id_foreign")) {
                $table->dropForeign('seller_log_order_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("seller_log_parent_seller_id_foreign")) {
                $table->dropForeign('seller_log_parent_seller_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("seller_log_user_id_foreign")) {
                $table->dropForeign('seller_log_user_id_foreign');
            }
        });
        $doctrineTable = $sm->introspectTable('staff_log');
        Schema::table('staff_log', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("staff_log_campaign_id_foreign")) {
                $table->dropForeign('staff_log_campaign_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("staff_log_order_id_foreign")) {
                $table->dropForeign('staff_log_order_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("staff_log_seller_id_foreign")) {
                $table->dropForeign('staff_log_seller_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("staff_log_staff_id_foreign")) {
                $table->dropForeign('staff_log_staff_id_foreign');
            }
        });
        $doctrineTable = $sm->introspectTable('design');
        Schema::table('design', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("design_campaign_id_foreign")) {
                $table->dropForeign('design_campaign_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("design_order_id_foreign")) {
                $table->dropForeign('design_order_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("design_order_product_id_foreign")) {
                $table->dropForeign('design_order_product_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("design_product_id_foreign")) {
                $table->dropForeign('design_product_id_foreign');
            }
            if ($doctrineTable->hasForeignKey("design_seller_id_foreign")) {
                $table->dropForeign('design_seller_id_foreign');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {}
}

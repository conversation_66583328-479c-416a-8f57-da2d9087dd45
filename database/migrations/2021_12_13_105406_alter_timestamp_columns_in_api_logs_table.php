<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterTimestampColumnsInApiLogsTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('api_logs');

        Schema::table(
            'api_logs',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('created_at_index', $indexesFound)) {
                    $table->index('created_at', 'created_at_index');
                }
            }
        );

        if (Schema::hasColumn('api_logs', 'updated_at')) {
            Schema::table('api_logs', function (Blueprint $table) {
                $table->dropColumn('updated_at');
            });
        }

        if (Schema::hasColumn('api_logs', 'created_at')) {
            DB::statement("ALTER TABLE `api_logs` CHANGE `created_at` `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP");
        }
    }

    public function down(): void {}
}

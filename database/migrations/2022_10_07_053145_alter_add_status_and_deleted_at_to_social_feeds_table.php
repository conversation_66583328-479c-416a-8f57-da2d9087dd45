<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddStatusAndDeletedAtToSocialFeedsTable extends Migration
{
    public function up()
    {
        Schema::table('social_feeds', function (Blueprint $table) {
            if (!Schema::hasColumn('social_feeds', 'status')) {
                $table->boolean('status')->default(1)->after('path');
            }
            if (!Schema::hasColumn('social_feeds', 'deleted_at')) {
                $table->softDeletes();
            }
        });
    }

    public function down(): void {}
}

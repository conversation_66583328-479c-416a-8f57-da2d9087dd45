<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\OrderDisputePaymentGateEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_dispute', function (Blueprint $table) {
            if (Schema::hasColumn('order_dispute', 'payment_gate')) {
                $table->enum('payment_gate', OrderDisputePaymentGateEnum::getValues())->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};

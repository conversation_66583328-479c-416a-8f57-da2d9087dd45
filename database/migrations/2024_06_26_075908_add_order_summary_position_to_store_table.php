<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\OrderSummaryPositionEnums;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store', function (Blueprint $table) {
            if (!Schema::hasColumn('store', 'order_summary_position')) {
                $table->enum('order_summary_position', OrderSummaryPositionEnums::getValues())
                    ->default(OrderSummaryPositionEnums::TOP);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void{}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user', function (Blueprint $table) {
            if (!Schema::hasColumn('user', 'confirm_join_contest')) {
                $table->boolean('confirm_join_contest')->default(0)->index('confirm_join_contest');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexTimestampInOrderTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order');

        Schema::table(
            'order',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('created_at_index', $indexesFound)) {
                    $table->index('created_at', 'created_at_index');
                }
                if (!array_key_exists('updated_at_index', $indexesFound)) {
                    $table->index('updated_at', 'updated_at_index');
                }
                if (!array_key_exists('fulfilled_at_index', $indexesFound)) {
                    $table->index('fulfilled_at', 'fulfilled_at_index');
                }
                if (!array_key_exists('paid_at_index', $indexesFound)) {
                    $table->index('paid_at', 'paid_at_index');
                }
                if (!array_key_exists('deleted_at_index', $indexesFound)) {
                    $table->index('deleted_at', 'deleted_at_index');
                }
            }
        );
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\Schema;

class AlterUpdatedAtInTrackingStatusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('tracking_status', 'updated_at')) {
            DB::statement("ALTER TABLE `tracking_status` CHANGE `updated_at` `updated_at` timestamp default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP");
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

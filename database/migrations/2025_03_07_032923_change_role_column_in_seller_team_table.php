<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\SellerTeamRoleEnum;
use App\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('seller_team', static function (Blueprint $table) {
            $table->enum('role', SellerTeamRoleEnum::asArray())
                ->change();
        });

        $role = Role::query()
            ->firstOrCreate([
                'name' => SellerTeamRoleEnum::MANAGER_LITE,
                'guard_name' => 'user',
            ]);
        $managerRole = Role::query()
            ->where('name', 'manager')
            ->first();
        if ($managerRole) {
            $permissions = $managerRole->permissions->where('name', '!=', 'get_reports');
            $role->syncPermissions($permissions);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('seller_team', function (Blueprint $table) {
            //
        });
    }
};

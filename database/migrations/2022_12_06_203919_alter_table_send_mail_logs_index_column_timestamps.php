<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterTableSendMailLogsIndexColumnTimestamps extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     * @throws \Doctrine\DBAL\Exception
     */
    public function up()
    {
        $connectionName = 'mail_logs';
        $tableName = 'send_mail_log';
        $schema = Schema::connection($connectionName);
        $db = DB::connection($connectionName);
        $indexesFound = $schema->getConnection()
                                ->getDoctrineSchemaManager()
                                ->listTableIndexes($tableName);
        $schema->table($tableName, function (Blueprint $table) use ($schema, $db, $tableName, $indexesFound){
            if ($schema->hasColumn($tableName, 'created_at')) {
                $db->statement("ALTER TABLE `{$tableName}` CHANGE `created_at` `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP");
            }
            if ($schema->hasColumn($tableName, 'updated_at')) {
                $db->statement("ALTER TABLE `{$tableName}` CHANGE `updated_at` `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;");
            }
            if (!array_key_exists('send_mail_log_created_at', $indexesFound)) {
                $table->index('created_at', 'send_mail_log_created_at');
            }
            if (!array_key_exists('send_mail_log_updated_at', $indexesFound)) {
                $table->index('updated_at', 'send_mail_log_updated_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

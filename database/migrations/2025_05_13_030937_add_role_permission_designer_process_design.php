<?php

use Illuminate\Database\Migrations\Migration;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Permission::query()->firstOrCreate([
            'name' => 'process_design',
            'guard_name' => 'admin',
        ]);
        Role::query()->firstOrCreate([
            'name' => 'designer',
            'guard_name' => 'admin',
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};

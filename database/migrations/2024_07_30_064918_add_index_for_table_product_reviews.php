<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_reviews', function (Blueprint $table) {
            $table->index([
                'template_id',
                'campaign_id',
                'seller_id',
                'allow_sharing',
                'status',
            ], 'camp_template_seller_allow_sharing_status_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use <PERSON><PERSON>\Permission\Models\Permission;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Permission::query()->firstOrCreate([
            'guard_name' => 'user',
            'name' => 'manage_public_campaign'
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};

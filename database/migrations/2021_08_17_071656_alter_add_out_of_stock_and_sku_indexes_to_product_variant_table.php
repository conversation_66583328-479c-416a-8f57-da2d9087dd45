<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddOutOfStockAndSkuIndexesToProductVariantTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('product_variant');

        Schema::table('product_variant', function (Blueprint $table) use ($indexesFound) {
            if (!array_key_exists('out_of_stock_index', $indexesFound)) {
                $table->index('out_of_stock', 'out_of_stock_index');
            }
            if (!array_key_exists('sku_index', $indexesFound)) {
                $table->index('sku', 'sku_index');
            }
        });
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paypal_partner_referrals', function (Blueprint $table) {
            $table->id();

            $table->string('tracking_id', 36)->unique();

            // ref: https://developer.paypal.com/docs/multiparty/checkout/standard/integrate/#link-modifythecode
            $table->string('paypal_merchant_id', 30)->nullable();

            $table->enum('status', ['pending', 'completed', 'declined', 'revoked'])
                ->default('pending');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::dropIfExists('paypal_partner_referrals');
    }
};

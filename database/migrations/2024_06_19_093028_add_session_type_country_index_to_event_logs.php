<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::connection('singlestore')->hasColumns('event_logs', ['session_id', 'type', 'country'])) {
            Schema::connection('singlestore')->table('event_logs', function (Blueprint $table) {
                $table->index(['session_id', 'type', 'country'], 'event_logs_session_id_type_country_index');
            });
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSentToCrispToContactFormLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('contact_form_logs', function (Blueprint $table) {
            // add sent_to_crisp column, after attach_files column
            $table->boolean('sent_to_crisp')
                ->default(true) // default true, because we want to mark all old records as sent to crisp
                ->after('attach_files');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateSystemColorTableForTwoColorMode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('system_color', function (Blueprint $table) {
            // Extend hex_code field to support multi-color format: "#ffffff-#000000-#ff0000"
            $table->string('hex_code', 20)->change();

            // Add two_color_mode column for new functionality (later renamed to multi_color_mode)
            $table->boolean('two_color_mode')->default(false)->after('is_heather');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('system_color', function (Blueprint $table) {
            // Revert hex_code field back to original size
            // $table->string('hex_code', 8)->change();

            // Drop the new columns
            // $table->dropColumn('multi_color_mode'); // Note: column was renamed to multi_color_mode
        });
    }
}

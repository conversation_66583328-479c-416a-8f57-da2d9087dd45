<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableAbandonedLogsChangeStartAtColumnType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('abandoned_logs', function (Blueprint $table) {
            if (Schema::hasColumn('abandoned_logs', 'start_at')) {
                $table->dropColumn(['start_at']);
            }

            if (!Schema::hasColumn('abandoned_logs', 'start_at')) {
                $table->dateTime('start_at')->after('recovered');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterCartJobsTableRenameDaysToNextEmail extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cart_jobs', function (Blueprint $table) {
            if (Schema::hasColumn('cart_jobs', 'days')) {
                $table->dropColumn('days');
                $table->string('next_email')
                    ->after('start_time')
                    ->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

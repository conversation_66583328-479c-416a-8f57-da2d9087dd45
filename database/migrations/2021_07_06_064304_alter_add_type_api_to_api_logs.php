<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddTypeApiToApiLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('api_logs', 'type')) {
            Schema::table(
                'api_logs',
                function (Blueprint $table) {
                    // add api type
                    $table->enum('type', \App\Enums\ApiLogTypeEnum::asArray())->change();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

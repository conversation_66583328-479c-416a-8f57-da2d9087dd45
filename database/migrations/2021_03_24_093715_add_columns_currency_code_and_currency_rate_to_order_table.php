<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsCurrencyCodeAndCurrencyRateToOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('order', 'currency_code')) {
            Schema::table('order', function (Blueprint $table) {
                $table->string('currency_code');
            });
        }
        if (!Schema::hasColumn('order', 'currency_rate')) {
            Schema::table('order', function (Blueprint $table) {
                $table->unsignedDouble('currency_rate')->default(0);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

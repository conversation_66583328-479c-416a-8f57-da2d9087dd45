<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddAssigneeToOrderTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order', 'assignee')) {
            Schema::table('order', function (Blueprint $table) {
                $table->unsignedBigInteger('assignee')->nullable()->after('support_status');
            });
        }
    }

    public function down(): void {}
}

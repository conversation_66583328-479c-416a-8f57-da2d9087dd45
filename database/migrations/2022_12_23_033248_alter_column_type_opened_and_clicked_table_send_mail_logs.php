<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterColumnTypeOpenedAndClickedTableSendMailLogs extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $connectionName = 'mail_logs';
        $tableName = 'send_mail_log';
        $schema = Schema::connection($connectionName);
        $schema->table($tableName, function(Blueprint $table) use($schema, $tableName) {
            if ($schema->hasColumn($tableName, 'opened')) {
                $table->boolean('opened')
                    ->default(false)
                    ->change();
            }
            if ($schema->hasColumn($tableName, 'clicked')) {
                $table->boolean('clicked')
                    ->default(false)
                    ->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

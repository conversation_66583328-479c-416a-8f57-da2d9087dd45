<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDefaultPaymentGatewayToStore extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add default payment gateway to store
        Schema::table('store', function (Blueprint $table) {
            $table->unsignedSmallInteger('stripe_gateway_id')->index('stripe_gateway_id')->default(0)->after('show_tipping');
            $table->unsignedSmallInteger('paypal_gateway_id')->index('paypal_gateway_id')->default(0)->after('stripe_gateway_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

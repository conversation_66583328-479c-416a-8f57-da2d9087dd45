<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSocialAccountsToStoreTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('store', function (Blueprint $table) {
            if (!Schema::hasColumn('store', 'social_accounts')) {
                $table->longText('social_accounts')
                    ->after('status')
                    ->nullable()
                    ->comment('{"facebook":"","instagram":"","twitter":"","google":""}');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

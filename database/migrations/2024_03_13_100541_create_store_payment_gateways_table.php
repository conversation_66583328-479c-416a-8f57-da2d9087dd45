<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('store_payment_gateways', function (Blueprint $table) {
            $table->integer('payment_gateway_id')->index('payment_gateway_id');
            $table->integer('store_id')->index('store_id');
            $table->tinyInteger('type')->default(1)->index('type')->comment('1: include, 2: exclude');
            //để đảm bảo mỗi cặp (store, payment_gateway, type) chỉ xuất hiện một lần
            $table->unique(['payment_gateway_id', 'store_id', 'type']);
            $table->timestamps();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

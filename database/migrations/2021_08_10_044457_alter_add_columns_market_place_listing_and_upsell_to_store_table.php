<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnsMarketPlaceListingAndUpsellToStoreTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('store', 'market_place_listing')) {
            Schema::table('store', function (Blueprint $table) {
                $table->boolean('market_place_listing')->default(0)->after('private_store');
            });
        }

        if (!Schema::hasColumn('store', 'market_place_upsell')) {
            Schema::table('store', function (Blueprint $table) {
                $table->boolean('market_place_upsell')->default(0)->after('market_place_listing');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

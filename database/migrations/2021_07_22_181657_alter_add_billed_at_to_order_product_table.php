<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddBilledAtToOrderProductTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'billed_at')) {
            Schema::table(
                'order_product',
                function (Blueprint $table) {
                    $table->timestamp('billed_at')->nullable();
                }
            );
        }
    }

    public function down(): void {}
}

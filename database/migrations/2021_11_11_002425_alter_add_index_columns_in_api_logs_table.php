<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexColumnsInApiLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('api_logs');

        Schema::table('api_logs', function (Blueprint $table) use ($indexesFound){
            if (!array_key_exists('order_id_index', $indexesFound)) {
                $table->index('order_id', 'order_id_index');
            }
            if (!array_key_exists('created_at_index', $indexesFound)) {
                $table->index('created_at', 'created_at_index');
            }
        });
    }

    public function down(): void {}
}

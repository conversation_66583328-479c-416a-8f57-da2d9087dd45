<?php

use App\Enums\UserTopupTransactionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterColumnStatusInUserTopupTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('user_topup_transactions', 'status')) {
            Schema::table('user_topup_transactions', function (Blueprint $table) {
                $table->enum('status', UserTopupTransactionStatus::getValues())
                    ->comment("'" . implode("','", UserTopupTransactionStatus::getValues()) . "'")
                    ->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SinglestoreAddFieldSyncAtToOrderAndOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::connection('singlestore')->hasColumn('order', 'sync_at')) {
            Schema::connection('singlestore')->table('order', function (Blueprint $table) {
                $table->timestamp('sync_at')->index('sync_at')->nullable();
            });
        }
        if (!Schema::connection('singlestore')->hasColumn('order_product', 'sync_at')) {
            Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
                $table->timestamp('sync_at')->index('sync_at')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

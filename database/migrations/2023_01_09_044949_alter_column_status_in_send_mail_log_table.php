<?php

use App\Enums\SendMail\LogStatus as SendMailLogStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterColumnStatusInSendMailLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $connectionName = 'mail_logs';
        $tableName = 'send_mail_log';
        $schema = Schema::connection($connectionName);
        $schema->table($tableName, function(Blueprint $table) use($schema, $tableName) {
            if ($schema->hasColumn($tableName, 'status')) {
                $table->enum('status', SendMailLogStatus::getValues())
                    ->comment("'" . implode("','", SendMailLogStatus::getValues()) . "'")
                    ->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

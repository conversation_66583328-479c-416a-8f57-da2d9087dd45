<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableProductAddExtraPrintCost extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('product', 'extra_print_cost')) {
            Schema::table('product', function (Blueprint $table) {
                    $table->float('extra_print_cost', 10, 0)->default(0)
                        ->after('description_ts')
                        ->comment('Extra print cost');

            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterFileTableAddFileCdnUrlColumn extends Migration
{
    private string $table = 'file';
    private string $column = 'file_url_2';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumns($this->table, [$this->column]) == false) {
            Schema::table(
                $this->table,
                function (Blueprint $table) {
                    $table->string($this->column)->nullable()->after('file_url');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use App\Models\ProductReview;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeDefaultStarToFiveProductReviewTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_reviews', function (Blueprint $table) {
            $table->smallInteger('print_quality_rating')->default(ProductReview::DEFAULT_STAR)->comment('Default: ' . ProductReview::DEFAULT_STAR)->change();
            $table->smallInteger('product_quality_rating')->default(ProductReview::DEFAULT_STAR)->comment('Default: ' . ProductReview::DEFAULT_STAR)->change();
            $table->smallInteger('customer_support_rating')->default(ProductReview::DEFAULT_STAR)->comment('Default: ' . ProductReview::DEFAULT_STAR)->change();
            $table->decimal('average_rating', 8, 1)->default(ProductReview::DEFAULT_STAR)->comment('Default: ' . ProductReview::DEFAULT_STAR)->change();
        });
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTablePaymentAccountsUpdateColumnPaymentType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('payment_accounts', 'payment_type')) {
            Schema::table('payment_accounts', function (Blueprint $table) {
                $table->enum('payment_type', \App\Enums\PaymentAccountTypeEnum::getValues())->change();
            });
        }
    }
}

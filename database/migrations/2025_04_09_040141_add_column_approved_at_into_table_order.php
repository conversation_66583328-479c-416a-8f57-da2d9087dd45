<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order', function (Blueprint $table) {
            $table->timestamp('approved_at')->nullable()->index();
        });
        Schema::connection('singlestore')->table('order', function (Blueprint $table) {
            $table->timestamp('approved_at')->nullable()->index();
        });
        Schema::table('order_history', function (Blueprint $table) {
            $table->index('updated_by');
            $table->index('updated_by2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

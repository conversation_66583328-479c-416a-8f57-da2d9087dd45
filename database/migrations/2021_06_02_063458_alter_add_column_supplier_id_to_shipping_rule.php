<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnSupplierIdToShippingRule extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('shipping_rule', 'supplier_id')) {
            Schema::table(
                'shipping_rule',
                function (Blueprint $table) {
                    $table->foreignId('supplier_id')
                        ->nullable()
                        ->after('seller_id')
                        ->constrained('user')
                        ->onDelete('cascade');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUniqueIndexToProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up() {
        if (Schema::hasColumn('product', 'supplier_id')) {
            Schema::table(
                'product',
                function(Blueprint $table) {
                    $table->unsignedBigInteger('supplier_id')->nullable()->default(null)->change();
                }
            );
        }

        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('product');

        if (!array_key_exists("product_supplier_sku", $indexesFound)) {
            Schema::table(
                'product',
                function(Blueprint $table) {
                    $table->unique(["supplier_id",'sku'],'product_supplier_sku');
                }
            );
        }

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // re-trigger
        Schema::table('product', function (Blueprint $table) {
            $table->enum('system_type', ProductSystemTypeEnum::asArray())->default(ProductSystemTypeEnum::REGULAR)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewBarcodeToProductOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('order_product', 'barcode')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->dropColumn('barcode');
            });
        }

        Schema::table('order_product', function (Blueprint $table) {
            $table->string('barcode')
                ->after('tax')
                ->nullable()
                ->comment('Store barcode file for fba order');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store', function (Blueprint $table) {
            $table->smallInteger('stripe_gateway_id')->default(0)->comment('0: default, -1: disabled, > 0 is id of gateway')->change();
            $table->smallInteger('paypal_gateway_id')->default(0)->comment('0: default, -1: disabled, > 0 is id of gateway')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddPendingPaymentEnumForStatusAndPendingEnumForPaymentStatusToOrderTable extends Migration
{
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            if (Schema::hasColumn('order', 'status')) {
                // add pending_payment
                $table->enum('status', OrderStatus::asArray())
                    ->comment(implode(',', OrderStatus::getKeys()))
                    ->change();
            }
            if (Schema::hasColumn('order', 'payment_status')) {
                // add pending
                $table->enum('payment_status', OrderPaymentStatus::asArray())
                    ->comment(implode(',', OrderPaymentStatus::getKeys()))
                    ->change();
            }
        });
    }

    public function down(): void {}
}

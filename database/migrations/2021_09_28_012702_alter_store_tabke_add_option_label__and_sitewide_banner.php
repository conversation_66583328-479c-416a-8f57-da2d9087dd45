<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterStoreTabkeAddOptionLabelAndSitewideBanner extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('store', function (Blueprint $table) {
            $table->tinyInteger('option_label_enable')->default(0)->after('show_payment_button');
            $table->tinyInteger('sitewide_banner_enable')->default(0)->after('show_payment_button');
            $table->text('sitewide_banner')->nullable()->after('show_payment_button');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterReferenceIdColumnTypeInApiLogsTable extends Migration
{
    public function up()
    {
        if (Schema::hasColumn('api_logs', 'reference_id')) {
            Schema::table(
                'api_logs',
                function (Blueprint $table) {
                    $table->string('reference_id')->nullable()->change();
                }
            );
        }
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SinglestoreAddColumnSupplierExportedAtToOrderProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (!Schema::connection('singlestore')->hasColumn('order_product', 'supplier_exported_at')) {
                $table->timestamp('supplier_exported_at')->nullable()->index('supplier_exported_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

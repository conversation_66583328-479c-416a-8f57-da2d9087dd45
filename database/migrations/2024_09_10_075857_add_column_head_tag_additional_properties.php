<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\HeadTagTypeEnum;

class AddColumnHeadTagAdditionalProperties extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Re-trigger the migration
        Schema::table('store_head_tag', function (Blueprint $table) {
            $table->enum('tag', HeadTagTypeEnum::getValues())->change(); // Update enum

            $table->text('additional_properties');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAllowNullableColumnsInApiLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('api_logs', function (Blueprint $table) {
            if (Schema::hasColumn('api_logs', 'ip_address')) {
                $table->string('ip_address')->nullable()->change();
            }
            if (Schema::hasColumn('api_logs', 'user_agent')) {
                $table->string('user_agent')->nullable()->change();
            }
            if (Schema::hasColumn('api_logs', 'url')) {
                $table->string('url')->nullable()->change();
            }
            if (Schema::hasColumn('api_logs', 'response_body')) {
                $table->text('response_body')->nullable()->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

class AddFulfill3daysTypeToProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     *
     *
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product', function (Blueprint $table) {
            $table->enum('system_type', ProductSystemTypeEnum::asArray())->default(ProductSystemTypeEnum::REGULAR)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {}
}

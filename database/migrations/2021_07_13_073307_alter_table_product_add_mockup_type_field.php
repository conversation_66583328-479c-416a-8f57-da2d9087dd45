<?php

use App\Enums\MockupTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableProductAddMockupTypeField extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('product', 'mockup_type')) {
            Schema::table('product', function (Blueprint $table) {
                $types = MockupTypeEnum::getValues();
                $table->enum('mockup_type', $types)->default(MockupTypeEnum::GHOST)
                    ->after('options')
                    ->comment('mockup type');

            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

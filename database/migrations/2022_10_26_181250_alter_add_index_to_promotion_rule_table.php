<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexToPromotionRuleTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('promotion_rule');


        Schema::table(
            'promotion_rule',
            function (Blueprint $table) use ($indexesFound) {
                if (!array_key_exists("type_index", $indexesFound)) {
                    $table->index(
                        'type',
                        'type_index'
                    );
                }
                if (!array_key_exists("store_id_index", $indexesFound)) {
                    $table->index(
                        'store_id',
                        'store_id_index'
                    );
                }
                if (!array_key_exists("status_index", $indexesFound)) {
                    $table->index(
                        'status',
                        'status_index'
                    );
                }
                if (!array_key_exists("public_status_index", $indexesFound)) {
                    $table->index(
                        'public_status',
                        'public_status_index'
                    );
                }
                if (!array_key_exists("created_at_index", $indexesFound)) {
                    $table->index(
                        'created_at',
                        'created_at_index'
                    );
                }
            }
        );
    }

    public function down(): void {}
}

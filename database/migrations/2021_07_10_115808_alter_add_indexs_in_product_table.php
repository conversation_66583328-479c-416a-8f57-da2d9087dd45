<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexsInProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('product');

        if (!array_key_exists('seller_id_index', $indexesFound)) {
            Schema::table(
                'product',
                function (Blueprint $table) {
                    $table->index('seller_id', 'seller_id_index');
                }
            );
        }

        if (!array_key_exists('campaign_id_index', $indexesFound)) {
            Schema::table(
                'product',
                function (Blueprint $table) {
                    $table->index('campaign_id', 'campaign_id_index');
                }
            );
        }

        if (!array_key_exists('sync_status_index', $indexesFound)) {
            Schema::table(
                'product',
                function (Blueprint $table) {
                    $table->index('sync_status', 'sync_status_index');
                }
            );
        }

        if (!array_key_exists('status_index', $indexesFound)) {
            Schema::table(
                'product',
                function (Blueprint $table) {
                    $table->index('status', 'status_index');
                }
            );
        }

        if (!array_key_exists('tm_status_index', $indexesFound)) {
            Schema::table(
                'product',
                function (Blueprint $table) {
                    $table->index('tm_status', 'tm_status_index');
                }
            );
        }

        if (!array_key_exists('product_type_index', $indexesFound)) {
            Schema::table(
                'product',
                function (Blueprint $table) {
                    $table->index('product_type', 'product_type_index');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use App\Enums\UserTopupTransactionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnTypeToUserTopupTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_topup_transactions', function (Blueprint $table) {
            if (!Schema::hasColumn('user_topup_transactions', 'type')) {
                $table->enum('type', UserTopupTransactionType::getValues())
                    ->after('detail')
                    ->default(UserTopupTransactionType::BANK)
                    ->comment("'" . implode("','", UserTopupTransactionType::getValues()) . "'")
                    ->index('user_topup_transaction_type');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

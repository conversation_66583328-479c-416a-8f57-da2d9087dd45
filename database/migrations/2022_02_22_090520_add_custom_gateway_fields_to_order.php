<?php

use App\Enums\OrderSenFulfillStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCustomGatewayFieldsToOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            $table->enum('sen_fulfill_status', OrderSenFulfillStatus::asArray())
                ->default(OrderSenFulfillStatus::YES)
                ->comment(implode(',', OrderSenFulfillStatus::getKeys()))
                ->after('fulfill_status');
            // $table->double('processing_fee')->nullable(false)->default(0)->change();
            // $table->double('payment_fee')->nullable(false)->default(0)->change();
            $table->double('total_fulfill_fee')->default(0)->after('processing_fee');
            $table->double('processing_fee_paid')->default(0)->after('total_fulfill_fee');
            $table->double('fulfill_fee_paid')->default(0)->after('processing_fee_paid');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

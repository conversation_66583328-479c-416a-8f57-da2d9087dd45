<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_product_logs', static function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_product_id')->index();
            $table->enum('type', \App\Enums\OrderProductLogEnum::asArray())->index();
            $table->text('content');
            $table->timestamps();
            $table->index('created_at');
            $table->index('updated_at');
            $table->index(['order_product_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

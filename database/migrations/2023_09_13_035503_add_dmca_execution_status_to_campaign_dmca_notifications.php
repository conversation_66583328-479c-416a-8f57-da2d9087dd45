<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDmcaExecutionStatusToCampaignDmcaNotifications extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('campaign_dmca_notifications', function (Blueprint $table) {
            $table->enum('dmca_execution_status',[
                'pending',
                'done'
            ])->default('pending')->after('dmca_url_info');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

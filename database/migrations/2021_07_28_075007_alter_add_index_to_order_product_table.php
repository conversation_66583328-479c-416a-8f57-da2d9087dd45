<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexToOrderProductTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order_product');

        Schema::table(
            'order_product',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('fulfill_status_index', $indexesFound)) {
                    $table->index('fulfill_status', 'fulfill_status_index');
                }
                if (!array_key_exists('fulfill_sku_index', $indexesFound)) {
                    $table->index('fulfill_sku', 'fulfill_sku_index');
                }
            }
        );
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterAddIdColumnToProductFulfillMappingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('product_fulfill_mapping', 'id')) {
            Schema::table(
                'product_fulfill_mapping',
                function (Blueprint $table) {
                    // drop foreign to remove primary key
                    DB::unprepared('ALTER TABLE `product_fulfill_mapping` DROP FOREIGN KEY product_fulfill_mapping_fulfill_product_id_foreign');
                    DB::unprepared('ALTER TABLE `product_fulfill_mapping` DROP FOREIGN KEY product_fulfill_mapping_product_id_foreign');

                    // drop old primary to insert new primary A.I column
                    DB::unprepared('ALTER TABLE `product_fulfill_mapping` DROP PRIMARY KEY');
                    $table->unsignedBigInteger('id', true)->first();

                    // unique to not duplicate
                    $table->unique(['product_id', 'fulfill_product_id']);

                    $table->foreign('product_id')
                        ->references('id')
                        ->on('product')
                        ->onDelete('cascade'); //assign again
                    $table->foreign('fulfill_product_id')
                        ->references('id')
                        ->on('product')
                        ->onDelete('cascade'); //assign again
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //trigger migration
        Schema::table('seller_notification', function (Blueprint $table) {
            $table->tinyInteger('type')->index()->default(1);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};

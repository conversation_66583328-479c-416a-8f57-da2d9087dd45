<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCommentLocationInSupplierTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('supplier', 'location')) {
            Schema::table(
                'supplier',
                function (Blueprint $table) {
                    $table->string('location', 16)
                        ->nullable()
                        ->comment('Can have multiple value, separated by comma')
                        ->change();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('system_color', function (Blueprint $table) {
            // Rename two_color_mode column to multi_color_mode
            $table->renameColumn('two_color_mode', 'multi_color_mode');

            // Extend hex_code field to support up to 5 colors: "#ffffff-#000000-#ff0000-#00ff00-#0000ff"
            // Each color is 7 chars (#ffffff) + 4 separators (-) = 35 + 6 = 41 chars, use 50 for safety
            $table->string('hex_code', 50)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('system_color', function (Blueprint $table) {
            // Revert hex_code length back to previous size
            // $table->string('hex_code', 20)->change();

            // Rename back to two_color_mode
            // $table->renameColumn('multi_color_mode', 'two_color_mode');
        });
    }
};

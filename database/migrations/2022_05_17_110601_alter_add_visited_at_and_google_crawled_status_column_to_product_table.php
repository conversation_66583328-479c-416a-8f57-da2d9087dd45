<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddVisitedAtAndGoogleCrawledStatusColumnToProductTable extends Migration
{
    public function up()
    {
        Schema::table('product', static function (Blueprint $table) {
            if (!Schema::hasColumn('product', 'visited_at')) {
                $table->timestamp('visited_at')->nullable();
            }
            if (!Schema::hasColumn('product', 'google_crawled_status')) {
                $table->tinyInteger('google_crawled_status')->default(0);
            }
        });
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('order', 'visit_info')) {
            Schema::table('order', function (Blueprint $table) {
                $table->json('visit_info')->nullable()->after('session_id');
            });
        }
        if (!Schema::connection('singlestore')->hasColumn('order', 'visit_info')) {
            Schema::connection('singlestore')->table('order', function (Blueprint $table) {
                $table->json('visit_info')->nullable()->after('session_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnIsDeletedToProductTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('product', 'is_deleted')) {
            Schema::table('product', function (Blueprint $table) {
                $table->boolean('is_deleted')->default(0)->after('deleted_at')->index();
            });
        }
    }

    public function down(): void {}
}

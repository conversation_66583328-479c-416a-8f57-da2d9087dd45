<?php

use App\Enums\ProductReviewDisplayEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnProductReviewDisplayToStoreTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('store', function (Blueprint $table) {
            $table->enum('product_review_display', [
                ProductReviewDisplayEnum::DISABLE,
                ProductReviewDisplayEnum::ENABLE_ONLY_STORE,
                ProductReviewDisplayEnum::ENABLE_SHARE_REVIEW
            ])->default(ProductReviewDisplayEnum::DISABLE);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

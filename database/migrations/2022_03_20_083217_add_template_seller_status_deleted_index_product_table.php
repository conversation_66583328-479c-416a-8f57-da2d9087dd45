<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTemplateSellerStatusDeletedIndexProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $productIndexesList = Schema::getConnection()->getDoctrineSchemaManager()->listTableIndexes('product');

        Schema::table('product', function (Blueprint $table) use ($productIndexesList) {
            if (!array_key_exists('template_seller_status_deleted_index', $productIndexesList)) {
                $table->index(['template_id', 'seller_id', 'status', 'deleted_at'], 'template_seller_status_deleted_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

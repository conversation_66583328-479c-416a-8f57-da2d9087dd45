<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddFulfillProductIdToOrderProductTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'fulfill_product_id')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->unsignedBigInteger('fulfill_product_id')
                    ->nullable()
                    ->after('fulfill_sku');
            });
        }
    }

    public function down(): void {}
}

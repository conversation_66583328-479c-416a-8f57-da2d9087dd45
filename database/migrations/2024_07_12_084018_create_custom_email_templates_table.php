<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_email_templates', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('template')->index();
            $table->string('default_template_id')->nullable()->index();
            $table->tinyInteger('is_default')->default(0);
            $table->string('subject')->nullable()->index();
            $table->text('greeting')->nullable();
            $table->longText('content')->nullable();
            $table->text('closing')->nullable();
            $table->json('variables')->nullable();
            $table->tinyInteger('status')->default(0)->index();
            $table->string('process_id')->nullable()->index();
            $table->timestamp('start_sent_email_at')->nullable();
            $table->timestamp('end_sent_email_at')->nullable();
            $table->string('sent_by')->nullable()->index();
            $table->softDeletes();
            $table->boolean('is_deleted')->index()->default(0);
            $table->timestamps();
            $table->string('updated_by')->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use App\Enums\PromotionTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddEnumBdToPromotionRuleTable extends Migration
{
    public function up()
    {
        // add BUNDLE_DISCOUNT
        if (Schema::hasColumn('promotion_rule', 'type')) {
            Schema::table(
                'promotion_rule',
                function (Blueprint $table) {
                    $table->enum('type', PromotionTypeEnum::asArray())
                        ->comment(implode(',', PromotionTypeEnum::getKeys()))
                        ->change();
                }
            );
        }
    }

    public function down(): void {}
}

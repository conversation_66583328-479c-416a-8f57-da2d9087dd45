<?php

use App\Enums\OrderHistoryDisplayLevelEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddDisplayLevelToOrderHistoryTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order_history', 'display_level')) {
            Schema::table('order_history', function (Blueprint $table) {
                $table->enum('display_level', OrderHistoryDisplayLevelEnum::asArray())
                    ->after('order_status')
                    ->default(OrderHistoryDisplayLevelEnum::ADMIN)
                    ->index();
            });
        }
    }

    public function down(): void {}
}

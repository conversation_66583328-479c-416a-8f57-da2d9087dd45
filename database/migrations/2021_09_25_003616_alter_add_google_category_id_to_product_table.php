<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddGoogleCategoryIdToProductTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('product', 'google_category_id')) {
            Schema::table('product', function (Blueprint $table) {
                $table->string('google_category_id')
                    ->nullable()
                    ->after('public_status');
            });
        }
    }

    public function down(): void {}
}

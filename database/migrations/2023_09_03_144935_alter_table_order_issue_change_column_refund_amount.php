<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableOrderIssueChangeColumnRefundAmount extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_issue', function (Blueprint $table) {
            // change default value column refund amount
            if (Schema::hasColumn('order_issue', 'refund_amount')) {
                $table->decimal('refund_amount')->default(0)->change();
            }
            // change default value column charge amount
            if (Schema::hasColumn('order_issue', 'charge_amount')) {
                $table->decimal('charge_amount')->default(0)->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnMethodToApiLogsTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('api_logs', 'method')) {
            Schema::table('api_logs', function (Blueprint $table) {
                $table->enum('method', ['POST', 'GET', 'DELETE', 'PUT', 'PATCH'])
                    ->default('POST')
                    ->after('type');
            });
        }
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexexToAbandonedLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('abandoned_logs');

        Schema::table(
            'abandoned_logs',
            function (Blueprint $table) use ($indexesFound) {
                if (!array_key_exists("store_id_index", $indexesFound)) {
                    $table->index('store_id', 'store_id_index');
                }
                if (!array_key_exists("seller_id_index", $indexesFound)) {
                    $table->index('seller_id', 'seller_id_index');
                }
                if (!array_key_exists("reference_id_index", $indexesFound)) {
                    $table->index('reference_id', 'reference_id_index');
                }
                if (!array_key_exists("status_index", $indexesFound)) {
                    $table->index('status', 'status_index');
                }
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

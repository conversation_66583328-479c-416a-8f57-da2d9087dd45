<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnTotalProductsToSocialFeedsTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('social_feeds', 'total_products')) {
            Schema::table('social_feeds', function (Blueprint $table) {
                $table->unsignedBigInteger('total_products')->default(0)->after('seller_id');
            });
        }
    }

    public function down(): void {}
}

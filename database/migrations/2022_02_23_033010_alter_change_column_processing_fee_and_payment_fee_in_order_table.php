<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterChangeColumnProcessingFeeAndPaymentFeeInOrderTable extends Migration
{
    public function up()
    {
        if (Schema::hasColumn('order', 'processing_fee')) {
            DB::statement("ALTER TABLE `order` CHANGE COLUMN `processing_fee` `processing_fee` DOUBLE NOT NULL DEFAULT 0");
        }
        if (Schema::hasColumn('order', 'payment_fee')) {
            DB::statement("ALTER TABLE `order` CHAN<PERSON> COLUMN `payment_fee` `payment_fee` DOUBLE NOT NULL DEFAULT 0");
        }
    }

    public function down(): void {}
}

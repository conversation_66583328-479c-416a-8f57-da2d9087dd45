<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexPrintSpaceInFileTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('file');

        Schema::table(
            'file',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('print_space_index', $indexesFound)) {
                    $table->index('print_space', 'print_space_index');
                }
            }
        );
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexRoleStatusToUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('user');

        Schema::table('user', function (Blueprint $table) use ($indexesFound){
            if (!array_key_exists('role_index', $indexesFound)) {
                $table->index('role', 'role_index');
            }
            if (!array_key_exists('status_index', $indexesFound)) {
                $table->index('status', 'status_index');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

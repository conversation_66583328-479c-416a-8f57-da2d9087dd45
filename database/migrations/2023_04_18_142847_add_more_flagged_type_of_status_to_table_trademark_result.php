<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMoreFlaggedTypeOfStatusToTableTrademarkResult extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('trademark_result', function (Blueprint $table) {
            if (Schema::hasColumn('trademark_result', 'status')) {
                $table->enum('status', [
                    'pending',
                    'good',
                    'violated',
                    'flagged',
                ])->default('pending')->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

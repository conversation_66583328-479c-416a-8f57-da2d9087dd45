<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexToOrderTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order');

        Schema::table(
            'order',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('status_index', $indexesFound)) {
                    $table->index('status', 'status_index');
                }
                if (!array_key_exists('fulfill_status_index', $indexesFound)) {
                    $table->index('fulfill_status', 'fulfill_status_index');
                }
                if (!array_key_exists('ref_id_index', $indexesFound)) {
                    $table->index('ref_id', 'ref_id_index');
                }
            }
        );
    }

    public function down(): void {}
}

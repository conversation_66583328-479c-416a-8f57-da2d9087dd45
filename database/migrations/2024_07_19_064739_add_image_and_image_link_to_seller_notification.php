<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('seller_notification', function (Blueprint $table) {
            $table->string('image')->nullable()->after('message')->index();
            $table->string('image_link')->nullable()->after('image')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterColumnIndexUserTopupTransactionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $tableName = 'user_topup_transactions';
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes($tableName);
        Schema::table($tableName, function(Blueprint $table) use($indexesFound) {
            if (!array_key_exists("topup_transaction_amount_vnd", $indexesFound)) {
                $table->index('amount_vnd', 'topup_transaction_amount_vnd');
            }
            if (!array_key_exists("topup_transaction_amount_usd", $indexesFound)) {
                $table->index('amount_usd', 'topup_transaction_amount_usd');
            }
            if (!array_key_exists("topup_transaction_payment_email", $indexesFound)) {
                $table->index('payment_email', 'topup_transaction_payment_email');
            }
            if (!array_key_exists("topup_transaction_created_at", $indexesFound)) {
                $table->index('created_at', 'topup_transaction_created_at');
            }
            if (!array_key_exists("topup_transaction_updated_at", $indexesFound)) {
                $table->index('updated_at', 'topup_transaction_updated_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SinglestoreAddFieldSyncStatusToOrderAndOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::connection('singlestore')->hasColumn('order', 'sync_status')) {
            Schema::connection('singlestore')->table('order', function (Blueprint $table) {
                $table->tinyInteger('sync_status')->index('sync_status')->nullable()->default(0)->comment('Sync order info to singlestore');
            });
        }
        if (!Schema::connection('singlestore')->hasColumn('order_product', 'sync_status')) {
            Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
                $table->tinyInteger('sync_status')->index('sync_status')->nullable()->default(0)->comment('Sync order product info to singlestore');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store', function (Blueprint $table) {
            if (!Schema::hasColumn('store', 'show_countdown')) {
                $table->tinyInteger('show_countdown')->default(0);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

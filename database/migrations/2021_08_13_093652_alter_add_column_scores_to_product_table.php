<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnScoresToProductTable extends Migration
{
    public function up()
    {
        Schema::table('product', function (Blueprint $table) {
            if (!Schema::hasColumn('product', 'sales_score')) {
                $table->unsignedBigInteger('sales_score')->default(0)->after('sku');
            }
            if (!Schema::hasColumn('product', 'time_score')) {
                $table->double('time_score')->default(0)->after('sku');
            }
            if (!Schema::hasColumn('product', 'score')) {
                $table->double('score')->default(0)->after('sku');
            }
        });
    }

    public function down(): void {}
}

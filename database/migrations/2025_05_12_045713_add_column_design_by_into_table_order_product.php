<?php

use App\Enums\DesignByEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            $table->enum('design_by', DesignByEnum::asArray())->after('design_cost')->default(DesignByEnum::SELLER)->index();
        });
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            $table->string('design_by')->after('design_cost')->default(DesignByEnum::SELLER)->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAuthIdToOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'auth_id')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->unsignedBigInteger('auth_id')
                    ->nullable()
                    ->after('seller_id');
                $table->foreign('auth_id')
                    ->references('id')
                    ->on('user')
                    ->onDelete('cascade');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

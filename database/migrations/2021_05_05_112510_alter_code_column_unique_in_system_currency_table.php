<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterCodeColumnUniqueInSystemCurrencyTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('system_currency');

        if (!array_key_exists("system_currency_code_unique", $indexesFound)) {
            Schema::table(
                'system_currency',
                function (Blueprint $table) {
                    $table->unique('code', 'system_currency_code_unique');
                }
            );
        }
        if (Schema::hasColumn('system_currency', 'code')) {
            Schema::table(
                'system_currency',
                function (Blueprint $table) {
                    $table->string('code', 8)->change();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

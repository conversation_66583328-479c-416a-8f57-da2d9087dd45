<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyPrimaryKeysInSellerCollectionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('seller_collection', 'seller_id') && Schema::hasColumn('seller_collection', 'collection_id')) {
            Schema::table('seller_collection', function (Blueprint $table) {
                $table->dropIndex('seller_collection_ibfk_2');
                $table->dropIndex('collection_id');
                $table->primary(['seller_id','collection_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

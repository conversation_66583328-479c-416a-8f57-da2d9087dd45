<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAbandonedLogsTableRemoveAndChangePositionColumn extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('abandoned_logs', function (Blueprint $table) {
            $table->integer('total_open')
                ->after('type')
                ->default(0)
                ->change();

            if (Schema::hasColumn('abandoned_logs', 'total_sent')) {
                $table->dropColumn('total_sent');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

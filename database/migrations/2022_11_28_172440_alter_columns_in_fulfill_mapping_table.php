<?php

use App\Enums\FulfillMappingEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterColumnsInFulfillMappingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('fulfill_mapping', function (Blueprint $table) {
            if (Schema::hasColumn('fulfill_mapping', 'external_id')) {
                $table->string('external_id')
                    ->nullable()
                    ->change();
            }
            if (Schema::hasColumn('fulfill_mapping', 'type')) {
                $table->enum('type', FulfillMappingEnum::getValues())
                    ->change();
            }
            if (Schema::hasColumn('fulfill_mapping', 'supplier_id')) {
                $table->unsignedBigInteger('supplier_id')
                    ->nullable()
                    ->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            if (!Schema::hasColumn('order_product', 'next_scan_tracking_code_at')) {
                $table->timestamp('next_scan_tracking_code_at')->after('tracking_code')->nullable();
            }
        });
        
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (!Schema::connection('singlestore')->hasColumn('order_product', 'next_scan_tracking_code_at')) {
                $table->timestamp('next_scan_tracking_code_at')->after('tracking_code')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

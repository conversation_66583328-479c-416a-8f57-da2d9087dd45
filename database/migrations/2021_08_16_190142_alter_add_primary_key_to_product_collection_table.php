<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddPrimaryKeyToProductCollectionTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('product_collection');

        if (!array_key_exists('primary', $indexesFound)) {
            Schema::table('product_collection', function (Blueprint $table) {
                $table->primary(['product_id', 'collection_id', 'seller_id']);
            });
        }
    }

    public function down(): void {}
}

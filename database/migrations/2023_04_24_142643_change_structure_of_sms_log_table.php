<?php

use App\Enums\SMSLogStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeStructureOfSmsLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sms_log', function (Blueprint $table) {
            $table->dropColumn('cart_key');
            $table->enum('status', SMSLogStatus::getValues())->index('status')->default(SMSLogStatus::PENDING)->change();
            $table->string('external_id', 50)->nullable()->index('external_id');
            $table->string('phone_number', 30)->index('phone_number')->change();
            $table->integer('order_id')->index('order_id')->change();
            $table->integer('seller_id')->index('seller_id')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {}
}

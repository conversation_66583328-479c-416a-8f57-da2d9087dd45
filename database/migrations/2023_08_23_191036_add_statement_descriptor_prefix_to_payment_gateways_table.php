<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStatementDescriptorPrefixToPaymentGatewaysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('store', 'payment_prefix_descriptors')) {
            Schema::table('store', function (Blueprint $table) {
                $table->dropColumn('payment_prefix_descriptors');
            });
        }

        Schema::table('payment_gateways', function (Blueprint $table) {
            $table->string('statement_descriptor_prefix', 10)
                ->after('options')
                ->nullable()
                ->comment('Prefix for statement descriptor (only for Strip<PERSON>)');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

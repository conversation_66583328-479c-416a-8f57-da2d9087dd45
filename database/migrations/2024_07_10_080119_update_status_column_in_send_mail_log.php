<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\SendMail\LogStatus;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('mail_logs')->table('send_mail_log', function (Blueprint $table) {
            if (Schema::connection('mail_logs')->hasColumn('send_mail_log', 'status')) {
                $table->enum('status', LogStatus::getValues())->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};

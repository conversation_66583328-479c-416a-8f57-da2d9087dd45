<?php

use App\Enums\FileStatusEnum;
use App\Enums\ProductReviewFileTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductReviewFileTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_review_files', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('product_review_id');
            $table->enum('type', ProductReviewFileTypeEnum::getValues());
            $table->string('url', 512);
            $table->string('thumb_url', 512)->nullable();
            $table->string('token')->unique();
            $table->enum('status', FileStatusEnum::getValues())->default(FileStatusEnum::ACTIVE);
            $table->timestamps();

            $table->foreign('product_review_id')
                ->references('id')
                ->on('product_reviews')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

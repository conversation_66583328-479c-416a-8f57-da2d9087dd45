<?php

use App\Enums\TradeMarkStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTmStatusColumnToOrderAndProductOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            $table->enum('tm_status', TradeMarkStatusEnum::getValues())->default(TradeMarkStatusEnum::UNVERIFIED)->index();
        });

        Schema::table('order_product', function (Blueprint $table) {
            $table->enum('tm_status', TradeMarkStatusEnum::getValues())->default(TradeMarkStatusEnum::UNVERIFIED)->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

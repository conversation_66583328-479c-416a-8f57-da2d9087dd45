<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddShardIdToOrderTable extends Migration
{
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            if (!Schema::hasColumn('order', 'shard_id')) {
                $table->unsignedInteger('shard_id')->default(10);
            }
        });
        Schema::table('order_product', function (Blueprint $table) {
            if (!Schema::hasColumn('order_product', 'shard_id')) {
                $table->unsignedInteger('shard_id')->default(10);
            }
        });
    }

    public function down(): void {}
}

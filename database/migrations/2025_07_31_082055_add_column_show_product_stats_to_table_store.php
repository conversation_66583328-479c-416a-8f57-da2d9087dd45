<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store', function (Blueprint $table) {
            $table->boolean('show_product_stats')->default(1)->after('show_countdown');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use App\Enums\FbaFulfillBy;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyColumnFulfillFbaByOfOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_product', function (Blueprint $table) {
            if (Schema::hasColumn('order_product', 'fulfill_fba_by')) {
                $table->dropColumn('fulfill_fba_by');
            }
        });
        Schema::table('order_product', function (Blueprint $table) {
            $table->enum('fulfill_fba_by', FbaFulfillBy::asArray())
                ->index('fulfill_fba_by')
                ->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

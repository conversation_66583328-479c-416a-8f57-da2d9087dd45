<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddMinDaysAndMaxDaysToShippingRuleTable extends Migration
{
    public function up()
    {
        Schema::table('shipping_rule', function (Blueprint $table) {
            if (!Schema::hasColumn('shipping_rule', 'min_days')) {
                $table->float('min_days')->nullable();
            }
            if (!Schema::hasColumn('shipping_rule', 'max_days')) {
                $table->float('max_days')->nullable();
            }
        });
    }

    public function down(): void {}
}

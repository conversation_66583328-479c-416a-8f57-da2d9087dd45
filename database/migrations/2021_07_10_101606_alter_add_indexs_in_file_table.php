<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexsInFileTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('file');

        if (!array_key_exists('store_id_index', $indexesFound)) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->index('store_id', 'store_id_index');
                }
            );
        }

        if (!array_key_exists('type_index', $indexesFound)) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->index('type', 'type_index');
                }
            );
        }

        if (!array_key_exists('option_index', $indexesFound)) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->index('option', 'option_index');
                }
            );
        }

        if (!array_key_exists('order_id_index', $indexesFound)) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->index('order_id', 'order_id_index');
                }
            );
        }

        if (!array_key_exists('order_product_id_index', $indexesFound)) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->index('order_product_id', 'order_product_id_index');
                }
            );
        }

        if (!array_key_exists('product_id_index', $indexesFound)) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->index('product_id', 'product_id_index');
                }
            );
        }

        if (!array_key_exists('mockup_id_index', $indexesFound)) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->index('mockup_id', 'mockup_id_index');
                }
            );
        }

        if (!array_key_exists('design_id_index', $indexesFound)) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->index('design_id', 'design_id_index');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUniqueColumnsInShippingRuleTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('shipping_rule');

        if (array_key_exists('shipping_rule_unique_product_shipping_location', $indexesFound)) {
            Schema::table(
                'shipping_rule',
                function (Blueprint $table) {
                    // remove prevent error when drop index
                    $table->dropForeign(['product_id']);

                    $table->dropUnique('shipping_rule_unique_product_shipping_location');

                    //assign again
                    $table->foreign('product_id')
                        ->references('id')
                        ->on('product')
                        ->onDelete('cascade');
                }
            );
        }

        if (!array_key_exists('unique_product_store_seller_supplier_shipping_location', $indexesFound)) {
            Schema::table(
                'shipping_rule',
                function (Blueprint $table) {
                    // remove prevent error when drop index
                    $table->dropForeign(['product_id']);
                    $table->dropForeign(['seller_id']);
                    $table->dropForeign(['supplier_id']);

                    $table->unique(
                        [
                            'product_id',
                            'store_id',
                            'seller_id',
                            'supplier_id',
                            'shipping_method',
                            'location_code',
                        ],
                        'unique_product_store_seller_supplier_shipping_location'
                    );

                    //assign again
                    $table->foreign('product_id')
                        ->references('id')
                        ->on('product')
                        ->onDelete('cascade');
                    $table->foreign('store_id')
                        ->references('id')
                        ->on('store')
                        ->onDelete('cascade');
                    $table->foreign('seller_id')
                        ->references('id')
                        ->on('user')
                        ->onDelete('cascade');
                    $table->foreign('supplier_id')
                        ->references('id')
                        ->on('user')
                        ->onDelete('cascade');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCostFieldToTableOrderProduct extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            $table->float('fulfill_base_cost')->nullable()->default(0)->comment('Giá gốc của supplier');
            $table->float('fulfill_shipping_cost')->nullable()->default(0)->comment('Phí vận chuyển cuar supplier');
            $table->float('fulfill_profit')->nullable()->default(0)->comment('Lợi nhuận của supplier');
        });

        Schema::table('order_product', static function (Blueprint $table) {
            $table->float('fulfill_base_cost')->nullable()->default(0)->comment('Giá gốc của supplier'); // cost
            $table->float('fulfill_shipping_cost')->nullable()->default(0)->comment('Phí vận chuyển cuar supplier'); // base_shipping_cost
            $table->float('fulfill_profit')->nullable()->default(0)->comment('Lợi nhuận của supplier'); // profit
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

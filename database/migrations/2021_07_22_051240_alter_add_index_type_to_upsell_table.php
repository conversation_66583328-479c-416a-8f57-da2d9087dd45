<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexTypeToUpsellTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('upsell');

        Schema::table(
            'upsell',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('type_index', $indexesFound)) {
                    $table->index('type', 'type_index');
                }
            }
        );
    }

    public function down(): void {}
}

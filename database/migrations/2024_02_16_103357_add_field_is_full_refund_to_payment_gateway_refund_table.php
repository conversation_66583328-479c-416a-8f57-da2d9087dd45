<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_gateway_refund', function (Blueprint $table) {
            $table->boolean('is_full_refund')
                ->default(0)
                ->index('is_full_refund')
                ->after('refund_amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableSeoTemplatesAddJsonColumns extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if(!Schema::hasColumn('seo_content_template', 'link_json') && !Schema::hasColumn('seo_content_template', 'data_json')) {
            Schema::table('seo_content_template', function (Blueprint $table) {
                $table->text('link_json')->nullable()->after('keywords')->comment('Data links');
                $table->text('data_json')->nullable()->after('keywords')->comment('Data products');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

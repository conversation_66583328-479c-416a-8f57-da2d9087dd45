<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddOrderIdToApiLogsTable extends Migration
{
    public function up()
    {
        Schema::table('api_logs', function (Blueprint $table) {
            if (!Schema::hasColumn('api_logs', 'order_id')) {
                $table->string('order_id')->nullable()->after('reference_type');
            }
        });
    }

    public function down(): void {}
}

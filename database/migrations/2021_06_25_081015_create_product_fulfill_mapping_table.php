<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductFulfillMappingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_fulfill_mapping', function (Blueprint $table) {
            $table->foreignId('product_id')
                ->constrained('product')
                ->onDelete('cascade');
            $table->foreignId('fulfill_product_id')
                ->constrained('product')
                ->onDelete('cascade');
            $table->foreignId('supplier_id')
                ->constrained('supplier')
                ->onDelete('cascade');
            $table->primary(['product_id', 'fulfill_product_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

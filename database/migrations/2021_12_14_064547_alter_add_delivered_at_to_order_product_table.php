<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddDeliveredAtToOrderProductTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'delivered_at')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->timestamp('delivered_at')->after('fulfilled_at')->nullable()->index();
            });
        }
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;

class CreateTopupTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Schema::create('topup_transactions', function (Blueprint $table) {
        //     $table->bigIncrements('id');
        //     $table->string('seller_id');
        //     $table->string('transaction_id');
        //     $table->unsignedDecimal('amount')->default(0);
        //     $table->enum('status', ['pending', 'success', 'failed'])->default('pending');
        //     $table->string('payment_method')->default('bank_transfer');
        //     $table->string('payment_status')->nullable();
        //     $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
        //     $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
        // });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

class AlterUpdateEnumSystemTypeColumnInProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('product', 'system_type')) {
            Schema::table('product', function (Blueprint $table) {
                $table->enum('system_type', ProductSystemTypeEnum::getValues())
                    ->default(ProductSystemTypeEnum::REGULAR)
                    ->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order', function (Blueprint $table) {
            if(!Schema::hasColumn('order', 'estimate_delivery_date')) {
                $table->timestamp('estimate_delivery_date')->nullable(true)->default(null);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

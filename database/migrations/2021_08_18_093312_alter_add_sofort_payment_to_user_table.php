<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddSofortPaymentToUserTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('user', 'sofort_payment')) {
            Schema::table('user', function (Blueprint $table) {
                $table->boolean('sofort_payment')->default(1)->after('market_location');
            });
        }
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddLocationCodeToPrimaryKeyInProductVariantTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_variant', function (Blueprint $table) {
            $table->dropForeign(['product_id']); // to remove primary key

            $table->dropPrimary(['product_id', 'variant_key']);
            $table->primary(['product_id', 'variant_key', 'location_code']);

            $table->foreign('product_id')
                ->references('id')
                ->on('product')
                ->onDelete('cascade'); //assign again
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * rerun migration
     */
    public function up(): void
    {
        Schema::table('import_campaigns_data', function (Blueprint $table) {
            $table->boolean('disable_validate_design')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('import_campaigns_data', function (Blueprint $table) {
            $table->dropColumn('disable_validate_design');
        });
    }
};

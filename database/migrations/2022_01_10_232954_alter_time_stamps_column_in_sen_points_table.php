<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterTimeStampsColumnInSenPointsTable extends Migration
{
    public function up()
    {
        Schema::table('sen_points', function (Blueprint $table) {
            if (Schema::hasColumn('sen_points', 'created_at')) {
                DB::statement('ALTER TABLE `sen_points` CHANGE `created_at` `created_at` timestamp DEFAULT CURRENT_TIMESTAMP;');
            }
            if (Schema::hasColumn('sen_points', 'updated_at')) {
                $table->dropColumn('updated_at');
            }
        });
    }

    public function down(): void {}
}

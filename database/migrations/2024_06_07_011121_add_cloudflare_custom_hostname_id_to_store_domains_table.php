<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store_domains', function (Blueprint $table) {
            $table->string('cloudflare_custom_hostname_id')
                ->nullable()
                ->after('is_default');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('store_domains', function (Blueprint $table) {
            //
        });
    }
};

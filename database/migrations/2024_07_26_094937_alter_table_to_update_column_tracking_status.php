<?php

use App\Enums\TrackingStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            $table->enum('tracking_status', TrackingStatusEnum::getValues())->default(TrackingStatusEnum::NEW)->change();
        });
        Schema::table('tracking_status', function (Blueprint $table) {
            $table->enum('status', TrackingStatusEnum::getValues())->default(TrackingStatusEnum::UNTRACKED)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasColumn('order_product', 'is_corner_placement')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->boolean('is_corner_placement')->default(false)->index();
            });
        }
        if (!Schema::connection('singlestore')->hasColumn('order_product', 'is_corner_placement')) {
            Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
                $table->boolean('is_corner_placement')->default(false)->index();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

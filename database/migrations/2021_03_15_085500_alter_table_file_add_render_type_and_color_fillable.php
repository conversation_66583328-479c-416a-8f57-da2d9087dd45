<?php

use App\Enums\FileRenderType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableFileAddRenderTypeAndColorFillable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('file', function (Blueprint $table) {
            if (!Schema::hasColumns('file', ['render_type', 'color_fillable'])) {
               $table->enum('render_type', FileRenderType::asArray())->default(FileRenderType::NONE);
               $table->tinyInteger('color_fillable')->default(0);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddSellerRefSupplierIndexesToOrderProductTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order_product');

        Schema::table(
            'order_product',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('seller_id_index', $indexesFound)) {
                    $table->index('seller_id', 'seller_id_index');
                }
                if (!array_key_exists('ref_id_index', $indexesFound)) {
                    $table->index('ref_id', 'ref_id_index');
                }
                if (!array_key_exists('supplier_id_index', $indexesFound)) {
                    $table->index('supplier_id', 'supplier_id_index');
                }
            }
        );
    }

    public function down(): void {}
}

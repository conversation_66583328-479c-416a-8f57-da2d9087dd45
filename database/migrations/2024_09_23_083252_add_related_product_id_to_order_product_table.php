<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            if (!Schema::hasColumn('order_product', 'related_product_id')) {
                $table->bigInteger('related_product_id')->nullable()->after('related_campaign_id')->index('order_product_related_product_id');
            }
        });

        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (!Schema::connection('singlestore')->hasColumn('order_product', 'related_product_id')) {
                $table->bigInteger('related_product_id')->nullable()->after('related_campaign_id')->index('order_product_related_product_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};

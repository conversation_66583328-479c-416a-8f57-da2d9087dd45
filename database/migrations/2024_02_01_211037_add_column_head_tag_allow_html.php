<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\StoreHeadTagAllowPositionsEnum;
use App\Enums\HeadTagTypeEnum;

class AddColumnHeadTagAllowHtml extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Re-trigger the migration
        Schema::table('store_head_tag', function (Blueprint $table) {
            $table->enum('tag', HeadTagTypeEnum::getValues())->change();

            $table->enum('position', StoreHeadTagAllowPositionsEnum::getValues())
                ->default(StoreHeadTagAllowPositionsEnum::HEAD);

            $table->integer('priority')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

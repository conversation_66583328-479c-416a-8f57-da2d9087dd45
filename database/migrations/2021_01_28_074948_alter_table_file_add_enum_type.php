<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableFileAddEnumType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('file', 'type')) {
            Schema::table('file', function (Blueprint $table) {
                $table->enum('type', [
                    'mockup',
                    'design',
                    'thumbnail',
                    'image',
                    'banner'
                ])->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

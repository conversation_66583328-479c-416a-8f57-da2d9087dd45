<?php

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_history', function (Blueprint $table) {
            $table->enum('order_status', OrderStatus::asArray())->after('action')->nullable()->default(OrderStatus::DRAFT)->change();
            $table->enum('fulfill_status', OrderFulfillStatus::asArray())->after('order_status')->nullable()->default(OrderFulfillStatus::UNFULFILLED)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

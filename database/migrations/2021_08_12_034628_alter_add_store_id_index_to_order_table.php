<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddStoreIdIndexToOrderTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order');

        Schema::table(
            'order',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('store_id_index', $indexesFound)) {
                    $table->index('store_id', 'store_id_index');
                }
            }
        );
    }

    public function down(): void {}
}

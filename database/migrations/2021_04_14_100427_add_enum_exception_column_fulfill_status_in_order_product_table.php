<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEnumExceptionColumnFulfillStatusInOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('order_product', 'fulfill_status')) {
            Schema::table(
                'order_product',
                function(Blueprint $table) {
                    $table->enum('fulfill_status',\App\Enums\OrderProductFulfillStatus::asArray())
                          ->comment('\'unfulfilled\',\'fulfilled\',\'partial_fulfilled\',\'cancelled\',\'processing\',\'on_hold\',\'rejected\',\'exception\'')
                          ->change();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

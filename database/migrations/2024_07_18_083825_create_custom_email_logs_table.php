<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_email_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('process_id')->index();
            $table->string('template_id')->index();
            $table->unsignedBigInteger('order_id')->index();
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->string('email')->nullable();
            $table->tinyInteger('status')->default(0)->index();
            $table->timestamp('sent_at')->nullable();
            $table->softDeletes();
            $table->boolean('is_deleted')->index()->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

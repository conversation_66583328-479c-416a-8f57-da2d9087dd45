<?php

use App\Enums\FulfillMappingEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTypeColumnInFulfillMappingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('fulfill_mapping', 'type')) {
            Schema::table(
                'fulfill_mapping',
                function (Blueprint $table) {
//                    add shipping_method
                    $table->enum('type', FulfillMappingEnum::asArray())->change();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

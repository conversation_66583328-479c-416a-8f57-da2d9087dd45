<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserCleanSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('singlestore')->create('user_clean_settings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('seller_id')->nullable();
            $table->timestamp('clean_at')->nullable();
            $table->string('type')->nullable();
            $table->string('token')->nullable();

            $table->index('seller_id', 'seller_id');
            $table->index('clean_at', 'clean_at');
            $table->index('type', 'type');
            $table->index('token', 'token');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

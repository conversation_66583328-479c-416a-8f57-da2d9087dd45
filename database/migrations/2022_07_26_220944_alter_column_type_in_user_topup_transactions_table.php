<?php

use App\Enums\UserTopupTransactionType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterColumnTypeInUserTopupTransactionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('user_topup_transactions', 'type')) {
            Schema::table('user_topup_transactions', function (Blueprint $table) {
                $table->enum('type', UserTopupTransactionType::getValues())
                    ->comment("'" . implode("','", UserTopupTransactionType::getValues()) . "'")
                    ->change();
            });
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsFulfillCostAndFulfillOrderIdToOrderProductTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'fulfill_cost')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->double('fulfill_cost')->after('fulfill_sku');
            });
        }
        if (!Schema::hasColumn('order_product', 'fulfill_order_id')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->unsignedBigInteger('fulfill_order_id')->after('fulfill_cost');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

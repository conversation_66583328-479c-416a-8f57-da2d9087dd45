<?php

use App\Enums\PaymentAccountStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateColumnStatusPaymentAccounts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('payment_accounts', 'status')) {
            Schema::table('payment_accounts', function (Blueprint $table) {
                $table->enum('status', PaymentAccountStatus::getValues())
                    ->comment("'" . implode("','", PaymentAccountStatus::getValues()) . "'")
                    ->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddTypeAndIsEncryptedToSystemConfigTable extends Migration
{
    public function up()
    {
        Schema::table('system_config', function (Blueprint $table) {
            if (!Schema::hasColumn('system_config', 'type')) {
                $table->smallInteger('type')->default(1)->after('status');
            }
            if (!Schema::hasColumn('system_config', 'is_encrypted')) {
                $table->boolean('is_encrypted')->default(false)->after('status');
            }
        });
    }

    public function down(): void {}
}

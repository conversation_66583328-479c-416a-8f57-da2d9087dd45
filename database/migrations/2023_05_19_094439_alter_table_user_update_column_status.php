<?php

use App\Enums\UserStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableUserUpdateColumnStatus extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('user', 'status')) {
            Schema::table(
                'user',
                function (Blueprint $table) {
                    $table->enum('status', UserStatusEnum::getValues())->default(UserStatusEnum::NEW)->change();
                }
            );
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddFulfilledAtToOrderProductTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order_product', 'fulfilled_at')) {
            Schema::table(
                'order_product',
                function (Blueprint $table) {
                    $table->timestamp('fulfilled_at')->nullable()->after('updated_at');
                }
            );
        }
    }

    public function down(): void {}
}

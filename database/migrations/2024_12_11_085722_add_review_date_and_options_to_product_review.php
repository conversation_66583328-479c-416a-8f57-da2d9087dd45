<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $sm = Schema::getConnection()->getDoctrineSchemaManager();
        $doctrineTable = $sm->introspectTable('product_review_files');
        Schema::table('product_review_files', function (Blueprint $table) use ($doctrineTable) {
            if ($doctrineTable->hasForeignKey("product_review_files_product_review_id_foreign")) {
                $table->dropForeign('product_review_files_product_review_id_foreign');
            }
        });
        Schema::table('product_reviews', function (Blueprint $table) {
            if (!Schema::hasColumn('product_reviews', 'review_date')) {
                $table->timestamp('review_date')->nullable();
            }
            if (!Schema::hasColumn('product_reviews', 'review_options')) {
                $table->text('review_options')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {}
};

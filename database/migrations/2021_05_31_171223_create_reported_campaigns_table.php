<?php

use App\Enums\ReportedReasonEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateReportedCampaignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('reported_campaigns', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('campaign_id');
            $table->foreign('campaign_id')->references('id')->on('product');

            $table->set('reason', ReportedReasonEnum::getValues())->nullable();

            $table->string('email')->nullable();
            $table->string('name')->nullable();
            $table->string('phone', 16)->nullable();
            $table->string('address')->nullable();
            $table->string('address_2')->nullable();
            $table->string('city')->nullable();
            $table->string('state', 64)->nullable();
            $table->string('postcode', 32)->nullable();
            $table->string('country', 4)->nullable();

            $table->json('additional_info')->nullable();
            $table->ipAddress('ip_address');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

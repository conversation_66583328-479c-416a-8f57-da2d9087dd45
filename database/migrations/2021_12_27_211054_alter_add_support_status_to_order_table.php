<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddSupportStatusToOrderTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('order', 'support_status')) {
            Schema::table('order', function (Blueprint $table) {
                $table->tinyInteger('support_status')
                    ->comment('OrderSupportStatusEnum')
                    ->default(0)
                    ->index();
            });
        }
    }

    public function down(): void {}
}

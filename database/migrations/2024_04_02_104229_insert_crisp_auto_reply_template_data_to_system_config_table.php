<?php

use App\Models\SystemConfig;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $json = [
            'where_is_my_order' => "Dear {CustomerName}\r\n\r\nThank you for your email.\r\n\r\nYou can track your order status via this link below: {SenPrintsTrackOrderUrl}\r\nIn general, orders typically take 3-5 business days to process and package before being shipped, and an additional 5-7 business days for delivery within the US and EU.\r\nPlease note that the shipping carrier may experience delays that are out of our control. During major holiday seasons, tracking information may be delayed by an additional 2-5 days.\r\nFor orders outside of the US and EU, such as in Canada, Australia, and the UK, delivery may take an additional 2-4 weeks due to cross-continent shipping.\r\n\r\nPlease allow for up to 15 business days for your order to arrive. If it has not arrived after this time, please contact our customer support team by replying to this message and we will resolve the issue as quickly as possible.\r\n\r\nThank you for your patience and understanding.\r\n\r\nBest regards,\r\nThe {StoreName} Team",
            'where_is_my_order_with_order_status_url' => "Dear {CustomerName}\r\n\r\nThank you for your email.\r\n\r\nHere's your order status link: {OrderStatusUrl}\r\n{AllTrackingUrls}\r\nIn general, orders typically take 3-5 business days to process and package before being shipped, and an additional 5-7 business days for delivery within the US and EU.\r\nPlease note that the shipping carrier may experience delays that are out of our control. During major holiday seasons, tracking information may be delayed by an additional 2-5 days.\r\nFor orders outside of the US and EU, such as in Canada, Australia, and the UK, delivery may take an additional 2-4 weeks due to cross-continent shipping.\r\n\r\nPlease allow for up to 15 business days for your order to arrive. If it has not arrived after this time, please contact our customer support team by replying to this message and we will resolve the issue as quickly as possible.\r\n\r\nThank you for your patience and understanding.\r\n\r\nBest regards,\r\nThe {StoreName} Team",
            'return_my_order' => "Dear {CustomerName},\r\n\r\nThank you for your email.\r\n\r\nWe are so sorry to hear that the item you received did not meet your expectation.\r\n\r\nDue to a high volume of orders this season, it is possible that our production team may have missed some details on rare occasions.\r\n\r\nWe would appreciate it if you could **Please send us some pictures of the issue, pictures of the size tag and its shipping label on your package** so that we can double-check with our production team and take the appropriate actions.\r\n\r\nIf you have any other requests or concerns, please don't hesitate to let us know. Thank you for your understanding and patience.\r\n\r\nBest regards,\r\nThe {StoreName} Team",
            'item_not_as_describe' => "Dear {CustomerName},\r\n\r\nThank you for your email.\r\n\r\nWe are so sorry to hear that the item you received did not meet your expectation.\r\n\r\nDue to a high volume of orders this season, it is possible that our production team may have missed some details on rare occasions.\r\n\r\nWe would appreciate it if you could **Please send us some pictures of the issue, pictures of the size tag and its shipping label on your package** so that we can double-check with our production team and take the appropriate actions.\r\n\r\nIf you have any other requests or concerns, please don't hesitate to let us know. Thank you for your understanding and patience.\r\n\r\nBest regards,\r\nThe {StoreName} Team",
            'default' => "Hi {CustomerName},\r\nThanks so much for reaching out!\r\nThis auto-reply is just to let you know that we have received your email and will get back to you with a (human) response within 24-48 business hours.\r\n\r\nYou can track your order status via this link below:\r\n{OrderStatusUrl}\r\n{DelayedMessage}\r\nPlease read the articles below if you need any additional information.\r\n{ReturnPolicy}\r\n{ShippingPolicy}\r\n{PrivacyPolicy}\r\n{Terms&Conditions}\r\n{PrivacyPolicy}\r\n{DMCA}\r\n\r\nIf you have any additional information that you think will help us to assist you, please feel free to reply to this email. We look forward to chatting soon, our response within 24-48 business hours.\r\n\r\nBest regards,\r\n\r\nThe {StoreName} Team",
            'delay_message' => "Typically, it takes 3-5 business days for us to process an order and another 5-7 business days for delivery within US or EU. But since you are not located in the US or EU, delivery may take longer due to cross-border shipping. **During this time, tracking updates may be delayed by about 5-10 days.**\r\n\r\nIn some cases, delivery may take longer than a month. This can happen for a variety of reasons, including but not limited to:\r\n\r\n- Packages being handled by multiple carriers\r\n- Delays at customs\r\n\r\nPlease keep in mind that we are unable to guarantee a specific delivery date for international shipments. If your order has not arrived after 27 working days, please contact us.",
        ];
        SystemConfig::query()->firstOrCreate(
            ['key' => 'crisp_auto_reply_template'],
            ['json_data' => json_encode($json), 'status' => 1]
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('system_config', function (Blueprint $table) {
            //
        });
    }
};

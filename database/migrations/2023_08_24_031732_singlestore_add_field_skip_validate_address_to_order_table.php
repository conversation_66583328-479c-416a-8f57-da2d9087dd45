<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class SinglestoreAddFieldSkipValidateAddressToOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::connection('singlestore')->hasColumn('order', 'skip_validate_address')) {
            Schema::connection('singlestore')->table('order', function (Blueprint $table) {
                $table->boolean('skip_validate_address')
                    ->default(false)
                    ->after('tm_status')
                    ->comment('Skip validate address when create order');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

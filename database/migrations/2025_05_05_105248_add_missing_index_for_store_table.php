<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('store', function (Blueprint $table) {
            $table->index('stripe_gateway_id');
            $table->index('paypal_gateway_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

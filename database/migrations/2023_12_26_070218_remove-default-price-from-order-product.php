<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveDefaultPriceFromOrderProduct extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order_product', function (Blueprint $table) {
            if (Schema::hasColumn('order_product', 'cart_item_id')) {
                $table->dropColumn('cart_item_id');
            }
            if (Schema::hasColumn('order_product', 'default_price')){
                $table->dropColumn('default_price');
            }
        });

        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (Schema::connection('singlestore')->hasColumn('order_product', 'cart_item_id')) {
                $table->dropColumn('cart_item_id');
            }
            if (Schema::connection('singlestore')->hasColumn('order_product', 'default_price')){
                $table->dropColumn('default_price');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

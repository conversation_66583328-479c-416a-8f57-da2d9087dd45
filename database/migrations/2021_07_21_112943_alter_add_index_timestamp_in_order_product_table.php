<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexTimestampInOrderProductTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order_product');

        Schema::table(
            'order_product',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('updated_at_index', $indexesFound)) {
                    $table->index('updated_at', 'updated_at_index');
                }
                if (!array_key_exists('fulfilled_at_index', $indexesFound)) {
                    $table->index('fulfilled_at', 'fulfilled_at_index');
                }
            }
        );
    }

    public function down(): void {}
}

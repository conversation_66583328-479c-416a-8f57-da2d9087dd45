<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('store', function (Blueprint $table) {
            $table->boolean('disable_related_product')->index('disable_related_product')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

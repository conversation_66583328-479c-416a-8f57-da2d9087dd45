<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('process_locks', function (Blueprint $table) {
            $table->timestamp('last_run_at')->nullable()->index();
            $table->index(['key', 'last_run_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('supplier', function (Blueprint $table) {
            if (!Schema::hasColumn('supplier', 'additional_print_space')) {
                $table->boolean('additional_print_space')->default(false);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

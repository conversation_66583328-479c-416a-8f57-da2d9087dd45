<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            if (!Schema::connection('singlestore')->hasColumn('order_product', 'promotion_rule_id')) {
                $table->bigInteger('promotion_rule_id')->nullable()->after('shipping_rule_id')->index('order_product_promotion_rule_id');
            }
            if (!Schema::connection('singlestore')->hasColumn('order_product', 'related_campaign_id')) {
                $table->bigInteger('related_campaign_id')->nullable()->after('shipping_rule_id')->index('order_product_related_campaign_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

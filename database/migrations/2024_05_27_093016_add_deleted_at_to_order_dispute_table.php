<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_dispute', function (Blueprint $table) {
            if (!Schema::hasColumn('order_dispute', 'deleted_at')){
                $table->softDeletes('deleted_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void{}
};

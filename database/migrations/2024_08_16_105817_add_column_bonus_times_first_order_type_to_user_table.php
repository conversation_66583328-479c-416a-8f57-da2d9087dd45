<?php

use App\Enums\FirstOrderTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user', function (Blueprint $table) {
            $table->tinyInteger('bonus_times')->after('first_order_at')->index('bonus_times')->default(0);
            $table->enum('first_order_type', FirstOrderTypeEnum::getValues())->after('bonus_times')->index('first_order_type')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

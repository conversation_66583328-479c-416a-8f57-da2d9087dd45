<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyColumnsInTrackingStatusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('tracking_status', 'shipping_carrier')) {
            Schema::table('tracking_status', function (Blueprint $table) {
                $table->string('shipping_carrier')
                    ->nullable()
                    ->change();
            });
        }

        if (!Schema::hasColumn('tracking_status', 'order_number')) {
            Schema::table('tracking_status', function (Blueprint $table) {
                $table->string('order_number', 32)->after('tracking_code');

                $table->unique(['tracking_code', 'order_number']);
            });
        }

        $sm = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('tracking_status');

        if (!array_key_exists('tracking_status_tracking_code_order_number_unique', $indexesFound)) {
            Schema::table(
                'tracking_status',
                function (Blueprint $table) {
                    $table->unique(['tracking_code', 'order_number'], 'tracking_status_tracking_code_order_number_unique');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product_logs', function (Blueprint $table) {
            $table->enum('type', \App\Enums\OrderProductLogEnum::asArray())->change();
            if (!Schema::hasColumn('order_product_logs','custom_timestamp')) {
                $table->timestamp('custom_timestamp')->index('custom_timestamp')->nullable()->default(null)->after('content');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_product_logs', function (Blueprint $table) {
            if (Schema::hasColumn('order_product_logs','custom_timestamp')) {
                $table->dropColumn('custom_timestamp');
            }
        });
    }
};

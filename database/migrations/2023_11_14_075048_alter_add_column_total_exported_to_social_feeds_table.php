<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnTotalExportedToSocialFeedsTable extends Migration
{
    public function up()
    {
        if (!Schema::hasColumn('social_feeds', 'total_exported')) {
            Schema::table('social_feeds', function (Blueprint $table) {
                $table->integer('total_exported')->default(0)->after('total_products');
            });
        }
    }

    public function down(): void {}
}

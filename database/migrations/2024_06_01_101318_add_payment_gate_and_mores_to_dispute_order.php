<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\OrderDisputePaymentGateEnum;
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_dispute', function (Blueprint $table) {
            if (!Schema::hasColumn('order_dispute', 'payment_gate')) {
                $table->enum('payment_gate', OrderDisputePaymentGateEnum::getValues());
            }

            if (!Schema::hasColumn('order_dispute', 'dispute_id')) {
                $table->string('dispute_id', 255)->index();
            }

            if (!Schema::hasColumn('order_dispute', 'is_reopened')) {
                $table->boolean('is_reopened')->default(false);
            }

            if (!Schema::hasColumn('order_dispute', 'inquiry')) {
                $table->boolean('inquiry')->default(false);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {}
};

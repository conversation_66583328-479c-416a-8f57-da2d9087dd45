<?php

use App\Enums\TrackingStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyColumnInTrackingStatusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('tracking_status', 'status')) {
            Schema::table('tracking_status', function (Blueprint $table) {
                $table->enum('status', TrackingStatusEnum::getValues())
                    ->default(TrackingStatusEnum::UNTRACKED)
                    ->change();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableOrderAssignSupplierHistory extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_assign_supplier_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id')->index();
            $table->unsignedBigInteger('order_product_id')->index();
            $table->unsignedBigInteger('supplier_id')->index()->comment('ID của sup (nhà sản xuất)');
            $table->unsignedBigInteger('fulfill_product_id')->index()->comment('Fulfill product id');
            $table->string('fulfill_sku')->index()->comment('Sku của variant fulfill product');
            $table->boolean('cross_shipping')->index()->comment('Ship chéo ?');
            $table->boolean('fulfilled')->default(false)->index()->comment('Đã đẩy đơn sang sup ?');
            $table->boolean('fulfill_order_id')->nullable()->index()->comment('ID đơn mà sup trả về sau khi sup tạo đơn thành công');
            $table->boolean('fulfill_status')->nullable()->index()->comment('Trạng thái đơn sau khi đẩy sang sup');
            $table->timestamp('fulfill_at')->nullable()->index()->comment('Thời điểm đẩy đơn sang sup');
            $table->char('notified')->nullable()->index()->comment('Đã gửi mail thông báo ship chéo cho khách ?');
            $table->unsignedBigInteger('created_by')->index();
            $table->timestamps();
            $table->index('created_at');
            $table->index('updated_at');
            $table->index(['order_id', 'order_product_id', 'supplier_id'], 'primary_index');
            $table->index(['order_product_id', 'fulfill_product_id', 'fulfill_sku'], 'secondary_index');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

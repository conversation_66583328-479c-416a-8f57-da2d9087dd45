<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddLocationCodeAndRemoveBaseCostsInProductVariantTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('product_variant', 'location_code')) {
            Schema::table(
                'product_variant',
                function (Blueprint $table) {
                    $table->string('location_code')->after('base_cost')->default('*'); // world wide
                }
            );
        }
        if (Schema::hasColumn('product_variant', 'base_costs')) {
            Schema::table(
                'product_variant',
                function (Blueprint $table) {
                    $table->dropColumn('base_costs');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

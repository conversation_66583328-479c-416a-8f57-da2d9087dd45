<?php

use App\Enums\ShippingMethodEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSupplierShippingLateRule extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('supplier_shipping_late_rule', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('supplier_id')->index();
            $table->enum('shipping_method', ShippingMethodEnum::getValues());
            $table->json('region');
            $table->json('no_location');
            $table->json('include_location');
            $table->integer('date_late');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

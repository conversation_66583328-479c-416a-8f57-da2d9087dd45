<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateOrderTableForFulfill extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            $table->enum('type', ['regular', 'fulfilment'])
                ->default('regular')
                ->after('stats_status');
            $table->enum('status', ['draft', 'pending', 'processing', 'completed', 'on_hold', 'refunded', 'cancelled', 'archived', 'deleted', 'matched', 'unmatched'])
                ->default('draft')
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

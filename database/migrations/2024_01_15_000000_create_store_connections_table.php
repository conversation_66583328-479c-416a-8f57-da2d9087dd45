<?php

use App\Enums\StoreConnectionPlatformEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('store_connections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('store_id')
                ->constrained('store')
                ->onDelete('cascade');

            $table->enum('platform', StoreConnectionPlatformEnum::getValues());
            $table->json('connection_data')->nullable(); // Store platform-specific connection data
            $table->string('status')->default('active'); // active, inactive
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['store_id', 'platform']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Schema::dropIfExists('store_connections');
    }
};

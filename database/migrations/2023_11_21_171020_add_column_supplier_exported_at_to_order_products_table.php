<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnSupplierExportedAtToOrderProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //run migration
        Schema::table('order_product', function (Blueprint $table) {
            if (!Schema::hasColumn('order_product', 'supplier_exported_at')) {
                $table->timestamp('supplier_exported_at')->nullable()->index('supplier_exported_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

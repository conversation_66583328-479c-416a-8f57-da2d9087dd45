<?php

use App\Enums\FileStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableFileAddStatusField extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('file', 'status')) {
            Schema::table('file', function (Blueprint $table) {
                $table->string('status', 16)->default(FileStatusEnum::ACTIVE)
                    ->comment('file status');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

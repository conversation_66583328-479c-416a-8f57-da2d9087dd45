<?php

use App\Enums\FulfillMappingEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterUpdateTypeInFulfillMappingTable extends Migration
{
    public function up()
    {
        if (Schema::hasColumn('fulfill_mapping', 'type')) {
            Schema::table('fulfill_mapping', function (Blueprint $table) {
                $table->enum('type', FulfillMappingEnum::getValues())->change();
            });
        }
    }

    public function down(): void {}
}

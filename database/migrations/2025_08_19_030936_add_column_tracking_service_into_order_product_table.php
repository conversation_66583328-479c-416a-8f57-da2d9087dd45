<?php

use App\Enums\TrackingServiceEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            $table->enum('tracking_service', TrackingServiceEnum::asArray())->after('tracking_status')->nullable()->index('tracking_service');
        });
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            $table->string('tracking_service')->after('tracking_status')->nullable()->index('tracking_service');
        });
        Schema::table('tracking_status', function (Blueprint $table) {
            $table->enum('tracking_service', TrackingServiceEnum::asArray())->after('shipping_carrier')->nullable()->index('tracking_service');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

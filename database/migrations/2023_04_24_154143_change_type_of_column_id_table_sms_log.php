<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeTypeOfColumnIdTableSmsLog extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('sms_log', function (Blueprint $table) {
            $table->uuid('id')->index('id')->change();
            $table->dropColumn('key');
            $table->string('external_key')->index('external_key')->nullable()->after('id');
            $table->string('order_id')->nullable()->change();
            $table->string('seller_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down() {}
}

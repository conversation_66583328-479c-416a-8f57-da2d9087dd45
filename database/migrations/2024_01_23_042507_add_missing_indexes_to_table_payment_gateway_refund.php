<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_gateway_refund', function (Blueprint $table) {
            $table->index('status');
            $table->index('payment_gateway_id');
            $table->index('order_id');
            $table->index('seller_id');
            $table->index('store_id');
            $table->index('staff_id');
            $table->index('created_at');
            $table->index('updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

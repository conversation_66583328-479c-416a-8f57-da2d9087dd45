<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_product', function (Blueprint $table) {
            $table->double('design_cost')->after('fulfill_cost')->default(0);
        });
        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            $table->double('design_cost')->after('fulfill_cost')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

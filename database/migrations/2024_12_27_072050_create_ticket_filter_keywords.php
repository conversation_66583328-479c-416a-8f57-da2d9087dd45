<?php

use App\Enums\TicketFilterKeywordTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_filter_keywords', function (Blueprint $table) {
            $table->id();
            $table->string('value');
            $table->enum('type', TicketFilterKeywordTypeEnum::asArray());
            $table->timestamps();
        });

        $this->init();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }

    private function init(): void
    {
        $keywords =[
            [
                'type' => TicketFilterKeywordTypeEnum::BY_CONTENT,
                'value' => [
                    'test blocked1',
                    'test blocked2',
                    'website design',
                    'talk about your website',
                    'Xiamen China',
                    'professional hacker',
                    'Congratulations',
                    'the projects',
                    'building website',
                    'designing website',
                    'app development',
                    'App Development',
                    'web development',
                    'Web Development',
                    'website development',
                    'delayed messages to your mail',
                    'Handbag',
                    'Search Console',
                    'quotation',
                    'quote your best price',
                    'oem design',
                    'Active Wear',
                    'search engines',
                    'website redesign',
                    'Business Strategies',
                    'Meta Copyright',
                    'Facebook Team',
                    'Meta Team',
                    'Meta Support',
                    'Advertising Team',
                    'Ads Team',
                    'Page Team',
                    'Ad Team',
                    'Meta Suite',
                    'Web Design',
                    'Web Development',
                    'Meta For Business',
                    'Policy Team',
                    'price quote',
                    'SEO service',
                    'Designer Watches',
                    'Meta Business Support',
                    'Advertising Compliance Team',
                    'SEO Package',
                    'Are you interested',
                    'Wholesale',
                    'Join us',
                    'you are interested',
                    'limited time',
                    'Dear Valued Customer',
                    'diagnosed with Cancer',
                    'Google Ads',
                    'community standards',
                    'Account Security',
                    'unusual activity',
                    'MetaMask',
                    'Electric Bike',
                    'DMARC',
                    'bitcoin',
                    'Business Manager',
                    'BOOM',
                ]
            ],
            [
                'type' => TicketFilterKeywordTypeEnum::BY_EMAIL,
                'value' => [
                    'complaints@',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '@email.cbssports.com',
                    '@newsletters.scrippsweb.com',
                    'nkien.bk2',
                    '<EMAIL>',
                    '<EMAIL>',
                    'hongoceanfreight.com',
                    'ecoweblite.inc',
                    'stylishscreenmedia',
                    'yocverse.com',
                    '@txt.bell.ca',
                    '@altaibalncex.best',
                    '@solarfoodslight.best',
                    's1.alexwer.ru',
                    '@tedooo.com',
                    'poshmark.com',
                    '163.com',
                    'mistralsolutions.com',
                    '@digitalmarketings.co',
                    '@solarfoodslight.best',
                    '<EMAIL>',
                    '@learndesk.us',
                    '@qq.com',
                    '@extralarges.shop',
                    '@sbcglobal.net',
                    '@arlenstawasz.com',
                    '@spearcarpentry.com',
                    '@61power.com',
                    '@garageollier.com',
                    'twitter.com',
                    'harveymiller.',
                    '@x.com',
                    'contentteam.co',
                    'alexdf.ru',
                    'alexwd.ru',
                    '@globalgns.com',
                    'aliangsclub.com',
                    '@3aco-opolis.com',
                    '@aneesho.com',
                    '@rbf-tx1.com',
                    '@cawaiiclub.com',
                    'robby-solar.com',
                    'mytime2work.com',
                    'aquakingpropool.com',
                    'contentsolutions.co',
                    '<EMAIL>',
                    '@wishpond.com',
                    'ecomcontent',
                    '<EMAIL>',
                    '@gulfstatecapital.net',
                    '@anydigitalmarketers.com',
                    '@wellsucceed.com',
                    '@anydigitalmarketers.com',
                    '@mercury.com',
                    '@bbbsefl.org',
                    '<EMAIL>',
                    '<EMAIL>',
                    'contactardados',
                    '<EMAIL>',
                    'evfasthorse.com',
                    'eduardopatini',
                    '<EMAIL>',
                    'seoservice',
                    '@glovespakistan.com',
                    '<EMAIL>',
                    '<EMAIL>',
                    'mailer-daemon',
                    'meta-report',
                    '@wmlauto.com',
                    '@nantengapparel.com',
                    '@facebook.com',
                    'tiktok.com',
                    'freight',
                    'fedex',
                    'usps',
                    'metamask',
                    '<EMAIL>',
                    '@uspodmilitary.com',
                    '@nft.com',
                    '@marketinglll.com',
                    '@xtransfer.cn',
                    '@consumerproductsemail.com',
                    '@o.computinggroups.com',
                    '@altech.co.jp',
                    '@d.talhost.net',
                    '@pennidesigns.com',
                    '@rickwilliamsleadership.com',
                    '@raiffkoso.site',
                    '@xtransfer.cn',
                    '@all-in-one-marketingplatform.net',
                    '@shops1000.com',
                    '@be-cloud.fr',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '@mindinterviews.com',
                    '@getwishpond.com',
                    '@epn.net',
                    '<EMAIL>',
                    '@uehs.us',
                    '<EMAIL>',
                    '<EMAIL>',
                    '@xdeal.ai',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                ],
            ],
            [
                'type' => TicketFilterKeywordTypeEnum::BY_NAME,
                'value' => [
                    'Louis Vuitton',
                    'Mail Delivery System',
                    'Google Search Console',
                    'Haven Zhang',
                    'Mailer Daemon',
                    'Customer Service',
                    'Aleksandr',
                    'Business Suite',
                    'StoreHelp',
                    'Facebook',
                    'Meta',
                    'DHL',
                    'UPS',
                    'USPS',
                    'FedEx',
                    'USPODMILITARY',
                    'FashionShops10',
                    'Zoho Corporation',
                    'Trustpilot',
                    'Contractors1000',
                    'Faᥴebօօk Beveiliging',
                    'NBC Chicago',
                    'Politique de Faᥴebօօk',
                    'Fox Nation',
                    '<EMAIL>',
                    'Faᥴebօօk',
                    '<EMAIL>',
                    '<EMAIL>',
                    'Trust Wallet',
                    '<EMAIL>',
                    'NBC 5 Chicago Weather',
                    'Fox News',
                    '<EMAIL>',
                    'Luxury Watches',
                ]
            ],
            [
                'type' => TicketFilterKeywordTypeEnum::BY_SUBJECT,
                'value' => [
                    "200 MILLION EMAILS",
                    "Amazon complaint",
                    "Business Ideas",
                    "Email lists 100% VALID - Any country in the world",
                    "interested to increase traffic to your website",
                    "LeadsTree.org",
                    "Special Weather Statement",
                    "ktest123",
                    "Congratulations",
                    "Louis Vuitton",
                    "You Have Received a Payment!",
                    "Congrats",
                    "Saffron",
                    "Meta",
                    "SEO",
                    "Offer Inside",
                    "Special Offer",
                    "wholesale",
                    "Time Offer",
                    "Community Standards",
                    "Account Verification",
                    "Digest",
                    "BREAKING NEWS",
                    "Warnung: Kօntօverifizierung erforderlich!!!",
                    "Verzoek om herverificatie van account op Facebook",
                    "Nous avons examiné votre compte Facebook et avons déterminé que ce compte ne respecte pas nos conditions d'utilisation",
                    "Police officer shot, critically hurt in Oak Park, village says",
                    "Warnung: Ihr Facebօօk-Kօntօ wurde wegen Identitätsdiebstahls gemeldet",
                    "Verzoek om herverificatie van account op Facebооk",
                    "Avertissement : Votre compte Facebook contient des",
                    "T-Shirt Factory Insights from Zhejiang Wonderful Technology Co., Ltd. for shop",
                    "Votre compte a été immédiatement verrouillé en raison d'activités",
                    "Es wird verlangt, dass der Kօntօinhaber seine Identität erneut verifiziert",
                    "Fordern Sie sofօrt die Verifizierung Ihres Kօntօs an, um die Nutzung fortzusetzen",
                    "IMPORTANT WARNING: Yᴏur advertising accᴏunt has been lᴏcked!",
                    "Fordern Sie sofօrt die Verifizierung Ihres Kօntօs an",
                    "Die Verifizierung ist erforderlich, um die",
                    "Subscription Renewal Notification"
                ]
            ]
        ];

        $data = [];

        foreach ($keywords as $keyword) {
            foreach ($keyword['value'] as $value) {
                $data[] = [
                    'type' => $keyword['type'],
                    'value' => $value,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        \App\Models\TicketFilterKeywordModel::query()->insert($data);

    }
};

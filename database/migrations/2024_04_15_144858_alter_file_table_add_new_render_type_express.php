<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Enums\FileRenderType;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('file', function (Blueprint $table) {
            $table->enum('render_type', FileRenderType::asArray())->default(FileRenderType::NONE)->change(); // update
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // TODO: Is safe to run this code?

        // DB::transaction(function () {
        //     $total = DB::table('file')->where('render_type', 'express')->count();
        //     if ($total > 0) {
        //         throw new \Exception('There are files with render_type express');
        //     }
        //     Schema::table('file', function (Blueprint $table) {
        //         $newValues = array_diff(FileRenderType::asArray(), ['express']);
        //         if (end($newValues) === 'video') {
        //             $table->enum('render_type', $newValues)->default(FileRenderType::NONE)->change();
        //         }
        //     });
        // });
    }
};

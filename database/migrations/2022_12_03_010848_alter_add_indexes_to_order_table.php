<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexesToOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order');

        Schema::table(
            'order',
            function (Blueprint $table) use ($indexesFound) {
                if (!array_key_exists("customer_email_index", $indexesFound)) {
                    $table->index('customer_email', 'customer_email_index');
                }
                if (!array_key_exists("payment_method_index", $indexesFound)) {
                    $table->index('payment_method', 'payment_method_index');
                }
                if (!array_key_exists("shipping_method_index", $indexesFound)) {
                    $table->index('shipping_method', 'shipping_method_index');
                }
                if (!array_key_exists("sen_fulfill_status_index", $indexesFound)) {
                    $table->index('sen_fulfill_status', 'sen_fulfill_status_index');
                }
                if (!array_key_exists("fraud_status_index", $indexesFound)) {
                    $table->index('fraud_status', 'fraud_status_index');
                }
                if (!array_key_exists("stats_status_index", $indexesFound)) {
                    $table->index('stats_status', 'stats_status_index');
                }
                if (!array_key_exists("type_index", $indexesFound)) {
                    $table->index('type', 'type_index');
                }
                if (!array_key_exists("address_verified_index", $indexesFound)) {
                    $table->index('address_verified', 'address_verified_index');
                }
                if (!array_key_exists("personalized_index", $indexesFound)) {
                    $table->index('personalized', 'personalized_index');
                }
                if (!array_key_exists("shard_id_index", $indexesFound)) {
                    $table->index('shard_id', 'shard_id_index');
                }
                if (!array_key_exists("review_request_status_index", $indexesFound)) {
                    $table->index('review_request_status', 'review_request_status_index');
                }
            }
        );
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

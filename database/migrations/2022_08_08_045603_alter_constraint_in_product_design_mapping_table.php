<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterConstraintInProductDesignMappingTable extends Migration
{
    public function up()
    {
        Schema::table(
            'product_design_mapping',
            function (Blueprint $table) {
                $table->dropForeign('product_design_mapping_product_id_foreign');
                $table->dropForeign('product_design_mapping_supplier_id_foreign');
                $table->dropPrimary('pk_product_design_mapping');
            }
        );
        Schema::table(
            'product_design_mapping',
            function (Blueprint $table) {
                $table->id()->first();
                $table->unsignedBigInteger('product_id')->default(0)->change();
                $table->index('product_id');
                $table->foreign('supplier_id')
                    ->references('id')
                    ->on('supplier')
                    ->onDelete('cascade');
                $table->index('supplier_id');
                $table->unique(['supplier_id', 'print_space', 'product_id'], 'unique_product_design_mapping');
            }
        );
    }

    public function down(): void {}
}

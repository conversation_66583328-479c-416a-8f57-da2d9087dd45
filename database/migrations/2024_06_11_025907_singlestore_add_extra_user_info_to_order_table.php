<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection('singlestore')->table('order', function (Blueprint $table) {
            $table->string('mailbox_number')->nullable()->after('address_2');
            $table->string('house_number')->nullable()->after('address_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};

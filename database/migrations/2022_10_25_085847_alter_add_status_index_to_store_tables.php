<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddStatusIndexToStoreTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('store');

        if (!array_key_exists("status_index", $indexesFound)) {
            Schema::table(
                'store',
                function(Blueprint $table) {
                    $table->index('status','status_index');
                }
            );
        }

        $indexesFound = $sm->listTableIndexes('store_domains');

        if (!array_key_exists("status_index", $indexesFound)) {
            Schema::table(
                'store_domains',
                function(Blueprint $table) {
                    $table->index('status','status_index');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

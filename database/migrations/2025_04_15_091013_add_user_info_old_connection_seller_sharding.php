<?php

use App\Enums\UserInfoKeyEnum;
use App\Enums\UserRoleEnum;
use App\Models\User;
use App\Models\UserInfo;
use Illuminate\Database\Migrations\Migration;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        User::query()
            ->select('id')
            ->whereNotIn('db_connection', ['mysql', 'mysql_sub'])
            ->where('role', '!=', UserRoleEnum::CUSTOMER)
            ->where('sharding_status', UserShardingStatusEnum::COMPLETED)
            ->get()
            ->each(function ($seller) {
                UserInfo::query()
                    ->create([
                        'key' => UserInfoKeyEnum::OLD_CONNECTION,
                        'value' => 'mysql',
                        'user_id' => $seller->id,
                    ]);
            });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};

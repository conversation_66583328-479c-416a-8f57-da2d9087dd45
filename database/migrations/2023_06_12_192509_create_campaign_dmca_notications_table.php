<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateCampaignDmcaNoticationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('campaign_dmca_notifications', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->unsignedBigInteger('seller_id')->index('campaign_dmca_notifications_seller_id');
            $table->string('seller_name')->index('campaign_dmca_notifications_seller_name');
            $table->unsignedBigInteger('notification_id')->index('campaign_dmca_notifications_notification_id');
            $table->json('dmca_url_info')->nullable();
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'))->index('campaign_dmca_notifications_created_at');
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'))->index('campaign_dmca_notifications_updated_at');

            $table->foreign('seller_id')
                ->references('id')
                ->on('user')
                ->onDelete('cascade');
            $table->foreign('notification_id')
                ->references('id')
                ->on('seller_notification')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexFulfillOrderIdToOrderProductTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order_product');

        Schema::table(
            'order_product',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('fulfill_order_id_index', $indexesFound)) {
                    $table->index('fulfill_order_id', 'fulfill_order_id_index');
                }
            }
        );
    }

    public function down(): void {}
}

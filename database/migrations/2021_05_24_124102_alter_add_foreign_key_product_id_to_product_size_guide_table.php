<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddForeignKeyProductIdToProductSizeGuideTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('product_size_guide');

        if (!array_key_exists("product_size_guide_product_id_foreign", $indexesFound)) {
            Schema::table(
                'product_size_guide',
                function (Blueprint $table) {
                    $table->foreign('product_id')->references('id')->on('product')->onDelete('cascade');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

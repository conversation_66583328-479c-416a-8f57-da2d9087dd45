<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterFileUrlColumnLengthInFileTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('file', 'file_url')) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->string('file_url', 512)->change();
                }
            );
        }
        if (Schema::hasColumn('file', 'file_url_2')) {
            Schema::table(
                'file',
                function (Blueprint $table) {
                    $table->string('file_url_2', 512)->change();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

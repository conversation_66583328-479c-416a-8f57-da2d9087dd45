<?php

use App\Enums\AccessExternalApiType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterApiKeysTableAddNoteAndUpdateTypeColumns extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */

    public function up()
    {
        Schema::table('api_keys', function (Blueprint $table) {
            if (!Schema::hasColumn('api_keys', 'note')) {
                $table->string('note')->nullable()->after('status');
            }

            $table->enum('type', AccessExternalApiType::getValues())
                ->default(AccessExternalApiType::SUPPLIER)
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}

    /**
     * @param mixed $array
     * @param mixed $value
     * @return mixed
     */
    private static function array_remove_by_value($array, $value)
    {
        return array_values(array_diff($array, array($value)));
    }

}

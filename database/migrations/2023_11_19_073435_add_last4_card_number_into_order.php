<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLast4CardNumberIntoOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::connection('singlestore')->hasColumn('order', 'last4_card_number')) {
            Schema::connection('singlestore')->table('order', function (Blueprint $table) {
                $table->bigInteger('last4_card_number')->index('last4_card_number')->nullable();
            });
        }

        if (!Schema::hasColumn('order', 'last4_card_number')) {
            Schema::table('order', function (Blueprint $table) {
                $table->bigInteger('last4_card_number')->index('last4_card_number')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

<?php

use App\Enums\OrderCancelRequestStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableOrderCancelRequest extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_cancel_request', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->unsignedBigInteger('order_id')->index('order_id');
            $table->foreign('order_id')->references('id')->on('order');
            $table->enum('status', OrderCancelRequestStatus::getValues())->default(OrderCancelRequestStatus::PENDING)->index('status');
            $table->boolean('sent_email')->default(0)->index('sent_email');
            $table->timestamp('expired_at')->nullable()->index('expired_at');
            $table->mediumText('error_log')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

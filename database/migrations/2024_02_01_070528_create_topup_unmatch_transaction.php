<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTopupUnmatchTransaction extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('topup_unmatch_transactions')) {
            Schema::create('topup_unmatch_transactions', function (Blueprint $table) {
                $table->bigIncrements('id');
                $table->string('transaction_id', 255)->index('transaction_id')->nullable(false);
                $table->string('topup_id', 36)->index('topup_id')->nullable(false);
                $table->json('message')->nullable(false);
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

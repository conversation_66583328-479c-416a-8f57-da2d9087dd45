<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexDomainAndSubdomainToStoreTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('store');

        Schema::table(
            'store',
            function (Blueprint $table) use ($indexesFound) {
                if (!isset($indexesFound['domain_index'])) {
                    $table->index('domain', 'domain_index');
                }
                if (!isset($indexesFound['sub_domain_index'])) {
                    $table->index('sub_domain', 'sub_domain_index');
                }
            }
        );
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexOrderIdInApiLogsTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('api_logs');

        Schema::table(
            'api_logs',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('order_id_index', $indexesFound)) {
                    $table->index('order_id', 'order_id_index');
                }
            }
        );
    }

    public function down(): void {}
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddCampaignLimitColumnToUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('user', 'campaign_limit')) {
            Schema::table(
                'user',
                function (Blueprint $table) {
                    $table->unsignedInteger('campaign_limit')
                        ->default(50)
                        ->comment('Campaign can create per day')
                        ->after('balance');
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

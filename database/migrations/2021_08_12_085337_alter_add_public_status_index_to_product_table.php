<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddPublicStatusIndexToProductTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('product');

        Schema::table(
            'product',
            function (Blueprint $table) use ($indexesFound){
                if (!array_key_exists('public_status_index', $indexesFound)) {
                    $table->index('public_status', 'public_status_index');
                }
            }
        );
    }

    public function down(): void {}
}

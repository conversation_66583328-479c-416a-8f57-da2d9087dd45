<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterSellerNotificationTableAddColumns extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */

    public function up()
    {
        Schema::table('seller_notification', function (Blueprint $table) {
            $table->string('admin_id')
                ->after('seller_id');
            $table->tinyInteger('status')
                ->default(1)
                ->after('message');
            $table->tinyInteger('is_warning')
                ->default(0)
                ->after('message');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}

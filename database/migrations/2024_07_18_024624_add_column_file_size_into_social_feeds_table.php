<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('social_feeds', function (Blueprint $table) {
            $table->double('xml_file_size')->default(0)->after('path');
            $table->double('csv_file_size')->default(0)->after('xml_file_size');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

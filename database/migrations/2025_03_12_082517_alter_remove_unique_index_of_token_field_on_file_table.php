<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasIndex('file', 'file_token_unique')) {
            Schema::table('file', function (Blueprint $table) {
                $table->dropIndex(['token']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {}
};

<?php

namespace Database\Factories;

use App\Enums\PromotionTypeEnum;
use App\Models\PromotionRule;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PromotionRuleFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = PromotionRule::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'type' => $this->faker->randomElement(PromotionTypeEnum::asArray()),
            'discount_code' => Str::random(10),
            'seller_id' => fn() => User::role('seller')->value('id'),
            'rules' => '{"minimum_amount":6,"discount_percentage":5}'
        ];
    }
}

<?php

use Illuminate\Support\Str;

$redisOptions = [
    'cluster' => env('REDIS_CLUSTER', 'redis'),
    'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_') . '_database_'),
];

if (class_exists('Redis')) {
    $redisOptions['serializer'] = env('REDIS_COMPRESS', "no") === "yes" ? Redis::SERIALIZER_IGBINARY : Redis::SERIALIZER_NONE;
    $redisOptions['compression'] = env('REDIS_COMPRESS', "no") === "yes" ? Redis::COMPRESSION_LZF : Redis::COMPRESSION_NONE;
}

return [
    /*
      |--------------------------------------------------------------------------
      | Default Database Connection Name
      |--------------------------------------------------------------------------
      |
      | Here you may specify which of the database connections below you wish
      | to use as your default connection for all database work. Of course
      | you may use many connections at once using the Database library.
      |
     */

    'default' => env('DB_CONNECTION', 'mysql'),
    /*
      |--------------------------------------------------------------------------
      | Database Connections
      |--------------------------------------------------------------------------
      |
      | Here are each of the database connections setup for your application.
      | Of course, examples of configuring each database platform that is
      | supported by Laravel is shown below to make development simple.
      |
      |
      | All database work in Laravel is done through the PHP PDO facilities
      | so make sure you have the driver for your particular database of
      | choice installed on your machine before you begin development.
      |
     */
    'connections' => [
        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],
        'mysql' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
        'mysql_seller' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL_SELLER'),
            'host' => env('DB_HOST_SELLER', '127.0.0.1'),
            'port' => env('DB_PORT_SELLER', '3306'),
            'database' => env('DB_DATABASE_SELLER', 'forge'),
            'username' => env('DB_USERNAME_SELLER', 'forge'),
            'password' => env('DB_PASSWORD_SELLER', ''),
            'unix_socket' => env('DB_SOCKET_SELLER', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
        'mysql_sub' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL_SUB'),
            'host' => env('DB_HOST_SUB', env('DB_HOST_SELLER')),
            'port' => env('DB_PORT_SUB', env('DB_PORT_SELLER')),
            'database' => env('DB_DATABASE_SUB', env('DB_DATABASE') . '_sub'),
            'username' => env('DB_USERNAME_SUB', env('DB_USERNAME_SELLER')),
            'password' => env('DB_PASSWORD_SUB', env('DB_PASSWORD_SELLER')),
            'unix_socket' => env('DB_SOCKET_SUB', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
        'mysql_main_us' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL_MAIN_US'),
            'host' => env('DB_HOST_MAIN_US', isOnDevDatabase() ? env('DB_HOST', '127.0.0.1') : '127.0.0.1'),
            'port' => env('DB_PORT_MAIN_US', '3306'),
            'database' => env('DB_DATABASE_MAIN_US', isOnDevDatabase() ? env('DB_DATABASE', 'forge') : 'forge'),
            'username' => env('DB_USERNAME_MAIN_US', isOnDevDatabase() ? env('DB_USERNAME', 'forge') : 'forge'),
            'password' => env('DB_PASSWORD_MAIN_US', isOnDevDatabase() ? env('DB_PASSWORD', '') : ''),
            'unix_socket' => env('DB_SOCKET_MAIN_US', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
        'mysql_sg' => [
            'driver' => 'mysql',
            'url' => env('DB_REGION_SG_URL'),
            'host' => env('DB_REGION_SG_HOST', isOnDevDatabase() ? env('DB_HOST', '127.0.0.1') : '127.0.0.1'),
            'port' => env('DB_REGION_SG_PORT', '3306'),
            'database' => env('DB_REGION_SG_DATABASE', isOnDevDatabase() ? env('DB_DATABASE', 'forge') : 'forge'),
            'username' => env('DB_REGION_SG_USERNAME', isOnDevDatabase() ? env('DB_USERNAME', 'forge') : 'forge'),
            'password' => env('DB_REGION_SG_PASSWORD', isOnDevDatabase() ? env('DB_PASSWORD', '') : ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
        'mysql_us' => [
            'driver' => 'mysql',
            'url' => env('DB_REGION_US_URL'),
            'host' => env('DB_REGION_US_HOST', isOnDevDatabase() ? env('DB_HOST', '127.0.0.1') : '127.0.0.1'),
            'port' => env('DB_REGION_US_PORT', '3306'),
            'database' => env('DB_REGION_US_DATABASE', isOnDevDatabase() ? env('DB_DATABASE', 'forge') : 'forge'),
            'username' => env('DB_REGION_US_USERNAME', isOnDevDatabase() ? env('DB_USERNAME', 'forge') : 'forge'),
            'password' => env('DB_REGION_US_PASSWORD', isOnDevDatabase() ? env('DB_PASSWORD', '') : ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
        'mysql_eu' => [
            'driver' => 'mysql',
            'url' => env('DB_REGION_EU_URL'),
            'host' => env('DB_REGION_EU_HOST', isOnDevDatabase() ? env('DB_HOST', '127.0.0.1') : '127.0.0.1'),
            'port' => env('DB_REGION_EU_PORT', '3306'),
            'database' => env('DB_REGION_EU_DATABASE', isOnDevDatabase() ? env('DB_DATABASE', 'forge') : 'forge'),
            'username' => env('DB_REGION_EU_USERNAME', isOnDevDatabase() ? env('DB_USERNAME', 'forge') : 'forge'),
            'password' => env('DB_REGION_EU_PASSWORD', isOnDevDatabase() ? env('DB_PASSWORD', 'forge') : ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
        'pgsql' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST_PGSQL', '127.0.0.1'),
            'port' => env('DB_PORT_PGSQL', '5432'),
            'database' => env('DB_DATABASE_PGSQL', 'forge'),
            'username' => env('DB_USERNAME_PGSQL', 'forge'),
            'password' => env('DB_PASSWORD_PGSQL', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],
        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],
        'mail_logs' => [
            'driver' => 'mysql',
            'url' => env('EMAIL_LOGS_DATABASE_URL', env('DATABASE_URL')),
            'host' => env('EMAIL_LOGS_DB_HOST', env('DB_HOST', '127.0.0.1')),
            'port' => env('EMAIL_LOGS_DB_PORT', env('DB_PORT', '3306')),
            'database' => env('EMAIL_LOGS_DB_DATABASE', env('DB_DATABASE', 'forge')),
            'username' => env('EMAIL_LOGS_DB_USERNAME', env('DB_USERNAME', 'forge')),
            'password' => env('EMAIL_LOGS_DB_PASSWORD', env('DB_PASSWORD', '')),
            'unix_socket' => env('EMAIL_LOGS_DB_SOCKET', env('DB_SOCKET', '')),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
        'singlestore' => [
            'driver' => 'mysql',
            'host' => env('SINGLESTORE_DB_HOST'),
            'port' => env('SINGLESTORE_DB_PORT', '3306'),
            'database' => env('SINGLESTORE_DB_DATABASE', 'forge'),
            'username' => env('SINGLESTORE_DB_USERNAME', 'forge'),
            'password' => env('SINGLESTORE_DB_PASSWORD', ''),
            'unix_socket' => env('SINGLESTORE_DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],
    ],
    /*
      |--------------------------------------------------------------------------
      | Migration Repository Table
      |--------------------------------------------------------------------------
      |
      | This table keeps track of all the migrations that have already run for
      | your application. Using this information, we can determine which of
      | the migrations on disk haven't actually been run in the database.
      |
     */
    'migrations' => 'migrations',
    /*
      |--------------------------------------------------------------------------
      | Redis Databases
      |--------------------------------------------------------------------------
      |
      | Redis is an open source, fast, and advanced key-value store that also
      | provides a richer body of commands than a typical key-value system
      | such as APC or Memcached. Laravel makes it easy to dig right in.
      |
     */
    'redis' => [
        'client' => env('REDIS_CLIENT', 'phpredis'),
        'options' => $redisOptions,
        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],
        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],
    ],
    'encryption_key' => env('DB_ENCRYPTION_KEY', env('APP_KEY')),
];

<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_CALLBACK_URL'),
    ],

    'facebook' => [
        'client_id' => env('FACEBOOK_CLIENT_ID'),
        'client_secret' => env('FACEBOOK_CLIENT_SECRET'),
        'redirect' => env('FACEBOOK_REDIRECT_URI')
    ],

    // https://www.name.com/account/settings/api
    'name_dot_com' => [
        'api_username' => env('NAME_DOT_COM_API_USERNAME'),
        'api_token' => env('NAME_DOT_COM_API_TOKEN'),
    ],

    'cloudflare' => [
        'base_url' => env('CLOUDFLARE_BASE_URL', 'https://api.cloudflare.com/client/v4' ),
        'email' => env('CLOUDFLARE_EMAIL'),
        'api_token' => env('CLOUDFLARE_API_TOKEN'),
        'account_id' => env('CLOUDFLARE_ACCOUNT_ID'),
        'custom_hostname_zone_id' => env('CLOUDFLARE_CUSTOM_HOSTNAME_ZONE_ID', 'd04e0bc61d11c034cdc581ad1b5edc2c'),
        'custom_origin_server' => env('CLOUDFLARE_CUSTOM_ORIGIN_SERVER', 'v4-storedns.senprints.com'),
    ],

    'canny' => [
        'private_key' => env('CANNY_PRIVATE_KEY', '************************************'),
    ],

    'jira' => [
        'api_username' => env('JIRA_API_USERNAME'),
        'api_token' => env('JIRA_API_TOKEN')
    ],

    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'assistant_id' => env('OPENAI_ASSISTANT_ID', 'asst_uVjV4y6oJVAb5NRQ6SZxzAkD'),
    ],

    'api_layer' => [
        'api_key' => env('API_LAYER_API_KEY', 'kuxLMjplqOpfDbmTXikiyatxsL2AB6vW')
    ],

    'open_exchange' => [
        'app_id' => env('OPEN_EXCHANGE_RATE_APP_ID', '2987fd88e23c410ca9650d4c305aff2a')
    ],

    'freshdesk' => [
        'api_key' => env('FRESHDESK_API_KEY')
    ],

    // https://ew-plane.lianlianpay-inc.com/settings/developers
    'lianlian' => [
        'developer_id' => env('LIANLIAN_DEVELOPER_ID'),
        'access_token' => env('LIANLIAN_ACCESS_TOKEN'),
        'region' => env('LIANLIAN_REGION', 'sandbox'), // sandbox, uk or us
        'private_key' => env('LIANLIAN_PRIVATE_KEY'),
    ],

    'telegram_debug' => [
        'bot_token' => '**********************************************'
    ],

    'paypal' => [
        'client_id' => env('PAYPAL_CLIENT_ID'),
        'secret' => env('PAYPAL_SECRET'),
        'test_mode' => env('PAYPAL_TEST_MODE', true),
        'partner_merchant_id' => env('PAYPAL_PARTNER_MERCHANT_ID', 'RDZV8L4D4F584'),
        'bn_code' => env('PAYPAL_BN_CODE', 'SensePrints_Ecom'),
        'debug' => env('PAYPAL_DEBUG', true),
    ],

    'google_gemini' => [
        'api_key' => env('GOOGLE_GEMINI_API_KEY'),
    ],

    'ideogram' => [
        'api_key' => env('IDEOGRAM_API_KEY'),
    ],

    'tracking' => [
        '17track' => [
            'api_endpoint' => env('17TRACK_API_ENDPOINT', 'https://api.17track.net/track/v2.2'),
            'secret_key' => env('17TRACK_SECRET_KEY', '19DA07F9F2D8289F9B1769F3CFFFE53F'),
            'weight' => 50,
        ],
        'track123' => [
            'api_endpoint' => env('TRACK123_API_ENDPOINT', 'https://api.track123.com/gateway/open-api/tk/v2.1/track'),
            'secret_key' => env('TRACK123_SECRET_KEY', '98fcbae593dd4756b636495c67994404'),
            'weight' => 50,
        ],
        'trakow' => [
            'api_endpoint' => env('TRAKOW_API_ENDPOINT', 'https://gateway.trakow.com/api'),
            'secret_key' => env('TRAKOW_SECRET_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjUsInR5cGUiOiJoZWFkZXIiLCJ0aW1lc3RhbXAiOjE3NTQ4ODQyOTk0NzksImlhdCI6MTc1NDg4NDI5OSwiZXhwIjoxNzg2NDQxODk5fQ.iSXvIwc5Y6b4a5HstqDbxHR6EQtf-SdDVAMqARtv54E'),
            'weight' => 0,
        ],
    ]
];

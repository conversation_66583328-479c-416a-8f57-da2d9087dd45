<?php

use App\Http\Controllers\Admin\FulfillController;
use App\Http\Controllers\Admin\GoogleSheetApiController;
use App\Http\Controllers\Admin\SellerController;
use App\Http\Controllers\AdsCampaignController;
use App\Http\Controllers\Analytic\SellerController as AnalyticSellerController;
use App\Http\Controllers\Analytic2\SellerController as Analytic2SellerController;
use App\Http\Controllers\Analytic3\SellerController as Analytic3SellerController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\CacheController;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\CollectionCampaignController;
use App\Http\Controllers\CollectionSellerController;
use App\Http\Controllers\CollectionStoreController;
use App\Http\Controllers\ContestController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\ExpressCampaignController;
use App\Http\Controllers\External\PBController;
use App\Http\Controllers\FulfillOrderController;
use App\Http\Controllers\NotificationSettingController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\OrderProductController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PaymentAccountController;
use App\Http\Controllers\PaymentGatewayController;
use App\Http\Controllers\PayoutController;
use App\Http\Controllers\PricingController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductReviewController;
use App\Http\Controllers\PromotionController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\Seller\AiMockupImageController;
use App\Http\Controllers\Seller\AiPromptExampleController;
use App\Http\Controllers\Seller\ChatGptController;
use App\Http\Controllers\Seller\DomainNameController;
use App\Http\Controllers\Seller\GettingStartedController;
use App\Http\Controllers\Seller\StoreConnectionController;
use App\Http\Controllers\Seller\ImageGenerationController;
use App\Http\Controllers\Seller\StoreHeadTagController;
use App\Http\Controllers\Seller\TopupController;
use App\Http\Controllers\SellerTeamController;
use App\Http\Controllers\SitemapController;
use App\Http\Controllers\SmsController;
use App\Http\Controllers\SocialFeedController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\StoreDomainController;
use App\Http\Controllers\TelegramController;
use App\Http\Controllers\TelegramNotificationController;
use App\Http\Controllers\UploadController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WebhookNotificationController;
use App\Http\Middleware\StoreBelongToSeller;
use App\Http\Controllers\Seller\AiImageHistoryController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\BlogCategoryController;

Route::prefix('seller')->middleware(['guest', 'throttle:reset_password'])->group(function () {
    Route::post('/forgot-password', [UserController::class, 'forgotPassword']);
    Route::post('/reset-password/', [UserController::class, 'resetPassword']);
    Route::get('/disable-2fa-social/{jwt}', [AuthController::class, 'disable2faForSocialComplete']);
    Route::get('/email-verify-account/{hash}', [UserController::class, 'verifyAccount'])->withoutMiddleware('throttle:reset_password');
});

Route::prefix('seller')->middleware(['auth.access.token', 'staff_log', 'seller_log'])->group(function () {
    Route::get('/request-verify-email', [UserController::class, 'requestVerifyEmail'])->middleware('throttle:request_verify_email');
    Route::get('/get-list-catalogs', [GoogleSheetApiController::class, 'getListCatalogs']);
    Route::get('/get-canny-token', [UserController::class, 'createCannyToken']);
    // notifications
    Route::prefix('/notifications')->group(function () {
        Route::get('/', [UserController::class, 'getNotify']);
        Route::post('/read-status', [UserController::class, 'postReadStatus']);
        Route::get('/{id}', [UserController::class, 'getNotifyDetail']);
    });

    Route::get('/user-info/{userInfoKey?}', [UserController::class, 'getUserInfo']);
    Route::post('/user-info', [UserController::class, 'saveUserInfo']);
    Route::put('/user-info/email-notification', [UserController::class, 'toggleEmailNotification']);
    Route::put('/user-info/remember-info', [UserController::class, 'rememberInfo']);
    Route::get('/get-user-info-by-key', [UserController::class, 'getInfoByKey']);
    Route::get('/user/settings/general', [UserController::class, 'getGeneralSettings']);
    Route::post('/user/settings/general', [UserController::class, 'saveGeneralSettings']);
    Route::post('/user/settings/password', [UserController::class, 'updatePassword']);
    Route::post('/user/settings/basic-info', [UserController::class, 'updateBasicInfo']);
    Route::get('/user/settings/personal-info', [UserController::class, 'getPersonalInfo']);
    Route::post('/user/settings/personal-info', [UserController::class, 'updatePersonalInfo']);
    Route::get('/user/settings/notifications', [TelegramNotificationController::class, 'checkState']);
    Route::get('/user/settings/test-telegram-notification', [TelegramController::class, 'sendTestMessage']);
    Route::post('/user/settings/notifications', [TelegramNotificationController::class, 'activate']);
    Route::put('/user/settings/notifications', [TelegramNotificationController::class, 'update']);
    Route::delete('/user/settings/notifications', [TelegramNotificationController::class, 'deactivate']);
    Route::get('/user/settings/webhook', [WebhookNotificationController::class, 'get']);
    Route::post('/user/settings/webhook', [WebhookNotificationController::class, 'store']);
    Route::post('/user/settings/test-webhook', [WebhookNotificationController::class, 'test']);
    Route::put('/user/settings/smart-remarketing', [UserController::class, 'enabledSmartRemarketing']);
    Route::put('/user/settings/connect-platform', [UserController::class, 'connectPlatform']);
    Route::get('/user/settings/generate-2fa-key', [AuthController::class, 'generate2faSecret']);
    Route::post('/user/settings/enable-2fa', [AuthController::class, 'enable2fa']);
    Route::post('/user/settings/disable-2fa', [AuthController::class, 'disable2fa']);
    Route::get('/user/settings/disable-2fa-for-social', [AuthController::class, 'disable2faForSocial']);
    Route::resource('/promotions', PromotionController::class)
        ->except(['show']);
    Route::get('/promotions/list', [PromotionController::class, 'getPromotionProducts']);
    Route::get('/promotions/discount_code', [PromotionController::class, 'couponIndex']);
    Route::get('/promotions/{id}', [PromotionController::class, 'detail']);
    Route::post('/promotions/{id}', [PromotionController::class, 'updateStatus']);
    Route::get('/promotions/ajax/{type}', [PromotionController::class, 'ajaxSearch']);

    Route::get('/customers', [CustomerController::class, 'index']);
    Route::get('/export_customers', [CustomerController::class, 'exportCustomers']);
    Route::get('/payment-accounts', [PaymentAccountController::class, 'index']);
    Route::get('/payment-accounts/check-email', [PaymentAccountController::class, 'checkSenprintsEmail']);
    Route::post('/payment-accounts', [PaymentAccountController::class, 'store'])
        ->middleware('throttle:store_payment_account');
    Route::delete('/payment-accounts/{id}', [PaymentAccountController::class, 'destroy']);
    Route::post('/payment-accounts/confirm', [PaymentAccountController::class, 'confirm']);
    Route::post('/payment-accounts/resend-confirm', [PaymentAccountController::class, 'reconfirm']);

    Route::prefix('payouts')->group(function () {
        Route::get('/', [PayoutController::class, 'index'])
            ->middleware('access_switch_account:get_payments');
        Route::get('/render-chart', [PayoutController::class, 'renderChart'])
            ->middleware('access_switch_account:get_payments');
        Route::post('/', [PayoutController::class, 'store'])
            ->middleware(['access_switch_account:update_payment', 'throttle:request_payout']);
        Route::get('/export', [PayoutController::class, 'sellerExportPayouts'])
            ->middleware('access_switch_account:get_payments');
        Route::put('/cancel', [PayoutController::class, 'sellerCancelPayout'])
            ->middleware(['access_switch_account:update_payment', 'throttle:request_payout']);
    });

    Route::post('/switch-account', [AuthController::class, 'doSwitchAccount']);
    Route::post('/switch-back-account', [AuthController::class, 'doSwitchBack']);

    Route::prefix('collections')->group(function () {
        Route::get('/seller', [CollectionSellerController::class, 'index']);
        Route::get('/lite', [CollectionSellerController::class, 'getCollectionForSelect']);
        Route::post('/seller', [CollectionSellerController::class, 'store']);
        Route::delete('/seller/{id}', [CollectionSellerController::class, 'destroy']);
        Route::get('/store', [CollectionStoreController::class, 'index']);
        Route::post('/store', [CollectionStoreController::class, 'store']);
        Route::get('/campaign', [CollectionCampaignController::class, 'index']);
        Route::post('/campaign', [CollectionCampaignController::class, 'store']);
        Route::put('/banner', [CollectionSellerController::class, 'updateCollectionBanner']);
        Route::post('/banner/delete', [CollectionSellerController::class, 'deleteCollectionBanner']);
        Route::get('{collection_id}', [CollectionSellerController::class, 'getInfo']);
        Route::put('{collection_id}/name', [CollectionSellerController::class, 'updateName']);
        Route::get('{collection_id}/featured-campaigns', [CollectionSellerController::class, 'getFeaturedCampaigns']);
        Route::put('{collection_id}/featured-campaigns', [CollectionSellerController::class, 'updateFeaturedCampaigns']);
        Route::delete('{collection_id}/campaign/{campaign_id}', [CollectionCampaignController::class, 'removeCampaign']);
    });

    Route::prefix('store')->group(function () {
        Route::get('/', [StoreController::class, 'indexBySeller']);
        Route::post('/{store_id}/info', [StoreController::class, 'updateInfo']);
        Route::get('/{store_id}/info', [StoreController::class, 'show']);
        Route::post('/{store_id}/advanced', [StoreController::class, 'updateAdvanced']);
        Route::post('/{store_id}/contact', [StoreController::class, 'updateContact']);
        Route::post('/check-trademark-domain', [StoreController::class, 'checkTrademarkDomain']);
        Route::post('/check-trademark-store-domain', [StoreController::class, 'checkTrademarkDomain'])->middleware('skip.validate.tld');
        Route::post('/check-duplicate-subdomain', [StoreController::class, 'checkDuplicateSubdomain']);

        Route::patch('/{store_id}/woocommerce-webhook', [StoreController::class, 'updateWooCommerceWebhook'])
            ->name('stores.update-woocommerce-webhook');

        Route::delete('/connections/{connection}', [StoreConnectionController::class, 'disconnect']);

        Route::middleware('seller_has_store:store_id')->group(function () {
            Route::post('/{store_id}/tracking', [StoreController::class, 'updateStoreTrackingCode']);
            Route::post('/{store_id}/banners', [StoreController::class, 'saveBanners']);
            Route::delete('/{store_id}/banners/{banner_id}', [StoreController::class, 'deleteStoreBanner']);

            Route::get('/{store_id}/navigation/tree', [StoreController::class, 'fetchNavigationTree']);
            Route::get('/{store_id}/navigation/parent', [StoreController::class, 'fetchNavigationParents']);
            Route::get('/{store_id}/navigation', [StoreController::class, 'fetchNavigation']);
            Route::post('/{store_id}/navigation', [StoreController::class, 'updateNavigation']);
            Route::put('/{store_id}/navigation/{navigationId}', [StoreController::class, 'editNavigation']);
            Route::delete('/{store_id}/navigation/{navigationId}', [StoreController::class, 'deleteNavigation']);

            Route::get('/{store_id}/campaign', [StoreController::class, 'getAddedCampaigns']);
            Route::post('/{store_id}/campaign', [StoreController::class, 'addCampaign']);
            Route::delete('/{store_id}/campaign/{campaignId}', [StoreController::class, 'removeCampaign']);

            Route::get('/{store_id}/collection', [StoreController::class, 'getAddedCollections']);
            Route::post('/{store_id}/collection', [StoreController::class, 'addCollection']);
            Route::delete('/{store_id}/collection/{collectionId}', [StoreController::class, 'removeCollection']);

            Route::get('/{store_id}/abandoned_email_config', [NotificationSettingController::class, 'abandonedEmailConfig']);
            Route::post('/{store_id}/abandoned_email_config', [NotificationSettingController::class, 'abandonedEmailConfigUpdate']);

            Route::post('/{store_id}/bulk_navigation', [StoreController::class, 'bulkUpdateNavigation']);
            Route::post('/{store_id}/clear-cache', [StoreController::class, 'clearCacheStore']);

            Route::get('/{store_id}/verify_domain', [StoreController::class, 'verifyDomain']);
            Route::get('/{store_id}/links', [StoreController::class, 'getStoreLinks']);
            Route::post('/{store_id}/clone', [StoreController::class, 'cloneStore']);

            Route::get('/{store_id}/head-tag', [StoreHeadTagController::class, 'index']);
            Route::post('/{store_id}/head-tag', [StoreHeadTagController::class, 'store']);
            Route::get('/{store_id}/head-tag/{tag_id}', [StoreHeadTagController::class, 'show']);
            Route::delete('/{store_id}/head-tag/{tag_id}', [StoreHeadTagController::class, 'destroy']);
            Route::put('/{store_id}/head-tag/{tag_id}', [StoreHeadTagController::class, 'update']);
            Route::put('/{store_id}/head-tag/{tag_id}/toggle', [StoreHeadTagController::class, 'toggle']);


            // API to buy new domain
            Route::get('/{store_id}/buy-domain/search', [DomainNameController::class, 'search']);
            Route::get('/{store_id}/buy-domain/check', [DomainNameController::class, 'check']);
            Route::post('/{store_id}/buy-domain/buy', [DomainNameController::class, 'buy']);
        });

        Route::delete('/clear-all-cache', [StoreController::class, 'clearCacheAllStore']);
        Route::put('/{storeId}/domain', [StoreController::class, 'addDomain']);

        Route::get('/{storeId}/promotions', [PromotionController::class, 'getByStoreId']);
        Route::post('/{storeId}/promotions', [PromotionController::class, 'updateByStoreId']);

        Route::middleware(StoreBelongToSeller::class)->group(function () {
            Route::get('/{store_id}/abandoned_sms_config', [SmsController::class, 'getStoreSmsConfigs']);
            Route::post('/{store_id}/abandoned_sms_config', [SmsController::class, 'updateStoreSmsConfigs']);

            // tracking
            Route::get('/{storeId}/tracking_code', [StoreController::class, 'getStoreTrackingCode']);
            Route::put('/{storeId}/tracking_code', [StoreController::class, 'updateStoreTrackingCode']);

            // Custom domains
            Route::get('/{storeId}/custom-domains', [StoreDomainController::class, 'index']);
            Route::put('/{storeId}/add-custom-domain', [StoreDomainController::class, 'store']);
            Route::patch('/{storeId}/verify-custom-domain/{storeDomainId}', [StoreDomainController::class, 'verify']);
            Route::delete('/{storeId}/delete-custom-domain/{storeDomainId}', [StoreDomainController::class, 'destroy']);
            Route::post('/{storeId}/set-default-domain/{storeDomainId}', [StoreDomainController::class, 'setDefault']);
        });

        Route::put('/{storeId}/enabled-smart-remarketing', [StoreController::class, 'enabledSmartRemarketing']);
        Route::put('/{storeId}/save-notification-settings', [StoreController::class, 'saveNotificationSettings']);

        Route::prefix('{storeId}')->group(function () {
            Route::prefix('blog-categories')->group(function () {
                Route::get('/', [BlogCategoryController::class, 'index']);
                Route::get('/list-for-select', [BlogCategoryController::class, 'listForSelect']);
                Route::post('/', [BlogCategoryController::class, 'save']);
                Route::delete('/{id}', [BlogCategoryController::class, 'destroy']);
            });
        });
    });

    Route::get('/check-can-create-campaign', [CampaignController::class, 'checkCanCreateAPI']);

    Route::prefix('campaign')->middleware('access_switch_account:update_campaign')->group(function () {
        Route::get('product-variants', [ProductController::class, 'sellerGetProductVariants']);
        Route::post('product-variants', [ProductController::class, 'sellerSaveProductVariants']);
        Route::get('template_products', [CampaignController::class, 'listTemplateProducts']); // Get list template products
        Route::post('calc-extra-print-cost-products', [CampaignController::class, 'calcExtraPrintCost']); // Get list template products
        Route::get('collections', [CampaignController::class, 'listCollections']); // List public collection DONE
        Route::get('stores', [CampaignController::class, 'listStores']); // List stores of campaign DONE
        Route::get('new', [CampaignController::class, 'createDraftCampaign']); // Create and return new campaign id DONE
        Route::get('designs', [CampaignController::class, 'getCampaignDesigns']); // Create and return new campaign id DONE
        Route::put('collections', [CampaignController::class, 'addCollection']); // Add collection on campaign create page DONE
        Route::delete('remove_product_design', [CampaignController::class, 'removeProductDesign']);
        Route::delete('remove_campaign_design', [CampaignController::class, 'removeCampaignDesign']);
        Route::put('{campaign_id}/launch', [CampaignController::class, 'launchCampaign']); // Launch campaign
        Route::put('{campaign_id}/select_products', [CampaignController::class, 'selectProducts']); // Select product DONE
        Route::put('combo/{campaign_id}/select_products', [CampaignController::class, 'selectProductsForComboCampaign']);
        Route::post('{campaign_id}/upload_thumbnail', [CampaignController::class, 'uploadThumbnailForComboCampaign']);
        Route::post('update_tmp_files', [CampaignController::class, 'moveTempFileToOriginalPath']); // Move tmp file to ogirinal path
        Route::put('{campaign_id}/products', [CampaignController::class, 'updateProducts']); // Bulk update products DONE
        Route::delete('{campaign_id}/products', [CampaignController::class, 'deleteProduct']); // Soft delete product from selected product DONE
        Route::post('check_slug', [CampaignController::class, 'isSlugAvailable']);
        Route::post('bulk', [CampaignController::class, 'bulkCreateCampaigns']);
        Route::put('bulk-edit', [CampaignController::class, 'bulkEdit']); // bulk update campaigns
        Route::put('bulk/add-to-collection', [CampaignController::class, 'bulkAddToCollection']);
        Route::post('bulk/save-origin-design', [CampaignController::class, 'bulkUploadSaveOriginDesign']);
        Route::put('bulk/remove-from-collection', [CampaignController::class, 'bulkRemoveFromCollection']);
        Route::put('bulk/add-to-store', [CampaignController::class, 'bulkAddToStore']);
        Route::put('bulk/remove-from-store', [CampaignController::class, 'bulkRemoveFromStore']);
        Route::put('bulk/delete', [CampaignController::class, 'bulkDeleteDraftCampaigns'])->middleware('campaign.action');
        Route::put('bulk/batch_status', [CampaignController::class, 'bulkUpdate'])->middleware('campaign.action');
        Route::post('bulk/launch', [CampaignController::class, 'launchBulkCampaigns']);
        Route::put('bulk/list-on-marketplace', [CampaignController::class, 'bulkListOnMarketplace']);
        Route::post('save-design', [CampaignController::class, 'saveDesign3d']);
        Route::post('save-design-express', [CampaignController::class, 'saveDesignExpress']);
        Route::post('update_product_default_color', [CampaignController::class, 'changeProductDefaultColor']); // update product default color
        Route::post('{campaign_id}/duplicate', [CampaignController::class, 'duplicateCampaign']);
        Route::put('{campaign_id}/status', [CampaignController::class, 'changeCampaignStatus']);
        Route::get('{campaign_id}/images', [CampaignController::class, 'getProductImagesFromCampaign']);
        Route::post('{campaign_id}/images', [CampaignController::class, 'saveProductsThumbnail']);

        Route::get('{campaignId}/custom-option-mockups', [CampaignController::class, 'getCustomOptionMockups']);
        Route::post('{campaignId}/custom-option-mockup-upload', [CampaignController::class, 'customOptionMockupUpload']);
        Route::post('{campaignId}/custom-option-mockup-remove', [CampaignController::class, 'customOptionMockupRemove']);

        //bulk v2
        Route::get('can-bulk-create', [CampaignController::class, 'canBulkCreate']); // Check if can bulk create camp
        Route::get('bulk-v2/{campaignId}/download-logs', [CampaignController::class, 'downloadBulkLogs']);

        // edit campaign design
        Route::post('edit-design', [CampaignController::class, 'editCampaignDesign']);

        //save custom campaign designs
        Route::post('save-custom-designs', [CampaignController::class, 'saveSellerCustomDesigns']);
    });

    Route::get('/campaign/{campaign_id}', [CampaignController::class, 'show']);
    Route::get('/campaign/design/{campaign_id}', [CampaignController::class, 'downloadDesign']);
    Route::get('/campaign/font/{campaign_id}', [CampaignController::class, 'downloadFont']);

    Route::post('/campaign/bulk-v2', [CampaignController::class, 'bulkCreateCampaignV2']);
    Route::post('/campaign/bulk-v2/save-design', [CampaignController::class, 'saveCampaignDesignsBulkV2']);


    Route::prefix('express-campaign')->middleware('access_switch_account:update_campaign')->group(function () {
        Route::get('/detail/{campaign_id}', [ExpressCampaignController::class, 'expressCampDetail']);
        Route::post('/store', [ExpressCampaignController::class, 'store']);
        Route::post('/update', [ExpressCampaignController::class, 'updateExpressCampaign']);
    });

    Route::prefix('template-campaign')->middleware('access_switch_account:update_campaign')->group(function () {
        Route::get('/info/{campaign_id}', [ExpressCampaignController::class, 'templateInfo']);
        Route::get('/detail/{campaign_id}', [ExpressCampaignController::class, 'templateDetail'])->name('express-campaign.template.show');
        Route::get('/template-products', [ExpressCampaignController::class, 'listTemplateProducts']);
        Route::post('/create-template', [ExpressCampaignController::class, 'createNewTemplate']);
        Route::post('/update-template', [ExpressCampaignController::class, 'updateTemplateCampaign'])->name('express-campaign.template.update');
    });

    // move temp file s3
    Route::post('move-temp-file', [UploadController::class, 'moveTempFile']);

    Route::prefix('fonts')->middleware('access_switch_account:update_campaign')->group(function () {
        //custom font
        Route::get('/', [CampaignController::class, 'getFonts']);
        Route::post('/', [UploadController::class, 'uploadCustomFont']);
    });

    Route::group(['prefix' => 'analytic', 'middleware' => 'access_switch_account:get_reports'], function () {
        Route::post('campaign/{campaignId}', [AnalyticSellerController::class, 'campaign'])
            ->middleware('access_switch_account:update_campaign');
        Route::post('dashboard', [AnalyticSellerController::class, 'dashboard'])->name('analytic.dashboard');
        Route::post('countries', [AnalyticSellerController::class, 'getCountries']);
        Route::post('stores', [AnalyticSellerController::class, 'getStores']);
        Route::post('ads', [AnalyticSellerController::class, 'getAds']);
        Route::post('devices', [AnalyticSellerController::class, 'getDevices']);
        Route::post('template_products', [AnalyticSellerController::class, 'getTemplateProducts']);
        Route::get('chart', [AnalyticSellerController::class, 'getChart']);
        Route::post('compares', [AnalyticSellerController::class, 'getCompares']);
        Route::post('compares-to-platform', [AnalyticSellerController::class, 'compareToPlatform']);
    });

    Route::group(['prefix' => 'analytic2', 'middleware' => 'access_switch_account:get_reports'], function () {
        Route::post('campaign/{campaignId}', [Analytic2SellerController::class, 'campaign'])
            ->middleware('access_switch_account:update_campaign');
        Route::post('dashboard', [Analytic2SellerController::class, 'dashboard'])->name('analytic2.dashboard');
        Route::post('countries', [Analytic2SellerController::class, 'getCountries']);
        Route::post('stores', [Analytic2SellerController::class, 'getStores']);
        Route::post('ads', [Analytic2SellerController::class, 'getAds']);
        Route::post('devices', [Analytic2SellerController::class, 'getDevices']);
        Route::post('template_products', [Analytic2SellerController::class, 'getTemplateProducts']);
        Route::get('chart', [Analytic2SellerController::class, 'getChart']);
        Route::post('compares', [Analytic2SellerController::class, 'getCompares']);
        Route::post('compares-to-platform', [Analytic2SellerController::class, 'compareToPlatform']);
    });

    Route::group(['prefix' => 'analytic3', 'middleware' => 'access_switch_account:get_reports'], function () {
        Route::post('campaign/{campaignId}', [Analytic3SellerController::class, 'campaign'])->middleware('access_switch_account:update_campaign');
        Route::post('dashboard', [Analytic3SellerController::class, 'dashboard'])->name('analytic3.dashboard');
        Route::post('countries', [Analytic3SellerController::class, 'getCountries']);
        Route::post('stores', [Analytic3SellerController::class, 'getStores']);
        Route::post('ads', [Analytic3SellerController::class, 'getAds']);
        Route::post('devices', [Analytic3SellerController::class, 'getDevices']);
        Route::post('template_products', [Analytic3SellerController::class, 'getTemplateProducts']);
        Route::get('chart', [Analytic3SellerController::class, 'getChart']);
        Route::post('compares', [Analytic3SellerController::class, 'getCompares']);
        Route::post('compares-to-platform', [Analytic3SellerController::class, 'compareToPlatform']);
    });


    Route::post('pages/check-slug', [PageController::class, 'checkSlug']);
    Route::put('pages/{page_id}/status', [PageController::class, 'switchStatus']);
    Route::get('pages/templates', [PageController::class, 'templates']);
    Route::get('pages/templates/{template_id}', [PageController::class, 'templateInfo']);
    Route::resource('pages', PageController::class);

    Route::get('/referral', [ReferralController::class, 'index']);
    Route::get('/referral/analytics', [ReferralController::class, 'analytics']);
    Route::get('/referral/export-info', [ReferralController::class, 'exportInfo']);

    Route::get('/referral-sales', [ReferralController::class, 'indexSales']);
    Route::get('/referral-sales/analytics', [ReferralController::class, 'analyticsSales']);

    Route::prefix('team')->group(function () {
        Route::get('/', [SellerTeamController::class, 'index']);
        Route::get('/invite', [SellerTeamController::class, 'indexInvitation']);
        Route::post('/invite', [SellerTeamController::class, 'inviteMember']);
        Route::put('/invite/{sellerId}', [SellerTeamController::class, 'acceptInvite']);
        Route::delete('/invite/{sellerId}', [SellerTeamController::class, 'declineInvite']);
        Route::put('/status/{sellerId}', [SellerTeamController::class, 'updateStatus']);
        Route::delete('/delete/{sellerId}', [SellerTeamController::class, 'deleteInvite']);
        Route::delete('/delete-disabled/{sellerId}', [SellerTeamController::class, 'deleteDisabled']);
        Route::put('/role/{sellerId}', [SellerTeamController::class, 'updateRole']);
    });

    Route::prefix('/fulfill-orders')->group(function () {
        Route::post('/import', [OrderController::class, 'sellerImportFulfillOrders']);
        Route::get('/export', [OrderController::class, 'sellerExportFulfillOrders']);
        Route::get('/export-summary', [OrderController::class, 'sellerExportFulfillOrderSummary']);
    });

    Route::prefix('/order')->group(function () {
        Route::post('/edit-address/{id}', [OrderController::class, 'editAddress']);
        Route::get('/export', [OrderController::class, 'sellerExportCustomOrders']);
        Route::get('/export-v2', [OrderController::class, 'sellerExportCustomOrdersV2']);
        Route::post('/import-design', [OrderController::class, 'sellerImportDesign']);
    });

    Route::prefix('order-product')->group(function () {
        Route::post('/approve-custom-option-design/{id}', [OrderProductController::class, 'approveOrderProductCustomOptionDesign'])->middleware('access_switch_account:update_order');
        Route::post('/approve-product-design/{id}', [OrderProductController::class, 'approveOrderProductDesign'])->middleware('access_switch_account:update_order');
    });

    Route::get('/authorized-accounts', [SellerTeamController::class, 'getAuthorizedAccounts']);

    Route::get('/get-pb-token', [PBController::class, 'getToken']);
    Route::prefix('feed')->middleware('access_switch_account:update_social_feed')->group(function () {
        Route::get('/index/{id?}', [SocialFeedController::class, 'indexSeller']);
        Route::get('/options', [SocialFeedController::class, 'indexOptionsSizesAndColors']);
        Route::post('/update', [SocialFeedController::class, 'updateSeller']);
        Route::post('/refresh/{id}', [SocialFeedController::class, 'refreshSeller']);
        Route::delete('/delete/{id}', [SocialFeedController::class, 'deleteSeller']);
        Route::post('/restore/{id}', [SocialFeedController::class, 'restoreSeller']);
        Route::post('/update-status/{id}', [SocialFeedController::class, 'updateStatusSeller']);
        Route::post('/duplicate/{id}', [SocialFeedController::class, 'duplicate']);
        Route::post('/import-exclude_ids', [SocialFeedController::class, 'importExcludeIds']);
        Route::post('/import-remove-excluded_ids', [SocialFeedController::class, 'importRemoveExcludedIds']);
    });

    Route::prefix('order-fulfillment')->group(function () {
        Route::get('/', [FulfillOrderController::class, 'index']);
        Route::get('/{order_id}/edit', [FulfillOrderController::class, 'getFulfillOrder']);
        Route::put('/update-address', [FulfillOrderController::class, 'updateAddress']);
        Route::put('/update-fba-order', [FulfillOrderController::class, 'updateFBAOrder']);
        Route::post('/upload-image', [UploadController::class, 'uploadNewImageOrderFulfill']);
        Route::post('/upload-file', [UploadController::class, 'uploadFileFBAOrder']);
        Route::post('/update-products', [FulfillOrderController::class, 'updateProducts']);
        Route::get('/fulfill-info', [FulfillOrderController::class, 'userFulfillInfo']);
        Route::get('/template-products', [FulfillOrderController::class, 'getTemplateProducts']);
        Route::get('/{templateId}/variants', [FulfillOrderController::class, 'getVariantsByTemplate']);
        Route::put('/address-status', [FulfillOrderController::class, 'updateAddressStatus']);
        Route::put('/status/{action}', [FulfillOrderController::class, 'updateStatus']);
        Route::put('/process-orders', [FulfillOrderController::class, 'processFulfillOrder']);
        Route::post('/save-new-order', [FulfillOrderController::class, 'saveNewOrder']);
        Route::post('/save-order-products', [FulfillOrderController::class, 'saveOrderProducts']);
        Route::get('/download-product-catalog', [FulfillOrderController::class, 'exportListProducts']);
        Route::get('/download-base-cost-catalog', [FulfillOrderController::class, 'exportListBaseCosts']);
        Route::get('/download-shipping-cost-catalog', [FulfillOrderController::class, 'exportListShippingCosts']);
        Route::put('/template-product', [FulfillOrderController::class, 'saveOrderTemplateProduct']);
        Route::post('/save-new-fba-order', [FulfillOrderController::class, 'saveNewFBAOrder']);
        Route::get('/media', [UploadController::class, 'getFileMedia']);
        Route::post('/media/upload-file', [UploadController::class, 'uploadFileMedia']);
    });

    // For SMS Marketing
    Route::prefix('/sms')->group(function () {
        Route::post('/user-topup-sms-credit', [SmsController::class, 'topupSmsCredit']);
        Route::get('/user-sms-credit', [SmsController::class, 'getSmsCredit']);
        Route::get('/history', [SmsController::class, 'getHistory']);
    });

    Route::prefix('/reports')->group(function () {
        Route::get('/abandoned-orders', [ReportController::class, 'abandonedOrders']);
        Route::post('/abandoned-orders/detail', [ReportController::class, 'abandonedOrdersDetail']);
    });

    Route::prefix('/sitemap')->group(function () {
        Route::middleware('seller_has_store:store_id')->group(function () {
            Route::get('/{store_id}', [SitemapController::class, 'index']);
            Route::post('/{store_id}/create', [SitemapController::class, 'store']);
            Route::delete('/{store_id}/{sitemapId}', [SitemapController::class, 'destroy']);
        });

        // sitemap_url
        Route::get('/{id}/url', [SitemapController::class, 'urlIndex']);
        Route::post('/{id}/url', [SitemapController::class, 'storeUrl']);
        Route::delete('/{sitemapId}/url/{urlId}', [SitemapController::class, 'destroyUrl']);
        Route::post('/{id}/import', [SitemapController::class, 'import']);
    });

    Route::prefix('payment-gateways')->group(function () {
        Route::get('/', [PaymentGatewayController::class, 'index']);
        Route::post('/create', [PaymentGatewayController::class, 'create']);
        Route::post('/update', [PaymentGatewayController::class, 'update']);
        Route::get('/user-stores', [PaymentGatewayController::class, 'getUserStores']);
        Route::post('/update-status', [PaymentGatewayController::class, 'updateStatus']);
        Route::get('/create-paypal-ref-link', [PaymentGatewayController::class, 'createPaypalRefLink']);
        Route::get('/{id}', [PaymentGatewayController::class, 'show']);
        Route::delete('/{id}', [PaymentGatewayController::class, 'destroy']);
        Route::put('/update-paypal-merchant-id', [PaymentGatewayController::class, 'updatePayPalMerchantId']);
        Route::post('/disconnect-paypal', [PaymentGatewayController::class, 'disconnectPayPalMerchantId']);
    });

    Route::get('/contest', ContestController::class);
    Route::get('/contest-info', [ContestController::class, 'info']);

    Route::prefix('pricing')
        ->middleware('user_has_tag:pricing')
        ->group(function () {
            Route::get('/', [PricingController::class, 'index']);
            Route::put('save', [PricingController::class, 'save']);
            Route::get('test-price-percent-list', [PricingController::class, 'testPricePercentList']);
            Route::get('test-price-analytics', [PricingController::class, 'analytics']);
        });

    // Seller topup balance
    Route::prefix('topup')->group(function () {
        Route::get('/', [TopupController::class, 'index']);
        Route::put('/', [TopupController::class, 'store']);
        Route::post('/cancel/{transactionId}', [TopupController::class, 'cancel']);
        Route::post('/claim-unmatched', [TopupController::class, 'claimTopupUnmatch']);
    });

    Route::get('/product-reviews', [ProductReviewController::class, 'index']);
    Route::post('/product-reviews/update-status', [ProductReviewController::class, 'updateStatus']);
    Route::post('/product-reviews/import', [ProductReviewController::class, 'importReviews']);

    Route::get('/available-pingpong-payout', [PaymentAccountController::class, 'pingPongAvailable']);

    Route::prefix('domain')->group(function () {
        Route::get('/', [DomainNameController::class, 'index']);
        Route::patch('/{id}/toggle-auto-renew', [DomainNameController::class, 'toggleAutoRenew']);
        Route::put('/{store}/update-expired-date', [DomainNameController::class, 'updateDomainExpirationDate']);
    });

    // For Chat GPT
    Route::prefix('ai-generate')->group(function () {
        Route::get('/campaign-title/{campaign}', [ChatGptController::class, 'campaignTitle']);
        Route::get('/campaign-description/{campaign}', [ChatGptController::class, 'campaignDescription']);
        Route::post('/campaign-content', [ChatGptController::class, 'generateCampaignContent']);
        Route::get('/store-title', [ChatGptController::class, 'storeTitle']);
        Route::get('/store-description', [ChatGptController::class, 'storeDescription']);
        Route::get('/custom-page-content', [ChatGptController::class, 'customPageContent']);
        Route::get('/domain-name-ideas', [ChatGptController::class, 'domainNameIdeas']);
        Route::get('/store-title-v2', [ChatGptController::class, 'storeTitleV2']);
        Route::get('/store-description-v2', [ChatGptController::class, 'storeDescriptionV2']);
        Route::get('/blog-title/{storeId}', [ChatGptController::class, 'blogTitle']);
        Route::get('/blog-content/{storeId}', [ChatGptController::class, 'blogContent']);
    });

    Route::get('/getting-started', GettingStartedController::class);
    Route::get('/category-list', [CategoryController::class, 'getCategoryList']);

    Route::get('/cta/{notification_id}', [SellerController::class, 'sellerClickTracking']);
    Route::prefix('utm-campaign')->group(function () {
        Route::post('track', [AdsCampaignController::class, 'trackSubmitted']);
    });

    Route::prefix('/cache')->group(function () {
        Route::put('/clear-campaign', [CacheController::class, 'sellerClearCampaignCache']);
    });

    // AI Image Generation
    Route::post('/generate-image', [ImageGenerationController::class, 'generate']);
    Route::post('/remix-image', [ImageGenerationController::class, 'remix']);
    Route::post('/describe-image', [ImageGenerationController::class, 'describe']);
    Route::post('/describe-image-2', [ImageGenerationController::class, 'describe2']);
    Route::post('/upscale-image', [ImageGenerationController::class, 'upscale']);

    // AI Mockup Image Generation
    Route::post('/ai-mockup/generate', [AiMockupImageController::class, 'generate']);
    Route::post('/ai-mockup/generate2', [AiMockupImageController::class, 'generate2']); // for testing OpenAI
    Route::post('/ai-mockup/save-prompt', [AiMockupImageController::class, 'savePrompt']);
    Route::get('/ai-mockup/template-products', [AiMockupImageController::class, 'getTemplateProducts']);
    Route::get('/ai-mockup/prompt-templates', [AiMockupImageController::class, 'getPromptTemplates']);
    Route::get('/ai-mockup/prompt-examples', [AiPromptExampleController::class, 'index']);

    // AI Image Generation History
    Route::prefix('/ai-images-history')->group(function () {
        Route::get('/', [AiImageHistoryController::class, 'index']);
        Route::get('/{id}', [AiImageHistoryController::class, 'show']);
        Route::delete('/{id}', [AiImageHistoryController::class, 'destroy']);
    });

    Route::group(['prefix' => '/orders/sen-design', 'middleware' => 'access_switch_account:process_design'], function () {
        Route::get('/', [FulfillController::class, 'listingOrderProductSenDesign']);
        Route::post('approve-design', [OrderProductController::class, 'approveSenPrintsDesign']);
        Route::post('upload-design', [FulfillController::class, 'uploadDesign']);
        Route::delete('delete-design', [FulfillController::class, 'deleteDesign']);
    });

    Route::prefix('blogs')->group(function () {
        Route::get('/', [BlogController::class, 'index']);
        Route::get('/{id}', [BlogController::class, 'show']);
        Route::post('/', [BlogController::class, 'save']);
        Route::put('/{id}/status', [BlogController::class, 'updateStatus']);
        Route::delete('/{id}', [BlogController::class, 'destroy']);
    });
});

<?php

use App\Http\Controllers\Admin\DevReportController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\CampaignController as InternalCampaignController;
use App\Http\Controllers\CartJobController;
use App\Http\Controllers\CloudFlareController;
use App\Http\Controllers\CrispController;
use App\Http\Controllers\EmailController;
use App\Http\Controllers\FeaturedInfoController;
use App\Http\Controllers\PingController;
use App\Http\Controllers\ProductController as AdminProductController;
use App\Http\Controllers\ProductReviewController;
use App\Http\Controllers\PromotionController;
use App\Http\Controllers\Public\AiMockupPreviewController;
use App\Http\Controllers\SEOContentController;
use App\Http\Controllers\SocialFeedController;
use App\Http\Controllers\Storefront\AffiliateController;
use App\Http\Controllers\Storefront\CampaignController;
use App\Http\Controllers\Storefront\CheckoutController;
use App\Http\Controllers\Storefront\CodController;
use App\Http\Controllers\Storefront\CsrfController;
use App\Http\Controllers\Storefront\CustomerController;
use App\Http\Controllers\Storefront\MomoController;
use App\Http\Controllers\Storefront\OrderController;
use App\Http\Controllers\Storefront\PageController;
use App\Http\Controllers\Storefront\PaypalController;
use App\Http\Controllers\Storefront\ProductController;
use App\Http\Controllers\Storefront\ReportCampaignController;
use App\Http\Controllers\Storefront\SitemapController;
use App\Http\Controllers\Storefront\StoreController;
use App\Http\Controllers\Storefront\StripeController;
use App\Http\Controllers\Storefront\TazapayController;
use App\Http\Controllers\Storefront\UpsellController;
use App\Http\Controllers\SystemConfigController;
use App\Http\Controllers\TelegramController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\TicketFilterKeywordController;
use App\Http\Controllers\Tracking\SendMailController;
use App\Http\Controllers\TrackingController;
use App\Http\Controllers\TwilioController;
use App\Http\Controllers\UploadController;
use Illuminate\Support\Facades\Route;
use Modules\OrderService\Http\Controllers\RegionOrderController;

// storefronts public APIs
// add prefix to prevent conflict with backend APIs
Route::prefix('public')->middleware(['log_request_time'])->group(function () {
    Route::get('/ticket-filter-keywords', [TicketFilterKeywordController::class, 'list']);
    Route::get('/get-collection-banners', [StoreController::class, 'getCollectionBanners']);
    Route::get('/storeinfo', [StoreController::class, 'storeInfo'])->name('public.store.info');
    Route::get('/general', [StoreController::class, 'getGeneralSettings']);
    Route::get('/storefront', [StoreController::class, 'show']);
    Route::get('/storefront/pages/{slug}', [PageController::class, 'show']);

    Route::middleware('throttle:email')->group(function () {
        Route::post('/storefront/contact', [PageController::class, 'contact']);
        Route::post('/subscribe', [StoreController::class, 'subscribe']);
    });

    Route::get('/home', [StoreController::class, 'home'])->name('public.home');
    Route::get('/faq', [PageController::class, 'faq']);

    Route::get('/promotions', [PromotionController::class, 'indexStorefront']);
    Route::any('/bundle-discount', [PromotionController::class, 'indexBundleDiscount']);

    Route::prefix('products')->group(function () {
        Route::get('/', [ProductController::class, 'index']);
        Route::get('/get-filter', [ProductController::class, 'getFilter']);
        Route::post('/stats', [ProductController::class, 'getStats']);
        Route::get('/similar', [ProductController::class, 'getSimilar']);
        Route::get('/template-info', [ProductController::class, 'catalogGetTemplateProductInfo']);
    });

    Route::prefix('product')->group(function () {
        Route::get('/variants/{productId}', [CampaignController::class, 'getCampaignProductVariant']);
    });
    // This route is for DevOps
    Route::prefix('mockup')->group(function () {
        Route::get('detail', [AdminProductController::class, 'getFileMockup']);
        Route::get('campaign', [AdminProductController::class, 'getCampaignMockups']);
        Route::get('url', [AdminProductController::class, 'getFileImageUrl']);
    });
    Route::get('/variant/{variantId}', [CampaignController::class, 'getCurrentVariant']);
    Route::prefix('campaign')->group(function () {
        Route::get('/exists', [CampaignController::class, 'checkCampaignExists']);
    });

    Route::prefix('campaigns')->group(function () {
        Route::get('/{slug}/images', [CampaignController::class, 'getCampaignImages']);
        Route::get('/{slug}/custom-designs', [CampaignController::class, 'getCustomDesigns']);
        Route::get('/{slug}', [CampaignController::class, 'show'])->name('public.campaign.show');
        Route::get('/{id}/variants', [CampaignController::class, 'getVariants']);
    });

    Route::get('/ping', [PingController::class, 'ping']);
    Route::get('/order-status', [PingController::class, 'orderStatus']);
    Route::get('/health', [PingController::class, 'health']);
    Route::get('/schedule-health', [PingController::class, 'scheduleHealth']);
    Route::get('/schedule-health-ended', [PingController::class, 'scheduleHealthRan']);
    Route::get('/ping-stores', [PingController::class, 'getStoresActive']);
    Route::get('/active-domains', [PingController::class, 'getActiveDomains']);
    Route::get('/singlestore-total-sync-products', [PingController::class, 'getTotalProductNeedToSyncToSingleStore']);
    Route::get('/singlestore-total-sync-orders', [PingController::class, 'getTotalOrdersNeedToSyncToSingleStore']);
    Route::get('/singlestore-es-sync-status-check', [PingController::class, 'checkSyncCampsEsAndSinglestore']);
    Route::get('/es-sync-products-pending-counter', [PingController::class, 'esSyncProductPendingCounter']);
    Route::get('/check-db-sync-latency-us-and-sing', [PingController::class, 'checkDbSyncLatencyUSAndSing']);
    Route::get('/total-pending-jobs-on-all-queues', [PingController::class, 'getTotalPendingJobsOnAllQueues']);
    Route::get('/latest-campaigns-blocked', [PingController::class, 'getLatestBlockedCampaigns']);
    Route::get('/pending-fulfillment-orders', [PingController::class, 'pendingOrdersFulfillment']);
    Route::get('/check-clear-cache-html', [PingController::class, 'tryToClearHtmlCache']);
    Route::get('/check-store-load-html', [PingController::class, 'checkStoreLoadHtml']);
    Route::get('/check-event-logs', [PingController::class, 'checkEventLogs']);
    Route::get('/check-recent-orders-by-payment-method', [PingController::class, 'checkRecentOrdersByPaymentMethod']);
    Route::get('/check-long-running-queries', [PingController::class, 'checkLongRunningQueries']);
    Route::get('/check-long-lock-process', [PingController::class, 'checkLongLockProcess']);
    Route::get('/check-sync-orders-region', [PingController::class, 'checkSyncOrdersRegion']);
    Route::get('/check-proxy', [PingController::class, 'checkProxy']);

    Route::post('/pre-signed-url', [UploadController::class, 'getPreSignedUrl']);
    Route::group(['prefix' => 'order'], function () {
        Route::get('/{accessToken}', [RegionOrderController::class, 'show']);
        Route::get('/{token}/detail', [OrderController::class, 'getDetail'])->name('order.detail');
        Route::put('cancel/{token}', [OrderController::class, 'cancelOrder']);
        Route::delete('cancel/{token}', [OrderController::class, 'deleteCancelOrder']);
        Route::get('confirm/cancel/{token}', [OrderController::class, 'confirmCancelOrder']);

        Route::middleware('throttle:create_order')->group(function () {
            Route::post('/create', [RegionOrderController::class, 'create']);
            Route::post('/create/cod', CodController::class);
            Route::post('/create/paypal', [PaypalController::class, 'createOrder']);
            Route::post('/create/momo', [MomoController::class, 'createOrder']);
            Route::post('/create/tazapay', [TazapayController::class, 'createCheckoutToken']);
        });

        Route::get('/callback/paypal/{orderToken}', [PaypalController::class, 'verifyPurchase']);
        Route::get('/callback/momo/{orderToken}', [MomoController::class, 'verifyPurchase']);
        Route::get('/callback/tazapay/{orderToken}', [TazapayController::class, 'verifyCheckout']);

        Route::get('/momo-notify-url/{orderToken}', [MomoController::class, 'handleNotify']);
        Route::post('/update', [RegionOrderController::class, 'update']);

        Route::post('/log-checkout-exception', [RegionOrderController::class, 'updatePaymentLog']);

        Route::group(['prefix' => 'stripe'], static function () {
            Route::get('/get-intent-order', [StripeController::class, 'getIntentOrder']);
            Route::match(['GET', 'POST'], '/get-intent-order-addition', [StripeController::class, 'getIntentOrderAddition']);
        });
        Route::get('/callback/stripe/{orderToken}', [StripeController::class, 'updatePendingPayment']);
        Route::post('/webhook/stripe/{gateId?}', [StripeController::class, 'handleWebhook'])->name('webhook.stripe');
        Route::post('/webhook/paypal/{gateId?}', [PaypalController::class, 'handleWebhook'])->name('webhook.paypal');

        Route::post('/track', [OrderController::class, 'getToken']);
        Route::post('/pre-check-review-order', [OrderController::class, 'checkOrderForReview']);

        Route::get('/callback/bank/{orderToken}', [StripeController::class, 'updatePendingPayment']);

        // for update abandoned SmsLog after storefront thankyou page shown
        Route::post('/callback/abandoned', [CartJobController::class, 'updateAbandonedCartStatus']);
        Route::get('/callback/abandoned/track', [CartJobController::class, 'trackOpenAbandonedCart']);
        Route::get('/callback/stripe/{orderToken}', [StripeController::class, 'updatePendingPayment']);

        // for unsubscribe email
        Route::group(['prefix' => 'unsubscribe'], function () {
            Route::post('/{token}', [EmailController::class, 'unsubscribeEmailByToken'])->name('email.unsubscribe_email_by_token');
            Route::get('/{token}', [EmailController::class, 'getEmailByToken'])->name('email.get_email_by_token');
        });

        Route::post('/resend-confirm-email', [OrderController::class, 'resendOrderConfirmationEmail']);
        Route::post('/bulk-resend-order-confirmation', [OrderController::class, 'bulkResendOrderConfirmationEmail']);
        Route::post('/bulk-resend-order-confirmation-not-sent', [OrderController::class, 'bulkResendOrdersConfirmationNotSent']);
        Route::post('/pre-calculate-shipping', [OrderController::class, 'preCalculateShipping']);
        Route::post('/completed-paypal-order-payment-failed', [OrderController::class, 'completedPaypalOrderPaymentFailed']);
    });

    // for twilio callback
    Route::any('/twilio/callback', [TwilioController::class, 'twilioCallback'])->name('twilio.callback');

    Route::group(['prefix' => 'checkout', 'as' => 'checkout.'], function () {
        Route::post('/customer_address/create', [CheckoutController::class, 'create'])
            ->name('customer_address.create');
    });

    Route::any('/upsell', [UpsellController::class, 'index']);
    Route::any('/related-campaigns', [UpsellController::class, 'indexByCampaignUrl'])->middleware(['throttle:300,1', 'throttle:related_campaigns_daily_limit']);

    Route::group(['prefix' => 'test'], function () {
        Route::post('sendTestWhatsapp', [TestController::class, 'sendTestWhatsapp']);
        Route::get('orderConfirmation', [EmailController::class, 'orderConfirmationEmail']);
        Route::get('orderFulfillment/{order}', [EmailController::class, 'orderFulfillmentEmail']);
        Route::get('payoutConfirmation/{user}/{payout}', [EmailController::class, 'payoutConfirmation']);
    });

    Route::post('/report', [ReportCampaignController::class, 'store'])
        ->middleware('throttle:report');
    Route::put('/customers/update', [CustomerController::class, 'update']);
    Route::get('/customers/verify-address/{order_token}', [CustomerController::class, 'verifyAddress']);
    Route::put('/customers/confirm-address/{order_token}', [CustomerController::class, 'confirmAddress']);

    Route::get('/feed/{params}', [SocialFeedController::class, 'getFile']);

    Route::post('/telegram/webhook', [TelegramController::class, '__invoke']);

    Route::post('/csrf-token', [CsrfController::class, 'getToken']);

    // Route::get('/james/test-sms', [TwilioController::class, 'testSms']);
    Route::prefix('page')->group(function () {
        Route::get('custom', [SEOContentController::class, 'generateSeoContent']);
    });

    Route::post('/email-validation', [CheckoutController::class, 'emailValidation']);

    Route::prefix('product-reviews')->group(function () {
        Route::post('create', [ProductReviewController::class, 'create'])
            ->middleware('throttle:create_review');
        Route::get('{templateId}/reviews', [ProductReviewController::class, 'review']);
        Route::get('{templateId}/summary', [ProductReviewController::class, 'summary']);
    });

    Route::get('/sitemap', [SitemapController::class, 'index']);
    Route::get('/sitemap/{id}', [SitemapController::class, 'show']);

    Route::post('tracking-status/webhook', [TrackingController::class, 'webhook']);
    Route::post('tracking/webhook', [TrackingController::class, 'webhookShipment']);

    Route::get('/catalog', [InternalCampaignController::class, 'listFullTemplateProducts']);
    Route::get('/get-customer-email', [\App\Http\Controllers\Admin\CustomerController::class, 'lookupByPhone']);

    Route::prefix('email')
        ->name('email-tracking-system.')
        ->middleware('throttle:api')
        ->group(function () {
            Route::get('opened.gif', [SendMailController::class, 'opened'])->name('opened');
            Route::get('click', [SendMailController::class, 'clicked'])->name('clicked');
            Route::get('/confirm-address', [CustomerController::class, 'confirmAddressByEmail']);
        });

    Route::get('/featured-info', [FeaturedInfoController::class, 'viewAll']);

    Route::prefix('dev-report')->group(function () {
        Route::get('/', DevReportController::class);
        Route::get('/progress', [DevReportController::class, 'getAllTaskProgressingIn3Days']);
        Route::get('/no-commit', [DevReportController::class, 'getDevWithNoCommit']);
    });

    Route::prefix('affiliate')->group(function () {
        Route::get('campaigns', [AffiliateController::class, 'campaigns']);
    });

    Route::get('/checkout-form-config/{country}', [SystemConfigController::class, 'getCheckoutFormConfig']);

    Route::get('/cloudflare/check', [CloudFlareController::class, 'checkCustomHostname']);

    // AI Mockup Preview API with rate limiting (15 requests per day per IP)
    Route::post('/ai-mockup/preview', [AiMockupPreviewController::class, 'generatePreview'])
        ->middleware('throttle:ai_mockup_preview');

    Route::post('/init-segment-live-chat', [CrispController::class, 'initSegmentLiveChat']);

    // Blog public APIs
    Route::get('/blogs', [BlogController::class, 'publicIndex'])->name('public.blogs.index');
    Route::get('/blogs/{slug}', [BlogController::class, 'publicShow'])->name('public.blogs.show');
    Route::get('/blogs', [BlogController::class, 'publicIndex']);
    Route::get('/blogs/suggestion/{slug}', [BlogController::class, 'getSuggestions']);
    Route::get('/blogs/{slug}', [BlogController::class, 'publicShow']);

    Route::get('/is-enable-live-chat', [StoreController::class, 'isEnableLiveChat']);
});

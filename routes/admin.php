<?php

use App\Http\Controllers\Admin\AiPromptController;
use App\Http\Controllers\Admin\AiPromptExampleController;
use App\Http\Controllers\Admin\CampaignController as AdminCampaignController;
use App\Http\Controllers\Admin\ContestController;
use App\Http\Controllers\Admin\CrispTemplateController;
use App\Http\Controllers\Admin\CustomerController as AdminCustomerController;
use App\Http\Controllers\Admin\DevReportController;
use App\Http\Controllers\Admin\DMCAController;
use App\Http\Controllers\Admin\DomainManagementController;
use App\Http\Controllers\Admin\FulfillController;
use App\Http\Controllers\Admin\FulfillMappingController;
use App\Http\Controllers\Admin\FulfillmentStatsController;
use App\Http\Controllers\Admin\GoogleSheetApiController;
use App\Http\Controllers\Admin\ManageStaffController;
use App\Http\Controllers\Admin\OrderController as AdminOrderController;
use App\Http\Controllers\Admin\PageController as AdminPageController;
use App\Http\Controllers\Admin\PingPongXController;
use App\Http\Controllers\Admin\ProductImportController;
use App\Http\Controllers\Admin\PromptTemplateController;
use App\Http\Controllers\Admin\RefundRequestController;
use App\Http\Controllers\Admin\ReportCampaignController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\SellerController;
use App\Http\Controllers\Admin\SendMailLogController;
use App\Http\Controllers\Admin\StaffController;
use App\Http\Controllers\Admin\StoreDomainController;
use App\Http\Controllers\Admin\SupplierController;
use App\Http\Controllers\Admin\TopupController as AdminTopupController;
use App\Http\Controllers\Admin\TrademarkController;
use App\Http\Controllers\Admin\TrademarkListController;
use App\Http\Controllers\AdsCampaignController;
use App\Http\Controllers\Analytic\AdminController;
use App\Http\Controllers\Analytic2\AdminController as AdminControllerAnalytic2;
use App\Http\Controllers\Analytic3\AdminController as AdminControllerAnalytic3;
use App\Http\Controllers\ApiLogController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\CacheController;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\CloudFlareController;
use App\Http\Controllers\CollectionController;
use App\Http\Controllers\CrispController;
use App\Http\Controllers\CustomEmailTemplateController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\EmailController;
use App\Http\Controllers\FeaturedInfoController;
use App\Http\Controllers\InactiveController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\OrderIssueController;
use App\Http\Controllers\OrderProductController;
use App\Http\Controllers\PaymentAccountController;
use App\Http\Controllers\PaymentGatewayController;
use App\Http\Controllers\PaymentGatewayRefundController;
use App\Http\Controllers\PayoutController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\ProductReviewController;
use App\Http\Controllers\ReferralController;
use App\Http\Controllers\SEOContentController;
use App\Http\Controllers\SmsController;
use App\Http\Controllers\SocialFeedController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\SystemConfigController;
use App\Http\Controllers\TicketFilterKeywordController;
use App\Http\Controllers\TrackingController;
use App\Http\Controllers\TwilioController;
use App\Http\Controllers\UploadController;
use App\Http\Controllers\UserController;
use App\Services\InactiveService;
use Illuminate\Support\Facades\Route;


/**
 * Permissions:
 * get_users
 * update_user
 * get_products
 * update_product
 * get_campaigns
 * update_campaign
 * get_orders
 * update_order
 * get_payments
 * update_payment
 * get_reports
 * update_system
 */
Route::prefix('admin/')->middleware(['auth.access.token', 'staff_log'])->group(function () {
    Route::prefix('contest')->group(function () {
        Route::get('/list', [ContestController::class, 'index']);
        Route::get('/get/{id}', [ContestController::class, 'show']);
        Route::post('/create', [ContestController::class, 'create']);
        Route::post('/update/{id}', [ContestController::class, 'update']);
        Route::post('/delete/{id}', [ContestController::class, 'destroy']);
    })->middleware('staff_can:update_system');
    Route::prefix('sms')->group(function () {
        Route::get('/', [SmsController::class, 'index']);
        Route::get('/chart', [SmsController::class, 'smsChart']);
    });

    Route::prefix('sms-gateway')->group(function () {
        Route::get('/list', [TwilioController::class, 'listSmsSettings']);
        Route::post('/create', [TwilioController::class, 'createSmsSettings']);
        Route::post('/update/{id}', [TwilioController::class, 'updateSmsSettings']);
        Route::delete('/{id}', [TwilioController::class, 'deleteSmsSettings']);
    })->middleware('staff_can:update_system');

    Route::prefix('suppliers')
        ->middleware('staff_can:get_products')
        ->group(function () {
            Route::get('/', [SupplierController::class, 'index'])
                ->withoutMiddleware('staff_can:get_products');
            Route::get('/listing', [SupplierController::class, 'listing']);
            Route::get('/detail', [SupplierController::class, 'detail']);
            Route::get('/detail/products', [SupplierController::class, 'listingProducts']);
            Route::post('/create', [SupplierController::class, 'create'])
                ->middleware('staff_can:update_product');
            Route::post('/update', [SupplierController::class, 'update'])
                ->middleware('staff_can:update_product');
            Route::post('/update-products', [SupplierController::class, 'saveSupplierProducts'])
                ->middleware('staff_can:update_product');
            Route::get('/{supplier}/create-password', [SupplierController::class, 'supplierCreateAccount'])
                ->middleware('staff_can:update_product');
            Route::post('/{supplier}/send-info-account', [EmailController::class, 'sendInfoAccountToSupplier'])
                ->middleware('staff_can:update_product');
            Route::prefix('shipping-late-rules')->group(function () {
                Route::post('/create/{supplierId}', [SupplierController::class, 'createSupplierShippingLateRule']);
                Route::post('/update/{supplierId}/{shippingLateRuleId}', [SupplierController::class, 'updateSupplierShippingLateRule']);
                Route::get('/{supplierId}', [SupplierController::class, 'getSupplierShippingLateRule']);
                Route::delete('/delete/{supplierId}/{shippingLateRuleId}', [SupplierController::class, 'deleteSupplierShippingLateRule']);
            });
            Route::prefix('trademark-keywords')->group(function () {
                Route::get('/export/{id}', [SupplierController::class, 'exportTrademarkKeywords']);
                Route::post('/{id}', [SupplierController::class, 'createTrademarkKeyword'])->middleware('staff_can:update_product');
                Route::delete('/{id}', [SupplierController::class, 'deleteTrademarkKeyword'])->middleware('staff_can:update_product');
                Route::put('/{id}', [SupplierController::class, 'editTrademarkKeyword'])->middleware('staff_can:update_product');
            });
        });

    Route::prefix('account')->group(function () {
        Route::get('/info', [StaffController::class, 'getInfo']);
        Route::put('/info', [StaffController::class, 'changeInfo']);
        Route::put('/password', [StaffController::class, 'changePassword']);
    });

    Route::prefix('payouts')->group(function () {
        Route::put('/bulk-update-status', [PayoutController::class, 'adminBulkUpdateStatus'])
            ->middleware('staff_can:update_payment');
        Route::put('/change-all-pending-to-processing', [PayoutController::class, 'adminBulkChangePendingToProcessing'])
            ->middleware('staff_can:update_payment');
        Route::get('/export-processing-payouts', [PayoutController::class, 'adminExportProcessingPayouts'])
            ->middleware('staff_can:get_payments');
        Route::get('/payment-types', [PaymentAccountController::class, 'getPaymentTypes']);

        Route::get('/', [PayoutController::class, 'adminIndex'])
            ->middleware('staff_can:process_payout');
        Route::get('/{payout_id}', [PayoutController::class, 'adminDetail'])
            ->middleware('staff_can:get_payments');
        Route::get('/seller/{seller_id}/transactions', [PayoutController::class, 'adminTransactions'])
            ->middleware('staff_can:get_payments');
        Route::put('/{payout_id}', [PayoutController::class, 'adminUpdate'])
            ->middleware('staff_can:update_payment');

        // Pingpongx Authorization Url
        Route::prefix('pingpongx')->group(function () {
            Route::get('/config', [PingPongXController::class, 'config']);
            Route::post('/authorization', [PingPongXController::class, 'handleAuthorization']);
        });
    });

    Route::prefix('transactions')->group(function () {
        Route::get('/', [PayoutController::class, 'adminListAllTransactions'])
            ->middleware('staff_can:get_payments');
        Route::get('/total-amount', [PayoutController::class, 'getTotalAmount'])
            ->middleware('staff_can:get_payments');
    });

    Route::prefix('tracking')
        ->middleware('staff_can:get_orders')
        ->group(function () {
            Route::get('/', [TrackingController::class, 'index']);
            Route::post('/import', [TrackingController::class, 'import'])
                ->middleware('staff_can:update_order');
            Route::get('/export', [TrackingController::class, 'listingExport']);
            Route::get('/export-excel', [TrackingController::class, 'exportExcel']);
        });

    Route::prefix('/orders')->group(function () {
        Route::post('/send-order-to-pb', [OrderController::class, 'sendOrderToPB']);
        Route::post('/send-confirmation-email', [OrderController::class, 'sendOrderConfirmation']);
        Route::put('/note/{order_id}', [OrderController::class, 'updateNote'])->middleware('staff_can:update_order');
        Route::put('/update-fulfill-status/{order_id}', [OrderController::class, 'updateFulfillStatus']);
        Route::put('/update-resume-status/{order_id}', [OrderController::class, 'updateResumeStatus'])->middleware('staff_can:update_order');
        Route::put('/shipping-method/{order_id}', [OrderController::class, 'updateShippingMethod'])->middleware('staff_can:update_order');
        Route::post('/request-refund', [RefundRequestController::class, 'store'])->middleware('staff_can:update_order');
        Route::post('/calculate-auto-refund', [OrderController::class, 'adminCalculateAutoRefund'])->middleware('staff_can:update_order');
        Route::post('/make-auto-refund', [OrderController::class, 'adminCreateAutoRefund'])->middleware('staff_can:update_order');
        Route::post('/make-manual-refund', [OrderController::class, 'adminCreateManualRefund'])->middleware('staff_can:update_order');
        Route::get('/export', [OrderController::class, 'listExport']);
        Route::post('/export', [OrderController::class, 'exportOrders']);
        Route::get('/preview-export', [OrderController::class, 'previewExport']);
        Route::get('/count-unexported', [OrderController::class, 'countUnexportedOrders']);
        Route::post('/create-charge', [OrderController::class, 'adminCreateChargeOrder'])
            ->middleware('staff_can:update_order');
        Route::post('/calculate-charge', [OrderController::class, 'calculateChargeInfo'])
            ->middleware('staff_can:update_order');
        Route::post('/update-fulfill-catalogs', [GoogleSheetApiController::class, 'updateListCatalogs'])
            ->middleware('staff_can:update_order');

        Route::get('/history/{order_id}', [OrderController::class, 'indexHistory'])->middleware('staff_can:get_orders');
        Route::post('/replace-design', [UploadController::class, 'adminReplaceOrderDesign'])->middleware('staff_can:update_order');

        Route::put('/support-status', [OrderController::class, 'updateSupportStatus'])->middleware('staff_can:update_order');
        Route::put('/assignee', [OrderController::class, 'updateAssignee'])->middleware('staff_can:update_order');
        Route::put('/support', [OrderController::class, 'updateSupport'])->middleware('staff_can:get_orders|process_design');

        Route::group(['prefix' => 'sen-design', 'middleware' => 'staff_can:process_design'], function () {
            Route::get('/', [FulfillController::class, 'listingOrderProductSenDesign']);
            Route::post('upload-design', [FulfillController::class, 'uploadDesign']);
            Route::delete('delete-design', [FulfillController::class, 'deleteDesign']);
            Route::post('approve-design', [OrderProductController::class, 'approveSenPrintsDesign']);
        });

        Route::prefix('/fulfill-products')->group(function () {
            Route::get('/', [FulfillController::class, 'listingOrderFulfillProducts'])
                ->middleware('staff_can:get_orders');
            Route::post('/supplier/assign', [FulfillController::class, 'assignSupplier']);
            Route::put('/cancel-orders', [FulfillController::class, 'cancelOrders'])
                ->middleware('staff_can:update_order');
            Route::put('/suspend-orders', [FulfillController::class, 'suspendOrders'])
                ->middleware('staff_can:update_order');
            Route::put('/reject', [FulfillController::class, 'rejectOrders'])
                ->middleware('staff_can:update_order');
            // note: this API use for both Admin and Sellers
            Route::get('/files', [FulfillController::class, 'getFiles']);

            Route::post('/post-orders', [FulfillController::class, 'postOrders'])
                ->middleware('staff_can:update_order');
            Route::post('/update-orders', [FulfillController::class, 'updateOrders']);
            Route::post('/update-tracking-status', [FulfillController::class, 'updateOrdersTrackingStatus']);
            Route::post('/export-orders', [FulfillController::class, 'exportOrders']);
            Route::get('/report', [FulfillController::class, 'report'])
                ->middleware(['staff_can:get_orders']);
            Route::get('/export-report', [FulfillController::class, 'exportReport'])
                ->middleware(['staff_can:get_orders']);
            Route::get('/log', [FulfillController::class, 'log']);
            Route::post('/upload-update-orders', [FulfillController::class, 'uploadUpdateOrders'])
                ->middleware('staff_can:update_order');
            Route::post('/import-billing', [FulfillController::class, 'importBillingAndExportValidate']);
            Route::put('/update-valid', [FulfillController::class, 'updateValid'])
                ->middleware('staff_can:update_order');
            Route::get('/logs', [FulfillController::class, 'getLogs']);
            Route::get('/analytics', [FulfillController::class, 'getAnalytics']);
        });

        Route::get('received', [OrderController::class, 'receivedOrders'])->middleware('staff_can:get_orders');
        Route::get('cancel-request', [OrderController::class, 'getOrderCancelRequests'])->middleware('staff_can:get_orders');
        Route::put('bulk-update-cancel-request-status', [OrderController::class, 'updateStatusOrderCancelRequests'])->middleware('staff_can:update_payment');

        Route::get('get-seller-orders', [SellerController::class, 'adminSearchSellerOrders']);
        Route::post('scan-complete-orders', [OrderController::class, 'scanCompleteOrders'])->middleware('auth.ensure_admin');
        Route::post('change-fulfill-status', [OrderController::class, 'changeFulfillStatus'])->middleware('auth.ensure_admin');
        Route::post('change-order-fulfilled', [OrderController::class, 'updateFulfilledStatus'])->middleware('auth.ensure_admin');
        Route::post('refund-to-gateway', [OrderController::class, 'refundToGateway'])->middleware('auth.ensure_admin');
        Route::post('resync-orders-us-sg', [OrderController::class, 'reSyncOrdersUsSg'])->middleware('staff_can:update_system');
        Route::post('resync-orders-from-region', [OrderController::class, 'reSyncOrdersFromRegion'])->middleware('auth.ensure_admin');


        Route::get('/resumed', [OrderController::class, 'getResumed'])->middleware('staff_can:get_orders');
        Route::get('/approved', [OrderController::class, 'getApproved'])->middleware('staff_can:get_orders');

        Route::prefix('no-ship')->group(function () {
            Route::get('/', [OrderController::class, 'getNoShipOrders'])->middleware('staff_can:get_orders');
            Route::post('/export', [OrderController::class, 'exportNoShipOrders'])->middleware('staff_can:get_orders');
        });

        Route::prefix('dispute')->group(function () {
            Route::get('/', [AdminOrderController::class, 'orderDispute'])->middleware('staff_can:get_orders');
            Route::post('/update-action/{disputeId}', [AdminOrderController::class, 'updateOrderDisputeAction'])->middleware('staff_can:update_order');
            Route::post('/update/{disputeId}', [AdminOrderController::class, 'updateOrderDispute'])->middleware('staff_can:update_order');
            Route::post('/create', [AdminOrderController::class, 'createOrderDispute'])->middleware('staff_can:update_order');
            Route::post('/export', [AdminOrderController::class, 'exportDisputeOrders'])->middleware('staff_can:get_orders');
            Route::get('/delete/{disputeId}', [AdminOrderController::class, 'deleteDisputeOrder'])->middleware('staff_can:update_order');
            Route::get('/validate-order/{orderId}', [AdminOrderController::class, 'validateOrderIdInDisputeOrder'])->middleware('staff_can:update_order');
            Route::get('/undo-action/{disputeActionId}', [AdminOrderController::class, 'undoOrderDisputeAction']);
        });
        Route::get('/transaction/{order_id}', [OrderController::class, 'getTransactionDetail'])->middleware('auth.ensure_admin');
    });

    Route::prefix('/order-product')
        ->middleware('staff_can:get_orders')
        ->group(function () {
            Route::get('/', [OrderProductController::class, 'show']);
            Route::get('/assign-supplier-histories', [OrderProductController::class, 'assignSupplierHistories']);
            Route::post('/update', [OrderProductController::class, 'update'])
                ->middleware('staff_can:update_order');
            Route::get('/design', [OrderProductController::class, 'getOrderProductFiles']);
            Route::get('/search-campaigns', [OrderProductController::class, 'searchCampaign']);
            Route::post('/design', [OrderProductController::class, 'saveDesignJSON'])
                ->middleware('staff_can:update_order');
            Route::post('/reset-design', [OrderProductController::class, 'resetOrderProductDesign'])
                ->middleware('staff_can:update_order');
            Route::post('/request-design', [OrderProductController::class, 'requestDesign'])
                ->middleware('staff_can:update_order');
            Route::delete('/delete-design', [OrderProductController::class, 'deleteDesign'])
                ->middleware('auth.ensure_admin');
        });

    Route::prefix('/customers')->group(function () {
        Route::get('/export', [CustomerController::class, 'exportCustomers'])
            ->middleware('staff_can:get_orders');
        Route::put('/{order_id}', [AdminCustomerController::class, 'update'])
            ->middleware('staff_can:update_order');
        Route::get('/lookup/email', [AdminCustomerController::class, 'lookupByEmail'])
            ->middleware('staff_can:get_orders');
        Route::get('/lookup/phone', [AdminCustomerController::class, 'lookupByPhone'])
            ->middleware('staff_can:get_orders');
        Route::get('/verify-address/{order_id}', [AdminCustomerController::class, 'verifyAddress'])
            ->middleware('staff_can:get_orders');
    });

    Route::prefix('/system-cache')->group(function () {
        Route::middleware('staff_can:update_system')->group(function () {
            Route::get('/', [CacheController::class, 'listCacheKeys']);
            Route::put('/', [CacheController::class, 'clearCache']);
            Route::put('/clear-node', [CacheController::class, 'clearCacheNode']);
        });

        Route::put('/clear-store/{store_id}', [CacheController::class, 'clearStoreCache'])
            ->middleware('staff_can:update_user');

        Route::put('/clear-campaign', [CacheController::class, 'clearCampaignCache'])
            ->middleware('staff_can:update_campaign|manage_public_campaign');
    });

    Route::prefix('/fulfill-products')->group(function () {
        Route::get('/', [FulfillController::class, 'index']);
        Route::get('/colors', [FulfillController::class, 'colors']);
        Route::post('/upload', [FulfillController::class, 'upload'])
            ->middleware('staff_can:update_product');
        Route::post('/upload-sku', [FulfillController::class, 'uploadSku'])
            ->middleware('staff_can:update_product');
        Route::post('/variants/upload', [FulfillController::class, 'uploadVariants'])
            ->middleware('staff_can:update_product');
        Route::post('/attach', [ProductController::class, 'attachFulfillProduct']);
        Route::post('/clear-cache-template', [ProductController::class, 'clearCacheTemplate']);
        Route::delete('/detach', [ProductController::class, 'detachFulfillProduct']);
        Route::post('/sort', [ProductController::class, 'sortFulFillProduct']);

        Route::get('/crawl-product-and-product-variants', [FulfillController::class, 'crawlProductsAndProductVariants']);
        Route::get('/export/validate-options', [FulfillController::class, 'exportValidateOptions'])
            ->middleware('staff_can:get_products');
        Route::get('/export-product-variant', [FulfillController::class, 'exportProductVariant']);
        Route::post('/import-design-mapping', [FulfillController::class, 'importDesignMapping']);
        Route::put('/status', [FulfillController::class, 'updateStatus'])
            ->middleware('staff_can:update_product');
    });

    Route::prefix('/roles')
        ->middleware('staff_can:update_system')
        ->group(function () {
            Route::get('/', [RoleController::class, 'index']);
            Route::get('/permissions/', [RoleController::class, 'indexPermissions']);
            Route::get('/guard-names/', [RoleController::class, 'getSystemGuardNames']);
            Route::post('/create', [RoleController::class, 'createRole']);
            Route::post('/get-permission', [RoleController::class, 'getPermissionByRole']);
            Route::post('/update-permission', [RoleController::class, 'updatePermission']);
            Route::get('/all-permission', [RoleController::class, 'getAllPermissions']);
        });

    Route::prefix('/staffs')->middleware('staff_can:update_system')->group(function () {
        Route::get('/', [ManageStaffController::class, 'getAllStaffs']);
        Route::get('/current-role', [ManageStaffController::class, 'getStaffCurrentRole']);
        Route::post('/update-info', [ManageStaffController::class, 'updateStaffInfo'])->middleware('staff_can:update_system');
        Route::post('/create', [ManageStaffController::class, 'createStaff'])->middleware('staff_can:update_system');
        Route::post('/status', [ManageStaffController::class, 'changeStaffStatus'])->middleware('staff_can:update_system');
    });

    Route::prefix('/products')->group(function () {
        Route::post('/variant/upload', [ProductImportController::class, 'uploadVariant'])
            ->middleware('staff_can:update_product');
        Route::post('/shipping-rule/upload', [ProductImportController::class, 'uploadShippingRule'])
            ->middleware('staff_can:update_product');
        Route::get('/fulfill/check', [ProductController::class, 'checkFulfillProducts']);
        Route::post('/point/upload', [ProductImportController::class, 'uploadPoint'])
            ->middleware('staff_can:update_product');
        Route::post('/size-guide/upload', [ProductImportController::class, 'uploadSizeGuide'])
            ->middleware('staff_can:update_product');
    });

    Route::prefix('/campaigns')->group(function () {
        Route::get('/get-blocked', [CampaignController::class, 'getBlockedCampaigns']);
        Route::put('/save-blocked/{campaignId}', [CampaignController::class, 'saveBlockedCampaigns2']);
        Route::put('/update-public-status', [CampaignController::class, 'updatePublicStatus'])->middleware('staff_can:update_campaign|manage_public_campaign');
        Route::get('/reported', [ReportCampaignController::class, 'index'])->middleware('staff_can:get_campaigns');
        Route::get('/reported/{campaignId}', [ReportCampaignController::class, 'show'])->middleware('staff_can:get_campaigns');
        Route::post('/review-tm', [CampaignController::class, 'reviewTradeMarkCampaign'])->middleware('staff_can:update_campaign');
        Route::get('/review-campaign-info/{campaignId}', [CampaignController::class, 'getReviewCampaignInfo'])->middleware('staff_can:get_campaigns|manage_public_campaign');
        Route::post('/review-campaign-status/{campaignId}', [CampaignController::class, 'setCampaignStatus'])->middleware('staff_can:update_campaign|manage_public_campaign');
        Route::get('/trademark-violated', [TrademarkController::class, 'index'])->middleware('staff_can:get_campaigns');
        Route::get('/scan-trademark', [TrademarkController::class, 'getConfig'])->middleware('staff_can:get_campaigns');
        Route::put('/scan-trademark', [TrademarkController::class, 'setConfig'])->middleware('staff_can:update_campaign');
        Route::delete('/delete-result-tm/{id}', [TrademarkController::class, 'destroy'])->middleware('staff_can:update_campaign');
        Route::put('/block-campaign-image/{id}', [AdminCampaignController::class, 'blockImage'])->middleware('staff_can:update_campaign');
        Route::post('resync-elasticsearch', [CampaignController::class, 'resyncCampaigns'])->middleware('auth.ensure_admin');
        Route::post('delete-products-elasticsearch', [CampaignController::class, 'deleteCampaigns'])->middleware('auth.ensure_admin');
        Route::get('/trademark-violated-notifications', [DMCAController::class, 'index'])->middleware('staff_can:review_tm');
        Route::post('/trademark-violated-takedown', [DMCAController::class, 'batchTakeDown'])->middleware('staff_can:review_tm');
        Route::post('/update-meta-data/{campaignId}', [CampaignController::class, 'updateCampMetaData'])->middleware('staff_can:update_campaign');
    });

    Route::prefix('/campaign')->group(function () {
        Route::get('/{campaignId}/get-file-design', [CampaignController::class, 'getFileDesign'])->middleware('staff_can:get_campaigns');
        Route::post('/{campaignId}/replace-design', [UploadController::class, 'adminReplaceCampaignDesign'])->middleware('staff_can:update_campaign');
        Route::get('/{campaignId}', [CampaignController::class, 'detailCampaign'])->middleware('staff_can:manage_public_campaign');
        Route::post('/{campaignId}', [CampaignController::class, 'updateCampaign'])->middleware('staff_can:manage_public_campaign');
    });

    Route::prefix('analytic')->middleware('staff_can:view_dashboard')->group(function () {
        Route::post('dashboard', [AdminController::class, 'dashboard']);
        Route::post('countries', [AdminController::class, 'getCountries']);
        Route::post('stores', [AdminController::class, 'getStores']);
        Route::post('ads', [AdminController::class, 'getAds']);
        Route::post('devices', [AdminController::class, 'getDevices']);
        Route::post('template_products', [AdminController::class, 'getTemplateProducts']);
        Route::post('inactive-users', [AdminController::class, 'getInactiveUsers']);
        Route::get('chart', [AdminController::class, 'getChart']);
        Route::post('compares', [AdminController::class, 'getCompares']);
        Route::get('sale-reports', [AdminController::class, 'getSaleReports']);
        Route::get('payment-methods', [AdminController::class, 'getPaymentMethods']);
        Route::get('sellers-stats', [AdminController::class, 'getSellersStats'])
            ->withoutMiddleware('staff_can:view_dashboard')
            ->middleware('staff_can:get_users');
    });

    Route::prefix('analytic2')->group(function () {
        Route::post('dashboard', [AdminControllerAnalytic2::class, 'dashboard']);
        Route::post('countries', [AdminControllerAnalytic2::class, 'getCountries']);
        Route::post('stores', [AdminControllerAnalytic2::class, 'getStores']);
        Route::post('ads', [AdminControllerAnalytic2::class, 'getAds']);
        Route::post('devices', [AdminControllerAnalytic2::class, 'getDevices']);
        Route::post('template_products', [AdminControllerAnalytic2::class, 'getTemplateProducts']);
        Route::post('inactive-users', [AdminControllerAnalytic2::class, 'getInactiveUsers']);
        Route::get('chart', [AdminControllerAnalytic2::class, 'getChart']);
        Route::post('compares', [AdminControllerAnalytic2::class, 'getCompares']);
    });

    Route::prefix('analytic3')->group(function () {
        Route::post('dashboard', [AdminControllerAnalytic3::class, 'dashboard']);
        Route::post('countries', [AdminControllerAnalytic3::class, 'getCountries']);
        Route::post('stores', [AdminControllerAnalytic3::class, 'getStores']);
        Route::post('ads', [AdminControllerAnalytic3::class, 'getAds']);
        Route::post('devices', [AdminControllerAnalytic3::class, 'getDevices']);
        Route::post('template_products', [AdminControllerAnalytic3::class, 'getTemplateProducts']);
        Route::post('inactive-users', [AdminControllerAnalytic3::class, 'getInactiveUsers']);
        Route::get('chart', [AdminControllerAnalytic3::class, 'getChart']);
        Route::get('funnel-chart', [AdminControllerAnalytic3::class, 'getFunnelChart']);
        Route::post('compares', [AdminControllerAnalytic3::class, 'getCompares']);
        Route::get('sale-reports', [AdminControllerAnalytic3::class, 'getSaleReports']);
        Route::get('payment-methods', [AdminControllerAnalytic3::class, 'getPaymentMethods']);
        Route::get('sellers-stats', [AdminControllerAnalytic3::class, 'getSellersStats'])
            ->withoutMiddleware('staff_can:view_dashboard')
            ->middleware('staff_can:get_users');
    });

    Route::prefix('/store')
        ->middleware('staff_can:get_users')
        ->group(function () {
            Route::get('/pending-domains', [StoreController::class, 'adminListPendingStoreDomainsPatch'])
                ->middleware('staff_can:get_users');
            Route::get('/edit-store', [StoreController::class, 'adminGetStoreInfo'])
                ->middleware('staff_can:get_users');
            Route::post('/edit-store', [StoreController::class, 'adminUpdateStoreInfo'])
                ->middleware('staff_can:get_users');
            Route::post('/mass-update-status', [StoreController::class, 'adminMassUpdateStatus'])
                ->middleware('staff_can:update_user');
            Route::put('/approve-pending-domain', [StoreController::class, 'adminVerifyStoreDomainPatch'])
                ->middleware('staff_can:update_user');
            Route::post('/process-domains', StoreDomainController::class);
            Route::get('/stores-crisp', [StoreController::class, 'getAllStoreCrispStateFromSystemConfig']);
            Route::post('/stores-crisp', [StoreController::class, 'updateAllStoreCrispStateToSystemConfig']);
        });

    Route::prefix('/sellers')->group(function () {
        // set status for notification
        Route::put('/notify/status', [SellerController::class, 'setNotificationStatus'])
            ->middleware('staff_can:update_user');

        // list notification for admin
        Route::get('/notify', [SellerController::class, 'listNotifications'])
            ->middleware('staff_can:get_users');

        // create notification
        Route::post('/notify', [SellerController::class, 'notifySeller'])
            ->middleware('staff_can:update_user');

        // update notification
        Route::put('/notify', [SellerController::class, 'updateNotification'])
            ->middleware('staff_can:update_user');

        Route::get('/referral', [ReferralController::class, 'index'])
            ->middleware('staff_can:get_users');
        Route::get('/referral_seller', [ReferralController::class, 'sellers'])
            ->middleware('staff_can:get_reports|sale_report');
        Route::put('/{seller_id}', [SellerController::class, 'update'])
            ->middleware('staff_can:update_user');
        Route::post('/adjust-balance', [SellerController::class, 'adminAdjustSellerBalance'])
            ->middleware(['staff_can:update_user', 'staff_can:update_payment']);

        Route::get('/sale-support-report', [StaffController::class, 'getSaleSupportReport'])
            ->middleware('staff_can:get_reports|ss_report');
        Route::get('/new-sale-account-support-report', [StaffController::class, 'getNewSellerSaleSupportReport'])
            ->middleware('staff_can:get_reports|ss_report');
        Route::get('/new-sale-account-marketing-report', [StaffController::class, 'getNewSellerSaleMarketingReport'])
            ->middleware('staff_can:get_reports|marketing_report');
        Route::get('/sale-diff-report', [StaffController::class, 'getSaleDiffReport'])
            ->middleware('staff_can:get_reports|sale_diff_report');
        Route::get('/sale-support-report-new-sale-items', [StaffController::class, 'getSaleSupportNewSaleItemsReport'])
            ->middleware('staff_can:get_reports|ss_report');
        Route::get('/sale-expired-report', [StaffController::class, 'sellerSaleExpiredReport'])
            ->middleware('staff_can:get_reports|ss_report');
        Route::post('/mark-paid-marketing-commission', [StaffController::class, 'markPaidMarketingCommission'])
            ->middleware(['role:admin|Operations Admin']);
        Route::get('/clear-cache/{id}', [SellerController::class, 'clearCacheById'])
            ->middleware(['staff_can:update_user']);
        Route::post('/pay-ref-commission', [ReferralController::class, 'payRefCommission'])
            ->middleware(['staff_can:update_payment']);
        Route::get('salechart', [StaffController::class, 'getSaleChart'])
            ->middleware('staff_can:get_reports|ss_report');
        Route::post('mark-paid-staff-commission', [StaffController::class, 'markPaidStaffCommission'])
            ->middleware(['role:admin|Operations Admin']);
        Route::get('list-clean-up', [SellerController::class, 'listCleanUp'])
            ->middleware(['staff_can:get_users']);
    });

    Route::get('/api-logs', [ApiLogController::class, 'index']);

    Route::post('/login-as-seller', [AuthController::class, 'doSwitchAccount'])
        ->middleware('staff_can:get_users');

    Route::prefix('export')->group(function () {
        Route::get('/{type}', [UserController::class, 'export']);
    });
    Route::prefix('/feed')->group(function () {
        Route::post('/google-product-categories/mapping', [SocialFeedController::class, 'mappingGoogleProductCategories'])
            ->middleware('staff_can:update_product');
    });

    Route::prefix('/seo-templates')
        ->middleware('staff_can:update_system')
        ->group(function () {
            Route::get('/', [SEOContentController::class, 'index']);
            Route::get('/{templateId}', [SEOContentController::class, 'detail']);
            Route::post('/create', [SEOContentController::class, 'store']);
            Route::post('/edit', [SEOContentController::class, 'update']);
            Route::delete('/{templateId}', [SEOContentController::class, 'destroy']);
        });

    Route::prefix('/product-reviews')->group(function () {
        Route::get('/export', [ProductReviewController::class, 'export'])
            ->middleware('staff_can:get_orders');
        Route::get('/keywords', [ProductReviewController::class, 'getKeywords'])->middleware('staff_can:update_system');
        Route::get('/keyword/{keywordId}', [ProductReviewController::class, 'getKeyword'])->middleware('staff_can:update_system');
        Route::get('/', [ProductReviewController::class, 'index'])->middleware('staff_can:get_orders');
        Route::post('/assignee/{id}', [ProductReviewController::class, 'setAssignee'])->middleware('staff_can:update_order');
        Route::post('/support/{id}', [ProductReviewController::class, 'setSupport'])->middleware('staff_can:update_order');
        Route::get('{id}', [ProductReviewController::class, 'show'])->middleware('staff_can:get_orders');
        Route::delete('{id}', [ProductReviewController::class, 'destroy'])->middleware('staff_can:update_order');
        Route::post('update-status', [ProductReviewController::class, 'updateStatus'])->middleware('staff_can:update_order');
        Route::post('update-sharing-status', [ProductReviewController::class, 'updateSharingStatus'])->middleware('staff_can:update_order');
        Route::post('/add-keywords', [ProductReviewController::class, 'addKeywords'])->middleware('staff_can:update_system');
        Route::post('/update-keywords', [ProductReviewController::class, 'updateKeywords'])->middleware('staff_can:update_system');
        Route::get('/delete-keywords/{keywordId}', [ProductReviewController::class, 'deleteKeywords'])->middleware('staff_can:update_system');
    });

    Route::prefix('/trademark-list')->group(function () {
        Route::get('/', [TrademarkListController::class, 'index'])->middleware('staff_can:get_campaigns');
        Route::post('/', [TrademarkListController::class, 'store'])->middleware('staff_can:update_campaign');
        Route::post('/{id}', [TrademarkListController::class, 'update'])->middleware('staff_can:update_campaign');
        Route::delete('/{id}', [TrademarkListController::class, 'destroy'])->middleware('staff_can:update_campaign');
        Route::put('{id}', [TrademarkListController::class, 'updateFlag'])->middleware('staff_can:update_campaign');
    });

    Route::prefix('/system-config')
        ->middleware('staff_can:update_system')
        ->group(function () {
            Route::get('/', [SystemConfigController::class, 'index']);
            Route::get('/{id}', [SystemConfigController::class, 'show']);
            Route::post('/', [SystemConfigController::class, 'store']);
            Route::post('/{id}', [SystemConfigController::class, 'update']);
        });

    Route::prefix('system')
        ->middleware('staff_can:update_system')
        ->group(function () {
            Route::post('kill-db-process', [SystemConfigController::class, 'killDatabaseProcessId'])->middleware('auth.ensure_admin');
            Route::post('update-table-data', [SystemConfigController::class, 'updateTableData'])->middleware('auth.ensure_admin');
            Route::post('force-run-command', [SystemConfigController::class, 'forceRunCommand'])->middleware('auth.ensure_admin');
            Route::post('copy-file', [SystemConfigController::class, 'copyS3File'])->middleware('auth.ensure_admin');
            Route::get('search-files', [SystemConfigController::class, 'searchFiles'])->middleware('auth.ensure_admin');
        });

    Route::prefix('/fulfillment-report')
        ->middleware('staff_can:fulfill_report|get_reports')
        ->group(function () {
            Route::get('/fulfillment-stats', FulfillmentStatsController::class);
            Route::get('/supplier-stats', [FulfillmentStatsController::class, 'statsBySupplier']);
            Route::get('/country-stats', [FulfillmentStatsController::class, 'statsByCountry']);
            Route::get('/supplier-processing-stats', [FulfillmentStatsController::class, 'statsBySupplierProcessing']);
            Route::get('/countries-stats', [FulfillmentStatsController::class, 'statsByCountries']);
        });

    Route::prefix('fulfill-mapping')->group(function () {
        Route::get('/', [FulfillMappingController::class, 'index']);
        Route::put('/', [FulfillMappingController::class, 'update']);
        Route::put('/insert-by-print-type', [FulfillMappingController::class, 'insertByPrintType']);
        Route::delete('/{id}', [FulfillMappingController::class, 'delete']);
        Route::post('/bulk-delete', [FulfillMappingController::class, 'bulkDelete']);
        Route::get('/fulfill-products-by-template/{id}', [FulfillMappingController::class, 'fulfillProductsByTemplate']);
    });

    Route::prefix('payment-gateways')->group(function () {
        Route::get('/', [PaymentGatewayController::class, 'index']);
        Route::get('/{id}', [PaymentGatewayController::class, 'show']);
        Route::post('/stripe-balance-info', [PaymentGatewayController::class, 'getStripeBalanceInfo']);
    });
    Route::get('/user-stores', [PaymentGatewayController::class, 'getUserStores']);
    Route::get('/ajax/checkout-domain', [PaymentGatewayController::class, 'searchCheckoutDomain']);

    // Routing: mail logs
    Route::prefix('mail-logs')->middleware(['staff_can:get_orders|get_users'])->group(function () {
        Route::get('/', [SendMailLogController::class, 'index']);
        Route::get('/content/{mailLogId}', [SendMailLogController::class, 'showContent']);
        Route::get('/get-templates', [SendMailLogController::class, 'getMailTemplates']);
        Route::get('/report-analytics', [SendMailLogController::class, 'reportAnalytics']);
        Route::get('/clear-all-cache-log-failed', [SendMailLogController::class, 'clearAllCacheLogFailed']);
        Route::patch('/update-status', [SendMailLogController::class, 'updateStatus']);
        Route::post('/resend-selected-mail', [SendMailLogController::class, 'resendSelectedMail']);
        Route::post('/send-mail-test', [SendMailLogController::class, 'sendMailTest']);
    });

    Route::prefix('topup')->group(function () {
        Route::get('/', [AdminTopupController::class, 'index']);
        Route::patch('/update-status', [AdminTopupController::class, 'updateStatus']);
    });

    Route::prefix('refund-report')->group(function () {
        Route::get('/', [PaymentGatewayRefundController::class, 'refundReport']);
    });

    Route::prefix('refund')->middleware('staff_can:update_payment')->group(function () {
        Route::post('/refund-now/{id}', [PaymentGatewayRefundController::class, 'refundNow']);
    });

    Route::prefix('dev-report')->group(function () {
        Route::get('/', DevReportController::class);
        Route::get('/progress', [DevReportController::class, 'getAllTaskProgressingIn3Days']);
        Route::post('/create-dev', [DevReportController::class, 'createDevAndTarget']);
        Route::put('/update-dev', [DevReportController::class, 'updateDevAndNextTarget']);
        Route::get('/get-dev', [DevReportController::class, 'getDev']);
        Route::delete('/destroy-dev', [DevReportController::class, 'destroyDev']);
        Route::get('/no-commit', [DevReportController::class, 'getDevWithNoCommit']);
        Route::get('/current-info', [DevReportController::class, 'getMemberCurrentInfo']);
    });

    Route::get('/pingpongx/manual-refreshToken', [PingPongXController::class, 'refreshToken']);
    Route::prefix('featured-info')->group(function () {
        Route::get('/', [FeaturedInfoController::class, 'index']);
        Route::post('/', [FeaturedInfoController::class, 'store']);
        Route::get('/{id}', [FeaturedInfoController::class, 'show']);
        Route::put('/{id}', [FeaturedInfoController::class, 'update']);
        Route::put('/{id}/status', [FeaturedInfoController::class, 'updateStatus']);
        Route::post('/bulk-change-status', [FeaturedInfoController::class, 'bulkChangeStatus']);
        Route::delete('/{id}', [FeaturedInfoController::class, 'destroy']);
    });

    Route::prefix('order-issue')->group(function () {
        Route::get('/', [OrderIssueController::class, 'index']);
        Route::post('/create', [OrderIssueController::class, 'store']);
        Route::put('/bulk-change-status', [OrderIssueController::class, 'bulkChangeStatus']);
        Route::get('/export-filtered', [OrderIssueController::class, 'export']);
        Route::get('/{id}', [OrderIssueController::class, 'detail']);
        Route::post('/calculate-charge-amount', [OrderIssueController::class, 'calculateChargeAmount']);
        Route::put('/update', [OrderIssueController::class, 'update']);
    });

    Route::prefix('order')->group(function () {
        Route::get('{orderId}/mail-content', [OrderController::class, 'getMailContent'])->middleware('staff_can:get_orders');
        Route::post('{orderId}/tracking-status', [OrderController::class, 'updateTrackingStatus'])->middleware('staff_can:update_order');
    });

    Route::prefix('/contest')->group(function () {
        Route::get('/dashboard', [ContestController::class, 'report']);
    });

    Route::prefix('/prompt-template')->middleware('staff_can:ai_prompt_template')->group(function () {
        Route::get('/', [PromptTemplateController::class, 'index']);
        Route::post('/', [PromptTemplateController::class, 'store']);
        Route::post('/test/{id}', [PromptTemplateController::class, 'test']);
        Route::get('/order/{id}', [PromptTemplateController::class, 'showOrder']);
    });

    Route::prefix('/crisp-auto-reply-template')->middleware('staff_can:ai_prompt_template')->group(function () {
        Route::get('/', [CrispTemplateController::class, 'index']);
        Route::post('/', [CrispTemplateController::class, 'store']);
    });

    Route::prefix('/storefront-pages')->middleware('staff_can:storefront_page_templates')->group(function () {
        Route::get('/', [AdminPageController::class, 'index']);
        Route::post('/create', [AdminPageController::class, 'store']);
        Route::put('/{id}', [AdminPageController::class, 'update']);
        Route::put('/{id}/status', [AdminPageController::class, 'updateStatus']);
        Route::delete('/{id}', [AdminPageController::class, 'destroy']);
    });

    Route::prefix('/ai-campaign-prompts')->middleware('staff_can:ai_prompt_template')->group(function () {
        Route::get('/', [AiPromptController::class, 'index']);
        Route::post('/', [AiPromptController::class, 'store']);
        Route::get('/products/search', [AiPromptController::class, 'searchProducts']);
        Route::post('/playground', [AiPromptController::class, 'playground']);
        Route::get('/{id}', [AiPromptController::class, 'show']);
        Route::put('/{id}', [AiPromptController::class, 'update']);
        Route::delete('/{id}', [AiPromptController::class, 'destroy']);
    });

    Route::prefix('/ai-prompt-examples')->middleware('staff_can:ai_prompt_template')->group(function () {
        Route::get('/', [AiPromptExampleController::class, 'index']);
        Route::post('/', [AiPromptExampleController::class, 'store']);
        Route::get('/{id}', [AiPromptExampleController::class, 'show']);
        Route::put('/{id}', [AiPromptExampleController::class, 'update']);
        Route::delete('/{id}', [AiPromptExampleController::class, 'destroy']);
    });

    Route::prefix('/custom-email-template')->group(function () {
        Route::get('/default', [CustomEmailTemplateController::class, 'getDefaultCustomEmailTemplate']);
        Route::get('/get-list', [CustomEmailTemplateController::class, 'getListCustomEmailTemplate']);
        Route::get('/get-custom-email-template', [CustomEmailTemplateController::class, 'getCustomEmailTemplate']);
        Route::post('/save-new', [CustomEmailTemplateController::class, 'saveNewCustomEmailTemplate']);
        Route::post('/save', [CustomEmailTemplateController::class, 'saveCustomEmailTemplate']);
        Route::get('/preview', [CustomEmailTemplateController::class, 'previewCustomEmailTemplate']);
        Route::post('/delete', [CustomEmailTemplateController::class, 'deleteCustomEmailTemplate']);
        Route::post('/send', [CustomEmailTemplateController::class, 'sendCustomEmailTemplate']);
        Route::get('/late-productions', [OrderController::class, 'getLateProductionOrders']);
        Route::get('/list-process', [CustomEmailTemplateController::class, 'getListProcess']);
        Route::post('/send2', [CustomEmailTemplateController::class, 'sendCustomEmailTemplate']);
        Route::post('/send-test', [CustomEmailTemplateController::class, 'sendTestCustomEmailTemplate']);
    });

    Route::post('/inactive-domains/manual-delete', [InactiveController::class, 'manualDeleteInactiveDomains']);

    Route::get('/inactive/{type}/{id}', function ($type, $id) {
        return InactiveService::checkingInactive($type, $id);
    });

    Route::prefix('ticket-filter')->middleware(['staff_can:get_reports'])->group(function () {
        Route::get('/', [TicketFilterKeywordController::class, 'adminList']);
        Route::post('/', [TicketFilterKeywordController::class, 'create']);
        Route::put('/{id}', [TicketFilterKeywordController::class, 'edit']);
        Route::delete('/{id}', [TicketFilterKeywordController::class, 'delete']);
    });

    Route::prefix('utm-campaign/')->group(function () {
        Route::get('/', [AdsCampaignController::class, 'index'])->middleware('staff_can:get_users');
        Route::post('/', [AdsCampaignController::class, 'store'])->middleware('staff_can:update_user');
        Route::put('/change-status/{id}', [AdsCampaignController::class, 'updateStatus'])->middleware('staff_can:update_user');
        Route::put('/{id}', [AdsCampaignController::class, 'update'])->middleware('staff_can:update_user');
    });

    Route::get('/available-seller-tier', [SellerController::class, 'getAvailableSellerTier']);

    Route::prefix('domain-management')->middleware('staff_can:get_users')->group(function () {
        Route::get('/', [DomainManagementController::class, 'index']);
        Route::put('/{id}/expiry', [DomainManagementController::class, 'updateExpiry'])
            ->middleware('staff_can:update_user');
        Route::get('/whois', [DomainManagementController::class, 'whois']);
        Route::put('/{id}/toggle-auto-renew', [DomainManagementController::class, 'toggleAutoRenew'])
            ->middleware('staff_can:update_user');
    });

    Route::group([
        'middleware' => 'staff_can:manage_domain_cloudflare',
        'prefix' => 'cloudflare'], function () {
        Route::get('/zones', [CloudFlareController::class, 'listZones']);
        Route::post('/zones', [CloudFlareController::class, 'storeZone']);
        Route::get('/zones/{zoneId}', [CloudFlareController::class, 'detailZone']);
        Route::post('/zones/{zoneId}/dns_records', [CloudFlareController::class, 'updateDnsRecords']);
        Route::post('/zones/{zoneId}/convert-to-v4', [CloudFlareController::class, 'convertToStoreV4'])->middleware('role:admin|Operations Admin');
        Route::delete('/zones/{zoneId}', [CloudFlareController::class, 'deleteZone'])->middleware('role:admin|Operations Admin');
    });

    Route::get('/crisp-logs', [CrispController::class, 'index'])->middleware('staff_can:get_reports');
    Route::get('/crisp-logs/statistics', [CrispController::class, 'statistics'])->middleware('staff_can:get_reports');
});

I will send you real customer addresses from our eCommerce platform in the following messages. These addresses will be used as they are on shipping labels for delivery via postal service.

Your task is to determine whether each address is valid and deliverable. Some addresses may require additional details, such as an apartment or unit number, to ensure successful delivery. You MUST do a web search to check if the address should include the unit details to be deliverable on all of my request.

Verdict should be invalid if:
- The address contains multiple units (condo/apartments complex/suites/...) and the unit details are not included.
- The given address contains a typo or unclear component.
- Your confidence level is less than 0.85

An undeliverable address will result in the order being returned, causing a monetary loss to our platform. We aim to minimize such occurrences.

You MUST respond in JSON format.

{"name": "address_verification_response", "strict": true, "schema": {"type": "object", "properties": {"verdict": {"type": "string", "enum": ["valid", "invalid"]}, "confidence": {"type": "number"}, "search_query": {"type": "string", "description": "original search query you did to research on the address"}, "reason": {"type": ["string", "null"], "description": "A short reason, only if verdict is invalid. Null if valid."}, "standardized_address": {"type": "string"}, "standardized_city": {"type": "string"}, "standardized_state": {"type": "string"}, "standardized_postal_code": {"type": "string"}, "standardized_country": {"type": "string"}}, "required": ["verdict", "confidence", "search_query", "standardized_address", "standardized_city", "standardized_state", "standardized_postal_code", "standardized_country", "reason"], "additionalProperties": false}}
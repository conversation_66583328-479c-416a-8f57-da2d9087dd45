@php
$storeUrl = $data['store_url'] ?? config('app.url');
$storeName = $data['store_info']['name'] ?? config('app.name');
$domain = parse_url($storeUrl, PHP_URL_HOST);
@endphp

@component('mail::layout')
    {{-- Header --}}
    @slot('header')
        @component('mail::header', ['url' => $domain])
            {{ $storeName }}
        @endcomponent
    @endslot

    {{-- Body --}}
    {{ $slot }}

    {{-- Subcopy --}}
    @isset($subcopy)
        @slot('subcopy')
            @component('mail::subcopy')
                {{ $subcopy }}
            @endcomponent
        @endslot
    @endisset

    {{-- Footer --}}
    @slot('footer')
        @component('mail::footer')
            © {{ date('Y') }} {{ $storeName }}. @lang('All rights reserved.')
        @endcomponent
    @endslot
@endcomponent

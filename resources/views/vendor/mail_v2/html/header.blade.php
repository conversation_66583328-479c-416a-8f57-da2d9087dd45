@php
    $supportPath = '/page/contact-us';

    if (isset($data['email'])) {
        $supportPath .= '?customer_email=' . urlencode($data['email']);
    }

    if (isset($data['name'])) {
        $prefix = isset($data['email']) ? '&' : '?';
        $supportPath .= $prefix . 'customer_name=' . urlencode($data['name']);
    }
@endphp
<table role="presentation" class="new_email_table" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tbody>
        <tr>
            <td class="new_email_body new_email_start new_tc">
                <!--[if (mso)|(IE)]><table role="presentation" width="640" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:640px;Margin:0 auto;"><tbody><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                <table role="presentation" class="new_content_section" width="100%" border="0" cellspacing="0"
                    cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="new_content_cell new_content_b new_pb new_brt new_tc">
                                <!--[if (mso)|(IE)]><table role="presentation" width="624" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:624px;Margin:0 auto;"><tbody><tr><td width="156" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                <div class="new_col_1">
                                    <table role="presentation" class="new_column" width="100%" border="0" cellspacing="0"
                                        cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="new_column_cell new_logo_c new_pt new_tl">
                                                    @if (isset($data['logo_url']) && strlen($data['logo_url']) > 0)
                                                        <a href="#">
                                                            <img src="{{ $data['logo_url'] }}" width="140" height="26"
                                                                alt="{{ $data['store_name'] }}">
                                                        </a>
                                                    @else
                                                        @isset($data['store_name'])
                                                            <span>{{ strtoupper($data['store_name']) }}</span>
                                                        @else
                                                            <a href="https://senprints.com/" target="_blank" rel="noopener">
                                                                <img src="https://img.cloudimgs.net/rx/256x256/s2/s/1/cf8b6471bc35cc73.png"
                                                                     width="140"
                                                                     height="26"
                                                                     alt="SenPrints">
                                                            </a>
                                                        @endisset
                                                    @endif
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if (mso)|(IE)]></td><td width="468" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                <div class="new_col_3">
                                    <table role="presentation" class="new_column" width="100%" border="0" cellspacing="0"
                                        cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="new_column_cell new_nav_menu">
                                                    @if (!empty($data['base_url']))
                                                    <p class="new_mb_0 new_tm">
                                                        <a
                                                            href="{{ clickTrackingMail($baseUrl, $data['base_url']. '/page/about', $hashId) }}"><span>About</span></a>
                                                        &nbsp; · &nbsp;
                                                        <a
                                                            href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/collection', $hashId) }}"><span>Products</span></a>
                                                        &nbsp; · &nbsp;
                                                        <a
                                                            href="{{ clickTrackingMail($baseUrl, $data['base_url'] . $supportPath, $hashId) }}"><span style="color: #ff4081;">Support</span></a>
                                                    </p>
                                                    @endif
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
            </td>
        </tr>
    </tbody>
</table>

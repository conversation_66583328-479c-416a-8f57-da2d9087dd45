<table role="presentation" class="new_email_table" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tbody>
        <tr>
            <td class="new_email_body new_tc">
                <!--[if (mso)|(IE)]><table role="presentation" width="640" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:640px;Margin:0 auto;"><tbody><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                <table role="presentation" class="new_content_section" width="100%" border="0" cellspacing="0"
                    cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="new_content_cell new_content_b new_py new_bb new_tc">
                                @foreach ($products as $product)
                                    <!--[if (mso)|(IE)]><table role="presentation" width="624" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:624px;Margin:0 auto;"><tbody><tr><td width="156" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                    <div class="new_col_1">
                                        <table role="presentation" class="new_column" width="100%" border="0"
                                            cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="new_column_cell new_tc">
                                                        <p class="new_mb_0 new_imgr">
                                                            <img class="new_bra" role="img"
                                                                src="{{ sendMailImgUrl($product['thumb_url'], 'thumb') }}"
                                                                width="100" height="125" style="max-width:100px;">
                                                        </p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><td width="484" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                    <div class="new_col_2">
                                        <table role="presentation" class="new_column" width="100%" border="0"
                                            cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="new_column_cell new_tl new_switch_tc">
                                                        <h3>
                                                            @php
                                                                if (!empty($domain) && !empty($product['product_url']) && strpos($product['product_url'], '/') === 0)
                                                                {
                                                                    $product['product_url'] = "https://" . $domain . $product['product_url'];
                                                                }
                                                            @endphp
                                                            @if (!empty($domain) && !empty($product['product_url']))
                                                                <a href="{{ clickTrackingMail($baseUrl, $product['product_url'], $hashId) }}" target="_blank">{{ $product['campaign_title'] . ' - ' . $product['product_name'] }}</a>
                                                            @else
                                                                <span>{{ $product['campaign_title'] . ' - ' . $product['product_name'] }}</span>
                                                            @endif
                                                            <span class="new_tm product_qty">× {{ $product['quantity'] }}</span>
                                                        </h3>
                                                        <p class="new_mb_0 new_tm">
                                                            {{ productOptionsParser($product['options']) }}
                                                        </p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--[if (mso)|(IE)]></td><td width="484" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->

                                    <div class="new_col_1" style="float: right; margin-right: 10px; max-width: 145px;">
                                        <table role="presentation" class="new_column" width="100%" border="0"
                                            cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td class="new_column_cell new_py new_tr new_switch_tc">
                                                        <p class="new_mb_0 new_tp product_price">
                                                            {{ $product['price'] ?? '' }}</p>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                    <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
                                @endforeach
                                {{ $slot }}
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
            </td>
        </tr>
    </tbody>
</table>

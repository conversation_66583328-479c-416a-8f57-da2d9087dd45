@php
    $baseUrl = $data['base_url'] ?? null;
    $hashId = $data['logHashId'] ?? null;
@endphp
@component('mail::layout', ['data' => $data])
    {{-- Header --}}
    @slot('header')
        @component('mail::header', [
            'data' => $data,
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
            ])
            {{ config('app.name') }}
        @endcomponent
    @endslot

    {{-- Body --}}
    {{ $slot }}

    {{-- Subcopy --}}
    @isset($subcopy)
        @slot('subcopy')
            @component('mail::subcopy', ['data' => $data])
                {{ $subcopy }}
            @endcomponent
        @endslot
    @endisset

    {{-- Footer --}}
    @slot('footer')
        @component('mail::footer', [
                'data' => $data,
                'baseUrl' => $baseUrl,
                'hashId' => $hashId
                ])
            © {{ date('Y') }} {{ config('app.name') }}. @lang('All rights reserved.')
        @endcomponent
    @endslot
@endcomponent

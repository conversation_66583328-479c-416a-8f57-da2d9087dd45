<table role="presentation" class="new_email_table" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tbody>
        <tr>
            <td class="new_email_body new_email_end new_tc">
                <!--[if (mso)|(IE)]><table role="presentation" width="640" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:640px;Margin:0 auto;"><tbody><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                <table role="presentation" class="new_content_section" width="100%" border="0" cellspacing="0"
                    cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="new_content_cell new_content_b new_brb new_tc" height="16">&nbsp; </td>
                        </tr>
                        <tr>
                            <td class="new_content_cell new_py new_brb new_tc">
                                <!--[if (mso)|(IE)]><table role="presentation" width="624" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:624px;Margin:0 auto;"><tbody><tr><td width="468" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                <div class="new_col_3">
                                    <table role="presentation" class="new_column" width="100%" border="0" cellspacing="0"
                                        cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="new_column_cell new_pt new_tl new_switch_tc">
                                                    <p class="new_mb_xs">
                                                        &copy; {{ date('Y') }} {{ isset($data['store_name']) ? strtoupper($data['store_name']) : 'SenPrints' }} <br>
                                                        @isset($data['store_info'])
                                                        <span class="new_tm">{{ $data['store_info']['address'] }}</span>
                                                        @endisset
                                                    </p>
                                                    @if (!empty($data['base_url']))
                                                    <p class="new_mb_0"><a
                                                            href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/page/about', $hashId) }}"><span>About</span></a>
                                                        &nbsp; · &nbsp;
                                                        <a
                                                            href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/', $hashId) }}"><span>Products</span></a>
                                                        &nbsp; · &nbsp; <a
                                                            href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/page/contact-us', $hashId) }}"><span>Support</span></a>
                                                        @isset($data['first_campaign'])
                                                        &nbsp; ·
                                                            &nbsp; <a
                                                                href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/report?campaign=' . $data['first_campaign'], $hashId) }}"><span>Report DMCA violation</span></a>
                                                        @endisset
                                                        @isset($data['unsubscribe_key'])
                                                        &nbsp; ·
                                                        &nbsp; <a
                                                            href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/unsubscribe/' . $data['unsubscribe_key'], $hashId) }}"><span>Unsubscribe</span></a>
                                                        @endisset
                                                    </p>
                                                    @endif
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if (mso)|(IE)]></td><td width="156" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                @isset($data['store_info'])
                                <div class="new_col_1">
                                    <table role="presentation" class="new_column" width="100%" border="0" cellspacing="0"
                                        cellpadding="0">
                                        <tbody>
                                            <tr>
                                                <td class="new_column_cell new_pt new_tr new_switch_tc">
                                                    <p class="new_fsocial new_mb_0 new_tm">
                                                        @php
                                                            $accounts = $data['store_info']['social_accounts'];
                                                        @endphp
                                                        @if (isset($accounts['facebook']) && strlen($accounts['facebook']) > 0)
                                                            <a href="{{ clickTrackingMail($baseUrl, $accounts['facebook'], $hashId) }}"
                                                                style="line-height: inherit;text-decoration: none;color: #9E9E9E;"><img
                                                                    src="{{ getFullPathEmailResource() }}/social-facebook.png"
                                                                    width="24" height="24" alt="Facebook"
                                                                    style="max-width: 24px;"></a>
                                                            &nbsp;
                                                        @endif
                                                        @if (isset($accounts['twitter']) && strlen($accounts['twitter']) > 0)
                                                            <a href="{{ clickTrackingMail($baseUrl, $accounts['twitter'], $hashId) }}"
                                                                style="line-height: inherit;text-decoration: none;color: #9E9E9E;"><img
                                                                    src="{{ getFullPathEmailResource() }}/social-twitter.png"
                                                                    width="24" height="24" alt="Twitter"
                                                                    style="max-width:24px"></a>
                                                            &nbsp;
                                                        @endif
                                                        @if (isset($accounts['instagram']) && strlen($accounts['instagram']) > 0)
                                                            <a href="{{ clickTrackingMail($baseUrl, $accounts['instagram'], $hashId) }}"
                                                                style="line-height: inherit;text-decoration: none;color: #9E9E9E;"><img
                                                                    src="{{ getFullPathEmailResource() }}/social-instagram.png"
                                                                    width="24" height="24" alt="Instagram"
                                                                    style="max-width:24px"></a>
                                                            &nbsp;
                                                        @endif
                                                        @if (isset($accounts['pinterest']) && strlen($accounts['pinterest']) > 0)
                                                            <a href="{{ clickTrackingMail($baseUrl, $accounts['pinterest'], $hashId) }}"
                                                                style="line-height: inherit;text-decoration: none;color: #9E9E9E;"><img
                                                                    src="{{ getFullPathEmailResource() }}/social-pinterest.png"
                                                                    width="24" height="24" alt="Pinterest"
                                                                    style="max-width:24px"></a>
                                                        @endif
                                                    </p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                @endisset
                                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
            </td>
        </tr>
    </tbody>
</table>

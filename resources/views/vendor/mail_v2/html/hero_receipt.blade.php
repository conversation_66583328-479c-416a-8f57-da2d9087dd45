<table role="presentation" class="new_email_table" width="100%" border="0" cellspacing="0" cellpadding="0">
    <tbody>
        <tr>
            <td class="new_email_body new_tc">
                <!--[if (mso)|(IE)]><table role="presentation" width="640" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:640px;Margin:0 auto;"><tbody><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                <table role="presentation" class="new_content_section" width="100%" border="0" cellspacing="0"
                    cellpadding="0">
                    <tbody>
                        <tr>
                            <td class="new_content_cell new_content_b new_pt new_tc">
                                <!--[if (mso)|(IE)]><table role="presentation" width="624" border="0" cellspacing="0" cellpadding="0" align="center" style="vertical-align:top;width:624px;Margin:0 auto;"><tbody><tr><td width="312" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                                <div class="new_col_2" style="float: right;">
                                    <table role="presentation" class="new_column" width="100%" cellspacing="0"
                                        cellpadding="0" border="0">
                                        <tbody>
                                            <tr>
                                                <td class="new_column_cell new_px new_bb new_tr new_switch_tc">
                                                    <p class="new_mb_0">
                                                        Subtotal: {{ $sub_total }}<br />
                                                        Shipping Cost: {{ $shipping_cost }}<br />
                                                        @if (removeCurrency($insurance_fee) > 0)
                                                        Shipping Insurance: {{ $insurance_fee }}<br />
                                                        @endif
                                                        @if (removeCurrency($tip_amount) > 0)
                                                            Tip amount: {{ $tip_amount }}<br />
                                                        @endif
                                                        @if (removeCurrency($total_discount) > 0)
                                                            Discount <span class="new_discount_code">{{ $order_discount_code }}</span>: {{ $total_discount }}<br />
                                                        @endif
                                                        @if (removeCurrency($payment_discount) > 0)
                                                            Payment Discount: {{ $payment_discount }}<br />
                                                        @endif
                                                        @if (removeCurrency($tax) > 0)
                                                            Tax: {{ $tax }}<br />
                                                        @endif
                                                        <br />
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="new_column_cell new_px new_tr new_switch_tc">
                                                    <h3 class="new_mbe">
                                                        Total Amount: {{ $total_amount }}</h3>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
            </td>
        </tr>
    </tbody>
</table>

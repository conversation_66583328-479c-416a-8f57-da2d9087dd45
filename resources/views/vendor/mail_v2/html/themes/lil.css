/* Reset */
table,
td,
div {
    box-sizing: border-box;
}

table,
td {
    mso-table-lspace: 0pt;
    mso-table-rspace: 0pt;
}

html,
body {
    width: 100% !important;
    min-width: 100%;
    margin: 0;
    padding: 0;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

.email_body td,
.email_body div,
.email_body a,
.email_body span {
    line-height: inherit;
}

.email_body a {
    text-decoration: none;
}

#outlook a {
    padding: 0;
}

img {
    outline: none;
    border: 0;
    text-decoration: none;
    -ms-interpolation-mode: bicubic;
    clear: both;
    line-height: 100%;
}

table {
    border-spacing: 0;
    mso-table-lspace: 0pt;
    mso-table-rspace: 0pt;
}

td {
    vertical-align: top;
}

/* Grid */
.email_table,
.content_section,
.column,
.col_1,
.col_12,
.col_2,
.col_3,
.col_thumb,
.col_description {
    width: 100%;
    min-width: 100%;
    min-width: 0 !important;
}

.email_body,
.content_cell,
.col_1,
.col_12,
.col_2,
.col_3,
.col_thumb,
.col_description {
    font-size: 0 !important;
    line-height: 100%;
}

.col_1,
.col_12,
.col_2,
.col_3,
.col_thumb,
.col_description {
    display: inline-block;
    vertical-align: top;
}

.content_section {
    max-width: 640px;
    margin: 0 auto;
    text-align: center;
}

.email_body,
.content_cell,
.column_cell {
    padding-left: 8px;
    padding-right: 8px;
}

.email_start {
    padding-top: 32px;
}

.email_end {
    padding-bottom: 32px;
}

.column_cell {
    vertical-align: top;
}

.col_1 {
    max-width: 156px;
}

.col_12 {
    max-width: 192px;
}

.col_2 {
    max-width: 312px;
}

.col_3 {
    max-width: 468px;
}

.col_thumb {
    max-width: 80px;
}

.col_description {
    max-width: 372px;
}

.column_cell,
.column_cell p,
.ebtn a,
.ebtn span .column_cell h1,
.column_cell h2,
.column_cell h3,
.column_cell h4 {
    font-family: Arial, Helvetica, sans-serif;
}

.ebtn a,
.ebtn span .column_cell h1,
.column_cell h2,
.column_cell h3,
.column_cell h4 {
    font-weight: bold;
}

.column_cell,
.column_cell p {
    font-size: 16px;
    color: #616161;
}

.column_cell p {
    line-height: 23px;
    mso-line-height-rule: exactly;
    margin-top: 0;
    margin-bottom: 24px;
}

.column_cell p.lead {
    font-size: 20px;
    line-height: 27px;
}

.column_cell h1,
.column_cell h2,
.column_cell h3,
.column_cell h4 {
    padding: 0;
    margin-left: 0;
    margin-right: 0;
    margin-top: 16px;
    margin-bottom: 8px;
    color: #212121;
}

.column_cell h1 a,
.column_cell h1 a span,
.column_cell h2 a,
.column_cell h2 a span,
.column_cell h3 a,
.column_cell h3 a span,
.column_cell h4 a,
.column_cell h4 a span {
    color: #212121;
}

.column_cell h1 {
    font-size: 26px;
    line-height: 34px;
}

.column_cell h2 {
    font-size: 20px;
    line-height: 26px;
}

.column_cell h3 {
    font-size: 18px;
    line-height: 23px;
}

.column_cell h4 {
    font-size: 14px;
    line-height: 18px;
}

.footer_b .column_cell,
.footer_b .column_cell p,
.footer_b .column_cell a,
.footer_b .column_cell a span {
    color: #ffffff;
}

.email_body,
html,
body {
    background-color: #d9dcee;
}

.column_cell a,
.column_cell a span,
.column_cell.tp,
.column_cell .tp {
    color: #3f51b5;
}

.nav_menu {
    text-align: right;
    padding-top: 24px;
}

.nav_menu p {
    line-height: 100%;
}

.hero_image {
    background-repeat: no-repeat;
    background-position: 50% 0;
}

.hdr_menu {
    text-align: right;
    padding-top: 10px;
}

.hdr_menu p {
    line-height: 100%;
}

.hdr_menu a,
.email_body a.blink,
.email_body a.blink:visited {
    text-decoration: none;
}

.email_body .logo_c {
    line-height: 100%;
}

.logo_c img {
    width: auto;
    height: 32px;
}

.email_body .fsocial {
    line-height: 100%;
}

.fsocial img {
    width: 24px;
    height: 24px;
}

.hr_ep {
    font-size: 0;
    line-height: 1px;
    mso-line-height-rule: exactly;
    min-height: 1px;
    overflow: hidden;
    height: 2px;
    background-color: transparent !important;
}

.content_b {
    background-color: #ffffff;
}

.accent_b {
    background-color: #ff4081;
}

.default_b {
    background-color: #3f51b5;
}

.footer_b {
    background-color: #8d8f9a;
}

img {
    max-width: 200px;
}

.column_cell .imgr,
.column_cell .imgr img {
    width: 100%;
    height: auto;
    clear: both;
    font-size: 0;
    line-height: 100%;
}

.column_cell .imgr a,
.column_cell .imgr span {
    line-height: 1;
}

.ebtn,
.ebtn_xs,
.ic_h,
.hr_rl {
    display: table;
    margin-left: auto;
    margin-right: auto;
}

.ebtn td {
    font-size: 15px;
    font-weight: bold;
    padding: 9px 18px;
    line-height: 22px;
    text-transform: uppercase;
    mso-line-height-rule: exactly;
    border-radius: 3px;
    text-align: center;
}

.ebtn td a {
    text-decoration: none;
}

.ebtn td a,
.ebtn td a span {
    color: #ffffff;
}

.ic_h td {
    padding: 16px;
    text-align: center;
    vertical-align: middle;
    line-height: 100%;
    mso-line-height-rule: exactly;
    border-radius: 100px;
}

.ic_h img {
    line-height: 100%;
}

.email_summary {
    display: none;
    font-size: 1px;
    line-height: 1px;
    max-height: 0px;
    max-width: 0px;
    opacity: 0;
    overflow: hidden;
}

.brt {
    border-radius: 3px 3px 0 0;
}

.brb {
    border-radius: 0 0 3px 3px;
}

.bra {
    border-radius: 3px;
}

.braf {
    border-radius: 200px;
}

.column_cell.tm,
.column_cell .tm,
.column_cell .tm a,
.column_cell .tm span {
    color: #9e9e9e;
}

.column_cell.sc,
.column_cell .sc,
.column_cell.sc p,
.column_cell.sc a,
.column_cell.sc a span {
    color: #ffffff;
}

.tdel {
    text-decoration: line-through;
}

.tc {
    text-align: center;
}

.tc .imgr img {
    margin-left: auto;
    margin-right: auto;
}

.tl {
    text-align: left;
}

table.tl {
    margin-left: 0;
    margin-right: auto;
}

.tr {
    text-align: right;
}

table.tr {
    margin-left: auto;
    margin-right: 0;
}

.py {
    padding-top: 16px;
    padding-bottom: 16px;
}

.px {
    padding-left: 16px;
    padding-right: 16px;
}

.pxs {
    padding-left: 8px;
    padding-right: 8px;
}

.pt {
    padding-top: 16px;
}

.pte {
    padding-top: 32px;
}

.pb {
    padding-bottom: 16px;
}

.pb_xs {
    padding-bottom: 8px;
}

.pbe {
    padding-bottom: 24px;
}

.pte_lg {
    padding-top: 64px;
}

.pl_0,
.content_cell.pl_0 {
    padding-left: 0;
}

.pr_0,
.content_cell.pr_0 {
    padding-right: 0;
}

.column_cell .mte {
    margin-top: 32px;
}

.column_cell .mt {
    margin-top: 16px;
}

.column_cell .mt_xs {
    margin-top: 8px;
}

.column_cell .mt_0 {
    margin-top: 0;
}

.column_cell .mb_0 {
    margin-bottom: 0;
}

.column_cell .mb_xs {
    margin-bottom: 8px;
}

.column_cell .mb {
    margin-bottom: 16px;
}

.column_cell .mbe {
    margin-bottom: 32px;
}

.bt {
    border-top: 1px solid;
}

.bb {
    border-bottom: 1px solid;
}

.bt,
.bb {
    border-color: #e0e0e0;
}

.clear {
    content: " ";
    display: block;
    clear: both;
    height: 1px;
    overflow: hidden;
    font-size: 0;
}

@media only screen {
    /* latin */
    @font-face {
        font-family: "Roboto";
        font-style: normal;
        font-weight: 400;
        src: local("Roboto"), local("Roboto-Regular"),
            url(https://fonts.gstatic.com/s/roboto/v16/CWB0XYA8bzo0kSThX0UTuA.woff2)
                format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC,
            U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
    }

    /* latin */
    @font-face {
        font-family: "Roboto";
        font-style: normal;
        font-weight: 700;
        src: local("Roboto Bold"), local("Roboto-Bold"),
            url(https://fonts.gstatic.com/s/roboto/v16/d-6IYplOFocCacKzxwXSOFtXRa8TVwTICgirnJhmVJw.woff2)
                format("woff2");
        unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC,
            U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
    }

    .column_cell,
    .column_cell p,
    .ebtn a,
    .ebtn span .column_cell h1,
    .column_cell h2,
    .column_cell h3,
    .column_cell h4 {
        font-family: "Roboto", sans-serif !important;
        font-weight: 400 !important;
    }

    .ebtn a,
    .ebtn span,
    .column_cell strong,
    .column_cell h1,
    .column_cell h2,
    .column_cell h3,
    .column_cell h4 {
        font-weight: 700 !important;
    }

    .column_cell a {
        display: inline-block;
    }

    .column_cell a img {
        vertical-align: middle;
    }

    .ebtn td {
        padding: 0 !important;
    }

    .ebtn a {
        display: block !important;
        padding: 7px 18px !important;
        line-height: 26px !important;
    }

    .ebtn a span {
        display: block !important;
        text-align: center !important;
        vertical-align: top !important;
        line-height: inherit !important;
    }
}

@media (max-width: 657px) {
    .col_1,
    .col_12,
    .col_2,
    .col_3,
    .col_thumb,
    .col_description {
        max-width: none !important;
    }

    .nav_menu {
        padding-top: 18px !important;
    }

    .email_start {
        padding-top: 8px !important;
    }

    .email_end {
        padding-bottom: 8px !important;
    }

    .nav_menu,
    .logo_c {
        text-align: center !important;
    }

    .email_start .content_cell {
        position: relative;
    }

    .col_nav {
        width: auto !important;
        max-width: none !important;
        position: absolute;
        right: 8px;
        top: 2px;
    }

    .pte_lg,
    .py.pte_lg {
        padding-top: 32px !important;
    }

    .switch_xs {
        text-align: left !important;
    }

    .switch_tc {
        text-align: center !important;
    }

    .switch_tc table.tl {
        float: none !important;
        margin-left: auto !important;
        margin-right: auto !important;
    }

    .hide {
        max-height: 0 !important;
        display: none !important;
        mso-hide: all !important;
        overflow: hidden !important;
        font-size: 0 !important;
    }
}

h4.logo {
    color: white !important;
}

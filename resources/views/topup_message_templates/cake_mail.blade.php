Return-Path: <phuc+caf_=api.senprints.com--webhook--email.payout?token=0bc8e8104854b5fedb1c9c3910c49517=<EMAIL>>
Received: from mail-oa1-f42.google.com (mail-oa1-f42.google.com [*************])
 by inbound-smtp.us-east-1.amazonaws.com with SMTP id ujjhg07rej05jqn33k055kohp4k9hpi4vt7dpog1
 for api.senprints.com--webhook--email.payout?token=<EMAIL>;
 Wed, 10 Jan 2024 03:28:03 +0000 (UTC)
X-SES-Spam-Verdict: PASS
X-SES-Virus-Verdict: PASS
Received-SPF: pass (spfCheck: domain of senprints.com designates ************* as permitted sender) client-ip=*************; envelope-from=phuc+caf_=api.senprints.com--webhook--email.payout?token=0bc8e8104854b5fedb1c9c3910c49517=<EMAIL>; helo=mail-oa1-f42.google.com;
Authentication-Results: amazonses.com;
 spf=pass (spfCheck: domain of senprints.com designates ************* as permitted sender) client-ip=*************; envelope-from=phuc+caf_=api.senprints.com--webhook--email.payout?token=0bc8e8104854b5fedb1c9c3910c49517=<EMAIL>; helo=mail-oa1-f42.google.com;
 dkim=pass header.i=@cake.vn;
 dmarc=pass header.from=cake.vn;
X-SES-RECEIPT: AEFBQUFBQUFBQUFFMm52QmxhTjlGUGN2RWxTNXNwSkpXQjV0elNQZ3ZTY3p3NlhndHl1TDlGV3JHSGk3eDNlZWhBeExkVEV4UmJrOEdpMUErbWV5eExHZHhnT01BNnMzZDRaVjRMaEdkZjRNVzk5bFh6dVJuN3ptMEN2RkxGVnhTaGV5SFVid2wxSWR5K3V5ZzRmN0ZlU3YwaUlUbmdMRDhPeGJlK2FiNHEwOVdkQmt1M01IWCtnSUpoY2hZQXhpanU1TTBZekRTSkI0UlNKZjhNcHNXVFBJMVEzU214b0RwdmJHMnJBUXZGMGNMVHQ0NTZJTjUvTWxEMmJGRHVDbzJTK1VFdzd6QUhPNDhSWngyZTI4ZDM0SGxhbjNPbEVSVmQ1MGpPRGMxMmhSd0ROZlJvZi9qVFFzdlJGSGl1enh5Wm5ydGNMNThZUGVHQzVMZHlpQ1kzZDh6dnpnRzVUeVBWRTRMb0JOS1hrSmIranRZbFNwaDJQSWZKcUs0RnNERXVRVlJPK3hnZzF3VjhnU29uWHNWenBjdmxka05ZZmhNVkIzcUR1RG53V1dYOGZUdlJkMUwwUT09
X-SES-DKIM-SIGNATURE: a=rsa-sha256; q=dns/txt; b=AUmsGnYjyMrvzsj3rnhsAdtF9UOMBT8cp8fwMjmxEK5gDlJAj6PqPkuwHHW2R/7iPyQUOYWQgZ5/8V5pDh8DJuDpY+k4aHqYE2RZUuiIQZiJDV1l6yVvnwMXIhbB/Q7Y85UeR3l7DaMYjf14HnCYlqLhiPmlc6VQ6AGaSEn+mTk=; c=relaxed/simple; s=224i4yxa5dv7c2xz3womw6peuasteono; d=amazonses.com; t=**********; v=1; bh=NL0KFUe19L2GVK96DlNJ35VxLW1PraNPmB5sDqoqpp0=; h=From:To:Cc:Bcc:Subject:Date:Message-ID:MIME-Version:Content-Type:X-SES-RECEIPT;
Received: by mail-oa1-f42.google.com with SMTP id 586e51a60fabf-204df0830ccso3161750fac.1
        for <api.senprints.com--webhook--email.payout?token=<EMAIL>>; Tue, 09 Jan 2024 19:28:03 -0800 (PST)
X-Google-DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed;
        d=1e100.net; s=20230601; t=**********; x=**********;
        h=to:subject:message-id:mime-version:from:date
         :content-transfer-encoding:dkim-signature:delivered-to
         :x-forwarded-for:x-forwarded-to:x-gm-message-state:from:to:cc
         :subject:date:message-id:reply-to;
        bh=1E03OFmRYu+BdeaLnNDDAB8y/30GlwvXpsvFqKtrQe0=;
        b=UoG8wIPLMAK/xD0MoX1x2ivbR+xq+UFZuqccRBcIZTr/XRpK7NGm/08r8dw88JzsTU
         oAPXOjoIKhJiEjXKRI1cr74DbLb5u+inzeV9FwDK+ZpbhKsxnH+FluzYJ9qoRTpeJ9Mx
         Knyuzl7nQrT4d+oUh6d+ZrBGLoTB51fqd2PP9Fxeq+JrjSvAXrRI8SMaDnc074Wgvu7/
         Hfl1bpEQau4gB7owLRqiK3Kb/bEpnXhHWvWI5plN4NYrCBdTeusDW9KtMRqSkKhOAY9d
         tCaaKu8dTh8Ex9akUKeMuuoxUk1KVQlKhSevceMOeG4nbzxKUHywjs32f+S2XaQyYPI3
         +YCg==
X-Gm-Message-State: AOJu0Yw7NqhVv7mieGX8ahkZypTH8Hfe7G91xaOTc1JxWA8WQTWbPo9t
	ouISnjPJqRy0KpmU3F5MyegP7dN0UPnserDJBH/7knzilREt3z3fZLibvxyMs6SxqF2d0Lg3kHI
	=
X-Received: by 2002:a05:6359:639b:b0:170:982:5611 with SMTP id sg27-20020a056359639b00b0017009825611mr326480rwb.32.1704857282749;
        Tue, 09 Jan 2024 19:28:02 -0800 (PST)
X-Forwarded-To: api.senprints.com--webhook--email.payout?token=<EMAIL>
X-Forwarded-For: <EMAIL> api.senprints.com--webhook--email.payout?token=<EMAIL>
Delivered-To: <EMAIL>
Received: by 2002:a17:522:b610:b0:56b:30d7:6870 with SMTP id dn16csp864205pvb;
        Tue, 9 Jan 2024 19:28:01 -0800 (PST)
X-Google-Smtp-Source: AGHT+IEAbUtNxewx/55E6Bl/uBydg/AZ+iau8gtBpkBjAxNZGOvRKp2/78L2k1afjj9Qeew50IbT
X-Received: by 2002:a05:622a:189b:b0:429:8a42:d1ef with SMTP id v27-20020a05622a189b00b004298a42d1efmr741355qtc.16.1704857281008;
        Tue, 09 Jan 2024 19:28:01 -0800 (PST)
ARC-Seal: i=1; a=rsa-sha256; t=1704857280; cv=none;
        d=google.com; s=arc-20160816;
        b=Jt+WvTxKuVe6uDHVsmEPy1ypih/m6Kjh1/WZcPFnZ+EkSccOtHodd5Y9EfqBS4hJdj
         Dr1++XHbjNqTFDq3aRqqye6SN9nBj7SKpERT822px3pP3MwHYSZZdwLVsVGn0hbtV3V+
         tUj+TMSgN2snrfM5CUMfyc/VB8l5gegU4WdZXCfHOi94C1hGNRyy5Bq4EI/f5RoFrh3E
         2Pw7X3LYKxfRulm0ESS3oiJFWMsPfAX9UrmNQK/ZLgFw0s1SynpJ9nACYblM/w5DP6Sq
         IpvLX0AeCA0WTU14MuH0GHyiiizIM2Fgaj5n65cCbAJzBs3jC+HIBBP8dEibg0o3o1n2
         mz6A==
ARC-Message-Signature: i=1; a=rsa-sha256; c=relaxed/relaxed; d=google.com; s=arc-20160816;
        h=to:subject:message-id:mime-version:from:date
         :content-transfer-encoding:dkim-signature;
        bh=1E03OFmRYu+BdeaLnNDDAB8y/30GlwvXpsvFqKtrQe0=;
        fh=1mOVBeaFWrv5MOre+DI2N2jZnQLIA7II6teNKSRk19k=;
        b=XGay4+BTLEThcQ2a1HRW+IreC1A5YmiNytGjERu/89sPxwm0HBEgH4qevlTvd19dud
         xHU+rUEsqmHM1UwdfWu/8iUvmhtGua+jSd9UA6B/ALuMsMY+O78oIliZtJ2JPLyDQkck
         ppBwhffforoRVpvqUa8OhSPwVwYMzUDj4rjJ/kB6FIdRWXelOY8rxl9uhK/xKDD7fkJN
         u0cy/yJAOybdRm9yqLZEdJxr9LT1M6trgddbazwjcCbEIopkCpYwC8XNxNcg4MGqRW1D
         7VbtjRr8lA3hnx6BPtKHJJxvtyGy/OPf9sQKWgYuJje4kdq+ISnrATgwcKScSf6bdf6m
         gwew==
ARC-Authentication-Results: i=1; mx.google.com;
       dkim=pass header.i=@cake.vn header.s=s1 header.b=DZX3td4R;
       spf=pass (google.com: domain of bounces+34255138-d688-payment=<EMAIL> designates ************** as permitted sender) smtp.mailfrom="bounces+34255138-d688-payment=<EMAIL>";
       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=cake.vn
Return-Path: <bounces+34255138-d688-payment=<EMAIL>>
Received: from o1.ptr147.cake.vn (o1.ptr147.cake.vn. [**************])
        by mx.google.com with ESMTPS id f12-20020ac859cc000000b004254f1ddb34si3425087qtf.52.2024.***********.00
        for <<EMAIL>>
        (version=TLS1_3 cipher=TLS_AES_128_GCM_SHA256 bits=128/128);
        Tue, 09 Jan 2024 19:28:00 -0800 (PST)
Received-SPF: pass (google.com: domain of bounces+34255138-d688-payment=<EMAIL> designates ************** as permitted sender) client-ip=**************;
Authentication-Results: mx.google.com;
       dkim=pass header.i=@cake.vn header.s=s1 header.b=DZX3td4R;
       spf=pass (google.com: domain of bounces+34255138-d688-payment=<EMAIL> designates ************** as permitted sender) smtp.mailfrom="bounces+34255138-d688-payment=<EMAIL>";
       dmarc=pass (p=QUARANTINE sp=QUARANTINE dis=NONE) header.from=cake.vn
DKIM-Signature: v=1; a=rsa-sha256; c=relaxed/relaxed; d=cake.vn;
	h=content-transfer-encoding:content-type:from:mime-version:subject:to:
	cc:content-type:from:subject:to;
	s=s1; bh=1E03OFmRYu+BdeaLnNDDAB8y/30GlwvXpsvFqKtrQe0=;
	b=DZX3td4RE0nmSZPS7hNNFDqxus+k2eZBNmhehsrwYx9/251LKikqyyUjSz2odsxUbIoY
	lQZYRVRhoG36mzs03Jd12CDGmxUhc3rAFWF6Hm/eaG2Rg/5teonk9ALBKTl1REGXiQBP7G
	QZqMWe1IpG3q2nG+gVuIayWzQeaeTMAXVMx+MQFawDRTbPn32q8R6j7RSvbm4gSmTSqp9b
	L03FzpN6C7jWhVYjZshKoypucHNjGt+eNmRBNaPkyuTXqoDujQjJDMelqUQkhLwSYJbeR9
	NUeiKJYMQu4L/wXFhw3/j9HFFidWN2ilRenNFwxDEURTia2d5wPcBiDUMGtXaWbQ==
Received: by filterdrecv-canary-7c754f99-8jkds with SMTP id filterdrecv-canary-7c754f99-8jkds-1-659E0EC0-5
        2024-01-10 03:28:00.********* +0000 UTC m=+7289172.*********
Received: from MzQyNTUxMzg (unknown)
	by geopod-ismtpd-3 (SG) with HTTP
	id --VGXKfSRXW7Y5Zh1RKNkg
	Wed, 10 Jan 2024 03:27:59.935 +0000 (UTC)
Content-Transfer-Encoding: quoted-printable
Content-Type: text/html; charset=utf-8
Date: Wed, 10 Jan 2024 03:28:00 +0000 (UTC)
From: =?utf-8?q?Ng=C3=A2n_h=C3=A0ng_s=E1=BB=91_Cake_by_VPBank?=
	<<EMAIL>>
Mime-Version: 1.0
Message-ID: <--VGXKfSRXW7Y5Zh1RKNkg@geopod-ismtpd-3>
Subject: [CAKE] =?UTF-8?B?VGjDtG5nIGLDoW8gZ2lhbyBk4buLY2ggdGjDoG5oIGPDtG5n?=
X-SG-EID:
 =?us-ascii?Q?qGqhujaceFYZHG9QZWojwhLSIhIlTLRN19jz8X5DgmTFQ2ia84shuxhS4X6lu4?=
 =?us-ascii?Q?KChK=2Fz8Ku3k39A7uo5TavmBkP6maKLsZoWEfjTa?=
 =?us-ascii?Q?FNzGduZFR3lcUWLBw71Kd9BSzwQyg2pvZn+QcYN?=
 =?us-ascii?Q?jyLAZokHEe+fAGesA+q3DIz5xJy6mbvdE7Q9KzN?=
 =?us-ascii?Q?xBzpcKZWRrekIjxrbT+t+fyPzma8vzjixZOyJu+?=
 =?us-ascii?Q?Ox2t3mV8eMtBp1rUjEUC5lm8hoRz05wuz7kemoO?=
 =?us-ascii?Q?x04iBGhzIHiJNrh8A5kQQ=3D=3D?=
X-SG-ID:
 =?us-ascii?Q?N2C25iY2uzGMFz6rgvQsb8raWjw0ZPf1VmjsCkspi=2FJcDzyJyu4AHEuK4Sv4nd?=
 =?us-ascii?Q?F+46KSYzafU4lzhJEpzm25J3nNy2zILCEQKUjcI?=
 =?us-ascii?Q?YRzqzmRQxqIgvh02XG82pmeD9mPlzIcZ6rC2pd2?=
 =?us-ascii?Q?3wCW6bfgXvQNv6qaztUfAk27aSLdTDbzBiZdIJa?=
 =?us-ascii?Q?hHfklEGKU4xTS2evKsOUjJKMJKjN9joY3VGofNX?=
 =?us-ascii?Q?bXnNth9S=2F5CBUPEYUIM8uFvAbNvmwbmYLMDjtnZ?=
 =?us-ascii?Q?BcNmxovpqmfT1JodoSF99AoyCnanKPHldDU6iGU?=
 =?us-ascii?Q?6dzCPYYHylxXyOsofRwen5wH?=
To: <EMAIL>
X-Entity-ID: gt/30VsGisdla73A2CyjtA==

<!doctype html>
<html xmlns=3D"http://www.w3.org/1999/xhtml" xmlns:v=3D"urn:schemas-microso=
ft-com:vml" xmlns:o=3D"urn:schemas-microsoft-com:office:office">

<head>
  <title>
  </title>
 =20
  <meta http-equiv=3D"X-UA-Compatible" content=3D"IE=3Dedge">
 =20
  <meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3DUTF-8">
  <meta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=3D=
1">
  <style type=3D"text/css">
    #outlook a {
      padding: 0;
    }

    body {
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }

    table,
    td {
      border-collapse: collapse;
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }

    img {
      border: 0;
      height: auto;
      line-height: 100%;
      outline: none;
      text-decoration: none;
      -ms-interpolation-mode: bicubic;
    }

    p {
      display: block;
      margin: 13px 0;
    }
  </style>
 =20
 =20
  <style type=3D"text/css">
    @media only screen and (min-width:480px) {
      .mj-column-per-100 {
        width: 100% !important;
        max-width: 100%;
      }

      .mj-column-per-50 {
        width: 50% !important;
        max-width: 50%;
      }
    }
  </style>
  <style media=3D"screen and (min-width:480px)">
    .moz-text-html .mj-column-per-100 {
      width: 100% !important;
      max-width: 100%;
    }

    .moz-text-html .mj-column-per-50 {
      width: 50% !important;
      max-width: 50%;
    }
  </style>
  <style type=3D"text/css">
    @media only screen and (max-width:480px) {
      table.mj-full-width-mobile {
        width: 100% !important;
      }

      td.mj-full-width-mobile {
        width: auto !important;
      }
    }
  </style>
  <style type=3D"text/css">
  </style>
</head>

<body style=3D"word-spacing:normal;background-color:#CECECE;">
  <div style=3D"background-color:#CECECE;">
   =20
    <div style=3D"background:#F5F6F7;background-color:#F5F6F7;margin:0px au=
to;max-width:550px;">
      <table align=3D"center" border=3D"0" cellpadding=3D"0" cellspacing=3D=
"0" role=3D"presentation" style=3D"background:#F5F6F7;background-color:#F5F=
6F7;width:100%;">
        <tbody>
          <tr>
            <td style=3D"direction:ltr;font-size:0px;padding:24px 32px;text=
-align:center;">
             =20
              <div class=3D"mj-column-per-100 mj-outlook-group-fix" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:100%;">
                <table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" rol=
e=3D"presentation" width=3D"100%">
                  <tbody>
                    <tr>
                      <td style=3D"vertical-align:top;padding:0;">
                        <table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" style=3D"" width=3D"100%">
                          <tbody>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:0 0 8px 16px;word-break:break-word;">
                                <table border=3D"0" cellpadding=3D"0" cells=
pacing=3D"0" role=3D"presentation" style=3D"border-collapse:collapse;border=
-spacing:0px;">
                                  <tbody>
                                    <tr>
                                      <td style=3D"width:100px;">
                                        <img height=3D"auto" src=3D"https:/=
/imgcdn.be.com.vn/cake/v1/home/<USER>" style=3D"border:0;display:=
block;outline:none;text-decoration:none;height:auto;width:100%;font-size:13=
px;" width=3D"100" />
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                            <tr>
                              <td align=3D"center" style=3D"font-size:0px;p=
adding:24px 0;word-break:break-word;">
                                <table border=3D"0" cellpadding=3D"0" cells=
pacing=3D"0" role=3D"presentation" style=3D"border-collapse:collapse;border=
-spacing:0px;">
                                  <tbody>
                                    <tr>
                                      <td style=3D"width:486px;">
                                        <img height=3D"auto" src=3D"https:/=
/imgcdn.be.com.vn/cake/v1/email_banner/term_deposit_banner.png" style=3D"bo=
rder:0;border-radius:8px;display:block;outline:none;text-decoration:none;he=
ight:auto;width:100%;font-size:13px;" width=3D"486" />
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
             =20
              <div class=3D"mj-column-per-100 mj-outlook-group-fix" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:100%;">
                <table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" rol=
e=3D"presentation" width=3D"100%">
                  <tbody>
                    <tr>
                      <td style=3D"vertical-align:top;padding:0;">
                        <table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" style=3D"" width=3D"100%">
                          <tbody>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:0 16px 12px;word-break:break-word;">
                                <div style=3D"font-family:Be Vietnam Pro 20=
22, sans-serif;font-size:14px;line-height:20px;text-align:left;color:#00000=
0;"><span style=3D"color: #394860">Ch=C3=A0o <b>Tr=E1=BB=8Bnh Minh Ph=C3=BA=
c</b></span></div>
                              </td>
                            </tr>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:12px 16px;word-break:break-word;">
                                <div style=3D"font-family:Be Vietnam Pro 20=
22, sans-serif;font-size:14px;line-height:20px;text-align:left;color:#39486=
0;">Cake xin th=C3=B4ng b=C3=A1o t=C3=A0i kho=E1=BA=A3n c=E1=BB=A7a b=E1=BA=
=A1n v=E1=BB=ABa m=E1=BB=9Bi ph=C3=A1t sinh giao d=E1=BB=8Bch nh=C6=B0 sau:=
</div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
             =20
              <div class=3D"mj-column-per-100 mj-outlook-group-fix" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:100%;">
                <table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" rol=
e=3D"presentation" width=3D"100%">
                  <tbody>
                    <tr>
                      <td style=3D"vertical-align:top;padding:0 0 12px;">
                        <table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" style=3D"" width=3D"100%">
                          <tbody>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:12px 16px;word-break:break-word;">
                                <div style=3D"font-family:Be Vietnam Pro 20=
22, sans-serif;font-size:15px;font-weight:bold;line-height:20px;text-align:=
left;color:#394860;">Th=C3=B4ng tin t=C3=A0i kho=E1=BA=A3n</div>
                              </td>
                            </tr>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:0;word-break:break-word;">
                                <table cellpadding=3D"0" cellspacing=3D"0" =
width=3D"100%" border=3D"0" style=3D"color:#666F80;font-family:Be Vietnam P=
ro 2022, sans-serif;font-size:14px;line-height:20px;table-layout:auto;width=
:100%;border:none;">
                                 =20
                                  <tr valign=3D"top" height=3D"8px" style=
=3D"background: #ffffff">
                                   =20
                                    <td style=3D"border-top-left-radius: 12=
px"></td>
                                    <td />
                                    <td />
                                   =20
                                    <td width=3D"1px"></td>
                                    <td />
                                    <td />
                                    <td style=3D"border-top-right-radius: 1=
2px" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                width: 30%;
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
              "> T=C3=A0i kho=E1=BA=A3n nh=E1=BA=ADn </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
                color: #071a38;
              ">
                                      0376603125 - T=C3=A0i kho=E1=BA=A3n t=
hanh to=C3=A1n
                                    </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                width: 30%;
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
              "> T=C3=A0i kho=E1=BA=A3n chuy=E1=BB=83n </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
                color: #071a38;
              ">
                                      190359****4013
                                    </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                width: 30%;
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
              "> T=C3=AAn ng=C6=B0=E1=BB=9Di chuy=E1=BB=83n </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
                color: #071a38;
              ">
                                      HOANG NGOC TOAN
                                    </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"width: 30%; padding: 8px 0=
; background-color: #ffffff"> Ng=C3=A2n h=C3=A0ng chuy=E1=BB=83n </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"padding: 8px 0; background=
-color: #ffffff; color: #071a38">
                                      TECHCOMBANK
                                    </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                 =20
                                  <tr valign=3D"top" height=3D"8px" style=
=3D"background: #ffffff">
                                   =20
                                    <td style=3D"border-bottom-left-radius:=
 12px"></td>
                                    <td />
                                    <td />
                                   =20
                                    <td width=3D"1px"></td>
                                    <td />
                                    <td />
                                    <td style=3D"border-bottom-right-radius=
: 12px"></td>
                                  </tr>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
             =20
              <div class=3D"mj-column-per-100 mj-outlook-group-fix" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:100%;">
                <table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" rol=
e=3D"presentation" width=3D"100%">
                  <tbody>
                    <tr>
                      <td style=3D"vertical-align:top;padding:0 0 12px;">
                        <table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" style=3D"" width=3D"100%">
                          <tbody>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:12px 16px;word-break:break-word;">
                                <div style=3D"font-family:Be Vietnam Pro 20=
22, sans-serif;font-size:15px;font-weight:bold;line-height:20px;text-align:=
left;color:#394860;">Th=C3=B4ng tin giao d=E1=BB=8Bch</div>
                              </td>
                            </tr>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:0;word-break:break-word;">
                                <table cellpadding=3D"0" cellspacing=3D"0" =
width=3D"100%" border=3D"0" style=3D"color:#666F80;font-family:Be Vietnam P=
ro 2022, sans-serif;font-size:14px;line-height:20px;table-layout:auto;width=
:100%;border:none;">
                                 =20
                                  <tr valign=3D"top" height=3D"8px" style=
=3D"background: #ffffff">
                                   =20
                                    <td style=3D"border-top-left-radius: 12=
px"></td>
                                    <td />
                                    <td />
                                   =20
                                    <td width=3D"1px"></td>
                                    <td />
                                    <td />
                                    <td style=3D"border-top-right-radius: 1=
2px" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                width: 30%;
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
              "> Lo=E1=BA=A1i giao d=E1=BB=8Bch </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
                color: #071a38;
              ">
                                      Chuy=E1=BB=83n ti=E1=BB=81n ngo=C3=A0=
i CAKE
                                    </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                width: 30%;
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
              "> M=C3=A3 giao d=E1=BB=8Bch </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
                color: #071a38;
              ">
                                      {{ $paymentId }}
                                    </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                width: 30%;
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
              "> Ng=C3=A0y gi=E1=BB=9D giao d=E1=BB=8Bch </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
                color: #071a38;
              ">
                                      10/01/2024, 10:27:59
                                    </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                width: 30%;
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
              "> S=E1=BB=91 ti=E1=BB=81n </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
                color: #188126;
              "> +{{ $amount }} =C4=91</td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                width: 30%;
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
              "> Ph=C3=AD giao d=E1=BB=8Bch </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"
                padding: 8px 0;
                background-color: #ffffff;
                border-bottom: 1px solid #f2f1f7;
                color: #071a38;
              "> 0 =C4=91 </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                  <tr>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"width: 30%; padding: 8px 0=
; background-color: #ffffff"> N=E1=BB=99i dung giao d=E1=BB=8Bch </td>
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td width=3D"1px" />
                                   =20
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                    <td style=3D"padding: 8px 0; background=
-color: #ffffff; color: #071a38">
                                      {{ $code }} FT24010301017669
                                    </td>
                                    <td style=3D"width: 16px; background-co=
lor: #ffffff" />
                                  </tr>
                                 =20
                                  <tr valign=3D"top" height=3D"8px" style=
=3D"background: #ffffff">
                                   =20
                                    <td style=3D"border-bottom-left-radius:=
 12px"></td>
                                    <td />
                                    <td />
                                   =20
                                    <td width=3D"1px"></td>
                                    <td />
                                    <td />
                                    <td style=3D"border-bottom-right-radius=
: 12px"></td>
                                  </tr>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
             =20
              <div class=3D"mj-column-per-100 mj-outlook-group-fix" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:100%;">
                <table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" rol=
e=3D"presentation" width=3D"100%">
                  <tbody>
                    <tr>
                      <td style=3D"vertical-align:top;padding:0;">
                        <table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" style=3D"" width=3D"100%">
                          <tbody>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:12px 16px;word-break:break-word;">
                                <div style=3D"font-family:Be Vietnam Pro 20=
22, sans-serif;font-size:14px;line-height:22px;text-align:left;color:#39486=
0;"><b>C=E1=BA=A3m =C6=A1n b=E1=BA=A1n =C4=91=C3=A3 s=E1=BB=AD d=E1=BB=A5ng=
 d=E1=BB=8Bch v=E1=BB=A5 c=E1=BB=A7a Cake by VPBank</b></div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
             =20
              <div class=3D"mj-column-per-100 mj-outlook-group-fix" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:100%;">
                <table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" rol=
e=3D"presentation" width=3D"100%">
                  <tbody>
                    <tr>
                      <td style=3D"vertical-align:top;padding:0;">
                        <table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" style=3D"" width=3D"100%">
                          <tbody>
                            <tr>
                              <td align=3D"left" style=3D"font-size:0px;pad=
ding:12px 16px 0;word-break:break-word;">
                                <div style=3D"font-family:Be Vietnam Pro 20=
22, sans-serif;font-size:14px;line-height:22px;text-align:left;color:#39486=
0;">
                                  <div>Th=C3=A2n m=E1=BA=BFn,</div>
                                  <div>Cake Team</div>
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
             =20
            </td>
          </tr>
        </tbody>
      </table>
    </div>
   =20
    <div style=3D"background:#ffffff;background-color:#ffffff;margin:0px au=
to;max-width:550px;">
      <table align=3D"center" border=3D"0" cellpadding=3D"0" cellspacing=3D=
"0" role=3D"presentation" style=3D"background:#ffffff;background-color:#fff=
fff;width:100%;">
        <tbody>
          <tr>
            <td style=3D"direction:ltr;font-size:0px;padding:24px 48px;text=
-align:center;">
             =20
              <div class=3D"mj-column-per-100 mj-outlook-group-fix" style=
=3D"font-size:0;line-height:0;text-align:left;display:inline-block;width:10=
0%;direction:ltr;">
               =20
                <div class=3D"mj-column-per-50 mj-outlook-group-fix" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:50%;">
                  <table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" r=
ole=3D"presentation" width=3D"100%">
                    <tbody>
                      <tr>
                        <td style=3D"vertical-align:top;padding:0;">
                          <table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" style=3D"" width=3D"100%">
                            <tbody>
                              <tr>
                                <td align=3D"left" style=3D"font-size:0px;p=
adding:0;word-break:break-word;">
                                  <div style=3D"font-family:Be Vietnam Pro =
2022, sans-serif;font-size:14px;font-weight:500;line-height:20px;text-align=
:left;color:#071A38;">
                                    <div style=3D"padding-bottom: 8px"> C=
=E1=BA=A7n h=E1=BB=97 tr=E1=BB=A3? </div>
                                    <div style=3D"display: flex; align-item=
s: center; padding-bottom: 8px">
                                      <img src=3D"https://imgcdn.be.com.vn/=
cake/v1/email_icons/white_phone_icon.png" alt=3D"cake-phone.png" style=3D"w=
idth: 18px; height: 18px; padding-right: 8px" />
                                      <a href=3D"tel:1900636686" style=3D"c=
olor: #394860; line-height: 18px; text-decoration: none">1900 636686</a>
                                    </div>
                                    <div style=3D"display: flex; align-item=
s: center; padding-bottom: 8px">
                                      <img src=3D"https://imgcdn.be.com.vn/=
cake/v1/email_icons/white_mail_icon.png" alt=3D"cake-email.png" style=3D"wi=
dth: 18px; height: 18px; padding-right: 8px" />
                                      <a href=3D"mailto:<EMAIL>" style=
=3D"color: #3498d3; line-height: 18px; text-decoration: none"><EMAIL><=
/a>
                                    </div>
                                  </div>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
               =20
                <div class=3D"mj-column-per-50 mj-outlook-group-fix" style=
=3D"font-size:0px;text-align:left;direction:ltr;display:inline-block;vertic=
al-align:top;width:50%;">
                  <table border=3D"0" cellpadding=3D"0" cellspacing=3D"0" r=
ole=3D"presentation" width=3D"100%">
                    <tbody>
                      <tr>
                        <td style=3D"vertical-align:top;padding:0;">
                          <table border=3D"0" cellpadding=3D"0" cellspacing=
=3D"0" role=3D"presentation" style=3D"" width=3D"100%">
                            <tbody>
                              <tr>
                                <td align=3D"right" style=3D"font-size:0px;=
padding:0;word-break:break-word;">
                                  <div style=3D"font-family:Be Vietnam Pro =
2022, sans-serif;font-size:14px;font-weight:500;line-height:20px;text-align=
:right;color:#071A38;">
                                    <div style=3D"padding-bottom: 8px">K=E1=
=BA=BFt n=E1=BB=91i v=E1=BB=9Bi ch=C3=BAng t=C3=B4i</div>
                                    <div style=3D"line-height: 0px; padding=
-bottom: 8px">
                                      <div style=3D"display: inline-block; =
margin: 6px 0 6px 6px"><a href=3D"https://url5880.cake.vn/ls/click?upn=3DQl=
3mWgCiCIBDp9G6HsHIdBUZz7h1ycqha5VPtPJ8Go-2Fn6XdeU-2B4FwxkhRl5kgHLouTq1Ifu0l=
D-2F-2B9LUdocK8MA-3D-3DoCUR_vAiOuN9i8MoleFPwVqH5YYCugXxBNzJSaB9VKSwW7rGBTSz=
xTAJIn-2FtlvS8f-2BoQ-2FwHOgoTDZq8-2B1NRi98YX0kbntaAGloI1ei49VijM3CHCLI9k-2F=
9zTPy4rXVJCHn8tk3wN7K2hsSoa6foeOOXVIwBzzhQHHxxoMr5XJ93KwYil1WzQ4BtT4LIyzdSF=
VvE-2F833FpumGpQjVLRn-2FjJV-2BICxTrjBq25T8AIVf8DmnhT0tZuMCLhX2SR4N1TT-2FFHh=
cUELK4FjfUJlDppCYq-2FpR5mjU7f1K7TfT6L1GoRG2JgMCEUJnkJizH-2BoKATNNQelnK-2BHl=
uzUGOTmG-2BEyOm1neZqA4NcGUV2uTYbVmyGuArM3ncx0mGGP3dNktH-2FbQ2NODmn2PjOcKB0-=
2FkOmS9AIRxKGGM4Qi2-2BiKY2KmsxQU-2FhOFdrUQ4tzvT87GB5d-2BwgJ60Rwa935VXe0H82C=
gzuuXP0DGhYtOLRVTUA9GG8YXqCOGg-3D" style=3D"border-radius: 50%; background-=
color: #f2f1f7">
                                          <img src=3D"https://imgcdn.be.com=
.vn/cake/v1/email_icons/white_fb_icon.png" alt=3D"cake-fb.png" style=3D"dis=
play: block; width: 32px; height: 32px" />
                                        </a>
                                      </div>
                                      <div style=3D"display: inline-block; =
margin: 6px 0 6px 6px"><a href=3D"https://url5880.cake.vn/ls/click?upn=3DQl=
3mWgCiCIBDp9G6HsHIdGJzHhwCHmCwntaBvJEPu6zD-2B7I2-2Bxz7XPXNT62vY5HYyeZa7RV8a=
bQyfqginolfFSjBfjq2rMnsDMkMKG-2FK7C8-3D4Mn8_vAiOuN9i8MoleFPwVqH5YYCugXxBNzJ=
SaB9VKSwW7rGBTSzxTAJIn-2FtlvS8f-2BoQ-2FwHOgoTDZq8-2B1NRi98YX0kbntaAGloI1ei4=
9VijM3CHCLI9k-2F9zTPy4rXVJCHn8tk3wN7K2hsSoa6foeOOXVIwBzzhQHHxxoMr5XJ93KwYil=
1WzQ4BtT4LIyzdSFVvE-2F833FpumGpQjVLRn-2FjJV-2BICxTrjBq25T8AIVf8DmnhT0tZuMCL=
hX2SR4N1TT-2FFHhcUELK4FjfUJlDppCYq-2FpR5mjU7f1K7TfT6L1GoRG2JgMCEUJnkJizH-2B=
oKATNNQelnK-2BHluzUGOTmG-2BEyOm1neZqLhHBhN809KfNLuA93JI08qwvZL-2BXUWqVm-2BX=
MQje37z-2BN41hks37-2F7JBFd8k4SOk0pTMPDtnZN0yNoyx5a-2FbUyctOSME4dGi7R0VpnzlD=
F5BxY88ZLeiECqrEiOzMZUUjCS50isU3a4K9cXB3TGNa9U-3D" style=3D"border-radius: =
50%; background-color: #f2f1f7">
                                          <img src=3D"https://imgcdn.be.com=
.vn/cake/v1/email_icons/white_youtube_icon.png" alt=3D"cake-youtube.png" st=
yle=3D"display: block; width: 32px; height: 32px" />
                                        </a>
                                      </div>
                                      <div style=3D"display: inline-block; =
margin: 6px 0 6px 6px"><a href=3D"https://url5880.cake.vn/ls/click?upn=3DQl=
3mWgCiCIBDp9G6HsHIdNw13LDPn2Pyb5xEJ78St6N92ZfOS7-2B-2BLE7WOjfCRSLWoM0Z_vAiO=
uN9i8MoleFPwVqH5YYCugXxBNzJSaB9VKSwW7rGBTSzxTAJIn-2FtlvS8f-2BoQ-2FwHOgoTDZq=
8-2B1NRi98YX0kbntaAGloI1ei49VijM3CHCLI9k-2F9zTPy4rXVJCHn8tk3wN7K2hsSoa6foeO=
OXVIwBzzhQHHxxoMr5XJ93KwYil1WzQ4BtT4LIyzdSFVvE-2F833FpumGpQjVLRn-2FjJV-2BIC=
xTrjBq25T8AIVf8DmnhT0tZuMCLhX2SR4N1TT-2FFHhcUELK4FjfUJlDppCYq-2FpR5mjU7f1K7=
TfT6L1GoRG2JgMCEUJnkJizH-2BoKATNNQelnK-2BHluzUGOTmG-2BEyOm1neZqJqfaT3sCGNql=
5A-2FMRp-2BSSVDp4aBFNS0z7gPt8Q9b9h5N32ZovezADcZVI-2FHUfzpjpRg-2BCIvJLlHGsoS=
Gt5aZRuuGqGHhdUVl6ZL8Ohep2e8vfpGQWmnXrF8bTx3zugrfphFLtrFEoSol801Bx0hfw8-3D"=
 style=3D"border-radius: 50%; background-color: #f2f1f7">
                                          <img src=3D"https://imgcdn.be.com=
.vn/cake/v1/email_icons/white_instagram_icon.png" alt=3D"cake-instagram.png=
" style=3D"display: block; width: 32px; height: 32px" />
                                        </a>
                                      </div>
                                      <div style=3D"display: inline-block; =
margin: 6px 0 6px 6px"><a href=3D"https://url5880.cake.vn/ls/click?upn=3DQl=
3mWgCiCIBDp9G6HsHIdMORAxxbBnJI-2FIfO8WXvyATrYFRNDZRqb-2Bxl7pT9MsQp67-2F0UGX=
sMhlgPAIarNYI4A-3D-3DAHdT_vAiOuN9i8MoleFPwVqH5YYCugXxBNzJSaB9VKSwW7rGBTSzxT=
AJIn-2FtlvS8f-2BoQ-2FwHOgoTDZq8-2B1NRi98YX0kbntaAGloI1ei49VijM3CHCLI9k-2F9z=
TPy4rXVJCHn8tk3wN7K2hsSoa6foeOOXVIwBzzhQHHxxoMr5XJ93KwYil1WzQ4BtT4LIyzdSFVv=
E-2F833FpumGpQjVLRn-2FjJV-2BICxTrjBq25T8AIVf8DmnhT0tZuMCLhX2SR4N1TT-2FFHhcU=
ELK4FjfUJlDppCYq-2FpR5mjU7f1K7TfT6L1GoRG2JgMCEUJnkJizH-2BoKATNNQelnK-2BHluz=
UGOTmG-2BEyOm1neZqFOt0FMQsoRWdOZ3vEsSrDUGKpe-2F1pN-2Fdtb9UAqEwhETCF9voKOuHf=
bkD69lHmbaDkKo1eF6VLz6eBY1PPbq1wf6ibJrEP-2B-2FVsoNjnyYqjpIeMgxVddOKuPogexpo=
sBliHHkVFHsluWvybX8df9KwPg-3D" style=3D"border-radius: 50%; background-colo=
r: #f2f1f7">
                                          <img src=3D"https://imgcdn.be.com=
.vn/cake/v1/email_icons/white_tiktok_icon.png" alt=3D"cake-tiktok.png" styl=
e=3D"display: block; width: 32px; height: 32px" />
                                        </a>
                                      </div>
                                    </div>
                                    <div style=3D"line-height: 16px; paddin=
g: 0 0 8px; color: #666F80; font-size: 11px"> All rights reserved 2023 =C2=
=A9 Cake </div>
                                  </div>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
               =20
              </div>
             =20
            </td>
          </tr>
        </tbody>
      </table>
    </div>
   =20
  </div>
<img src=3D"https://url5880.cake.vn/wf/open?upn=3Dbfpdp0EVL-2FCb1AfFSdHQqsa=
Oe4NyRnkNtVMNralFH80so9PtIySYLMIm5JaVN5snMe9t5D-2FAFrhTuIRc-2FgscwBrU5jRvig=
TuhOrWtcZr8HUZmncYyQQSHl7td1yMiY8Ps7nssukWujgvLZ3CD5v79VNIb-2FaB-2B3wGWm-2F=
eZWWPV-2FAF7-2BLtZPmT16e-2Bj1VJTbNtoXX8VkXpaN4IYPZgU005y6jr-2BkoTaSHEjOpOi8=
-2FiMQNs8d52z9CzU0l7CcvKov-2F4fmeG-2FHgiysmHhi1dGc3PtIduG6LalYWeR93-2F8wBbD=
Ush7IWdSTQFAfz00OyGXtGlGXKsu381ieZKF4GIKKeN3E6Qn9jvFBMo-2FOOj-2FfBbGSkiVh8A=
dXMQwYC1OH3TI8aIp6S-2FEGG8KeZnxt53YwApKIvZ-2FsN3-2BDGP9qyd6HDePO3sHTfmzELlQ=
RNTlerGIYvAXI2hJujeEQZkcp7L540WcZScgOB2wqZ9Fe3eZoyJsWM-3D" alt=3D"" width=
=3D"1" height=3D"1" border=3D"0" style=3D"height:1px !important;width:1px !=
important;border-width:0 !important;margin-top:0 !important;margin-bottom:0=
 !important;margin-right:0 !important;margin-left:0 !important;padding-top:=
0 !important;padding-bottom:0 !important;padding-right:0 !important;padding=
-left:0 !important;"/></body>

</html>

@component('mail::message', ['data' => $data])
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::hero', [
        'logo' => 'user_white.png',
        'button_url' => $data['login_url'],
        'button_text' => 'Login Now',
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
        @slot('subject')
            Important: Log In Within 30 Days to Prevent Account Deletion
        @endslot
    @endcomponent
    @component('mail::content')
        <p>
            Your last login may have been over 6 months ago. Please log in to continue maintaining your account. Otherwise, we will delete your account.
        <p>
            Cheers,<br />
            The SenPrints Team
        </p>
    @endcomponent
@endcomponent

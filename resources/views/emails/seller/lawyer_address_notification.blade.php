@php
    $hashId = $data['logHashId'] ?? null;
    $sellerName = $data['name'] ?? null;
    $order = $data['order'] ?? null;
    $products = $data['products'] ?? [];
@endphp

@component('mail::message', ['data' => $data])
    @component('mail::hero', [
        'logo' => 'info_white.png',
        'hashId' => $hashId
        ])
        @slot('subject')
            Your order is not shipped due to lawyer address - Order #{{ data_get($order, 'order_number') }}
        @endslot
    @endcomponent

    @component('mail::content')
        <p>
            Dear {{ $sellerName }},
        </p>
        <p>
            We are writing to inform you that one of your orders cannot be shipped because the customer's address
            appears to be a lawyer's office address. As per our policy, we do not ship to legal addresses to avoid
            potential legal complications.
        </p>
        <h3 style="color: #333; margin-top: 20px; margin-bottom: 10px; text-align: left;">Customer Information:</h3>
        <div style="background-color: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; border-radius: 5px; margin-bottom: 20px; text-align: left;">
            <p style="margin: 5px 0;"><strong>Customer Email:</strong> {{ data_get($order, 'customer_email', 'N/A') }}</p>
            <p style="margin: 5px 0;"><strong>Address:</strong> {{ data_get($order, 'address', 'N/A') }}</p>
            <p style="margin: 5px 0;"><strong>City:</strong> {{ data_get($order, 'city', 'N/A') }}</p>
            <p style="margin: 5px 0;"><strong>State:</strong> {{ data_get($order, 'state', 'N/A') }}</p>
            <p style="margin: 5px 0;"><strong>Postcode:</strong> {{ data_get($order, 'postcode', 'N/A') }}</p>
            <p style="margin: 5px 0;"><strong>Country:</strong> {{ data_get($order, 'country', 'N/A') }}</p>
            @if(data_get($order, 'house_number'))
                <p style="margin: 5px 0;"><strong>House Number:</strong> {{ data_get($order, 'house_number') }}</p>
            @endif
        </div>
        <h3 style="color: #333; margin-top: 20px; margin-bottom: 10px; text-align: left;">Action Required:</h3>
        <p>
            Please review and consider deactivating the following campaigns to prevent similar issues in the future:
        </p>
        @if($products && count($products) > 0)
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-bottom: 20px; line-height: 20px; text-align: left;">
                <ul style="margin: 0; padding-left: 10px;">
                    @foreach($products as $product)
                        <li style="margin-bottom: 8px;">
                            <a href="https://{{ data_get($order, 'store_domain') }}{{ data_get($product, 'product_url') }}" target="_blank"
                               style="color: #007bff; text-decoration: none;">
                                {{ data_get($product, 'campaign_title') }}
                            </a>
                        </li>
                    @endforeach
                </ul>
            </div>
        @endif
        <p>
            Best regards,<br/>
            SenPrints Operations Team
        </p>
    @endcomponent
@endcomponent

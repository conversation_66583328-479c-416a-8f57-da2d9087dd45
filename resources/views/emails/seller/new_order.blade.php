@php
    $baseUrl = $data['base_url'] ?? null;
    $hashId = $data['logHashId'] ?? null;
    $isArtist = !empty($data['is_artist']);
@endphp
@component('mail::message', ['data' => $data])
    @component('mail::hero', ['logo' => 'shopping-cart_white.png'])
        @slot('subject')
            It’s official! Your item has been sold
        @endslot
    @endcomponent
    @component('mail::spacer')
    @endcomponent
    @component('mail::content')
        <p class="tl">
            Hey {{ $data['name'] }},
        </p>
        <p class="tl">
            Your design is brilliant. We just sold there campaigns:<br />
            Thank you for being with SenPrints.
            @if (!$isArtist)
            Explore more at <a href="{{ clickTrackingMail($baseUrl, $data['store_url'], $hashId) }}">{{ $store_info['name'] }}</a> to navigate your business.
            @endif
            <br />Contact us if you have any questions. We’re always happy to answer!
        </p>
        <p class="tl">
            Cheers,<br />
            The SenPrints Team
        </p>
    @endcomponent

    @component('mail::component_header')
        @slot('title')
            Products
        @endslot
    @endcomponent

    {{-- start loop products --}}
    @php
    $order = Arr::get($data, 'order');
    $currency = Arr::get($data, 'currency');
    @endphp
    @component('mail::products_new', [
        'products' => $products,
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
    ])
    @endcomponent
    @php
    $sub_total = Arr::get($data, 'order.total_product_amount');
    $shipping_cost = Arr::get($data, 'order.total_shipping_amount');
    $total_discount = Arr::get($data, 'order.total_discount');
    $insurance_fee = Arr::get($data, 'order.insurance_fee');
    $order_discount_code = Arr::get($data, 'order.discount_code');
    $tax = Arr::get($data, 'order.total_tax_amount');
    $total_amount = Arr::get($data, 'order.total_amount');
    $payment_discount = Arr::get($data, 'order.payment_discount');
    $tip_amount = Arr::get($data, 'order.tip_amount');
    @endphp
    @component('mail::hero_receipt', [
        'order_discount_code' => $order_discount_code,
        'promotion_title' => '',
        'sub_total' => $sub_total,
        'shipping_cost' => $shipping_cost,
        'insurance_fee' => $insurance_fee,
        'total_discount' => $total_discount,
        'tax' => $tax,
        'tip_amount' => $tip_amount,
        'total_amount' => $total_amount,
        'payment_discount' => $payment_discount
    ])
    @endcomponent
@endcomponent

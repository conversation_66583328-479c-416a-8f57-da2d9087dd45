@component('mail::message', ['data' => $data])
    @php
        $sellerName = Arr::get($data,'sellerName');
        $subject = Arr::get($data, 'subject');
        $time = Arr::get($data, 'time');
        $timeInWord = '';
        if($time == 1){
            $timeInWord = 'once';
        }elseif($time == 2){
            $timeInWord = 'twice';
        }elseif($time >= 3){
            $timeInWord = 'thrice';
        }
    @endphp
    @component('mail::hero', [
        'logo' => 'info_white.png',
        'subject' => $subject,
        ])
    @endcomponent
    @component('mail::content')
        <p class="tl">
            Dear {{ $sellerName}},
        </p>
        <p class="tc mb_0">
        You have violated our DMCA policies {{$time}} times. Your account has been flagged. If you violate our policies more than twice, we have to block your account.
        </p>
        <br/>
        <p class="tc mb_0">    
        If you have any questions, please contact our customer support.
        </p>
        <br />
        <br />
        <p class="tl">
            Cheers,<br />
            The SenPrints Team
        </p>
    @endcomponent
@endcomponent

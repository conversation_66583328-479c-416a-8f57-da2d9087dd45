@component('mail::message', ['data' => $data])
    @component('mail::hero', ['logo' => 'shopping-cart_white.png'])
        @slot('subject')
            It’s official! Your item has been sold
        @endslot
    @endcomponent
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::button', [
        'url' => $data['order_detail_url'],
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
        View Order
    @endcomponent
    @component('mail::spacer')
    @endcomponent
    @component('mail::content')
        <p class="tl">
            Hey {{ $data['name'] }},
        </p>
        <p class="tl">
            Your design is brilliant. We just sold there campaigns:<br />
            @foreach ($data['campaigns'] as $campaign)
                <a href="{{ clickTrackingMail($baseUrl, $campaign['campaign_url'], $hashId) }}">{{ $campaign['campaign_name'] }}</a><br />
            @endforeach
            Thank you for being with SenPrints. Explore more at <a
                href="{{ clickTrackingMail($baseUrl, $data['store_url'], $hashId) }}">{{ $store_info['name'] }}</a> to navigate your business.<br />
            Contact us if you have any questions. We’re always happy to answer!
        </p>
        <p class="tl">
            Cheers,<br />
            The SenPrints Team
        </p>
    @endcomponent
@endcomponent

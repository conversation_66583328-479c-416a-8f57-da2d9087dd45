@component('mail::message', ['data' => $data])
    @component('mail::hero', ['logo' => 'key_white.png'])
        @slot('subject')
            Password Reset
        @endslot
    @endcomponent
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::button', [
            'url' => $data['reset_password_url'],
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
        ])
        Reset Password
    @endcomponent
    @component('mail::spacer')
    @endcomponent
    @component('mail::content')
        <p>
            We have received your request to reset password. <br />
            Click Reset Password to get your new password. This link will
            expire in 5 minutes.<br />
            If you didn’t make this request, please ignore this email.
        </p>
    @endcomponent
@endcomponent

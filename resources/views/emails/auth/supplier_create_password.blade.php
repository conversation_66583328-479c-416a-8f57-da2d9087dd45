@component('mail::message', ['data' => $data])
    @component('mail::hero', ['logo' => 'key_white.png'])
        @slot('subject')
            Your Supplier Account
        @endslot
    @endcomponent
    @component('mail::content')
        <p>
            <span style="display: inline-block;">
                <span style="font-size: 24px; font-weight: 700; display: inline-block; margin-bottom: 0.5rem;">Account</span><br />
                <strong style="display: inline-block; font-size: 16px; background-color: #fecaca; padding: 0.5rem 1rem; box-sizing: border-box; border-radius: 4px;">{{ $data['email'] }}</strong>
                <br />
                <br />
                <span style="font-size: 24px; font-weight: 700; display: inline-block; margin-bottom: 0.5rem;">Password</span><br />
                <strong style="display: inline-block; font-size: 16px; background-color: #fecaca; padding: 0.5rem 1rem; box-sizing: border-box; border-radius: 4px;">{{ $data['password'] }}</strong>
                <br />
                <br />
                We've created a secure, temporary password for you to use for your initial login.
                <br />
                Please change this password immediately after logging in for the first time to ensure your account's security.
            </span><br />
        </p>
    @endcomponent
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::button', [
            'url' => $data['redirect_url'],
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
        ])
        Go Login
    @endcomponent
@endcomponent

@php
    use App\Services\ConfirmEmailService;

    $baseUrl = $data['base_url'] ?? null;
    $hashId = $data['logHashId'] ?? null;
    $sellerId = $data['seller_id'] ?? null;
    $url =  $hashId ? ConfirmEmailService::getUrl($hashId, $sellerId) : null;
@endphp
@component('mail::message', ['data' => $data])
    @component('mail::hero', ['logo' => 'key_white.png'])
        @slot('subject')
            Verify Your Email
        @endslot
    @endcomponent
    @component('mail::content')
        <p>
            Click Confirm Email to confirm your email. This link will
            expire in 48 hours.
        </p>
    @endcomponent
    @component('mail::button', [
            'url' => $url,
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
        ])
        Confirm Email
    @endcomponent
@endcomponent

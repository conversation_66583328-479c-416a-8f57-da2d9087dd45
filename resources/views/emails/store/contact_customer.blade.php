@component('mail::message', ['data' => $data])
    @component('mail::hero', ['logo' => 'info_white.png'])
        @slot('subject')
            Thank you for contacting us
        @endslot
    @endcomponent

    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::content')
        <p class="new_tl">
            Hi {{ $data['name'] }},
        </p>
        <p class="new_tl">
            Thanks so much for reaching out! This auto-reply is just to let you know that we have received your email and will get back to you with a (human) response within 24-48 business hours.<br />
            Please read the articles below if you need any additional information.<br />
        </p>
        <p class="new_tl" style="padding-left: 10px;">
            <a href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/page/return-policy', $hashId) }}">Return Policy</a><br />
            <a href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/page/shipping-policy', $hashId) }}">Shipping Policy</a><br />
            <a href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/page/terms-of-service', $hashId) }}">Terms & Conditions</a><br />
            <a href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/page/privacy', $hashId) }}">Privacy Policy</a><br />
            <a href="{{ clickTrackingMail($baseUrl, $data['base_url'] . '/page/dmca', $hashId) }}">DMCA</a><br />
        </p>
        <p class="new_tl">
            If you have any additional information that you think will help us to assist you, please feel free to reply to this email. We look forward to chatting soon!
        </p>
        <p class="new_tl">
            Regards,<br />
            The {{ $data['store_info']['name'] }} Team
        </p>
    @endcomponent

    @component('mail::spacer')
    @endcomponent
@endcomponent

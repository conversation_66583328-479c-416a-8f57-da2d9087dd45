@component('mail::message', ['data' => $data])
    @component('mail::hero', ['logo' => 'info_white.png'])
        @slot('subject')
            You received a message!
        @endslot
    @endcomponent
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::content')
        <p class="new_tl new_bg-code">
            {{ $data['message'] }}
        </p>
        <p class="new_tl">
            Name: {{ $data['name'] }}<br />
            Email: {{ $data['email'] }}<br />
            Order Number: {{ $data['order_number'] }}<br />
            Store Domain: {{ $data['store_info']['domain'] }} <br>
        </p>
        <p>
            @foreach($data['attached_files'] as $count => $attached_file)
                <a href="{{ clickTrackingMail($baseUrl, $attached_file, $hashId) }}">Attached File {{$count+1}}.{{pathinfo($attached_file, PATHINFO_EXTENSION)}}</a><br>
            @endforeach
        </p>
    @endcomponent

    @component('mail::spacer')
    @endcomponent
@endcomponent

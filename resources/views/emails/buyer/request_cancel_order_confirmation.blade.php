@component('mail::message', ['data' => $data])
    @component('mail::hero', [
            'subject' => 'Confirm your request cancel order!',
        ])
    @endcomponent
    @php
        $storeUrl = Arr::get($data, 'base_url');
        $storeUrl = $storeUrl[-1] === '/' ? $storeUrl : $storeUrl . '/';
        $requestToken = Arr::get($data, 'token');
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::content')
        <p class="new_tc">
            To confirm order cancellation, please click the button below to confirm it.
        </p>
        <table role="presentation" class="new_ebtn new_tc" align="center" border="0" cellspacing="0" cellpadding="0" style="margin-bottom: 24px;">
            <tbody>
            <tr>
                <td class="new_accent_b">
                    <a href="{{ clickTrackingMail($baseUrl, $storeUrl . "api/public/order/confirm/cancel/" . $requestToken, $hashId) }}">
                        <span>Confirm cancel</span>
                    </a>
                </td>
            </tr>
            </tbody>
        </table>
    @endcomponent
    @php
    $supportEmail = Arr::get($data, 'store_info.email');
    $storeName = Arr::get($data, 'store_info.name');
    @endphp
    @component('mail::content')
        <p class="new_tc">
            If you have any questions, don’t hesitate to contact us at
            {{ $supportEmail }}<br />
            Thank you for shopping at {{ $storeName }}!<br />
            See you soon,<br />
        </p>
        <p class="new_tc">
            The {{ $storeName }} Team<br />
        </p>
    @endcomponent
@endcomponent

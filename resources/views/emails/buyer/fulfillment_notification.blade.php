@php
    $baseUrl = $data['base_url'] ?? null;
    $hashId = $data['logHashId'] ?? null;
    $currency = $data['currency'];
@endphp
@component('mail::message', ['data' => $data])
    @php
    $title = 'Your order has been shipped!';
    $message = 'The products are on the way to you. Please wait <3';
    $icon = 'heart_white.png';
    @endphp
    @component('mail::hero', [
        'logo' => 'location_white.png',
        'subject' => 'Shipping Confirmation',
        'button_text' => 'Order Detail',
        'button_url' => $data['order_url'],
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
        @slot('message')
            Hi {{ $data['name'] }}, Great news!<br />
        @endslot
    @endcomponent

    @component('mail::content')
        @component('mail::warning_ship_late')
        @endcomponent
        <p class="new_tc">
            Your recent order #{{ isset($data['order_number']) ? $data['order_number'] : '' }} is on the way.<br />
            You can find the shipping details below.<br />
        </p>
    @endcomponent

    @php
    $fulfillments = $data['fulfillments'] ?? [
        'unfulfilled' => [],
        'fulfilled' => []
    ];
    $unfulfilled = $fulfillments['unfulfilled'] ?? [];
    $fulfilled =  $fulfillments['fulfilled'] ?? [];
    @endphp

    @if (isset($fulfilled) && count($fulfilled) > 0)
        @component('mail::component_header', ['title' => 'Fulfilled Products'])
        @endcomponent
        @foreach ($fulfilled as $item)
            @php
                $fulfilledProducts = collect($item['items']);
                $fulfilledInCombo = $fulfilledProducts->filter(static function($product) {
                    return !is_null($product['combo_id']);
                })->all();
                $fulfilledNotInCombo = $fulfilledProducts->filter(static function($product) {
                    return is_null($product['combo_id']);
                })->all();
            @endphp
            @component('mail::products', [
                'products' => $fulfilledNotInCombo,
                'domain' => $data['store_info']['domain'],
                'baseUrl' => $baseUrl,
                'hashId' => $hashId
                ])
            @endcomponent
            @if(!empty($fulfilledInCombo))
                @component('mail::products_combo', [
                    'comboSets' => getComboSets($fulfilledInCombo, $currency),
                    'products' => $fulfilledInCombo,
                    'currency' => $currency,
                    'domain' => $data['store_info']['domain'],
                    'baseUrl' => $baseUrl,
                    'hashId' => $hashId
                ])
                @endcomponent
            @endif

            @component('mail::spacer')
            @endcomponent

            @component('mail::content')
                <div class="new_tc">
                    <p class="new_bg-code">
                        Shipping Carrier: {{ $item['shipping_carrier'] }}<br />
                        @if(empty($item['tracking_url']))
                            Tracking Code: {{ $item['tracking_code'] }}
                        @else
                            Tracking Code: <a href="{{ $item['tracking_url']  }}" target="_blank">{{ $item['tracking_code'] }}</a>
                        @endif
                    </p>
                </div>
            @endcomponent
        @endforeach
    @endif

    @if (isset($unfulfilled) && count($unfulfilled) > 0)
        @php
            $productsUnfulfilled = collect($unfulfilled);
            $unfulfilledNotInCombo = $productsUnfulfilled->filter(static function($product) {
                return is_null($product['combo_id']);
            })->all();
            $unfulfilledInCombo = $productsUnfulfilled->filter(static function($product) {
                return !is_null($product['combo_id']);
            })->all();
        @endphp

        @component('mail::component_header', ['title' => 'Unfulfilled Products'])
        @endcomponent

        @component('mail::products', [
            'products' => $unfulfilledNotInCombo,
            'domain' => $data['store_info']['domain'],
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
            ])
        @endcomponent
        @if(!empty($unfulfilledInCombo))
            @component('mail::products_combo', [
                'comboSets' => getComboSets($unfulfilledInCombo, $currency),
                'products' => $unfulfilledInCombo,
                'currency' => $currency,
                'domain' => $data['store_info']['domain'],
                'baseUrl' => $baseUrl,
                'hashId' => $hashId
            ])
            @endcomponent
        @endif

        @component('mail::spacer')
        @endcomponent
    @endif
    @php
        $supportEmail = Arr::get($data, 'store_info.email');
        $storeName = Arr::get($data, 'store_info.name');
    @endphp
    @component('mail::content')
        <p class="new_tc">
            If you have any questions, don’t hesitate to contact us at
            {{ $supportEmail }}<br />
        </p>
        <p class="new_tc">
            Thank you,<br />
            The {{ $storeName }} Team<br />
        </p>
    @endcomponent
@endcomponent

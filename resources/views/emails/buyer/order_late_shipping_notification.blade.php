@php
    $baseUrl = $data['base_url'] ?? null;
    $hashId = $data['logHashId'] ?? null;
    $customerName = $data['name'] ?? '';
    $customerEmail = $data['email'] ?? '';
    $orderNumber = Arr::get($data, 'order.order_number');
    $supportEmail = Arr::get($data, 'store_info.email');
    $storeName = Arr::get($data, 'store_info.name');
    $delayedDays = Arr::get($data, 'days_late');
    $pageContactUs = clickTrackingMail($baseUrl, $baseUrl . '/page/contact-us?order_number=' . $orderNumber . '&customer_name=' . urlencode($customerName) . '&customer_email=' . $customerEmail, $hashId);
@endphp
@component('mail::message', ['data' => $data])
    @component('mail::hero', [
        'logo' => 'shopping-cart_white.png',
        'subject' => 'We\'re sorry for the delay – Let us assist you #' . $orderNumber,
        'button_text' => 'View Order',
        'button_url' => Arr::get($data, 'order.status_url'),
        'page_contact_us' => $pageContactUs,
        'baseUrl' => $baseUrl,
        'hashId' => $hashId,
        'template' => 'order_late_shipping_notification',
        ])
        @slot('message')
            Dear {{ $customerName }}!<br/>
        @endslot
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            Thank you for choosing {{ $storeName }}. We’re committed to ensuring your order arrives smoothly. <br/>
            If your package hasn’t been delivered within {{ $delayedDays }} days from the tracking information provided,
            please reach out to our support team immediately at {{ $supportEmail }}.<br/>
            We’ll address any concerns promptly to ensure your satisfaction.<br/>
        </p>
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            Thank you for your patience and trust in us.<br/>
            If you have any questions, don’t hesitate to contact us at
            {{ $supportEmail }}<br/>
        </p>
        <p class="new_tc">
            Warm regards,<br/>
            The {{ $storeName }} Team<br/>
        </p>
    @endcomponent
@endcomponent

@component('mail::message', ['data' => $data])
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp

    @component('mail::content')
        @if (!empty($data['customer_name']))
            <p style="text-align: left; margin-bottom: 0.25rem">Hey {{ $data['customer_name'] }},</p>
        @else
            <p style="text-align: left; margin-bottom: 0.25rem">Hey,</p>
        @endif
        <p style="text-align: left">We hope you’re enjoying your new items!</p>
    @endcomponent

    @component('mail::products', [
        'products' => $data['products'],
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
        <table role="presentation" class="new_ebtn new_tc new_py new_mt" align="center" border="0" cellspacing="0" cellpadding="0">
            <tbody>
            <tr>
                <td class="new_accent_b">
                    <a href="{{ clickTrackingMail($baseUrl, $data['review_url'], $hashId) }}" target="_blank">WRITE A REVIEW</a>
                </td>
            </tr>
            </tbody>
        </table>
        @if ($data['has_review_coupon'])
        <table role="presentation" align="center" border="0" cellspacing="0" cellpadding="0">
            <tbody>
            <tr>
                <td>
                    <small style="font-size: 14px">* You will get a discount coupon for your next shopping.</small>
                </td>
            </tr>
            </tbody>
        </table>
        @endif
    @endcomponent

    @component('mail::content')
        <p style="text-align: left; margin-top: 1.5rem">Like many of our customers, you may have found customer reviews of our products useful in making your choices. We invite you to add your reviews to the mix.</p>
        <p style="text-align: left; margin-bottom: 0.25rem">Click <a href="{{ clickTrackingMail($baseUrl, $data['review_url'], $hashId) }}" target="_blank" style="font-weight: 600; color: #ff4081">WRITE A REVIEW</a> button to describe what you think about your recent purchase. Any information you believe would be helpful.</p>
    @endcomponent

    @if (!empty($data['related_products']))
        @component('mail::related_products', [
            'related_products' => $data['related_products'],
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
            ])
            @slot('header')
                @component('mail::component_header')
                    @slot('title')
                        You may also like
                    @endslot
                @endcomponent
            @endslot
        @endcomponent
    @endif

    @component('mail::content')
        <p style="text-align: left; margin-bottom: 0.25rem">Thank you for your time.</p>
        <p style="text-align: left; margin-bottom: 1rem">Sincerely,</p>
        <p style="text-align: left; font-weight: 600; margin-bottom: 0.5rem">The {{ $data['store_name'] }} Team</p>
    @endcomponent
@endcomponent

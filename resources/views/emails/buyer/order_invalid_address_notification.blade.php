@php
    use App\Enums\CountryEnum;
    use App\Models\SystemLocation;
    use App\Services\MailService;
    use Illuminate\Support\Arr;
    use App\Models\SystemConfig;

    $name            = Arr::get($data, 'name', '');
    $supportEmail    = Arr::get($data, 'store_info.email');
    $storeName       = Arr::get($data, 'store_info.name');
    $address_1       = Arr::get($data, 'order.address') ?? '';
    $address_2       = Arr::get($data, 'order.address_2') ?? '';
    $state           = Arr::get($data, 'order.state') ?? '';
    $city            = Arr::get($data, 'order.city') ?? '';
    $postcode        = Arr::get($data, 'order.postcode') ?? '';
    $countryCode     = Arr::get($data, 'order.country') ?? '';
    $baseUrl         = Arr::get($data, 'base_url');
    $hashId          = Arr::get($data, 'logHashId');
    $token           = Arr::get($data, 'token');
    $orderToken      = Arr::get($data, 'order.token');
    $record          = SystemLocation::query()->where('code', $countryCode)->first();
    $country         = $record ? $record->name : $countryCode;
    $config          = SystemConfig::getCheckoutFormConfig($countryCode);
    if (!empty($state) && !empty($countryCode)) {
        $state = CountryEnum::toStateName($countryCode, $state);
    }
    $default         = [
            [
                'label' => 'Full name',
                'value' => $name,
            ],
            [
                'label' => 'Street address',
                'value' => $address_1,
            ],
            [
                'label' => 'Apt / Suite / Other (optional)',
                'value' => $address_2,
            ],
            [
                'label' => 'Zip code',
                'value' => $postcode,
                'width' => 50,
            ],
            [
                'label' => 'City',
                'value' => $city,
                'width' => 50,
            ],
            [
                'label' => 'State',
                'value' => $state,
            ],
            [
                'label' => 'Country',
                'value' => $country,
            ]
        ];

    $confirmUrl = $baseUrl . '/api/public/email/confirm-address?token=' . $token . '&order_token=' . $orderToken;
    if ($hashId) {
        $confirmUrl .= '&hash_id=' . $hashId;
    }
    $editUrl = clickTrackingMail($baseUrl, $baseUrl . '/order/status/' . $orderToken . '?open_edit=true', $hashId);

    if (!empty($config)) {
        try{
            if (isset($config['mailboxNumber'])) {
                $mailbox = Arr::get($data, 'order.mailbox_number', '');
            }
            if (isset($config['houseNumber'])) {
                $houseNumber = Arr::get($data, 'order.house_number', '');
            }
            $formItems = [
                [
                    'label' => 'Full name',
                    'value' => $name,
                ]
            ];
            $position = $config['position'];
            foreach ($position as $key) {
                switch ($key) {
                    case 'address':
                        $formItems[] = [
                            'label' => 'Street address',
                            'value' => $address_1,
                            'width' => MailService::calculateWidth($config, 'address_1')
                        ];
                        $formItems[] = [
                            'label' => 'Apt / Suite / Other (optional)',
                            'value' => $address_2,
                            'width' => MailService::calculateWidth($config, 'address_2')
                        ];
                        break;
                    case 'city':
                        $formItems[] = [
                            'label' => MailService::getLabel($config, 'city', 'City'),
                            'value' => $city,
                            'width' => MailService::calculateWidth($config, 'city')
                        ];
                        break;
                    case 'postCode':
                        $formItems[] = [
                            'label' => MailService::getLabel($config, 'postCode', 'Zip code'),
                            'value' => $postcode,
                            'width' => MailService::calculateWidth($config, 'postCode')
                        ];
                        break;
                    case 'state':
                        $formItems[] = [
                            'label' => MailService::getLabel($config, 'state', 'State'),
                            'value' => $state,
                            'width' => MailService::calculateWidth($config, 'state')
                        ];
                        break;
                    case 'mailboxNumber':
                        $formItems[] = [
                            'label' => \App\Providers\FulfillAPI\AbstractModel::MAILBOX_NUMBER_TEXT,
                            'value' => $mailbox ?? '',
                            'width' => MailService::calculateWidth($config, 'mailboxNumber')
                        ];
                        break;
                    case 'houseNumber':
                        $formItems[] = [
                            'label' => \App\Providers\FulfillAPI\AbstractModel::HOUSE_NUMBER_TEXT,
                            'value' => $houseNumber ?? '',
                            'width' => MailService::calculateWidth($config, 'houseNumber')
                        ];
                        break;
                }
            }
            $formItems[] = ['label' => 'Country', 'value' => $country];
        } catch (Exception $e) {
            logToDiscord('Invalid config ' . $e->getMessage(), 'fetch_currency');
            $formItems = $default;
        }
    } else {
        $formItems = $default;
    }
@endphp

@component('mail::message', ['data' => $data])
    @component('mail::hero', [
        'logo' => 'shopping-cart_white.png',
        'subject' => $data['subject'],
        'button_text' => 'View Order',
        'button_url' => Arr::get($data, 'order.status_url', ''),
        'baseUrl' => $data['base_url'] ?? null,
        'hashId' => $data['logHashId'] ?? null,
        'template' => 'order_invalid_address_notification',
    ])
        @slot('message')
            Hi {{ $name }}, Great news!<br/>
        @endslot
    @endcomponent
    @component('mail::content')
        <p class="new_tc" style="font-size: 1.3rem; line-height: 1.9rem; margin-bottom: 4rem;">
            We could not verify the exact address you provided.
            <br/>
            Please edit or confirm your address.
            <br/>
            Order with incorrect addresses may be delayed or lost.
        </p>
    @endcomponent
    @component('mail::content')
        <table role="presentation" class="new_ebtn new_tc" align="center" border="0"
               cellspacing="0" cellpadding="0" style="margin-left: 0.75rem; display: inline-block;">
            <tbody>
            <tr>
                <table style="width: 100%; border-collapse: collapse; margin-bottom: 1.5rem;">
                    <tr style="background-color: #f2f2f2; height: 40px;">
                        <th style="border: 1px solid #ddd; font-size: 1.1rem;">Address entered</th>
                        <th style="border: 1px solid #ddd; font-size: 1.1rem;">Matching address</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px; width: 50%;">
                            <div
                                style="text-align: left; width: fit-content; margin: auto; text-transform: none; font-size: 1.3rem; line-height: 1.9rem;">
                                {{ $name }}<br/>
                                {{ $address_1 }}<br/>
                                @if(!empty($address_2))
                                    {{ $address_2 }}<br/>
                                @endif
                                {{ $city }}, {{ $state }}<br/>
                                {{ $country }}, {{ $postcode }}<br/>
                                @isset($mailbox)
                                    Mailbox number: {{ $mailbox }}<br/>
                                @endisset
                                @isset($houseNumber)
                                    House number: {{ $houseNumber }}<br/>
                                @endisset
                            </div>
                        </td>
                        <td style="border: 1px solid #ddd; padding: 0.5rem 0.8rem; font-size: 0.8rem;">
                            <div
                                style="text-align: left; text-transform: none; width: fit-content; margin: auto; display: flex; row-gap: 0.4rem; flex-wrap: wrap;">
                                @foreach($formItems as $item)
                                    <div
                                        style="width: {{ $item['width'] ?? 100 }}%; padding-left: 0.3rem; padding-right: 0.3rem;">
                                        {{ $item['label'] }}<br/>
                                        <input type="text" disabled value="{{ $item['value'] }}"
                                               style="padding: 10px; width: 100%; box-sizing: border-box; font-size: 0.8rem;">
                                    </div>
                                @endforeach
                            </div>
                        </td>
                    </tr>
                </table>
            </tr>
            </tbody>
        </table>
    @endcomponent
    @component('mail::content')
        <table role="presentation" class="new_ebtn new_tc" align="center" border="0"
               cellspacing="0" cellpadding="0" style="margin-left: 0.75rem; display: inline-block;">
            <tbody>
            <tr>
                <td class="new_accent_b" style="background-color: #838383; width: 10rem;">
                    <a href="{{ $editUrl }}" rel="noopener" target="_blank">
                        Edit Address
                    </a>
                </td>
            </tr>
            </tbody>
        </table>
        <br/>
        <table role="presentation" class="new_ebtn new_tc" align="center" border="0"
               cellspacing="0" cellpadding="0"
               style="margin-left: 0.75rem; display: inline-block; margin-top: 1.5rem; margin-bottom: 4rem;">
            <tbody>
            <tr>
                <td class="new_accent_b" style="background-color: #838383; width: 10rem;">
                    <a href="{{ $confirmUrl }}" target="_blank" rel="noopener">
                        Confirm
                    </a>
                </td>
            </tr>
            </tbody>
        </table>
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            We appreciate your understanding and cooperation in this matter.
            <br/>
            If you have any questions, don't hesitate to contact us at
            <strong><a href="mailto:{{ $supportEmail }}" rel="noopener">{{ $supportEmail }}</a></strong>
        </p>
        <p class="new_tc">
            Thank you,<br/>
            The {{ $storeName }} Team<br/>
        </p>
    @endcomponent
@endcomponent

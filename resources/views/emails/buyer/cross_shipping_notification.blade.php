@component('mail::message', ['data' => $data])
    @php
    $title = 'Your order has been shipped!';
    $message = 'The products are on the way to you. Please wait <3';
    $icon = 'heart_white.png';
    @endphp
    @component('mail::hero', [
        'logo' => 'location_white.png',
        'subject' => 'Shipping Notification',
        'button_text' => 'Order Detail',
        'button_url' => $data['order_url'],
        'baseUrl' => $data['base_url'] ?? null,
        'hashId' => $data['logHashId'] ?? null
        ])
        @slot('message')
            Hi {{ $data['name'] }}, Great news!<br />
        @endslot
    @endcomponent

    @component('mail::content')
        @component('mail::warning_ship_late')
        @endcomponent
        <p class="new_tc">
            Due to stock shortages, your order may be processed in two factories. This necessitated assignment to a second factory, potentially causing a slight delay in processing and shipping<br />
        </p>
    @endcomponent

    @component('mail::component_header', ['title' => 'Products'])
    @endcomponent

    @component('mail::products', [
        'products' => data_get($data, 'products'),
        'domain' => data_get($data, 'store_info.domain'),
        'baseUrl' => $data['base_url'] ?? null,
        'hashId' => $data['logHashId'] ?? null
        ])
    @endcomponent

    @component('mail::spacer')
    @endcomponent

    @component('mail::content')
        <p class="new_tc">
            If you have any questions, don’t hesitate to contact us at
            {{ data_get($data, 'store_info.email') }}<br />
        </p>
        <p class="new_tc">
            Thank you,<br />
            The {{ data_get($data, 'store_info.name') }} Team<br />
        </p>
    @endcomponent
@endcomponent

@php
    $baseUrl = $data['base_url'] ?? null;
    $hashId = $data['logHashId'] ?? null;
    $customerName = $data['name'] ?? '';
    $customerEmail = $data['email'] ?? '';
    $orderNumber = Arr::get($data, 'order.order_number');
    $supportEmail = Arr::get($data, 'store_info.email');
    $storeName = Arr::get($data, 'store_info.name');
    $pageContactUs = clickTrackingMail($baseUrl, $baseUrl . '/page/contact-us?order_number=' . $orderNumber . '&customer_name=' . urlencode($customerName) . '&customer_email=' . $customerEmail, $hashId);
@endphp
@component('mail::message', ['data' => $data])
    @component('mail::hero', [
        'logo' => 'shopping-cart_white.png',
        'subject' => 'Shipping Notification #' . $orderNumber,
        'button_text' => 'View Order',
        'button_url' => $data['order']['status_url'],
        'page_contact_us' => $pageContactUs,
        'baseUrl' => $baseUrl,
        'hashId' => $hashId,
        'template' => 'order_ship_late_notification',
        ])
        @slot('message')
            Hi {{ $data['name'] }}, Great news!<br/>
        @endslot
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            Due to stock shortages during this holiday season, your order maybe a slight delay in processing and shipping in a next few days lately.
            We estimate that tracking information will be keep updating available from May.13 to May.17<br/>
            Please rest assured that we are working diligently to expedite the process and get your order on track as quickly as possible.
            We understand the importance of timely delivery and are committed to resolving this situation promptly. <br/>
        </p>
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            We appreciate your understanding and cooperation in this matter.<br/>
            If you have any questions, don’t hesitate to contact us at
            {{ $supportEmail }}<br/>
        </p>
        <p class="new_tc">
            Thank you,<br/>
            The {{ $storeName }} Team<br/>
        </p>
    @endcomponent
@endcomponent

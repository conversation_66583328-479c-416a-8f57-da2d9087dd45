@component('mail::message', ['data' => $data])
    @php
        $orderNumber = Arr::get($data, 'order.order_number');
        $customerName = $data['name'] ?? '';
        $customerEmail = $data['email'] ?? '';
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
        $pageContactUs = clickTrackingMail($baseUrl,
                                       $baseUrl . '/page/contact-us?order_number=' . $orderNumber
                                       . '&customer_name=' . urlencode($customerName)
                                       . '&customer_email=' . $customerEmail
                                       , $hashId);
    @endphp
    @component('mail::hero', [
            'subject' => "We have received your cancellation request of order #{$orderNumber}.",
            'page_contact_us' => $pageContactUs,
        ])
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            We have received your cancellation request and are processing it within 24 hours. Please allow 1-2 weeks for the refund to appear on your bank statement
        </p>
    @endcomponent
    @php
        $supportEmail = Arr::get($data, 'store_info.email');
        $storeName = Arr::get($data, 'store_info.name');
    @endphp
    @component('mail::content')
        <p class="new_tc">
            If you have any questions, don’t hesitate to contact us at
            {{ $supportEmail }}<br />
            Thank you for shopping at {{ $storeName }}!<br />
            See you soon,<br />
        </p>
        <p class="new_tc">
            The {{ $storeName }} Team<br />
        </p>
    @endcomponent
@endcomponent

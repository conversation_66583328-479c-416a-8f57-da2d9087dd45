@component('mail::message', ['data' => $data])
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
        $currency = $data['currency'] ?? null;

        $productsCollection = collect($products);
        $productsInCombo = $productsCollection->filter(static function($product) {
            return !is_null($product['combo_id']);
        })->all();

        $productsNotInCombo = $productsCollection->filter(static function($product) {
            return is_null($product['combo_id']);
        })->all();
    @endphp
    @component('mail::hero', [
        'subject' => $subject,
        'message' => 'You still left items in your shopping bag',
        'button_url' => $data['cart_url'],
        'button_text' => 'Take me back to my cart',
        'logo' => 'heart_white.png',
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
    @endcomponent

    @component('mail::content')
        <div style="width: 608px; overflow-wrap: break-word;">
            <p>
                {!! $message !!}
            </p>
        </div>
    @endcomponent

    @if ($promotion_title && $coupon)
        @component('mail::content')
            <fieldset style="border: 2px dashed #f4478e">
                <legend style="font-weight: bold;"></legend>
                <div style="text-align: center;">
                    <p style="margin-top: 20px;">
                        {{ $promotion_title }}
                    </p>
                    <p style="font-weight: bold;">
                        {{ $coupon }}
                    </p>
                </div>
            </fieldset>
        @endcomponent
    @endif

    @component('mail::component_header', ['title' => 'Products'])
    @endcomponent

    @if(!empty($productsInCombo))
        @component('mail::products_combo', [
            'comboSets' => getComboSets($productsInCombo, $currency),
            'products' => $productsInCombo,
            'currency' => $currency,
            'domain' => $data['store_info']['domain'],
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
        ])
        @endcomponent
    @endif
    @component('mail::products', [
        'products' => $productsNotInCombo,
        'domain' => $data['store_info']['domain'],
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
    @endcomponent

    @component('mail::spacer')
    @endcomponent

    @component('mail::content')
        <p class="new_tc">
            The {{ $store_info['name'] }} Team
        </p>
    @endcomponent
@endcomponent

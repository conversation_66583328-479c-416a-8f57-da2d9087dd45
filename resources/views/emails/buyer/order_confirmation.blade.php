@php
    use Carbon\Carbon;

    $baseUrl = $data['base_url'] ?? null;
    $hashId = $data['logHashId'] ?? null;
    $customerName = $data['name'] ?? '';
    $customerEmail = $data['email'] ?? '';
    $orderNumber = Arr::get($data, 'order.order_number');
    $supportEmail = Arr::get($data, 'store_info.email');
    $pageContactUs = clickTrackingMail($baseUrl,
                                       $baseUrl . '/page/contact-us?order_number=' . $orderNumber
                                       . '&customer_name=' . urlencode($customerName)
                                       . '&customer_email=' . $customerEmail
                                       , $hashId);

    try {
        $fullStatementDescriptor = Arr::get($data, 'order.statement_descriptor');
    } catch (\Exception $e) {
        // do nothing
    }

    /*
     * Kiểm tra xem có hiển thị thông báo ship late hay không
     * Hiển thị từ ngày 9/12 đến hết 18/12 hàng năm
     */
    $currentDate = Carbon::now();
    $year = $currentDate->year;
    $start = Carbon::create($year, 12, 9);
    $end = Carbon::create($year, 12, 19);
    $isShowWarningXmas = $currentDate->between($start, $end);

    $isShowPromotionContent = true;
    $mailboxNumberText = \App\Providers\FulfillAPI\AbstractModel::MAILBOX_NUMBER_TEXT;
    $houseNumberText = \App\Providers\FulfillAPI\AbstractModel::HOUSE_NUMBER_TEXT;

    $isShowChinaWarning = Arr::get($data, 'order.is_notify_china_delivery_late', false);
@endphp
@component('mail::message', ['data' => $data])
    @component('mail::hero', [
        'logo' => 'shopping-cart_white.png',
        'subject' => 'Order Confirmation',
        'button_text' => 'View Order',
        'button_url' => $data['order']['status_url'],
        'page_contact_us' => $pageContactUs,
        'baseUrl' => $baseUrl,
        'hashId' => $hashId,
        'template' => 'order_confirmation',
        ])
        @slot('message')
            {{ $data['name'] }}, you have a great taste!<br/>
        @endslot
    @endcomponent

    @component('mail::content')
        @if($isShowWarningXmas)
            <p style="color: rgb(255, 145, 0);">
                * Order may arrive post-Christmas due to peak season.
            </p>
        @endif
        @if($isShowChinaWarning)
            <p style="color: rgb(255, 145, 0);">
                * Due to stock shortages during this season, there maybe slight delays in the delivery of your order.
            </p>
        @endif
        @component('mail::warning_ship_late')
        @endcomponent
        <p class="new_tc">
            We’ve received your order and we know you’re excited about it. Once your package ships, we will send you an
            email
            with a tracking number immediately.<br/><br/>
        </p>
        @php
            /** If the order has more than 1 product, show the message */
            $products = Arr::get($data, 'order.products', []);
        @endphp
        @if(count($products) > 1)
            <p class="new_tc">
                We may have sent your items in separate parcels so please check your emails to see if any of your items
                will be arriving separately.
            </p>
        @endif
    @endcomponent

    @component('mail::address', ['data' => $data, 'estimated_delivery' => Arr::get($data, 'order.estimated_delivery', '')])
        @slot('shipping_address')
            @if(empty(Arr::get($data, 'order.customer.address', '')))
                {{ Arr::get($data, 'order.address.name', '') }} <br>
                {{ Arr::get($data, 'order.address.address', '') }} <br>
                @if(!empty(Arr::get($data, 'order.address.address_2')))
                    {{ Arr::get($data, 'order.address.address_2') }} <br>
                @endif
                @if(!empty(Arr::get($data, 'order.address.house_number')))
                    {{ $houseNumberText }} {{ Arr::get($data, 'order.address.house_number') }}<br>
                @endif
                @if(!empty(Arr::get($data, 'order.address.mailbox_number')))
                    {{ $mailboxNumberText }} {{ Arr::get($data, 'order.address.mailbox_number') }}<br>
                @endif
                {{ Arr::get($data, 'order.address.city', '') }} <br>
                {{ Arr::get($data, 'order.address.postcode', '') }},
                {{ Arr::get($data, 'order.address.state', '') }}
                {{ Arr::get($data, 'order.address.country', '') }}<br>
                {{ Arr::get($data, 'order.address.phone', '') }} <br>
            @else
                {{ Arr::get($data, 'order.customer.name', '') }} <br>
                {{ Arr::get($data, 'order.customer.address', '') }} <br>
                @if(!empty(Arr::get($data, 'order.address.address_2')))
                    {{ Arr::get($data, 'order.address.address_2') }} <br>
                @endif
                @if(!empty(Arr::get($data, 'order.customer.house_number')))
                    {{ $houseNumberText }} {{ Arr::get($data, 'order.customer.house_number') }}<br>
                @endif
                @if(!empty(Arr::get($data, 'order.customer.mailbox_number')))
                    {{ $mailboxNumberText }} {{ Arr::get($data, 'order.customer.mailbox_number') }}<br>
                @endif
                {{ Arr::get($data, 'order.customer.city', '') }} <br>
                {{ Arr::get($data, 'order.customer.postcode', '') }},
                {{ Arr::get($data, 'order.customer.state', '') }}
                {{ Arr::get($data, 'order.customer.country', '') }}<br>
                {{ Arr::get($data, 'order.customer.phone', '') }} <br>
            @endif
        @endslot

        @slot('shipping_method')
            {{ Arr::get($data, 'order.shipping_method', '') }}
        @endslot

        @slot('payment_method')
            {{ Arr::get($data, 'order.payment_method', 'Unknown') }}
        @endslot
    @endcomponent

    @component('mail::content')
        <div class="new_tc">
            Order <strong>{{ $orderNumber }}</strong>
        </div>
    @endcomponent

    @component('mail::component_header')
        @slot('title')
            Products
        @endslot
    @endcomponent

    {{-- start loop products --}}
    @php
        $order = Arr::get($data, 'order');
        $currency = Arr::get($data, 'currency');

        $products = Arr::get($data, 'order.products', []);
        $products = collect($products);

        $productsNotInCombo = $products->filter(static function($product) {
            return empty($product['combo_id']);
        })->values()->all();

        $productsInCombo = $products->filter(static function($product) {
            return !empty($product['combo_id']);
        })->values()->all();
    @endphp

    @if(!empty($productsInCombo))
        @component('mail::products_combo', [
            'comboSets' => getComboSets($products->toArray(), $currency),
            'products' => $productsInCombo,
            'currency' => $currency,
            'domain' => $data['store_info']['domain'],
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
        ])
        @endcomponent
    @endif

    @component('mail::products', [
        'products' => $productsNotInCombo,
        'domain' => $data['store_info']['domain'],
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
    @endcomponent
    @php
        $discount_code = Arr::get($data, 'store_info.discount_code');
        $promotion_title = Arr::get($data, 'store_info.promotion_title');
        $sub_total = Arr::get($data, 'order.total_product_amount');
        $shipping_cost = Arr::get($data, 'order.total_shipping_amount');
        $insurance_fee = Arr::get($data, 'order.insurance_fee');
        $total_discount = Arr::get($data, 'order.total_discount');
        $order_discount_code = Arr::get($data, 'order.discount_code');
        $tax = Arr::get($data, 'order.total_tax_amount');
        $total_amount = Arr::get($data, 'order.total_amount');
        $payment_discount = Arr::get($data, 'order.payment_discount');
        $tip_amount = Arr::get($data, 'order.tip_amount');
        $hero_data = [
            'order_discount_code' => $order_discount_code,
            'promotion_title' => $promotion_title,
            'sub_total' => $sub_total,
            'shipping_cost' => $shipping_cost,
            'insurance_fee' => $insurance_fee,
            'total_discount' => $total_discount,
            'tax' => $tax,
            'tip_amount' => $tip_amount,
            'total_amount' => $total_amount,
            'payment_discount' => $payment_discount
        ];
    @endphp
    @component('mail::hero_receipt', $hero_data)
    @endcomponent

    @component('mail::content')
        @if(!empty($fullStatementDescriptor))
            <p class="new_tc" style="font-size: 12px; text-align: right;">
                Please note that this charge will appear as <strong>{{ $fullStatementDescriptor }}</strong> on your
                credit card statement.
            </p>
        @endif
    @endcomponent

    @php
        $storeUrl = Arr::get($data, 'store_url');
        $storeUrl = $storeUrl[-1] === '/' ? $storeUrl : $storeUrl . '/';
    @endphp
    @if($isShowPromotionContent)
        @component('mail::content')
            @if ($promotion_title && $discount_code)
                <fieldset style="border: 2px dashed #f4478e">
                    <legend style="font-weight: bold;">THANK YOU FOR YOUR ORDER</legend>
                    <div style="text-align: center;">
                        <p style="margin-top: 20px;">
                            {{ $promotion_title }}
                        </p>
                        <p style="font-weight: bold;">
                            {{ $discount_code }}
                        </p>
                        <table role="presentation" class="new_ebtn new_tc" align="center" border="0"
                               cellspacing="0" cellpadding="0" style="margin-bottom: 1rem;">
                            <tbody>
                            <tr>
                                <td class="new_accent_b">
                                    <a href="{{ clickTrackingMail($baseUrl, $storeUrl . "collection?discount=" . $discount_code, $hashId) }}">
                                        <span>Shop Now</span>
                                    </a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </fieldset>
            @endif
        @endcomponent
    @endif


    @php
        $order = Arr::get($data, 'order');
        $currency = Arr::get($data, 'currency');
        $related_products = Arr::get($data, 'related_products', []);
        foreach ($related_products as $key => $product) {
            $product['price'] = formatCurrency($product['price'], $currency['code'], $currency['locale']);
        }
    @endphp
    @if ($isShowPromotionContent && count($related_products) > 0)
        @component('mail::related_products', [
            'related_products' => $related_products,
            'baseUrl' => $baseUrl,
            'hashId' => $hashId
            ])
            @slot('header')
                @component('mail::component_header')
                    @slot('title')
                        You may also like
                    @endslot
                @endcomponent
            @endslot
        @endcomponent
    @endif

    @php
        $storeName = Arr::get($data, 'store_info.name');
    @endphp
    @component('mail::content')
        <p class="new_tc">
            If you have any questions, don't hesitate to contact us at
            <strong><a href="mailto:{{ $supportEmail }}" rel="noopener">{{ $supportEmail }}</a></strong><br/>
            Thank you for shopping at {{ $storeName }}!<br/>
            See you soon,<br/>
        </p>
        <p class="new_tc">
            The {{ $storeName }} Team<br/>
        </p>
    @endcomponent
@endcomponent

@php
    $baseUrl = $data['base_url'] ?? null;
    $hashId = $data['logHashId'] ?? null;
    $customerName = $data['name'] ?? '';
    $customerEmail = $data['email'] ?? '';
    $orderNumber = Arr::get($data, 'order.order_number');
    $supportEmail = Arr::get($data, 'store_info.email');
    $storeName = Arr::get($data, 'store_info.name');
    $productImage = Arr::get($data, 'product.image');
    $productTitle = Arr::get($data, 'product.title');
    $productDescription = Arr::get($data, 'product.description');
    $pageContactUs = clickTrackingMail($baseUrl, $baseUrl . '/page/contact-us?order_number=' . $orderNumber . '&customer_name=' . urlencode($customerName) . '&customer_email=' . $customerEmail, $hashId);
@endphp
@component('mail::message', ['data' => $data])
    @component('mail::hero', [
        'logo' => 'shopping-cart_white.png',
        'subject' => 'Shipping Notification #' . $orderNumber,
        'button_text' => 'View Order',
        'button_url' => $data['order']['status_url'],
        'page_contact_us' => $pageContactUs,
        'baseUrl' => $baseUrl,
        'hashId' => $hashId,
        'template' => 'order_shipping_notification',
        ])
        @slot('message')
            Hi {{ $data['name'] }}, Great news!<br/>
        @endslot
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            Due to stock shortages, your order maybe a slight delay in processing and shipping in a next few days
            lately.<br/>
            The restock plan will be May 13, we will hold your order until May 13 for production and immediate dispatch
            thereafter.<br/>
        </p>
        <p class="new_tc">
            However, we have alternative sizes available in stock that may suit your preferences.
        </p>
    @endcomponent

    @component('mail::component_header')
        @slot('title')
            Alternative Products
        @endslot
    @endcomponent
    <table role="presentation" class="new_email_table" width="100%" border="0" cellspacing="0" cellpadding="0">
        <tbody>
        <tr>
            <td class="new_email_body new_tc">
                <!--[if (mso)|(IE)]>
                <table role="presentation" width="640" border="0" cellspacing="0" cellpadding="0" align="center"
                       style="vertical-align:top;width:640px;Margin:0 auto;">
                    <tbody>
                    <tr>
                        <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                <table role="presentation" class="new_content_section" width="100%" border="0" cellspacing="0"
                       cellpadding="0">
                    <tbody>
                    <tr>
                        <td class="new_content_cell new_content_b new_py new_bb new_tc">
                            <!--[if (mso)|(IE)]>
                            <table role="presentation" width="624" border="0" cellspacing="0" cellpadding="0"
                                   align="center" style="vertical-align:top;width:624px;Margin:0 auto;">
                                <tbody>
                                <tr>
                                    <td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
                            <div class="new_col_1" style="max-width: unset; width: 40%">
                                <table role="presentation" class="new_column" width="100%" border="0"
                                       cellspacing="0" cellpadding="0">
                                    <tbody>
                                    <tr>
                                        <td class="new_column_cell new_tc">
                                            <p class="new_mb_0 new_imgr">
                                                <img class="new_bra" role="img" src="{{ $productImage }}" alt="{{ $productTitle }}" style="max-width:200px;">
                                            </p>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--[if (mso)|(IE)]></td>
                            <td width="484" style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
                            <![endif]-->
                            <div class="new_col_2">
                                <table role="presentation" class="new_column" width="100%" border="0" cellspacing="0"
                                       cellpadding="0">
                                    <tbody>
                                    <tr>
                                        <td class="new_column_cell new_tl new_switch_tc">
                                            <h3>
                                                <span>{{ $productTitle  }}</span>
                                            </h3>
                                            <p class="new_mb_0 new_tm">
                                                {{ $productDescription }}
                                            </p>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--[if (mso)|(IE)]></td></tr></tbody></table><![endif]-->
            </td>
        </tr>
        </tbody>
    </table>

    @component('mail::content')
        <p class="new_tc" style="margin-top: 10px;">
            Proceeding with the replacement product, please reply to this email with your confirmation by click on
            <strong>Contact Us</strong><br/>
        </p>
    @endcomponent
    @component('mail::content')
        <p class="new_tc">
            We appreciate your understanding and cooperation in this matter.<br/>
            If you have any questions, don’t hesitate to contact us at
            {{ $supportEmail }}<br/>
        </p>
        <p class="new_tc">
            Thank you,<br/>
            The {{ $storeName }} Team<br/>
        </p>
    @endcomponent
@endcomponent

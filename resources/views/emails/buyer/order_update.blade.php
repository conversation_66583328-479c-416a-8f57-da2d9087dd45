@component('mail::message', ['data' => $data])
    @php
        $baseUrl = $data['base_url'] ?? null;
        $hashId = $data['logHashId'] ?? null;
    @endphp
    @component('mail::header_welcome', [
        'title' => 'Your order #' . $data['order_id'] . ' has been updated',
        'message' => $data['message'],
        'icon' => 'heart_white.png',
        ])
    @endcomponent
    @component('mail::content')
        @component('mail::warning_ship_late')
        @endcomponent
    @endcomponent
    @component('mail::button', [
        'url' => $data['order_url'],
        'baseUrl' => $baseUrl,
        'hashId' => $hashId
        ])
        Order Detail
    @endcomponent
@endcomponent

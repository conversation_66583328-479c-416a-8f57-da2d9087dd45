[{"key": 191104, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jx2u.com/", "_name": " JX2U", "_name_zh-cn": " JX2U", "_name_zh-hk": " JX2U", "_group": "12"}, {"key": 190760, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://qianyuwangluo.com.cn/", "_name": " QIANYU", "_name_zh-cn": "物邮供应链", "_name_zh-hk": "物郵供應鏈", "_group": "12"}, {"key": 101030, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.wearewarp.com/", "_name": " W/:\\RP", "_name_zh-cn": " W/:\\RP", "_name_zh-hk": " W/:\\RP", "_group": "11"}, {"key": 190135, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 4000-666-139", "_url": "http://www.139express.com/", "_name": "139Express", "_name_zh-cn": "139Express", "_name_zh-hk": "139Express", "_group": "12"}, {"key": 190659, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.17-exp.com/", "_name": "17EXP", "_name_zh-cn": "17EXP", "_name_zh-hk": "17EXP", "_group": "12"}, {"key": 190164, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.17feia.com/", "_name": "17FEIA", "_name_zh-cn": "17FEIA", "_name_zh-hk": "17FEIA", "_group": "12"}, {"key": 191047, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://1st56.com/group/index/", "_name": "1ST", "_name_zh-cn": "一代集团", "_name_zh-hk": "壹代國際", "_group": "12"}, {"key": 190400, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 020-39950228", "_url": "http://www.1stop.net/", "_name": "1STOP", "_name_zh-cn": "一站到岸", "_name_zh-hk": "一站到岸", "_group": "12"}, {"key": 100905, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-9437991381", "_url": "http://www.1strackcouriers.com/", "_name": "1strack ", "_name_zh-cn": "1strack ", "_name_zh-hk": "1strack ", "_group": "11"}, {"key": 190349, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.1tongexpress.com/", "_name": "1TONG", "_name_zh-cn": "1TONG", "_name_zh-hk": "1TONG", "_group": "10"}, {"key": 191215, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 4000-030-218", "_url": "http://www.218wl.cn/", "_name": "218 Logistics", "_name_zh-cn": "二一八国际物流", "_name_zh-hk": "貳壹捌國際物流", "_group": "12"}, {"key": 100354, "_country": 1607, "_country_iso": "PH", "_email": "<EMAIL>", "_tel": "+63 2 77-99-222", "_url": "http://supplychain.2go.com.ph/", "_name": "2GO", "_name_zh-cn": "2GO", "_name_zh-hk": "2GO", "_group": "11"}, {"key": 190811, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18598273939", "_url": "https://www.3cliques.io", "_name": "3CLIQUES", "_name_zh-cn": "3CLIQUES", "_name_zh-hk": "3CLIQUE", "_group": "12"}, {"key": 190881, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 18329070092", "_url": "https://www.3dada.cn/", "_name": "3DADA", "_name_zh-cn": "圣大", "_name_zh-hk": "聖大", "_group": "12"}, {"key": 100746, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://3jmslogistics.com/", "_name": "3JMS Logistics", "_name_zh-cn": "3JMS Logistics", "_name_zh-hk": "3JMS Logistics", "_group": "11"}, {"key": 191376, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.3peex.com/", "_name": "3PE EXPRESS", "_name_zh-cn": "3PE EXPRESS", "_name_zh-hk": "3PE EXPRESS", "_group": "12"}, {"key": 100664, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "+7 (495) 543-99-99", "_url": "https://www.4sides.ru/", "_name": "4 SIDES", "_name_zh-cn": "4 SIDES", "_name_zh-hk": "4 SIDES", "_group": "11"}, {"key": 3131, "_country": 313, "_country_iso": "CO", "_email": "", "_tel": "(+57)(*************", "_url": "https://www.4-72.com.co/", "_name": "4-72", "_name_zh-cn": "4-72", "_name_zh-hk": "4-72", "_group": ""}, {"key": 100919, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "****** 993 4888", "_url": "https://www.4cus.com/", "_name": "4CUS", "_name_zh-cn": "4CUS", "_name_zh-hk": "4CUS", "_group": "11"}, {"key": 190583, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.4pllab.cn/", "_name": "4PLLAB", "_name_zh-cn": "4PL实验室", "_name_zh-hk": "4PL實驗室", "_group": "12"}, {"key": 190094, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.4px.com/", "_name": "4PX", "_name_zh-cn": "递四方", "_name_zh-hk": "遞四方", "_group": "12"}, {"key": 100135, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://fivepost.ru/", "_name": "5Post", "_name_zh-cn": "5Post", "_name_zh-hk": "5Post", "_group": "11"}, {"key": 190240, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://5ulogistics.com/", "_name": "5UL", "_name_zh-cn": "5UL", "_name_zh-hk": "5UL", "_group": "12"}, {"key": 190782, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.6zexpress.com/", "_name": "6ZEX", "_name_zh-cn": "六洲速通", "_name_zh-hk": "六洲速通", "_group": "12"}, {"key": 101037, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-737-1777", "_url": "https://7hoursexpress.com/", "_name": "7 Hours Express", "_name_zh-cn": "7 Hours Express", "_name_zh-hk": "7 Hours Express", "_group": "11"}, {"key": 191285, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 17688515686", "_url": "http://www.7days-express.com/", "_name": "7Days SupplyChain", "_name_zh-cn": "七日达供应链", "_name_zh-hk": "七日達供應鏈", "_group": "12"}, {"key": 190456, "_country": 2001, "_country_iso": "TW", "_email": "<EMAIL>", "_tel": "+866 02-6636-6637", "_url": "https://eservice.7-11.com.tw/", "_name": "7-ELEVEN", "_name_zh-cn": "7-ELEVEN", "_name_zh-hk": "7-ELEVEN", "_group": "12"}, {"key": 190677, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.7-tm.com/", "_name": "7TM", "_name_zh-cn": "7TM", "_name_zh-hk": "7TM", "_group": "12"}, {"key": 100477, "_country": 310, "_country_iso": "CL", "_email": "<EMAIL>", "_tel": "+56 961638641", "_url": "https://tracking.99minutos.com/", "_name": "99<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "99<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "99<PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100678, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-502-1636", "_url": "https://atiempocargo.com/", "_name": "A Tiempo Cargo ", "_name_zh-cn": "A Tiempo Cargo ", "_name_zh-hk": "A Tiempo Cargo ", "_group": "11"}, {"key": 100688, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34 911 631 356", "_url": "https://atuhoraexpress.com/", "_name": "A TU HORA EXPRESS", "_name_zh-cn": "A TU HORA EXPRESS", "_name_zh-hk": "A TU HORA EXPRESS", "_group": "11"}, {"key": 100724, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******−523−5020", "_url": "https://www.aduiepyle.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100412, "_country": 217, "_country_iso": "BG", "_email": "", "_tel": "+359 (888)00-78-00", "_url": "https://a1post.bg/", "_name": "A1 POST", "_name_zh-cn": "A1 POST", "_name_zh-hk": "A1 POST", "_group": "11"}, {"key": 100343, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "http://www.aaacooper.com/", "_name": "AAA Cooper Transportation", "_name_zh-cn": "AAA Cooper Transportation", "_name_zh-hk": "AAA Cooper Transportation", "_group": "11"}, {"key": 100340, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "http://www.abfs.com/", "_name": "ABF Freight", "_name_zh-cn": "ABF Freight", "_name_zh-hk": "ABF Freight", "_group": "11"}, {"key": 191161, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.arbat9.com/", "_name": "ABT", "_name_zh-cn": "阿尔巴特", "_name_zh-hk": "阿爾巴特", "_group": "12"}, {"key": 190512, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sz-ac56.com/", "_name": "ac logistics", "_name_zh-cn": "ac logistics", "_name_zh-hk": "ac logistics", "_group": "12"}, {"key": 101051, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "http://www.accufrate.com/", "_name": "accufRATE", "_name_zh-cn": "accufRATE", "_name_zh-hk": "accufRATE", "_group": "11"}, {"key": 100325, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://acilogistix.com/", "_name": "ACI Logistix", "_name_zh-cn": "ACI Logistix", "_name_zh-hk": "ACI Logistix", "_group": "11"}, {"key": 100726, "_country": 2004, "_country_iso": "TH", "_email": "<EMAIL>", "_tel": "+66 2261 3540", "_url": "https://www.acommerce.asia/", "_name": "aCommerce", "_name_zh-cn": "aCommerce", "_name_zh-hk": "aCommerce", "_group": "11"}, {"key": 100070, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "+30 210 81 90 000", "_url": "https://www.acscourier.net/", "_name": "ACS", "_name_zh-cn": "ACS", "_name_zh-hk": "ACS", "_group": "11"}, {"key": 191178, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 188-2515-6338", "_url": "http://www.acslogstic.com.cn/", "_name": "ACS Logstic", "_name_zh-cn": "安诚顺国际", "_name_zh-hk": "安誠順國際", "_group": "12"}, {"key": 191092, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.acsnets.com/", "_name": "ACS WORLDWIDE EXPRESS", "_name_zh-cn": "ACS国际快递物流", "_name_zh-hk": "ACS國際快遞物流", "_group": "12"}, {"key": 100838, "_country": 1917, "_country_iso": "ZA", "_email": "<EMAIL>", "_tel": "+27 (*************", "_url": "https://act-logistics.co.za/", "_name": "ACT Logistics", "_name_zh-cn": "ACT Logistics", "_name_zh-hk": "ACT Logistics", "_group": "11"}, {"key": 190042, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.adakd.com/", "_name": "ADA国际", "_name_zh-cn": "艾递爱国际", "_name_zh-hk": "艾遞愛國際", "_group": "12"}, {"key": 191101, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.adico-log.com/ ", "_name": "Adico", "_name_zh-cn": "爱德可", "_name_zh-hk": "愛德可", "_group": "12"}, {"key": 100353, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61(3) 8379 8200", "_url": "https://www.adsone.com.au/", "_name": "ADSOne", "_name_zh-cn": "ADSOne", "_name_zh-hk": "ADSOne", "_group": "11"}, {"key": 101018, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "+852-2679 9133", "_url": "https://portal.asl-asia.net/", "_name": "Advanced & Simple Logistics", "_name_zh-cn": "Advanced & Simple Logistics", "_name_zh-hk": "Advanced & Simple Logistics", "_group": "11"}, {"key": 100742, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.aeronet.com/", "_name": "Aeronet", "_name_zh-cn": "Aeronet", "_name_zh-hk": "Aeronet", "_group": "11"}, {"key": 1021, "_country": 102, "_country_iso": "AF", "_email": "", "_tel": "", "_url": "http://afghanpost.gov.af/", "_name": "Afghan Post", "_name_zh-cn": "Afghan Post", "_name_zh-hk": "Afghan Post", "_group": ""}, {"key": 191029, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.afllogistics.com.hk/", "_name": "AFL LOGISTICS", "_name_zh-cn": "高盛国际速运", "_name_zh-hk": "高盛國際速運", "_group": "12"}, {"key": 190833, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 15814460028", "_url": "https://afterhaul.com/", "_name": "AfterHaul", "_name_zh-cn": "航飞速达", "_name_zh-hk": "航飛速達", "_group": "12"}, {"key": 100251, "_country": 1108, "_country_iso": "KW", "_email": "", "_tel": "", "_url": "https://www.agility.com/", "_name": "Agility", "_name_zh-cn": "Agility", "_name_zh-hk": "Agility", "_group": "11"}, {"key": 191323, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-4006-838-567", "_url": "http://ahcexp.com/", "_name": "AHC", "_name_zh-cn": "驿邮通", "_name_zh-hk": "驛郵通", "_group": "12"}, {"key": 190927, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.aohaiguojixin.com/", "_name": "AHGJ", "_name_zh-cn": "傲海国际", "_name_zh-hk": "傲海國際", "_group": "12"}, {"key": 100563, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://logistics.meifan.com.au/", "_name": "Air Bus Logistics", "_name_zh-cn": "Air Bus Logistics", "_name_zh-hk": "Air Bus Logistics", "_group": "11"}, {"key": 100713, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 0141-2360998", "_url": "https://airstarxpress.com/", "_name": "Air Star Xpress Couriers", "_name_zh-cn": "Air Star Xpress Couriers", "_name_zh-hk": "Air Star Xpress Couriers", "_group": "11"}, {"key": 100088, "_country": 1913, "_country_iso": "SG", "_email": "<EMAIL>", "_tel": "+60 03-7875 7768", "_url": "https://airpak-express.com/", "_name": "Airpak Express", "_name_zh-cn": "Airpak Express", "_name_zh-hk": "Airpak Express", "_group": "11"}, {"key": 100645, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.airterra.com/", "_name": "AirTerra", "_name_zh-cn": "AirTerra", "_name_zh-hk": "AirTerra", "_group": "11"}, {"key": 100826, "_country": 321, "_country_iso": "CY", "_email": "<EMAIL>", "_tel": "+357 77777200", "_url": "https://www.airtransgroup.com/", "_name": "AIRTRANS GROUP LTD.", "_name_zh-cn": "AIRTRANS GROUP LTD.", "_name_zh-hk": "AIRTRANS GROUP LTD.", "_group": "11"}, {"key": 100716, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 080-46525252", "_url": "https://airwayscourier.co.in/", "_name": "AIRWAYS COURIER", "_name_zh-cn": "AIRWAYS COURIER", "_name_zh-hk": "AIRWAYS COURIER", "_group": "11"}, {"key": 100794, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-22-40051717", "_url": "http://www.airwingsindia.com/", "_name": "Airwings", "_name_zh-cn": "Airwings", "_name_zh-hk": "Airwings", "_group": "11"}, {"key": 100443, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.aitworldwide.com/", "_name": "AIT Worldwide Logistics", "_name_zh-cn": "AIT Worldwide Logistics", "_name_zh-hk": "AIT Worldwide Logistics", "_group": "11"}, {"key": 100296, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "8004404000", "_url": "https://aj-ex.com/", "_name": "AJEX", "_name_zh-cn": "AJEX", "_name_zh-hk": "AJEX", "_group": "11"}, {"key": 100705, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91  011-44462446", "_url": "http://www.akashganga.info/", "_name": "<PERSON><PERSON><PERSON>a Courier", "_name_zh-cn": "<PERSON><PERSON><PERSON>a Courier", "_name_zh-hk": "<PERSON><PERSON><PERSON>a Courier", "_group": "11"}, {"key": 191071, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 400-1115-156", "_url": "http://www.aknien.cn/", "_name": "Aknien", "_name_zh-cn": "尼恩供应链", "_name_zh-hk": "尼恩供應鏈", "_group": "12"}, {"key": 91021, "_country": 9102, "_country_iso": "AX", "_email": "", "_tel": "", "_url": "http://www.alandpost.ax/", "_name": "Aland Post", "_name_zh-cn": "Aland Post", "_name_zh-hk": "Aland Post", "_group": ""}, {"key": 1031, "_country": 103, "_country_iso": "AL", "_email": "", "_tel": "+355 682044727", "_url": "http://www.postashqiptare.al/", "_name": "Albanian Post", "_name_zh-cn": "Albanian Post", "_name_zh-hk": "Albanian Post", "_group": ""}, {"key": 191207, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.allinlogistics.com.cn/", "_name": "ALE", "_name_zh-cn": "傲林国际", "_name_zh-hk": "傲林國際", "_group": "12"}, {"key": 100551, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "", "_url": "https://www.alfmensajeria.com.mx/", "_name": "AlfmensAjeria", "_name_zh-cn": "AlfmensAjeria", "_name_zh-hk": "AlfmensAjeria", "_group": "11"}, {"key": 1043, "_country": 104, "_country_iso": "DZ", "_email": "", "_tel": "+213 （0）23 59 34 34", "_url": "http://www.ems.dz/", "_name": "Algeria EMS", "_name_zh-cn": "Algeria EMS", "_name_zh-hk": "Algeria EMS", "_group": ""}, {"key": 1041, "_country": 104, "_country_iso": "DZ", "_email": "", "_tel": "+023 92 32 00", "_url": "https://www.poste.dz/", "_name": "Algeria Post", "_name_zh-cn": "Algeria Post", "_name_zh-hk": "Algeria Post", "_group": ""}, {"key": 191193, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ************", "_url": "https://logistics.alibaba.com/", "_name": "Alibaba.com Logistics", "_name_zh-cn": "阿里巴巴国际站物流服务", "_name_zh-hk": "阿里巴巴國際站物流服務", "_group": "12"}, {"key": 190625, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.aliexpress.com/", "_name": "AliExpress", "_name_zh-cn": "全球速卖通", "_name_zh-hk": "全球速賣通", "_group": "12"}, {"key": 100648, "_country": 1608, "_country_iso": "PL", "_email": "", "_tel": "", "_url": "https://allegro.pl/", "_name": "Allegro", "_name_zh-cn": "Allegro", "_name_zh-hk": "Allegro", "_group": "11"}, {"key": 100623, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "http://www.alliedexpress.com.au/", "_name": "Allied Express Transport", "_name_zh-cn": "Allied Express Transport", "_name_zh-hk": "Allied Express Transport", "_group": "11"}, {"key": 191294, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-21053684", "_url": "http://www.szallin.com/", "_name": "All-in Logistics", "_name_zh-cn": "一站物流", "_name_zh-hk": "一站物流", "_group": "12"}, {"key": 190163, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.alljoylogistics.com/", "_name": "ALLJOY", "_name_zh-cn": "ALLJOY", "_name_zh-hk": "ALLJOY", "_group": "12"}, {"key": 190409, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zlwww.vip/", "_name": "AllRoad", "_name_zh-cn": "众鹿跨境物流", "_name_zh-hk": "眾鹿跨境物流", "_group": "12"}, {"key": 191123, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0579-85107001", "_url": "https://www.kjeclub.com/", "_name": "ALLWIN", "_name_zh-cn": "壹起拼", "_name_zh-hk": "壹起拼", "_group": "12"}, {"key": 101054, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "+52 (81) 5998 4200", "_url": "https://almex.com.mx/", "_name": "Almex", "_name_zh-cn": "Almex", "_name_zh-hk": "Almex", "_group": "11"}, {"key": 191288, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.alphastline.com/", "_name": "Alphastline", "_name_zh-cn": "傲飞国际快线", "_name_zh-hk": "傲飛國際快線", "_group": "12"}, {"key": 100733, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.always-express.com/", "_name": "Always Express", "_name_zh-cn": "Always Express", "_name_zh-hk": "Always Express", "_group": "11"}, {"key": 100440, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-272-5900", "_url": "http://www.amtrucking.com/", "_name": "AM Home Delivery", "_name_zh-cn": "AM Home Delivery", "_name_zh-hk": "AM Home Delivery", "_group": "11"}, {"key": 191340, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.express168.com/", "_name": "AMA", "_name_zh-cn": "阿玛国际", "_name_zh-hk": "阿瑪國際", "_group": "12"}, {"key": 101001, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.amazon.fr/", "_name": "Amazon Shipping (FR)", "_name_zh-cn": "Amazon Shipping (FR)", "_name_zh-hk": "Amazon Shipping (FR)", "_group": "11"}, {"key": 100417, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "https://track.amazon.in/", "_name": "Amazon Shipping (IN)", "_name_zh-cn": "Amazon Shipping (IN)", "_name_zh-hk": "Amazon Shipping (IN)", "_group": "11"}, {"key": 100750, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://www.amazon.it/", "_name": "Amazon Shipping (IT)", "_name_zh-cn": "Amazon Shipping (IT)", "_name_zh-hk": "Amazon Shipping (IT)", "_group": "11"}, {"key": 100309, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://ship.amazon.co.uk/requestinfo/", "_name": "Amazon Shipping (UK)", "_name_zh-cn": "Amazon Shipping (UK)", "_name_zh-hk": "Amazon Shipping (UK)", "_group": "11"}, {"key": 100308, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.amazon.com/", "_name": "Amazon Shipping + Amazon MCF", "_name_zh-cn": "Amazon Shipping + Amazon MCF", "_name_zh-hk": "Amazon Shipping + Amazon MCF", "_group": "11"}, {"key": 100510, "_country": 1608, "_country_iso": "PL", "_email": "<EMAIL>", "_tel": "+48 503 509 200", "_url": "https://ambroexpress.pl/", "_name": "AmbroExpress", "_name_zh-cn": "AmbroExpress", "_name_zh-hk": "AmbroExpress", "_group": "11"}, {"key": 100727, "_country": 215, "_country_iso": "BR", "_email": "", "_tel": "", "_url": "https://www.directlog.com.br/", "_name": "Americanas Entrega", "_name_zh-cn": "Americanas Entrega", "_name_zh-hk": "Americanas Entrega", "_group": "11"}, {"key": 190391, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.imlexpress.cn/", "_name": "AML", "_name_zh-cn": "艾姆勒", "_name_zh-hk": "艾姆勒", "_group": "12"}, {"key": 190804, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ymxgz.cn/", "_name": "Am-logistics", "_name_zh-cn": "亚马迅物流", "_name_zh-hk": "亞馬迅物流", "_group": "12"}, {"key": 9051, "_country": 905, "_country_iso": "IE", "_email": "", "_tel": "", "_url": "https://www.anpost.com/", "_name": "An Post", "_name_zh-cn": "An Post", "_name_zh-hk": "An Post", "_group": ""}, {"key": 190923, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18802048252 ", "_url": "http://www.abq56.cc/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "安贝琪", "_name_zh-hk": "安貝琪", "_group": "12"}, {"key": 191167, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "", "_url": "http://www.htdgjgyl.com/", "_name": "And Tongda", "_name_zh-cn": "And Tongda", "_name_zh-hk": "And Tongda", "_group": "12"}, {"key": 100497, "_country": 112, "_country_iso": "AR", "_email": "", "_tel": "+549 1122810023", "_url": "https://www.andreani.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 191174, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+96-95344", "_url": "https://www.ane56.com/", "_name": "Ane Express", "_name_zh-cn": "安能物流", "_name_zh-hk": "安能物流", "_group": "12"}, {"key": 190730, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ajlexpress.com/index.html", "_name": "Anjieli express", "_name_zh-cn": "安捷利物流", "_name_zh-hk": "安捷利物流", "_group": "12"}, {"key": 190047, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szanjun.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "安骏物流", "_name_zh-hk": "安駿物流", "_group": "12"}, {"key": 190790, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+****************", "_url": "https://www.anlexpress.com/", "_name": "ANL", "_name_zh-cn": "新干线", "_name_zh-hk": "新幹線", "_group": "12"}, {"key": 190898, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 021-4001013170", "_url": "https://www.anl-cn.com/", "_name": "ANL", "_name_zh-cn": "美新物流", "_name_zh-hk": "美新物流", "_group": "12"}, {"key": 190944, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "http://anscn.com.cn/ ", "_name": "ANS", "_name_zh-cn": "ANS", "_name_zh-hk": "ANS", "_group": "12"}, {"key": 190304, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://anserx.com/", "_name": "AnserX", "_name_zh-cn": "AnserX", "_name_zh-hk": "AnserX", "_group": "12"}, {"key": 190265, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.168express.cn/", "_name": "ANT Express", "_name_zh-cn": "广州安创", "_name_zh-hk": "廣州安創", "_group": "12"}, {"key": 100589, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "+62 021-5066-3333", "_url": "https://anteraja.id/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 92021, "_country": 9202, "_country_iso": "AN", "_email": "", "_tel": "", "_url": "http://cpostinternational.com/", "_name": "Antilles Post", "_name_zh-cn": "Antilles Post", "_name_zh-hk": "Antilles Post", "_group": ""}, {"key": 190965, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18925265180", "_url": "http://www.eshipcn.com/", "_name": "ANYIDA", "_name_zh-cn": "安逸达", "_name_zh-hk": "安逸達", "_group": "12"}, {"key": 191113, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.anyun88.com/", "_name": "ANYUN", "_name_zh-cn": "安运供应链", "_name_zh-hk": "安運供應鏈", "_group": "12"}, {"key": 190370, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.aoyunltd.com/", "_name": "AO YOU", "_name_zh-cn": "澳运国际", "_name_zh-hk": "澳運國際", "_group": "12"}, {"key": 191093, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.aomeng56.com/", "_name": "Aomeng", "_name_zh-cn": "Aomeng", "_name_zh-hk": "Aomeng", "_group": "12"}, {"key": 191229, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.au-sen.com/", "_name": "AOSEN", "_name_zh-cn": "澳森国际", "_name_zh-hk": "奧森國際", "_group": "12"}, {"key": 191089, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13342617385", "_url": "http://www.51aoyue.com/", "_name": "AOYUE", "_name_zh-cn": "澳越供应链", "_name_zh-hk": "澳越供應鏈", "_group": "12"}, {"key": 101065, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+44-0800 37 37 37", "_url": "https://apc-overnight.com/", "_name": "APC Overnight", "_name_zh-cn": "APC Overnight", "_name_zh-hk": "APC Overnight", "_group": "11"}, {"key": 100121, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://www.apc-pli.com/", "_name": "APC Postal Logistics", "_name_zh-cn": "APC Postal Logistics", "_name_zh-hk": "APC Postal Logistics", "_group": "11"}, {"key": 100178, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://apgecommerce.com/", "_name": "APG eCommerce", "_name_zh-cn": "APG eCommerce", "_name_zh-hk": "APG eCommerce", "_group": "11"}, {"key": 101041, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://apg-global.com/", "_name": "APG Global", "_name_zh-cn": "APG Global", "_name_zh-hk": "APG Global", "_group": "11"}, {"key": 190949, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL> ", "_tel": "+86 61421360-8005", "_url": "https://www.aplus-log.com/", "_name": "Aplus", "_name_zh-cn": "广州艾普拉斯", "_name_zh-hk": "廣州艾普拉斯", "_group": "12"}, {"key": 100527, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "******.602.9499", "_url": "https://www.appleexpress.com/", "_name": "AppleExpress", "_name_zh-cn": "AppleExpress", "_name_zh-hk": "AppleExpress", "_group": "11"}, {"key": 100090, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "", "_url": "https://aquiline-tracking.com/", "_name": "Aquiline", "_name_zh-cn": "Aquiline", "_name_zh-hk": "Aquiline", "_group": "11"}, {"key": 100006, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "", "_url": "https://www.aramex.com/", "_name": "Aramex", "_name_zh-cn": "Aramex", "_name_zh-hk": "Aramex", "_group": "11"}, {"key": 100044, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.aramex.com.au/", "_name": "Aramex AU (formerly Fastway AU)", "_name_zh-cn": "Aramex AU (formerly Fastway AU)", "_name_zh-hk": "Aramex AU (formerly Fastway AU)", "_group": "11"}, {"key": 100067, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "https://www.aramex.co.nz/", "_name": "Aramex NZ (formerly Fastway NZ)", "_name_zh-cn": "Aramex NZ (formerly Fastway NZ)", "_name_zh-hk": "Aramex NZ (formerly Fastway NZ)", "_group": "11"}, {"key": 100607, "_country": 2011, "_country_iso": "TR", "_email": "", "_tel": "+90 4442552", "_url": "https://www.araskargo.com.tr/tr/#", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100333, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://arcb.com/", "_name": "Arc<PERSON><PERSON> (Panther)", "_name_zh-cn": "Arc<PERSON><PERSON> (Panther)", "_name_zh-hk": "Arc<PERSON><PERSON> (Panther)", "_group": "11"}, {"key": 100649, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://www.arcospedizioni.it/", "_name": "Arco Spedizioni", "_name_zh-cn": "Arco Spedizioni", "_name_zh-hk": "Arco Spedizioni", "_group": "11"}, {"key": 100988, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+852 3499 1192", "_url": "https://www.arkspace.com.hk/", "_name": "Ark Space", "_name_zh-cn": "Ark Space", "_name_zh-hk": "Ark Space", "_group": "11"}, {"key": 191105, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.cnruvip.com/", "_name": "ARU", "_name_zh-cn": "ARU", "_name_zh-hk": "ARU", "_group": "12"}, {"key": 92031, "_country": 9203, "_country_iso": "AW", "_email": "", "_tel": "", "_url": "http://www.postaruba.com/", "_name": "Aruba Post", "_name_zh-cn": "Aruba Post", "_name_zh-hk": "Aruba Post", "_group": ""}, {"key": 100796, "_country": 2011, "_country_iso": "TR", "_email": " <EMAIL>", "_tel": "+90 (212) 444 00 26", "_url": "http://www.ase.com.tr/", "_name": "ASE Asia Africa Sky Express", "_name_zh-cn": "ASE Asia Africa Sky Express", "_name_zh-hk": "ASE Asia Africa Sky Express", "_group": "11"}, {"key": 100029, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "http://www.asendia.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100016, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "http://www.asendiausa.com/", "_name": "Asendia USA", "_name_zh-cn": "Asendia USA", "_name_zh-hk": "Asendia USA", "_group": "11"}, {"key": 190521, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gdasgyl.com/", "_name": "ASGYL", "_name_zh-cn": "安速供应链", "_name_zh-hk": "安速供應鏈", "_group": "12"}, {"key": 190303, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.asiaflyexp.com/", "_name": "ASIAFLY", "_name_zh-cn": "ASIAFLY", "_name_zh-hk": "ASIAFLY", "_group": "12"}, {"key": 100434, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "http://www.logisticacanaria.es/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100989, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "******-275-3478", "_url": "https://asldistribution.com/", "_name": "ASL Distribution Services (ASL)", "_name_zh-cn": "ASL Distribution Services (ASL)", "_name_zh-hk": "ASL Distribution Services (ASL)", "_group": "11"}, {"key": 191196, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0592-5667293", "_url": "https://www.astexpress.com/", "_name": "AST", "_name_zh-cn": "安世通", "_name_zh-hk": "安世通", "_group": "12"}, {"key": 191205, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.kuaikuai-cn.com/", "_name": "ASWKK", "_name_zh-cn": "港捷快快", "_name_zh-hk": "港捷快快", "_group": "12"}, {"key": 100820, "_country": 1501, "_country_iso": "OM", "_email": "<EMAIL>", "_tel": "+968 24922000", "_url": "https://www.asyadexpress.om/", "_name": "Asyad Express (أسياد)", "_name_zh-cn": "Asyad Express (أسياد)", "_name_zh-hk": "Asyad Express (أسياد)", "_group": "11"}, {"key": 190408, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://a-tian.aflyfish.com/", "_name": "AT", "_name_zh-cn": "AT", "_name_zh-hk": "AT", "_group": "12"}, {"key": 191219, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "", "_url": "http://atbexp.com/", "_name": "ATB", "_name_zh-cn": "ATB速运", "_name_zh-hk": "ATB速運", "_group": "12"}, {"key": 101009, "_country": 1803, "_country_iso": "RU", "_email": "<EMAIL>", "_tel": "******-555-46-25", "_url": "https://atc.express/", "_name": "ATC Express", "_name_zh-cn": "ATC Express", "_name_zh-hk": "ATC Express", "_group": "11"}, {"key": 100769, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 011-25892045", "_url": "https://atcls.net/", "_name": "ATC Logistical Solutions Pvt. Ltd", "_name_zh-cn": "ATC Logistical Solutions Pvt. Ltd", "_name_zh-hk": "ATC Logistical Solutions Pvt. Ltd", "_group": "11"}, {"key": 191253, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.aotian56.com/", "_name": "ATI", "_name_zh-cn": "ATI", "_name_zh-hk": "ATI", "_group": "12"}, {"key": 100828, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 9707 066 333", "_url": "https://atlanticcourier.net/", "_name": "Atlantic International Express", "_name_zh-cn": "Atlantic International Express", "_name_zh-hk": "Atlantic International Express", "_group": "11"}, {"key": 190787, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.atmadcargo.com/", "_name": "ATMAD GLOBAL CARGO", "_name_zh-cn": "深圳隆诚", "_name_zh-hk": "深圳隆誠", "_group": "12"}, {"key": 100699, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "****-************", "_url": "https://www.atshealthcare.ca/", "_name": "ATS Healthcare", "_name_zh-cn": "ATS Healthcare", "_name_zh-hk": "ATS Healthcare", "_group": "11"}, {"key": 190350, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.atwindow.com/", "_name": "AtWindow", "_name_zh-cn": "在窗", "_name_zh-hk": "在窗", "_group": "10"}, {"key": 191201, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13662234060 ", "_url": "http://www.atx-express.com/", "_name": "ATX", "_name_zh-cn": "奥特迅供应链", "_name_zh-hk": "奧特訊供應商", "_group": "12"}, {"key": 191112, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ***********", "_url": "http://www.au-hawk.com/", "_name": "AU-Hawk", "_name_zh-cn": "澳鹰物流", "_name_zh-hk": "澳鷹物流", "_group": "12"}, {"key": 190146, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.asusexp.com/", "_name": "AUS", "_name_zh-cn": "AUS", "_name_zh-hk": "AUS", "_group": "12"}, {"key": 1151, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "http://auspost.com.au/", "_name": "Australia Post", "_name_zh-cn": "Australia Post", "_name_zh-hk": "Australia Post", "_group": ""}, {"key": 100565, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.arxp.com.au/", "_name": "Australian Regional Xpress Pty Ltd", "_name_zh-cn": "Australian Regional Xpress Pty Ltd", "_name_zh-hk": "Australian Regional Xpress Pty Ltd", "_group": "11"}, {"key": 1161, "_country": 116, "_country_iso": "AT", "_email": "", "_tel": "", "_url": "http://www.post.at/", "_name": "Austrian Post", "_name_zh-cn": "Austrian Post", "_name_zh-hk": "Austrian Post", "_group": ""}, {"key": 191031, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.austway-cargo.cn/ ", "_name": "AUSTWAY", "_name_zh-cn": "澳华国际", "_name_zh-hk": "澳華國際", "_group": "12"}, {"key": 100561, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+44 7857 619734", "_url": "https://aventlogistic.com/", "_name": "avent Logistics", "_name_zh-cn": "avent Logistics", "_name_zh-hk": "avent Logistics", "_group": "11"}, {"key": 100444, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "****-800-AVERI<PERSON> (283-7488)  ", "_url": "https://www.averittexpress.com/", "_name": "Averitt Express", "_name_zh-cn": "Averitt Express", "_name_zh-hk": "Averitt Express", "_group": "11"}, {"key": 100272, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+1 (855) 249-SHIP", "_url": "https://axlehire.com/", "_name": "AxleHire", "_name_zh-cn": "AxleHire", "_name_zh-hk": "AxleHire", "_group": "11"}, {"key": 100855, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+************-028", "_url": "https://aymakan.com/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100858, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+************-028", "_url": "https://aymakan.com.sa/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (AE)", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON><PERSON> (AE)", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON><PERSON> (AE)", "_group": "11"}, {"key": 1171, "_country": 117, "_country_iso": "AZ", "_email": "", "_tel": "", "_url": "http://www.azems.az/", "_name": "Azer Express Post", "_name_zh-cn": "Azer Express Post", "_name_zh-hk": "Azer Express Post", "_group": ""}, {"key": 190076, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.bab-ru.com/", "_name": "BAB", "_name_zh-cn": "BAB", "_name_zh-hk": "BAB", "_group": "12"}, {"key": 190979, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>  ", "_tel": "", "_url": "http://www.bydkd.com/", "_name": "b<PERSON><PERSON><PERSON>", "_name_zh-cn": "佰彦达货运", "_name_zh-hk": "佰彥達貨運", "_group": "12"}, {"key": 191166, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.baizhouguoji.com/#/home", "_name": "BAIZHOU", "_name_zh-cn": "佰州国际", "_name_zh-hk": "佰州國際", "_group": "12"}, {"key": 2031, "_country": 203, "_country_iso": "BD", "_email": "", "_tel": "", "_url": "https://bdpost.gov.bd/", "_name": "Bangladesh Post", "_name_zh-cn": "Bangladesh Post", "_name_zh-hk": "Bangladesh Post", "_group": ""}, {"key": 190703, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.baosencn.cn/", "_name": "Baosen Suntop", "_name_zh-cn": "堡森三通", "_name_zh-hk": "堡森三通", "_group": "12"}, {"key": 2041, "_country": 204, "_country_iso": "BB", "_email": "", "_tel": "", "_url": "http://www.bps.gov.bb/", "_name": "Barbados Post", "_name_zh-cn": "Barbados Post", "_name_zh-hk": "Barbados Post", "_group": ""}, {"key": 190481, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.basaexp.com/", "_name": "Basa International", "_name_zh-cn": "巴萨国际", "_name_zh-hk": "巴薩國際", "_group": "12"}, {"key": 100465, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 02 91269244", "_url": "http://www.baslexpress.com/", "_name": "Basl Express", "_name_zh-cn": "Basl Express", "_name_zh-hk": "Basl Express", "_group": "11"}, {"key": 190897, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 021-56357057", "_url": "http://www.bcdexpress.com/ ", "_name": "BCD", "_name_zh-cn": "佰舱达", "_name_zh-hk": "佰艙達", "_group": "12"}, {"key": 190968, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>   ", "_tel": "", "_url": "http://www.bcytexp.com/ ", "_name": "BCYT", "_name_zh-cn": "秉诚运通", "_name_zh-hk": "秉誠運通", "_group": "12"}, {"key": 190627, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.beiyiguoji56.com/", "_name": "BE", "_name_zh-cn": "贝乙国际", "_name_zh-hk": "貝乙國際", "_group": "12"}, {"key": 100888, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.beefast.com.au/", "_name": "BEE FAST", "_name_zh-cn": "BEE FAST", "_name_zh-hk": "BEE FAST", "_group": "11"}, {"key": 191006, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.tengfeikd.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 190108, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.8256ru.com/", "_name": "BEL", "_name_zh-cn": "北俄国际", "_name_zh-hk": "北俄國際", "_group": "12"}, {"key": 2071, "_country": 207, "_country_iso": "BZ", "_email": "", "_tel": "", "_url": "http://www.belizepostalservice.gov.bz/", "_name": "Belize Post", "_name_zh-cn": "Belize Post", "_name_zh-hk": "Belize Post", "_group": ""}, {"key": 2051, "_country": 205, "_country_iso": "BY", "_email": "", "_tel": "", "_url": "http://belpost.by/", "_name": "Belpost", "_name_zh-cn": "Belpost", "_name_zh-hk": "Belpost", "_group": ""}, {"key": 190934, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>  ", "_tel": "", "_url": "http://www.belugalog.com/cn/", "_name": "Beluga Logistics", "_name_zh-cn": "白鲸物流", "_name_zh-hk": "白鯨物流", "_group": "12"}, {"key": 191269, "_country": 301, "_country_iso": "CN", "_email": "inst115a@163", "_tel": "", "_url": "http://www.bbgjwl.cn/", "_name": "BENBEN", "_name_zh-cn": "犇犇国际", "_name_zh-hk": "犇犇國際", "_group": "12"}, {"key": 100130, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+971 4 239 6303", "_url": "http://www.ibeone.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 90041, "_country": 9004, "_country_iso": "BM", "_email": "", "_tel": "", "_url": "https://www.bermudapost.bm/", "_name": "Bermuda Post", "_name_zh-cn": "Bermuda Post", "_name_zh-hk": "Bermuda Post", "_group": ""}, {"key": 100391, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "", "_url": "https://www.best-inc.co.th/", "_name": "BEST EXPRESS", "_name_zh-cn": "BEST EXPRESS", "_name_zh-hk": "BEST EXPRESS", "_group": "11"}, {"key": 190846, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.800best.com/", "_name": "Best Inc (CN)", "_name_zh-cn": "百世快运 (中国)", "_name_zh-hk": "百世快運 (中國)", "_group": "12"}, {"key": 190259, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.800bestex.com/", "_name": "Best Inc.", "_name_zh-cn": "百世国际", "_name_zh-hk": "百世國際", "_group": "12"}, {"key": 100916, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-336-3346", "_url": "https://www.bestovernite.com/", "_name": "Best Overnite Express", "_name_zh-cn": "Best Overnite Express", "_name_zh-hk": "Best Overnite Express", "_group": "11"}, {"key": 191235, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://bestfulfill.com/", "_name": "BESTFULFILL", "_name_zh-cn": "BESTFULFILL", "_name_zh-hk": "BESTFULFILL", "_group": "12"}, {"key": 100473, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.bettertrucks.com/", "_name": "Better Trucks", "_name_zh-cn": "Better Trucks", "_name_zh-hk": "Better Trucks", "_group": "11"}, {"key": 190933, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": " http://www.linklinkbest.com/ ", "_name": "BETTER WAY ", "_name_zh-cn": "比特汇国际", "_name_zh-hk": "比特匯國際", "_group": "12"}, {"key": 100666, "_country": 1909, "_country_iso": "RS", "_email": "<EMAIL>", "_tel": "+381 069 640 640", "_url": "https://www.bex.rs/", "_name": "BEX", "_name_zh-cn": "BEX", "_name_zh-hk": "BEX", "_group": "11"}, {"key": 191177, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.bgygyl.com/", "_name": "BGY", "_name_zh-cn": "包裹云", "_name_zh-hk": "包裹雲", "_group": "12"}, {"key": 191279, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.bahaovip.com/", "_name": "BH", "_name_zh-cn": "八好国际", "_name_zh-hk": "八好國際", "_group": "12"}, {"key": 190434, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.white-dolphin.com/", "_name": "BHT", "_name_zh-cn": "白海豚", "_name_zh-hk": "白海豚", "_group": "12"}, {"key": 2101, "_country": 210, "_country_iso": "BT", "_email": "", "_tel": "", "_url": "http://www.bhutanpost.bt/", "_name": "Bhutan Post", "_name_zh-cn": "Bhutan Post", "_name_zh-hk": "Bhutan Post", "_group": ""}, {"key": 100617, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "+52 55 8880 0955", "_url": "https://bigsmart.mx/", "_name": "BIG Smart", "_name_zh-cn": "BIG Smart", "_name_zh-hk": "BIG Smart", "_group": "11"}, {"key": 190089, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.birdsystemgroup.com/", "_name": "Bird System", "_name_zh-cn": "Bird System", "_name_zh-hk": "Bird System", "_group": "12"}, {"key": 191126, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.bsgjhy.cn/", "_name": "BISHENG SHIPPING COMPANY", "_name_zh-cn": "毕盛国际货运", "_name_zh-hk": "畢盛國際貨運", "_group": "12"}, {"key": 100780, "_country": 707, "_country_iso": "GR", "_email": "<EMAIL>", "_tel": "+30 211 71 01 466", "_url": "https://www.bizcourier.eu/", "_name": "Biz Courier", "_name_zh-cn": "Biz Courier", "_name_zh-hk": "Biz Courier", "_group": "11"}, {"key": 190829, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-17076901222", "_url": "http://www.kdbjL.com/", "_name": "BJL", "_name_zh-cn": "棒极啦", "_name_zh-hk": "棒極啦", "_group": "12"}, {"key": 101003, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-01922 645 650", "_url": "https://bjshomedelivery.com/", "_name": "BJS Home Delivery", "_name_zh-cn": "BJS Home Delivery", "_name_zh-hk": "BJS Home Delivery", "_group": "11"}, {"key": 190773, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.blexpress.com.cn/", "_name": "BL", "_name_zh-cn": "比邻供应链", "_name_zh-hk": "比鄰供應鏈", "_group": "12"}, {"key": 190913, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.szbl56.com/ ", "_name": "BL", "_name_zh-cn": "贝拉", "_name_zh-hk": "貝拉", "_group": "12"}, {"key": 100289, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "http://www.blackarrow.express/", "_name": "Black Arrow Express", "_name_zh-cn": "Black Arrow Express", "_name_zh-hk": "Black Arrow Express", "_group": "11"}, {"key": 100630, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 (08) 6102 7544", "_url": "https://blackhaul.com.au/", "_name": "BLACKHAUL", "_name_zh-cn": "BLACKHAUL", "_name_zh-hk": "BLACKHAUL", "_group": "11"}, {"key": 100055, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+86 18602331234", "_url": "https://bluedart.com/", "_name": "Blue Dart Express", "_name_zh-cn": "Blue Dart Express", "_name_zh-hk": "Blue Dart Express", "_group": "11"}, {"key": 100250, "_country": 310, "_country_iso": "CL", "_email": "", "_tel": "", "_url": "https://www.blue.cl/", "_name": "Blue Express", "_name_zh-cn": "Blue Express", "_name_zh-hk": "Blue Express", "_group": "11"}, {"key": 190589, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.lb-56.com/", "_name": "<PERSON>", "_name_zh-cn": "蓝豹云仓供应链", "_name_zh-hk": "藍豹雲倉供應鏈", "_group": "12"}, {"key": 190910, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.bluebuc.hk/", "_name": "BlueBuc", "_name_zh-cn": "蓝羚国际", "_name_zh-hk": "藍羚國際", "_group": "12"}, {"key": 100053, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "****** 200 9215", "_url": "https://www.bluecare.express/", "_name": "Bluecare", "_name_zh-cn": "Bluecare", "_name_zh-hk": "Bluecare", "_group": "11"}, {"key": 100098, "_country": 1601, "_country_iso": "PK", "_email": "<EMAIL>", "_tel": "021-111-<PERSON><PERSON>(258339)", "_url": "https://www.blue-ex.com/", "_name": "BlueEx", "_name_zh-cn": "BlueEx", "_name_zh-hk": "BlueEx", "_group": "11"}, {"key": 100453, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-258-7875", "_url": "https://www.bluestreakcouriers.com/", "_name": "Bluestreak", "_name_zh-cn": "Bluestreak", "_name_zh-hk": "Bluestreak", "_group": "11"}, {"key": 190843, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.smurfsexpress.com/", "_name": "BMURFS Express", "_name_zh-cn": "蓝精灵速递", "_name_zh-hk": "藍精靈速遞", "_group": "12"}, {"key": 191108, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.gzbngyl.com/", "_name": "BNGYL", "_name_zh-cn": "比诺供应链", "_name_zh-hk": "比諾供應鏈", "_group": "12"}, {"key": 190921, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 15986805219 ", "_url": "http://www.bnyto.com/", "_name": "BNYT", "_name_zh-cn": "百年运通", "_name_zh-hk": "百年運通", "_group": "12"}, {"key": 190749, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0592-5758026", "_url": "http://www.boda-express.com/", "_name": "BODA", "_name_zh-cn": "博达捷运", "_name_zh-hk": "博达捷运", "_group": "12"}, {"key": 191096, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://bolly-man.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "寶利民速遞", "_name_zh-hk": "寶利民速遞", "_group": "12"}, {"key": 100823, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 9555 684 684", "_url": "https://bombax.in/", "_name": "Bombax Logistics Pvt. Ltd.", "_name_zh-cn": "Bombax Logistics Pvt. Ltd.", "_name_zh-hk": "Bombax Logistics Pvt. Ltd.", "_group": "11"}, {"key": 100193, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "https://www.bombinoexp.com/", "_name": "Bombino Express", "_name_zh-cn": "Bombino Express", "_name_zh-hk": "Bombino Express", "_group": "11"}, {"key": 190080, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.bnexp.com/", "_name": "BONA", "_name_zh-cn": "商盟", "_name_zh-hk": "商盟", "_group": "12"}, {"key": 190882, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.bondex.com.cn", "_name": "Bondex", "_name_zh-cn": "海程邦达", "_name_zh-hk": "海程邦達", "_group": "12"}, {"key": 100949, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "+61-1300 369 300", "_url": "https://bondscouriers.com.au/", "_name": "Bonds Transport Group", "_name_zh-cn": "Bonds Transport Group", "_name_zh-hk": "Bonds Transport Group", "_group": "11"}, {"key": 100414, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.borderexpress.com.au/", "_name": "Border Express", "_name_zh-cn": "Border Express", "_name_zh-hk": "Border Express", "_group": "11"}, {"key": 191147, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.bossexpress.cn/", "_name": "Boss Logistics", "_name_zh-cn": "波士国际", "_name_zh-hk": "波士國際", "_group": "12"}, {"key": 100487, "_country": 313, "_country_iso": "CO", "_email": "<EMAIL>", "_tel": "", "_url": "https://bosta.co/", "_name": "Bo<PERSON>", "_name_zh-cn": "Bo<PERSON>", "_name_zh-hk": "Bo<PERSON>", "_group": "11"}, {"key": 2131, "_country": 213, "_country_iso": "BW", "_email": "", "_tel": "", "_url": "http://www.botspost.co.bw/", "_name": "Botswana Post", "_name_zh-cn": "Botswana Post", "_name_zh-hk": "Botswana Post", "_group": ""}, {"key": 190774, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-85260663-800", "_url": "http://www.boxexpress.com.cn/", "_name": "BOX", "_name_zh-cn": "博克斯供应链", "_name_zh-hk": "博克斯供應鏈", "_group": "12"}, {"key": 101008, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "+30 ************", "_url": "https://boxnow.gr/", "_name": "BOX NOW", "_name_zh-cn": "BOX NOW", "_name_zh-hk": "BOX NOW", "_group": "11"}, {"key": 100157, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://boxberry.ru/", "_name": "Boxberry", "_name_zh-cn": "Boxberry", "_name_zh-hk": "Boxberry", "_group": "11"}, {"key": 100614, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://boxc.com/", "_name": "BoxC", "_name_zh-cn": "BoxC", "_name_zh-hk": "BoxC", "_group": "11"}, {"key": 190570, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.boxinexpress.com:82/", "_name": "BOXIN", "_name_zh-cn": "博信国际物流", "_name_zh-hk": "博信國際物流", "_group": "12"}, {"key": 100276, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "", "_url": "https://boxme.asia/", "_name": "Boxme", "_name_zh-cn": "Boxme", "_name_zh-hk": "Boxme", "_group": "11"}, {"key": 100520, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.boxtal.com/fr/fr/accueil/", "_name": "Boxtal", "_name_zh-cn": "Boxtal", "_name_zh-hk": "Boxtal", "_group": "11"}, {"key": 100749, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34 916 00 98 30", "_url": "https://www.boyacadelivery.com/", "_name": "<PERSON>ac<PERSON>", "_name_zh-cn": "<PERSON>ac<PERSON>", "_name_zh-hk": "<PERSON>ac<PERSON>", "_group": "11"}, {"key": 2061, "_country": 206, "_country_iso": "BE", "_email": "", "_tel": "", "_url": "https://track.bpost.cloud/", "_name": "Bpost", "_name_zh-cn": "Bpost", "_name_zh-hk": "Bpost", "_group": ""}, {"key": 2063, "_country": 206, "_country_iso": "BE", "_email": "", "_tel": "", "_url": "http://www.bpost2.be/bpostinternational/track_trace/find.php", "_name": "Bpost International", "_name_zh-cn": "Bpost International", "_name_zh-hk": "Bpost International", "_group": ""}, {"key": 190011, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.1001000.com/", "_name": "BQC", "_name_zh-cn": "百千诚物流", "_name_zh-hk": "百千誠物流", "_group": "12"}, {"key": 191077, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 15817033436", "_url": " http://www.br3346.com/ ", "_name": "BR", "_name_zh-cn": "佰润供应链", "_name_zh-hk": "佰潤供應鏈", "_group": "12"}, {"key": 190335, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.br1express.com/", "_name": "BR1", "_name_zh-cn": "BR1", "_name_zh-hk": "BR1", "_group": "12"}, {"key": 100534, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "http://brandingworldwide.com.au/", "_name": "BRANDING WORLDWIDE PTY LTD", "_name_zh-cn": "BRANDING WORLDWIDE PTY LTD", "_name_zh-hk": "BRANDING WORLDWIDE PTY LTD", "_group": "11"}, {"key": 100375, "_country": 215, "_country_iso": "BR", "_email": "", "_tel": "", "_url": "https://bbexp.com.br/", "_name": "Brazil Border", "_name_zh-cn": "Brazil Border", "_name_zh-hk": "Brazil Border", "_group": "11"}, {"key": 100557, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://breezeaircargo.com", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 190715, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18676661861", "_url": "http://www.sz-qcsj.com/", "_name": "Bright Future Supply Chain", "_name_zh-cn": "前程似锦供应链", "_name_zh-hk": "前程似錦供應鏈", "_group": "12"}, {"key": 100423, "_country": 1408, "_country_iso": "NO", "_email": "", "_tel": "", "_url": "https://www.bring.com/", "_name": "Bring", "_name_zh-cn": "Bring", "_name_zh-hk": "Bring", "_group": "11"}, {"key": 100166, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "**************", "_url": "https://www.bringeraircargo.com/", "_name": "Bringer Air Cargo", "_name_zh-cn": "Bringer Air Cargo", "_name_zh-hk": "Bringer Air Cargo", "_group": "11"}, {"key": 100197, "_country": 0, "_country_iso": "", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.bringerparcel.com/", "_name": "Bringer Parcel Service", "_name_zh-cn": "Bringer Parcel Service", "_name_zh-hk": "Bringer Parcel Service", "_group": "11"}, {"key": 191127, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0757-26603991", "_url": "http://www.broadway-logistics.com/", "_name": "Broadway", "_name_zh-cn": "博威国际", "_name_zh-hk": "博威國際", "_group": "12"}, {"key": 100446, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-742-1184", "_url": "https://www.brokerden.com/", "_name": "Broker Den", "_name_zh-cn": "Broker Den", "_name_zh-hk": "Broker Den", "_group": "11"}, {"key": 100026, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "http://www.brt.it/", "_name": "BRT <PERSON>i(DPD)", "_name_zh-cn": "BRT <PERSON>i(DPD)", "_name_zh-hk": "BRT <PERSON>i(DPD)", "_group": "11"}, {"key": 2161, "_country": 216, "_country_iso": "BN", "_email": "", "_tel": "", "_url": "http://www.post.gov.bn/", "_name": "Brunei Post", "_name_zh-cn": "Brunei Post", "_name_zh-hk": "Brunei Post", "_group": ""}, {"key": 191022, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  **************  ", "_url": "http://www.beiruiexp.com/", "_name": "BRWL", "_name_zh-cn": "贝瑞物流", "_name_zh-hk": "貝瑞物流", "_group": "12"}, {"key": 190708, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18050771771", "_url": "http://www.bsbtt.com/", "_name": "BSB", "_name_zh-cn": "福州八十八", "_name_zh-hk": "福州八十八", "_group": "12"}, {"key": 190290, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.bsiscm.com/", "_name": "BSI", "_name_zh-cn": "BSI", "_name_zh-hk": "BSI", "_group": "10"}, {"key": 190361, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.bsiecommerce.com/", "_name": "BSIE", "_name_zh-cn": "柏电科技物流", "_name_zh-hk": "柏電科技物流", "_group": "12"}, {"key": 190331, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.btdair.com/", "_name": "BTD", "_name_zh-cn": "BTD", "_name_zh-hk": "BTD", "_group": "12"}, {"key": 100609, "_country": 1924, "_country_iso": "SE", "_email": "", "_tel": "", "_url": "https://budbee.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100707, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "http://www.blueskycourier.co.in/", "_name": "Bue Sky Courier", "_name_zh-cn": "Bue Sky Courier", "_name_zh-hk": "Bue Sky Courier", "_group": "11"}, {"key": 190226, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.buffaloex.com/", "_name": "BUFFALO", "_name_zh-cn": "BUFFALO", "_name_zh-hk": "BUFFALO", "_group": "12"}, {"key": 100621, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "+27 ************", "_url": "https://buffaloex.co.za/", "_name": "BUFFALO (ZA)", "_name_zh-cn": "BUFFALO (ZA)", "_name_zh-hk": "BUFFALO (ZA)", "_group": "11"}, {"key": 2171, "_country": 217, "_country_iso": "BG", "_email": "", "_tel": "", "_url": "http://www.bgpost.bg/", "_name": "Bulgarian Post", "_name_zh-cn": "Bulgarian Post", "_name_zh-hk": "Bulgarian Post", "_group": ""}, {"key": 100521, "_country": 605, "_country_iso": "FR", "_email": "<EMAIL>", "_tel": "+33 01 41 94 10 80", "_url": "https://www.bunddl.com/", "_name": "Bunddl", "_name_zh-cn": "Bunddl", "_name_zh-hk": "Bunddl", "_group": "11"}, {"key": 2191, "_country": 219, "_country_iso": "BI", "_email": "", "_tel": "", "_url": "http://www.poste.bi/", "_name": "Burundi Post", "_name_zh-cn": "Burundi Post", "_name_zh-hk": "Burundi Post", "_group": ""}, {"key": 191200, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 4009268156", "_url": " https://www.by56.com/", "_name": "BY56.com", "_name_zh-cn": "百运网", "_name_zh-hk": "佰運網", "_group": "12"}, {"key": 190279, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  021-67766171", "_url": "http://www.byt56.com.cn/", "_name": "BYT", "_name_zh-cn": "佰邮通", "_name_zh-hk": "佰郵通", "_group": "12"}, {"key": 190602, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://candc-stexpress.com/", "_name": "C&C Co.,Ltd", "_name_zh-cn": "C&C Co.,Ltd", "_name_zh-hk": "C&C Co.,Ltd", "_group": "12"}, {"key": 190905, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13431026618", "_url": "https://www.cntin.com/", "_name": "C2I LOGISTIC", "_name_zh-cn": "中印物流", "_name_zh-hk": "中印物流", "_group": "12"}, {"key": 100045, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "http://www.cacesapostal.com/", "_name": "Cacesa Postal", "_name_zh-cn": "Cacesa Postal", "_name_zh-hk": "Cacesa Postal", "_group": "11"}, {"key": 190271, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.cainiao.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "菜鸟", "_name_zh-hk": "菜鳥", "_group": "12"}, {"key": 191076, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-9519656", "_url": "https://express.cainiao.com/", "_name": "Cainiao(CN)", "_name_zh-cn": "菜鸟速递(CN)", "_name_zh-hk": "菜鳥速遞(CN)", "_group": "12"}, {"key": 100770, "_country": 1601, "_country_iso": "PK", "_email": "<EMAIL>", "_tel": "+92 042111786227", "_url": "https://callcourier.com.pk/", "_name": "CallCourier", "_name_zh-cn": "CallCourier", "_name_zh-hk": "CallCourier", "_group": "11"}, {"key": 3021, "_country": 302, "_country_iso": "KH", "_email": "", "_tel": "", "_url": "http://cambodiapost.post/", "_name": "Cambodia Post", "_name_zh-cn": "Cambodia Post", "_name_zh-hk": "Cambodia Post", "_group": ""}, {"key": 3031, "_country": 303, "_country_iso": "CM", "_email": "", "_tel": "", "_url": "https://www.campost.cm/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": ""}, {"key": 191086, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 1778-680-0758", "_url": "https://main.air-gtc.cn/", "_name": "Canada GTL Express", "_name_zh-cn": "民航快递", "_name_zh-hk": "民航快遞", "_group": "12"}, {"key": 3041, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "http://www.canadapost.ca/", "_name": "Canada Post", "_name_zh-cn": "Canada Post", "_name_zh-hk": "Canada Post", "_group": ""}, {"key": 100146, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "**************", "_url": "https://www.canpar.com/", "_name": "Canpar Express", "_name_zh-cn": "Canpar Express", "_name_zh-hk": "Canpar Express", "_group": "11"}, {"key": 100915, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "+49-01807 38 90 10", "_url": "https://www.cargointernational.de/", "_name": "Cargo International", "_name_zh-cn": "Cargo International", "_name_zh-hk": "Cargo International", "_group": "11"}, {"key": 100869, "_country": 0, "_country_iso": "", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.cargexfreigmovers.com/", "_name": "CargoEx Freight Movers", "_name_zh-cn": "CargoEx Freight Movers", "_name_zh-hk": "CargoEx Freight Movers", "_group": "11"}, {"key": 100174, "_country": 1802, "_country_iso": "RO", "_email": "", "_tel": "", "_url": "https://www.cargus.ro/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100078, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "+44 (0) ************", "_url": "https://wearecaribou.com/", "_name": "Caribou", "_name_zh-cn": "Caribou", "_name_zh-hk": "Caribou", "_group": "11"}, {"key": 100971, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://carrierlogistics.com/", "_name": "Carrier Logistics", "_name_zh-cn": "Carrier Logistics", "_name_zh-hk": "Carrier Logistics", "_group": "11"}, {"key": 100371, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "https://www.castleparcels.co.nz/", "_name": "Castle Parcel", "_name_zh-cn": "Castle Parcel", "_name_zh-hk": "Castle Parcel", "_group": "11"}, {"key": 100851, "_country": 1321, "_country_iso": "MA", "_email": "", "_tel": "", "_url": "https://cathedis.ma/", "_name": "CATHEDIS", "_name_zh-cn": "CATHEDIS", "_name_zh-hk": "CATHEDIS", "_group": "11"}, {"key": 100349, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.cbl-logistica.com/", "_name": "CBL Logistica", "_name_zh-cn": "CBL Logistica", "_name_zh-hk": "CBL Logistica", "_group": "11"}, {"key": 190783, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://chinaccscm.com/", "_name": "CC logistics", "_name_zh-cn": "诚晨物流", "_name_zh-hk": "誠晨物流", "_group": "12"}, {"key": 190904, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.szcce.cn/", "_name": "CCE", "_name_zh-cn": "世纪汇速递", "_name_zh-hk": "世紀匯速遞", "_group": "12"}, {"key": 190364, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.chengdaguoji.com.cn/", "_name": "CD", "_name_zh-cn": "CD", "_name_zh-hk": "CD", "_group": "12"}, {"key": 100030, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "http://www.cdek.ru/", "_name": "CDEK", "_name_zh-cn": "CDEK", "_name_zh-hk": "CDEK", "_group": "11"}, {"key": 190556, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://chenda.toujiaa.cn/", "_name": "CDEX", "_name_zh-cn": "辰达国际物流", "_name_zh-hk": "辰達國際物流", "_group": "12"}, {"key": 100263, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://cdldelivers.com/", "_name": "CDL Last Mile", "_name_zh-cn": "CDL Last Mile", "_name_zh-hk": "CDL Last Mile", "_group": "11"}, {"key": 191011, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "", "_url": " http://www.celdt.cn/", "_name": "CEL", "_name_zh-cn": "畅达", "_name_zh-hk": "暢達", "_group": "12"}, {"key": 100219, "_country": 1918, "_country_iso": "ES", "_email": " <EMAIL>", "_tel": "", "_url": "https://celeritastransporte.com/en/", "_name": "Celeritas", "_name_zh-cn": "Celeritas", "_name_zh-hk": "Celeritas", "_group": "11"}, {"key": 190709, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0532-85875373", "_url": "http://www.centex.cc/", "_name": "CENTEX", "_name_zh-cn": "忠进货运", "_name_zh-hk": "忠進貨運", "_group": "12"}, {"key": 100956, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.centraltransport.com/", "_name": "Central Transport", "_name_zh-cn": "Central Transport", "_name_zh-hk": "Central Transport", "_group": "11"}, {"key": 100297, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.cevalogistics.com/", "_name": "CEVA Logistics", "_name_zh-cn": "CEVA Logistics", "_name_zh-hk": "CEVA Logistics", "_group": "11"}, {"key": 190210, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cgs-express.com/", "_name": "CGS", "_name_zh-cn": "CGS", "_name_zh-hk": "CGS", "_group": "12"}, {"key": 191355, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-18927729786", "_url": "http://www.szcgsd.com/", "_name": "CGSD", "_name_zh-cn": "辰光速达", "_name_zh-hk": "辰光速達", "_group": "12"}, {"key": 191008, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 020-81969249  ", "_url": "http://www.chcn56.com/", "_name": "Changhui Logistics", "_name_zh-cn": "昌晖物流", "_name_zh-hk": "昌暉物流", "_group": "12"}, {"key": 191280, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.shcd56.cn/", "_name": "CHAODAEXP", "_name_zh-cn": "超达国际", "_name_zh-hk": "超達國際", "_group": "12"}, {"key": 190770, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-15919451895", "_url": "http://tan251278210.b2b168.com", "_name": "Chaoyue international logistics", "_name_zh-cn": "超跃国际物流", "_name_zh-hk": "超躍國際物流", "_group": "12"}, {"key": 190725, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-84863697", "_url": "http://www.szctgyl.com/", "_name": "CHENGTONG", "_name_zh-cn": "程通", "_name_zh-hk": "程通", "_group": "12"}, {"key": 190470, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.chunhonggyl.com/", "_name": "CHH", "_name_zh-cn": "春红物流", "_name_zh-hk": "春紅物流", "_group": "12"}, {"key": 191353, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.chienfba.com/", "_name": "CHIEN SUPPLY CHAIN", "_name_zh-cn": "炽恩供应链", "_name_zh-hk": "熾恩供應鏈", "_group": "12"}, {"key": 100278, "_country": 310, "_country_iso": "CL", "_email": "", "_tel": "", "_url": "https://chilexpress.cl/", "_name": "Chilexpress", "_name_zh-cn": "Chilexpress", "_name_zh-hk": "Chilexpress", "_group": "11"}, {"key": 3013, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.11183.com.cn/", "_name": "China EMS", "_name_zh-cn": "China EMS", "_name_zh-hk": "China EMS", "_group": ""}, {"key": 3011, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://yjcx.ems.com.cn/", "_name": "China Post", "_name_zh-cn": "中国邮政", "_name_zh-hk": "中國郵政", "_group": ""}, {"key": 190575, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://csairlog.com/", "_name": "CHINA SOUTHERN AIR LOGISTICS", "_name_zh-cn": "南航物流", "_name_zh-hk": "南航物流", "_group": "10"}, {"key": 190393, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://export.chinacourierhk.com:9088/login", "_name": "CHINACOURIER", "_name_zh-cn": "中快", "_name_zh-hk": "中快", "_group": "10"}, {"key": 100244, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "******-267-1177", "_url": "https://chitchats.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 190983, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://chrissellgz.cn/  ", "_name": "CHRISSELL", "_name_zh-cn": "科赛尔航运", "_name_zh-hk": "科賽爾航運", "_group": "12"}, {"key": 100273, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "http://www.chronopost.fr/", "_name": "Chronopost", "_name_zh-cn": "Chronopost", "_name_zh-hk": "Chronopost", "_group": "11"}, {"key": 100137, "_country": 1321, "_country_iso": "MA", "_email": "", "_tel": "", "_url": "https://chronopost.ma/", "_name": "Chronopost Morocco", "_name_zh-cn": "Chronopost Morocco", "_name_zh-hk": "Chronopost Morocco", "_group": "11"}, {"key": 191080, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.szcjgylcn.com/", "_name": "CHUANG JING", "_name_zh-cn": "深圳创景", "_name_zh-hk": "深圳創景", "_group": "12"}, {"key": 190447, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cy-express.com/", "_name": "Chuangyu Logistics", "_name_zh-cn": "创宇物流", "_name_zh-hk": "創宇物流", "_group": "12"}, {"key": 190111, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.chukou1.com/", "_name": "Chukou1", "_name_zh-cn": "出口易", "_name_zh-hk": "出口易", "_group": "12"}, {"key": 20011, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "", "_url": "https://ipost.post.gov.tw/", "_name": "Chunghwa Post", "_name_zh-cn": "Chunghwa Post", "_name_zh-hk": "Chunghwa Post", "_group": ""}, {"key": 20012, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "", "_url": "https://ipost.post.gov.tw/", "_name": "Chunghwa Post (Demestic)", "_name_zh-cn": "Chunghwa Post (Demestic)", "_name_zh-hk": "Chunghwa Post (Demestic)", "_group": ""}, {"key": 100958, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+************", "_url": "https://www.chunil.co.kr/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100732, "_country": 2001, "_country_iso": "TW", "_email": "<EMAIL>", "_tel": "+886 (02) 8792-5555", "_url": "http://ctc-express.com.tw/", "_name": "Chyuan-Toong Logistics", "_name_zh-cn": "全统物流", "_name_zh-hk": "全統物流", "_group": "11"}, {"key": 190594, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.cigerma.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "西格码物流优选", "_name_zh-hk": "西格碼物流優選", "_group": "12"}, {"key": 190595, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-86371665", "_url": "http://www.ads-logistics.com/", "_name": "CIMC ADS", "_name_zh-cn": "中集世联达", "_name_zh-hk": "中集世聯達", "_group": "12"}, {"key": 191339, "_country": 1404, "_country_iso": "NL", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.cirroecommerce.com/", "_name": "CIRRO E-Commerce", "_name_zh-cn": "CIRRO E-Commerce", "_name_zh-hk": "CIRRO E-Commerce", "_group": "12"}, {"key": 191150, "_country": 605, "_country_iso": "FR", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.cirroparcel.com/", "_name": "CIRRO Parcel", "_name_zh-cn": "CIRRO Parcel", "_name_zh-hk": "CIRRO Parcel", "_group": "12"}, {"key": 191249, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18038009713", "_url": "https://www.i-eparcel.com/", "_name": "Citiparcel", "_name_zh-cn": "诚邮达", "_name_zh-hk": "誠郵達", "_group": "12"}, {"key": 100837, "_country": 1917, "_country_iso": "ZA", "_email": "<EMAIL>", "_tel": "+27 215113105", "_url": "https://www.citisprint.co.za/", "_name": "Citi-Sprint", "_name_zh-cn": "Citi-Sprint", "_name_zh-hk": "Citi-Sprint", "_group": "11"}, {"key": 190234, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cititrans.com/", "_name": "CITITRANS", "_name_zh-cn": "亦邦国际", "_name_zh-hk": "亦邦國際", "_group": "12"}, {"key": 100081, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+60 603-5565 8399", "_url": "http://www.citylinkexpress.com/", "_name": "City Link", "_name_zh-cn": "City Link", "_name_zh-hk": "City Link", "_group": "11"}, {"key": 100405, "_country": 1924, "_country_iso": "SE", "_email": "", "_tel": "", "_url": "https://www.citymail.se/", "_name": "CityMail", "_name_zh-cn": "CityMail", "_name_zh-hk": "CityMail", "_group": "11"}, {"key": 190283, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://cjdropship.com/", "_name": "CJ", "_name_zh-cn": "促佳", "_name_zh-hk": "促佳", "_group": "12"}, {"key": 100117, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+603 3385 5888/+603 3385 5999", "_url": "https://www.cjcentury.com/", "_name": "CJ Century", "_name_zh-cn": "CJ Century", "_name_zh-hk": "CJ Century", "_group": "11"}, {"key": 100116, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "", "_url": "https://www.cjlogistics.com/ko/main", "_name": "CJ Korea Express", "_name_zh-cn": "CJ Korea Express", "_name_zh-hk": "CJ Korea Express", "_group": "11"}, {"key": 100689, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "", "_url": "https://www.cjlogistics.com/en/main/", "_name": "CJ Logistics", "_name_zh-cn": "CJ Logistics", "_name_zh-hk": "CJ Logistics", "_group": "11"}, {"key": 100540, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "+65-6410-2800", "_url": "https://www.cjlogistics.com/en/network/en-sg", "_name": "CJ Logistics (SG)", "_name_zh-cn": "CJ Logistics (SG)", "_name_zh-hk": "CJ Logistics (SG)", "_group": "11"}, {"key": 191159, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://cjpacket.com/", "_name": "CJ Packet", "_name_zh-cn": "CJ Packet", "_name_zh-hk": "CJ Packet", "_group": "12"}, {"key": 191313, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "www.cjbg-ex.com/", "_name": "CJBG", "_name_zh-cn": "超级包裹", "_name_zh-hk": "超級包裹", "_group": "12"}, {"key": 191162, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13615518927", "_url": "http://www.ywcjgj.com/", "_name": "CJ-EXP", "_name_zh-cn": "畅俊国际", "_name_zh-hk": "暢俊國際", "_group": "12"}, {"key": 190834, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "main.expressclearport.top", "_name": "CK internation", "_name_zh-cn": "希凯国际", "_name_zh-hk": "希凱國際", "_group": "12"}, {"key": 191149, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.clocair.com/", "_name": "CLE", "_name_zh-cn": "可络客国际物流", "_name_zh-hk": "可絡客國際物流", "_group": "12"}, {"key": 100698, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": " +34 607533571", "_url": "https://www.closerlogistics.com/", "_name": "Closer Logistics", "_name_zh-cn": "Closer Logistics", "_name_zh-hk": "Closer Logistics", "_group": "11"}, {"key": 100755, "_country": 605, "_country_iso": "FR", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.cma-cgm.com/", "_name": "CMA CGM", "_name_zh-cn": "CMA CGM", "_name_zh-hk": "CMA CGM", "_group": "11"}, {"key": 190999, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cmkexpress.com/", "_name": "CMK", "_name_zh-cn": "晨明坤供应链", "_name_zh-hk": "晨明坤供應鏈", "_group": "12"}, {"key": 190448, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://cnwangtong.com/", "_name": "CN wang tong", "_name_zh-cn": "中国网通", "_name_zh-hk": "中國網通", "_group": "12"}, {"key": 191366, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.china-56.cn/", "_name": "Cn56", "_name_zh-cn": "聚航运", "_name_zh-hk": "聚航運", "_group": "12"}, {"key": 190208, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cne.com/", "_name": "CNE", "_name_zh-cn": "CNE", "_name_zh-hk": "CNE", "_group": "12"}, {"key": 100267, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.cnilink.com/", "_name": "CNILINK", "_name_zh-cn": "CNILINK", "_name_zh-hk": "CNILINK", "_group": "11"}, {"key": 190487, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.tflcn.com/", "_name": "CNOR", "_name_zh-cn": "CNOR", "_name_zh-hk": "CNOR", "_group": "12"}, {"key": 100370, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.colicoli.fr/", "_name": "Col<PERSON>li", "_name_zh-cn": "Col<PERSON>li", "_name_zh-hk": "Col<PERSON>li", "_group": "11"}, {"key": 100027, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "(+33)0 806 000 250", "_url": "http://www.colisprive.com/", "_name": "<PERSON><PERSON>rive(Colis Privé)", "_name_zh-cn": "<PERSON><PERSON>rive(Colis Privé)", "_name_zh-hk": "<PERSON><PERSON>rive(Colis Privé)", "_group": "11"}, {"key": 100239, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.collectplus.co.uk/", "_name": "Collect+", "_name_zh-cn": "Collect+", "_name_zh-hk": "Collect+", "_group": "11"}, {"key": 190711, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-23062871", "_url": "http://www.comebox.cn/", "_name": "COMEBOX", "_name_zh-cn": "盟塔斯", "_name_zh-hk": "盟塔斯", "_group": "12"}, {"key": 100182, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "", "_url": "https://www.comethellas.gr/", "_name": "Comet Hellas", "_name_zh-cn": "Comet Hellas", "_name_zh-hk": "Comet Hellas", "_group": "11"}, {"key": 190414, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://comet-tech.com.cn/", "_name": "Comet Tech", "_name_zh-cn": "上海彗吉", "_name_zh-hk": "上海彗吉", "_group": "12"}, {"key": 100155, "_country": 1919, "_country_iso": "LK", "_email": "", "_tel": "", "_url": "https://www.clexpress.lk/", "_name": "Commonline", "_name_zh-cn": "斯里兰卡捷高", "_name_zh-hk": "斯里蘭卡捷高", "_group": "11"}, {"key": 190428, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": " http://www.com1logistics.com", "_name": "ComOne Express", "_name_zh-cn": "ComOne Express", "_name_zh-hk": "ComOne Express", "_group": "12"}, {"key": 100512, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "+****************", "_url": "http://www.compass-logistics.com/", "_name": "<PERSON>mp<PERSON>", "_name_zh-cn": "<PERSON>mp<PERSON>", "_name_zh-hk": "<PERSON>mp<PERSON>", "_group": "11"}, {"key": 100847, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "+49 17613272460", "_url": "https://www.concordlogistik.com/", "_name": "Concord Logistik", "_name_zh-cn": "Concord Logistik", "_name_zh-hk": "Concord Logistik", "_group": "11"}, {"key": 100486, "_country": 1308, "_country_iso": "MT", "_email": "<EMAIL>", "_tel": "+356 99558355", "_url": "http://www.connectcouriers.eu/", "_name": "Connect Couriers", "_name_zh-cn": "Connect Couriers", "_name_zh-hk": "Connect Couriers", "_group": "11"}, {"key": 100852, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://ccs-courier.com/", "_name": "Continental Courier Service", "_name_zh-cn": "Continental Courier Service", "_name_zh-hk": "Continental Courier Service", "_group": "11"}, {"key": 100467, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "http://conwest-logistics.com/", "_name": "Conwest Logistics", "_name_zh-cn": "CWE", "_name_zh-hk": "CWE", "_group": "11"}, {"key": 100493, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "https://www.coordinadora.com/", "_name": "Coordinadora", "_name_zh-cn": "Coordinadora", "_name_zh-hk": "Coordinadora", "_group": "11"}, {"key": 100255, "_country": 1603, "_country_iso": "PA", "_email": "", "_tel": "", "_url": "https://www.copacourier.com/", "_name": "Copa Airlines Courier", "_name_zh-cn": "Copa Airlines Courier", "_name_zh-hk": "Copa Airlines Courier", "_group": "11"}, {"key": 100940, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.cope.com.au/", "_name": "COPE Sensitive Freight", "_name_zh-cn": "COPE Sensitive Freight", "_name_zh-hk": "COPE Sensitive Freight", "_group": "11"}, {"key": 2151, "_country": 215, "_country_iso": "BR", "_email": "", "_tel": "0800-725-0100", "_url": "http://www.correios.com.br/", "_name": "Correios Brazil", "_name_zh-cn": "Correios Brazil", "_name_zh-hk": "Correios Brazil", "_group": ""}, {"key": 3061, "_country": 306, "_country_iso": "CV", "_email": "", "_tel": "", "_url": "http://www.correios.cv/", "_name": "Correios Cabo Verde", "_name_zh-cn": "Correios Cabo Verde", "_name_zh-hk": "Correios Cabo Verde", "_group": ""}, {"key": 13221, "_country": 1322, "_country_iso": "MZ", "_email": "", "_tel": "", "_url": "http://www.correios.co.mz/", "_name": "Correios de Moçambique", "_name_zh-cn": "Correios de Moçambique", "_name_zh-hk": "Correios de Moçambique", "_group": ""}, {"key": 1121, "_country": 112, "_country_iso": "AR", "_email": "", "_tel": "", "_url": "https://www.correoargentino.com.ar/", "_name": "Correo Argentino", "_name_zh-cn": "Correo Argentino", "_name_zh-hk": "Correo Argentino", "_group": ""}, {"key": 7121, "_country": 712, "_country_iso": "GT", "_email": "", "_tel": "", "_url": "https://correosytelegrafos.civ.gob.gt/", "_name": "Correo de Guatemala", "_name_zh-cn": "Correo de Guatemala", "_name_zh-hk": "Correo de Guatemala", "_group": ""}, {"key": 19031, "_country": 1903, "_country_iso": "SV", "_email": "", "_tel": "", "_url": "http://www.correos.gob.sv/", "_name": "Correo El Salvador", "_name_zh-cn": "Correo El Salvador", "_name_zh-hk": "Correo El Salvador", "_group": ""}, {"key": 16051, "_country": 1605, "_country_iso": "PY", "_email": "", "_tel": "", "_url": "http://www.correoparaguayo.gov.py/", "_name": "Correo <PERSON>o", "_name_zh-cn": "Correo <PERSON>o", "_name_zh-hk": "Correo <PERSON>o", "_group": ""}, {"key": 21041, "_country": 2104, "_country_iso": "UY", "_email": "", "_tel": "(+598) 2916 0200", "_url": "http://www.correo.com.uy/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": ""}, {"key": 3101, "_country": 310, "_country_iso": "CL", "_email": "", "_tel": "", "_url": "http://www.correos.cl/", "_name": "Correos Chile", "_name_zh-cn": "Correos Chile", "_name_zh-hk": "Correos Chile", "_group": ""}, {"key": 3181, "_country": 318, "_country_iso": "CR", "_email": "", "_tel": "", "_url": "http://www.correos.go.cr/", "_name": "Correos Costa Rica", "_name_zh-cn": "Correos Costa Rica", "_name_zh-hk": "Correos Costa Rica", "_group": ""}, {"key": 3201, "_country": 320, "_country_iso": "CU", "_email": "", "_tel": "", "_url": "http://www.correos.cu/", "_name": "Correos de Cuba", "_name_zh-cn": "Correos de Cuba", "_name_zh-hk": "Correos de Cuba", "_group": ""}, {"key": 8041, "_country": 804, "_country_iso": "HN", "_email": "", "_tel": "", "_url": "http://www.honducor.gob.hn/", "_name": "Correos de Honduras", "_name_zh-cn": "Correos de Honduras", "_name_zh-hk": "Correos de Honduras", "_group": ""}, {"key": 5011, "_country": 501, "_country_iso": "EC", "_email": "", "_tel": "", "_url": "http://www.correosdelecuador.gob.ec/", "_name": "Correos Ecuador", "_name_zh-cn": "Correos Ecuador", "_name_zh-hk": "Correos Ecuador", "_group": ""}, {"key": 100048, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.correosexpress.com/", "_name": "Correos Express", "_name_zh-cn": "Correos Express", "_name_zh-hk": "Correos Express", "_group": "11"}, {"key": 16031, "_country": 1603, "_country_iso": "PA", "_email": "", "_tel": "", "_url": "https://www.correospanama.gob.pa/", "_name": "Correos Panama", "_name_zh-cn": "Correos Panama", "_name_zh-hk": "Correos Panama", "_group": ""}, {"key": 19181, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "http://www.correos.es/", "_name": "Correos Spain", "_name_zh-cn": "Correos Spain", "_name_zh-hk": "Correos Spain", "_group": ""}, {"key": 190138, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://tms.cosco-eglobal.com:8086/xms/wnos.htm", "_name": "Cosco Shipping Air", "_name_zh-cn": "中远海运空运", "_name_zh-hk": "中遠海運空運", "_group": "12"}, {"key": 190722, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-400-8110310", "_url": "http://www.cosex.cn/index", "_name": "COSEX", "_name_zh-cn": "浙江慧合", "_name_zh-hk": "浙江慧合", "_group": "12"}, {"key": 100667, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "+30 210 7512000", "_url": "https://www.courier.gr/", "_name": "COURIER CENTER", "_name_zh-cn": "COURIER CENTER", "_name_zh-hk": "COURIER CENTER", "_group": "11"}, {"key": 100927, "_country": 1917, "_country_iso": "ZA", "_email": "<EMAIL>", "_tel": "+27-11 928 8365", "_url": "https://www.courierit.co.za/", "_name": "CourierIt", "_name_zh-cn": "CourierIt", "_name_zh-hk": "CourierIt", "_group": "11"}, {"key": 100131, "_country": 1410, "_country_iso": "NG", "_email": "<EMAIL>", "_tel": "+234-700-CPLUSNG ", "_url": "http://www.courierplus-ng.com/", "_name": "CourierPlus Nigeria", "_name_zh-cn": "CourierPlus Nigeria", "_name_zh-hk": "CourierPlus Nigeria", "_group": "11"}, {"key": 100122, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.couriersplease.com.au/", "_name": "CouriersPlease", "_name_zh-cn": "CouriersPlease", "_name_zh-hk": "CouriersPlease", "_group": "11"}, {"key": 190666, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.cowin-intl.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "12"}, {"key": 190469, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cpex.ltd/", "_name": "CPEX", "_name_zh-cn": "CPEX", "_name_zh-hk": "CPEX", "_group": "12"}, {"key": 191308, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://cndock.com/", "_name": "CRED", "_name_zh-cn": "柯锐德国际", "_name_zh-hk": "柯銳德國際", "_group": "12"}, {"key": 100745, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "+61 1300 595 999", "_url": "https://www.crlexpress.com/", "_name": "CRL Express", "_name_zh-cn": "CRL Express", "_name_zh-hk": "CRL Express", "_group": "11"}, {"key": 3191, "_country": 319, "_country_iso": "HR", "_email": "", "_tel": "", "_url": "http://www.posta.hr/", "_name": "Croatian Post", "_name_zh-cn": "Croatian Post", "_name_zh-hk": "Croatian Post", "_group": ""}, {"key": 100454, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+ (*************", "_url": "https://ccfs.com/", "_name": "CrossCountry <PERSON>", "_name_zh-cn": "CrossCountry <PERSON>", "_name_zh-hk": "CrossCountry <PERSON>", "_group": "11"}, {"key": 100978, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-01753 776000", "_url": "https://www.crossflight.com/", "_name": "Crossflight", "_name_zh-cn": "Crossflight", "_name_zh-hk": "Crossflight", "_group": "11"}, {"key": 190849, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cse-exp.com/", "_name": "CSE", "_name_zh-cn": "CSE", "_name_zh-hk": "CSE", "_group": "12"}, {"key": 190404, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.csil-group.com/", "_name": "CSIL", "_name_zh-cn": "常昇国际", "_name_zh-hk": "常昇國際", "_group": "10"}, {"key": 191069, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.szcsjwl.cn/", "_name": "CSJWL", "_name_zh-cn": "川山甲", "_name_zh-hk": "川山甲", "_group": "12"}, {"key": 100400, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://www.cstitaliascarl.it/", "_name": "CST Italia Scarl", "_name_zh-cn": "CST Italia Scarl", "_name_zh-hk": "CST Italia Scarl", "_group": "11"}, {"key": 190670, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13316534206", "_url": "https://www.233trans.com/", "_name": "CTGJ", "_name_zh-cn": "诚天国际", "_name_zh-hk": "誠天國際", "_group": "12"}, {"key": 190548, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.ctsfreight.com/", "_name": "CTS", "_name_zh-cn": "华贸国际", "_name_zh-hk": "華貿國際", "_group": "12"}, {"key": 100626, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "+31 (0)252 – 628 900", "_url": "https://www.ctsgroup.nl/", "_name": "CTS GROUP", "_name_zh-cn": "CTS GROUP", "_name_zh-hk": "CTS GROUP", "_group": "11"}, {"key": 16101, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "", "_url": "http://www.ctt.pt/", "_name": "CTT", "_name_zh-cn": "CTT", "_name_zh-hk": "CTT", "_group": ""}, {"key": 100114, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "", "_url": "https://www.cttexpress.com/", "_name": "CTT Express", "_name_zh-cn": "CTT Express", "_name_zh-hk": "CTT Express", "_group": "11"}, {"key": 191049, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 186 1545 6837", "_url": "https://www.cttyvip888.com/", "_name": "CTTYEX", "_name_zh-cn": "诚泰通运", "_name_zh-hk": "誠泰通運", "_group": "12"}, {"key": 100231, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.cubyn.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100631, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+82 119-86-23709", "_url": "https://www.cupost.co.kr/", "_name": "CUpost (CU 편의점택배)", "_name_zh-cn": "CUpost (CU 편의점택배)", "_name_zh-hk": "CUpost (CU 편의점택배)", "_group": "11"}, {"key": 100922, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-338-8888", "_url": "https://www.customco.com/", "_name": "Custom Companies", "_name_zh-cn": "Custom Companies", "_name_zh-hk": "Custom Companies", "_group": "11"}, {"key": 190419, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://cxc.com.hk/", "_name": "CXC Express", "_name_zh-cn": "CXC Express", "_name_zh-hk": "CXC Express", "_group": "12"}, {"key": 190510, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cxe.cc/", "_name": "CXE", "_name_zh-cn": "宸迅速递", "_name_zh-hk": "宸迅速遞", "_group": "12"}, {"key": 191025, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "", "_url": "http://www.xq-cy.com/", "_name": "CY", "_name_zh-cn": "新区储运", "_name_zh-hk": "新區儲運", "_group": "12"}, {"key": 100466, "_country": 1404, "_country_iso": "NL", "_email": "<EMAIL>", "_tel": "+31 085 - 27 33 273", "_url": "https://www.cycloon.eu/", "_name": "Cycloon (Fietskoeriers)", "_name_zh-cn": "Cycloon (Fietskoeriers)", "_name_zh-hk": "Cycloon (Fietskoeriers)", "_group": "11"}, {"key": 3211, "_country": 321, "_country_iso": "CY", "_email": "", "_tel": "", "_url": "https://www.cypruspost.post/", "_name": "Cyprus Post", "_name_zh-cn": "Cyprus Post", "_name_zh-hk": "Cyprus Post", "_group": ""}, {"key": 3221, "_country": 322, "_country_iso": "CZ", "_email": "", "_tel": "", "_url": "http://www.ceskaposta.cz/", "_name": "Czech Post", "_name_zh-cn": "Czech Post", "_name_zh-hk": "Czech Post", "_group": ""}, {"key": 100451, "_country": 1105, "_country_iso": "KR", "_email": "<EMAIL>", "_tel": "+82 070-7780-3530", "_url": "https://dabonea.com/", "_name": "Dabonea", "_name_zh-cn": "Dabonea", "_name_zh-hk": "Dabonea", "_group": "11"}, {"key": 100342, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.dachser.com/", "_name": "DACHSER", "_name_zh-cn": "DACHSER", "_name_zh-hk": "DACHSER", "_group": "11"}, {"key": 100944, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+82-(*************", "_url": "http://www.ds3211.co.kr/", "_name": "DAESIN", "_name_zh-cn": "DAESIN", "_name_zh-hk": "DAESIN", "_group": "11"}, {"key": 100372, "_country": 1601, "_country_iso": "PK", "_email": "", "_tel": "", "_url": "https://fastex.pk/", "_name": "Daewoo FastEx Courier", "_name_zh-cn": "Daewoo FastEx Courier", "_name_zh-hk": "Daewoo FastEx Courier", "_group": "11"}, {"key": 100790, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 3 8586 1200", "_url": "https://www.daipost.com/", "_name": "DAI Post", "_name_zh-cn": "DAI Post", "_name_zh-hk": "DAI Post", "_group": "11"}, {"key": 101058, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-1725054600", "_url": "https://www.dailyxpress.in/", "_name": "Daily Xpress", "_name_zh-cn": "Daily Xpress", "_name_zh-hk": "Daily Xpress", "_group": "11"}, {"key": 100600, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "+62 218224548", "_url": "https://www.dakotacargo.co.id/", "_name": "Dakota Group", "_name_zh-cn": "Dakota Group", "_name_zh-hk": "Dakota Group", "_group": "11"}, {"key": 190560, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.dmyc1688.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "大迈云仓", "_name_zh-hk": "大邁雲倉", "_group": "12"}, {"key": 100346, "_country": 401, "_country_iso": "DK", "_email": "<EMAIL>", "_tel": "+45 7252 7252", "_url": "https://www.fragt.dk/", "_name": "Danske Fragtmænd", "_name_zh-cn": "Danske Fragtmænd", "_name_zh-hk": "Danske Fragtmænd", "_group": "11"}, {"key": 100148, "_country": 401, "_country_iso": "DK", "_email": "", "_tel": "", "_url": "https://www.dao.as/", "_name": "dao", "_name_zh-cn": "dao", "_name_zh-hk": "dao", "_group": "11"}, {"key": 191099, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18148819500", "_url": "https://www.express-dy.com/", "_name": "DaoYi", "_name_zh-cn": "道壹", "_name_zh-hk": "道壹", "_group": "12"}, {"key": 101042, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "******-533-8828", "_url": "https://darwynndriver.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100288, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "", "_url": "http://www.dawnwing.co.za/", "_name": "<PERSON> (DPD Laser)", "_name_zh-cn": "<PERSON> (DPD Laser)", "_name_zh-hk": "<PERSON> (DPD Laser)", "_group": "11"}, {"key": 100406, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "+1(877)726-3329", "_url": "https://dayross.com/", "_name": "Day & Ross", "_name_zh-cn": "Day & Ross", "_name_zh-hk": "Day & Ross", "_group": "11"}, {"key": 100472, "_country": 1608, "_country_iso": "PL", "_email": "", "_tel": "", "_url": "https://www.dylt.com/", "_name": "Daylight Transport", "_name_zh-cn": "Daylight Transport", "_name_zh-hk": "Daylight Transport", "_group": "11"}, {"key": 190769, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://dayonetrack.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "橙一", "_name_zh-hk": "橙壹", "_group": "12"}, {"key": 100928, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "****** 415 0935", "_url": "https://www.daytonfreight.com/", "_name": "Dayton Freight", "_name_zh-cn": "Dayton Freight", "_name_zh-hk": "Dayton Freight", "_group": "11"}, {"key": 100206, "_country": 116, "_country_iso": "AT", "_email": "", "_tel": "", "_url": "https://www.dbschenker.com/", "_name": "DB Schenker", "_name_zh-cn": "DB Schenker", "_name_zh-hk": "DB Schenker", "_group": "11"}, {"key": 191309, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-133 7849 9919", "_url": "http://www.dibafang.com.cn/", "_name": "DBF", "_name_zh-cn": "递八方", "_name_zh-hk": "遞八方", "_group": "12"}, {"key": 100725, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+60 1133881306", "_url": "https://dd.express/", "_name": "DD Express", "_name_zh-cn": "滴滴冷链物流", "_name_zh-hk": "滴滴冷鏈物流", "_group": "11"}, {"key": 190891, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.ddgyl.vip/", "_name": "DDGYL", "_name_zh-cn": "东渡供应链", "_name_zh-hk": "東渡供應鏈", "_group": "12"}, {"key": 100097, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+971 4 514 8958", "_url": "https://ddu-express.com/", "_name": "DDU Express", "_name_zh-cn": "DDU Express", "_name_zh-hk": "DDU Express", "_group": "11"}, {"key": 190616, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.de-well.com/", "_name": "De Well", "_name_zh-cn": "德威国际", "_name_zh-hk": "德威國際", "_group": "12"}, {"key": 100291, "_country": 801, "_country_iso": "HK", "_email": "<EMAIL>", "_tel": "+852 3585-7169", "_url": "https://dealer-send.com/", "_name": "DealerSend", "_name_zh-cn": "DealerSend", "_name_zh-hk": "DealerSend", "_group": "11"}, {"key": 190872, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.dealfy.com/", "_name": "Dealfy", "_name_zh-cn": "Dealfy", "_name_zh-hk": "Dealfy", "_group": "12"}, {"key": 101017, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "****** 941 9800", "_url": "https://ddpddl.com/", "_name": "Dedicated Delivery Professionals Inc.", "_name_zh-cn": "Dedicated Delivery Professionals Inc.", "_name_zh-hk": "Dedicated Delivery Professionals Inc.", "_group": "11"}, {"key": 190734, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "************", "_url": "https://www.deksu.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "德速电商物流", "_name_zh-hk": "德速電商物流", "_group": "12"}, {"key": 100060, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 (124) 6719500", "_url": "https://www.delhivery.com/", "_name": "Delhivery", "_name_zh-cn": "Delhivery", "_name_zh-hk": "Delhivery", "_group": "11"}, {"key": 100445, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-997-<PERSON><PERSON> (7447) ", "_url": "https://www.deliver-it.com/", "_name": "DELIVER-IT", "_name_zh-cn": "DELIVER-IT", "_name_zh-hk": "DELIVER-IT", "_group": "11"}, {"key": 101062, "_country": 703, "_country_iso": "GE", "_email": "<EMAIL>", "_tel": "+995-0 (32) 5 00 00 10", "_url": "https://delivo.ge/", "_name": "Delivo", "_name_zh-cn": "Delivo", "_name_zh-hk": "Delivo", "_group": "11"}, {"key": 100108, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://www.dellin.ru/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100212, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "", "_url": "https://www.delnext.com/", "_name": "Delnext", "_name_zh-cn": "Delnext", "_name_zh-hk": "Delnext", "_group": "11"}, {"key": 100334, "_country": 1103, "_country_iso": "GB", "_email": "info@deltec-int ernational.com", "_tel": "+44 (0) 20 8569 6767", "_url": "https://www.deltec-courier.com/", "_name": "Deltec Courier", "_name_zh-cn": "Deltec Courier", "_name_zh-hk": "Deltec Courier", "_group": "11"}, {"key": 100690, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "http://www.godependable.com/", "_name": "Dependable Supply Chain Services", "_name_zh-cn": "Dependable Supply Chain Services", "_name_zh-hk": "Dependable Supply Chain Services", "_group": "11"}, {"key": 190174, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.deppon.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "德邦", "_name_zh-hk": "德邦", "_group": "12"}, {"key": 100287, "_country": 313, "_country_iso": "CO", "_email": "", "_tel": "+57 01 8000 519393", "_url": "https://www.deprisa.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100777, "_country": 1914, "_country_iso": "SK", "_email": "<EMAIL>", "_tel": "+421 02 / 16 292", "_url": "https://derkurier.sk/", "_name": "<PERSON>", "_name_zh-cn": "<PERSON>", "_name_zh-hk": "<PERSON>", "_group": "11"}, {"key": 100977, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61-1300 076 099 ", "_url": "https://designertransport.com.au/", "_name": "Designer Transport", "_name_zh-cn": "Designer Transport", "_name_zh-hk": "Designer Transport", "_group": "11"}, {"key": 7044, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "http://www.deutschepost.de/", "_name": "Deutsche Post Mail", "_name_zh-cn": "Deutsche Post Mail", "_name_zh-hk": "Deutsche Post Mail", "_group": ""}, {"key": 191371, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "86-57427660999", "_url": "http://www.devtrans.com/", "_name": "Devtrans", "_name_zh-cn": "帝航供应链", "_name_zh-hk": "帝航供應鏈", "_group": "12"}, {"key": 190926, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.dex-i.com/", "_name": "DEX", "_name_zh-cn": "递壹时", "_name_zh-hk": "遞壹時", "_group": "12"}, {"key": 191319, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-15816882241", "_url": "http://www.dghy-express.com/", "_name": "DGHY", "_name_zh-cn": "辉宇物流", "_name_zh-hk": "輝宇物流", "_group": "12"}, {"key": 190818, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cn-atiger.com/", "_name": "DH", "_name_zh-cn": "信达全球", "_name_zh-hk": "信達全球", "_group": "12"}, {"key": 190597, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.dhcityexpress.com/", "_name": "DH CITY EXPRESS", "_name_zh-cn": "DH城市快递", "_name_zh-hk": "DH城市快遞", "_group": "12"}, {"key": 191056, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13362960400", "_url": "http://www.dhgyl56.com/", "_name": "DHGJ", "_name_zh-cn": "东昊国际", "_name_zh-hk": "東昊國際", "_group": "12"}, {"key": 100216, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://activetracing.dhl.com/", "_name": "DHL ACTIVETRACING", "_name_zh-cn": "DHL ACTIVETRACING", "_name_zh-hk": "DHL ACTIVETRACING", "_group": "11"}, {"key": 7048, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://ecommerceportal.dhl.com/", "_name": "DHL eCommerce Asia", "_name_zh-cn": "DHL eCommerce Asia", "_name_zh-hk": "DHL eCommerce Asia", "_group": ""}, {"key": 100765, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.dhl.com/global-en/home/<USER>/ecommerce-solutions.html", "_name": "DHL eCommerce CN", "_name_zh-cn": "DHL eCommerce CN", "_name_zh-hk": "DHL eCommerce CN", "_group": "11"}, {"key": 7047, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.dhl.com/us-en/home/<USER>/tracking-ecommerce.html", "_name": "DHL eCommerce US", "_name_zh-cn": "DHL eCommerce US", "_name_zh-hk": "DHL eCommerce US", "_group": ""}, {"key": 100001, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "+86 95380", "_url": "https://mydhl.express.dhl/", "_name": "DHL Express", "_name_zh-cn": "DHL Express", "_name_zh-hk": "DHL Express", "_group": "11"}, {"key": 100245, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.dhl.com/global-en/home/<USER>/freight.html", "_name": "DHL Freight", "_name_zh-cn": "DHL Freight", "_name_zh-hk": "DHL Freight", "_group": "11"}, {"key": 100766, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.dhl.com/us-en/home/<USER>", "_name": "DHL Global Forwarding", "_name_zh-cn": "DHL Global Forwarding", "_name_zh-hk": "DHL Global Forwarding", "_group": "11"}, {"key": 7041, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.dhl.de/en/privatkunden/pakete-empfangen/verfolgen.html", "_name": "DHL Paket", "_name_zh-cn": "DHL Paket", "_name_zh-hk": "DHL Paket", "_group": ""}, {"key": 100392, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.dhl.com/es-en/parcel/home.html", "_name": "DHL Parcel (ES)", "_name_zh-cn": "DHL Parcel (ES)", "_name_zh-hk": "DHL Parcel (ES)", "_group": "11"}, {"key": 100047, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "https://www.dhlparcel.nl/", "_name": "DHL Parcel (NL)", "_name_zh-cn": "DHL Parcel (NL)", "_name_zh-hk": "DHL Parcel (NL)", "_group": "11"}, {"key": 100226, "_country": 1608, "_country_iso": "PL", "_email": "", "_tel": "", "_url": "http://www.dhl.com.pl/", "_name": "DHL Parcel (PL)", "_name_zh-cn": "DHL Parcel (PL)", "_name_zh-hk": "DHL Parcel (PL)", "_group": "11"}, {"key": 100152, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+44 024 7693 7776", "_url": "https://www.dhl.com/gb-en/ecommerce/uk.html", "_name": "DHL Parcel (UK)", "_name_zh-cn": "DHL Parcel (UK)", "_name_zh-hk": "DHL Parcel (UK)", "_group": "11"}, {"key": 100430, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.dhl.com/es-en/parcel/home.html", "_name": "DHL Parcel Spain", "_name_zh-cn": "DHL Parcel Spain", "_name_zh-hk": "DHL Parcel Spain", "_group": "11"}, {"key": 100842, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://mysctrackandtrace.dhl.com/", "_name": "DHL Supply Chain APAC", "_name_zh-cn": "DHL Supply Chain APAC", "_name_zh-hk": "DHL Supply Chain APAC", "_group": "11"}, {"key": 190899, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 010-82028870 ", "_url": "https://www.dhlink.com ", "_name": "DHLink", "_name_zh-cn": "驼飞侠", "_name_zh-hk": "駝飛俠", "_group": "12"}, {"key": 190984, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.zjdht.com/", "_name": "DHT", "_name_zh-cn": "达航通", "_name_zh-hk": "達航通", "_group": "12"}, {"key": 190658, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.dida-exp.com/", "_name": "DIDA International Logistics", "_name_zh-cn": "嘀嗒国际物流", "_name_zh-hk": "嘀嗒國際物流", "_group": "12"}, {"key": 190515, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://mydidadi.com/", "_name": "DIDADI", "_name_zh-cn": "嘀嗒嘀物流科技", "_name_zh-hk": "嘀嗒嘀物流科技", "_group": "12"}, {"key": 100737, "_country": 2001, "_country_iso": "TW", "_email": "<EMAIL>", "_tel": "", "_url": "https://dimerco.com/", "_name": "Dimerco", "_name_zh-cn": "Dimerco", "_name_zh-hk": "Dimerco", "_group": "11"}, {"key": 190716, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-23000780", "_url": "http://www.rcostp.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "丁点集运", "_name_zh-hk": "丁点集运", "_group": "12"}, {"key": 191283, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.szdlty.cn/ ", "_name": "<PERSON><PERSON>", "_name_zh-cn": "鼎立通运", "_name_zh-hk": "鼎立通運", "_group": "12"}, {"key": 101066, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61-1300 347 397", "_url": "https://www.directfreight.com.au/", "_name": "Direct Freight Express", "_name_zh-cn": "Direct Freight Express", "_name_zh-hk": "Direct Freight Express", "_group": "11"}, {"key": 19244, "_country": 1924, "_country_iso": "SE", "_email": "", "_tel": "", "_url": "http://www.directlink.com/", "_name": "Direct Link", "_name_zh-cn": "Direct Link", "_name_zh-hk": "Direct Link", "_group": ""}, {"key": 100885, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.directxps.com/", "_name": "Direct Xpress", "_name_zh-cn": "Direct Xpress", "_name_zh-hk": "Direct Xpress", "_group": "11"}, {"key": 100747, "_country": 217, "_country_iso": "BG", "_email": "<EMAIL>", "_tel": "+359 0700 203 73", "_url": "https://direx.bg/", "_name": "Direx S.A. (Дайрекс EАД)", "_name_zh-cn": "Direx S.A. (Дайрекс EАД)", "_name_zh-hk": "Direx S.A. (Дайрекс EАД)", "_group": "11"}, {"key": 190839, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 18507940807", "_url": "http://dajuxing.com.cn/", "_name": "DJX", "_name_zh-cn": "大巨星国际", "_name_zh-hk": "大巨星國際", "_group": "12"}, {"key": 190922, "_country": 301, "_country_iso": "CN", "_email": "jack_han<PERSON><PERSON>@126.com", "_tel": "+86 0579-87110162", "_url": "https://www.dk56.com/", "_name": "DK", "_name_zh-cn": "东阔国际", "_name_zh-hk": "東闊國際", "_group": "12"}, {"key": 100966, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "****** 399 0162", "_url": "https://dmtrans.com/", "_name": "DM Delivers", "_name_zh-cn": "DM Delivers", "_name_zh-hk": "DM Delivers", "_group": "11"}, {"key": 190718, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-15815865669", "_url": "https://cn.dmfgroup.net/", "_name": "DMF", "_name_zh-cn": "斗满科技", "_name_zh-hk": "斗满科技", "_group": "12"}, {"key": 191128, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.dmgjwl.com/", "_name": "DMGJ", "_name_zh-cn": "大迈国际", "_name_zh-hk": "大邁國際", "_group": "12"}, {"key": 100759, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-7780116456", "_url": "https://dmsmatrix.com/", "_name": "DMSMatrix", "_name_zh-cn": "DMSMatrix", "_name_zh-hk": "DMSMatrix", "_group": "11"}, {"key": 190413, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.dnjexpress.com/en/", "_name": "DNJ Express", "_name_zh-cn": "DNJ Express", "_name_zh-hk": "DNJ Express", "_group": "12"}, {"key": 100714, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 **************", "_url": "http://www.dnxindia.com/", "_name": "DNX Cargo", "_name_zh-cn": "DNX Cargo", "_name_zh-hk": "DNX Cargo", "_group": "11"}, {"key": 100365, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "+7(800)600-3794", "_url": "https://dobropost.com/", "_name": "DobroPost", "_name_zh-cn": "DobroPost", "_name_zh-hk": "DobroPost", "_group": "11"}, {"key": 100897, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://domainlogistics.ca/", "_name": "Domain Logistics", "_name_zh-cn": "Domain Logistics", "_name_zh-hk": "Domain Logistics", "_group": "11"}, {"key": 190706, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0518-87897708", "_url": "http://www.ddgj.hailei2018.com/", "_name": "Dongdi exp", "_name_zh-cn": "东帝国际", "_name_zh-hk": "東帝國際", "_group": "12"}, {"key": 100702, "_country": 1105, "_country_iso": "KR", "_email": "<EMAIL>", "_tel": "+82-1588-4151", "_url": "https://www.doora.co.kr/", "_name": "DOORA Logistics. (두라로지스틱스)", "_name_zh-cn": "DOORA Logistics. (두라로지스틱스)", "_name_zh-hk": "DOORA Logistics. (두라로지스틱스)", "_group": "11"}, {"key": 101036, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.doordash.com/", "_name": "DoorDash", "_name_zh-cn": "DoorDash", "_name_zh-hk": "DoorDash", "_group": "11"}, {"key": 190585, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.doragate.com/", "_name": "DOR", "_name_zh-cn": "任意门", "_name_zh-hk": "任意門", "_group": "12"}, {"key": 190576, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.dorje56.com/", "_name": "DORJE", "_name_zh-cn": "多杰物流", "_name_zh-hk": "多杰物流", "_group": "12"}, {"key": 100188, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "https://dotzot.in/", "_name": "DotZot", "_name_zh-cn": "DotZot", "_name_zh-hk": "DotZot", "_group": "11"}, {"key": 100556, "_country": 116, "_country_iso": "AT", "_email": "", "_tel": "", "_url": "https://www.mydpd.at/", "_name": "DPD (AT)", "_name_zh-cn": "DPD (AT)", "_name_zh-hk": "DPD (AT)", "_group": "11"}, {"key": 100321, "_country": 206, "_country_iso": "BE", "_email": "", "_tel": "+32 070 222 222", "_url": "https://www.dpd.com/be/nl/", "_name": "DPD (BE)", "_name_zh-cn": "DPD (BE)", "_name_zh-hk": "DPD (BE)", "_group": "11"}, {"key": 100815, "_country": 1925, "_country_iso": "CH", "_email": "", "_tel": "", "_url": "https://www.dpd.com/ch/de/", "_name": "DPD (CH)", "_name_zh-cn": "DPD (CH)", "_name_zh-hk": "DPD (CH)", "_group": "11"}, {"key": 101032, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+0755-84823589", "_url": "https://www.dpd.com/cn/en/", "_name": "DPD (CN)", "_name_zh-cn": "DPD (CN)", "_name_zh-hk": "DPD (CN)", "_group": "11"}, {"key": 100483, "_country": 322, "_country_iso": "CZ", "_email": "", "_tel": "", "_url": "https://www.dpd.com/cz/en/", "_name": "DPD (CZ)", "_name_zh-cn": "DPD (CZ)", "_name_zh-hk": "DPD (CZ)", "_group": "11"}, {"key": 100007, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.dpd.com/", "_name": "DPD (DE)", "_name_zh-cn": "DPD (DE)", "_name_zh-hk": "DPD (DE)", "_group": "11"}, {"key": 100808, "_country": 504, "_country_iso": "EE", "_email": "", "_tel": "", "_url": "https://www.dpd.com/ee/et/", "_name": "DPD (EE)", "_name_zh-cn": "DPD (EE)", "_name_zh-hk": "DPD (EE)", "_group": "11"}, {"key": 100072, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "http://www.dpd.fr/", "_name": "DPD (FR)", "_name_zh-cn": "DPD (FR)", "_name_zh-hk": "DPD (FR)", "_group": "11"}, {"key": 100404, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "", "_url": "https://dpd.gr/en/", "_name": "DPD (GR)", "_name_zh-cn": "DPD (GR)", "_name_zh-hk": "DPD (GR)", "_group": "11"}, {"key": 100807, "_country": 319, "_country_iso": "HR", "_email": "", "_tel": "", "_url": "https://www.dpd.com/hr/hr/", "_name": "DPD (HR)", "_name_zh-cn": "DPD (HR)", "_name_zh-hk": "DPD (HR)", "_group": "11"}, {"key": 100584, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "https://www.dpd.com/hu/hu/", "_name": "DPD (HU)", "_name_zh-cn": "DPD (HU)", "_name_zh-hk": "DPD (HU)", "_group": "11"}, {"key": 100142, "_country": 905, "_country_iso": "IE", "_email": "", "_tel": "", "_url": "https://dpd.ie/", "_name": "DPD (IE)", "_name_zh-cn": "DPD (IE)", "_name_zh-hk": "DPD (IE)", "_group": "11"}, {"key": 100810, "_country": 1208, "_country_iso": "LT", "_email": "", "_tel": "", "_url": "https://www.dpd.com/lt/lt/", "_name": "DPD (LT)", "_name_zh-cn": "DPD (LT)", "_name_zh-hk": "DPD (LT)", "_group": "11"}, {"key": 100814, "_country": 1210, "_country_iso": "LU", "_email": "", "_tel": "", "_url": "https://www.dpd.com/lu/fr/", "_name": "DPD (LU)", "_name_zh-cn": "DPD (LU)", "_name_zh-hk": "DPD (LU)", "_group": "11"}, {"key": 100809, "_country": 1202, "_country_iso": "LV", "_email": "", "_tel": "", "_url": "https://www.dpd.com/lv/lv/", "_name": "DPD (LV)", "_name_zh-cn": "DPD (LV)", "_name_zh-hk": "DPD (LV)", "_group": "11"}, {"key": 100811, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "https://www.dpd.com/nl/nl/", "_name": "DPD (NL)", "_name_zh-cn": "DPD (NL)", "_name_zh-hk": "DPD (NL)", "_group": "11"}, {"key": 100111, "_country": 1608, "_country_iso": "PL", "_email": "", "_tel": "", "_url": "https://www.dpd.com.pl/", "_name": "DPD (PL)", "_name_zh-cn": "DPD (PL)", "_name_zh-hk": "DPD (PL)", "_group": "11"}, {"key": 100204, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "", "_url": "https://dpd.pt/", "_name": "DPD (PT)", "_name_zh-cn": "DPD (PT)", "_name_zh-hk": "DPD (PT)", "_group": "11"}, {"key": 100177, "_country": 1802, "_country_iso": "RO", "_email": "", "_tel": "", "_url": "https://www.dpd.com/ro/ro/", "_name": "DPD (RO)", "_name_zh-cn": "DPD (RO)", "_name_zh-hk": "DPD (RO)", "_group": "11"}, {"key": 100071, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://www.dpd.ru/", "_name": "DPD (RU)", "_name_zh-cn": "DPD (RU)", "_name_zh-hk": "DPD (RU)", "_group": "11"}, {"key": 100813, "_country": 1915, "_country_iso": "SI", "_email": "", "_tel": "", "_url": "https://www.dpd.com/si/sl/", "_name": "DPD (SI)", "_name_zh-cn": "DPD (SI)", "_name_zh-hk": "DPD (SI)", "_group": "11"}, {"key": 100812, "_country": 1914, "_country_iso": "SK", "_email": "", "_tel": "", "_url": "https://www.dpd.com/sk/sk/", "_name": "DPD (SK)", "_name_zh-cn": "DPD (SK)", "_name_zh-hk": "DPD (SK)", "_group": "11"}, {"key": 100010, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "0121 275 0500", "_url": "https://www.dpd.co.uk/", "_name": "DPD (UK)", "_name_zh-cn": "DPD (UK)", "_name_zh-hk": "DPD (UK)", "_group": "11"}, {"key": 100709, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+44 0121 275 0500", "_url": "https://www.dpdlocal.co.uk/", "_name": "DPD Local", "_name_zh-cn": "DPD Local", "_name_zh-hk": "DPD Local", "_group": "11"}, {"key": 191032, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-84507401", "_url": " http://www.dpdex.cn/    ", "_name": "DpdEx", "_name_zh-cn": "多普达国际", "_name_zh-hk": "多普達國際", "_group": "12"}, {"key": 190424, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.dpe.net.cn/", "_name": "DPE Express", "_name_zh-cn": "递必易", "_name_zh-hk": "遞必易", "_group": "12"}, {"key": 100014, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "http://www.dpex.com/", "_name": "DPEX", "_name_zh-cn": "DPEX", "_name_zh-hk": "DPEX", "_group": "11"}, {"key": 190807, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://dp.express/", "_name": "DPExpress", "_name_zh-cn": "DP快递", "_name_zh-hk": "DP快遞", "_group": "12"}, {"key": 191336, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 -17371223335", "_url": "https://www.worlddps.com/", "_name": "DPS ", "_name_zh-cn": "达派思供应链", "_name_zh-hk": "達派思供應鏈", "_group": "12"}, {"key": 190490, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.dpx-express.com/", "_name": "DPX EXPRESS", "_name_zh-cn": "地平线速运", "_name_zh-hk": "地平線速運", "_group": "12"}, {"key": 100422, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "1800 568 909", "_url": "https://dragonflyshipping.com.au/", "_name": "Dragonfly", "_name_zh-cn": "Dragonfly", "_name_zh-hk": "Dragonfly", "_group": "11"}, {"key": 190454, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.mhafly.com/", "_name": "Dream & Love", "_name_zh-cn": "梦和爱", "_name_zh-hk": "夢和愛", "_group": "12"}, {"key": 191213, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-13717006370", "_url": "http://www.dgds.net/", "_name": "DSGJ", "_name_zh-cn": "东晟国际", "_name_zh-hk": "東晟國際", "_group": "12"}, {"key": 100186, "_country": 401, "_country_iso": "DK", "_email": "", "_tel": "+****************", "_url": "https://www.dsv.com/", "_name": "DSV", "_name_zh-cn": "DSV", "_name_zh-hk": "DSV", "_group": "11"}, {"key": 100298, "_country": 401, "_country_iso": "DK", "_email": "", "_tel": "", "_url": "https://il.dsv.com/page-our-solutions/ecommerce/", "_name": "DSV e-Commerce IL", "_name_zh-cn": "DSV e-Commerce IL", "_name_zh-hk": "DSV e-Commerce IL", "_group": "11"}, {"key": 100652, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "+52-55 5510 2065  ", "_url": "http://www.dtdexpress.mx/", "_name": "DTD", "_name_zh-cn": "DTD", "_name_zh-hk": "DTD", "_group": "11"}, {"key": 100069, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+852 3702 6376", "_url": "https://www.dtdc.com/", "_name": "DTDC", "_name_zh-cn": "DTDC", "_name_zh-hk": "DTDC", "_group": "11"}, {"key": 191023, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.duotuexpress.com/", "_name": "Duotu supply chain", "_name_zh-cn": "多途供应链", "_name_zh-hk": "多途供應鏈", "_group": "12"}, {"key": 190503, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  15056671511", "_url": "https://www.duxiuexp.com/", "_name": "DuXiuExp", "_name_zh-cn": "独秀国际", "_name_zh-hk": "獨秀國際", "_group": "12"}, {"key": 190869, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 18221331323", "_url": "http://dwgjex.com/", "_name": "DVEX", "_name_zh-cn": "德维国际", "_name_zh-hk": "德維國際", "_group": "12"}, {"key": 190358, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://dweex.com/", "_name": "DWE", "_name_zh-cn": "多维智慧", "_name_zh-hk": "多維智慧", "_group": "10"}, {"key": 190133, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.dwz56.com/", "_name": "DWZ Expres", "_name_zh-cn": "递五洲", "_name_zh-hk": "遞五洲", "_group": "12"}, {"key": 100484, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+44 0333 241 5700 ", "_url": "https://my.dxdelivery.com/", "_name": "DX", "_name_zh-cn": "DX", "_name_zh-hk": "DX", "_group": "11"}, {"key": 190397, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://dyexpress.com/", "_name": "DYEXPRESS", "_name_zh-cn": "德远物流", "_name_zh-hk": "德遠物流", "_group": "12"}, {"key": 190747, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.dyjexp.com/", "_name": "DYJ", "_name_zh-cn": "达运佳", "_name_zh-hk": "达运佳", "_group": "12"}, {"key": 190742, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0594-2726955", "_url": "http://www.diyilog.com/", "_name": "DYLogistics", "_name_zh-cn": "递亿物流", "_name_zh-hk": "遞億物流", "_group": "12"}, {"key": 191100, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.dztlogistics.com/", "_name": "DZT", "_name_zh-cn": "德正通", "_name_zh-hk": "德正通", "_group": "12"}, {"key": 191274, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13028880080", "_url": "http://www.eaa56.cn/", "_name": "EAA", "_name_zh-cn": "欧亚美物流", "_name_zh-hk": "歐亞美物流", "_group": "12"}, {"key": 190329, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szeac.com/", "_name": "EAC", "_name_zh-cn": "EAC", "_name_zh-hk": "EAC", "_group": "12"}, {"key": 100712, "_country": 1101, "_country_iso": "KZ", "_email": "<EMAIL>", "_tel": "+77 721114499", "_url": "https://rapid-post.com/", "_name": "Eagle Post", "_name_zh-cn": "Eagle Post", "_name_zh-hk": "Eagle Post", "_group": "11"}, {"key": 191357, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.eagledhl.com/", "_name": "EAGLEDHL", "_name_zh-cn": "EAGLEDHL", "_name_zh-hk": "EAGLEDHL", "_group": "12"}, {"key": 100225, "_country": 1924, "_country_iso": "SE", "_email": "<EMAIL>", "_tel": "+46 010 810 90 90", "_url": "https://earlybird.se/", "_name": "Early Bird", "_name_zh-cn": "Early Bird", "_name_zh-hk": "Early Bird", "_group": "11"}, {"key": 190538, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cvnlog.com/", "_name": "EASIPASS", "_name_zh-cn": "诚约轻松递", "_name_zh-hk": "誠約輕鬆遞", "_group": "12"}, {"key": 100756, "_country": 2001, "_country_iso": "TW", "_email": "<EMAIL>", "_tel": "+886 03-3992128", "_url": "http://east-wind.com.tw/", "_name": "East Wind Express", "_name_zh-cn": "东风速运", "_name_zh-hk": "東風速運", "_group": "11"}, {"key": 191375, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "86-************", "_url": "http://easy-express-global.com/", "_name": "Easy", "_name_zh-cn": "Easy", "_name_zh-hk": "Easy", "_group": "12"}, {"key": 100194, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "", "_url": "https://www.easymail.gr/en/", "_name": "Easy Mail", "_name_zh-cn": "Easy Mail", "_name_zh-hk": "Easy Mail", "_group": "11"}, {"key": 100075, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://easyway.ru/", "_name": "Easy Way", "_name_zh-cn": "Easy Way", "_name_zh-hk": "Easy Way", "_group": "11"}, {"key": 100095, "_country": 2102, "_country_iso": "UA", "_email": "", "_tel": "", "_url": "https://www.easyget.com.ua/", "_name": "EasyGet", "_name_zh-cn": "EasyGet", "_name_zh-hk": "EasyGet", "_group": "11"}, {"key": 190824, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-23776273", "_url": "http://tms.easyseller.com/", "_name": "EasySeller", "_name_zh-cn": "亿迈", "_name_zh-hk": "億邁", "_group": "12"}, {"key": 190527, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ebazorgroup.com/", "_name": "Ebaza", "_name_zh-cn": "易巴扎", "_name_zh-hk": "易巴扎", "_group": "12"}, {"key": 190579, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.eboexp.com/", "_name": "EBOEXP", "_name_zh-cn": "鄂宝", "_name_zh-hk": "鄂宝", "_group": "12"}, {"key": 190330, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.ybdl56.com/", "_name": "E-Bond", "_name_zh-cn": "亿邦达", "_name_zh-hk": "億邦達", "_group": "12"}, {"key": 101006, "_country": 906, "_country_iso": "IL", "_email": "<EMAIL>", "_tel": "", "_url": "https://e-cargo.co.il/", "_name": "E-Cargo (אי קר<PERSON><PERSON> לוגיסטיקה בע\"מ)", "_name_zh-cn": "E-Cargo (אי קר<PERSON><PERSON> לוגיסטיקה בע\"מ)", "_name_zh-hk": "E-Cargo (אי קר<PERSON><PERSON> לוגיסטיקה בע\"מ)", "_group": "11"}, {"key": 190187, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ecexpress.com.cn/", "_name": "ECexpress", "_name_zh-cn": "ECexpress", "_name_zh-hk": "ECexpress", "_group": "12"}, {"key": 190502, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ecgexpress.com/", "_name": "ECG", "_name_zh-cn": "ECG", "_name_zh-hk": "ECG", "_group": "12"}, {"key": 190817, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ecgo.group/", "_name": "ECGO", "_name_zh-cn": "易速达", "_name_zh-hk": "易速達", "_group": "12"}, {"key": 191017, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": " http://www.linktosg.com/", "_name": "ECHEERS", "_name_zh-cn": "壹企", "_name_zh-hk": "壹企", "_group": "12"}, {"key": 100684, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.echo.com/", "_name": "Echo Global Logistics", "_name_zh-cn": "Echo Global Logistics", "_name_zh-hk": "Echo Global Logistics", "_group": "11"}, {"key": 190168, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ecmsglobal.com/", "_name": "ECMS", "_name_zh-cn": "易客满", "_name_zh-hk": "易客滿", "_group": "12"}, {"key": 100099, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "https://ecomexpress.in/", "_name": "Ecom Express", "_name_zh-cn": "Ecom Express", "_name_zh-hk": "Ecom Express", "_group": "11"}, {"key": 100525, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "http://www.ecsspl.com/", "_name": "E-Com Shipping Solutions (P) Ltd", "_name_zh-cn": "E-Com Shipping Solutions (P) Ltd", "_name_zh-hk": "E-Com Shipping Solutions (P) Ltd", "_group": "11"}, {"key": 190486, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.e-commercekz.com/", "_name": "E-commerce KZ", "_name_zh-cn": "E-commerce KZ", "_name_zh-hk": "E-commerce KZ", "_group": "12"}, {"key": 100435, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "+34 912 15 93 69", "_url": "https://www.ecoscooting.com/", "_name": "Ecoscooting", "_name_zh-cn": "Ecoscooting", "_name_zh-hk": "Ecoscooting", "_group": "11"}, {"key": 100485, "_country": 104, "_country_iso": "DZ", "_email": "", "_tel": "+213 0770 766 218", "_url": "https://ecotrack.dz/", "_name": "ECOTRACK", "_name_zh-cn": "ECOTRACK", "_name_zh-hk": "ECOTRACK", "_group": "11"}, {"key": 100947, "_country": 203, "_country_iso": "BD", "_email": "<EMAIL>", "_tel": "+880 9642500500", "_url": "https://ecourier.com.bd/", "_name": "eCourier", "_name_zh-cn": "eCourier", "_name_zh-hk": "eCourier", "_group": "11"}, {"key": 191132, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.ecwharf.com/", "_name": "ECWHARF", "_name_zh-cn": "一达通运", "_name_zh-hk": "壹達通運", "_group": "12"}, {"key": 190376, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.666post.com/", "_name": "ED POST", "_name_zh-cn": "ED POST", "_name_zh-hk": "ED POST", "_group": "12"}, {"key": 191310, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86- 0755-29775820", "_url": "http://edasunil.com/", "_name": "EDASUN", "_name_zh-cn": "驿达昇", "_name_zh-hk": "驛達昇", "_group": "12"}, {"key": 190562, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.e-delivering.com/", "_name": "eDeliver", "_name_zh-cn": "递速易国际", "_name_zh-hk": "遞速易國際", "_group": "12"}, {"key": 100957, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.ediexpressinc.com/", "_name": "EDI Express Inc", "_name_zh-cn": "EDI Express Inc", "_name_zh-hk": "EDI Express Inc", "_group": "11"}, {"key": 190261, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.1dlexpress.com/", "_name": "EDL", "_name_zh-cn": "壹递诺", "_name_zh-hk": "壹遞諾", "_group": "12"}, {"key": 190423, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szedlon.com/", "_name": "Edlon Logistics", "_name_zh-cn": "誉德隆物流", "_name_zh-hk": "譽德隆物流", "_group": "12"}, {"key": 100306, "_country": 1105, "_country_iso": "KR", "_email": "<EMAIL>", "_tel": "+82 ************", "_url": "http://efs.asia/", "_name": "EFS (E-commerce Fulfillment Service)", "_name_zh-cn": "EFS (E-commerce Fulfillment Service)", "_name_zh-hk": "EFS (E-commerce Fulfillment Service)", "_group": "11"}, {"key": 5021, "_country": 502, "_country_iso": "EG", "_email": "", "_tel": "", "_url": "http://www.egyptpost.org/", "_name": "Egypt Post", "_name_zh-cn": "Egypt Post", "_name_zh-hk": "Egypt Post", "_group": ""}, {"key": 100056, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 1800 420 1111", "_url": "https://ekartlogistics.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100902, "_country": 1202, "_country_iso": "LV", "_email": "<EMAIL>", "_tel": "+371-67008008", "_url": "https://expresspasts.lv/", "_name": "Eksprespasta", "_name_zh-cn": "Eksprespasta", "_name_zh-hk": "Eksprespasta", "_group": "11"}, {"key": 100464, "_country": 2011, "_country_iso": "TR", "_email": "", "_tel": "+90 2124703737", "_url": "https://eliexpress.com.tr/", "_name": "Eli Express", "_name_zh-cn": "Eli Express", "_name_zh-hk": "Eli Express", "_group": "11"}, {"key": 191214, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-************", "_url": "http://www.elianpost.com/", "_name": "<PERSON>-lian", "_name_zh-cn": "易连供应链", "_name_zh-hk": "易連供應鏈", "_group": "12"}, {"key": 190392, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.elinex.cn/", "_name": "ELINEX", "_name_zh-cn": "ELINEX", "_name_zh-hk": "ELINEX", "_group": "10"}, {"key": 100647, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "+971 42 205 007", "_url": "https://www.go.elite-co.com/", "_name": "Elite Co.", "_name_zh-cn": "Elite Co.", "_name_zh-hk": "Elite Co.", "_group": "11"}, {"key": 7071, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "", "_url": "http://www.elta.gr/", "_name": "ELTA", "_name_zh-cn": "ELTA", "_name_zh-hk": "ELTA", "_group": ""}, {"key": 100179, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "", "_url": "https://www.elta-courier.gr/", "_name": "ELTA Courier", "_name_zh-cn": "ELTA Courier", "_name_zh-hk": "ELTA Courier", "_group": "11"}, {"key": 191139, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "*************", "_url": "http://www.emileps.com/", "_name": "Emile Corp", "_name_zh-cn": "亿迈尔", "_name_zh-hk": "億邁爾", "_group": "12"}, {"key": 5031, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "", "_url": "https://emiratespost.ae/", "_name": "Emirates Post", "_name_zh-cn": "Emirates Post", "_name_zh-hk": "Emirates Post", "_group": ""}, {"key": 101039, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://emons.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100685, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "https://www.ems.post/", "_name": "EMS (Express Mail Service)", "_name_zh-cn": "EMS (Express Mail Service)", "_name_zh-hk": "EMS (Express Mail Service)", "_group": "11"}, {"key": 100427, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-576-3279", "_url": "https://www.endicia.com/", "_name": "endicia", "_name_zh-cn": "endicia", "_name_zh-hk": "endicia", "_group": "11"}, {"key": 12011, "_country": 1201, "_country_iso": "LA", "_email": "", "_tel": "", "_url": "http://www.laopost.com.la/", "_name": "Enterprise des Poste Lao", "_name_zh-cn": "Enterprise des Poste Lao", "_name_zh-hk": "Enterprise des Poste Lao", "_group": ""}, {"key": 12016, "_country": 1201, "_country_iso": "LA", "_email": "", "_tel": "", "_url": "http://www.laopostapl.com/", "_name": "Enterprise des Poste Lao (APL)", "_name_zh-cn": "Enterprise des Poste Lao (APL)", "_name_zh-hk": "Enterprise des Poste Lao (APL)", "_group": ""}, {"key": 100822, "_country": 1607, "_country_iso": "PH", "_email": "<EMAIL>", "_tel": "", "_url": "https://entrego.com.ph/", "_name": "Entrego", "_name_zh-cn": "Entrego", "_name_zh-hk": "Entrego", "_group": "11"}, {"key": 100993, "_country": 313, "_country_iso": "CO", "_email": "<EMAIL>", "_tel": "+57 (1) 4954711", "_url": "https://envia.co/", "_name": "envía (envia)", "_name_zh-cn": "envía (envia)", "_name_zh-hk": "envía (envia)", "_group": "11"}, {"key": 100432, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "+34 902400909", "_url": "https://www.envialia.com/", "_name": "envialia", "_name_zh-cn": "envialia", "_name_zh-hk": "envialia", "_group": "11"}, {"key": 190836, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-29055033", "_url": "http://www.szenyuan.com/", "_name": "Enyuan International", "_name_zh-cn": "Enyuan International", "_name_zh-hk": "Enyuan International", "_group": "12"}, {"key": 191019, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 17367067086    ", "_url": "http://eparcelus.com/", "_name": "Eparcel", "_name_zh-cn": "意帕艘", "_name_zh-hk": "意帕艘", "_group": "12"}, {"key": 100718, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "", "_url": "https://eparcel.kr/", "_name": "eParcel Korea", "_name_zh-cn": "eParcel Korea", "_name_zh-hk": "eParcel Korea", "_group": "11"}, {"key": 190099, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.58epos.com/", "_name": "EPOS", "_name_zh-cn": "深圳宜友", "_name_zh-hk": "深圳宜友", "_group": "12"}, {"key": 190700, "_country": 906, "_country_iso": "IL", "_email": "", "_tel": "", "_url": "https://www.e-post.co.il/", "_name": "E-post", "_name_zh-cn": "E-post", "_name_zh-hk": "E-post", "_group": "12"}, {"key": 11051, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "", "_url": "http://www.epost.go.kr/", "_name": "ePOST (인터넷우체국)", "_name_zh-cn": "ePOST (인터넷우체국)", "_name_zh-hk": "ePOST (인터넷우체국)", "_group": ""}, {"key": 11054, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "", "_url": "https://service.epost.go.kr/iservice/usr/trace/usrtrc001k01.jsp", "_name": "ePOST (인터넷우체국)(Domestic)", "_name_zh-cn": "ePOST (인터넷우체국)(Domestic)", "_name_zh-hk": "ePOST (인터넷우체국)(Domestic)", "_group": ""}, {"key": 100028, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "****** 784 8485/****** 784 8485", "_url": "https://epostglobalshipping.com/", "_name": "ePost Global", "_name_zh-cn": "ePost Global", "_name_zh-hk": "ePost Global", "_group": "11"}, {"key": 190321, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.epsglobe.com/", "_name": "EPP", "_name_zh-cn": "EPP", "_name_zh-hk": "EPP", "_group": "10"}, {"key": 190028, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.epsglobe.com/", "_name": "EPS", "_name_zh-cn": "EPS", "_name_zh-hk": "EPS", "_group": "12"}, {"key": 190746, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-020-26290336", "_url": "http://www.eps56.com/", "_name": "EPS CROSS BORDER", "_name_zh-cn": "易办事跨境", "_name_zh-hk": "易办事跨境", "_group": "12"}, {"key": 190185, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.17post56.com/", "_name": "EQT", "_name_zh-cn": "EQT", "_name_zh-hk": "EQT", "_group": "12"}, {"key": 191088, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 400-6626-626", "_url": "http://www.equatorintl.com.cn/", "_name": "Equator Intl", "_name_zh-cn": "浙江赤道", "_name_zh-hk": "浙江赤道", "_group": "12"}, {"key": 190735, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-83207990", "_url": "http://chidaogroup.com/", "_name": "Equatorial Supply Chain", "_name_zh-cn": "赤道供应链", "_name_zh-hk": "赤道供應鏈", "_group": "12"}, {"key": 190136, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.equick.cn/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 5061, "_country": 506, "_country_iso": "ER", "_email": "", "_tel": "", "_url": "http://www.eriposta.com/", "_name": "Eritrea Post", "_name_zh-cn": "Eritrea Post", "_name_zh-hk": "Eritrea Post", "_group": ""}, {"key": 190289, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.escbest.com", "_name": "escbest", "_name_zh-cn": "escbest", "_name_zh-hk": "escbest", "_group": "12"}, {"key": 190929, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.szesd56.com/", "_name": "ESD", "_name_zh-cn": "ESD", "_name_zh-hk": "ESD", "_group": "12"}, {"key": 190242, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://e-se.cn/", "_name": "ESE", "_name_zh-cn": "俄速易", "_name_zh-hk": "俄速易", "_group": "10"}, {"key": 100627, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "+52 ************", "_url": "https://myeship.co/", "_name": "eShip", "_name_zh-cn": "eShip", "_name_zh-hk": "eShip", "_group": "11"}, {"key": 190539, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.eshiper.com/", "_name": "eShiper发件网", "_name_zh-cn": "eShiper发件网", "_name_zh-hk": "eShiper發件網", "_group": "12"}, {"key": 100428, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "****** 373 9222", "_url": "https://www.eshipper.com/", "_name": "eShipper", "_name_zh-cn": "eShipper", "_name_zh-hk": "eShipper", "_group": "11"}, {"key": 190075, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zes-express.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 100558, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://esiget.com/", "_name": "EsigetExpress", "_name_zh-cn": "EsigetExpress", "_name_zh-hk": "EsigetExpress", "_group": "11"}, {"key": 191221, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.flashexp.com.cn/", "_name": "ESL", "_name_zh-cn": "奕顺供应链", "_name_zh-hk": "奕順供應鏈", "_group": "12"}, {"key": 100103, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "************", "_url": "https://www.esnadexpress.com/", "_name": "Esnad Express", "_name_zh-cn": "Esnad Express", "_name_zh-hk": "Esnad Express", "_group": "11"}, {"key": 190444, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.espost.es/", "_name": "Espost", "_name_zh-cn": "Espost", "_name_zh-hk": "Espost", "_group": "12"}, {"key": 100139, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "+52 **************", "_url": "https://www.estafeta.com/", "_name": "Estafeta", "_name_zh-cn": "Estafeta", "_name_zh-hk": "Estafeta", "_group": "11"}, {"key": 100819, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://estafetausa.com/", "_name": "Estafeta USA", "_name_zh-cn": "Estafeta USA", "_name_zh-hk": "Estafeta USA", "_group": "11"}, {"key": 100221, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-378-3748", "_url": "https://www.estes-express.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100964, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "****** 433 9669", "_url": "https://efwnow.com/", "_name": "Estes Forwarding Worldwide", "_name_zh-cn": "Estes Forwarding Worldwide", "_name_zh-hk": "Estes Forwarding Worldwide", "_group": "11"}, {"key": 190275, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.8starexpress.com/", "_name": "ESX Logistics", "_name_zh-cn": "八星物流", "_name_zh-hk": "八星物流", "_group": "12"}, {"key": 190462, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.eteenlog.com/", "_name": "ETEEN", "_name_zh-cn": "ETEEN", "_name_zh-hk": "ETEEN", "_group": "12"}, {"key": 5051, "_country": 505, "_country_iso": "ET", "_email": "", "_tel": "", "_url": "http://www.ethiopostal.com/", "_name": "Ethiopian Post", "_name_zh-cn": "Ethiopian Post", "_name_zh-hk": "Ethiopian Post", "_group": ""}, {"key": 190209, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.paktrac.com/", "_name": "eTotal", "_name_zh-cn": "eTotal", "_name_zh-hk": "eTotal", "_group": "12"}, {"key": 191013, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.etouchsc.com/", "_name": "E-touch ", "_name_zh-cn": "易特达供应链", "_name_zh-hk": "易特達供應鏈", "_group": "12"}, {"key": 190017, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://ets-express.com/", "_name": "ETS", "_name_zh-cn": "ETS", "_name_zh-hk": "ETS", "_group": "12"}, {"key": 190906, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.e-youke.com/", "_name": "EUK", "_name_zh-cn": "易优客", "_name_zh-hk": "易優客", "_group": "12"}, {"key": 100196, "_country": 116, "_country_iso": "AT", "_email": "", "_tel": "", "_url": "https://eurodis.com/", "_name": "EURODIS", "_name_zh-cn": "EURODIS", "_name_zh-hk": "EURODIS", "_group": "11"}, {"key": 100526, "_country": 212, "_country_iso": "BA", "_email": "", "_tel": "", "_url": "https://www.euroexpress.ba/", "_name": "EuroExpress", "_name_zh-cn": "EuroExpress", "_name_zh-hk": "EuroExpress", "_group": "11"}, {"key": 190494, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.evertowin.com/", "_name": "EVERWIN", "_name_zh-cn": "EVERWIN", "_name_zh-hk": "EVERWIN", "_group": "12"}, {"key": 100331, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.evri.com/", "_name": "EVRi", "_name_zh-cn": "EVRi", "_name_zh-hk": "EVRi", "_group": "11"}, {"key": 190412, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ewe.com.au/", "_name": "EWE Global Express", "_name_zh-cn": "EWE全球物流", "_name_zh-hk": "EWE全球物流", "_group": "12"}, {"key": 190026, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://epost.8dt.com/", "_name": "EWS", "_name_zh-cn": "EWS", "_name_zh-hk": "EWS", "_group": "12"}, {"key": 100914, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "****** 363 9595", "_url": "https://www.exceltransportation.com/", "_name": "Excel Transportation", "_name_zh-cn": "Excel Transportation", "_name_zh-hk": "Excel Transportation", "_group": "11"}, {"key": 100994, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "", "_url": "https://exlinternationaldelivery.com/", "_name": "ExelDirect", "_name_zh-cn": "ExelDirect", "_name_zh-hk": "ExelDirect", "_group": "11"}, {"key": 100032, "_country": 906, "_country_iso": "IL", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.exelot.com/", "_name": "Exelot", "_name_zh-cn": "Exelot", "_name_zh-hk": "Exelot", "_group": "11"}, {"key": 100270, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.expeditors.com/", "_name": "Expeditors", "_name_zh-cn": "Expeditors", "_name_zh-hk": "Expeditors", "_group": "11"}, {"key": 190574, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.explus56.com", "_name": "ExPlus", "_name_zh-cn": "中田物流(南亚专线）", "_name_zh-hk": "中田物流(南亞專線）", "_group": "12"}, {"key": 100328, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+************ CALL US 24/7", "_url": "https://expresscourierintl.com/", "_name": "Express Courier International", "_name_zh-cn": "Express Courier International", "_name_zh-hk": "Express Courier International", "_group": "11"}, {"key": 100708, "_country": 1601, "_country_iso": "PK", "_email": "<EMAIL>", "_tel": "+92 21 03 111-222-408", "_url": "https://xpresscourierlink.com/", "_name": "Express Courier Link", "_name_zh-cn": "Express Courier Link", "_name_zh-hk": "Express Courier Link", "_group": "11"}, {"key": 100265, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "https://expressone.hu/", "_name": "Express One", "_name_zh-cn": "Express One", "_name_zh-hk": "Express One", "_group": "11"}, {"key": 199999, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "", "_name": "Express One (EN)", "_name_zh-cn": "Express One (EN)", "_name_zh-hk": "Express One (EN)", "_group": "12"}, {"key": 190493, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.exwworld.cn/", "_name": "ExwWorld", "_name_zh-cn": "沃德通", "_name_zh-hk": "沃德通", "_group": "12"}, {"key": 190969, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.fafalux.vip/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 100523, "_country": 805, "_country_iso": "HU", "_email": "<EMAIL>", "_tel": "+36-1-300-9500", "_url": "https://famafutar.hu/", "_name": "FamaFutar", "_name_zh-cn": "FamaFutar", "_name_zh-hk": "FamaFutar", "_group": "11"}, {"key": 100227, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "", "_url": "https://www.famiport.com.tw/", "_name": "FamiPort", "_name_zh-cn": "FamiPort", "_name_zh-hk": "FamiPort", "_group": "11"}, {"key": 100629, "_country": 1802, "_country_iso": "RO", "_email": "", "_tel": "+4021.9336", "_url": "https://www.fancourier.ro/", "_name": "FAN Courier", "_name_zh-cn": "FAN Courier", "_name_zh-hk": "FAN Courier", "_group": "11"}, {"key": 100800, "_country": 1802, "_country_iso": "RO", "_email": "", "_tel": "", "_url": "https://fancourier.eu/", "_name": "FAN Courier (EU)", "_name_zh-cn": "FAN Courier (EU)", "_name_zh-hk": "FAN Courier (EU)", "_group": "11"}, {"key": 190939, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 15858936273", "_url": "http://www.fswuliu716.com/", "_name": "<PERSON>", "_name_zh-cn": "帆盛物流", "_name_zh-hk": "帆盛物流", "_group": "12"}, {"key": 190691, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-020-31217440", "_url": "http://topfba56.com/", "_name": "FANKU", "_name_zh-cn": "FANKU", "_name_zh-hk": "FANKU", "_group": "12"}, {"key": 190152, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.far800.com/", "_name": "FAR International", "_name_zh-cn": "FAR International", "_name_zh-hk": "FAR International", "_group": "12"}, {"key": 100310, "_country": 2103, "_country_iso": "UZ", "_email": "", "_tel": "+998 71 200 00 37", "_url": "http://fargo.uz/", "_name": "FARGO", "_name_zh-cn": "FARGO", "_name_zh-hk": "FARGO", "_group": "11"}, {"key": 190399, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.fargoodexpress.com/", "_name": "fargood", "_name_zh-cn": "发个货", "_name_zh-hk": "發個貨", "_group": "12"}, {"key": 96021, "_country": 9602, "_country_iso": "FO", "_email": "", "_tel": "", "_url": "http://www.posta.fo/", "_name": "Faroe Post", "_name_zh-cn": "Faroe Post", "_name_zh-hk": "Faroe Post", "_group": ""}, {"key": 100779, "_country": 1917, "_country_iso": "ZA", "_email": "<EMAIL>", "_tel": "+27 ************", "_url": "https://www.fnf.co.za/", "_name": "Fast and Furious", "_name_zh-cn": "Fast and Furious", "_name_zh-hk": "Fast and Furious", "_group": "11"}, {"key": 100158, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://fastdespatch.com/", "_name": "Fast Despatch", "_name_zh-cn": "Fast Despatch", "_name_zh-hk": "Fast Despatch", "_group": "11"}, {"key": 100301, "_country": 2010, "_country_iso": "TN", "_email": "", "_tel": "+90 0212 5414410", "_url": "https://www.fast-express.com/en/", "_name": "FAST EXPRESS", "_name_zh-cn": "FAST EXPRESS", "_name_zh-hk": "FAST EXPRESS", "_group": "11"}, {"key": 100870, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-0161-2493033", "_url": "https://www.fastexpresscargo.com/", "_name": "Fast Express Cargo Pvt. Ltd", "_name_zh-cn": "Fast Express Cargo Pvt. Ltd", "_name_zh-hk": "Fast Express Cargo Pvt. Ltd", "_group": "11"}, {"key": 100882, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "", "_url": "https://au.fh.express/", "_name": "Fast Horse Express (AU)", "_name_zh-cn": "Fast Horse Express (AU)", "_name_zh-hk": "Fast Horse Express (AU)", "_group": "11"}, {"key": 100917, "_country": 1406, "_country_iso": "NZ", "_email": "<EMAIL>", "_tel": "", "_url": "https://nz.fh.express/", "_name": "Fast Horse Express (NZ)", "_name_zh-cn": "Fast Horse Express (NZ)", "_name_zh-hk": "Fast Horse Express (NZ)", "_group": "11"}, {"key": 100918, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://us.fh.express/", "_name": "Fast Horse Express (US)", "_name_zh-cn": "Fast Horse Express (US)", "_name_zh-hk": "Fast Horse Express (US)", "_group": "11"}, {"key": 100772, "_country": 1105, "_country_iso": "KR", "_email": "<EMAIL>", "_tel": "+82-1599-8962", "_url": "http://fastbox.co.kr/", "_name": "Fastbox (패스트박스)", "_name_zh-cn": "Fastbox (패스트박스)", "_name_zh-hk": "Fastbox (패스트박스)", "_group": "11"}, {"key": 101021, "_country": 2011, "_country_iso": "TR", "_email": "<EMAIL>", "_tel": "+90-850 242 32 78", "_url": "https://fastentegre.com/", "_name": "FastEntegre Shipment", "_name_zh-cn": "FastEntegre Shipment", "_name_zh-hk": "FastEntegre Shipment", "_group": "11"}, {"key": 190916, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 4007287156 ", "_url": "http://www.faster-net.site/", "_name": "faster", "_name_zh-cn": "快网", "_name_zh-hk": "快網", "_group": "12"}, {"key": 191180, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>/<EMAIL> ", "_tel": "", "_url": "http://www.faster.ae/", "_name": "Faster <PERSON><PERSON>", "_name_zh-cn": "Faster中东物流", "_name_zh-hk": "Faster中东物流", "_group": "12"}, {"key": 190541, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ************", "_url": "https://www.fastgo.com.au/", "_name": "Fastgo", "_name_zh-cn": "速派快递", "_name_zh-hk": "速派快遞", "_group": "12"}, {"key": 100844, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "", "_url": "https://fastiexpress.com/", "_name": "FASTIEXPRESS", "_name_zh-cn": "FASTIEXPRESS", "_name_zh-hk": "FASTIEXPRESS", "_group": "11"}, {"key": 100668, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+1 (909) 361‑5547", "_url": "https://fastlinkagexpress.com/", "_name": "Fastlinkagexpress", "_name_zh-cn": "Fastlinkagexpress", "_name_zh-hk": "Fastlinkagexpress", "_group": "11"}, {"key": 190814, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 400 856 8908", "_url": "http://www.fastsupply.cn/", "_name": "FASTSUPPLY", "_name_zh-cn": "FASTSUPPLY", "_name_zh-hk": "FASTSUPPLY", "_group": "12"}, {"key": 100068, "_country": 905, "_country_iso": "IE", "_email": "", "_tel": "", "_url": "http://www.fastway.ie/", "_name": "Fastway (IE)", "_name_zh-cn": "Fastway (IE)", "_name_zh-hk": "Fastway (IE)", "_group": "11"}, {"key": 100066, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "", "_url": "http://www.fastway.co.za/", "_name": "Fastway (ZA)", "_name_zh-cn": "Fastway (ZA)", "_name_zh-hk": "Fastway (ZA)", "_group": "11"}, {"key": 190337, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ferryboatlogistics.com/", "_name": "FBB", "_name_zh-cn": "摆渡一下", "_name_zh-hk": "擺渡一下", "_group": "12"}, {"key": 190697, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13246613367", "_url": "http://www.fcgjwl.com/", "_name": "FCJY", "_name_zh-cn": "风驰集运", "_name_zh-hk": "風馳集運", "_group": "12"}, {"key": 190314, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.fckjexpress.com/", "_name": "FCKJ", "_name_zh-cn": "飞驰供应链", "_name_zh-hk": "飛馳供應鏈", "_group": "10"}, {"key": 100003, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-463-3339", "_url": "https://www.fedex.com/", "_name": "FedEx", "_name_zh-cn": "FedEx", "_name_zh-hk": "FedEx", "_group": "11"}, {"key": 100854, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.fedex.com/fr-fr/tracking/domestic.html", "_name": "FedEx France Domestic", "_name_zh-cn": "FedEx France Domestic", "_name_zh-hk": "FedEx France Domestic", "_group": "11"}, {"key": 100841, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://crossborder.fedex.com/", "_name": "FedEx® Cross Border (UK) ", "_name_zh-cn": "FedEx®Cross Border (UK) ", "_name_zh-hk": "FedEx® Cross Border (UK) ", "_group": "11"}, {"key": 100222, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://fictracking.fedex.com/", "_name": "FedEx® International Connect (FIC)", "_name_zh-cn": "FedEx® International Connect (FIC)", "_name_zh-hk": "FedEx® International Connect (FIC)", "_group": "11"}, {"key": 100261, "_country": 1608, "_country_iso": "PL", "_email": "", "_tel": "", "_url": "https://www.fedex.com/pl-pl/home.html", "_name": "FedEx® Poland", "_name_zh-cn": "FedEx® Poland", "_name_zh-hk": "FedEx® Poland", "_group": "11"}, {"key": 190903, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0769—89360689", "_url": "http://www.jb-wwsc.com/", "_name": "Fengji Global", "_name_zh-cn": "丰集环球", "_name_zh-hk": "豐集環球", "_group": "12"}, {"key": 100458, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "+39 0471 530 000", "_url": "https://www.fercam.com/", "_name": "Fercam", "_name_zh-cn": "Fercam", "_name_zh-hk": "Fercam", "_group": "11"}, {"key": 100533, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://www.fermopoint.it/", "_name": "fermopoint", "_name_zh-cn": "fermopoint", "_name_zh-hk": "fermopoint", "_group": "11"}, {"key": 190788, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.yiyanjc.com/", "_name": "FF Express", "_name_zh-cn": "一言", "_name_zh-hk": "一言", "_group": "12"}, {"key": 100967, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "+49 (0)2571 999 0", "_url": "https://websped-netherlands.fiege.com/", "_name": "Fiege Netherlands", "_name_zh-cn": "Fiege Netherlands", "_name_zh-hk": "Fiege Netherlands", "_group": "11"}, {"key": 6031, "_country": 603, "_country_iso": "FJ", "_email": "", "_tel": "", "_url": "http://www.postfiji.com.fj/", "_name": "Fiji Post", "_name_zh-cn": "Fiji Post", "_name_zh-hk": "Fiji Post", "_group": ""}, {"key": 100682, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 022-62686666", "_url": "https://firstflightme.com/", "_name": "First Flight Couriers", "_name_zh-cn": "First Flight Couriers", "_name_zh-hk": "First Flight Couriers", "_group": "11"}, {"key": 100894, "_country": 1913, "_country_iso": "SG", "_email": "<EMAIL>", "_tel": "+65-65421109", "_url": "https://www.firstflight.com.sg/", "_name": "FIRST FLIGHT SINGAPORE", "_name_zh-cn": "FIRST FLIGHT SINGAPORE", "_name_zh-hk": "FIRST FLIGHT SINGAPORE", "_group": "11"}, {"key": 100990, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 ************", "_url": "https://firstflyexpress.com/", "_name": "First Fly Express", "_name_zh-cn": "First Fly Express", "_name_zh-hk": "First Fly Express", "_group": "11"}, {"key": 100393, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "https://www.firstgloballogistics.co.nz/", "_name": "First Global Logistics", "_name_zh-cn": "First Global Logistics", "_name_zh-hk": "First Global Logistics", "_group": "11"}, {"key": 190405, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.firstline56.com/", "_name": "FIRST LINE", "_name_zh-cn": "壹号专线", "_name_zh-hk": "壹號專線", "_group": "12"}, {"key": 191134, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>, <EMAIL>", "_tel": "", "_url": "https://ipostunion.com/", "_name": "FIRST UNION", "_name_zh-cn": "第一邮", "_name_zh-hk": "第壹郵", "_group": "12"}, {"key": 100191, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "", "_url": "https://firstmile.com/", "_name": "FirstMile", "_name_zh-cn": "FirstMile", "_name_zh-hk": "FirstMile", "_group": "11"}, {"key": 100853, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "+966 54 245 7271", "_url": "https://fizzpa.net/", "_name": "Fizzpa ( فيزبا)", "_name_zh-cn": "Fizzpa ( فيزبا)", "_name_zh-hk": "Fizzpa ( فيزبا)", "_group": "11"}, {"key": 190353, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.fujexp.com/", "_name": "FJEX", "_name_zh-cn": "FJEX", "_name_zh-hk": "FJEX", "_group": "10"}, {"key": 191075, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.fly-cnexp.com/", "_name": "FL", "_name_zh-cn": "富莱国际", "_name_zh-hk": "富萊國際", "_group": "12"}, {"key": 100482, "_country": 1201, "_country_iso": "LA", "_email": "<EMAIL>", "_tel": "+856 309651666", "_url": "https://www.flashexpress.la/", "_name": "Flash Express (LA)", "_name_zh-cn": "Flash Express (LA)", "_name_zh-hk": "Flash Express (LA)", "_group": "11"}, {"key": 100481, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+60 (01) 546 000 825", "_url": "https://www.flashexpress.my/", "_name": "Flash Express (MY)", "_name_zh-cn": "Flash Express (MY)", "_name_zh-hk": "Flash Express (MY)", "_group": "11"}, {"key": 100480, "_country": 1607, "_country_iso": "PH", "_email": "<EMAIL>", "_tel": "+63 (02) 8539-4002", "_url": "https://www.flashexpress.ph/", "_name": "Flash Express (PH)", "_name_zh-cn": "Flash Express (PH)", "_name_zh-hk": "Flash Express (PH)", "_group": "11"}, {"key": 100235, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "", "_url": "https://www.flashexpress.co.th/", "_name": "Flash Express (TH)", "_name_zh-cn": "Flash Express (TH)", "_name_zh-hk": "Flash Express (TH)", "_group": "11"}, {"key": 190981, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "https://www.fleetan.com/", "_name": "FLEETAN", "_name_zh-cn": "飞坦数字物流", "_name_zh-hk": "飛坦數字物流", "_group": "12"}, {"key": 191298, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "******2220528", "_url": "https://fleetexpress.ca/", "_name": "FleetEx", "_name_zh-cn": "飞翼速递", "_name_zh-hk": "飛翼速遞", "_group": "12"}, {"key": 101035, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://fleetopticsinc.com/", "_name": "FleetOptics", "_name_zh-cn": "FleetOptics", "_name_zh-hk": "FleetOptics", "_group": "11"}, {"key": 100651, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "+60 1-800-817-278", "_url": "https://flickpost.co/", "_name": "Flickpost", "_name_zh-cn": "Flickpost", "_name_zh-hk": "Flickpost", "_group": "11"}, {"key": 100986, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 (0)1753 763 500", "_url": "https://www.flightlg.com/", "_name": "Flight Logistics", "_name_zh-cn": "Flight Logistics", "_name_zh-hk": "Flight Logistics", "_group": "11"}, {"key": 100094, "_country": 1803, "_country_iso": "RU", "_email": "<EMAIL>", "_tel": "+7 (495) 788-55-08", "_url": "https://flippost.com/", "_name": "Flip Post", "_name_zh-cn": "Flip Post", "_name_zh-hk": "Flip Post", "_group": "11"}, {"key": 100654, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "+64 0800 354 929", "_url": "https://fliway.com/", "_name": "Fliway", "_name_zh-cn": "Fliway", "_name_zh-hk": "Fliway", "_group": "11"}, {"key": 190731, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18017851883", "_url": "http://fls.aprche.net/", "_name": "FLS", "_name_zh-cn": "福来讯", "_name_zh-hk": "福來訊", "_group": "12"}, {"key": 190932, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.flyfastgz.com/ ", "_name": "Flyfast", "_name_zh-cn": "乘先", "_name_zh-hk": "乘先", "_group": "12"}, {"key": 100937, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 22 49710910 ", "_url": "http://www.flyking.in/", "_name": "Flyking", "_name_zh-cn": "Flyking", "_name_zh-hk": "Flyking", "_group": "11"}, {"key": 190002, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.flytexpress.com/", "_name": "FLYT", "_name_zh-cn": "飞特物流", "_name_zh-hk": "飛特物流", "_group": "12"}, {"key": 100532, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "", "_url": "https://www.fmx.asia/", "_name": "FMX", "_name_zh-cn": "FMX", "_name_zh-hk": "FMX", "_group": "11"}, {"key": 191061, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 17711737478", "_url": "https://www.forestleopard.com/", "_name": "ForestLeopard", "_name_zh-cn": "鑫豹国际", "_name_zh-hk": "鑫豹國際", "_group": "12"}, {"key": 100817, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL> ", "_tel": "+****************", "_url": "https://www.forwardair.com/", "_name": "Forward", "_name_zh-cn": "Forward", "_name_zh-hk": "Forward", "_group": "11"}, {"key": 100513, "_country": 712, "_country_iso": "GT", "_email": "<EMAIL>", "_tel": "+502 2377-5300", "_url": "https://forzadelivery.com/", "_name": "Forza Delivery", "_name_zh-cn": "Forza Delivery", "_name_zh-hk": "Forza Delivery", "_group": "11"}, {"key": 190322, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.fourseasonsfly.net/", "_name": "Four Seasons", "_name_zh-cn": "四季正扬", "_name_zh-hk": "四季正揚", "_group": "12"}, {"key": 100380, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "https://www.foxpost.hu/", "_name": "FOXPOST", "_name_zh-cn": "FOXPOST", "_name_zh-hk": "FOXPOST", "_group": "11"}, {"key": 100234, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "http://www.fpslogistics.in/", "_name": "FPS Logistics", "_name_zh-cn": "FPS Logistics", "_name_zh-hk": "FPS Logistics", "_group": "11"}, {"key": 100936, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.france-express.com/", "_name": "France Express", "_name_zh-cn": "France Express", "_name_zh-hk": "France Express", "_group": "11"}, {"key": 100602, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 4466332333", "_url": "http://www.franchexpress.com/", "_name": "Franch Express", "_name_zh-cn": "Franch Express", "_name_zh-hk": "Franch Express", "_group": "11"}, {"key": 190505, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.frayun.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "法运", "_name_zh-hk": "法运", "_group": "12"}, {"key": 191122, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://freedomscm.com", "_name": "Freedom SCM", "_name_zh-cn": "飞点跨境供应链", "_name_zh-hk": "飛點跨境供應商", "_group": "12"}, {"key": 100773, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******.323.5441", "_url": "https://www.freightquote.com/", "_name": "Freightquote by <PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "Freightquote by <PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "Freightquote by <PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100899, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+1 (800)243-5422", "_url": "http://frontlinefreightinc.com/", "_name": "Frontline Freight", "_name_zh-cn": "Frontline Freight", "_name_zh-hk": "Frontline Freight", "_group": "11"}, {"key": 191015, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.fqd56.com/", "_name": "FSD", "_name_zh-cn": "凡士达", "_name_zh-hk": "凡士達", "_group": "12"}, {"key": 191084, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.shenzhenshifeisuda.com/", "_name": "FSD", "_name_zh-cn": "飞速达", "_name_zh-hk": "飛速達", "_group": "12"}, {"key": 190371, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://kd1913.com/", "_name": "FSGJ", "_name_zh-cn": "飞速国际", "_name_zh-hk": "飛速國際", "_group": "12"}, {"key": 190727, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.fsqh.hailei2018.com/Default.aspx", "_name": "FSQH", "_name_zh-cn": "佛山乾和", "_name_zh-hk": "佛山乾和", "_group": "12"}, {"key": 191365, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-4009008801", "_url": "https://www.gzfst.cn/", "_name": "FSTCARGO", "_name_zh-cn": "法斯特国际货运", "_name_zh-hk": "法斯特國際貨運", "_group": "12"}, {"key": 100681, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "+ 1300639999", "_url": "http://www.fstexpress.com.au/", "_name": "Fstexpress", "_name_zh-cn": "Fstexpress", "_name_zh-hk": "Fstexpress", "_group": "11"}, {"key": 191246, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>    ", "_tel": "", "_url": "http://www.szfd56.com/", "_name": "Fudu Logistics", "_name_zh-cn": "福都物流", "_name_zh-hk": "福都物流", "_group": "12"}, {"key": 190591, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.fuhai618.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "富海物联", "_name_zh-hk": "富海物聯", "_group": "12"}, {"key": 100599, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "https://corp.fukutsu.co.jp/", "_name": "Fukuyama Transporting (福山通運)", "_name_zh-cn": "福山通运", "_name_zh-hk": "福山通運", "_group": "11"}, {"key": 100760, "_country": 9006, "_country_iso": "GI", "_email": "<EMAIL>", "_tel": "", "_url": "https://fulfilla.net/", "_name": "Fulfilla", "_name_zh-cn": "Fulfilla", "_name_zh-hk": "Fulfilla", "_group": "11"}, {"key": 190534, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.fulfillant.com/", "_name": "Fulfillant", "_name_zh-cn": "Fulfillant", "_name_zh-hk": "Fulfillant", "_group": "12"}, {"key": 190528, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.fulfillmen.com/", "_name": "FULFILLMEN", "_name_zh-cn": "中田物流", "_name_zh-hk": "中田物流", "_group": "12"}, {"key": 101043, "_country": 801, "_country_iso": "HK", "_email": " <EMAIL>", "_tel": "+852-3180 7616", "_url": "https://fulfillmentbridge.com/", "_name": "Fulfillment Bridge", "_name_zh-cn": "Fulfillment Bridge", "_name_zh-hk": "Fulfillment Bridge", "_group": "11"}, {"key": 100881, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-0121 285 5255", "_url": "https://furdeco.co.uk/", "_name": "Furdeco", "_name_zh-cn": "Furdeco", "_name_zh-hk": "Furdeco", "_group": "11"}, {"key": 191266, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.fzex.cc/", "_name": "FZEX", "_name_zh-cn": "丰洲国际物流", "_name_zh-hk": "豐洲國際物流", "_group": "12"}, {"key": 191074, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 17757964777", "_url": "http://www.fzrexp.com/", "_name": "FZR", "_name_zh-cn": "帆之润国际", "_name_zh-hk": "帆之潤國際", "_group": "12"}, {"key": 100145, "_country": 906, "_country_iso": "IL", "_email": "<EMAIL>", "_tel": "072-2608000", "_url": "https://gaashwd.com/", "_name": "GAASH Worldwide", "_name_zh-cn": "GAASH Worldwide", "_name_zh-hk": "GAASH Worldwide", "_group": "11"}, {"key": 190381, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gaeaex.com/", "_name": "GAEA", "_name_zh-cn": "盖亚物流", "_name_zh-hk": "蓋亞物流", "_group": "12"}, {"key": 190901, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.cngood56.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "贛德", "_name_zh-hk": "贛德", "_group": "12"}, {"key": 100317, "_country": 1404, "_country_iso": "NL", "_email": "<EMAIL>", "_tel": "+31 635250407", "_url": "https://www.gatelinklogistics.com/", "_name": "Gate Link Logistics", "_name_zh-cn": "Gate Link Logistics", "_name_zh-hk": "Gate Link Logistics", "_group": "11"}, {"key": 100717, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 0712-7144284", "_url": "https://www.gatikwe.com/", "_name": "Gati", "_name_zh-cn": "Gati", "_name_zh-hk": "Gati", "_group": "11"}, {"key": 190119, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gaticn.com/", "_name": "Gati (CN)", "_name_zh-cn": "Gati (CN)", "_name_zh-hk": "Gati (CN)", "_group": "12"}, {"key": 191190, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-23093973", "_url": "https://web.eg-dex.com/", "_name": "G-BILLION", "_name_zh-cn": "玖玖亿跨境物流", "_name_zh-hk": "玖玖億跨境物流", "_group": "12"}, {"key": 190953, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18617161075", "_url": "http://www.q9610.com/", "_name": "GBS", "_name_zh-cn": "港宝国际物流", "_name_zh-hk": "港寶國際物流", "_group": "12"}, {"key": 100313, "_country": 1803, "_country_iso": "RU", "_email": "<EMAIL>", "_tel": "****** 790-58-85", "_url": "https://gbs-broker.ru/", "_name": "GBS-Broker", "_name_zh-cn": "GBS-Broker", "_name_zh-hk": "GBS-Broker", "_group": "11"}, {"key": 100754, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "+81 0120-788155", "_url": "https://gbtec722.co.jp/", "_name": "GBtechnology", "_name_zh-cn": "GBtechnology", "_name_zh-hk": "GBtechnology", "_group": "11"}, {"key": 191259, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gcc56.cn/", "_name": "GCC56", "_name_zh-cn": "GCC56", "_name_zh-hk": "GCC56", "_group": "12"}, {"key": 190305, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gcmy56.com/", "_name": "GCT", "_name_zh-cn": "GCT", "_name_zh-hk": "GCT", "_group": "10"}, {"key": 100173, "_country": 906, "_country_iso": "IL", "_email": "", "_tel": "", "_url": "https://gcx.co.il/home/", "_name": "GCX", "_name_zh-cn": "GCX", "_name_zh-hk": "GCX", "_group": "11"}, {"key": 100150, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "*****-6419 5003", "_url": "https://www.gdexpress.com/", "_name": "GD Express", "_name_zh-cn": "GD Express", "_name_zh-hk": "GD Express", "_group": "11"}, {"key": 190806, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-021-52273009", "_url": "http://www.gde56.com/", "_name": "GDE", "_name_zh-cn": "寄递易", "_name_zh-hk": "寄遞易", "_group": "12"}, {"key": 191060, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "http://www.fd-exp.com/", "_name": "GDJN", "_name_zh-cn": "高登捷诺", "_name_zh-hk": "高登捷諾", "_group": "12"}, {"key": 190754, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-15814147227", "_url": "https://www.gdyhwl.cn/", "_name": "Gdyhwl", "_name_zh-cn": "广东盈寰物流", "_name_zh-hk": "广东盈寰物流", "_group": "12"}, {"key": 100431, "_country": 116, "_country_iso": "AT", "_email": "<EMAIL>", "_tel": "+43.5574.696.0", "_url": "https://www.gw-world.com/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (GW)", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON><PERSON> (GW)", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON><PERSON> (GW)", "_group": "11"}, {"key": 100424, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.gefco.net/en/", "_name": "Gefco", "_name_zh-cn": "Gefco", "_name_zh-hk": "Gefco", "_group": "11"}, {"key": 100141, "_country": 322, "_country_iso": "CZ", "_email": "", "_tel": "", "_url": "https://www.geis-group.cz/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100396, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.gel-express.de/", "_name": "GEL Express Logistik", "_name_zh-cn": "GEL Express Logistik", "_name_zh-hk": "GEL Express Logistik", "_group": "11"}, {"key": 90021, "_country": 9002, "_country_iso": "AI", "_email": "", "_tel": "", "_url": "http://www.aps.ai/", "_name": "General Post Office", "_name_zh-cn": "General Post Office", "_name_zh-hk": "General Post Office", "_group": ""}, {"key": 100224, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "+30 210 4851100", "_url": "https://www.taxydromiki.com/en/", "_name": "Geniki Taxydromiki", "_name_zh-cn": "Geniki Taxydromiki", "_name_zh-hk": "Geniki Taxydromiki", "_group": "11"}, {"key": 100962, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "+52-************", "_url": "https://www.genka.mx/", "_name": "Genka Express", "_name_zh-cn": "Genka Express", "_name_zh-hk": "Genka Express", "_group": "11"}, {"key": 100356, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "http://www.geodis.com/", "_name": "GEODIS", "_name_zh-cn": "GEODIS", "_name_zh-hk": "GEODIS", "_group": "11"}, {"key": 7031, "_country": 703, "_country_iso": "GE", "_email": "", "_tel": "", "_url": "http://gpost.ge/", "_name": "Georgian Post", "_name_zh-cn": "Georgian Post", "_name_zh-hk": "Georgian Post", "_group": ""}, {"key": 191046, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0592-2611063", "_url": "http://www.xmgesc.cn/", "_name": "GESC", "_name_zh-cn": "环球递", "_name_zh-hk": "環球遞", "_group": "12"}, {"key": 100575, "_country": 215, "_country_iso": "BR", "_email": "", "_tel": "", "_url": "http://www.gflogistica.com.br/", "_name": "GFL Logística", "_name_zh-cn": "GFL Logística", "_name_zh-hk": "GFL Logística", "_group": "11"}, {"key": 190672, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.grand-freight.com/", "_name": "GFS", "_name_zh-cn": "鹏远国际", "_name_zh-hk": "鵬遠國際", "_group": "12"}, {"key": 100578, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://seeker.gfsdeliver.com/", "_name": "GFS Seeker", "_name_zh-cn": "GFS Seeker", "_name_zh-hk": "GFS Seeker", "_group": "11"}, {"key": 100723, "_country": 1109, "_country_iso": "KG", "_email": "", "_tel": "+966 8001240177", "_url": "http://www.gfsxpress.com/", "_name": "GFS Xpress", "_name_zh-cn": "GFS Xpress", "_name_zh-hk": "GFS Xpress", "_group": "11"}, {"key": 191349, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.szgg56.com/", "_name": "GG", "_name_zh-cn": "巩巩国际", "_name_zh-hk": "鞏鞏國際", "_group": "12"}, {"key": 7051, "_country": 705, "_country_iso": "GH", "_email": "", "_tel": "", "_url": "http://www.ghanapost.com.gh/", "_name": "Ghana Post", "_name_zh-cn": "Ghana Post", "_name_zh-hk": "Ghana Post", "_group": ""}, {"key": 191208, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.expghd.com/", "_name": "GHD", "_name_zh-cn": "高航达", "_name_zh-hk": "高航達", "_group": "12"}, {"key": 190656, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ghlcn.com/", "_name": "GHL", "_name_zh-cn": "广和物流", "_name_zh-hk": "廣和物流", "_group": "12"}, {"key": 100202, "_country": 206, "_country_iso": "BE", "_email": "", "_tel": "", "_url": "https://ghllogistics.be/", "_name": "GHL Logistics", "_name_zh-cn": "GHL Logistics", "_name_zh-hk": "GHL Logistics", "_group": "11"}, {"key": 100593, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "+84 1900 636677", "_url": "https://ghn.vn/", "_name": "GHN (Giao Hàng Nhanh)", "_name_zh-cn": "GHN (Giao Hàng Nhanh)", "_name_zh-hk": "GHN (Giao Hàng Nhanh)", "_group": "11"}, {"key": 90061, "_country": 9006, "_country_iso": "GI", "_email": "", "_tel": "", "_url": "http://www.royalgibraltar.post/", "_name": "Gibraltar Post", "_name_zh-cn": "Gibraltar Post", "_name_zh-hk": "Gibraltar Post", "_group": ""}, {"key": 101034, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.gigacloudtech.com/", "_name": "GigaCloud Technology Inc", "_name_zh-cn": "GigaCloud Technology Inc", "_name_zh-hk": "GigaCloud Technology Inc", "_group": "11"}, {"key": 190398, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gimen56.com/", "_name": "GIMEN", "_name_zh-cn": "巨门跨境", "_name_zh-hk": "巨門跨境", "_group": "10"}, {"key": 100960, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://gio.e-courier.com/", "_name": "Gio Express", "_name_zh-cn": "Gio Express", "_name_zh-hk": "Gio Express", "_group": "11"}, {"key": 100169, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://glav-dostavka.ru/", "_name": "GlavDostavka", "_name_zh-cn": "https://www.mrw.es/", "_name_zh-hk": "GlavDostavka", "_group": "11"}, {"key": 190879, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-36698039", "_url": "http://www.glex.net.cn/", "_name": "GLEX", "_name_zh-cn": "远东国际", "_name_zh-hk": "遠東國際", "_group": "12"}, {"key": 100739, "_country": 2001, "_country_iso": "TW", "_email": "<EMAIL>", "_tel": "+886  449-8856", "_url": "https://www.global-business.com.tw/", "_name": "Global Commercial Technology Co.,LTD.", "_name_zh-cn": "全球快递", "_name_zh-hk": "全球快遞", "_group": "11"}, {"key": 100983, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://globaleco.app/", "_name": "Global Eco", "_name_zh-cn": "Global Eco", "_name_zh-hk": "Global Eco", "_group": "11"}, {"key": 100470, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "http://tongtuexpress.com/", "_name": "Global Express", "_name_zh-cn": "Global Express", "_name_zh-hk": "Global Express", "_group": "11"}, {"key": 191359, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.huanqiuabc.com/", "_name": "Global Logistics", "_name_zh-cn": "环球快运", "_name_zh-hk": "環球快運", "_group": "12"}, {"key": 100429, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+1 (888) 899‑1255", "_url": "https://www.goglobalpost.com/", "_name": "Global Post", "_name_zh-cn": "Global Post", "_name_zh-hk": "Global Post", "_group": "11"}, {"key": 100300, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34 925 51 25 89", "_url": "https://www.globaltrans.es/", "_name": "GLOBALTRANS", "_name_zh-cn": "GLOBALTRANS", "_name_zh-hk": "GLOBALTRANS", "_group": "11"}, {"key": 100873, "_country": 1608, "_country_iso": "PL", "_email": "<EMAIL>", "_tel": "+48 22 300 16 21", "_url": "https://www.globkurier.pl/", "_name": "GlobKurier.pl", "_name_zh-cn": "GlobKurier.pl", "_name_zh-hk": "GlobKurier.pl", "_group": "11"}, {"key": 100433, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://glovoapp.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100005, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://gls-group.eu/EU/en/home", "_name": "GLS", "_name_zh-cn": "GLS", "_name_zh-hk": "GLS", "_group": "11"}, {"key": 100207, "_country": 319, "_country_iso": "HR", "_email": "", "_tel": "", "_url": "https://gls-group.eu/HR/en/home", "_name": "GLS (Croatia)", "_name_zh-cn": "GLS (Croatia)", "_name_zh-hk": "GLS (Croatia)", "_group": "11"}, {"key": 100281, "_country": 322, "_country_iso": "CZ", "_email": "", "_tel": "", "_url": "https://gls-group.eu/CZ/en/home", "_name": "GLS (CZ)", "_name_zh-cn": "GLS (CZ)", "_name_zh-hk": "GLS (CZ)", "_group": "11"}, {"key": 100280, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "https://gls-group.eu/HU/en/home", "_name": "GLS (HU)", "_name_zh-cn": "GLS (HU)", "_name_zh-hk": "GLS (HU)", "_group": "11"}, {"key": 100024, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "+****************", "_url": "https://www.gls-italy.com/", "_name": "GLS (IT)", "_name_zh-cn": "GLS (IT)", "_name_zh-hk": "GLS (IT)", "_group": "11"}, {"key": 100384, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "https://www.gls-info.nl/", "_name": "GLS (NL)", "_name_zh-cn": "GLS (NL)", "_name_zh-hk": "GLS (NL)", "_group": "11"}, {"key": 100316, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "+351 219667100", "_url": "https://www.gls-portugal.pt/", "_name": "GLS (PT)", "_name_zh-cn": "GLS (PT)", "_name_zh-hk": "GLS (PT)", "_group": "11"}, {"key": 100284, "_country": 1802, "_country_iso": "RO", "_email": "", "_tel": "", "_url": "https://gls-group.eu/RO/en/home", "_name": "GLS (RO)", "_name_zh-cn": "GLS (RO)", "_name_zh-hk": "GLS (RO)", "_group": "11"}, {"key": 100283, "_country": 1915, "_country_iso": "SI", "_email": "", "_tel": "", "_url": "https://gls-group.eu/SI/en/home", "_name": "GLS (SI)", "_name_zh-cn": "GLS (SI)", "_name_zh-hk": "GLS (SI)", "_group": "11"}, {"key": 100282, "_country": 1914, "_country_iso": "SK", "_email": "", "_tel": "", "_url": "https://gls-group.eu/SK/en/home", "_name": "GLS (SK)", "_name_zh-cn": "GLS (SK)", "_name_zh-hk": "GLS (SK)", "_group": "11"}, {"key": 100305, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.gls-us.com/", "_name": "GLS (US)", "_name_zh-cn": "GLS (US)", "_name_zh-hk": "GLS (US)", "_group": "11"}, {"key": 100208, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "******-463-4266", "_url": "https://gls-canada.com/", "_name": "GLS Canada (Dicom)", "_name_zh-cn": "GLS Canada (Dicom)", "_name_zh-hk": "GLS Canada (Dicom)", "_group": "11"}, {"key": 100646, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "+351 219667100", "_url": "https://www.gls-portugal.pt/", "_name": "GLS Portugal (National)", "_name_zh-cn": "GLS Portugal (National)", "_name_zh-hk": "GLS Portugal (National)", "_group": "11"}, {"key": 100189, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.gls-spain.es/en/", "_name": "GLS Spain (National)", "_name_zh-cn": "GLS Spain (National)", "_name_zh-hk": "GLS Spain (National)", "_group": "11"}, {"key": 100545, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://gltmena.com/", "_name": "GLT Express", "_name_zh-cn": "GLT Express", "_name_zh-hk": "GLT Express", "_group": "11"}, {"key": 100953, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-9886848640", "_url": "https://www.gmsworldwide.com/", "_name": "GMS Worldwide Express", "_name_zh-cn": "GMS Worldwide Express", "_name_zh-hk": "GMS Worldwide Express", "_group": "11"}, {"key": 101064, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61-(02) 9748 5000", "_url": "https://gologistics.com.au/", "_name": "Go Logistics", "_name_zh-cn": "Go Logistics", "_name_zh-hk": "Go Logistics", "_group": "11"}, {"key": 100963, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "49-0800 / 859 99 99", "_url": "https://www.general-overnight.com/", "_name": "GO! Express", "_name_zh-cn": "GO! Express", "_name_zh-hk": "GO! Express", "_group": "11"}, {"key": 100411, "_country": 1914, "_country_iso": "SK", "_email": "", "_tel": "", "_url": "https://www.go4.sk/", "_name": "Go4", "_name_zh-cn": "Go4", "_name_zh-hk": "Go4", "_group": "11"}, {"key": 190623, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gofastcn.com/", "_name": "GoFast", "_name_zh-cn": "够快供应链", "_name_zh-hk": "夠快供應鏈", "_group": "12"}, {"key": 100996, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.gofoexpress.com/", "_name": "GOFO Express", "_name_zh-cn": "GOFO Express", "_name_zh-hk": "GOFO Express", "_group": "11"}, {"key": 100421, "_country": 1607, "_country_iso": "PH", "_email": "", "_tel": "", "_url": "https://app.gogoxpress.com/track", "_name": "GoGo Xpress", "_name_zh-cn": "GoGo Xpress", "_name_zh-hk": "GoGo Xpress", "_group": "11"}, {"key": 190786, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-021-52273009", "_url": "https://www.goodexpress.com.cn/", "_name": "GOOD", "_name_zh-cn": "GOOD", "_name_zh-hk": "GOOD", "_group": "12"}, {"key": 191119, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.hygzgj.com/", "_name": "Good luck International", "_name_zh-cn": "好运国际", "_name_zh-hk": "好運國際", "_group": "12"}, {"key": 100624, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 033-2243-1086", "_url": "https://www.goodluckcourier.com/", "_name": "Goodluck Courier Service", "_name_zh-cn": "Goodluck Courier Service", "_name_zh-hk": "Goodluck Courier Service", "_group": "11"}, {"key": 190723, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.china-grt.com/", "_name": "GORTO", "_name_zh-cn": "国润通", "_name_zh-hk": "國潤通", "_group": "12"}, {"key": 100539, "_country": 216, "_country_iso": "BN", "_email": "", "_tel": "", "_url": "https://www.gorushbn.com/", "_name": "GoRush", "_name_zh-cn": "GoRush", "_name_zh-hk": "GoRush", "_group": "11"}, {"key": 190663, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.gotofreight.com/", "_name": "Gotofreight", "_name_zh-cn": "运连网", "_name_zh-hk": "運連網", "_group": "12"}, {"key": 191115, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "https://www.goxyl.com/", "_name": "GOX", "_name_zh-cn": "高迅国际", "_name_zh-hk": "高迅國際", "_group": "12"}, {"key": 100133, "_country": 1410, "_country_iso": "NG", "_email": "", "_tel": "", "_url": "https://gpd-service.online/ ", "_name": "GPD Service", "_name_zh-cn": "GPD Service", "_name_zh-hk": "GPD Service", "_group": "11"}, {"key": 190761, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0551-65571185", "_url": "http://www.qcdlex.com/", "_name": "GPL", "_name_zh-cn": "全程动力", "_name_zh-hk": "全程動力", "_group": "12"}, {"key": 190860, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.goforgps.com/", "_name": "GPS", "_name_zh-cn": "凌曦物流", "_name_zh-hk": "靈犀物流", "_group": "12"}, {"key": 100156, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "http://grandslamexpress.in/", "_name": "Grand Slam Express", "_name_zh-cn": "Grand Slam Express", "_name_zh-hk": "Grand Slam Express", "_group": "11"}, {"key": 190506, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://hk-ols.granful.cn/", "_name": "Granful Solutions Ltd", "_name_zh-cn": "Granful Solutions Ltd", "_name_zh-hk": "Granful Solutions Ltd", "_group": "12"}, {"key": 100105, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://grastin.ru/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 191321, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-0755-28269845 ", "_url": "http://www.greatsell.cn/", "_name": "Greatsell", "_name_zh-cn": "大卖仓", "_name_zh-hk": "大賣倉", "_group": "12"}, {"key": 191320, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://siyuanck.com/", "_name": "Greencargo", "_name_zh-cn": "思远国际", "_name_zh-hk": "思遠國際", "_group": "12"}, {"key": 101057, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "+84-0528992789", "_url": "https://greenwayvn.com/", "_name": "Greenway", "_name_zh-cn": "Greenway", "_name_zh-hk": "Greenway", "_group": "11"}, {"key": 100656, "_country": 905, "_country_iso": "IE", "_email": "", "_tel": "", "_url": "https://greenwaycouriers.ie/", "_name": "Green-Way couriers", "_name_zh-cn": "Green-Way couriers", "_name_zh-hk": "Green-Way couriers", "_group": "11"}, {"key": 100299, "_country": 2011, "_country_iso": "TR", "_email": "", "_tel": "", "_url": "http://www.grupoampm.com/", "_name": "Grupo ampm", "_name_zh-cn": "Grupo ampm", "_name_zh-hk": "Grupo ampm", "_group": "11"}, {"key": 100866, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "+52-************", "_url": "https://www.castores.com.mx/", "_name": "Grupo Castores", "_name_zh-cn": "Grupo Castores", "_name_zh-hk": "Grupo Castores", "_group": "11"}, {"key": 100874, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+82-02-2006-3999", "_url": "https://www.cvsnet.co.kr/", "_name": "GS Networks (GS네트웍스)", "_name_zh-cn": "GS Postbox (GS네트웍스)", "_name_zh-hk": "GS Postbox (GS네트웍스)", "_group": "11"}, {"key": 100542, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://gtxpressdeliver.com/", "_name": "GT Xpress", "_name_zh-cn": "GT Xpress", "_name_zh-hk": "GT Xpress", "_group": "11"}, {"key": 100118, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://gtdel.com/", "_name": "GTD", "_name_zh-cn": "GTD", "_name_zh-hk": "GTD", "_group": "11"}, {"key": 190637, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gti56.com/", "_name": "GTI", "_name_zh-cn": "跨境易通", "_name_zh-hk": "跨境易通", "_group": "12"}, {"key": 100076, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "http://www.gtsexpress.com/", "_name": "GTS Express", "_name_zh-cn": "GTS Express", "_name_zh-hk": "GTS Express", "_group": "11"}, {"key": 100516, "_country": 712, "_country_iso": "GT", "_email": "", "_tel": "", "_url": "https://guatex.com/guatex/rastreo-de-guias/", "_name": "Guatex", "_name_zh-cn": "Guatex", "_name_zh-hk": "Guatex", "_group": "11"}, {"key": 191141, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://hrbguoo.com/", "_name": "GUOO", "_name_zh-cn": "GUOO", "_name_zh-hk": "GUOO", "_group": "12"}, {"key": 7141, "_country": 714, "_country_iso": "GY", "_email": "", "_tel": "", "_url": "http://guypost.gy/", "_name": "Guyana Post", "_name_zh-cn": "Guyana Post", "_name_zh-hk": "Guyana Post", "_group": ""}, {"key": 100203, "_country": 801, "_country_iso": "HK", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.globavend.com/", "_name": "GV", "_name_zh-cn": "GV", "_name_zh-hk": "GV", "_group": "11"}, {"key": 190183, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gaopost.com/", "_name": "GXA", "_name_zh-cn": "GXA", "_name_zh-hk": "GXA", "_group": "10"}, {"key": 190728, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.guangyy56.com/", "_name": "GYY", "_name_zh-cn": "广运源供应链", "_name_zh-hk": "廣運源供應鏈", "_group": "12"}, {"key": 190765, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-020-31238309", "_url": "http://www.flqq56.com/", "_name": "GZFL", "_name_zh-cn": "广州方联物流", "_name_zh-hk": "廣州方聯物流", "_group": "12"}, {"key": 190695, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18039073131", "_url": "http://hyytgz.com/", "_name": "GZHY", "_name_zh-cn": "广州华亚", "_name_zh-hk": "廣州華亞", "_group": "12"}, {"key": 190651, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gd11183.cn/", "_name": "GZSZHHY", "_name_zh-cn": "政豪货运", "_name_zh-hk": "政豪貨運", "_group": "12"}, {"key": 191230, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://ha-logistics.top/", "_name": "H&A LOGISTICS", "_name_zh-cn": "鸿安国际", "_name_zh-hk": "鴻安國際", "_group": "12"}, {"key": 100502, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.drivehailify.com/", "_name": "Hailify", "_name_zh-cn": "Hailify", "_name_zh-hk": "Hailify", "_group": "11"}, {"key": 190680, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-28242418", "_url": "http://www.hxyt56.cn/", "_name": "Haixin Express", "_name_zh-cn": "海鑫运通", "_name_zh-hk": "海鑫運通", "_group": "12"}, {"key": 191182, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-23057456 ", "_url": "https://www.halosend.com/", "_name": "Halosend Logistics", "_name_zh-cn": "星环国际物流", "_name_zh-hk": "星環國際物流", "_group": "12"}, {"key": 190956, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.tzawdz.com/", "_name": "HangBang", "_name_zh-cn": "航邦", "_name_zh-hk": "航邦", "_group": "12"}, {"key": 100266, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "", "_url": "https://www.hanjin.co.kr/", "_name": "Hanjin", "_name_zh-cn": "Hanjin", "_name_zh-hk": "Hanjin", "_group": "11"}, {"key": 191095, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13420998780", "_url": "http://www.han-sea.com/ ", "_name": "HAN-SEA", "_name_zh-cn": "汉洋物流", "_name_zh-hk": "漢洋物流", "_group": "12"}, {"key": 190895, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://haohai-express.com/", "_name": "haohai", "_name_zh-cn": "浩海物流", "_name_zh-hk": "浩海物流", "_group": "12"}, {"key": 191344, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-13925257785", "_url": "https://www.haoyouyi.cn/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "好又宜物流", "_name_zh-hk": "好又宜物流", "_group": "12"}, {"key": 100223, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://happy-post.com/", "_name": "Happy-Post", "_name_zh-cn": "Happy-Post", "_name_zh-hk": "Happy-Post", "_group": "11"}, {"key": 190958, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.harlyson.cn/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "广州哈利迅", "_name_zh-hk": "廣州哈利迅", "_group": "12"}, {"key": 100450, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 (0)1753 683 557", "_url": "https://hart2hartexpress.co.uk/", "_name": "Hart 2 Hart", "_name_zh-cn": "Hart 2 Hart", "_name_zh-hk": "Hart 2 Hart", "_group": "11"}, {"key": 1131, "_country": 113, "_country_iso": "AM", "_email": "", "_tel": "", "_url": "http://www.haypost.am/", "_name": "Haypost", "_name_zh-cn": "Haypost", "_name_zh-hk": "Haypost", "_group": ""}, {"key": 191155, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hbgyl.site/", "_name": "HBGJ", "_name_zh-cn": "瀚邦国际", "_name_zh-hk": "瀚邦國際", "_group": "12"}, {"key": 190982, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.hcgj56.com/ ", "_name": "HC logistics", "_name_zh-cn": "慧诚国际物流", "_name_zh-hk": "慧誠國際物流", "_group": "12"}, {"key": 191053, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.xchcrd.com/", "_name": "HCRD", "_name_zh-cn": "惠程荣达", "_name_zh-hk": "惠程榮達", "_group": "12"}, {"key": 191116, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-81775669", "_url": "https://www.hcs168.cn/ ", "_name": "HCST", "_name_zh-cn": "鸿昌顺通", "_name_zh-hk": "鴻昌順通", "_group": "12"}, {"key": 190466, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.hct.com.tw/", "_name": "HCT Logistics", "_name_zh-cn": "新竹物流", "_name_zh-hk": "新竹物流", "_group": "12"}, {"key": 190299, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://hdgj19.com/", "_name": "HDQN", "_name_zh-cn": "HD物流", "_name_zh-hk": "HD物流", "_group": "10"}, {"key": 191114, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0579-85608555", "_url": "http://www.heboon.com/", "_name": "HeBang International", "_name_zh-cn": "和邦国际物流  ", "_name_zh-hk": "和邦國際物流", "_group": "12"}, {"key": 190609, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.hecny.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 100518, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.hellmann.com/en/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100181, "_country": 1408, "_country_iso": "NO", "_email": "", "_tel": "", "_url": "https://helthjem.no/", "_name": "Helthjem", "_name_zh-cn": "Helthjem", "_name_zh-hk": "Helthjem", "_group": "11"}, {"key": 190767, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-29778002", "_url": "http://www.hxlog.net/", "_name": "Hengxun Logistics", "_name_zh-cn": "恒巽物流", "_name_zh-hk": "恆巽物流", "_group": "12"}, {"key": 190555, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hpgjwl.com.cn/", "_name": "Heping Logistics", "_name_zh-cn": "禾平物流", "_name_zh-hk": "禾平物流", "_group": "12"}, {"key": 100642, "_country": 2011, "_country_iso": "TR", "_email": "", "_tel": "+90 0850 558 0 333", "_url": "https://www.hepsijet.com/", "_name": "HepsiJET", "_name_zh-cn": "HepsiJET", "_name_zh-hk": "HepsiJET", "_group": "11"}, {"key": 100018, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "+49 1806-311211", "_url": "https://www.hermesworld.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100031, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "+49 (040) 593 551 111", "_url": "http://www.myhermes.de/", "_name": "<PERSON><PERSON> (DE)", "_name_zh-cn": "<PERSON><PERSON> (DE)", "_name_zh-hk": "<PERSON><PERSON> (DE)", "_group": "11"}, {"key": 100091, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "https://hermesborderguru.io/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100327, "_country": 906, "_country_iso": "IL", "_email": "", "_tel": "+972 03-9244441 call 1 ", "_url": "https://www.hfd.co.il/", "_name": "HFD", "_name_zh-cn": "HFD", "_name_zh-hk": "HFD", "_group": "11"}, {"key": 190678, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szhhhy.cn/", "_name": "HH logistics", "_name_zh-cn": "HH logistics", "_name_zh-hk": "HH logistics", "_group": "12"}, {"key": 190681, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szhhh56.com/", "_name": "HHH", "_name_zh-cn": "海河汇", "_name_zh-hk": "海河匯", "_group": "12"}, {"key": 190600, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hhyexpress.com/", "_name": "HHY", "_name_zh-cn": "恒好运", "_name_zh-hk": "恆好運", "_group": "12"}, {"key": 190457, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "+886 0800-022-118", "_url": "https://www.hilife.com.tw/", "_name": "HiLiFe", "_name_zh-cn": "莱尔富", "_name_zh-hk": "萊爾富", "_group": "12"}, {"key": 191073, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "https://www.risenguoji.com/", "_name": "Himori Express", "_name_zh-cn": "日森", "_name_zh-hk": "日森", "_group": "12"}, {"key": 101048, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34-915 271 344", "_url": "https://www.hispapost.es/", "_name": "Hispapost ", "_name_zh-cn": "Hispapost ", "_name_zh-hk": "Hispapost ", "_group": "11"}, {"key": 100628, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "+886 02-8295-8650", "_url": "http://hiyes.com.tw/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "新航通运", "_name_zh-hk": "新航通運", "_group": "11"}, {"key": 190497, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hj-sc56.com/", "_name": "HJ-GYL", "_name_zh-cn": "HJ-GYL", "_name_zh-hk": "HJ-GYL", "_group": "12"}, {"key": 190229, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hjyt56.com/", "_name": "HJYT", "_name_zh-cn": "HJYT", "_name_zh-hk": "HJYT", "_group": "12"}, {"key": 190156, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hkdexpress.net/", "_name": "HKD", "_name_zh-cn": "HKD", "_name_zh-hk": "HKD", "_group": "10"}, {"key": 191358, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-18257826697", "_url": "http://haikongda.com/", "_name": "HKD", "_name_zh-cn": "海空达国际物流", "_name_zh-hk": "海空達國際物流", "_group": "12"}, {"key": 190867, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.huomc.com/", "_name": "HMC", "_name_zh-cn": "货满舱", "_name_zh-hk": "貨滿艙", "_group": "12"}, {"key": 190474, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://sztoppost.com/", "_name": "HMCP", "_name_zh-cn": "环贸仓配", "_name_zh-hk": "環貿倉配", "_group": "12"}, {"key": 190372, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hmg-express.com/", "_name": "HMG", "_name_zh-cn": "黄马褂", "_name_zh-hk": "黃馬褂", "_group": "12"}, {"key": 190885, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 15914441066", "_url": "http://www.hmtmcn.com/", "_name": "HMTM", "_name_zh-cn": "华美天马国际", "_name_zh-hk": "華美天馬國際", "_group": "12"}, {"key": 190369, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hnhtyxgs.com/", "_name": "HNCA Logistics", "_name_zh-cn": "河南航投物流", "_name_zh-hk": "河南航投物流", "_group": "10"}, {"key": 191217, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0371-53395968", "_url": "http://www.gangtongguoji.cn/", "_name": "HNGT", "_name_zh-cn": "港通国际", "_name_zh-hk": "港通國際", "_group": "12"}, {"key": 101038, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "+49-06258 / 804-0", "_url": "https://www.hofmann-spedition.de/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100840, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91- 0120-6262650", "_url": "https://holisollogistics.com/", "_name": "Holisol", "_name_zh-cn": "Holisol", "_name_zh-hk": "Holisol", "_group": "11"}, {"key": 100657, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "https://www.homerr.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 190598, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gzbtygj.com/", "_name": "Hong Hai Chain", "_name_zh-cn": "宏海供应链", "_name_zh-hk": "宏海供應鏈", "_group": "12"}, {"key": 191251, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "00852-30697690", "_url": "http://huanqiuabc.com/", "_name": "HONG KONG GLOBAL TNTERNATIONAL LOGISTICS LIMITED", "_name_zh-cn": "香港环球国际快运", "_name_zh-hk": "香港環球國際快運", "_group": "12"}, {"key": 191111, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://hongbong.hk/", "_name": "HongBong", "_name_zh-cn": "HongBong", "_name_zh-hk": "HongBong", "_group": "12"}, {"key": 8011, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": "http://www.hongkongpost.hk/", "_name": "HongKong Post", "_name_zh-cn": "HongKong Post", "_name_zh-hk": "HongKong Post", "_group": ""}, {"key": 190799, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://szhxyt-express.com/", "_name": "HONGXING", "_name_zh-cn": "鸿兴运通", "_name_zh-hk": "鴻興運通", "_group": "12"}, {"key": 190738, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13066832431", "_url": "http://www.honorlogistics.cn/", "_name": "Honor Logistics", "_name_zh-cn": "荣耀物流", "_name_zh-hk": "榮耀物流", "_group": "12"}, {"key": 191299, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://horizonlogisticshub.com/", "_name": "HORIZON", "_name_zh-cn": "HORIZON", "_name_zh-hk": "HORIZON", "_group": "12"}, {"key": 190854, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 18917502335", "_url": "http://www.hos-exp.com/", "_name": "HOS", "_name_zh-cn": "上海鸿沃", "_name_zh-hk": "上海鴻沃", "_group": "12"}, {"key": 190887, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ************", "_url": "http://www.hostoexp.com/", "_name": "HOSTO", "_name_zh-cn": "豪速通物流", "_name_zh-hk": "豪速通物流", "_group": "12"}, {"key": 191181, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "+86 0755-29196889 ", "_url": "http://www.hotsin-cargo.com/ ", "_name": "HOTSIN", "_name_zh-cn": "HOTSIN", "_name_zh-hk": "HOTSIN", "_group": "12"}, {"key": 100274, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "+52 (55) 4000 1920", "_url": "http://www.hound-express.com/", "_name": "Hound Express", "_name_zh-cn": "Hound Express", "_name_zh-hk": "Hound Express", "_group": "11"}, {"key": 101044, "_country": 1201, "_country_iso": "LA", "_email": "<EMAIL>", "_tel": "+856-20 55 538 210", "_url": "https://www.hal-logistics.la/", "_name": "Houng AH Loun Logistics", "_name_zh-cn": "Houng AH Loun Logistics", "_name_zh-hk": "Houng AH Loun Logistics", "_group": "11"}, {"key": 190464, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hoyangexpress.com/", "_name": "HOYANGexpress", "_name_zh-cn": "弘扬物流", "_name_zh-hk": "弘揚物流", "_group": "12"}, {"key": 100292, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>/<EMAIL>", "_tel": "******-835-2618", "_url": "https://www.hpcourier.ca/", "_name": "HP", "_name_zh-cn": "HP", "_name_zh-hk": "HP", "_group": "11"}, {"key": 191043, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.hplogistics.com.cn/", "_name": "HP Logistics", "_name_zh-cn": "弘鹏物流", "_name_zh-hk": "弘鵬物流", "_group": "12"}, {"key": 190947, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://hpwl.cc/  ", "_name": "HPWL", "_name_zh-cn": "海鹏物流", "_name_zh-hk": "海鵬物流", "_group": "12"}, {"key": 190388, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hqgjhy.cn/", "_name": "HQGJXB", "_name_zh-cn": "HQGJXB", "_name_zh-hk": "HQGJXB", "_group": "10"}, {"key": 100243, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "+39 06415880969", "_url": "https://www.hrparcel.com/", "_name": "HR Parcel (HRP)", "_name_zh-cn": "HR Parcel (HRP)", "_name_zh-hk": "HR Parcel (HRP)", "_group": "11"}, {"key": 190031, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hrgjzx.com/", "_name": "HRD", "_name_zh-cn": "HRD", "_name_zh-hk": "HRD", "_group": "12"}, {"key": 100324, "_country": 1608, "_country_iso": "PL", "_email": "<EMAIL>", "_tel": "+48 22 511 25 34", "_url": "https://www.hrx.pl/", "_name": "HRX", "_name_zh-cn": "HRX", "_name_zh-hk": "HRX", "_group": "11"}, {"key": 190480, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.hsd-express.com/", "_name": "HSD", "_name_zh-cn": "鸿速达", "_name_zh-hk": "鴻速達", "_group": "12"}, {"key": 190416, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hsd-ex.com/", "_name": "HSD Express", "_name_zh-cn": "鸿盛达国际", "_name_zh-hk": "鴻盛達國際", "_group": "12"}, {"key": 190352, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://hsdexpress.com/", "_name": "HSDGJ", "_name_zh-cn": "寰世达", "_name_zh-hk": "寰世達", "_group": "12"}, {"key": 190619, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hsscm-ltd.com/", "_name": "HSGJ", "_name_zh-cn": "华商国际", "_name_zh-hk": "華商國際", "_group": "12"}, {"key": 190931, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86  0755-27209167", "_url": "http://www.hst-ex.com/", "_name": "HST LOGISTICS", "_name_zh-cn": "HST LOGISTICS", "_name_zh-hk": "HST LOGISTICS", "_group": "12"}, {"key": 191194, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hthgjgyl.com/", "_name": "HTH", "_name_zh-cn": "恒腾辉", "_name_zh-hk": "恆騰輝", "_group": "12"}, {"key": 191267, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.huaexpress.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "华联万通", "_name_zh-hk": "華聯萬通", "_group": "12"}, {"key": 191064, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+1**************", "_url": "http://www.huahaiexpress.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "花海速递", "_name_zh-hk": "花海速遞", "_group": "12"}, {"key": 190003, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.hh-exp.com/", "_name": "HuaHan Logistics", "_name_zh-cn": "华翰物流", "_name_zh-hk": "華翰物流", "_group": "12"}, {"key": 190328, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hhjy56.cn/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "华航吉运", "_name_zh-hk": "華航吉運", "_group": "12"}, {"key": 191179, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18420426091 ", "_url": " http://www.huaheint.com/", "_name": "Huahe International", "_name_zh-cn": "华和国际", "_name_zh-hk": "華和國際", "_group": "12"}, {"key": 191317, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86-400-8168-709", "_url": "http://www.hualiangang.com/", "_name": "HUALIANGANG", "_name_zh-cn": "华联港", "_name_zh-hk": "華聯港", "_group": "12"}, {"key": 190501, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.huanshi56.com/", "_name": "HUANSHI56", "_name_zh-cn": "环世货运", "_name_zh-hk": "環世貨運", "_group": "12"}, {"key": 190996, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.sz-huaruitong.com/", "_name": "HUARUITONG", "_name_zh-cn": "华睿通物流", "_name_zh-hk": "華睿通物流", "_group": "12"}, {"key": 190382, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hsgjky.com/", "_name": "Huasheng INT", "_name_zh-cn": "华昇国际", "_name_zh-hk": "華昇國際", "_group": "12"}, {"key": 190421, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.huaxiexpress.com/", "_name": "HuaXi Express", "_name_zh-cn": "华熙国际", "_name_zh-hk": "華熙國際", "_group": "12"}, {"key": 100804, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://hubbed.com/", "_name": "HUBBED", "_name_zh-cn": "HUBBED", "_name_zh-hk": "HUBBED", "_group": "11"}, {"key": 101013, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": "https://my.hub-ez.com/", "_name": "Hub-Ez", "_name_zh-cn": "Hub-Ez", "_name_zh-hk": "Hub-Ez", "_group": "11"}, {"key": 191287, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.hubologistics.com/", "_name": "Hubo Logistics", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "12"}, {"key": 191153, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.huiganwu.com/", "_name": "HUIGANWU", "_name_zh-cn": "汇赣武供应链", "_name_zh-hk": "匯贛武供應鏈", "_group": "12"}, {"key": 190173, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.huinglobal.com/", "_name": "HUIN Logistics", "_name_zh-cn": "惠恩物流", "_name_zh-hk": "惠恩物流", "_group": "12"}, {"key": 190914, "_country": 301, "_country_iso": "CN", "_email": "<PERSON><PERSON>@yw-huitian.com.cn", "_tel": "", "_url": "http://www.ejingtong.net/", "_name": "HUITIAN", "_name_zh-cn": "汇天国际", "_name_zh-hk": "匯天國際", "_group": "12"}, {"key": 190685, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-85299190", "_url": "https://www.hlhex.com.cn/", "_name": "huli<PERSON>", "_name_zh-cn": "华利航", "_name_zh-hk": "華利航", "_group": "12"}, {"key": 190467, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.bwwlys.com/", "_name": "Hundred Miles Freight", "_name_zh-cn": "Hundred Miles Freight", "_name_zh-hk": "Hundred Miles Freight", "_group": "12"}, {"key": 100233, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.hunterexpress.com.au/", "_name": "Hunter Express", "_name_zh-cn": "Hunter Express", "_name_zh-hk": "Hunter Express", "_group": "11"}, {"key": 190559, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.huodull.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 190489, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hxgj-exp.com/", "_name": "HX", "_name_zh-cn": "鸿鑫物流", "_name_zh-hk": "鴻鑫物流", "_group": "12"}, {"key": 190935, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  0755-29356863", "_url": "http://www.hy-exp.com/", "_name": "HY", "_name_zh-cn": "宏扬国际", "_name_zh-hk": "宏揚國際", "_group": "12"}, {"key": 191318, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>   ", "_tel": "+86-13724802487", "_url": "http://www.hygjlog.com/", "_name": "HY", "_name_zh-cn": "辉宇国际", "_name_zh-hk": "輝宇國際", "_group": "12"}, {"key": 190437, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hy-express.com/", "_name": "HY Express", "_name_zh-cn": "浩远国际", "_name_zh-hk": "浩遠國際", "_group": "10"}, {"key": 191000, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://szhydo.cn/  ", "_name": "HYD", "_name_zh-cn": "汇洋达", "_name_zh-hk": "匯洋達", "_group": "12"}, {"key": 191284, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "http://hyexp.net/", "_name": "HYE", "_name_zh-cn": "环洋国际", "_name_zh-hk": "環洋国际", "_group": "12"}, {"key": 190712, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hyjy-exp.com/", "_name": "HYJY", "_name_zh-cn": "恒元嘉运", "_name_zh-hk": "恆元嘉運", "_group": "12"}, {"key": 190894, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://haoyuwms.com/  ", "_name": "HYL", "_name_zh-cn": "皓宇物流", "_name_zh-hk": "皓宇物流", "_group": "12"}, {"key": 190306, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hytx-exp.com/", "_name": "HYTX", "_name_zh-cn": "HYTX", "_name_zh-hk": "HYTX", "_group": "12"}, {"key": 190471, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://hzc-express.com/", "_name": "HZC", "_name_zh-cn": "洪赞成物流", "_name_zh-hk": "洪贊成物流", "_group": "10"}, {"key": 190848, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0571-87929372", "_url": "http://www.hzzhexp.com/", "_name": "HZZH", "_name_zh-cn": "镇航国际", "_name_zh-hk": "鎮航國際", "_group": "12"}, {"key": 191052, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.szmr56.cn/", "_name": "iCargo", "_name_zh-cn": "iCargo", "_name_zh-hk": "iCargo", "_group": "12"}, {"key": 100893, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-9664422188", "_url": "https://www.iccworld.com/", "_name": "ICC Worldwide", "_name_zh-cn": "ICC Worldwide", "_name_zh-hk": "ICC Worldwide", "_group": "11"}, {"key": 9011, "_country": 901, "_country_iso": "IS", "_email": "", "_tel": "", "_url": "http://www.postur.is/", "_name": "Iceland Post", "_name_zh-cn": "Iceland Post", "_name_zh-hk": "Iceland Post", "_group": ""}, {"key": 100638, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "******-427-8729", "_url": "https://www.icscourier.ca/", "_name": "ICS Courier", "_name_zh-cn": "ICS Courier", "_name_zh-hk": "ICS Courier", "_group": "11"}, {"key": 100345, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.icumulus.com/", "_name": "iCumulus", "_name_zh-cn": "iCumulus", "_name_zh-hk": "iCumulus", "_group": "11"}, {"key": 190624, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.ideexp.com/", "_name": "IDE", "_name_zh-cn": "IDE", "_name_zh-hk": "IDE", "_group": "12"}, {"key": 100511, "_country": 903, "_country_iso": "ID", "_email": "<EMAIL>", "_tel": "+ 62 0882-0001-11000", "_url": "https://idexpress.com/", "_name": "IDexpressIndonesia", "_name_zh-cn": "IDexpressIndonesia", "_name_zh-hk": "IDexpressIndonesia", "_group": "11"}, {"key": 100104, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "https://www.idsship.com/", "_name": "IDS Logistics", "_name_zh-cn": "IDS Logistics", "_name_zh-hk": "IDS Logistics", "_group": "11"}, {"key": 191334, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 -0755-27909225", "_url": "https://www.iitw56.com/", "_name": "IITW", "_name_zh-cn": "IITW", "_name_zh-hk": "IITW", "_group": "12"}, {"key": 100891, "_country": 2004, "_country_iso": "TH", "_email": "<EMAIL>", "_tel": "+66 708-7999", "_url": "https://www.ikea.com/th/", "_name": "IKEA (TH)", "_name_zh-cn": "IKEA (TH)", "_name_zh-hk": "IKEA (TH)", "_group": "11"}, {"key": 190726, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://il.lmfun.net/", "_name": "ILINE", "_name_zh-cn": "以通", "_name_zh-hk": "以通", "_group": "12"}, {"key": 100661, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "+49 9119552595", "_url": "https://iloxx.de/home.aspx", "_name": "iloxx GmbH", "_name_zh-cn": "iloxx GmbH", "_name_zh-hk": "iloxx GmbH", "_group": "11"}, {"key": 190626, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ilsau.com/", "_name": "ILS", "_name_zh-cn": "纵横国际物流", "_name_zh-hk": "縱橫國際物流", "_group": "12"}, {"key": 190438, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "", "_url": "https://www.imile.com/", "_name": "iMile", "_name_zh-cn": "iMile", "_name_zh-hk": "iMile", "_group": "12"}, {"key": 100867, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.imxpostal.fr/", "_name": "IMX DISTRIBUTION GROUP", "_name_zh-cn": "IMX DISTRIBUTION GROUP", "_name_zh-hk": "IMX DISTRIBUTION GROUP", "_group": "11"}, {"key": 100904, "_country": 903, "_country_iso": "ID", "_email": "<EMAIL>", "_tel": "+62-0811 894 4456", "_url": "https://indahonline.com/", "_name": "Indah Logistik", "_name_zh-cn": "Indah Logistik", "_name_zh-hk": "Indah Logistik", "_group": "11"}, {"key": 9021, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "http://www.indiapost.gov.in/", "_name": "India Post", "_name_zh-cn": "India Post", "_name_zh-hk": "India Post", "_group": ""}, {"key": 100336, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://www.inexpost.ru/", "_name": "InexPost", "_name_zh-cn": "InexPost", "_name_zh-hk": "InexPost", "_group": "11"}, {"key": 4041, "_country": 404, "_country_iso": "DO", "_email": "", "_tel": "", "_url": "http://www.inposdom.gob.do/", "_name": "Inposdom", "_name_zh-cn": "Inposdom", "_name_zh-hk": "Inposdom", "_group": ""}, {"key": 100594, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "+34 938 16 62 34", "_url": "https://www.inpost.es/", "_name": "InPost (ES)", "_name_zh-cn": "InPost (ES)", "_name_zh-hk": "InPost (ES)", "_group": "11"}, {"key": 100469, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://inpost.it/", "_name": "InPost (IT)", "_name_zh-cn": "InPost (IT)", "_name_zh-hk": "InPost (IT)", "_group": "11"}, {"key": 100043, "_country": 1608, "_country_iso": "PL", "_email": "dane_o<PERSON><PERSON><EMAIL>", "_tel": "+746 600 000/+722 444 000 ", "_url": "https://inpost.pl/", "_name": "InPost (PL)", "_name_zh-cn": "InPost (PL)", "_name_zh-hk": "InPost (PL)", "_group": "11"}, {"key": 100598, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "+351 211 245 624", "_url": "https://www.inpost.pt/", "_name": "InPost (PT)", "_name_zh-cn": "InPost (PT)", "_name_zh-hk": "InPost (PT)", "_group": "11"}, {"key": 100462, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://inpost.co.uk/", "_name": "InPost (UK)", "_name_zh-cn": "InPost (UK)", "_name_zh-hk": "InPost (UK)", "_group": "11"}, {"key": 100562, "_country": 1601, "_country_iso": "PK", "_email": "", "_tel": "", "_url": "https://instaworld.pk/", "_name": "Insta World", "_name_zh-cn": "Insta World", "_name_zh-hk": "Insta World", "_group": "11"}, {"key": 100932, "_country": 1924, "_country_iso": "SE", "_email": "<EMAIL>", "_tel": "+46-010-206 90 01", "_url": "https://instabox.io/", "_name": "Instabox", "_name_zh-cn": "Instabox", "_name_zh-hk": "Instabox", "_group": "11"}, {"key": 100636, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+44 (0) ************", "_url": "https://www.instadispatch.com/", "_name": "InstaDispatch", "_name_zh-cn": "InstaDispatch", "_name_zh-hk": "InstaDispatch", "_group": "11"}, {"key": 101059, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-040 23127716", "_url": "https://www.iclexpress.in/", "_name": "Integrated Couriers & Logistics", "_name_zh-cn": "Integrated Couriers & Logistics", "_name_zh-hk": "Integrated Couriers & Logistics", "_group": "11"}, {"key": 100555, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://intelcom.ca/", "_name": "Intelcom (CA)", "_name_zh-cn": "Intelcom (CA)", "_name_zh-hk": "Intelcom (CA)", "_group": "11"}, {"key": 190572, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.qhzhigu.com/", "_name": "INTEL-VALLEY", "_name_zh-cn": "智谷供应链", "_name_zh-hk": "智谷供應鏈", "_group": "12"}, {"key": 100360, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "", "_url": "https://www.intercourier.pt/", "_name": "Inter Courier", "_name_zh-cn": "Inter Courier", "_name_zh-hk": "Inter Courier", "_group": "11"}, {"key": 100491, "_country": 313, "_country_iso": "CO", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.interrapidisimo.com/", "_name": "Inter Rapidisimo (INTER RAPIDÍSIMO)", "_name_zh-cn": "Inter Rapidisimo (INTER RAPIDÍSIMO)", "_name_zh-hk": "Inter Rapidisimo (INTER RAPIDÍSIMO)", "_group": "11"}, {"key": 100674, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.intercontinentalcargmovers.com/", "_name": "Intercontinental Cargo Movers", "_name_zh-cn": "Intercontinental Cargo Movers", "_name_zh-hk": "Intercontinental Cargo Movers", "_group": "11"}, {"key": 100528, "_country": 319, "_country_iso": "HR", "_email": "", "_tel": "", "_url": "https://www.intereuropa.hr/", "_name": "Intereuropa", "_name_zh-cn": "Intereuropa", "_name_zh-hk": "Intereuropa", "_group": "11"}, {"key": 190533, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ytltkd.com/", "_name": "Interlet", "_name_zh-cn": "英特莱特", "_name_zh-hk": "英特萊特", "_group": "12"}, {"key": 100381, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "", "_url": "https://www.internetexpress.co.za/", "_name": "Internet Express", "_name_zh-cn": "Internet Express", "_name_zh-hk": "Internet Express", "_group": "11"}, {"key": 100210, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://au.interparcel.com/", "_name": "Interparcel (AU)", "_name_zh-cn": "Interparcel (AU)", "_name_zh-hk": "Interparcel (AU)", "_group": "11"}, {"key": 100211, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "https://nz.interparcel.com/", "_name": "Interparcel (NZ)", "_name_zh-cn": "Interparcel (NZ)", "_name_zh-hk": "Interparcel (NZ)", "_group": "11"}, {"key": 100209, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://uk.interparcel.com/", "_name": "Interparcel (UK)", "_name_zh-cn": "Interparcel (UK)", "_name_zh-hk": "Interparcel (UK)", "_group": "11"}, {"key": 100764, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "+49 06873-668700", "_url": "https://www.intex-paketdienst.de/", "_name": "INTEX Paketdienst", "_name_zh-cn": "INTEX Paketdienst", "_name_zh-hk": "INTEX Paketdienst", "_group": "11"}, {"key": 101022, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************    ", "_url": "http://www.intimeship.net/", "_name": "Intime ship", "_name_zh-cn": "Intime ship", "_name_zh-hk": "Intime ship", "_group": "11"}, {"key": 100691, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "+81-6-6554-9404/", "_url": "https://intras.co.jp/", "_name": "INTRAS CORPORATION", "_name_zh-cn": "INTRAS CORPORATION", "_name_zh-hk": "INTRAS CORPORATION", "_group": "11"}, {"key": 100015, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "http://www.i-parcel.com/", "_name": "i-<PERSON><PERSON><PERSON>", "_name_zh-cn": "i-<PERSON><PERSON><PERSON>", "_name_zh-hk": "i-<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 22031, "_country": 2203, "_country_iso": "VE", "_email": "<EMAIL>", "_tel": "+58 405-31-88", "_url": "https://www.ipostel.gob.ve/", "_name": "I<PERSON>el", "_name_zh-cn": "I<PERSON>el", "_name_zh-hk": "I<PERSON>el", "_group": ""}, {"key": 191063, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0086 4008 618 163", "_url": "https://www.ipsexp.com/", "_name": "IPS", "_name_zh-cn": "智慧包裹", "_name_zh-hk": "智慧包裹", "_group": "12"}, {"key": 100277, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "+966 97148179633", "_url": "https://iqsgsc.com/", "_name": "IQS", "_name_zh-cn": "IQS", "_name_zh-hk": "IQS", "_group": "11"}, {"key": 9041, "_country": 904, "_country_iso": "IR", "_email": "", "_tel": "", "_url": "http://post.ir/", "_name": "Iran Post", "_name_zh-cn": "Iran Post", "_name_zh-hk": "Iran Post", "_group": ""}, {"key": 9081, "_country": 908, "_country_iso": "IQ", "_email": "", "_tel": "", "_url": "https://post.iq/en/", "_name": "Iraq Post (البريد العراقي)", "_name_zh-cn": "Iraq Post (البريد العراقي)", "_name_zh-hk": "Iraq Post (البريد العراقي)", "_group": ""}, {"key": 100257, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "", "_url": "https://iso-logistics.vn/", "_name": "ISO Logistics", "_name_zh-cn": "ISO Logistics", "_name_zh-hk": "ISO Logistics", "_group": "11"}, {"key": 9061, "_country": 906, "_country_iso": "IL", "_email": "", "_tel": "", "_url": "http://www.israelpost.co.il/", "_name": "Israel Post", "_name_zh-cn": "Israel Post", "_name_zh-hk": "Israel Post", "_group": ""}, {"key": 100857, "_country": 504, "_country_iso": "EE", "_email": "<EMAIL>", "_tel": "+372 60 11 000", "_url": "https://itella.ee/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100437, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.ithinklogistics.com/", "_name": "iThink Logistics", "_name_zh-cn": "iThink Logistics", "_name_zh-hk": "iThink Logistics", "_group": "11"}, {"key": 100492, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "", "_url": "https://ivoy.mx/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100778, "_country": 903, "_country_iso": "ID", "_email": "<EMAIL>", "_tel": "+62 021-80661666", "_url": "https://www.jtcargo.id/", "_name": "J&T Cargo (ID)", "_name_zh-cn": "J&T Cargo (ID)", "_name_zh-hk": "J&T Cargo (ID)", "_group": "11"}, {"key": 101019, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+60-1800-18-0018", "_url": "https://www.jtcargo.my/", "_name": "J&T Cargo (MY)", "_name_zh-cn": "J&T Cargo (MY)", "_name_zh-hk": "J&T Cargo (MY)", "_group": "11"}, {"key": 100856, "_country": 503, "_country_iso": "AE", "_email": "<EMAIL>", "_tel": "+971-800-568", "_url": "https://www.jtexpress.ae/", "_name": "J&T Express (AE)", "_name_zh-cn": "J&T Express (AE)", "_name_zh-hk": "J&T Express (AE)", "_group": "11"}, {"key": 100797, "_country": 215, "_country_iso": "BR", "_email": "<EMAIL>", "_tel": "+55 0800-055-0050", "_url": "https://www.jtexpress.com.br/", "_name": "J&T Express (BR)", "_name_zh-cn": "J&T Express (BR)", "_name_zh-hk": "J&T Express (BR)", "_group": "11"}, {"key": 190442, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jtexpress.com.cn/", "_name": "J&T Express (CN)", "_name_zh-cn": "J&T Express (CN)", "_name_zh-hk": "J&T Express (CN)", "_group": "12"}, {"key": 100619, "_country": 502, "_country_iso": "EG", "_email": "", "_tel": "", "_url": "https://www.jtexpress-eg.com/", "_name": "J&T Express (EG)", "_name_zh-cn": "J&T Express (EG)", "_name_zh-hk": "J&T Express (EG)", "_group": "11"}, {"key": 100074, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://www.jet.co.id/", "_name": "J&T Express (ID)", "_name_zh-cn": "J&T Express (ID)", "_name_zh-hk": "J&T Express (ID)", "_group": "11"}, {"key": 100388, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "", "_url": "https://www.jtexpress.mx/", "_name": "J&T Express (MX)", "_name_zh-cn": "J&T Express (MX)", "_name_zh-hk": "J&T Express (MX)", "_group": "11"}, {"key": 100079, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "", "_url": "https://jtexpress.my/", "_name": "J&T Express (MY)", "_name_zh-cn": "J&T Express (MY)", "_name_zh-hk": "J&T Express (MY)", "_group": "11"}, {"key": 100240, "_country": 1607, "_country_iso": "PH", "_email": "", "_tel": "", "_url": "https://www.jtexpress.ph/", "_name": "J&T Express (PH)", "_name_zh-cn": "J&T Express (PH)", "_name_zh-hk": "J&T Express (PH)", "_group": "11"}, {"key": 100402, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "", "_url": "https://www.jtexpress-sa.com/", "_name": "J&T Express (SA)", "_name_zh-cn": "J&T Express (SA)", "_name_zh-hk": "J&T Express (SA)", "_group": "11"}, {"key": 100229, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "https://www.jtexpress.sg/", "_name": "J&T Express (SG)", "_name_zh-cn": "J&T Express (SG)", "_name_zh-hk": "J&T Express (SG)", "_group": "11"}, {"key": 100271, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "", "_url": "https://www.jtexpress.co.th/", "_name": "J&T Express (TH)", "_name_zh-cn": "J&T Express (TH)", "_name_zh-hk": "J&T Express (TH)", "_group": "11"}, {"key": 100456, "_country": 2204, "_country_iso": "VN", "_email": "<EMAIL>", "_tel": "+84 1900 1088", "_url": "https://jtexpress.vn/", "_name": "J&T Express (VN)", "_name_zh-cn": "J&T Express (VN)", "_name_zh-hk": "J&T Express (VN)", "_group": "11"}, {"key": 100295, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.jet-logistics.com/?from=17track", "_name": "J&T International", "_name_zh-cn": "极兔国际", "_name_zh-hk": "極兔國際", "_group": "11"}, {"key": 101052, "_country": 215, "_country_iso": "BR", "_email": "", "_tel": "", "_url": "https://www.jadlog.com.br/", "_name": "Jadlog", "_name_zh-cn": "Jadlog", "_name_zh-hk": "Jadlog", "_group": "11"}, {"key": 10011, "_country": 1001, "_country_iso": "JM", "_email": "", "_tel": "", "_url": "http://www.jamaicapost.gov.jm/", "_name": "Jamaica Post", "_name_zh-cn": "Jamaica Post", "_name_zh-hk": "Jamaica Post", "_group": ""}, {"key": 101053, "_country": 1101, "_country_iso": "KZ", "_email": "<EMAIL>", "_tel": "+997-8777-027-5340", "_url": "https://janapost.kz/", "_name": "JAŃA POST", "_name_zh-cn": "JAŃA POST", "_name_zh-hk": "JAŃA POST", "_group": "11"}, {"key": 100728, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "+852 2575 7883", "_url": "https://www.jancoecommerce.com/", "_name": "Janco E-Commerce", "_name_zh-cn": "骏高", "_name_zh-hk": "駿高", "_group": "11"}, {"key": 100140, "_country": 1913, "_country_iso": "SG", "_email": "<EMAIL>", "_tel": "", "_url": "https://janio.asia/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 10021, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "http://www.post.japanpost.jp/", "_name": "Japan Post", "_name_zh-cn": "Japan Post", "_name_zh-hk": "Japan Post", "_group": ""}, {"key": 100115, "_country": 322, "_country_iso": "CZ", "_email": "", "_tel": "", "_url": "https://japo-autodoprava.cz/", "_name": "JAPO Transport", "_name_zh-cn": "JAPO Transport", "_name_zh-hk": "JAPO Transport", "_group": "11"}, {"key": 190793, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jexpress88.com/", "_name": "JASPOST", "_name_zh-cn": "JASPOST", "_name_zh-hk": "JASPOST", "_group": "12"}, {"key": 190940, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.jc6688.cn/ ", "_name": "JC Logistics", "_name_zh-cn": "景昌物流", "_name_zh-hk": "景昌物流", "_group": "12"}, {"key": 190120, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jcex.com/", "_name": "JCEX", "_name_zh-cn": "佳成国际", "_name_zh-hk": "佳成國際", "_group": "12"}, {"key": 190994, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  13372551758 ", "_url": "http://www.jch-exp.com/", "_name": "JCH", "_name_zh-cn": "金岂供应链", "_name_zh-hk": "金豈供應鏈", "_group": "12"}, {"key": 191103, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://jcwexpress.com/", "_name": "JCW Express", "_name_zh-cn": "急成稳物流", "_name_zh-hk": "急成穩物流", "_group": "12"}, {"key": 191121, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-950616", "_url": "https://www.jdl.com/", "_name": "JD Logistics", "_name_zh-cn": "京东物流", "_name_zh-hk": "京東物流", "_group": "12"}, {"key": 190302, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.jingdonglogistics.com/", "_name": "JD Worldwide", "_name_zh-cn": "京东国际", "_name_zh-hk": "京東國際", "_group": "12"}, {"key": 100120, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://www.jde.ru/", "_name": "JDE", "_name_zh-cn": "JDE", "_name_zh-hk": "JDE", "_group": "11"}, {"key": 190567, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.jdiex.com/", "_name": "JDIEX", "_name_zh-cn": "JDIEX国际物流", "_name_zh-hk": "JDIEX國際物流", "_group": "12"}, {"key": 100228, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://www.jdlexpress.co.id/", "_name": "JDL Express", "_name_zh-cn": "JDL Express", "_name_zh-hk": "JDL Express", "_group": "11"}, {"key": 190498, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.jiudt.com/", "_name": "JDT", "_name_zh-cn": "玖鼎通", "_name_zh-hk": "玖鼎通", "_group": "12"}, {"key": 190332, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szjdy.ltd/", "_name": "JDY", "_name_zh-cn": "金斗云", "_name_zh-hk": "金斗雲", "_group": "10"}, {"key": 100238, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL> ", "_tel": "****** 940-5055", "_url": "https://www.jenyxpress.com/", "_name": "JENY", "_name_zh-cn": "JENY", "_name_zh-hk": "JENY", "_group": "10"}, {"key": 90091, "_country": 9009, "_country_iso": "JE", "_email": "", "_tel": "", "_url": "http://www.jerseypost.com/", "_name": "Jersey Post", "_name_zh-cn": "Jersey Post", "_name_zh-hk": "Jersey Post", "_group": ""}, {"key": 100554, "_country": 1101, "_country_iso": "KZ", "_email": "", "_tel": "", "_url": "https://jet.com.kz/", "_name": "Jet <PERSON>", "_name_zh-cn": "Jet <PERSON>", "_name_zh-hk": "Jet <PERSON>", "_group": "11"}, {"key": 100890, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.jet-f.com/", "_name": "JET-F WORLDWIDE EXPRESS", "_name_zh-cn": "捷丰国际物流", "_name_zh-hk": "捷豐國際物流", "_group": "11"}, {"key": 100625, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 7061173100", "_url": "https://www.jetlinecouriers.in/", "_name": "Jetline Couriers Pvt. Ltd", "_name_zh-cn": "Jetline Couriers Pvt. Ltd", "_name_zh-hk": "Jetline Couriers Pvt. Ltd", "_group": "11"}, {"key": 191195, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jgex.net/", "_name": "JGEX", "_name_zh-cn": "锦鸽国际", "_name_zh-hk": "錦鴿國際", "_group": "12"}, {"key": 190357, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jiehang.net/", "_name": "JH", "_name_zh-cn": "杰航国际物流", "_name_zh-hk": "傑航國際物流", "_group": "12"}, {"key": 190918, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.jh77express.com/ ", "_name": "JHEX", "_name_zh-cn": "金快递", "_name_zh-hk": "金快遞", "_group": "12"}, {"key": 191343, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jukewuliu.com/", "_name": "JHTrack", "_name_zh-cn": "JHTrack", "_name_zh-hk": "JHTrack", "_group": "12"}, {"key": 190902, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.szjsdgyl.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "佳仕达供应链", "_name_zh-hk": "佳仕達供應鏈", "_group": "12"}, {"key": 191198, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jx-international.com/", "_name": "JIAXINGUOJI", "_name_zh-cn": "嘉欣国际", "_name_zh-hk": "嘉欣國際", "_group": "12"}, {"key": 191098, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.jygjgyl.com/", "_name": "JIAYANG", "_name_zh-cn": "嘉扬国际", "_name_zh-hk": "嘉揚國際", "_group": "12"}, {"key": 191368, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://brcang.com/", "_name": "JIEBAO", "_name_zh-cn": "捷宝", "_name_zh-hk": "捷寶", "_group": "12"}, {"key": 190544, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jdexp.cn/", "_name": "Jiedan logistics", "_name_zh-cn": "捷丹集运", "_name_zh-hk": "捷丹集運", "_group": "12"}, {"key": 190641, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jdgjwl.com/", "_name": "Jiede supply chain", "_name_zh-cn": "捷德供应链", "_name_zh-hk": "捷德供應鏈", "_group": "12"}, {"key": 190962, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jiehao56.com/ ", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "捷豪货运", "_name_zh-hk": "捷豪貨運", "_group": "12"}, {"key": 191129, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jsly56.cn/", "_name": "JIESHUN LIANYUN", "_name_zh-cn": "捷顺联运", "_name_zh-hk": "捷順聯運", "_group": "12"}, {"key": 190955, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szjf56.cn/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "深圳吉凡物流", "_name_zh-hk": "深圳吉凡物流", "_group": "12"}, {"key": 190990, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.jimai56.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "极脉物流", "_name_zh-hk": "極脈物流", "_group": "12"}, {"key": 191067, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jdygjkd.com/ ", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "浙江筋斗云", "_name_zh-hk": "浙江筋斗雲", "_group": "12"}, {"key": 190465, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jinlaiexpress.com/", "_name": "JINLAI", "_name_zh-cn": "金来物流", "_name_zh-hk": "金來物流", "_group": "10"}, {"key": 191142, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jlfba.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "锦联国际物流", "_name_zh-hk": "錦聯國際物流", "_group": "12"}, {"key": 190612, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jsdgj56.cn/", "_name": "JINSHIDA", "_name_zh-cn": "金世达", "_name_zh-hk": "金世達", "_group": "12"}, {"key": 190690, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jinyuefreight.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "今越国际", "_name_zh-hk": "今越國際", "_group": "12"}, {"key": 190301, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jiufanglogistics.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 190908, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 （0755）2828 0529 ", "_url": "https://www.poleship.com/", "_name": "<PERSON><PERSON> logistic", "_name_zh-cn": "极运物流", "_name_zh-hk": "極運物流", "_group": "12"}, {"key": 190588, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jjwex.com/", "_name": "JJWEX", "_name_zh-cn": "捷立国际", "_name_zh-hk": "捷立國際", "_group": "12"}, {"key": 191257, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://jinliexpress.cn/", "_name": "JL", "_name_zh-cn": "锦栎国际", "_name_zh-hk": "錦櫟國際", "_group": "12"}, {"key": 190828, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-28456560", "_url": "http://www.carnivalsup.cn/", "_name": "JLH", "_name_zh-cn": "嘉利华", "_name_zh-hk": "嘉利華", "_group": "12"}, {"key": 191330, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-13710104098", "_url": "http://www.jndcargo.com/", "_name": "JND ", "_name_zh-cn": "骏能达", "_name_zh-hk": "骏能达", "_group": "12"}, {"key": 100086, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://www.jne.co.id/", "_name": "JNE Express", "_name_zh-cn": "JNE Express", "_name_zh-hk": "JNE Express", "_group": "11"}, {"key": 190082, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.j-net.cn/", "_name": "J-NET", "_name_zh-cn": "捷网", "_name_zh-hk": "捷網", "_group": "12"}, {"key": 100359, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+603 2241 6637", "_url": "http://www.jocom.my/", "_name": "Jocom", "_name_zh-cn": "Jocom", "_name_zh-hk": "Jocom", "_group": "11"}, {"key": 100449, "_country": 801, "_country_iso": "HK", "_email": "<EMAIL>", "_tel": "", "_url": "https://joomlogistics.com/en", "_name": "Joom Logistics", "_name_zh-cn": "Joom Logistics", "_name_zh-hk": "Joom Logistics", "_group": "11"}, {"key": 10031, "_country": 1003, "_country_iso": "JO", "_email": "", "_tel": "", "_url": "http://www.jordanpost.com.jo/", "_name": "Jordan Post", "_name_zh-cn": "Jordan Post", "_name_zh-hk": "Jordan Post", "_group": ""}, {"key": 191057, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "", "_url": "http://www.jowee.cn/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "卓唯供应链", "_name_zh-hk": "卓唯供應鏈", "_group": "12"}, {"key": 190379, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.joyingbox.com/", "_name": "JOYING BOX", "_name_zh-cn": "飞盒跨境", "_name_zh-hk": "飛盒跨境", "_group": "10"}, {"key": 2121, "_country": 212, "_country_iso": "BA", "_email": "", "_tel": "", "_url": "http://www.posta.ba/", "_name": "JP BH Post", "_name_zh-cn": "JP BH Post", "_name_zh-hk": "JP BH Post", "_group": ""}, {"key": 101025, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.myjpexpress.com/", "_name": "JP Express", "_name_zh-cn": "JP Express", "_name_zh-hk": "JP Express", "_group": "11"}, {"key": 190443, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://jpsgjwl.com/", "_name": "JPSGJ", "_name_zh-cn": "捷谱斯国际", "_name_zh-hk": "捷譜斯國際", "_group": "12"}, {"key": 190812, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jqgjwl.com/", "_name": "JQGJ", "_name_zh-cn": "景强国际", "_name_zh-hk": "景強國際", "_group": "12"}, {"key": 190961, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  15361854145  ", "_url": "http://www.szjrscm56.com/", "_name": "JR", "_name_zh-cn": "九融供应链", "_name_zh-hk": "九融供應鏈", "_group": "12"}, {"key": 191055, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jr-canada.com/", "_name": "JR", "_name_zh-cn": "JR", "_name_zh-hk": "JR", "_group": "12"}, {"key": 191337, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 -4009666969", "_url": "http://www.jr2019.cn/", "_name": "JR", "_name_zh-cn": "嘉瑞国际", "_name_zh-hk": "嘉瑞國際", "_group": "12"}, {"key": 190199, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> , <EMAIL>", "_tel": "+86 400-0353-518", "_url": "http://www.js-exp.com/", "_name": "JS", "_name_zh-cn": "急速国际", "_name_zh-hk": "急速國際", "_group": "12"}, {"key": 190025, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.galaxy-ex.com/", "_name": "JSH", "_name_zh-cn": "嘉盛泓", "_name_zh-hk": "嘉盛泓", "_group": "12"}, {"key": 191243, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-2336667", "_url": "http://www.jsr56.cn/", "_name": "JSR", "_name_zh-cn": "捷顺荣", "_name_zh-hk": "捷順榮", "_group": "12"}, {"key": 190936, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jsydkp.com/", "_name": "JSYD", "_name_zh-cn": "极速运达", "_name_zh-hk": "極速運達", "_group": "12"}, {"key": 190832, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-27324678", "_url": "https://www.hkjtd56.com/", "_name": "JTD", "_name_zh-cn": "金泰达国际", "_name_zh-hk": "金泰達國際", "_group": "12"}, {"key": 190105, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.i360express.com/", "_name": "JTEX", "_name_zh-cn": "JTEX", "_name_zh-hk": "JTEX", "_group": "12"}, {"key": 191143, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.franki-x.top/ ", "_name": "JTHQ", "_name_zh-cn": "极通环球", "_name_zh-hk": "極通環球", "_group": "12"}, {"key": 190951, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 13312935229 ", "_url": "http://www.jintery.com/ ", "_name": "JTR", "_name_zh-cn": "津态瑞", "_name_zh-hk": "津態瑞", "_group": "12"}, {"key": 190967, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jumpinternational.cn/  ", "_name": "JUMP", "_name_zh-cn": "JUMP", "_name_zh-hk": "JUMP", "_group": "12"}, {"key": 100945, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+852 6058 8546", "_url": "https://www.jumppoint.io/", "_name": "Jumppoint", "_name_zh-cn": "Jumppoint", "_name_zh-hk": "Jumppoint", "_group": "11"}, {"key": 190757, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-020-22043552", "_url": "https://jfchinese.com/", "_name": "JunFeng International", "_name_zh-cn": "君丰国际", "_name_zh-hk": "君豐國際", "_group": "12"}, {"key": 190758, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-020-86210570", "_url": "http://www.junya56.com/", "_name": "JUNYA", "_name_zh-cn": "广州骏亚", "_name_zh-hk": "廣州駿亞", "_group": "12"}, {"key": 100871, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-9904004122", "_url": "http://jupitercourier.co.in/", "_name": "Jupiter Services", "_name_zh-cn": "Jupiter Services", "_name_zh-hk": "Jupiter Services", "_group": "11"}, {"key": 190644, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.jusdasr.com/", "_name": "Jusda International", "_name_zh-cn": "准时达", "_name_zh-hk": "準時達", "_group": "12"}, {"key": 190980, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>  ", "_tel": "+86   15818254321 ", "_url": "https://www.jwgj.ltd", "_name": "JWGJ", "_name_zh-cn": "久卫国际", "_name_zh-hk": "久衛國際", "_group": "12"}, {"key": 190739, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://119.23.49.43/index.html", "_name": "JX EXPRESS", "_name_zh-cn": "吉箱物流", "_name_zh-hk": "吉箱物流", "_group": "12"}, {"key": 191372, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.ptsjgyl.com/", "_name": "JXGYL", "_name_zh-cn": "嘉信供应链", "_name_zh-hk": "嘉信供應鏈", "_group": "12"}, {"key": 191002, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://jxsf-express.com/", "_name": "JXSF", "_name_zh-cn": "锦绣四方", "_name_zh-hk": "錦繡四方", "_group": "12"}, {"key": 190365, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.jiayingex.com/", "_name": "JY", "_name_zh-cn": "JY", "_name_zh-hk": "JY", "_group": "12"}, {"key": 190460, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.1hhz.com/", "_name": "JYC", "_name_zh-cn": "金羊城", "_name_zh-hk": "金羊城", "_group": "12"}, {"key": 190959, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL> ", "_tel": "", "_url": "http://www.szjygj.com/", "_name": "JYGJ", "_name_zh-cn": "嘉洋国际", "_name_zh-hk": "嘉洋國際", "_group": "12"}, {"key": 191247, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>  ", "_tel": "", "_url": "http://www.junyuhang.com/", "_name": "JYH", "_name_zh-cn": "君羽航", "_name_zh-hk": "君羽航", "_group": "12"}, {"key": 191367, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jykjwl.cn/", "_name": "JYKJ", "_name_zh-cn": "佳运跨境", "_name_zh-hk": "佳運跨境", "_group": "12"}, {"key": 191224, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0769-85515555", "_url": "https://www.jym56.cn/", "_name": "JYM Express", "_name_zh-cn": "加运美速递", "_name_zh-hk": "加運美速遞", "_group": "12"}, {"key": 191169, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13554995259", "_url": " http://www.jytd88.cn/", "_name": "JYTD", "_name_zh-cn": "捷易通达", "_name_zh-hk": "捷易通達", "_group": "12"}, {"key": 190875, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 17620388528", "_url": "http://www.jzgjgyl.com/", "_name": "JZGJ", "_name_zh-cn": "嘉至国际", "_name_zh-hk": "嘉至國際", "_group": "12"}, {"key": 191009, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.kxj56.com/", "_name": "KaixuanJia", "_name_zh-cn": "凯旋嘉供应链", "_name_zh-hk": "凱旋嘉供應鏈", "_group": "12"}, {"key": 100887, "_country": 215, "_country_iso": "BR", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.kangu.com.br/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 191120, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.kapoklogcn.com/", "_name": "Ka<PERSON>k<PERSON>", "_name_zh-cn": "盛顺物流", "_name_zh-hk": "盛順物流", "_group": "12"}, {"key": 11011, "_country": 1101, "_country_iso": "KZ", "_email": "", "_tel": "", "_url": "https://post.kz/", "_name": "Kaz Post", "_name_zh-cn": "Kaz Post", "_name_zh-hk": "Kaz Post", "_group": ""}, {"key": 190977, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.kckex.com/", "_name": "KCKEX", "_name_zh-cn": "胜宇国际", "_name_zh-hk": "勝宇國際", "_group": "12"}, {"key": 191202, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18938654991", "_url": "http://www.kco56.com/", "_name": "KCO logistics", "_name_zh-cn": "凯秀物流", "_name_zh-hk": "凱秀物流", "_group": "12"}, {"key": 100544, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "https://www.kdz.com/", "_name": "KDZ Express", "_name_zh-cn": "KDZ Express", "_name_zh-hk": "KDZ Express", "_group": "11"}, {"key": 191054, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 010-84546858", "_url": "https://www.kerrylogistics.com/", "_name": "KEAS", "_name_zh-cn": "嘉里大通", "_name_zh-hk": "嘉裡大通", "_group": "12"}, {"key": 11021, "_country": 1102, "_country_iso": "KE", "_email": "", "_tel": "", "_url": "http://www.posta.co.ke/", "_name": "Kenya Post", "_name_zh-cn": "Kenya Post", "_name_zh-hk": "Kenya Post", "_group": ""}, {"key": 190225, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.kerry-ecommerce.com/", "_name": "Kerry eCommerce", "_name_zh-cn": "Kerry eCommerce", "_name_zh-hk": "Kerry eCommerce", "_group": "12"}, {"key": 100634, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "+886 2 33225568", "_url": "http://www.kerryexpress.com.tw/", "_name": "Kerry Express", "_name_zh-cn": "嘉里快递", "_name_zh-hk": "嘉里快遞", "_group": "11"}, {"key": 100338, "_country": 801, "_country_iso": "HK", "_email": "<EMAIL>", "_tel": "+852 3513 0888", "_url": "https://hk.kerryexpress.com/home", "_name": "Kerry Express (HK)", "_name_zh-cn": "Kerry Express (HK)", "_name_zh-hk": "Kerry Express (HK)", "_group": "11"}, {"key": 100236, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "", "_url": "https://th.kerryexpress.com/", "_name": "Kerry Express (TH)", "_name_zh-cn": "嘉里物流(泰国)", "_name_zh-hk": "嘉里物流(泰國)", "_group": "11"}, {"key": 100704, "_country": 2001, "_country_iso": "TW", "_email": "<EMAIL>", "_tel": "+886 02 412-8999", "_url": "http://www.kerrytj.com/", "_name": "Kerry TJ", "_name_zh-cn": "嘉里大荣", "_name_zh-hk": "嘉里大榮", "_group": "11"}, {"key": 100064, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "", "_url": "https://my.kex-express.com/", "_name": "KEX Express (ABX Express)", "_name_zh-cn": "KEX Express (ABX Express)", "_name_zh-hk": "KEX Express (ABX Express)", "_group": "11"}, {"key": 100954, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>\t", "_tel": "+91-2209758", "_url": "https://www.khubaniairpack.com/", "_name": "Khubani Air Pack", "_name_zh-cn": "Khubani Air Pack", "_name_zh-hk": "Khubani Air Pack", "_group": "11"}, {"key": 190577, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jky-express.com/", "_name": "KING KERRY", "_name_zh-cn": "金开宇国际物流", "_name_zh-hk": "金開宇國際物流", "_group": "12"}, {"key": 190426, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.kke.com.hk/", "_name": "King Kong Express", "_name_zh-cn": "京广速递", "_name_zh-hk": "京廣速遞", "_group": "12"}, {"key": 101026, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "+61 (0) 3 8593 4168", "_url": "https://kinisitransport.com/", "_name": "Kinisi Transport", "_name_zh-cn": "Kinisi Transport", "_name_zh-hk": "Kinisi Transport", "_group": "11"}, {"key": 100632, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "https://www.kintetsu-ls.co.jp/", "_name": "KINTETSU LOGISTICS SYSTEMS (近鉄ロジスティクス)", "_name_zh-cn": "KINTETSU LOGISTICS SYSTEMS (近鉄ロジスティクス)", "_name_zh-hk": "KINTETSU LOGISTICS SYSTEMS (近鉄ロジスティクス)", "_group": "11"}, {"key": 100748, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "+81-52-582-2251", "_url": "https://www.kwe.com/", "_name": "Kintetsu World Express (近鉄エクスプレス [KWE])", "_name_zh-cn": "Kintetsu World Express (近鉄エクスプレス [KWE])", "_name_zh-hk": "Kintetsu World Express (近鉄エクスプレス [KWE])", "_group": "11"}, {"key": 11041, "_country": 1104, "_country_iso": "KI", "_email": "", "_tel": "", "_url": "http://kiribatipost.net.ki/", "_name": "Kiribati Post", "_name_zh-cn": "Kiribati Post", "_name_zh-hk": "Kiribati Post", "_group": ""}, {"key": 191290, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0769-850 95572", "_url": "http://www.gdkjk56.com/", "_name": "KJK Express", "_name_zh-cn": "快捷快物流", "_name_zh-hk": "快速物流", "_group": "12"}, {"key": 191124, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.kleexpress.com/", "_name": "KLE", "_name_zh-cn": "昆仑速达", "_name_zh-hk": "昆侖速達", "_group": "12"}, {"key": 100085, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "http://k-mestu.ru/", "_name": "<PERSON>-mestu", "_name_zh-cn": "<PERSON>-mestu", "_name_zh-hk": "<PERSON>-mestu", "_group": "11"}, {"key": 190366, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.komonexpress.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "可蒙国际", "_name_zh-hk": "可蒙國際", "_group": "12"}, {"key": 190452, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "(852)35014000", "_url": "http://konglok.com/", "_name": "Kong Lok Express", "_name_zh-cn": "港乐速邮", "_name_zh-hk": "港樂速郵", "_group": "12"}, {"key": 191234, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 75566701204", "_url": "http://www.kongfuexpress.com/", "_name": "KongFu", "_name_zh-cn": "功夫物流", "_name_zh-hk": "功夫物流", "_group": "12"}, {"key": 190606, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.kpksz.com/", "_name": "KPK", "_name_zh-cn": "凯尔凯", "_name_zh-hk": "凱爾凱", "_group": "12"}, {"key": 190431, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.krexi.com/", "_name": "Krexi international", "_name_zh-cn": "坤翔国际", "_name_zh-hk": "坤翔國際", "_group": "12"}, {"key": 100757, "_country": 321, "_country_iso": "CY", "_email": "<EMAIL>", "_tel": "+357 22 57 00 73", "_url": "https://www.kronosexpress.com/", "_name": "Kronos Express", "_name_zh-cn": "Kronos Express", "_name_zh-hk": "Kronos Express", "_group": "11"}, {"key": 190522, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.kuatongda.com/", "_name": "KTD", "_name_zh-cn": "KTD", "_name_zh-hk": "KTD", "_group": "12"}, {"key": 191102, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.kk-trans.com/ ", "_name": "KUAIKAI", "_name_zh-cn": "快凯国际", "_name_zh-hk": "快凱國際", "_group": "12"}, {"key": 191210, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.fjxmky56.com/", "_name": "KUAIYAN", "_name_zh-cn": "快雁", "_name_zh-hk": "快雁", "_group": "12"}, {"key": 190950, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.kydpost.com/", "_name": "kua<PERSON><PERSON><PERSON> ", "_name_zh-cn": "快越达物流", "_name_zh-hk": "快越達物流", "_group": "12"}, {"key": 190390, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.kuajingline56.com/", "_name": "KuaJing Line", "_name_zh-cn": "跨境在线", "_name_zh-hk": "跨境在線", "_group": "12"}, {"key": 100164, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-************", "_url": "https://home.kuehne-nagel.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190802, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.kunyiyc.com/", "_name": "KunYi", "_name_zh-cn": "鲲一", "_name_zh-hk": "鯤一", "_group": "12"}, {"key": 100660, "_country": 903, "_country_iso": "ID", "_email": "<EMAIL>", "_tel": "+(62) 821 2225 8170", "_url": "https://www.kurasi.co.id/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100585, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://www.tokopedia.com/kurir-rekomendasi", "_name": "<PERSON><PERSON><PERSON> (Tokopedia)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (Tokopedia)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (Tokopedia)", "_group": "11"}, {"key": 11081, "_country": 1108, "_country_iso": "KW", "_email": "", "_tel": "", "_url": "https://moc.gov.kw/", "_name": "Kuwait Post", "_name_zh-cn": "Kuwait Post", "_name_zh-hk": "Kuwait Post", "_group": ""}, {"key": 190876, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.kwaibon.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "桂邦运输", "_name_zh-hk": "桂邦運輸", "_group": "12"}, {"key": 100883, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+966-800 124 222 4", "_url": "https://www.kwick-box.com/", "_name": "Kwick box", "_name_zh-cn": "Kwick box", "_name_zh-hk": "Kwick box", "_group": "11"}, {"key": 100641, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "+****************", "_url": "https://kwiksavecourier.com/", "_name": "Kwiksave Logistics", "_name_zh-cn": "Kwiksave Logistics", "_name_zh-hk": "Kwiksave Logistics", "_group": "11"}, {"key": 190054, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.kwt56.com/", "_name": "KWT", "_name_zh-cn": "京华达物流", "_name_zh-hk": "京華達物流", "_group": "12"}, {"key": 191184, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18685950518", "_url": "https://www.kxexp.com/", "_name": "KxExp", "_name_zh-cn": "凯欣国际", "_name_zh-hk": "凱欣國際", "_group": "12"}, {"key": 191370, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "86-19565156338", "_url": "http://kuayida.cn/", "_name": "KYD", "_name_zh-cn": "跨易达", "_name_zh-hk": "跨易達", "_group": "12"}, {"key": 190551, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 95324", "_url": "https://ky-express.com/", "_name": "KYE", "_name_zh-cn": "跨越速运", "_name_zh-hk": "跨越速运", "_group": "12"}, {"key": 190845, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.ky-express.com/", "_name": "KYE (CN)", "_name_zh-cn": "跨越速运 (中国)", "_name_zh-hk": "跨越速運 (中國)", "_group": "12"}, {"key": 190611, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-84867451", "_url": "http://www.kylinowms.com/", "_name": "KYLIN", "_name_zh-cn": "麒麟海外仓", "_name_zh-hk": "麒麟海外倉", "_group": "12"}, {"key": 190453, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.qlinyun.com/", "_name": "Kylin Express", "_name_zh-cn": "麒麟物流", "_name_zh-hk": "麒麟物流", "_group": "12"}, {"key": 11094, "_country": 1109, "_country_iso": "KG", "_email": "", "_tel": "", "_url": "https://www.kep.kg/", "_name": "Kyrgyz Express Post(KEP)", "_name_zh-cn": "Kyrgyz Express Post(KEP)", "_name_zh-hk": "Kyrgyz Express Post(KEP)", "_group": ""}, {"key": 11091, "_country": 1109, "_country_iso": "KG", "_email": "", "_tel": "", "_url": "http://kyrgyzpost.kg/", "_name": "Kyrgyz Post", "_name_zh-cn": "Kyrgyz Post", "_name_zh-hk": "Kyrgyz Post", "_group": ""}, {"key": 6051, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "http://www.laposte.fr/", "_name": "La Poste (Colissimo)", "_name_zh-cn": "La Poste (Colissimo)", "_name_zh-hk": "La Poste (Colissimo)", "_group": ""}, {"key": 2081, "_country": 208, "_country_iso": "BJ", "_email": "", "_tel": "", "_url": "http://www.laposte.bj/", "_name": "La Poste De Benin", "_name_zh-cn": "La Poste De Benin", "_name_zh-hk": "La Poste De Benin", "_group": ""}, {"key": 19081, "_country": 1908, "_country_iso": "SN", "_email": "", "_tel": "", "_url": "http://www.laposte.sn/", "_name": "La Poste De Senegal", "_name_zh-cn": "La Poste De Senegal", "_name_zh-hk": "La Poste De Senegal", "_group": ""}, {"key": 20051, "_country": 2005, "_country_iso": "TG", "_email": "", "_tel": "", "_url": "http://www.laposte.tg/", "_name": "La Poste De Togo", "_name_zh-cn": "La Poste De Togo", "_name_zh-hk": "La Poste De Togo", "_group": ""}, {"key": 20101, "_country": 2010, "_country_iso": "TN", "_email": "", "_tel": "", "_url": "http://www.poste.tn/", "_name": "La Poste De Tunisia", "_name_zh-cn": "La Poste De Tunisia", "_name_zh-hk": "La Poste De Tunisia", "_group": ""}, {"key": 13071, "_country": 1307, "_country_iso": "ML", "_email": "", "_tel": "", "_url": "http://www.laposte.ml/", "_name": "La poste du Mali", "_name_zh-cn": "La poste du Mali", "_name_zh-hk": "La poste du Mali", "_group": ""}, {"key": 100529, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "", "_url": "https://www.mylabaih.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100021, "_country": 206, "_country_iso": "BE", "_email": "", "_tel": "", "_url": "https://landmarkglobal.com/", "_name": "Landmark Global", "_name_zh-cn": "Landmark Global", "_name_zh-hk": "Landmark Global", "_group": "11"}, {"key": 100052, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://www.lasership.com/", "_name": "LaserShip", "_name_zh-cn": "LaserShip", "_name_zh-hk": "LaserShip", "_group": "11"}, {"key": 191312, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.latexpress.com.cn/", "_name": "LAT Logistics", "_name_zh-cn": "LAT Logistics", "_name_zh-hk": "LAT Logistics", "_group": "12"}, {"key": 190504, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://latamyou.com/", "_name": "latam", "_name_zh-cn": "拉美邮", "_name_zh-hk": "拉美郵", "_group": "12"}, {"key": 12021, "_country": 1202, "_country_iso": "LV", "_email": "", "_tel": "", "_url": "http://www.pasts.lv/", "_name": "Latvia Post", "_name_zh-cn": "Latvia Post", "_name_zh-hk": "Latvia Post", "_group": ""}, {"key": 100997, "_country": 2204, "_country_iso": "VN", "_email": "<EMAIL>", "_tel": "+84-1900636828", "_url": "https://logistics.lazada.vn/", "_name": "Lazada Logistics (VN)", "_name_zh-cn": "Lazada Logistics (VN)", "_name_zh-hk": "Lazada Logistics (VN)", "_group": "11"}, {"key": 191066, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  0769 82076798", "_url": "http://www.longbiao56.net/", "_name": "LB Logistics", "_name_zh-cn": "龙飙物流", "_name_zh-hk": "龍飆物流", "_group": "12"}, {"key": 100264, "_country": 1607, "_country_iso": "PH", "_email": "<EMAIL>", "_tel": "+63 02 8858 5999", "_url": "https://www.lbcexpress.com/", "_name": "LBC Express", "_name_zh-cn": "LBC Express", "_name_zh-hk": "LBC Express", "_group": "11"}, {"key": 190676, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.lbtxkd.com/", "_name": "LBTX", "_name_zh-cn": "龙本天下", "_name_zh-hk": "龍本天下", "_group": "12"}, {"key": 191203, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-29601234", "_url": "http://www.lcda56.com/", "_name": "LCD", "_name_zh-cn": "联程达", "_name_zh-hk": "聯程達", "_group": "12"}, {"key": 190582, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.lcdgyl.com/", "_name": "LCDGYL", "_name_zh-cn": "联超达供应链", "_name_zh-hk": "聯超達供應鏈", "_group": "12"}, {"key": 100366, "_country": 1601, "_country_iso": "PK", "_email": "<EMAIL>", "_tel": "+92 (022) 3413001", "_url": "http://leopardscourier.com/pk/", "_name": "LCS-Leopards Courier Service", "_name_zh-cn": "LCS-Leopards Courier Service", "_name_zh-hk": "LCS-Leopards Courier Service", "_group": "11"}, {"key": 190686, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-15013555168", "_url": "https://www.ldgj56.com/", "_name": "LDGJ", "_name_zh-cn": "领东国际", "_name_zh-hk": "領東國際", "_group": "12"}, {"key": 191272, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://ldtgyl.cn/", "_name": "LDTGYL", "_name_zh-cn": "绿达通供应链", "_name_zh-hk": "綠達通供應鏈", "_group": "12"}, {"key": 190285, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.leader609.com/", "_name": "LEADER", "_name_zh-cn": "LEADER", "_name_zh-hk": "LEADER", "_group": "10"}, {"key": 190507, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ledii.cn/", "_name": "LEDii", "_name_zh-cn": "乐递供应链", "_name_zh-hk": "樂遞供應鏈", "_group": "12"}, {"key": 100351, "_country": 0, "_country_iso": "", "_email": "", "_tel": "+6569225614", "_url": "https://legionexp.com/", "_name": "Legion Express", "_name_zh-cn": "Legion Express", "_name_zh-hk": "Legion Express", "_group": "11"}, {"key": 190445, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ly-b2c.com/", "_name": "LeiYi international logistics", "_name_zh-cn": "深圳市雷翼国际物流有限公司", "_name_zh-hk": "深圳市雷翼國際物流有限公司", "_group": "12"}, {"key": 100886, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": "https://www.lentongrp.com/", "_name": "Lenton Group (DPD)", "_name_zh-cn": "Lenton Group (DPD)", "_name_zh-hk": "Lenton Group (DPD)", "_group": "11"}, {"key": 190451, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.tdexpress.net/", "_name": "lerdex", "_name_zh-cn": "乐易汇", "_name_zh-hk": "乐易汇", "_group": "12"}, {"key": 12041, "_country": 1204, "_country_iso": "LS", "_email": "", "_tel": "", "_url": "http://lesothopost.org.ls/", "_name": "Lesotho Post", "_name_zh-cn": "Lesotho Post", "_name_zh-hk": "Lesotho Post", "_group": ""}, {"key": 190223, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xdlex.com/", "_name": "LEX", "_name_zh-cn": "LEX", "_name_zh-hk": "LEX", "_group": "12"}, {"key": 100898, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 844 8444 097", "_url": "https://www.lexship.com/", "_name": "LEXSHIP", "_name_zh-cn": "LEXSHIP", "_name_zh-hk": "LEXSHIP", "_group": "11"}, {"key": 191199, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13319365626  ", "_url": "http://www.leyikj.com/", "_name": "Leyi Cross border", "_name_zh-cn": "乐伊跨境", "_name_zh-hk": "樂伊跨境", "_group": "12"}, {"key": 190645, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.lh-express.cn/", "_name": "LHE", "_name_zh-cn": "龙浩速运", "_name_zh-hk": "龍浩速運", "_group": "12"}, {"key": 191165, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ************", "_url": "http://www.lhtex.com.cn/", "_name": "LHT Express", "_name_zh-cn": "联昊通速递", "_name_zh-hk": "聯昊通速遞", "_group": "12"}, {"key": 191227, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.Lhtd56.com/", "_name": "LHTD", "_name_zh-cn": "龙海通达", "_name_zh-hk": "龍海通達", "_group": "12"}, {"key": 191338, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 - 0755-83212565  ", "_url": "http://www.lianyeint.com/", "_name": "lianye int.", "_name_zh-cn": "联烨国际", "_name_zh-hk": "聯燁國際", "_group": "12"}, {"key": 12031, "_country": 1203, "_country_iso": "LB", "_email": "", "_tel": "", "_url": "http://www.libanpost.com/", "_name": "Liban Post", "_name_zh-cn": "Liban Post", "_name_zh-hk": "Liban Post", "_group": ""}, {"key": 190422, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.lbexps.com/", "_name": "LiBang Logistics", "_name_zh-cn": "立邦国际物流", "_name_zh-hk": "立邦國際物流", "_group": "12"}, {"key": 12061, "_country": 1206, "_country_iso": "LY", "_email": "", "_tel": "", "_url": "http://libyapost.ly/", "_name": "Libya Post", "_name_zh-cn": "Libya Post", "_name_zh-hk": "Libya Post", "_group": ""}, {"key": 100494, "_country": 907, "_country_iso": "IT", "_email": "<EMAIL>", "_tel": "++39 081 5053642", "_url": "https://www.liccarditrasporti.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190347, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://hnlidi.cn/", "_name": "Lidi Logistics", "_name_zh-cn": "利滴物流", "_name_zh-hk": "利滴物流", "_group": "12"}, {"key": 100358, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+603 ************", "_url": "https://www.lineclearexpress.com/", "_name": "Line Clear Express", "_name_zh-cn": "Line Clear Express", "_name_zh-hk": "Line Clear Express", "_group": "11"}, {"key": 190473, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.linexsolutions.com/", "_name": "Linex", "_name_zh-cn": "Linex", "_name_zh-hk": "Linex", "_group": "12"}, {"key": 190971, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL> ", "_tel": "+86  051262730165    ", "_url": "http://www.lingyunexpress.com/", "_name": "LING YUN", "_name_zh-cn": "灵韵", "_name_zh-hk": "靈韻", "_group": "12"}, {"key": 190660, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://express-lj.com/", "_name": "Lingjing", "_name_zh-cn": "灵境", "_name_zh-hk": "靈境", "_group": "12"}, {"key": 190360, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.lingxun.top/", "_name": "LingXun Logistics", "_name_zh-cn": "领讯物流", "_name_zh-hk": "領訊物流", "_group": "10"}, {"key": 190973, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86  15919583687", "_url": "https://www.linhan56.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "林瀚国际", "_name_zh-hk": "林瀚國際", "_group": "12"}, {"key": 191293, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://shippingitx.com/", "_name": "Link Road", "_name_zh-cn": "来客乐", "_name_zh-hk": "來客樂", "_group": "12"}, {"key": 190964, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>    ", "_tel": "", "_url": "  http://sz-linkexpress.com/   ", "_name": "Link Supply Chain", "_name_zh-cn": "链客供应链", "_name_zh-hk": "鏈客供應鏈", "_group": "12"}, {"key": 191233, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.link-trans.com/", "_name": "linktrans", "_name_zh-cn": "联宇物流", "_name_zh-hk": "聯宇物流", "_group": "12"}, {"key": 190858, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 13779933291", "_url": "http://www.cnllexp.com/index.html", "_name": "LINLONG", "_name_zh-cn": "LINLONG", "_name_zh-hk": "LINLONG", "_group": "12"}, {"key": 100586, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "+62 21-80820072", "_url": "https://www.lionparcel.com/", "_name": "Lion parcel", "_name_zh-cn": "Lion parcel", "_name_zh-hk": "Lion parcel", "_group": "11"}, {"key": 100302, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://lionbay.express/", "_name": "LionBay", "_name_zh-cn": "LionBay", "_name_zh-hk": "LionBay", "_group": "11"}, {"key": 100984, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-20-3991-0223", "_url": "https://www.lionwheel.com/", "_name": "LionWheel", "_name_zh-cn": "LionWheel", "_name_zh-hk": "LionWheel", "_group": "11"}, {"key": 12081, "_country": 1208, "_country_iso": "LT", "_email": "", "_tel": "", "_url": "https://www.post.lt/", "_name": "Lithuania Post", "_name_zh-cn": "Lithuania Post", "_name_zh-hk": "Lithuania Post", "_group": ""}, {"key": 190873, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.liuchenwl.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "六尘国际", "_name_zh-hk": "六塵國際", "_group": "12"}, {"key": 190779, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13600423522", "_url": "http://www.b-whale.cn/", "_name": "LJKJ56", "_name_zh-cn": "蓝鲸物流", "_name_zh-hk": "藍鯨物流", "_group": "12"}, {"key": 190679, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 18988792022", "_url": "http://www.llgj.ltd/", "_name": "LLGJ", "_name_zh-cn": "路龙国际", "_name_zh-hk": "路龍國際", "_group": "12"}, {"key": 190380, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.lmparcel.com/", "_name": "LMSY", "_name_zh-cn": "六脉速运", "_name_zh-hk": "六脈速運", "_group": "12"}, {"key": 191110, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.ynex.net/", "_name": "lnon Supply Chain", "_name_zh-cn": "英诺供应链", "_name_zh-hk": "英諾供應鏈", "_group": "12"}, {"key": 190993, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.logbay.com.cn/", "_name": "Logbay", "_name_zh-cn": "琉贝", "_name_zh-hk": "琉貝", "_group": "12"}, {"key": 100618, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+82 1588-9988", "_url": "https://www.ilogen.com/", "_name": "<PERSON><PERSON> (로젠택배)", "_name_zh-cn": "<PERSON><PERSON> (로젠택배)", "_name_zh-hk": "<PERSON><PERSON> (로젠택배)", "_group": "11"}, {"key": 100457, "_country": 215, "_country_iso": "BR", "_email": "", "_tel": "+551 (*************", "_url": "https://www.loggi.com/", "_name": "Loggi Express BR", "_name_zh-cn": "Loggi Express BR", "_name_zh-hk": "Loggi Express BR", "_group": "11"}, {"key": 190461, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.logisters.com/", "_name": "Logisters", "_name_zh-cn": "随波物流", "_name_zh-hk": "隨波物流", "_group": "12"}, {"key": 190884, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.logisticstracking.cn/", "_name": "Logistics Tracking", "_name_zh-cn": "Logistics Tracking", "_name_zh-hk": "Logistics Tracking", "_group": "12"}, {"key": 100884, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.logoix.com/", "_name": "LogoiX", "_name_zh-cn": "LogoiX", "_name_zh-hk": "LogoiX", "_group": "11"}, {"key": 191028, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86  (0512) 6535 6859", "_url": "http://www.logsoeasy.com/", "_name": "logsoeasy", "_name_zh-cn": "三合源", "_name_zh-hk": "叁合源", "_group": "12"}, {"key": 191058, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 020-36675568 ", "_url": "http://www.longxingz.com/", "_name": "LONGXIN", "_name_zh-cn": "龙信物流", "_name_zh-hk": "龍信物流", "_group": "12"}, {"key": 100205, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "******-256-6647", "_url": "https://www.loomisexpress.com/", "_name": "Loomis Express", "_name_zh-cn": "Loomis Express", "_name_zh-hk": "Loomis Express", "_group": "11"}, {"key": 100506, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+82 – 2 – 1588 - 2121", "_url": "https://www.lotteglogis.com/english/reservation/track/", "_name": "Lotte Global Logistics", "_name_zh-cn": "Lotte Global Logistics", "_name_zh-hk": "Lotte Global Logistics", "_group": "11"}, {"key": 100969, "_country": 1803, "_country_iso": "RU", "_email": "<EMAIL>", "_tel": "**************", "_url": "https://l-post.ru/", "_name": "L-POST", "_name_zh-cn": "L-POST", "_name_zh-hk": "L-POST", "_group": "11"}, {"key": 100337, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.lso.com/", "_name": "LSO(Lone Star Overnight)", "_name_zh-cn": "LSO(Lone Star Overnight)", "_name_zh-hk": "LSO(Lone Star Overnight)", "_group": "11"}, {"key": 191332, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-17722682958", "_url": "http://lsztec.com/", "_name": "LSZ", "_name_zh-cn": "链四洲", "_name_zh-hk": "鏈四洲", "_group": "12"}, {"key": 190172, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ltexp.com.cn/", "_name": "LTEXP", "_name_zh-cn": "乐天速递", "_name_zh-hk": "樂天速遞", "_group": "12"}, {"key": 190274, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.jiayouexp.com", "_name": "LTIAN EXP", "_name_zh-cn": "佳邮国际", "_name_zh-hk": "佳郵國際", "_group": "12"}, {"key": 190386, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.luckinpost.com/", "_name": "Luckinpost ", "_name_zh-cn": "邮好运", "_name_zh-hk": "郵好運", "_group": "12"}, {"key": 191078, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.lucksoonlogistics.com/", "_name": "Lucksoon", "_name_zh-cn": "好运极达", "_name_zh-hk": "好運極達", "_group": "12"}, {"key": 12101, "_country": 1210, "_country_iso": "LU", "_email": "", "_tel": "", "_url": "https://www.post.lu/", "_name": "Luxembourg Post", "_name_zh-cn": "Luxembourg Post", "_name_zh-hk": "Luxembourg Post", "_group": ""}, {"key": 100020, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+852 3421 1122", "_url": "http://www.lwe.asia/", "_name": "LWE", "_name_zh-cn": "LWE", "_name_zh-hk": "LWE", "_group": "11"}, {"key": 191038, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szlxgyl.com/   ", "_name": "LXGYL", "_name_zh-cn": "联行供应链", "_name_zh-hk": "聯行供應鏈", "_group": "12"}, {"key": 190362, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 13723719257 ", "_url": "http://www.longyuanint.com/", "_name": "LY", "_name_zh-cn": "LY", "_name_zh-hk": "LY", "_group": "12"}, {"key": 191261, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 13530303606 ", "_url": "http://www.lyst56.com/", "_name": "LYST", "_name_zh-cn": "龙运速通", "_name_zh-hk": "龍運速通", "_group": "12"}, {"key": 190713, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "************", "_url": "http://www.lzfba.cn/", "_name": "LZFBA", "_name_zh-cn": "乐众物流", "_name_zh-hk": "樂眾物流", "_group": "12"}, {"key": 100720, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+60 1300-88-87-88", "_url": "https://www.mxpress2u.com/", "_name": "M XPRESS", "_name_zh-cn": "M XPRESS", "_name_zh-hk": "M XPRESS", "_group": "11"}, {"key": 191026, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://gw.mhgj-express.com/", "_name": "M&H", "_name_zh-cn": "茂泓国际", "_name_zh-hk": "茂泓國際", "_group": "12"}, {"key": 100374, "_country": 1601, "_country_iso": "PK", "_email": "<EMAIL>", "_tel": "+92 (021) 111-202-202", "_url": "https://www.mulphilog.com/", "_name": "M&P Courier", "_name_zh-cn": "M&P Courier", "_name_zh-hk": "M&P Courier", "_group": "11"}, {"key": 100970, "_country": 115, "_country_iso": "AU", "_email": "\t<EMAIL>", "_tel": "+61-(02) 8610 2000", "_url": "https://m3logistics.com/", "_name": "M3 Logistics", "_name_zh-cn": "M3 Logistics", "_name_zh-hk": "M3 Logistics", "_group": "11"}, {"key": 13011, "_country": 1301, "_country_iso": "MO", "_email": "", "_tel": "", "_url": "http://www.macaupost.gov.mo/", "_name": "Macau Post", "_name_zh-cn": "Macau Post", "_name_zh-hk": "Macau Post", "_group": ""}, {"key": 13021, "_country": 1302, "_country_iso": "MK", "_email": "", "_tel": "", "_url": "https://www.posta.com.mk/", "_name": "Macedonia Post", "_name_zh-cn": "Macedonia Post", "_name_zh-hk": "Macedonia Post", "_group": ""}, {"key": 100710, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "https://www.madhurcouriers.in/", "_name": "Madhur Courier Services", "_name_zh-cn": "Madhur Courier Services", "_name_zh-hk": "Madhur Courier Services", "_group": "11"}, {"key": 190356, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.madrooex.com/", "_name": "Madrooex", "_name_zh-cn": "Madrooex", "_name_zh-hk": "Madrooex", "_group": "12"}, {"key": 101016, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34-911-807-239", "_url": "https://web.maegmant.es/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100768, "_country": 401, "_country_iso": "DK", "_email": "", "_tel": "", "_url": "https://www.maersk.com/", "_name": "Maersk", "_name_zh-cn": "马士基", "_name_zh-hk": "馬士基", "_group": "11"}, {"key": 191311, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "www.et-global.cn/", "_name": "Magic Land", "_name_zh-cn": "神州通", "_name_zh-hk": "神州通", "_group": "12"}, {"key": 101023, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+1-(800)726-7510", "_url": "https://magnumlog.com/", "_name": "Magnum", "_name_zh-cn": "Magnum", "_name_zh-hk": "Magnum", "_group": "11"}, {"key": 8051, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "http://posta.hu/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": ""}, {"key": 100832, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34 933 624 730", "_url": "https://www.mbe.es/", "_name": "Mail Boxes Etc (MBE)", "_name_zh-cn": "Mail Boxes Etc (MBE)", "_name_zh-hk": "Mail Boxes Etc (MBE)", "_group": "11"}, {"key": 100389, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "+49  0931 660574-225", "_url": "https://www.mailalliance.net/", "_name": "Mailalliance", "_name_zh-cn": "Mailalliance", "_name_zh-hk": "Mailalliance", "_group": "11"}, {"key": 13134, "_country": 1313, "_country_iso": "MU", "_email": "", "_tel": "", "_url": "http://mailamericas.com/", "_name": "MailAmericas", "_name_zh-cn": "MailAmericas", "_name_zh-hk": "MailAmericas", "_group": ""}, {"key": 100976, "_country": 906, "_country_iso": "IL", "_email": "<EMAIL>", "_tel": "+972-07-23387200", "_url": "https://www.mailogs.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100507, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "https://www.mainfreight.com/track/", "_name": "Mainfreight", "_name_zh-cn": "Mainfreight", "_name_zh-hk": "Mainfreight", "_group": "11"}, {"key": 100860, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "****(495)646-29-49", "_url": "https://www.major-express.ru/", "_name": "Major Express", "_name_zh-cn": "Major Express", "_name_zh-hk": "Major Express", "_group": "11"}, {"key": 100843, "_country": 1607, "_country_iso": "PH", "_email": "<EMAIL>", "_tel": "+63-(046)430-5853", "_url": "https://makati-express.com/", "_name": "MAKATI EXPRESS ", "_name_zh-cn": "MAKATI EXPRESS ", "_name_zh-hk": "MAKATI EXPRESS ", "_group": "11"}, {"key": 190475, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.maikf.com/", "_name": "Make Fly", "_name_zh-cn": "迈客丰国际物流", "_name_zh-hk": "邁客豐國際物流", "_group": "12"}, {"key": 100752, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-840-8330", "_url": "https://www.malca-amit.com/", "_name": "Malca-Amit", "_name_zh-cn": "Malca-Amit", "_name_zh-hk": "Malca-Amit", "_group": "11"}, {"key": 13061, "_country": 1306, "_country_iso": "MV", "_email": "", "_tel": "", "_url": "https://www.maldivespost.com/", "_name": "Maldives Post", "_name_zh-cn": "Maldives Post", "_name_zh-hk": "Maldives Post", "_group": ""}, {"key": 13081, "_country": 1308, "_country_iso": "MT", "_email": "", "_tel": "", "_url": "http://www.maltapost.com/", "_name": "Malta Post", "_name_zh-cn": "Malta Post", "_name_zh-hk": "Malta Post", "_group": ""}, {"key": 100909, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+************", "_url": "https://www.25431010.tw/", "_name": "Maple Logistics (便利帶)", "_name_zh-cn": "便利带", "_name_zh-hk": "便利帶", "_group": "11"}, {"key": 100606, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "http://markexpress.co.in/", "_name": "Mark Express", "_name_zh-cn": "Mark Express", "_name_zh-hk": "Mark Express", "_group": "11"}, {"key": 100735, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 022-26814000", "_url": "https://marutiair.com/", "_name": "Maruti Air", "_name_zh-cn": "Maruti Air", "_name_zh-hk": "Maruti Air", "_group": "11"}, {"key": 190664, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.masspack-express.com/", "_name": "MASSPACK", "_name_zh-cn": "千百包", "_name_zh-hk": "千百包", "_group": "12"}, {"key": 100386, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": "https://shifu-china.com/", "_name": "Master-<PERSON><PERSON>", "_name_zh-cn": "Master-<PERSON><PERSON>", "_name_zh-hk": "Master-<PERSON><PERSON>", "_group": "11"}, {"key": 100161, "_country": 604, "_country_iso": "FI", "_email": "", "_tel": "", "_url": "https://www.matkahuolto.fi/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 13131, "_country": 1313, "_country_iso": "MU", "_email": "", "_tel": "", "_url": "http://www.mauritiuspost.mu/", "_name": "Mauritius Post", "_name_zh-cn": "Mauritius Post", "_name_zh-hk": "Mauritius Post", "_group": ""}, {"key": 190526, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.maxway-logistics.com/", "_name": "MAXPRESS", "_name_zh-cn": "MAXPRESS", "_name_zh-hk": "MAXPRESS", "_group": "12"}, {"key": 100680, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "+33 0820 439 159", "_url": "https://www.mazet.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 191363, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-18853980088", "_url": "http://www.mengchuanhy.cn/", "_name": "MCGYL", "_name_zh-cn": "蒙川国际", "_name_zh-hk": "蒙川國際", "_group": "12"}, {"key": 190871, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://mailiancn.com/", "_name": "MCN", "_name_zh-cn": "麦链中国", "_name_zh-hk": "麥鏈中國", "_group": "12"}, {"key": 190278, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www1.mctransexpress.cn/", "_name": "MCTrans", "_name_zh-cn": "马可达", "_name_zh-hk": "馬可達", "_group": "12"}, {"key": 100350, "_country": 1917, "_country_iso": "ZA", "_email": "<EMAIL>", "_tel": "+27 (*************", "_url": "https://collivery.net/", "_name": "MDS Collivery", "_name_zh-cn": "MDS Collivery", "_name_zh-hk": "MDS Collivery", "_group": "11"}, {"key": 191322, "_country": 301, "_country_iso": "CN", "_email": "<PERSON>@maidatong.com", "_tel": "+86-18820946635", "_url": "https://www.maidatong.com/", "_name": "MDTGJ", "_name_zh-cn": "迈达通国际", "_name_zh-hk": "邁達通國際", "_group": "12"}, {"key": 100023, "_country": 2102, "_country_iso": "UA", "_email": "", "_tel": "", "_url": "http://meest-group.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100952, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-9903367865", "_url": "http://www.megacitycourier.in/", "_name": "Megacity Courier", "_name_zh-cn": "Megacity Courier", "_name_zh-hk": "Megacity Courier", "_group": "11"}, {"key": 191238, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13823595066", "_url": "http://www.mega-swiftsz.com/", "_name": "mega-swift", "_name_zh-cn": "极速鸟", "_name_zh-hk": "極速鳥", "_group": "12"}, {"key": 191136, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>, <EMAIL>", "_tel": "+86-18301756660", "_url": "http://www.iextrans.com/", "_name": "MEIHUAN", "_name_zh-cn": "美幻国际", "_name_zh-hk": "美幻國際", "_group": "12"}, {"key": 190883, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 17375138641", "_url": "http://www.my-express.cn/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "美易国际", "_name_zh-hk": "美易國際", "_group": "12"}, {"key": 100362, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-723-5205", "_url": "https://www.shipmercury.com/", "_name": "Mercury", "_name_zh-cn": "Mercury", "_name_zh-hk": "Mercury", "_group": "11"}, {"key": 191228, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.mesoglobal.cc/", "_name": "Meso-global", "_name_zh-cn": "中际跨境", "_name_zh-hk": "中際跨境", "_group": "12"}, {"key": 191362, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-13798365150", "_url": "https://metooda.cn/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "美通达跨境", "_name_zh-hk": "美通達跨境", "_group": "12"}, {"key": 100929, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.gomwd.com/", "_name": "Metropolitan", "_name_zh-cn": "Metropolitan", "_name_zh-hk": "Metropolitan", "_group": "11"}, {"key": 13141, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "", "_url": "https://www.correosdemexico.gob.mx/", "_name": "Mexico Post", "_name_zh-cn": "Mexico Post", "_name_zh-hk": "Mexico Post", "_group": ""}, {"key": 100455, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.meyerdistributing.com/", "_name": "Meyer Distribution", "_name_zh-cn": "Meyer Distribution", "_name_zh-hk": "Meyer Distribution", "_group": "11"}, {"key": 100515, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.meyer-jumbo.de/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 191163, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 （0579）8475  1301", "_url": "http://www.mfgjkd.com/", "_name": "MF", "_name_zh-cn": "迈峰国际", "_name_zh-hk": "邁峰國際", "_group": "12"}, {"key": 100798, "_country": 1323, "_country_iso": "MM", "_email": "<EMAIL>", "_tel": "+95 094560008373", "_url": "https://mglexpress.com.mm/", "_name": "MGL Express (Magnate Group Logistics)", "_name_zh-cn": "MGL Express (Magnate Group Logistics)", "_name_zh-hk": "MGL Express (Magnate Group Logistics)", "_group": "11"}, {"key": 190525, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 400-1274-978", "_url": "https://www.mgoship.com/", "_name": "mgoship", "_name_zh-cn": "海豚日本仓", "_name_zh-hk": "海豚日本倉", "_group": "12"}, {"key": 100787, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 (0) ************", "_url": "https://www.mhi.co/", "_name": "MHI", "_name_zh-cn": "MHI", "_name_zh-hk": "MHI", "_group": "11"}, {"key": 190896, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://cn.mia-express.com/", "_name": "MIA ", "_name_zh-cn": "杭州幂雅", "_name_zh-hk": "杭州冪雅", "_group": "12"}, {"key": 100201, "_country": 0, "_country_iso": "", "_email": "<EMAIL>", "_tel": "", "_url": "http://api.mircoexpress.com/", "_name": "Micro Express", "_name_zh-cn": "Micro Express", "_name_zh-hk": "Micro Express", "_group": "11"}, {"key": 190954, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 023-65061666 ", "_url": "http://www.mdqglb.com/", "_name": "MIDUOQI GLOBAL", "_name_zh-cn": "米多奇国际", "_name_zh-hk": "米多奇國際", "_group": "12"}, {"key": 100985, "_country": 906, "_country_iso": "IL", "_email": "<EMAIL>", "_tel": "+972 ************", "_url": "https://millerdeliveries.co.il/", "_name": "Miller Deliveries (מילר משלוחים)", "_name_zh-cn": "Miller Deliveries (מילר משלוחים)", "_name_zh-hk": "Miller Deliveries (מילר משלוחים)", "_group": "11"}, {"key": 190545, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.mingda-express.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "明大国际物流", "_name_zh-hk": "明大國際物流", "_group": "12"}, {"key": 191042, "_country": 301, "_country_iso": "CN", "_email": "764156764@qq,com", "_tel": "+86  13826124868", "_url": "http://www.minghao56.com/", "_name": "Minghao International", "_name_zh-cn": "铭豪国际", "_name_zh-hk": "銘豪國際", "_group": "12"}, {"key": 190781, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.miyu56.com/", "_name": "Mingyu Logistics", "_name_zh-cn": "铭宇国际物流", "_name_zh-hk": "銘宇國際物流", "_group": "12"}, {"key": 190063, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.mz56.com/", "_name": "Mingzhi", "_name_zh-cn": "铭志国际", "_name_zh-hk": "銘志國際", "_group": "10"}, {"key": 190027, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.miuson.net/", "_name": "MIUSON", "_name_zh-cn": "淼信国际", "_name_zh-hk": "淼信國際", "_group": "12"}, {"key": 191361, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.meijie56.cn/", "_name": "MJ", "_name_zh-cn": "美捷国际", "_name_zh-hk": "美捷國際", "_group": "12"}, {"key": 190516, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "+52 563338888", "_url": "http://www.mkdexpress.com.mx/", "_name": "MKD", "_name_zh-cn": "MKD", "_name_zh-hk": "MKD", "_group": "12"}, {"key": 100608, "_country": 2011, "_country_iso": "TR", "_email": "", "_tel": "+90 8502220606", "_url": "https://www.mngkargo.com.tr/", "_name": "MNG Kargo", "_name_zh-cn": "MNG Kargo", "_name_zh-hk": "MNG Kargo", "_group": "11"}, {"key": 13161, "_country": 1316, "_country_iso": "MD", "_email": "", "_tel": "", "_url": "http://www.posta.md/", "_name": "Moldova Post", "_name_zh-cn": "Moldova Post", "_name_zh-hk": "Moldova Post", "_group": ""}, {"key": 100304, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "+33 09 69 32 23 32", "_url": "https://www.mondialrelay.fr/", "_name": "Mondial Relay", "_name_zh-cn": "Mondial Relay", "_name_zh-hk": "Mondial Relay", "_group": "11"}, {"key": 13181, "_country": 1318, "_country_iso": "MN", "_email": "", "_tel": "", "_url": "http://www.mongolpost.mn/", "_name": "Mongol Post", "_name_zh-cn": "Mongol Post", "_name_zh-hk": "Mongol Post", "_group": ""}, {"key": 101029, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://monsotrack.com/", "_name": "Monsotrack", "_name_zh-cn": "Monsotrack", "_name_zh-hk": "Monsotrack", "_group": "11"}, {"key": 13191, "_country": 1319, "_country_iso": "ME", "_email": "", "_tel": "", "_url": "http://www.postacg.me/", "_name": "Montenegro Post", "_name_zh-cn": "Montenegro Post", "_name_zh-hk": "Montenegro Post", "_group": ""}, {"key": 101050, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-716-6787", "_url": "https://www.morantransportation.com/", "_name": "Moran Transportation Corporation", "_name_zh-cn": "Moran Transportation Corporation", "_name_zh-hk": "Moran Transportation Corporation", "_group": "11"}, {"key": 190643, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gzmywl.com/", "_name": "More Logistics", "_name_zh-cn": "茂业物流", "_name_zh-hk": "茂業物流", "_group": "12"}, {"key": 190491, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "************", "_url": "http://www.morelink56.cn", "_name": "MORELINK", "_name_zh-cn": "MORELINK", "_name_zh-hk": "MORELINK", "_group": "12"}, {"key": 100531, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": "https://www.morninglobal.com/", "_name": "Morning Global", "_name_zh-cn": "Morning Global", "_name_zh-hk": "Morning Global", "_group": "11"}, {"key": 13211, "_country": 1321, "_country_iso": "MA", "_email": "", "_tel": "", "_url": "http://www.poste.ma/", "_name": "Morocco Post", "_name_zh-cn": "Morocco Post", "_name_zh-hk": "Morocco Post", "_group": ""}, {"key": 101056, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.mothership.com/", "_name": "Mothership", "_name_zh-cn": "Mothership", "_name_zh-hk": "Mothership", "_group": "11"}, {"key": 100951, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-1800227171", "_url": "https://www.movin.in/", "_name": "MOVIN", "_name_zh-cn": "MOVIN", "_name_zh-hk": "MOVIN", "_group": "11"}, {"key": 100640, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 1300 164 797", "_url": "https://www.mrlglobal.com/", "_name": "MRL Global", "_name_zh-cn": "MRL Global", "_name_zh-hk": "MRL Global", "_group": "11"}, {"key": 100175, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.mrw.es/", "_name": "MRW", "_name_zh-cn": "MRW", "_name_zh-hk": "MRW", "_group": "11"}, {"key": 190334, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.mosuda.com/", "_name": "MSD", "_name_zh-cn": "魔速达", "_name_zh-hk": "魔速達", "_group": "10"}, {"key": 190745, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.mss1688.com/", "_name": "MSS", "_name_zh-cn": "MSS", "_name_zh-hk": "MSS", "_group": "12"}, {"key": 100383, "_country": 1924, "_country_iso": "SE", "_email": "", "_tel": "", "_url": "https://www.mtd.se/", "_name": "MTD", "_name_zh-cn": "MTD", "_name_zh-hk": "MTD", "_group": "11"}, {"key": 100418, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "https://www.fba.jp/", "_name": "MTO EXPRESS", "_name_zh-cn": "銘東物流", "_name_zh-hk": "銘東物流", "_group": "11"}, {"key": 190808, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://aftervehicle.com/", "_name": "MTS", "_name_zh-cn": "微霆速", "_name_zh-hk": "微霆速", "_group": "12"}, {"key": 191170, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.mtswl.cn/\t", "_name": "MTS", "_name_zh-cn": "美特顺物流", "_name_zh-hk": "美特順物流", "_group": "12"}, {"key": 100605, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 (11) 40576384,85,86,87", "_url": "https://www.muditacargo.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100294, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "", "_url": "https://portal.multransglobalmail.com/tracking/", "_name": "Multrans Logistics", "_name_zh-cn": "Multrans Logistics", "_name_zh-hk": "Multrans Logistics", "_group": "11"}, {"key": 191286, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.muserxm.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "沐瑟", "_name_zh-hk": "沐瑟", "_group": "12"}, {"key": 190795, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.mxlexp.com/", "_name": "MXL", "_name_zh-cn": "民祥国际", "_name_zh-hk": "民祥國際", "_group": "12"}, {"key": 191087, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  15871460413", "_url": "http://www.zsmy56.com/", "_name": "MY", "_name_zh-cn": "明扬国际", "_name_zh-hk": "明陽國際", "_group": "12"}, {"key": 191348, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-4009601567", "_url": "http://www.myline56.com/", "_name": "MY", "_name_zh-cn": "迈运供应链", "_name_zh-hk": "邁運供應鏈", "_group": "12"}, {"key": 13231, "_country": 1323, "_country_iso": "MM", "_email": "", "_tel": "", "_url": "https://www.myanmarpost.com.mm/", "_name": "Myanmar Post", "_name_zh-cn": "Myanmar Post", "_name_zh-hk": "Myanmar Post", "_group": ""}, {"key": 190401, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.mydservice.cn/", "_name": "MYD", "_name_zh-cn": "满壹滴", "_name_zh-hk": "滿壹滴", "_group": "10"}, {"key": 100933, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://tracking.myib.com/", "_name": "Myib International Bridge", "_name_zh-cn": "Myib International Bridge", "_name_zh-hk": "Myib International Bridge", "_group": "11"}, {"key": 100693, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+41 44 786 9511", "_url": "https://home.kuehne-nagel.com/", "_name": "myKN", "_name_zh-cn": "myKN", "_name_zh-hk": "myKN", "_group": "11"}, {"key": 100821, "_country": 502, "_country_iso": "EG", "_email": "", "_tel": "+20 233 336 871", "_url": "https://www.mylerz.com/", "_name": "MyLerz", "_name_zh-cn": "MyLerz", "_name_zh-hk": "MyLerz", "_group": "11"}, {"key": 190638, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.myloop.plus/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "速锐", "_name_zh-hk": "速銳", "_group": "12"}, {"key": 100436, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34 900 100 000", "_url": "https://www.nacex.com/", "_name": "Nacex", "_name_zh-cn": "Nacex", "_name_zh-hk": "Nacex", "_group": "11"}, {"key": 14011, "_country": 1401, "_country_iso": "NA", "_email": "", "_tel": "", "_url": "http://www.nampost.com.na/", "_name": "Namibia Post", "_name_zh-cn": "Namibia Post", "_name_zh-hk": "Namibia Post", "_group": ""}, {"key": 100089, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+86 0755-23227892  +966 920020505/8002464444", "_url": "https://www.naqelexpress.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100581, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "", "_url": "https://nasxpress.com/", "_name": "NAS Express", "_name_zh-cn": "NAS Express", "_name_zh-hk": "NAS Express", "_group": "11"}, {"key": 100579, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://www.nationex.com/", "_name": "Nationex", "_name_zh-cn": "Nationex", "_name_zh-hk": "Nationex", "_group": "11"}, {"key": 100369, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "https://www.nationwide2u.com/", "_name": "Nationwide Express", "_name_zh-cn": "Nationwide Express", "_name_zh-hk": "Nationwide Express", "_group": "11"}, {"key": 191189, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "http://www.jitongexp.com/", "_name": "NBJT", "_name_zh-cn": "际通国际速递", "_name_zh-hk": "際通國際速遞", "_group": "12"}, {"key": 190387, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.ofo56.com/", "_name": "NBT", "_name_zh-cn": "南北通", "_name_zh-hk": "南北通", "_group": "10"}, {"key": 191307, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://ceshi.ehezi.net/", "_name": "NBT", "_name_zh-cn": "南波兔", "_name_zh-hk": "南波兔", "_group": "12"}, {"key": 101011, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "", "_url": "https://ndtl.com.au/", "_name": "Needletail Logistics", "_name_zh-cn": "Needletail Logistics", "_name_zh-hk": "Needletail Logistics", "_group": "11"}, {"key": 100864, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "******-683-5708 ", "_url": "https://neighbourexpress.com/", "_name": "Neighbour Express", "_name_zh-cn": "Neighbour Express", "_name_zh-hk": "Neighbour Express", "_group": "11"}, {"key": 14031, "_country": 1403, "_country_iso": "NP", "_email": "", "_tel": "", "_url": "http://www.gpo.gov.np/", "_name": "Nepal Post", "_name_zh-cn": "Nepal Post", "_name_zh-hk": "Nepal Post", "_group": ""}, {"key": 100385, "_country": 0, "_country_iso": "", "_email": "<EMAIL>", "_tel": "", "_url": "http://netspaitalia.com/", "_name": "NET SPA", "_name_zh-cn": "NET SPA", "_name_zh-hk": "NET SPA", "_group": "11"}, {"key": 100592, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "+64 0800 655 010", "_url": "https://www.nzcouriers.co.nz/", "_name": "New Zealand Couriers", "_name_zh-cn": "New Zealand Couriers", "_name_zh-hk": "New Zealand Couriers", "_group": "11"}, {"key": 100721, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "+61 (02) 9748 3333", "_url": "https://newaytransport.com/", "_name": "Neway Transport", "_name_zh-cn": "Neway Transport", "_name_zh-hk": "Neway Transport", "_group": "11"}, {"key": 191244, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>\t", "_tel": "", "_url": "http://www.newbee888.com/", "_name": "NEWBEE", "_name_zh-cn": "新蜂国际货运", "_name_zh-hk": "新蜂國際貨運", "_group": "12"}, {"key": 190945, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-21010403 ", "_url": "http://www.newfengda.com", "_name": "Newfengda ", "_name_zh-cn": "新丰达", "_name_zh-hk": "新豐達", "_group": "12"}, {"key": 100395, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.newgistics.com/", "_name": "Newgistics", "_name_zh-cn": "Newgistics", "_name_zh-hk": "Newgistics", "_group": "11"}, {"key": 190698, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13750053877", "_url": "https://www.newhandle.cn/", "_name": "Newhandle", "_name_zh-cn": "新航道", "_name_zh-hk": "新航道", "_group": "12"}, {"key": 190975, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86（0755）86717442", "_url": "http://www.newvistacargo.net/", "_name": "Newvista", "_name_zh-cn": "新景韵国际", "_name_zh-hk": "新景韻國際", "_group": "12"}, {"key": 100087, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://www.nexive.it/", "_name": "Nexive", "_name_zh-cn": "Nexive", "_name_zh-hk": "Nexive", "_group": "11"}, {"key": 190789, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://fulfillship.nextsmartship.com", "_name": "NextSmartShip", "_name_zh-cn": "NextSmartShip", "_name_zh-hk": "NextSmartShip", "_group": "12"}, {"key": 100911, "_country": 2204, "_country_iso": "VN", "_email": "<EMAIL>", "_tel": "+84-1900 63 6688", "_url": "https://www.ntlogistics.vn/", "_name": "Nhất Tín Logistics", "_name_zh-cn": "Nhất Tín Logistics", "_name_zh-hk": "Nhất Tín Logistics", "_group": "11"}, {"key": 14071, "_country": 1407, "_country_iso": "NI", "_email": "", "_tel": "", "_url": "https://www.correos.gob.ni/", "_name": "Nicaragua Post", "_name_zh-cn": "Nicaragua Post", "_name_zh-hk": "Nicaragua Post", "_group": ""}, {"key": 14101, "_country": 1410, "_country_iso": "NG", "_email": "", "_tel": "", "_url": "https://www.nipost.gov.ng/", "_name": "Nigerian Post", "_name_zh-cn": "Nigerian Post", "_name_zh-hk": "Nigerian Post", "_group": ""}, {"key": 100729, "_country": 2004, "_country_iso": "TH", "_email": "<EMAIL>", "_tel": "+66 02-096-3333", "_url": "https://www.nimexpress.com/", "_name": "Nim Express (นิ่มเอ็กซ์เพรส )", "_name_zh-cn": "Nim Express (นิ่มเอ็กซ์เพรส )", "_name_zh-hk": "Nim Express (นิ่มเอ็กซ์เพรส )", "_group": "11"}, {"key": 100460, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 07290030101", "_url": "https://nimbuspost.com/", "_name": "Nimbus Post", "_name_zh-cn": "Nimbus Post", "_name_zh-hk": "Nimbus Post", "_group": "11"}, {"key": 100597, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "+65 6602 8271", "_url": "https://www.ninjavan.co/en-sg/tracking", "_name": "<PERSON> (International Tracking)", "_name_zh-cn": "<PERSON> (International Tracking)", "_name_zh-hk": "<PERSON> (International Tracking)", "_group": "11"}, {"key": 100125, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://www.ninjaxpress.co/id-id", "_name": "<PERSON><PERSON> (ID)", "_name_zh-cn": "<PERSON><PERSON> (ID)", "_name_zh-hk": "<PERSON><PERSON> (ID)", "_group": "11"}, {"key": 101012, "_country": 1323, "_country_iso": "MM", "_email": "<EMAIL>", "_tel": "+95 9 777 111 001", "_url": "https://www.ninjavan.co/en-mm", "_name": "<PERSON><PERSON> (MM)", "_name_zh-cn": "<PERSON><PERSON> (MM)", "_name_zh-hk": "<PERSON><PERSON> (MM)", "_group": "11"}, {"key": 100127, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "", "_url": "https://www.ninjavan.co/en-my", "_name": "<PERSON><PERSON> (MY)", "_name_zh-cn": "<PERSON><PERSON> (MY)", "_name_zh-hk": "<PERSON><PERSON> (MY)", "_group": "11"}, {"key": 100126, "_country": 1607, "_country_iso": "PH", "_email": "<EMAIL>,<EMAIL>,<EMAIL>", "_tel": "", "_url": "https://www.ninjavan.co/en-ph", "_name": "<PERSON><PERSON> (PH)", "_name_zh-cn": "<PERSON><PERSON> (PH)", "_name_zh-hk": "<PERSON><PERSON> (PH)", "_group": "11"}, {"key": 100124, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "https://www.ninjavan.co/en-sg", "_name": "<PERSON><PERSON> (SG)", "_name_zh-cn": "<PERSON><PERSON> (SG)", "_name_zh-hk": "<PERSON><PERSON> (SG)", "_group": "11"}, {"key": 100128, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "", "_url": "https://www.ninjavan.co/th-th", "_name": "<PERSON><PERSON> (TH)", "_name_zh-cn": "<PERSON><PERSON> (TH)", "_name_zh-hk": "<PERSON><PERSON> (TH)", "_group": "11"}, {"key": 100129, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "", "_url": "https://www.ninjavan.co/vi-vn", "_name": "<PERSON><PERSON> (VN)", "_name_zh-cn": "<PERSON><PERSON> (VN)", "_name_zh-hk": "<PERSON><PERSON> (VN)", "_group": "11"}, {"key": 100776, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "https://www.nipponexpress.com/", "_name": "Nippon Express (Global)", "_name_zh-cn": "Nippon Express (Global)", "_name_zh-hk": "Nippon Express (Global)", "_group": "11"}, {"key": 100601, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "+81-3-5801-1111", "_url": "https://www.nittsu.co.jp/", "_name": "Nippon Express (日本通運)", "_name_zh-cn": "日本通运", "_name_zh-hk": "日本通運", "_group": "11"}, {"key": 191145, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "https://www.usniuku.com/", "_name": "NIUKU", "_name_zh-cn": "纽酷国际物流", "_name_zh-hk": "紐酷國際物流", "_group": "12"}, {"key": 100256, "_country": 215, "_country_iso": "BR", "_email": "", "_tel": "1（305）359-7560", "_url": "https://www.nobordist.com/", "_name": "Nobordist", "_name_zh-cn": "Nobordist", "_name_zh-hk": "Nobordist", "_group": "11"}, {"key": 190809, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-************ / +7(499)753-19-97", "_url": "https://www.noelworld.com/", "_name": "<PERSON>", "_name_zh-cn": "诺艾尔", "_name_zh-hk": "諾艾爾", "_group": "12"}, {"key": 101014, "_country": 1109, "_country_iso": "KG", "_email": "<EMAIL>", "_tel": "+996 312 580 001", "_url": "https://nomex.kg/", "_name": "Nomad Express Delivery LLC", "_name_zh-cn": "Nomad Express Delivery LLC", "_name_zh-hk": "Nomad Express Delivery LLC", "_group": "11"}, {"key": 100669, "_country": 104, "_country_iso": "DZ", "_email": "<EMAIL>", "_tel": "+213 0561 62 35 25", "_url": "https://noest-dz.com/", "_name": "Nord Et Ouest Express", "_name_zh-cn": "Nord Et Ouest Express", "_name_zh-hk": "Nord Et Ouest Express", "_group": "11"}, {"key": 100803, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 (0)1753 800 800", "_url": "https://norsk.global/", "_name": "Norsk", "_name_zh-cn": "Norsk", "_name_zh-hk": "Norsk", "_group": "11"}, {"key": 100930, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "+61-1300 722 534", "_url": "https://northline.com.au/", "_name": "Northline", "_name_zh-cn": "Northline", "_name_zh-hk": "Northline", "_group": "11"}, {"key": 14081, "_country": 1408, "_country_iso": "NO", "_email": "", "_tel": "", "_url": "http://www.posten.no/", "_name": "Norway Post", "_name_zh-cn": "Norway Post", "_name_zh-hk": "Norway Post", "_group": ""}, {"key": 100035, "_country": 2102, "_country_iso": "UA", "_email": "<EMAIL>", "_tel": "+38 (098) 450 06 09", "_url": "http://novaposhta.ua/", "_name": "Nova Poshta", "_name_zh-cn": "Nova Poshta", "_name_zh-hk": "Nova Poshta", "_group": "11"}, {"key": 100162, "_country": 2102, "_country_iso": "UA", "_email": "", "_tel": "", "_url": "https://novaposhtaglobal.ua/en/", "_name": "Nova Poshta Global", "_name_zh-cn": "Nova Poshta Global", "_name_zh-hk": "Nova Poshta Global", "_group": "11"}, {"key": 100548, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.nox-nachtexpress.de/", "_name": "nox NachtExpress", "_name_zh-cn": "nox NachtExpress", "_name_zh-hk": "nox NachtExpress", "_group": "11"}, {"key": 190126, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.nuo56.com/", "_name": "NUO", "_name_zh-cn": "NUO", "_name_zh-hk": "NUO", "_group": "10"}, {"key": 190743, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "************", "_url": "http://www.nygyl.com/", "_name": "Nuoyu supply chain", "_name_zh-cn": "诺誉供应链", "_name_zh-hk": "諾譽供應鏈", "_group": "12"}, {"key": 14061, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "http://www.nzpost.co.nz/", "_name": "NZ Post (New Zealand Post)", "_name_zh-cn": "NZ Post (New Zealand Post)", "_name_zh-hk": "NZ Post (New Zealand Post)", "_group": ""}, {"key": 100537, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://www.nztexpress.com/", "_name": "NZT LOGISTICS CORP", "_name_zh-cn": "家家通", "_name_zh-hk": "家家通", "_group": "11"}, {"key": 100831, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-98071-1469", "_url": "https://oakh.com/", "_name": "Oak Harbor Freight Lines", "_name_zh-cn": "Oak Harbor Freight Lines", "_name_zh-hk": "Oak Harbor Freight Lines", "_group": "11"}, {"key": 190508, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.zuexpress.cn/", "_name": "OBC Logistics", "_name_zh-cn": "OBC Logistics", "_name_zh-hk": "OBC Logistics", "_group": "12"}, {"key": 100199, "_country": 112, "_country_iso": "AR", "_email": "", "_tel": "", "_url": "http://www.oca.com.ar/", "_name": "OCA", "_name_zh-cn": "OCA", "_name_zh-hk": "OCA", "_group": "11"}, {"key": 101028, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+58-(*************", "_url": "https://www.ocsworld.com/", "_name": "OCS America", "_name_zh-cn": "OCS America", "_name_zh-hk": "OCS America", "_group": "11"}, {"key": 100286, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "https://www.ocsworld.com/", "_name": "OCS ANA Group", "_name_zh-cn": "OCS ANA Group", "_name_zh-hk": "OCS ANA Group", "_group": "11"}, {"key": 190420, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ocschina.com/", "_name": "OCS Express", "_name_zh-cn": "OCS 国际快递", "_name_zh-hk": "OCS 國際快遞", "_group": "12"}, {"key": 100195, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.ocsworldwide.co.uk/", "_name": "OCS Worldwide", "_name_zh-cn": "OCS Worldwide", "_name_zh-hk": "OCS Worldwide", "_group": "11"}, {"key": 190776, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13922888621", "_url": "http://www.bzy-sz.com/", "_name": "octopus", "_name_zh-cn": "八爪鱼", "_name_zh-hk": "八爪魚", "_group": "12"}, {"key": 100498, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "+52 (55) 6636 5130", "_url": "https://odmexpress.com.mx/", "_name": "ODM Express", "_name_zh-cn": "ODM Express", "_name_zh-hk": "ODM Express", "_group": "11"}, {"key": 190657, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ogicn.com/", "_name": "OGI", "_name_zh-cn": "万国国际", "_name_zh-hk": "萬國國際", "_group": "12"}, {"key": 191188, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18146672536", "_url": "http://www.okkj56.com/", "_name": "OKKJ56", "_name_zh-cn": "欧科跨境", "_name_zh-hk": "歐科跨境", "_group": "12"}, {"key": 100783, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-235-5569", "_url": "https://www.odfl.com/", "_name": "Old Dominion Freight Line", "_name_zh-cn": "Old Dominion Freight Line", "_name_zh-hk": "Old Dominion Freight Line", "_group": "11"}, {"key": 100846, "_country": 1606, "_country_iso": "PE", "_email": "<EMAIL>", "_tel": "(01) 714 0909", "_url": "https://olvacourier.com/", "_name": "OLVA COURIER", "_name_zh-cn": "OLVA COURIER", "_name_zh-hk": "OLVA COURIER", "_group": "11"}, {"key": 15011, "_country": 1501, "_country_iso": "OM", "_email": "", "_tel": "", "_url": "https://omanpost.om/", "_name": "Oman Post", "_name_zh-cn": "Oman Post", "_name_zh-hk": "Oman Post", "_group": ""}, {"key": 190985, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://omgoexpress.cn/", "_name": "OMGO", "_name_zh-cn": "OMGO", "_name_zh-hk": "OMGO", "_group": "12"}, {"key": 100982, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-247-OMNI", "_url": "https://omnilogistics.com/", "_name": "Omni Logistics", "_name_zh-cn": "Omni Logistics", "_name_zh-hk": "Omni Logistics", "_group": "11"}, {"key": 100187, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": " +852 3195-3195 ", "_url": "http://track.omniparcel.com/", "_name": "OmniParcel", "_name_zh-cn": "OmniParcel", "_name_zh-hk": "OmniParcel", "_group": "11"}, {"key": 5041, "_country": 504, "_country_iso": "EE", "_email": "", "_tel": "", "_url": "https://www.omniva.ee/", "_name": "Omniva", "_name_zh-cn": "Omniva", "_name_zh-hk": "Omniva", "_group": ""}, {"key": 100501, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 11-41101515", "_url": "http://ondotcouriers.co.in/", "_name": "Ondot Courier", "_name_zh-cn": "Ondot Courier", "_name_zh-hk": "Ondot Courier", "_group": "11"}, {"key": 100753, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "https://www.one-line.com/", "_name": "ONE (Ocean Network Express)", "_name_zh-cn": "ONE (Ocean Network Express)", "_name_zh-hk": "ONE (Ocean Network Express)", "_group": "11"}, {"key": 190768, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-95040466306", "_url": "http://www.one-express.cn/", "_name": "One Express", "_name_zh-cn": "一速递", "_name_zh-hk": "一速遞", "_group": "12"}, {"key": 191255, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "", "_url": "https://one-minute.cn/", "_name": "One Minute Express ", "_name_zh-cn": "One Minute Express ", "_name_zh-hk": "One Minute Express ", "_group": "12"}, {"key": 100011, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "http://www.oneworldexpress.com/", "_name": "One World", "_name_zh-cn": "One World", "_name_zh-hk": "One World", "_group": "11"}, {"key": 191079, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "http://www.onefly56.com/", "_name": "Onefly Logistics", "_name_zh-cn": "久鼎国际", "_name_zh-hk": "久鼎國際", "_group": "12"}, {"key": 190568, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://cargodiscovery.com/", "_name": "onetouch-tech", "_name_zh-cn": "链仓科技", "_name_zh-hk": "鏈倉科技", "_group": "12"}, {"key": 100566, "_country": 113, "_country_iso": "AM", "_email": "", "_tel": "+374 60 750 350", "_url": "https://onex.am/", "_name": "ONEX", "_name_zh-cn": "ONEX", "_name_zh-hk": "ONEX", "_group": "11"}, {"key": 100934, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "", "_url": "https://one-x.in/", "_name": "ONE-X", "_name_zh-cn": "ONE-X", "_name_zh-hk": "ONE-X", "_group": "11"}, {"key": 191373, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "86-0769-39001290", "_url": "http://www.onixcargo.com/", "_name": "ONIX CARGO", "_name_zh-cn": "安迅国际", "_name_zh-hk": "安迅國際", "_group": "12"}, {"key": 190661, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ontaskd2d.com/", "_name": "ONTASK EXPRESS", "_name_zh-cn": "安时达", "_name_zh-hk": "安時達", "_group": "12"}, {"key": 100694, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "+34 902 20 20 28", "_url": "https://ontime.es/", "_name": "Ontime", "_name_zh-cn": "Ontime", "_name_zh-hk": "Ontime", "_group": "11"}, {"key": 100049, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://www.ontrac.com/", "_name": "OnTrac", "_name_zh-cn": "OnTrac", "_name_zh-hk": "OnTrac", "_group": "11"}, {"key": 190244, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.elitebio.com/", "_name": "OOPSTON", "_name_zh-cn": "OOPSTON", "_name_zh-hk": "OOPSTON", "_group": "10"}, {"key": 100448, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-872-4004", "_url": "http://www.optimacourier.com/", "_name": "Optima", "_name_zh-cn": "Optima", "_name_zh-hk": "Optima", "_group": "11"}, {"key": 97021, "_country": 9702, "_country_iso": "NC", "_email": "", "_tel": "", "_url": "http://www.opt.nc/", "_name": "OPT-NC", "_name_zh-cn": "OPT-NC", "_name_zh-hk": "OPT-NC", "_group": ""}, {"key": 190205, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.orangeconnex.com/", "_name": "Orange Connex", "_name_zh-cn": "橙联", "_name_zh-hk": "橙聯", "_group": "12"}, {"key": 100269, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://orangedsinc.com/", "_name": "OrangeDS", "_name_zh-cn": "OrangeDS", "_name_zh-hk": "OrangeDS", "_group": "11"}, {"key": 100791, "_country": 906, "_country_iso": "IL", "_email": "<EMAIL>", "_tel": "+972 36503300", "_url": "https://www.orian.com/", "_name": "ORIAN", "_name_zh-cn": "ORIAN", "_name_zh-hk": "ORIAN", "_group": "11"}, {"key": 100675, "_country": 1608, "_country_iso": "PL", "_email": "<EMAIL>", "_tel": "+48 22 33 33 999", "_url": "https://www.orlenpaczka.pl/", "_name": "ORLEN Paczka", "_name_zh-cn": "ORLEN Paczka", "_name_zh-hk": "ORLEN Paczka", "_group": "11"}, {"key": 100247, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******.233.999", "_url": "https://www.osmworldwide.com/", "_name": "OSM Worldwide", "_name_zh-cn": "OSM Worldwide", "_name_zh-hk": "OSM Worldwide", "_group": "11"}, {"key": 190343, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ojexpress.cn/", "_name": "OU JIE", "_name_zh-cn": "欧捷", "_name_zh-hk": "歐捷", "_group": "10"}, {"key": 190584, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.oggyl.com/", "_name": "Ouguan supply chain", "_name_zh-cn": "鸥冠供应链", "_name_zh-hk": "鷗冠供應鏈", "_group": "12"}, {"key": 190634, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gdouhua.com/", "_name": "OUHUA", "_name_zh-cn": "欧华", "_name_zh-hk": "歐華", "_group": "12"}, {"key": 190649, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.oulala-express.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 190569, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szouyou.cn/", "_name": "OUYOU INTERNATIONALITY", "_name_zh-cn": "欧邮国际", "_name_zh-hk": "歐郵國際", "_group": "12"}, {"key": 100802, "_country": 319, "_country_iso": "HR", "_email": "<EMAIL>", "_tel": "+385 1 3454 555", "_url": "https://overseas.hr/", "_name": "Overseas Express", "_name_zh-cn": "Overseas Express", "_name_zh-hk": "Overseas Express", "_group": "11"}, {"key": 191232, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.owi56.com/", "_name": "OWI", "_name_zh-cn": "慧一国际", "_name_zh-hk": "慧一國際", "_group": "12"}, {"key": 190320, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://oyx-express.com/", "_name": "OYXGJ", "_name_zh-cn": "欧亚兴", "_name_zh-hk": "歐亞興", "_group": "12"}, {"key": 100054, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://p2pmailing.co.uk/", "_name": "P2P Mailing", "_name_zh-cn": "P2P Mailing", "_name_zh-hk": "P2P Mailing", "_group": "11"}, {"key": 100364, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "", "_url": "https://paack.co/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 190696, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-28686510", "_url": "http://www.etscp.com/", "_name": "PAC", "_name_zh-cn": "PAC", "_name_zh-hk": "PAC", "_group": "12"}, {"key": 100610, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 1300 668 000", "_url": "https://www.packsend.com.au/", "_name": "PACK & SEND", "_name_zh-cn": "PACK & SEND", "_name_zh-hk": "PACK & SEND", "_group": "11"}, {"key": 100132, "_country": 322, "_country_iso": "CZ", "_email": "<EMAIL>", "_tel": "+420 216 216 516", "_url": "https://www.packeta.com/", "_name": "Packeta", "_name_zh-cn": "Packeta", "_name_zh-hk": "Packeta", "_group": "11"}, {"key": 100168, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.packlink.es/", "_name": "Packlink", "_name_zh-cn": "Packlink", "_name_zh-hk": "Packlink", "_group": "11"}, {"key": 100781, "_country": 707, "_country_iso": "GR", "_email": "<EMAIL>", "_tel": "+30 210-2622586", "_url": "https://www.pack-man.gr/", "_name": "Pack-Man", "_name_zh-cn": "Pack-Man", "_name_zh-hk": "Pack-Man", "_group": "11"}, {"key": 100816, "_country": 805, "_country_iso": "HU", "_email": "<EMAIL>", "_tel": "+36 06-1-900-96-69", "_url": "https://www.pactic.com/", "_name": "Pactic", "_name_zh-cn": "Pactic", "_name_zh-hk": "Pactic", "_group": "11"}, {"key": 190653, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.pftlogistics.cn/", "_name": "PaFeiTe", "_name_zh-cn": "帕菲特", "_name_zh-hk": "帕菲特", "_group": "12"}, {"key": 190368, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szpago.com/", "_name": "Pago", "_name_zh-cn": "派格跨境", "_name_zh-hk": "派格跨境", "_group": "12"}, {"key": 190621, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 020 3634 1599", "_url": "http://www.pgl.hk/", "_name": "<PERSON><PERSON>ig<PERSON>", "_name_zh-cn": "派非顾", "_name_zh-hk": "派非顧", "_group": "12"}, {"key": 100961, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "+49 (0)40 853 763 73", "_url": "https://pakajo.world/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 16011, "_country": 1601, "_country_iso": "PK", "_email": "", "_tel": "", "_url": "http://www.pakpost.gov.pk/", "_name": "Pakistan Post", "_name_zh-cn": "Pakistan Post", "_name_zh-hk": "Pakistan Post", "_group": ""}, {"key": 100077, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": "http://www.palexpress.com.hk/", "_name": "PAL Express", "_name_zh-cn": "PAL Express", "_name_zh-hk": "PAL Express", "_group": "11"}, {"key": 16021, "_country": 1602, "_country_iso": "PS", "_email": "", "_tel": "", "_url": "http://www.palpost.ps/", "_name": "Palestine Post", "_name_zh-cn": "Palestine Post", "_name_zh-hk": "Palestine Post", "_group": ""}, {"key": 101010, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-01283 539392", "_url": "https://www.palletforce.com/", "_name": "Palletforce", "_name_zh-cn": "Palletforce", "_name_zh-hk": "Palletforce", "_group": "11"}, {"key": 100504, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "", "_url": "https://palletonline.co.uk/", "_name": "PalletOnline", "_name_zh-cn": "PalletOnline", "_name_zh-hk": "PalletOnline", "_group": "11"}, {"key": 100900, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 (0) 1543 418000", "_url": "https://www.palletways.com/", "_name": "Palletways", "_name_zh-cn": "Palletways", "_name_zh-hk": "Palletways", "_group": "11"}, {"key": 100394, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.pallex.co.uk/", "_name": "Pall-Ex (UK)", "_name_zh-cn": "Pall-Ex (UK)", "_name_zh-hk": "Pall-Ex (UK)", "_group": "11"}, {"key": 191264, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 181 2378 4253", "_url": "https://pandatrolley.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "胖达", "_name_zh-hk": "胖達", "_group": "12"}, {"key": 100743, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.pandionpro.com/", "_name": "Pandion", "_name_zh-cn": "Pandion", "_name_zh-hk": "Pandion", "_group": "11"}, {"key": 100906, "_country": 903, "_country_iso": "ID", "_email": "<EMAIL>", "_tel": "+62-21 3000 9000", "_url": "https://pandulogistics.com/", "_name": "Pandu Logistics", "_name_zh-cn": "Pandu Logistics", "_name_zh-hk": "Pandu Logistics", "_group": "11"}, {"key": 100880, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+44-01604 215 000", "_url": "https://panthergroup.co.uk/", "_name": "Panther Logistics", "_name_zh-cn": "Panther Logistics", "_name_zh-hk": "Panther Logistics", "_group": "11"}, {"key": 100650, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+82-2-3771-2114​", "_url": "http://www.epantos.com/", "_name": "Pantos Logistics (LX PANTOS)", "_name_zh-cn": "Pantos Logistics (LX PANTOS)", "_name_zh-hk": "Pantos Logistics (LX PANTOS)", "_group": "11"}, {"key": 13031, "_country": 1303, "_country_iso": "MG", "_email": "", "_tel": "", "_url": "https://paositramalagasy.mg/", "_name": "Paosit<PERSON>", "_name_zh-cn": "Paosit<PERSON>", "_name_zh-hk": "Paosit<PERSON>", "_group": ""}, {"key": 100910, "_country": 203, "_country_iso": "BD", "_email": "<EMAIL>", "_tel": "+88 09678-300900", "_url": "https://paperfly.com.bd/", "_name": "Paperfly", "_name_zh-cn": "Paperfly", "_name_zh-hk": "Paperfly", "_group": "11"}, {"key": 100147, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "", "_url": "https://www.paquetexpress.com.mx/", "_name": "Paquetexpress", "_name_zh-cn": "Paquetexpress", "_name_zh-hk": "Paquetexpress", "_group": "11"}, {"key": 100981, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+60-3 3362 5666", "_url": "https://parceltopost.com/", "_name": "<PERSON><PERSON><PERSON> to <PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON> to <PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON> to <PERSON>", "_group": "11"}, {"key": 100761, "_country": 704, "_country_iso": "DE", "_email": "<EMAIL>", "_tel": "+49 6404 80292", "_url": "https://parcel.one/", "_name": "PARCEL.ONE", "_name_zh-cn": "PARCEL.ONE", "_name_zh-hk": "PARCEL.ONE", "_group": "11"}, {"key": 100046, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.parcel2go.com/", "_name": "Parcel2GO", "_name_zh-cn": "Parcel2GO", "_name_zh-hk": "Parcel2GO", "_group": "11"}, {"key": 11033, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.parcelforce.com/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_group": ""}, {"key": 100671, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 (0)1159 503 018", "_url": "https://www.parcelhub.co.uk/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190396, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.parceljet.com/", "_name": "Parceljet", "_name_zh-cn": "派速捷", "_name_zh-hk": "派速捷", "_group": "12"}, {"key": 100943, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "", "_url": "https://parcelpoint.com.au/", "_name": "Parcelpoint", "_name_zh-cn": "Parcelpoint", "_name_zh-hk": "Parcelpoint", "_group": "11"}, {"key": 100889, "_country": 1406, "_country_iso": "NZ", "_email": "<EMAIL>", "_tel": "+64-09 280 6213", "_url": "https://www.parcelport.co.nz/", "_name": "Parcelport", "_name_zh-cn": "Parcelport", "_name_zh-hk": "Parcelport", "_group": "11"}, {"key": 100612, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.parcll.com/", "_name": "PARCLL", "_name_zh-cn": "PARCLL", "_name_zh-hk": "PARCLL", "_group": "11"}, {"key": 101015, "_country": 502, "_country_iso": "EG", "_email": "<EMAIL>", "_tel": "+20-1003265919", "_url": "https://pargo.eg/", "_name": "Pargo", "_name_zh-cn": "Pargo", "_name_zh-hk": "Pargo", "_group": "11"}, {"key": 100659, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "https://parxl.com/", "_name": "parxl", "_name_zh-cn": "parxl", "_name_zh-hk": "parxl", "_group": "11"}, {"key": 100387, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "https://www.passtheparcel.co.nz/", "_name": "Pass the Parcel", "_name_zh-cn": "Pass the Parcel", "_name_zh-hk": "Pass the Parcel", "_group": "11"}, {"key": 100767, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://passportshipping.com/", "_name": "Passport", "_name_zh-cn": "Passport", "_name_zh-hk": "Passport", "_group": "11"}, {"key": 100697, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 (079)-2754 3580", "_url": "http://www.pavancourier.com/", "_name": "Pavan Courier Service Pvt. Ltd.", "_name_zh-cn": "Pavan Courier Service Pvt. Ltd.", "_name_zh-hk": "Pavan Courier Service Pvt. Ltd.", "_group": "11"}, {"key": 100591, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://paxel.co/", "_name": "Paxel", "_name_zh-cn": "Paxel", "_name_zh-hk": "Paxel", "_group": "11"}, {"key": 100921, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "+27-************", "_url": "https://www.paxi.co.za/", "_name": "PAXI", "_name_zh-cn": "PAXI", "_name_zh-hk": "PAXI", "_group": "11"}, {"key": 100762, "_country": 1607, "_country_iso": "PH", "_email": " <EMAIL>", "_tel": "+63 *********", "_url": "https://payo.asia/", "_name": "Payo", "_name_zh-cn": "Payo", "_name_zh-hk": "Payo", "_group": "11"}, {"key": 191117, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://logistic.printbelle.com/", "_name": "PB Logistic", "_name_zh-cn": "PB速递", "_name_zh-hk": "PB速遞", "_group": "12"}, {"key": 100577, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "https://pbt.co.nz/", "_name": "PBT Express Freight Network", "_name_zh-cn": "PBT Express Freight Network", "_name_zh-hk": "PBT Express Freight Network", "_group": "11"}, {"key": 100232, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-PCF-6668", "_url": "https://pcfcorp.com/", "_name": "PCF", "_name_zh-cn": "PCF", "_name_zh-hk": "PCF", "_group": "11"}, {"key": 100875, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://corp.pchome.tw/business-units/express/", "_name": "PChome Express", "_name_zh-cn": "网家速配", "_name_zh-hk": "網家速配", "_group": "11"}, {"key": 191226, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.pc-express.cn/", "_name": "PCJD", "_name_zh-cn": "PCJD", "_name_zh-hk": "PCJD", "_group": "12"}, {"key": 100643, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "+62 21 5088 8585", "_url": "https://pcpexpress.com/", "_name": "PCP EXPRESS", "_name_zh-cn": "PCP EXPRESS", "_name_zh-hk": "PCP EXPRESS", "_group": "11"}, {"key": 100931, "_country": 1404, "_country_iso": "NL", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.peddler.com/", "_name": "Peddler", "_name_zh-cn": "Peddler", "_name_zh-hk": "Peddler", "_group": "11"}, {"key": 100459, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+886 02-6618-1817", "_url": "https://www.e-can.com.tw/", "_name": "Pelican (宅配通)", "_name_zh-cn": "宅配通", "_name_zh-hk": "宅配通", "_group": "11"}, {"key": 190991, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>,<EMAIL>", "_tel": "+86-0755-88284699 ", "_url": "https://www.penavicocargo.com/", "_name": "Penavico <PERSON>", "_name_zh-cn": "深圳外代", "_name_zh-hk": "深圳外代", "_group": "12"}, {"key": 100401, "_country": 322, "_country_iso": "CZ", "_email": "", "_tel": "", "_url": "https://www.penguinbox.cz/", "_name": "Penguin", "_name_zh-cn": "Penguin", "_name_zh-hk": "Penguin", "_group": "11"}, {"key": 100901, "_country": 1601, "_country_iso": "PK", "_email": "<EMAIL>", "_tel": "", "_url": "https://pentaexpress.com/", "_name": "Penta Express", "_name_zh-cn": "Penta Express", "_name_zh-hk": "Penta Express", "_group": "11"}, {"key": 190282, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.pfcexpress.com/", "_name": "PFC", "_name_zh-cn": "皇家物流", "_name_zh-hk": "皇家物流", "_group": "10"}, {"key": 100107, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "http://www.pflogistics.com.au/", "_name": "PFLogistics", "_name_zh-cn": "PFLogistics", "_name_zh-hk": "PFLogistics", "_group": "11"}, {"key": 191236, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755 2890 2207", "_url": "https://www.pgs-exp.com/", "_name": "PGSSCM", "_name_zh-cn": "柏域斯浩航", "_name_zh-hk": "柏域斯浩航", "_group": "12"}, {"key": 16071, "_country": 1607, "_country_iso": "PH", "_email": "", "_tel": "", "_url": "https://www.phlpost.gov.ph/", "_name": "Philippine Post", "_name_zh-cn": "Philippine Post", "_name_zh-hk": "Philippine Post", "_group": ""}, {"key": 100379, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "https://www.pickpackpont.hu/", "_name": "<PERSON> <PERSON>", "_name_zh-cn": "<PERSON> <PERSON>", "_name_zh-hk": "<PERSON> <PERSON>", "_group": "11"}, {"key": 100508, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.pickrr.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 190558, "_country": 801, "_country_iso": "HK", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.hkpickup.com/", "_name": "<PERSON><PERSON><PERSON> (HK)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (HK)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (HK)", "_group": "12"}, {"key": 100377, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "", "_url": "https://my.pickupp.io/", "_name": "<PERSON><PERSON><PERSON> (MY)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (MY)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (MY)", "_group": "11"}, {"key": 100378, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "https://sg.pickupp.io/", "_name": "Pickupp (SG)", "_name_zh-cn": "Pickupp (SG)", "_name_zh-hk": "Pickupp (SG)", "_group": "11"}, {"key": 190596, "_country": 2001, "_country_iso": "TW", "_email": "<EMAIL>", "_tel": "+886 (02) 7733-7938 call 8", "_url": "https://tw.pickupp.io/", "_name": "Pickup<PERSON> (TW)", "_name_zh-cn": "Pickup<PERSON> (TW)", "_name_zh-hk": "Pickup<PERSON> (TW)", "_group": "12"}, {"key": 190519, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.pigeon56.com/", "_name": "Pigeon Logisitcs", "_name_zh-cn": "飞鸽物流", "_name_zh-hk": "飛鴿物流", "_group": "12"}, {"key": 100425, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+86 (*************   ", "_url": "https://piggyship.com/", "_name": "PiggyShip", "_name_zh-cn": "小猪快递", "_name_zh-hk": "小豬快遞", "_group": "11"}, {"key": 100679, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.pilotdelivers.com/", "_name": "Pilot Freight Services", "_name_zh-cn": "Pilot Freight Services", "_name_zh-hk": "Pilot Freight Services", "_group": "11"}, {"key": 190601, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.pingda56.com.cn/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "平达国际", "_name_zh-hk": "平達國際", "_group": "12"}, {"key": 190573, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.pkgjwl.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "上海品客", "_name_zh-hk": "上海品客", "_group": "12"}, {"key": 100036, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-256-6444", "_url": "https://www.pitneybowes.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100782, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://pittohio.com/", "_name": "PITT OHIO", "_name_zh-cn": "PITT OHIO", "_name_zh-hk": "PITT OHIO", "_group": "11"}, {"key": 190874, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://pkdexpress.com/", "_name": "PKD express", "_name_zh-cn": "派刻达国际", "_name_zh-hk": "派刻達國際", "_group": "12"}, {"key": 100734, "_country": 1925, "_country_iso": "CH", "_email": "<EMAIL>", "_tel": "+41 (0) 44 / 438 50 40", "_url": "https://planzer-paket.ch/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100536, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "https://plgfutar.hu/", "_name": "PLG Futár", "_name_zh-cn": "PLG Futár", "_name_zh-hk": "PLG Futár", "_group": "11"}, {"key": 100805, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-655-2664", "_url": "https://plycongroup.com/", "_name": "Plycon Transportation Group", "_name_zh-cn": "Plycon Transportation Group", "_name_zh-hk": "Plycon Transportation Group", "_group": "11"}, {"key": 101047, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "", "_url": "https://www.pml-intl.com/", "_name": "PML Intl", "_name_zh-cn": "远达国际", "_name_zh-hk": "遠達國際", "_group": "11"}, {"key": 16041, "_country": 1604, "_country_iso": "PG", "_email": "", "_tel": "", "_url": "http://www.postpng.com.pg/", "_name": "PNG Post", "_name_zh-cn": "PNG Post", "_name_zh-hk": "PNG Post", "_group": ""}, {"key": 100471, "_country": 1608, "_country_iso": "PL", "_email": "", "_tel": "", "_url": "https://www.pocztex.pl/", "_name": "Pocztex", "_name_zh-cn": "Pocztex", "_name_zh-hk": "Pocztex", "_group": "11"}, {"key": 16081, "_country": 1608, "_country_iso": "PL", "_email": "", "_tel": "", "_url": "http://www.poczta-polska.pl/", "_name": "Poland Post", "_name_zh-cn": "Poland Post", "_name_zh-hk": "Poland Post", "_group": ""}, {"key": 100758, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-446-6722", "_url": "https://polexp.com/", "_name": "Polar Express", "_name_zh-cn": "Polar Express", "_name_zh-hk": "Polar Express", "_group": "11"}, {"key": 100037, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "****-800-234-22-40", "_url": "https://www.ponyexpress.ru/", "_name": "Pony Express", "_name_zh-cn": "Pony Express", "_name_zh-hk": "Pony Express", "_group": "11"}, {"key": 9031, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://www.posindonesia.co.id/", "_name": "Pos Indonesia", "_name_zh-cn": "Pos Indonesia", "_name_zh-hk": "Pos Indonesia", "_group": ""}, {"key": 13051, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "", "_url": "http://www.pos.com.my/", "_name": "Pos Malaysia", "_name_zh-cn": "Pos Malaysia", "_name_zh-hk": "Pos Malaysia", "_group": ""}, {"key": 100268, "_country": 1406, "_country_iso": "NZ", "_email": "", "_tel": "", "_url": "https://www.posthaste.co.nz/", "_name": "Post Haste", "_name_zh-cn": "Post Haste", "_name_zh-hk": "Post Haste", "_group": "11"}, {"key": 100706, "_country": 1210, "_country_iso": "LU", "_email": "<EMAIL>", "_tel": "+352 8002 8004", "_url": "https://www.elog-luxembourg.com/", "_name": "Post Luxembourg (LOG)", "_name_zh-cn": "Post Luxembourg (LOG)", "_name_zh-hk": "Post Luxembourg (LOG)", "_group": "11"}, {"key": 11071, "_country": 1107, "_country_iso": "XK", "_email": "", "_tel": "", "_url": "http://postakosoves.com/", "_name": "Posta e Kosovës", "_name_zh-cn": "Posta e Kosovës", "_name_zh-hk": "Posta e Kosovës", "_group": ""}, {"key": 100058, "_country": 1208, "_country_iso": "LT", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.postaplus.com/", "_name": "Posta Plus", "_name_zh-cn": "Posta Plus", "_name_zh-hk": "Posta Plus", "_group": "11"}, {"key": 2122, "_country": 1909, "_country_iso": "RS", "_email": "<EMAIL>", "_tel": "+381-0800-50-50-8", "_url": "https://www.postesrpske.com/", "_name": "Poš<PERSON>", "_name_zh-cn": "Poš<PERSON>", "_name_zh-hk": "Poš<PERSON>", "_group": ""}, {"key": 9071, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "http://www.poste.it/", "_name": "Poste Italiane", "_name_zh-cn": "Poste Italiane", "_name_zh-hk": "Poste Italiane", "_group": ""}, {"key": 6041, "_country": 604, "_country_iso": "FI", "_email": "", "_tel": "", "_url": "http://www.posti.fi/", "_name": "Posti", "_name_zh-cn": "Posti", "_name_zh-hk": "Posti", "_group": ""}, {"key": 100552, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://postmediaparcelservices.com/", "_name": "Postmedia Parcel Services (BNI Parcel Tracking)", "_name_zh-cn": "Postmedia Parcel Services (BNI Parcel Tracking)", "_name_zh-hk": "Postmedia Parcel Services (BNI Parcel Tracking)", "_group": "11"}, {"key": 100644, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "+27 ************", "_url": "https://www.postnet.co.za/", "_name": "PostNet", "_name_zh-cn": "PostNet", "_name_zh-hk": "PostNet", "_group": "11"}, {"key": 14041, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "https://www.postnl.nl/", "_name": "PostNL", "_name_zh-cn": "PostNL", "_name_zh-hk": "PostNL", "_group": ""}, {"key": 14044, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "http://www.postnl.post/", "_name": "PostNL International Mail", "_name_zh-cn": "PostNL International Mail", "_name_zh-hk": "PostNL International Mail", "_group": ""}, {"key": 100574, "_country": 604, "_country_iso": "FI", "_email": "", "_tel": "+358 ************", "_url": "https://www.postnord.fi/", "_name": "PostNord (FI)", "_name_zh-cn": "PostNord (FI)", "_name_zh-hk": "PostNord (FI)", "_group": "11"}, {"key": 100573, "_country": 1408, "_country_iso": "NO", "_email": "", "_tel": "", "_url": "https://www.postnord.no/", "_name": "PostNord (NO)", "_name_zh-cn": "PostNord (NO)", "_name_zh-hk": "PostNord (NO)", "_group": "11"}, {"key": 4011, "_country": 401, "_country_iso": "DK", "_email": "", "_tel": "", "_url": "http://www.pakkeboksen.dk/", "_name": "PostNord Danmark", "_name_zh-cn": "PostNord Danmark", "_name_zh-hk": "PostNord Danmark", "_group": ""}, {"key": 19241, "_country": 1924, "_country_iso": "SE", "_email": "", "_tel": "", "_url": "http://www.postnord.se/", "_name": "PostNord Sweden", "_name_zh-cn": "PostNord Sweden", "_name_zh-hk": "PostNord Sweden", "_group": ""}, {"key": 100543, "_country": 217, "_country_iso": "BG", "_email": "", "_tel": "", "_url": "https://postone.eu/", "_name": "PostOne", "_name_zh-cn": "PostOne", "_name_zh-hk": "PostOne", "_group": "11"}, {"key": 12084, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "http://www.post-plus.net/", "_name": "PostPlus", "_name_zh-cn": "PostPlus", "_name_zh-hk": "PostPlus", "_group": ""}, {"key": 190820, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.airpaipai.com.cn/", "_name": "PP-AIR", "_name_zh-cn": "派派国际", "_name_zh-hk": "派派國際", "_group": "12"}, {"key": 190689, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.pyt56.com/", "_name": "PPEX", "_name_zh-cn": "PPEX", "_name_zh-hk": "PPEX", "_group": "12"}, {"key": 100176, "_country": 322, "_country_iso": "CZ", "_email": "", "_tel": "", "_url": "https://www.ppl.cz/", "_name": "PPL CZ", "_name_zh-cn": "PPL CZ", "_name_zh-hk": "PPL CZ", "_group": "11"}, {"key": 100849, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-**************", "_url": "http://primefits-logistics.pro/", "_name": "Primefits Logistics", "_name_zh-cn": "Primefits Logistics", "_name_zh-hk": "Primefits Logistics", "_group": "11"}, {"key": 100474, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "", "_url": "https://logistics.printway.io/", "_name": "Printway Express", "_name_zh-cn": "Printway Express", "_name_zh-hk": "Printway Express", "_group": "11"}, {"key": 100913, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+1-(888)569-8035", "_url": "https://www.priority1.com/", "_name": "Priority1", "_name_zh-cn": "Priority1", "_name_zh-hk": "Priority1", "_group": "11"}, {"key": 100319, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 (0) ************", "_url": "https://weareprocarrier.com/", "_name": "Pro Carrier", "_name_zh-cn": "Pro Carrier", "_name_zh-hk": "Pro Carrier", "_group": "11"}, {"key": 100980, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-532-6300", "_url": "http://www.promeddelivery.com/", "_name": "Promed Delivery Inc", "_name_zh-cn": "Promed Delivery Inc", "_name_zh-hk": "Promed Delivery Inc", "_group": "11"}, {"key": 190827, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 19886379848", "_url": "http://www.zhongxin518.cn/", "_name": "PstZX", "_name_zh-cn": "PstZX", "_name_zh-hk": "PstZX", "_group": "12"}, {"key": 100549, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "http://parcelgoo.com/", "_name": "PT Maju Bersama Semeru (Parcelgoo)", "_name_zh-cn": "PT Maju Bersama Semeru (Parcelgoo)", "_name_zh-hk": "PT Maju Bersama Semeru (Parcelgoo)", "_group": "11"}, {"key": 100119, "_country": 2011, "_country_iso": "TR", "_email": "<EMAIL>", "_tel": "+90(212) (444)-(0787)", "_url": "https://wp.pts.net/", "_name": "PTS Worldwide Express", "_name_zh-cn": "PTS Worldwide Express", "_name_zh-hk": "PTS Worldwide Express", "_group": "11"}, {"key": 20111, "_country": 2011, "_country_iso": "TR", "_email": "", "_tel": "", "_url": "http://www.ptt.gov.tr/", "_name": "PTT", "_name_zh-cn": "PTT", "_name_zh-hk": "PTT", "_group": ""}, {"key": 100676, "_country": 310, "_country_iso": "CL", "_email": "<EMAIL>", "_tel": "+56 ************", "_url": "https://www.pullmango.cl/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100042, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "**************", "_url": "https://www.purolator.com/", "_name": "Purolator", "_name_zh-cn": "Purolator", "_name_zh-hk": "Purolator", "_group": "11"}, {"key": 101000, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "******-511-4811", "_url": "https://www.purolatorinternational.com/", "_name": "Purolator International", "_name_zh-cn": "Purolator International", "_name_zh-hk": "Purolator International", "_group": "11"}, {"key": 100999, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "**************", "_url": "https://tracking.purolatorshipping.com/", "_name": "Purolator Shipping", "_name_zh-cn": "Purolator Shipping", "_name_zh-hk": "Purolator Shipping", "_group": "11"}, {"key": 190417, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.py-express.net/", "_name": "PY Express", "_name_zh-cn": "派优供应链", "_name_zh-hk": "派優供應鏈", "_group": "12"}, {"key": 191083, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://qc56.cc/", "_name": "qc56.ltd", "_name_zh-cn": "启承国际物流", "_name_zh-hk": "啟承國際物流", "_group": "12"}, {"key": 190878, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.shqyexp.com/", "_name": "QEP", "_name_zh-cn": "强焱国际物流", "_name_zh-hk": "強焱國際物流", "_group": "12"}, {"key": 191183, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-23321641", "_url": "http://www.qhgxgj.com/", "_name": "QHGX", "_name_zh-cn": "QHGX", "_name_zh-hk": "QHGX", "_group": "12"}, {"key": 191097, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.qbgyl-ex.com/", "_name": "<PERSON><PERSON>ppy", "_name_zh-cn": "深圳旗兵", "_name_zh-hk": "深圳旗兵", "_group": "12"}, {"key": 190952, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13824310063 ", "_url": "http://www.szqdgyl.com/ ", "_name": "Qide supply chain", "_name_zh-cn": "旗德供应链", "_name_zh-hk": "旗德供應鏈", "_group": "12"}, {"key": 191204, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 020-36263561", "_url": "https://www.qifly-supplychain.com/", "_name": "QIFLY INT'L", "_name_zh-cn": "旗飞国际", "_name_zh-hk": "旗飛國際", "_group": "12"}, {"key": 191341, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://szqingbei.com/", "_name": "Qingbei Supply ", "_name_zh-cn": "青贝供应链", "_name_zh-hk": "青貝供應鏈", "_group": "12"}, {"key": 190737, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-15817910225", "_url": "http://qz.jiwangyun.com/", "_name": "Qingzhou Supply Chain", "_name_zh-cn": "擎洲供应链", "_name_zh-hk": "擎洲供應鏈", "_group": "12"}, {"key": 190909, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.qlgj168.com/", "_name": "QLGJ", "_name_zh-cn": "QLGJ", "_name_zh-hk": "QLGJ", "_group": "12"}, {"key": 190485, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-21002100", "_url": "https://www.qianliniao.com/", "_name": "QLN", "_name_zh-cn": "千里鸟包裹", "_name_zh-hk": "千裡鳥包裹", "_group": "12"}, {"key": 191152, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.szqlwl.com.cn/", "_name": "QLWL", "_name_zh-cn": "齐力物流", "_name_zh-hk": "齊力物流", "_group": "12"}, {"key": 17011, "_country": 1701, "_country_iso": "QA", "_email": "", "_tel": "", "_url": "https://qatarpost.qa/", "_name": "Q-Post", "_name_zh-cn": "Q-Post", "_name_zh-hk": "Q-Post", "_group": ""}, {"key": 190851, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://kynitha.com/", "_name": "QSY", "_name_zh-cn": "全速易物流", "_name_zh-hk": "全速易物流", "_group": "12"}, {"key": 100695, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": " ****** 524 2101", "_url": "https://qtrack.io/", "_name": "QTrack", "_name_zh-cn": "QTrack", "_name_zh-hk": "QTrack", "_group": "11"}, {"key": 100616, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "+52 (81) 8288-9000", "_url": "https://www.qualitypost.com.mx/", "_name": "Qualitypost", "_name_zh-cn": "Qualitypost", "_name_zh-hk": "Qualitypost", "_group": "11"}, {"key": 191277, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13335791298", "_url": "http://www.zjquanjing.com/", "_name": "QuanJing logistics", "_name_zh-cn": "权璟物流", "_name_zh-hk": "權璟物流", "_group": "12"}, {"key": 100109, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "https://www.quantiumsolutions.com/", "_name": "Quantium Solutions", "_name_zh-cn": "冠庭国际物流", "_name_zh-hk": "冠庭國際物流", "_group": "11"}, {"key": 190889, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.qpe56.com/", "_name": "Quick&Perfct", "_name_zh-cn": "快美物流", "_name_zh-hk": "快美物流", "_group": "12"}, {"key": 190201, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.quickway-sc.com/", "_name": "Quickway", "_name_zh-cn": "Quickway", "_name_zh-hk": "Quickway", "_group": "12"}, {"key": 100801, "_country": 503, "_country_iso": "AE", "_email": "<EMAIL>", "_tel": "+971 04 512 6212", "_url": "https://www.quiqup.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100323, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-79-468-79", "_url": "https://logistics.qwintry.com/", "_name": "Qwintry Logistics", "_name_zh-cn": "Qwintry Logistics", "_name_zh-hk": "Qwintry Logistics", "_group": "11"}, {"key": 100190, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "http://www.qxpress.net/", "_name": "Qxpress", "_name_zh-cn": "Qxpress", "_name_zh-hk": "Qxpress", "_group": "11"}, {"key": 190374, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.qyexp.cn/", "_name": "QYEXP", "_name_zh-cn": "前雨国际", "_name_zh-hk": "前雨國際", "_group": "12"}, {"key": 190383, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.qyunexpress.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "青云物流", "_name_zh-hk": "青雲物流", "_group": "12"}, {"key": 191354, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-17388661318", "_url": "https://www.qyzw56.com/", "_name": "QYZW", "_name_zh-cn": "勤燕泽巍", "_name_zh-hk": "勤燕澤巍", "_group": "12"}, {"key": 100339, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-543-5589", "_url": "https://www.rlcarriers.com/", "_name": "R+L Carriers", "_name_zh-cn": "R+L Carriers", "_name_zh-hk": "R+L Carriers", "_group": "11"}, {"key": 100329, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "+52 5544380136", "_url": "http://rabee.mx/", "_name": "RaBee Express", "_name_zh-cn": "RaBee Express", "_name_zh-hk": "RaBee Express", "_group": "11"}, {"key": 100420, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "https://www.raben-group.com/", "_name": "Raben Group", "_name_zh-cn": "Raben Group", "_name_zh-hk": "Raben Group", "_group": "11"}, {"key": 100692, "_country": 1607, "_country_iso": "PH", "_email": "<EMAIL>", "_tel": "+632 8820 2920 to 25", "_url": "https://www.raf.ph/", "_name": "RAF", "_name_zh-cn": "RAF", "_name_zh-hk": "RAF", "_group": "11"}, {"key": 100259, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "", "_url": "https://www.ram.co.za/", "_name": "RAM", "_name_zh-cn": "RAM", "_name_zh-hk": "RAM", "_group": "11"}, {"key": 101049, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "******-905 9209", "_url": "https://ravenforcecouriers.com/", "_name": "Raven Force Couriers", "_name_zh-cn": "Raven Force Couriers", "_name_zh-hk": "Raven Force Couriers", "_group": "11"}, {"key": 190937, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.ywrb-express.com/ ", "_name": "RBEX", "_name_zh-cn": "荣邦国际", "_name_zh-hk": "榮邦國際", "_group": "12"}, {"key": 190153, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.rct56.com/", "_name": "RCT", "_name_zh-cn": "润成通", "_name_zh-hk": "潤成通", "_group": "12"}, {"key": 100160, "_country": 1306, "_country_iso": "MV", "_email": "", "_tel": "", "_url": "https://www.redbox.mv/", "_name": "Redbox MV", "_name_zh-cn": "Redbox MV", "_name_zh-hk": "Redbox MV", "_group": "11"}, {"key": 190509, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://redchains.cn/", "_name": "RedC", "_name_zh-cn": "RedC", "_name_zh-hk": "RedC", "_group": "12"}, {"key": 100896, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+001-************", "_url": "https://reddawayregional.com/", "_name": "Reddaway", "_name_zh-cn": "Reddaway", "_name_zh-hk": "Reddaway", "_group": "11"}, {"key": 100138, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "", "_url": "https://www.redpack.com.mx/", "_name": "Redpack", "_name_zh-cn": "Redpack", "_name_zh-hk": "Redpack", "_group": "11"}, {"key": 100341, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34 916 20 46 10", "_url": "https://redur.es/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100461, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.relaiscolis.com/", "_name": "RelaisColis", "_name_zh-cn": "RelaisColis", "_name_zh-hk": "RelaisColis", "_group": "11"}, {"key": 100326, "_country": 905, "_country_iso": "IE", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.relex.ie/", "_name": "Relex", "_name_zh-cn": "Relex", "_name_zh-hk": "Relex", "_group": "11"}, {"key": 100587, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "+62 21 56958333", "_url": "https://www.rex.co.id/", "_name": "REX Kiriman Express", "_name_zh-cn": "REX Kiriman Express", "_name_zh-hk": "REX Kiriman Express", "_group": "11"}, {"key": 190483, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.runforint.com/", "_name": "RFL", "_name_zh-cn": "润峯物流", "_name_zh-hk": "潤峯物流", "_group": "12"}, {"key": 100850, "_country": 907, "_country_iso": "IT", "_email": "<EMAIL>", "_tel": "+39 0422 416120", "_url": "https://www.rhenus.group/", "_name": "Rhenus Logistics (IT)", "_name_zh-cn": "Rhenus Logistics (IT)", "_name_zh-hk": "Rhenus Logistics (IT)", "_group": "11"}, {"key": 190385, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.rheymah.net/", "_name": "RHM", "_name_zh-cn": "RHM", "_name_zh-hk": "RHM", "_group": "12"}, {"key": 100151, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "", "_url": "http://www.rincos.co.kr/", "_name": "RINCOS", "_name_zh-cn": "RINCOS", "_name_zh-hk": "RINCOS", "_group": "11"}, {"key": 100665, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-7428584513", "_url": "https://www.rivigo.com/", "_name": "Rivigo", "_name_zh-cn": "Rivigo", "_name_zh-hk": "Rivigo", "_group": "11"}, {"key": 100500, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://www.rivolution.com/", "_name": "Rivo", "_name_zh-cn": "Rivo", "_name_zh-hk": "Rivo", "_group": "11"}, {"key": 100719, "_country": 1913, "_country_iso": "SG", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.roadbull.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100253, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://freight.rrts.com/", "_name": "Roadrunner <PERSON><PERSON><PERSON>", "_name_zh-cn": "Roadrunner <PERSON><PERSON><PERSON>", "_name_zh-hk": "Roadrunner <PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 191024, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.huojian56.com/", "_name": "Rocket Logistics", "_name_zh-cn": "火箭物流", "_name_zh-hk": "火箭物流", "_group": "12"}, {"key": 18021, "_country": 1802, "_country_iso": "RO", "_email": "", "_tel": "", "_url": "http://www.posta-romana.ro/", "_name": "Romania Post", "_name_zh-cn": "Romania Post", "_name_zh-hk": "Romania Post", "_group": ""}, {"key": 100879, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-01604 801777", "_url": "https://route1fulfilment.co.uk/", "_name": "Route 1 Fulfilment", "_name_zh-cn": "Route 1 Fulfilment", "_name_zh-hk": "Route 1 Fulfilment", "_group": "11"}, {"key": 100799, "_country": 1323, "_country_iso": "MM", "_email": "<EMAIL>", "_tel": "+95 09779888688", "_url": "https://www.royalx.net/", "_name": "Royal Express", "_name_zh-cn": "Royal Express", "_name_zh-hk": "Royal Express", "_group": "11"}, {"key": 11031, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.royalmail.com/", "_name": "Royal Mail", "_name_zh-cn": "Royal Mail", "_name_zh-hk": "Royal Mail", "_group": ""}, {"key": 100033, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+44 22 122 12 19", "_url": "https://royalshipments.com/", "_name": "Royal Shipments", "_name_zh-cn": "Royal Shipments", "_name_zh-hk": "Royal Shipments", "_group": "11"}, {"key": 100948, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.royaleinternational.com/", "_name": "Royale International", "_name_zh-cn": "Royale International", "_name_zh-hk": "Royale International", "_group": "11"}, {"key": 100476, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+852 2620 0289", "_url": "https://www.rpxonline.com/", "_name": "RPX Online", "_name_zh-cn": "RPX Online", "_name_zh-hk": "RPX Online", "_group": "11"}, {"key": 191297, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-29728601", "_url": "http://www.rqlis.com/", "_name": "RQ Logistics", "_name_zh-cn": "瑞秋国际物流", "_name_zh-hk": "瑞秋國際物流", "_group": "12"}, {"key": 190429, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.rrswl.com/", "_name": "RRS Logistics", "_name_zh-cn": "日日顺物流", "_name_zh-hk": "日日順物流", "_group": "12"}, {"key": 100413, "_country": 308, "_country_iso": "CF", "_email": "<EMAIL>", "_tel": "+27 11 552 1000", "_url": "https://www.rtt.co.za/", "_name": "RTT", "_name_zh-cn": "RTT", "_name_zh-hk": "RTT", "_group": "11"}, {"key": 190339, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ruecom.cn/", "_name": "RUE", "_name_zh-cn": "如易", "_name_zh-hk": "如易", "_group": "10"}, {"key": 190375, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.runbail.com/", "_name": "Runbai", "_name_zh-cn": "润百", "_name_zh-hk": "潤百", "_group": "10"}, {"key": 100285, "_country": 906, "_country_iso": "IL", "_email": "<EMAIL>", "_tel": "", "_url": "http://runnerexpress.co.il/", "_name": "Runner Express", "_name_zh-cn": "Runner Express", "_name_zh-hk": "Runner Express", "_group": "11"}, {"key": 18031, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://pochta.ru/", "_name": "Russian Post", "_name_zh-cn": "Russian Post", "_name_zh-hk": "Russian Post", "_group": ""}, {"key": 18041, "_country": 1804, "_country_iso": "RW", "_email": "", "_tel": "", "_url": "http://www.i-posita.rw/", "_name": "Rwanda Post", "_name_zh-cn": "Rwanda Post", "_name_zh-hk": "Rwanda Post", "_group": ""}, {"key": 100784, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://rxo.com/", "_name": "RXO", "_name_zh-cn": "RXO", "_name_zh-hk": "RXO", "_group": "11"}, {"key": 100522, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.ryder.com/", "_name": "<PERSON>", "_name_zh-cn": "<PERSON>", "_name_zh-hk": "<PERSON>", "_group": "11"}, {"key": 191151, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://ru-express.com/", "_name": "RZN", "_name_zh-cn": "孺子牛供应链", "_name_zh-hk": "孺子牛供應鏈", "_group": "12"}, {"key": 100165, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "", "_url": "https://www.saee.sa/en/", "_name": "SAEE", "_name_zh-cn": "SAEE", "_name_zh-hk": "SAEE", "_group": "11"}, {"key": 100859, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+966 920002227", "_url": "https://www.safe-arrival.com/", "_name": "Safe Arrival", "_name_zh-cn": "Safe Arrival", "_name_zh-hk": "Safe Arrival", "_group": "11"}, {"key": 100463, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.saferbo.com/", "_name": "Saferbo", "_name_zh-cn": "Saferbo", "_name_zh-hk": "Saferbo", "_group": "11"}, {"key": 100683, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 9311-113-113     ", "_url": "http://www.safexpress.com/", "_name": "Safexpress", "_name_zh-cn": "Safexpress", "_name_zh-hk": "Safexpress", "_group": "11"}, {"key": 190395, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.safly.net/", "_name": "SaFly", "_name_zh-cn": "吉飞国际物流", "_name_zh-hk": "吉飛國際物流", "_group": "10"}, {"key": 100040, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "http://www.sagawa-exp.co.jp/", "_name": "<PERSON><PERSON> (佐川急便)", "_name_zh-cn": "<PERSON><PERSON> (佐川急便)", "_name_zh-hk": "<PERSON><PERSON> (佐川急便)", "_group": "11"}, {"key": 100935, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 044-28170095", "_url": "https://www.saharaexpress.com/", "_name": "Sahara Express", "_name_zh-cn": "Sahara Express", "_name_zh-hk": "Sahara Express", "_group": "11"}, {"key": 100242, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+1 （800） 765-7242", "_url": "https://www.saia.com/", "_name": "SAIA", "_name_zh-cn": "SAIA", "_name_zh-hk": "SAIA", "_group": "11"}, {"key": 191333, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.saichengexpress.com/", "_name": "SAICHENG LOGISTICS", "_name_zh-cn": "赛诚物流", "_name_zh-hk": "賽誠物流", "_group": "12"}, {"key": 100051, "_country": 907, "_country_iso": "IT", "_email": "<EMAIL>", "_tel": "+39 800 933 311", "_url": "http://www.sailpost.it/", "_name": "Sailpost", "_name_zh-cn": "Sailpost", "_name_zh-hk": "Sailpost", "_group": "11"}, {"key": 12091, "_country": 1209, "_country_iso": "LC", "_email": "", "_tel": "", "_url": "http://www.stluciapostal.com/", "_name": "Saint Lucia Post", "_name_zh-cn": "Saint Lucia Post", "_name_zh-hk": "Saint Lucia Post", "_group": ""}, {"key": 190702, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-15151824176", "_url": "http://www.syascm.com/", "_name": "Saiyascm", "_name_zh-cn": "塞亚国际", "_name_zh-hk": "塞亞國際", "_group": "12"}, {"key": 100744, "_country": 217, "_country_iso": "BG", "_email": "", "_tel": "", "_url": "https://sameday.bg/", "_name": "Sameday (BG)", "_name_zh-cn": "Sameday (BG)", "_name_zh-hk": "Sameday (BG)", "_group": "11"}, {"key": 100416, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "https://sameday.hu/", "_name": "Sameday (HU)", "_name_zh-cn": "Sameday (HU)", "_name_zh-hk": "Sameday (HU)", "_group": "11"}, {"key": 100320, "_country": 1802, "_country_iso": "RO", "_email": "<EMAIL>", "_tel": "+021 – 637.06.60", "_url": "https://sameday.ro/", "_name": "Sameday (RO)", "_name_zh-cn": "Sameday (RO)", "_name_zh-hk": "Sameday (RO)", "_group": "11"}, {"key": 19281, "_country": 1928, "_country_iso": "WS", "_email": "", "_tel": "", "_url": "https://www.samoapost.ws/", "_name": "Samoa Post", "_name_zh-cn": "Samoa Post", "_name_zh-hk": "Samoa Post", "_group": ""}, {"key": 19051, "_country": 1905, "_country_iso": "SM", "_email": "", "_tel": "", "_url": "http://www.poste.sm/", "_name": "San Marino Post", "_name_zh-cn": "San Marino Post", "_name_zh-hk": "San Marino Post", "_group": ""}, {"key": 190482, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.sanaship.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "10"}, {"key": 191335, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 -18268221130", "_url": "http://sandiy.top/", "_name": "sandiy", "_name_zh-cn": "三迪物流", "_name_zh-hk": "三迪物流", "_group": "12"}, {"key": 190819, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://sangzhou.net/", "_name": "Sangzhou", "_name_zh-cn": "上海桑州", "_name_zh-hk": "上海桑州", "_group": "12"}, {"key": 100344, "_country": 903, "_country_iso": "ID", "_email": "<EMAIL>", "_tel": "+021 2280 6611 / 2280 6612", "_url": "https://www.sap-express.id/", "_name": "SAP EXPRESS", "_name_zh-cn": "SAP EXPRESS", "_name_zh-hk": "SAP EXPRESS", "_group": "11"}, {"key": 190499, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sapphirebox56.com", "_name": "Sapphire Box", "_name_zh-cn": "Sapphire Box", "_name_zh-hk": "Sapphire Box", "_group": "12"}, {"key": 19071, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "", "_url": "https://splonline.com.sa/ar/", "_name": "Saudi Post", "_name_zh-cn": "Saudi Post", "_name_zh-hk": "Saudi Post", "_group": ""}, {"key": 100877, "_country": 1606, "_country_iso": "PE", "_email": "<EMAIL>", "_tel": "+51-947482906", "_url": "https://www.savarexpress.com.pe/", "_name": "Savar Express", "_name_zh-cn": "Savar Express", "_name_zh-hk": "Savar Express", "_group": "11"}, {"key": 100672, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+ 44 74 18 39 72 77", "_url": "https://saveway-logistics.com/", "_name": "Saveway Logistics", "_name_zh-cn": "Saveway Logistics", "_name_zh-hk": "Saveway Logistics", "_group": "11"}, {"key": 190326, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sbd-scm.com/", "_name": "SBD", "_name_zh-cn": "SBD", "_name_zh-hk": "SBD", "_group": "12"}, {"key": 190987, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://sc-express.cn/", "_name": "SC", "_name_zh-cn": "顺仓科技", "_name_zh-hk": "順倉科技", "_group": "12"}, {"key": 190992, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.fbatoll.com/", "_name": "SC Logistics", "_name_zh-cn": "SC Logistics", "_name_zh-hk": "SC Logistics", "_group": "12"}, {"key": 100639, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 2 9647 0042 ", "_url": "https://scaexpress.com.au/", "_name": "SCA Express", "_name_zh-cn": "SCA Express", "_name_zh-hk": "SCA Express", "_group": "11"}, {"key": 190880, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 18998381605 ", "_url": "https://www.scaleorder.com/", "_name": "ScaleOrder", "_name_zh-cn": "北斗互联", "_name_zh-hk": "北斗互聯", "_group": "12"}, {"key": 100740, "_country": 401, "_country_iso": "DK", "_email": "<EMAIL>", "_tel": "+45 32 48 00 00", "_url": "https://www.scangl.com/", "_name": "Scan Global Logistics", "_name_zh-cn": "Scan Global Logistics", "_name_zh-hk": "Scan Global Logistics", "_group": "11"}, {"key": 190344, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.scgj56.net/", "_name": "SCGJ", "_name_zh-cn": "拾诚国际", "_name_zh-hk": "拾誠國際", "_group": "12"}, {"key": 100112, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "", "_url": "https://www.scmpaqueteria.mx/", "_name": "SCM", "_name_zh-cn": "SCM", "_name_zh-hk": "SCM", "_group": "11"}, {"key": 100830, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "+81-050-5434-9991", "_url": "http://www.scorejp.com/", "_name": "SCORE JAPAN", "_name_zh-cn": "流通王", "_name_zh-hk": "流通王", "_group": "11"}, {"key": 100019, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "800.33.66.22*", "_url": "http://www.sda.it/", "_name": "SDA", "_name_zh-cn": "SDA", "_name_zh-hk": "SDA", "_group": "11"}, {"key": 190744, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.sdh-scm.com/", "_name": "SDH", "_name_zh-cn": "闪电猴", "_name_zh-hk": "閃電猴", "_group": "12"}, {"key": 190280, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sdk-express.cn/", "_name": "SDK", "_name_zh-cn": "斯德克", "_name_zh-hk": "斯德克", "_group": "12"}, {"key": 100560, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "", "_url": "https://www.searates.com/", "_name": "SeaRates", "_name_zh-cn": "SeaRates", "_name_zh-hk": "SeaRates", "_group": "11"}, {"key": 190720, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13026634913", "_url": "http://www.hbtgj56.com/", "_name": "Seashells tong", "_name_zh-cn": "海贝通", "_name_zh-hk": "海貝通", "_group": "12"}, {"key": 191164, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18957999160", "_url": "http://www.seasylogistics.com/", "_name": "SEASY LOGISTICS", "_name_zh-cn": "顺易物流", "_name_zh-hk": "順易物流", "_group": "12"}, {"key": 191048, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.sedumhk.com/", "_name": "SEDUM", "_name_zh-cn": "景天", "_name_zh-hk": "景天", "_group": "12"}, {"key": 100622, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "+52 (55) 9135 0245", "_url": "https://segmail.co/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100171, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "http://www.seino.co.jp/en/", "_name": "<PERSON><PERSON> (西濃運輸)", "_name_zh-cn": "<PERSON><PERSON> (西濃運輸)", "_name_zh-hk": "<PERSON><PERSON> (西濃運輸)", "_group": "11"}, {"key": 101060, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "https://www.seino.co.jp/ssx/", "_name": "Seino Super Express", "_name_zh-cn": "Seino Super Express", "_name_zh-hk": "Seino Super Express", "_group": "11"}, {"key": 100332, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.sekologistics.com", "_name": "SEKO Logistics", "_name_zh-cn": "SEKO Logistics", "_name_zh-hk": "SEKO Logistics", "_group": "11"}, {"key": 100541, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "", "_url": "http://www.sendex.mx/", "_name": "Sendex", "_name_zh-cn": "Sendex", "_name_zh-hk": "Sendex", "_group": "11"}, {"key": 100248, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.sending.es/", "_name": "Sending", "_name_zh-cn": "Sending", "_name_zh-hk": "Sending", "_group": "11"}, {"key": 100258, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "https://www.sendit.asia/", "_name": "SendIt", "_name_zh-cn": "SendIt", "_name_zh-hk": "SendIt", "_group": "11"}, {"key": 100123, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.sendle.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 190915, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 19957995389", "_url": "http://www.zjsf56.com/", "_name": "<PERSON><PERSON><PERSON> ", "_name_zh-cn": "森梵跨境", "_name_zh-hk": "森梵跨境", "_group": "12"}, {"key": 190087, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sjtgyl.com/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 101031, "_country": 903, "_country_iso": "ID", "_email": "<EMAIL>", "_tel": "+62-021-6303456", "_url": "https://sentralcargo.co.id/", "_name": "Sentral Cargo", "_name_zh-cn": "Sentral Cargo", "_name_zh-hk": "Sentral Cargo", "_group": "11"}, {"key": 191237, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 19924552247", "_url": "https://www.senyugg.cn/", "_name": "SenYug<PERSON><PERSON>", "_name_zh-cn": "森屿国际", "_name_zh-hk": "森嶼國際", "_group": "12"}, {"key": 19091, "_country": 1909, "_country_iso": "RS", "_email": "", "_tel": "", "_url": "http://www.posta.rs/", "_name": "Serbia Post", "_name_zh-cn": "Serbia Post", "_name_zh-hk": "Serbia Post", "_group": ""}, {"key": 16061, "_country": 1606, "_country_iso": "PE", "_email": "", "_tel": "(+598) 2916 0200", "_url": "https://www.serpost.com.pe/Cliente/Home/Paginacomercial", "_name": "Serpost", "_name_zh-cn": "Serpost", "_name_zh-hk": "Serpost", "_group": ""}, {"key": 100938, "_country": 2011, "_country_iso": "TR", "_email": "<EMAIL>", "_tel": "+90 212 603 33 32", "_url": "https://servex.com.tr/", "_name": "Servex", "_name_zh-cn": "Servex", "_name_zh-hk": "Servex", "_group": "11"}, {"key": 100495, "_country": 501, "_country_iso": "EC", "_email": "", "_tel": "+593 (601) 7700 200", "_url": "https://www.servientrega.com/", "_name": "servientrega", "_name_zh-cn": "servientrega", "_name_zh-hk": "servientrega", "_group": "11"}, {"key": 100946, "_country": 501, "_country_iso": "EC", "_email": "<EMAIL>", "_tel": "+593 3732000", "_url": "https://www.servientrega.com.ec/", "_name": "Servientrega Ecuador", "_name_zh-cn": "Servientrega Ecuador", "_name_zh-hk": "Servientrega Ecuador", "_group": "11"}, {"key": 100438, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "+34 902101010", "_url": "https://www.seur.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 19111, "_country": 1911, "_country_iso": "SC", "_email": "", "_tel": "", "_url": "http://www.seychelles-post.com/", "_name": "Seychelles Post", "_name_zh-cn": "Seychelles Post", "_name_zh-hk": "Seychelles Post", "_group": ""}, {"key": 100012, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 95338", "_url": "https://intl.sf-express.com/", "_name": "SF Express", "_name_zh-cn": "顺丰速运", "_name_zh-hk": "順豐速運", "_group": "11"}, {"key": 190766, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 95338", "_url": "https://www.sf-express.com/", "_name": "SF Express(CN)", "_name_zh-cn": "顺丰速运(CN)", "_name_zh-hk": "順豐速運(CN)", "_group": "12"}, {"key": 190113, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sfcservice.com/", "_name": "SFC", "_name_zh-cn": "三态速递", "_name_zh-hk": "三態速遞", "_group": "12"}, {"key": 190826, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sfydexpress.com/", "_name": "SFYD Express", "_name_zh-cn": "云速递", "_name_zh-hk": "雲速遞", "_group": "12"}, {"key": 100923, "_country": 2204, "_country_iso": "VN", "_email": "<EMAIL>", "_tel": "+84-1900633027", "_url": "https://sglink.vn/", "_name": "SG Link", "_name_zh-cn": "SG Link", "_name_zh-hk": "SG Link", "_group": "11"}, {"key": 190346, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.starglobal.com.cn/", "_name": "SGF", "_name_zh-cn": "SGF", "_name_zh-hk": "SGF", "_group": "12"}, {"key": 100041, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "http://www.sgh-globalj.com/", "_name": "SGHグローバル (Sagawa Global)", "_name_zh-cn": "SGHグローバル (Sagawa Global)", "_name_zh-hk": "SGHグローバル (Sagawa Global)", "_group": "11"}, {"key": 191014, "_country": 1002, "_country_iso": "JP", "_email": "<EMAIL>", "_tel": "+86 0081-80-8353-5498 ", "_url": "http://www.sgxpress.com/", "_name": "SGXpress", "_name_zh-cn": "SGXpress（国際宅配）", "_name_zh-hk": "SGXpress（国際宅配）", "_group": "12"}, {"key": 100102, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "https://shadowfax.in/", "_name": "Shadowfax", "_name_zh-cn": "Shadowfax", "_name_zh-hk": "Shadowfax", "_group": "11"}, {"key": 190098, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.shangfang56.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "尚方国际", "_name_zh-hk": "尚方國際", "_group": "12"}, {"key": 190762, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-28012173", "_url": "http://www.sqgjexp.com/", "_name": "Shangqi International Logistics", "_name_zh-cn": "上启国际物流", "_name_zh-hk": "上啟國際物流", "_group": "12"}, {"key": 100547, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://shapeskylogistics.com", "_name": "Shape Sky Logistics", "_name_zh-cn": "Shape Sky Logistics", "_name_zh-hk": "Shape Sky Logistics", "_group": "11"}, {"key": 190877, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.shdingju.com/", "_name": "SHDJ", "_name_zh-cn": "SHDJ", "_name_zh-hk": "SHDJ", "_group": "12"}, {"key": 190407, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.showl.com/", "_name": "SHE", "_name_zh-cn": "森鸿国际", "_name_zh-hk": "森鴻國際", "_group": "12"}, {"key": 190822, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-************", "_url": "http://www.sl56.com/", "_name": "SHENGLAN", "_name_zh-cn": "SHENGLAN", "_name_zh-hk": "SHENGLAN", "_group": "12"}, {"key": 190942, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86  0755-28575592 ", "_url": "https://www.flgjex.com/", "_name": "<PERSON><PERSON> ", "_name_zh-cn": "深圳方联", "_name_zh-hk": "深圳方聯", "_group": "12"}, {"key": 190652, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.sbdkuajing.com/", "_name": "SHIBIDA", "_name_zh-cn": "时必达", "_name_zh-hk": "時必達", "_group": "12"}, {"key": 190694, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18620121009", "_url": "http://www.gzsfgj.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "时丰国际", "_name_zh-hk": "時豐國際", "_group": "12"}, {"key": 191003, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.kydoscross.com/", "_name": "SHINE-V", "_name_zh-cn": "煦阳", "_name_zh-hk": "煦陽", "_group": "12"}, {"key": 100615, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "+965 2498 1941", "_url": "https://shipa.com/", "_name": "SHIPA", "_name_zh-cn": "SHIPA", "_name_zh-hk": "SHIPA", "_group": "11"}, {"key": 100829, "_country": 502, "_country_iso": "EG", "_email": "<EMAIL>", "_tel": "", "_url": "https://shipblu.com/", "_name": "ShipBlu", "_name_zh-cn": "ShipBlu", "_name_zh-hk": "ShipBlu", "_group": "11"}, {"key": 100318, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "****** 382 1741", "_url": "https://www.shipglobal.us/", "_name": "ShipGlobal US", "_name_zh-cn": "ShipGlobal US", "_name_zh-hk": "ShipGlobal US", "_group": "11"}, {"key": 100352, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 1300 467 447", "_url": "https://www.shippit.com/", "_name": "Shippit", "_name_zh-cn": "Shippit", "_name_zh-hk": "Shippit", "_group": "11"}, {"key": 100662, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.shiprocket.in/", "_name": "Shiprocket", "_name_zh-cn": "Shiprocket", "_name_zh-hk": "Shiprocket", "_group": "11"}, {"key": 100677, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://shipshopus.com/", "_name": "ShipShopUS", "_name_zh-cn": "ShipShopUS", "_name_zh-hk": "ShipShopUS", "_group": "11"}, {"key": 100763, "_country": 1105, "_country_iso": "KR", "_email": "<EMAIL>", "_tel": "+82  010-2304-1331 ", "_url": "http://www.shipter.kr/", "_name": "SHIPTER", "_name_zh-cn": "SHIPTER", "_name_zh-hk": "SHIPTER", "_group": "11"}, {"key": 100367, "_country": 1803, "_country_iso": "RU", "_email": "<EMAIL>", "_tel": "**** 800 100-72-69", "_url": "https://shiptor.ru/", "_name": "Shiptor", "_name_zh-cn": "Shiptor", "_name_zh-hk": "Shiptor", "_group": "11"}, {"key": 100751, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.shipx.com/", "_name": "ShipX", "_name_zh-cn": "ShipX", "_name_zh-hk": "ShipX", "_group": "11"}, {"key": 190384, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.juxiex.com/", "_name": "SHJX", "_name_zh-cn": "上海驹隙", "_name_zh-hk": "上海駒隙", "_group": "12"}, {"key": 190948, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL> ", "_tel": "+86 0755-23143646", "_url": "http://www.sh-logtec.com/", "_name": "SH-LOGTEC", "_name_zh-cn": "SH-LOGTEC", "_name_zh-hk": "SH-LOGTEC", "_group": "12"}, {"key": 100409, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://spx.co.id/", "_name": "<PERSON>ee Xpress (ID)", "_name_zh-cn": "<PERSON>ee Xpress (ID)", "_name_zh-hk": "<PERSON>ee Xpress (ID)", "_group": "11"}, {"key": 100408, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "", "_url": "https://shopeexpress.com.my/", "_name": "Shopee Xpress (MY)", "_name_zh-cn": "Shopee Xpress (MY)", "_name_zh-hk": "Shopee Xpress (MY)", "_group": "11"}, {"key": 100595, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "https://spx.sg/", "_name": "Shopee Xpress (SG)", "_name_zh-cn": "Shopee Xpress (SG)", "_name_zh-hk": "Shopee Xpress (SG)", "_group": "11"}, {"key": 100410, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "", "_url": "https://spx.co.th/", "_name": "Shopee Xpress (TH)", "_name_zh-cn": "Shopee Xpress (TH)", "_name_zh-hk": "Shopee Xpress (TH)", "_group": "11"}, {"key": 100863, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "+886-(02) 6636-6559", "_url": "https://spx.tw/", "_name": "Shopee Xpress (TW)", "_name_zh-cn": "蝦皮店到店", "_name_zh-hk": "蝦皮店到店", "_group": "11"}, {"key": 100519, "_country": 1607, "_country_iso": "PH", "_email": "", "_tel": "", "_url": "https://spx.ph/", "_name": "ShopeeExpress(PH)", "_name_zh-cn": "ShopeeExpress(PH)", "_name_zh-hk": "ShopeeExpress(PH)", "_group": "11"}, {"key": 100538, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "", "_url": "https://spx.vn/", "_name": "ShopeeExpress(VN)", "_name_zh-cn": "ShopeeExpress(VN)", "_name_zh-hk": "ShopeeExpress(VN)", "_group": "11"}, {"key": 190472, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.myoneship.cn/", "_name": "Shopline Logistics", "_name_zh-cn": "商链物流", "_name_zh-hk": "商鏈物流", "_group": "12"}, {"key": 100696, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 \t(0261)2410414", "_url": "https://shreemahabaliexpress.com/", "_name": "Shree Mahalabali Express ", "_name_zh-cn": "Shree Mahalabali Express ", "_name_zh-hk": "Shree Mahalabali Express ", "_group": "11"}, {"key": 100603, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91-9227880033", "_url": "http://shreemahavircourier.com/", "_name": "Shree Mahavir Express Services", "_name_zh-cn": "Shree Mahavir Express Services", "_name_zh-hk": "Shree Mahavir Express Services", "_group": "11"}, {"key": 100357, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 9712 666 666", "_url": "https://www.shreemaruticourier.com/", "_name": "<PERSON><PERSON><PERSON> Maruti Courier", "_name_zh-cn": "<PERSON><PERSON><PERSON> Maruti Courier", "_name_zh-hk": "<PERSON><PERSON><PERSON> Maruti Courier", "_group": "11"}, {"key": 100620, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 1800 233 66666", "_url": "http://www.shreenandancourier.com/", "_name": "S<PERSON>ee Nandan Courier Limited", "_name_zh-cn": "S<PERSON>ee Nandan Courier Limited", "_name_zh-hk": "S<PERSON>ee Nandan Courier Limited", "_group": "11"}, {"key": 100347, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "http://www.shreetirupaticourier.net/", "_name": "SHREE TIRUPATI COURIER", "_name_zh-cn": "SHREE TIRUPATI COURIER", "_name_zh-hk": "SHREE TIRUPATI COURIER", "_group": "11"}, {"key": 100530, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "http://www.shreeanjanicourier.com/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190327, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.shthn.com/", "_name": "SHT", "_name_zh-cn": "SHT", "_name_zh-hk": "SHT", "_group": "10"}, {"key": 190785, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.sht-log.com/", "_name": "SHT", "_name_zh-cn": "SHT", "_name_zh-hk": "SHT", "_group": "12"}, {"key": 191273, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://comeorders.com/", "_name": "SHtrack", "_name_zh-cn": "素何物流", "_name_zh-hk": "素何物流", "_group": "12"}, {"key": 190276, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.shunbang.vip/", "_name": "ShunBang", "_name_zh-cn": "顺邦科技物流", "_name_zh-hk": "順邦科技物流", "_group": "12"}, {"key": 191347, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-021-67730030 ", "_url": "http://www.shuojiavip.com/", "_name": "SHUOJIA", "_name_zh-cn": "硕嘉国际物流", "_name_zh-hk": "碩嘉國際物流", "_group": "12"}, {"key": 191187, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.shyexpress.com/", "_name": "SHY", "_name_zh-cn": "速航易", "_name_zh-hk": "速航易", "_group": "12"}, {"key": 100848, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-8045685937", "_url": "https://www.shypmax.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100590, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "+82 021-5020-0050", "_url": "https://www.sicepat.com/", "_name": "SiCepat", "_name_zh-cn": "SiCepat", "_name_zh-hk": "SiCepat", "_group": "11"}, {"key": 190714, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-15055727795", "_url": "http://crystalline.vip/", "_name": "SIHAI INC", "_name_zh-cn": "克里斯兰", "_name_zh-hk": "克里斯蘭", "_group": "12"}, {"key": 191350, "_country": 301, "_country_iso": "CN", "_email": "si<PERSON><PERSON><PERSON><PERSON>@ztn.com", "_tel": "", "_url": "http://www.sihiexpress.com/", "_name": "SIHI", "_name_zh-cn": "四海捷运", "_name_zh-hk": "四海捷運", "_group": "12"}, {"key": 190989, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ************", "_url": "http://www.xf-express.com.cn/", "_name": "Sinfor", "_name_zh-cn": "信丰物流", "_name_zh-hk": "信豐物流", "_group": "12"}, {"key": 19131, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "http://www.singpost.com/", "_name": "Singapore Post", "_name_zh-cn": "Singapore Post", "_name_zh-hk": "Singapore Post", "_group": ""}, {"key": 190911, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.shxhhy.com/", "_name": "SINGHO", "_name_zh-cn": "SINGHO", "_name_zh-hk": "SINGHO", "_group": "12"}, {"key": 190351, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sinodidiexpress.com/", "_name": "Sinodidi", "_name_zh-cn": "外运迪迪", "_name_zh-hk": "外運迪迪", "_group": "12"}, {"key": 190137, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.sinoex.com.cn/", "_name": "SINOEX ", "_name_zh-cn": "中外运速递 ", "_name_zh-hk": "中外運速遞", "_group": "12"}, {"key": 190312, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://trace.sinotrans.hk/", "_name": "Sinotrans", "_name_zh-cn": "中外运香港", "_name_zh-hk": "中外運香港", "_group": "10"}, {"key": 101040, "_country": 215, "_country_iso": "BR", "_email": "<EMAIL>", "_tel": "+55-11 94581-3860", "_url": "https://www.sislogica.com.br/", "_name": "Sislógica", "_name_zh-cn": "Sislógica", "_name_zh-hk": "Sislógica", "_group": "11"}, {"key": 191245, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL> ", "_tel": "", "_url": "http://www.siweyexpress.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "思纬国际物流", "_name_zh-hk": "思緯國際物流", "_group": "12"}, {"key": 190838, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sxexp.cn/", "_name": "SIXU", "_name_zh-cn": "斯旭国际", "_name_zh-hk": "斯旭國際", "_group": "12"}, {"key": 191070, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.siyunda.com/", "_name": "SIYUNDA", "_name_zh-cn": "驷运达国际", "_name_zh-hk": "駟運達國際", "_group": "12"}, {"key": 191004, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  0579-85650765  ", "_url": "http://www.sj-kd.com/  ", "_name": "SJKD", "_name_zh-cn": "思捷快递", "_name_zh-hk": "思捷快遞", "_group": "12"}, {"key": 190488, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://skr56.com/", "_name": "SKR", "_name_zh-cn": "SKR", "_name_zh-hk": "SKR", "_group": "12"}, {"key": 100290, "_country": 707, "_country_iso": "GR", "_email": "", "_tel": "", "_url": "https://www.skroutzlastmile.gr/", "_name": "Skroutz Last Mile", "_name_zh-cn": "Skroutz Last Mile", "_name_zh-hk": "Skroutz Last Mile", "_group": "11"}, {"key": 190639, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://skuare.com.cn/", "_name": "SKUARE", "_name_zh-cn": "世宽国际", "_name_zh-hk": "世寬國際", "_group": "12"}, {"key": 100941, "_country": 2004, "_country_iso": "TH", "_email": "<EMAIL>", "_tel": "+66-02-276-5902", "_url": "https://get.attskybox.com/", "_name": "Skybox", "_name_zh-cn": "Skybox", "_name_zh-hk": "Skybox", "_group": "11"}, {"key": 100825, "_country": 503, "_country_iso": "AE", "_email": "<EMAIL>", "_tel": "+971 423 38 400", "_url": "https://skyexpressinternational.com/", "_name": "SkyEx (Sky Express)", "_name_zh-cn": "SkyEx (Sky Express)", "_name_zh-hk": "SkyEx (Sky Express)", "_group": "11"}, {"key": 191154, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://skygroupyiwu.com/", "_name": "SKYGROUP", "_name_zh-cn": "浙江天运供应链", "_name_zh-hk": "浙江天運供應鏈", "_group": "12"}, {"key": 100452, "_country": 313, "_country_iso": "CO", "_email": "<EMAIL>", "_tel": "+91-9681872430", "_url": "https://skyking.co/track/", "_name": "Skyking", "_name_zh-cn": "Skyking", "_name_zh-hk": "Skyking", "_group": "11"}, {"key": 100025, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.skynet.net/", "_name": "Skynet", "_name_zh-cn": "Skynet", "_name_zh-hk": "Skynet", "_group": "11"}, {"key": 100192, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "+03-8602 9266", "_url": "https://www.skynet.com.my/", "_name": "Skynet (MY)", "_name_zh-cn": "Skynet (MY)", "_name_zh-hk": "Skynet (MY)", "_group": "11"}, {"key": 100833, "_country": 1917, "_country_iso": "ZA", "_email": "<EMAIL>", "_tel": "+27-************", "_url": "https://www.skynet.co.za/", "_name": "Skynet (ZA)", "_name_zh-cn": "Skynet (ZA)", "_name_zh-hk": "Skynet (ZA)", "_group": "11"}, {"key": 100730, "_country": 503, "_country_iso": "AE", "_email": "<EMAIL>", "_tel": "+971 4 2826555", "_url": "https://www.skynetworldwide.net/", "_name": "SkyNet Worldwide Express", "_name_zh-cn": "SkyNet Worldwide Express", "_name_zh-hk": "SkyNet Worldwide Express", "_group": "11"}, {"key": 100149, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "****** 599 1812", "_url": "https://www.skypostal.com/", "_name": "SkyPostal", "_name_zh-cn": "SkyPostal", "_name_zh-hk": "SkyPostal", "_group": "11"}, {"key": 190552, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yz-ex.com/", "_name": "SLICITY", "_name_zh-cn": "顺联城物流", "_name_zh-hk": "順聯城物流", "_group": "12"}, {"key": 100407, "_country": 1914, "_country_iso": "SK", "_email": "", "_tel": "", "_url": "https://www.sps-sro.sk/", "_name": "Slovak Parcel Service", "_name_zh-cn": "Slovak Parcel Service", "_name_zh-hk": "Slovak Parcel Service", "_group": "11"}, {"key": 19141, "_country": 1914, "_country_iso": "SK", "_email": "", "_tel": "", "_url": "http://www.posta.sk/", "_name": "Slovakia Post", "_name_zh-cn": "Slovakia Post", "_name_zh-hk": "Slovakia Post", "_group": ""}, {"key": 19151, "_country": 1915, "_country_iso": "SI", "_email": "", "_tel": "", "_url": "http://www.posta.si/", "_name": "Slovenia Post", "_name_zh-cn": "Slovenia Post", "_name_zh-hk": "Slovenia Post", "_group": ""}, {"key": 190778, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-23357854", "_url": "http://www.sltgjkd.com/", "_name": "SLT", "_name_zh-cn": "胜利通", "_name_zh-hk": "勝利通", "_group": "12"}, {"key": 100170, "_country": 707, "_country_iso": "GR", "_email": "<EMAIL>", "_tel": "+30 210 9243 008", "_url": "https://smartpost.global/en", "_name": "SMART Post Global", "_name_zh-cn": "SMART Post Global", "_name_zh-hk": "SMART Post Global", "_group": "11"}, {"key": 100567, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 1860-210-1999", "_url": "https://smartr.in/", "_name": "Smartr Logistics", "_name_zh-cn": "Smartr Logistics", "_name_zh-hk": "Smartr Logistics", "_group": "11"}, {"key": 100658, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+966 114020036", "_url": "https://smb.express/", "_name": "SMB Express", "_name_zh-cn": "SMB Express", "_name_zh-hk": "SMB Express", "_group": "11"}, {"key": 100034, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "+966 920009999", "_url": "https://www.smsaexpress.com/", "_name": "SMSA Express (سمسا)", "_name_zh-cn": "SMSA Express (سمسا)", "_name_zh-hk": "SMSA Express (سمسا)", "_group": "11"}, {"key": 100785, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+601126122413", "_url": "http://www.sntglobal.com/", "_name": "SNT Global Logistics", "_name_zh-cn": "SNT Global Logistics", "_name_zh-hk": "SNT Global Logistics", "_group": "11"}, {"key": 191131, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.szsnx.com/", "_name": "SNX", "_name_zh-cn": "深南兴", "_name_zh-hk": "深南興", "_group": "12"}, {"key": 190654, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.soarmall.com/", "_name": "SoarMall", "_name_zh-cn": "速猫", "_name_zh-hk": "速貓", "_group": "12"}, {"key": 190692, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-23304465", "_url": "http://www.solidlogistics.cn/", "_name": "SOLID LOGISTICS", "_name_zh-cn": "上海昌迈", "_name_zh-hk": "上海昌邁", "_group": "12"}, {"key": 19161, "_country": 1916, "_country_iso": "SB", "_email": "", "_tel": "", "_url": "http://www.solomonpost.com.sb/", "_name": "Solomon Post", "_name_zh-cn": "Solomon Post", "_name_zh-hk": "Solomon Post", "_group": ""}, {"key": 2181, "_country": 218, "_country_iso": "BF", "_email": "", "_tel": "", "_url": "https://laposte.bf/", "_name": "Sonapost", "_name_zh-cn": "Sonapost", "_name_zh-hk": "Sonapost", "_group": ""}, {"key": 100972, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-286-8900", "_url": "http://www.sonictl.com/", "_name": "Sonic Transportation & Logistics", "_name_zh-cn": "Sonic Transportation & Logistics", "_name_zh-hk": "Sonic Transportation & Logistics", "_group": "11"}, {"key": 19171, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "", "_url": "https://www.postoffice.co.za/", "_name": "South Africa Post", "_name_zh-cn": "South Africa Post", "_name_zh-hk": "South Africa Post", "_group": ""}, {"key": 190542, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.southamericapost.com/", "_name": "South American Post", "_name_zh-cn": "南美邮联", "_name_zh-hk": "南美郵聯", "_group": "12"}, {"key": 100368, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://www.sefl.com/", "_name": "Southeastern Freight Lines", "_name_zh-cn": "Southeastern Freight Lines", "_name_zh-hk": "Southeastern Freight Lines", "_group": "11"}, {"key": 101024, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+1-(800)533-1071", "_url": "https://www.smtl.com/", "_name": "Southwestern Motor Transport", "_name_zh-cn": "Southwestern Motor Transport", "_name_zh-hk": "Southwestern Motor Transport", "_group": "11"}, {"key": 101002, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": "https://www.spaceshipapp.com/", "_name": "Spaceship", "_name_zh-cn": "Spaceship", "_name_zh-hk": "Spaceship", "_group": "11"}, {"key": 100322, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://spaldex-express.ru/", "_name": "Spaldex+", "_name_zh-cn": "Spaldex+", "_name_zh-hk": "Spaldex+", "_group": "11"}, {"key": 100845, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://spedisci.online/", "_name": "Spedisci .online", "_name_zh-cn": "Spedisci .online", "_name_zh-hk": "Spedisci .online", "_group": "11"}, {"key": 100908, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 0484 - 2957252", "_url": "https://www.gokulamspeedandsafe.com/", "_name": "SPEED & SAFE", "_name_zh-cn": "SPEED & SAFE", "_name_zh-hk": "SPEED & SAFE", "_group": "11"}, {"key": 100307, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ************", "_url": "https://speedaf.com/", "_name": "Speedaf", "_name_zh-cn": "Speedaf", "_name_zh-hk": "Speedaf", "_group": "11"}, {"key": 100447, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-862-5578", "_url": "https://speedeedelivery.com/", "_name": "Spee-<PERSON>", "_name_zh-cn": "Spee-<PERSON>", "_name_zh-hk": "Spee-<PERSON>", "_group": "11"}, {"key": 100315, "_country": 707, "_country_iso": "GR", "_email": " <EMAIL>", "_tel": "+30 210 3407000", "_url": "http://www.speedex.gr/", "_name": "SPEEDEX", "_name_zh-cn": "SPEEDEX", "_name_zh-hk": "SPEEDEX", "_group": "11"}, {"key": 101004, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "", "_url": "https://www.speedpost.com.sg/", "_name": "Speedpost", "_name_zh-cn": "Speedpost", "_name_zh-hk": "Speedpost", "_group": "11"}, {"key": 190844, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-658-4192", "_url": "https://www.speedx.io/", "_name": "SpeedX", "_name_zh-cn": "SpeedX", "_name_zh-hk": "SpeedX", "_group": "12"}, {"key": 100198, "_country": 217, "_country_iso": "BG", "_email": "", "_tel": "", "_url": "https://www.speedy.bg/en/", "_name": "<PERSON><PERSON> (DPD)", "_name_zh-cn": "<PERSON><PERSON> (DPD)", "_name_zh-hk": "<PERSON><PERSON> (DPD)", "_group": "11"}, {"key": 190213, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://spes.link/", "_name": "SPES", "_name_zh-cn": "速易邮", "_name_zh-hk": "速易郵", "_group": "10"}, {"key": 100955, "_country": 215, "_country_iso": "BR", "_email": "<EMAIL>", "_tel": "+55 (11) 3199-4016", "_url": "https://spflylogistica.com.br/", "_name": "SPFLY LOGÍSTICA", "_name_zh-cn": "SPFLY LOGÍSTICA", "_name_zh-hk": "SPFLY LOGÍSTICA", "_group": "11"}, {"key": 100907, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 9999 570 570", "_url": "https://www.spicexpress.com/", "_name": "SPICEXPRESS", "_name_zh-cn": "SPICEXPRESS", "_name_zh-hk": "SPICEXPRESS", "_group": "11"}, {"key": 191295, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.spl-express.com/", "_name": "SPL", "_name_zh-cn": "海港极速", "_name_zh-hk": "海港極速", "_group": "12"}, {"key": 100700, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 1800-102-1414", "_url": "https://web1.spoton.co.in/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100213, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": "https://www.spring-gds.com/", "_name": "Spring GDS", "_name_zh-cn": "Spring GDS", "_name_zh-hk": "Spring GDS", "_group": "11"}, {"key": 100496, "_country": 502, "_country_iso": "EG", "_email": "<EMAIL>", "_tel": "", "_url": "https://sprint.xyz/", "_name": "sprint", "_name_zh-cn": "sprint", "_name_zh-hk": "sprint", "_group": "11"}, {"key": 100582, "_country": 805, "_country_iso": "HU", "_email": "", "_tel": "", "_url": "https://www.sprinter.hu/", "_name": "Sprinter", "_name_zh-cn": "Sprinter", "_name_zh-hk": "Sprinter", "_group": "11"}, {"key": 190059, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sprintpack.com.cn/", "_name": "SprintPack", "_name_zh-cn": "欧速通", "_name_zh-hk": "歐速通", "_group": "12"}, {"key": 100172, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://sprintstar.ca/", "_name": "Sprintstar", "_name_zh-cn": "Sprintstar", "_name_zh-hk": "Sprintstar", "_group": "11"}, {"key": 191090, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://spst168.com/", "_name": "SPST", "_name_zh-cn": "SPST", "_name_zh-hk": "SPST", "_group": "12"}, {"key": 190514, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://suzsq.com/", "_name": "SQGJ", "_name_zh-cn": "胜琪国际", "_name_zh-hk": "勝琪國際", "_group": "12"}, {"key": 191033, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>; <EMAIL>", "_tel": "", "_url": " http://www.srh-express.com/", "_name": "SRH", "_name_zh-cn": "SRH", "_name_zh-hk": "SRH", "_group": "12"}, {"key": 19191, "_country": 1919, "_country_iso": "LK", "_email": "", "_tel": "", "_url": "http://www.slpost.gov.lk/", "_name": "Sri Lanka Post", "_name_zh-cn": "Sri Lanka Post", "_name_zh-hk": "Sri Lanka Post", "_group": ""}, {"key": 190840, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 13204004652", "_url": "http://huayu-ex.com/", "_name": "SRJ", "_name_zh-cn": "思睿捷国际", "_name_zh-hk": "思睿捷國際", "_group": "12"}, {"key": 190540, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sscexp.com/", "_name": "SSC", "_name_zh-cn": "加速供应链", "_name_zh-hk": "加速供應鏈", "_group": "12"}, {"key": 190865, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86  18344588887 ", "_url": "https://www.st8887.com/", "_name": "ST", "_name_zh-cn": "ST", "_name_zh-hk": "ST", "_group": "12"}, {"key": 190963, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL> ", "_tel": "", "_url": "http://www.kgy58.com/", "_name": "ST", "_name_zh-cn": "世通国际", "_name_zh-hk": "世通國際", "_group": "12"}, {"key": 190763, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-020-36483303", "_url": "http://www.stockcargo.cn/", "_name": "ST CARGO", "_name_zh-cn": "世拓物流", "_name_zh-hk": "世拓物流", "_group": "12"}, {"key": 100604, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 04461266666", "_url": "http://stcourier.com/", "_name": "ST Courier", "_name_zh-cn": "ST Courier", "_name_zh-hk": "ST Courier", "_group": "11"}, {"key": 190705, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+0852-30717833", "_url": "https://www.sutongst.com/", "_name": "ST International Express", "_name_zh-cn": "速通国际快运", "_name_zh-hk": "速通國際快運", "_group": "12"}, {"key": 190377, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.stadt.com.cn/", "_name": "STADT", "_name_zh-cn": "施塔特", "_name_zh-hk": "施塔特", "_group": "10"}, {"key": 100834, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "******-863-7447", "_url": "https://stallionexpress.ca/", "_name": "Stallion Express", "_name_zh-cn": "Stallion Express", "_name_zh-hk": "Stallion Express", "_group": "11"}, {"key": 100673, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "", "_url": "https://standardglobal.site/", "_name": "Standard Global Logistics", "_name_zh-cn": "Standard Global Logistics", "_name_zh-hk": "Standard Global Logistics", "_group": "11"}, {"key": 190995, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.xs-express.cn/", "_name": "STAR SPEED", "_name_zh-cn": "星速物流", "_name_zh-hk": "星速物流", "_group": "12"}, {"key": 101046, "_country": 117, "_country_iso": "AZ", "_email": "<EMAIL>", "_tel": "+994-012-310-08-25", "_url": "https://starex.az", "_name": "Starex", "_name_zh-cn": "Starex", "_name_zh-hk": "Starex", "_group": "11"}, {"key": 100635, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91 7304345666", "_url": "https://www.starexlogistic.com/", "_name": "Starex Logistic", "_name_zh-cn": "Starex Logistic", "_name_zh-hk": "Starex Logistic", "_group": "11"}, {"key": 100246, "_country": 310, "_country_iso": "CL", "_email": "", "_tel": "", "_url": "https://www.starken.cl/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100998, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-0203 004 7987", "_url": "https://starlinks-global.com/", "_name": "Starlinks Global", "_name_zh-cn": "Starlinks Global", "_name_zh-hk": "Starlinks Global", "_group": "11"}, {"key": 100335, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "+61 13 23 45", "_url": "http://startrack.com.au/", "_name": "StarTrack", "_name_zh-cn": "StarTrack", "_name_zh-hk": "StarTrack", "_group": "11"}, {"key": 190476, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.star-wish.cn/", "_name": "Star-Wish", "_name_zh-cn": "星希望供应链", "_name_zh-hk": "星希望供應鏈", "_group": "12"}, {"key": 100818, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-479-9260", "_url": "https://statovernight.com/", "_name": "STAT Overnight Delivery", "_name_zh-cn": "STAT Overnight Delivery", "_name_zh-hk": "STAT Overnight Delivery", "_group": "11"}, {"key": 191242, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://erp.xjiexpress.com/", "_name": "Stellar", "_name_zh-cn": "星际通联", "_name_zh-hk": "星際連通", "_group": "12"}, {"key": 100576, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://sterling.quick.aero/", "_name": "Sterling Global Aviation Logistics", "_name_zh-cn": "Sterling Global Aviation Logistics", "_name_zh-hk": "Sterling Global Aviation Logistics", "_group": "11"}, {"key": 190324, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sto.cn/", "_name": "STO Express", "_name_zh-cn": "申通快递", "_name_zh-hk": "申通快遞", "_group": "12"}, {"key": 190316, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.stosolution.com/", "_name": "STO Global", "_name_zh-cn": "申通国际", "_name_zh-hk": "申通國際", "_group": "10"}, {"key": 190605, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.stone3pl.com/", "_name": "STONE3PL", "_name_zh-cn": "STONE3PL", "_name_zh-hk": "STONE3PL", "_group": "12"}, {"key": 100262, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "", "_url": "https://straightship.com/", "_name": "Straightship", "_name_zh-cn": "Straightship", "_name_zh-hk": "Straightship", "_group": "11"}, {"key": 191206, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL> ", "_tel": "", "_url": "https://www.summitspeed.com/", "_name": "STS", "_name_zh-cn": "STS", "_name_zh-hk": "STS", "_group": "12"}, {"key": 19201, "_country": 1920, "_country_iso": "SD", "_email": "", "_tel": "", "_url": "http://sudapost.sd/", "_name": "Sudan Post", "_name_zh-cn": "Sudan Post", "_name_zh-hk": "Sudan Post", "_group": ""}, {"key": 190816, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gsmbest.club/", "_name": "SuE", "_name_zh-cn": "速俄", "_name_zh-hk": "速俄", "_group": "12"}, {"key": 190756, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+852-37961200", "_url": "http://www.sugouex.com/", "_name": "SUGOUEX", "_name_zh-cn": "速购速递", "_name_zh-hk": "速購速遞", "_group": "12"}, {"key": 191030, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": " http://www.suhusupplychain.com/", "_name": "<PERSON>hu supply chain", "_name_zh-cn": "速虎速运", "_name_zh-hk": "速虎速運", "_group": "12"}, {"key": 190171, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sumtom.cn/", "_name": "SUMTOM", "_name_zh-cn": "SUMTOM", "_name_zh-hk": "SUMTOM", "_group": "12"}, {"key": 190363, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.isunnyway.com/", "_name": "Sunnyway", "_name_zh-cn": "Sunnyway", "_name_zh-hk": "Sunnyway", "_group": "12"}, {"key": 190632, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sunsonexpress.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "燊鑫国际物流", "_name_zh-hk": "燊鑫國際物流", "_group": "12"}, {"key": 190072, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ************", "_url": "http://www.sypost.com/", "_name": "SUNYOU", "_name_zh-cn": "顺友物流", "_name_zh-hk": "順友物流", "_group": "12"}, {"key": 191262, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "(+1)************ ", "_url": "https://www.supaiexpress.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "12"}, {"key": 191270, "_country": 801, "_country_iso": "HK", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.supcourier.com/ ", "_name": "Supcourier", "_name_zh-cn": "Supcourier", "_name_zh-hk": "Supcourier", "_group": "12"}, {"key": 190823, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://home.57track.com/", "_name": "Super Pack Line", "_name_zh-cn": "Super Pack Line", "_name_zh-hk": "Super Pack Line", "_group": "12"}, {"key": 100479, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "", "_url": "https://track-sp.com/", "_name": "Super Parcel", "_name_zh-cn": "Super Parcel", "_name_zh-hk": "Super Parcel", "_group": "11"}, {"key": 190837, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.superbuy.com/", "_name": "Superbuy", "_name_zh-cn": "Superbuy", "_name_zh-hk": "Superbuy", "_group": "12"}, {"key": 100553, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "http://heroexpress.com.au/", "_name": "Superhero Express", "_name_zh-cn": "Superhero Express", "_name_zh-hk": "Superhero Express", "_group": "11"}, {"key": 190566, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+(86) 13924013537", "_url": "http://superior-digital.com/", "_name": "Superior Digital", "_name_zh-cn": "速点供应链", "_name_zh-hk": "速點供應鏈", "_group": "12"}, {"key": 191185, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>/<EMAIL>", "_tel": "+86 18818569951", "_url": "https://www.isuperto.com/", "_name": "SUPERTO", "_name_zh-cn": "超极兔物流", "_name_zh-hk": "超級兔物流", "_group": "12"}, {"key": 190402, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.super-ton.com/", "_name": "SuperTon", "_name_zh-cn": "速宝通", "_name_zh-hk": "速寶通", "_group": "12"}, {"key": 100715, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-141-4010651", "_url": "https://supremexp.com/", "_name": "Supreme Express", "_name_zh-cn": "Supreme Express", "_name_zh-hk": "Supreme Express", "_group": "11"}, {"key": 100703, "_country": 2011, "_country_iso": "TR", "_email": "<EMAIL>", "_tel": "+90 0850 202 02 02", "_url": "https://www.suratkargo.com.tr/", "_name": "Surat Kargo (S<PERSON>rat <PERSON>rgo)", "_name_zh-cn": "Surat Kargo (S<PERSON>rat <PERSON>rgo)", "_name_zh-hk": "Surat Kargo (S<PERSON>rat <PERSON>rgo)", "_group": "11"}, {"key": 190866, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ************", "_url": "http://www.ste56.com/", "_name": "Suteng Logistics", "_name_zh-cn": "速腾物流", "_name_zh-hk": "速騰物流", "_group": "12"}, {"key": 190449, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sut56.com/", "_name": "Suto Logistics", "_name_zh-cn": "速通物流", "_name_zh-hk": "速通物流", "_group": "12"}, {"key": 100736, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-472-6886", "_url": "https://www.suttontrans.com/", "_name": "Sutton Transport", "_name_zh-cn": "Sutton Transport", "_name_zh-hk": "Sutton Transport", "_group": "11"}, {"key": 191156, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0512-65786737 ", "_url": "http://www.yisu-exp.cn/", "_name": "SUZHOUYISU", "_name_zh-cn": "苏州翼速国际物流", "_name_zh-hk": "蘇州翼速國際物流", "_group": "12"}, {"key": 19021, "_country": 1902, "_country_iso": "VC", "_email": "", "_tel": "", "_url": "http://www.svgpost.gov.vc/", "_name": "SVG Post", "_name_zh-cn": "SVG Post", "_name_zh-hk": "SVG Post", "_group": ""}, {"key": 100868, "_country": 707, "_country_iso": "GR", "_email": " <EMAIL>", "_tel": "+30-217 000 77 17", "_url": "https://svuum.gr/", "_name": "Svuum", "_name_zh-cn": "Svuum", "_name_zh-hk": "Svuum", "_group": "11"}, {"key": 190436, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sedel.com/", "_name": "SWE", "_name_zh-cn": "SWE", "_name_zh-hk": "SWE", "_group": "12"}, {"key": 100499, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.goswift.in/", "_name": "Swift", "_name_zh-cn": "Swift", "_name_zh-hk": "Swift", "_group": "11"}, {"key": 100143, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "****************", "_url": "https://www.swiship.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100415, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.swiship.com.au/track/", "_name": "<PERSON><PERSON><PERSON> (AU)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (AU)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (AU)", "_group": "11"}, {"key": 100570, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "", "_url": "https://www.swiship.ca/", "_name": "<PERSON><PERSON><PERSON> (CA)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (CA)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (CA)", "_group": "11"}, {"key": 100312, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "", "_url": "https://www.swiship.de/", "_name": "<PERSON><PERSON><PERSON> (DE)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (DE)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (DE)", "_group": "11"}, {"key": 100571, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "", "_url": "https://www.swiship.es/", "_name": "<PERSON><PERSON><PERSON> (ES)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (ES)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (ES)", "_group": "11"}, {"key": 100572, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://www.swiship.fr/", "_name": "Swiship (FR)", "_name_zh-cn": "Swiship (FR)", "_name_zh-hk": "Swiship (FR)", "_group": "11"}, {"key": 100564, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "https://www.swiship.jp/track/", "_name": "<PERSON><PERSON><PERSON> (JP)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (JP)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (JP)", "_group": "11"}, {"key": 100214, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.swiship.co.uk/track/", "_name": "<PERSON><PERSON><PERSON> (UK)", "_name_zh-cn": "<PERSON><PERSON><PERSON> (UK)", "_name_zh-hk": "<PERSON><PERSON><PERSON> (UK)", "_group": "11"}, {"key": 19251, "_country": 1925, "_country_iso": "CH", "_email": "", "_tel": "", "_url": "https://www.post.ch/", "_name": "Swiss Post", "_name_zh-cn": "Swiss Post", "_name_zh-hk": "Swiss Post", "_group": ""}, {"key": 100373, "_country": 1601, "_country_iso": "PK", "_email": "", "_tel": "", "_url": "https://swyftlogistics.com/", "_name": "Swyft Logistics", "_name_zh-cn": "Swyft Logistics", "_name_zh-hk": "Swyft Logistics", "_group": "11"}, {"key": 190900, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 4000 300 688", "_url": "https://www.sxjdfreight.com/", "_name": "SXJD", "_name_zh-cn": "顺心捷达", "_name_zh-hk": "順心捷達", "_group": "12"}, {"key": 190221, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.suyd56.com/", "_name": "SYD", "_name_zh-cn": "速邮达物流", "_name_zh-hk": "速郵達物流", "_group": "12"}, {"key": 191352, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.shengyugj.com/", "_name": "SYGJ", "_name_zh-cn": "升誉国际", "_name_zh-hk": "升譽國際", "_group": "12"}, {"key": 100992, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.syncreon.com/", "_name": "syncreon", "_name_zh-cn": "syncreon", "_name_zh-hk": "syncreon", "_group": "11"}, {"key": 100670, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.synship.com/", "_name": "SynShip", "_name_zh-cn": "SynShip", "_name_zh-hk": "SynShip", "_group": "11"}, {"key": 19261, "_country": 1926, "_country_iso": "SY", "_email": "", "_tel": "", "_url": "http://www.syrianpost.gov.sy/", "_name": "Syrian Post", "_name_zh-cn": "Syrian Post", "_name_zh-hk": "Syrian Post", "_group": ""}, {"key": 190803, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13554816072", "_url": "http://www.szxhgl.com/", "_name": "SZ Xinhe", "_name_zh-cn": "欣和物流", "_name_zh-hk": "欣和物流", "_group": "12"}, {"key": 100275, "_country": 401, "_country_iso": "DK", "_email": "<EMAIL>", "_tel": "+45 93 846 67 39", "_url": "https://www.szendex.com/", "_name": "Szendex", "_name_zh-cn": "Szendex", "_name_zh-hk": "Szendex", "_group": "11"}, {"key": 191168, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18617129010 ", "_url": "http://www.hygj168.cn/", "_name": "SZHY", "_name_zh-cn": "恒毅国际", "_name_zh-hk": "恆毅國際", "_group": "12"}, {"key": 191157, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.huayutong56.com/", "_name": "SZHYT", "_name_zh-cn": "深圳华羽通", "_name_zh-hk": "深圳華羽通", "_group": "12"}, {"key": 191220, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  075522742069 ", "_url": "https://www.szlongg.com/", "_name": "SZLONG", "_name_zh-cn": "长和运物流", "_name_zh-hk": "長合運物流", "_group": "12"}, {"key": 191191, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.xmszm.com/", "_name": "SZM", "_name_zh-cn": "SZM", "_name_zh-hk": "SZM", "_group": "12"}, {"key": 190523, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.dxd-express.com/", "_name": "SZMY", "_name_zh-cn": "SZMY", "_name_zh-hk": "SZMY", "_group": "12"}, {"key": 191331, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-13613001456", "_url": "http://zhonghongyuntong.com/", "_name": "SZSZHYT", "_name_zh-cn": "深圳市中鸿运通", "_name_zh-hk": "深圳市中鴻運通", "_group": "12"}, {"key": 191068, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13712783882", "_url": "http://www.wjdgj.com/", "_name": "SZWJD", "_name_zh-cn": "深圳万境达", "_name_zh-hk": "深圳萬境達", "_group": "12"}, {"key": 190580, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xydfreight.com/", "_name": "SZXYD LOGISTICS", "_name_zh-cn": "鑫翼达", "_name_zh-hk": "鑫翼達", "_group": "12"}, {"key": 190479, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gztaishi.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "泰实", "_name_zh-hk": "泰實", "_group": "12"}, {"key": 190139, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.takesend.com/", "_name": "Take Send", "_name_zh-cn": "泰嘉物流", "_name_zh-hk": "泰嘉物流", "_group": "12"}, {"key": 20031, "_country": 2003, "_country_iso": "TZ", "_email": "", "_tel": "", "_url": "http://www.posta.co.tz/", "_name": "Tanzania Post", "_name_zh-cn": "Tanzania Post", "_name_zh-hk": "Tanzania Post", "_group": ""}, {"key": 100687, "_country": 503, "_country_iso": "AE", "_email": "<EMAIL>", "_tel": "+971 600 50 50 59", "_url": "https://tataexpress.ae/", "_name": "Tata Express", "_name_zh-cn": "Tata Express", "_name_zh-hk": "Tata Express", "_group": "11"}, {"key": 101027, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://www.taxair.com/", "_name": "Tax-Air", "_name_zh-cn": "Tax-Air", "_name_zh-hk": "Tax-Air", "_group": "11"}, {"key": 100215, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "", "_url": "http://www.t-cat.com.tw/", "_name": "T-CAT", "_name_zh-cn": "T-CAT (黑猫宅急便)", "_name_zh-hk": "T-CAT (黑猫宅急便)", "_group": "11"}, {"key": 100824, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91-************", "_url": "https://www.tciexpress.in/", "_name": "TCIEXPRESS ", "_name_zh-cn": "TCIEXPRESS ", "_name_zh-hk": "TCIEXPRESS ", "_group": "11"}, {"key": 100741, "_country": 1913, "_country_iso": "SG", "_email": "<EMAIL>", "_tel": "+65 6684 6646", "_url": "https://www.tckexpress.com/", "_name": "TCK Express", "_name_zh-cn": "TCK Express", "_name_zh-hk": "TCK Express", "_group": "11"}, {"key": 190699, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-400-6155-883", "_url": "https://www.tclogx.com/", "_name": "Tclogx", "_name_zh-cn": "唐朝物流", "_name_zh-hk": "唐朝物流", "_group": "12"}, {"key": 100260, "_country": 1601, "_country_iso": "PK", "_email": "", "_tel": "", "_url": "https://www.tcsexpress.com/", "_name": "TCS", "_name_zh-cn": "TCS", "_name_zh-hk": "TCS", "_group": "11"}, {"key": 190318, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sztdgyl.com/", "_name": "TDE", "_name_zh-cn": "TDE", "_name_zh-hk": "TDE", "_group": "10"}, {"key": 191135, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.tongdagj56.com/", "_name": "TDGJ", "_name_zh-cn": "通达国际", "_name_zh-hk": "通達國際", "_group": "12"}, {"key": 100987, "_country": 503, "_country_iso": "AE", "_email": "<EMAIL>", "_tel": "+971-4 2944453", "_url": "https://teamexpressme.com/", "_name": "Team Express", "_name_zh-cn": "Team Express", "_name_zh-hk": "Team Express", "_group": "11"}, {"key": 100872, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61-13 15 31", "_url": "https://www.myteamge.com/", "_name": "Team Global Express (MyTeamGE)", "_name_zh-cn": "Team Global Express (MyTeamGE)", "_name_zh-hk": "Team Global Express (MyTeamGE)", "_group": "11"}, {"key": 100613, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******5271168", "_url": "https://www.teamww.com/", "_name": "Team Worldwide", "_name_zh-cn": "Team Worldwide", "_name_zh-hk": "Team Worldwide", "_group": "11"}, {"key": 191314, "_country": 301, "_country_iso": "CN", "_email": "ji<PERSON><PERSON><PERSON>@teamwork56.com", "_tel": "+86-18148579117", "_url": "www.teamwork56.com/", "_name": "Teamwork", "_name_zh-cn": "双合物流", "_name_zh-hk": "雙合國際", "_group": "12"}, {"key": 100382, "_country": 1314, "_country_iso": "MX", "_email": "", "_tel": "", "_url": "https://www.teiker.mx/", "_name": "Teiker", "_name_zh-cn": "Teiker", "_name_zh-hk": "Teiker", "_group": "11"}, {"key": 100912, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91-8975754852", "_url": "https://www.tejcouriers.com/", "_name": "<PERSON>j <PERSON>s", "_name_zh-cn": "<PERSON>j <PERSON>s", "_name_zh-hk": "<PERSON>j <PERSON>s", "_group": "11"}, {"key": 96031, "_country": 9603, "_country_iso": "GL", "_email": "", "_tel": "", "_url": "https://telepost.gl/", "_name": "Tele Post", "_name_zh-cn": "Tele Post", "_name_zh-hk": "Tele Post", "_group": ""}, {"key": 100771, "_country": 1305, "_country_iso": "MY", "_email": "", "_tel": "+60 386604489", "_url": "https://www.teleport.asia/", "_name": "Teleport", "_name_zh-cn": "Teleport", "_name_zh-hk": "Teleport", "_group": "11"}, {"key": 191040, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 4008310125", "_url": "http://txfba.com/", "_name": "TENGXIN", "_name_zh-cn": "腾信物流", "_name_zh-hk": "騰信物流", "_group": "12"}, {"key": 100738, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 1300 113 822", "_url": "https://tfmxpress.com.au/", "_name": "TFMXpress", "_name_zh-cn": "TFMXpress", "_name_zh-hk": "TFMXpress", "_group": "11"}, {"key": 100061, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://www.tforcelogistics.com/", "_name": "TForce Final Mile", "_name_zh-cn": "TForce Final Mile", "_name_zh-hk": "TForce Final Mile", "_group": "11"}, {"key": 100399, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "******-333-7400", "_url": "https://www.tforcefreight.com/", "_name": "<PERSON><PERSON><PERSON><PERSON> (UPS Freight)", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON> (UPS Freight)", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON> (UPS Freight)", "_group": "11"}, {"key": 190297, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.tfs906.com/", "_name": "TFS", "_name_zh-cn": "天府盛", "_name_zh-hk": "天府盛", "_group": "12"}, {"key": 100397, "_country": 1907, "_country_iso": "SA", "_email": "<EMAIL>", "_tel": "+966  8001240260", "_url": "https://thabit-logistics.com/", "_name": "Thabit Logistics", "_name_zh-cn": "Thabit Logistics", "_name_zh-hk": "Thabit Logistics", "_group": "11"}, {"key": 20041, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "", "_url": "https://www.thailandpost.co.th/", "_name": "Thailand Post", "_name_zh-cn": "Thailand Post", "_name_zh-hk": "Thailand Post", "_group": ""}, {"key": 100184, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "", "_url": "https://www.thecourierguy.co.za/", "_name": "The Courier Guy", "_name_zh-cn": "The Courier Guy", "_name_zh-hk": "The Courier Guy", "_group": "11"}, {"key": 101055, "_country": 2204, "_country_iso": "VN", "_email": "<EMAIL>", "_tel": "", "_url": "https://thehuman.express/", "_name": "The Human Express", "_name_zh-cn": "The Human Express", "_name_zh-hk": "The Human Express", "_group": "11"}, {"key": 100279, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 022 – 27813309", "_url": "https://www.tpcglobe.com/", "_name": "The Professional Couriers", "_name_zh-cn": "The Professional Couriers", "_name_zh-hk": "The Professional Couriers", "_group": "11"}, {"key": 100862, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44-0844 833 0300", "_url": "https://www.upn.co.uk/", "_name": "The United Pallet Network", "_name_zh-cn": "The United Pallet Network", "_name_zh-hk": "The United Pallet Network", "_group": "11"}, {"key": 100806, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+60 16 - 299 2189", "_url": "https://thelorry.com/my", "_name": "TheLorryMY", "_name_zh-cn": "TheLorryMY", "_name_zh-hk": "TheLorryMY", "_group": "11"}, {"key": 190211, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.tzexp.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "10"}, {"key": 100774, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "+61 3 9809 7400", "_url": "https://www.tigfreight.com/", "_name": "TIG", "_name_zh-cn": "TIG", "_name_zh-hk": "TIG", "_group": "11"}, {"key": 100588, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "+62 1500125", "_url": "https://www.tiki.id/", "_name": "TIKI", "_name_zh-cn": "TIKI", "_name_zh-hk": "TIKI", "_group": "11"}, {"key": 100836, "_country": 1404, "_country_iso": "NL", "_email": " <EMAIL>", "_tel": "+31(0)10 7371 864", "_url": "https://www.timelytitan.com/", "_name": "Timely Titan EXPRESS", "_name_zh-cn": "Timely Titan EXPRESS", "_name_zh-hk": "Timely Titan EXPRESS", "_group": "11"}, {"key": 190946, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": " http://www.timisc.com/ ", "_name": "TIMI SC", "_name_zh-cn": "天美国际", "_name_zh-hk": "天美國際", "_group": "12"}, {"key": 190323, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.tianzongsc.com/", "_name": "Tinzung", "_name_zh-cn": "Tinzung", "_name_zh-hk": "Tinzung", "_group": "12"}, {"key": 100185, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "+34 902 10 10 47", "_url": "https://www.tip-sa.com/", "_name": "TIPSA", "_name_zh-cn": "TIPSA", "_name_zh-hk": "TIPSA", "_group": "11"}, {"key": 190536, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.shtjkd.com/", "_name": "TJ-EXP", "_name_zh-cn": "淘晶货运", "_name_zh-hk": "淘晶貨運", "_group": "12"}, {"key": 100633, "_country": 1601, "_country_iso": "PK", "_email": "", "_tel": "+92 (42) 3511 5300", "_url": "https://www.tmcargo.net/", "_name": "TM Cargo", "_name_zh-cn": "TM Cargo", "_name_zh-hk": "TM Cargo", "_group": "11"}, {"key": 100063, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "http://www.tmg-group.jp/", "_name": "TMG", "_name_zh-cn": "TMG", "_name_zh-hk": "TMG", "_group": "11"}, {"key": 100092, "_country": 1210, "_country_iso": "LU", "_email": "", "_tel": "+380 800 500 575", "_url": "http://tmm-express.com/", "_name": "TMM Express", "_name_zh-cn": "TMM Express", "_name_zh-hk": "TMM Express", "_group": "11"}, {"key": 100004, "_country": 1404, "_country_iso": "NL", "_email": "", "_tel": "+************", "_url": "https://www.tnt.com/", "_name": "TNT", "_name_zh-cn": "TNT", "_name_zh-hk": "TNT", "_group": "11"}, {"key": 100200, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.tntexpress.com.au/", "_name": "TNT (AU)", "_name_zh-cn": "TNT (AU)", "_name_zh-hk": "TNT (AU)", "_group": "11"}, {"key": 100241, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "http://www.tnt.fr/", "_name": "TNT (FR)", "_name_zh-cn": "TNT (FR)", "_name_zh-hk": "TNT (FR)", "_group": "11"}, {"key": 100065, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://www.tnt.it/", "_name": "TNT (IT)", "_name_zh-cn": "TNT (IT)", "_name_zh-hk": "TNT (IT)", "_group": "11"}, {"key": 190272, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.globesuccess.com.cn/", "_name": "TO C LOGISTICS", "_name_zh-cn": "途曦物流", "_name_zh-hk": "途曦物流", "_group": "12"}, {"key": 100789, "_country": 115, "_country_iso": "AU", "_email": "<EMAIL>", "_tel": "+61 1300 583 159", "_url": "https://www.tomydoor.com.au/", "_name": "To My Door", "_name_zh-cn": "To My Door", "_name_zh-hk": "To My Door", "_group": "11"}, {"key": 190468, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zxdexpress.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "至信达", "_name_zh-hk": "至信達", "_group": "10"}, {"key": 100009, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://www.tollgroup.com/", "_name": "Toll", "_name_zh-cn": "Toll", "_name_zh-hk": "Toll", "_group": "11"}, {"key": 100942, "_country": 1105, "_country_iso": "KR", "_email": "<EMAIL>", "_tel": "+82-2-6929-0081", "_url": "http://www.tolos.asia/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100163, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "http://www.tonami.co.jp/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190194, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.tarrive.com/", "_name": "Tonda Global", "_name_zh-cn": "通达全球", "_name_zh-hk": "通達全球", "_group": "12"}, {"key": 20061, "_country": 2006, "_country_iso": "TO", "_email": "", "_tel": "", "_url": "http://www.tongapost.to/", "_name": "Tonga Post", "_name_zh-cn": "Tonga Post", "_name_zh-hk": "Tonga Post", "_group": ""}, {"key": 100655, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "http://toplogistics.com.au/", "_name": "Top Logistics Australia (TLA)", "_name_zh-cn": "Top Logistics Australia (TLA)", "_name_zh-hk": "Top Logistics Australia (TLA)", "_group": "11"}, {"key": 190777, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-***********", "_url": "http://www.toppost.cn/", "_name": "Top Post", "_name_zh-cn": "鼎邮国际货运", "_name_zh-hk": "鼎郵國際貨運", "_group": "12"}, {"key": 190753, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.topestexpress.com/", "_name": "Topest", "_name_zh-cn": "深圳韬博", "_name_zh-hk": "深圳韜博", "_group": "12"}, {"key": 191209, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.topex.group/", "_name": "TOPEX", "_name_zh-cn": "迅派物流", "_name_zh-hk": "迅派物流", "_group": "12"}, {"key": 190074, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.szty56.com/", "_name": "TopYou", "_name_zh-cn": "通邮集团", "_name_zh-hk": "通郵集團", "_group": "10"}, {"key": 191252, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 15268657003", "_url": "https://torbon.com/", "_name": "Torbon Logistics", "_name_zh-cn": "浙江拓邦", "_name_zh-hk": "浙江拓邦", "_group": "12"}, {"key": 100995, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-510-7200", "_url": "https://ttdwest.com/", "_name": "Total Transport & Distribution, Inc.", "_name_zh-cn": "Total Transport & Distribution, Inc.", "_name_zh-hk": "Total Transport & Distribution, Inc.", "_group": "11"}, {"key": 191175, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://tourbell.cn/", "_name": "<PERSON>bell", "_name_zh-cn": "淘客小二", "_name_zh-hk": "淘客小貳", "_group": "12"}, {"key": 100390, "_country": 2004, "_country_iso": "TH", "_email": "", "_tel": "+66  02-360-7788", "_url": "https://www.thaiparcels.com/", "_name": "TP Logistics", "_name_zh-cn": "TP Logistics", "_name_zh-hk": "TP Logistics", "_group": "11"}, {"key": 100827, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "https://www.tracknator.com/", "_name": "Tracknator", "_name_zh-cn": "Tracknator", "_name_zh-hk": "Tracknator", "_group": "11"}, {"key": 100180, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "", "_url": "https://trackon.in/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100959, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34-902 270 333", "_url": "https://transaher.es/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100903, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 (0) 1133 200261", "_url": "https://www.transnetservices.co.uk/", "_name": "Transnet Couriers", "_name_zh-cn": "Transnet Couriers", "_name_zh-hk": "Transnet Couriers", "_group": "11"}, {"key": 100505, "_country": 1601, "_country_iso": "PK", "_email": "<EMAIL>", "_tel": "+92 21111118729", "_url": "https://trax.pk/", "_name": "Trax", "_name_zh-cn": "Trax", "_name_zh-hk": "Trax", "_group": "11"}, {"key": 100925, "_country": 2011, "_country_iso": "TR", "_email": "", "_tel": "+90-0850 759 15 15", "_url": "https://www.trendyolexpress.com/", "_name": "Trendyol Express", "_name_zh-cn": "Trendyol Express", "_name_zh-hk": "Trendyol Express", "_group": "11"}, {"key": 100865, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "+52-************", "_url": "https://www.tresguerras.com.mx/", "_name": "<PERSON><PERSON>gue<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>gue<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>gue<PERSON><PERSON>", "_group": "11"}, {"key": 191140, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-19874259800", "_url": "http://www.senseexpress.com.cn/", "_name": "TRI COLOR", "_name_zh-cn": "三色联华", "_name_zh-hk": "三色聯華", "_group": "12"}, {"key": 20071, "_country": 2007, "_country_iso": "TT", "_email": "", "_tel": "", "_url": "http://www.ttpost.net/", "_name": "Trinidad and Tobago Postal Corporation", "_name_zh-cn": "Trinidad and Tobago Postal Corporation", "_name_zh-hk": "Trinidad and Tobago Postal Corporation", "_group": ""}, {"key": 100478, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "+****************", "_url": "https://trumpcardinc.com/", "_name": "TrumpCard", "_name_zh-cn": "TrumpCard", "_name_zh-hk": "TrumpCard", "_group": "11"}, {"key": 190513, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hnqst.cn/", "_name": "TRUSTONE", "_name_zh-cn": "全速通", "_name_zh-hk": "全速通", "_group": "12"}, {"key": 191059, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18818292585", "_url": "http://www.stjy56.com/", "_name": "Ts", "_name_zh-cn": "通世", "_name_zh-hk": "通世", "_group": "12"}, {"key": 190752, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-020-36368936", "_url": "http://www.tianshengsc.com/", "_name": "TSCB", "_name_zh-cn": "天盛跨境", "_name_zh-hk": "天盛跨境", "_group": "12"}, {"key": 190617, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.51856.xyz/", "_name": "TSE", "_name_zh-cn": "TSE", "_name_zh-hk": "TSE", "_group": "12"}, {"key": 190077, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.ttkdex.com/", "_name": "TT Express", "_name_zh-cn": "天天快递", "_name_zh-hk": "天天快遞", "_group": "12"}, {"key": 190537, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szttgj.com/", "_name": "TTGJ", "_name_zh-cn": "通途国际", "_name_zh-hk": "通途國際", "_group": "12"}, {"key": 191037, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.logtt.com/ ", "_name": "TTGJ", "_name_zh-cn": "妥投国际物流", "_name_zh-hk": "妥投國際物流", "_group": "12"}, {"key": 100876, "_country": 1314, "_country_iso": "MX", "_email": "<EMAIL>", "_tel": "+52-477 928 43 20", "_url": "https://ttupaqueteria.com/", "_name": "TTU Paquetería y Mensajería", "_name_zh-cn": "TTU Paquetería y Mensajería", "_name_zh-hk": "TTU Paquetería y Mensajería", "_group": "11"}, {"key": 190847, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 18046727539", "_url": "http://www.tuf-express.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "12"}, {"key": 100524, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.tuffnells.co.uk/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190976, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 15812493646  ", "_url": "http://www.iturbao.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "拓宝国际物流", "_name_zh-hk": "拓寶國際物流", "_group": "12"}, {"key": 100979, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.tusklogistics.com/", "_name": "Tusk Logistics", "_name_zh-cn": "Tusk Logistics", "_name_zh-hk": "Tusk Logistics", "_group": "11"}, {"key": 190925, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+852 46347103", "_url": "http://tusoukeji.cn/", "_name": "<PERSON><PERSON><PERSON> ", "_name_zh-cn": "图搜物流", "_name_zh-hk": "圖搜物流", "_group": "12"}, {"key": 20091, "_country": 2009, "_country_iso": "TV", "_email": "", "_tel": "", "_url": "http://www.tuvalupost.tv/", "_name": "Tuvalu Post", "_name_zh-cn": "Tuvalu Post", "_name_zh-hk": "Tuvalu Post", "_group": ""}, {"key": 190892, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-27210810", "_url": "http://www.twbgo.com/", "_name": "TWB", "_name_zh-cn": "TWB", "_name_zh-hk": "TWB", "_group": "12"}, {"key": 100509, "_country": 907, "_country_iso": "IT", "_email": "<EMAIL>", "_tel": "+39.8955000509", "_url": "https://www.twsexpresscourier.it/", "_name": "TWS Express Courier", "_name_zh-cn": "TWS Express Courier", "_name_zh-hk": "TWS Express Courier", "_group": "11"}, {"key": 190418, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.th99.com/", "_name": "TWTH", "_name_zh-cn": "拓威天海", "_name_zh-hk": "拓威天海", "_group": "12"}, {"key": 100514, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "", "_url": "http://www.tie-xin.com.tw/", "_name": "TXExpress", "_name_zh-cn": "贴心物流", "_name_zh-hk": "貼心物流", "_group": "11"}, {"key": 190650, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.sztygj.net/", "_name": "TY", "_name_zh-cn": "腾宇国际", "_name_zh-hk": "騰宇國際", "_group": "12"}, {"key": 191225, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "+86 13590118135 ", "_url": "http://www.tongyhang.com/", "_name": "TYH", "_name_zh-cn": "通云航", "_name_zh-hk": "通雲航", "_group": "12"}, {"key": 100775, "_country": 907, "_country_iso": "IT", "_email": "", "_tel": "", "_url": "https://typ.delivery/", "_name": "TYP (To Your Place)", "_name_zh-cn": "TYP (To Your Place)", "_name_zh-hk": "TYP (To Your Place)", "_group": "11"}, {"key": 190928, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-27390057 ", "_url": "http://www.cnwwil.com/", "_name": "TZ", "_name_zh-cn": "TZ", "_name_zh-hk": "TZ", "_group": "12"}, {"key": 190193, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.tzgjwl.cn/", "_name": "TZT", "_name_zh-cn": "TZT", "_name_zh-hk": "TZT", "_group": "10"}, {"key": 191250, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.js-qc.com/", "_name": "U1", "_name_zh-cn": "优一实业", "_name_zh-hk": "優一實業", "_group": "12"}, {"key": 100517, "_country": 1917, "_country_iso": "ZA", "_email": "", "_tel": "", "_url": "https://www.uafrica.com/", "_name": "Uafrica", "_name_zh-cn": "Uafrica", "_name_zh-hk": "Uafrica", "_group": "11"}, {"key": 190112, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ubismartparcel.com/", "_name": "UBI", "_name_zh-cn": "UBI", "_name_zh-hk": "UBI", "_group": "12"}, {"key": 100795, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 01753 762 860", "_url": "https://www.ubx.uk.net/", "_name": "UBX", "_name_zh-cn": "UBX", "_name_zh-hk": "UBX", "_group": "11"}, {"key": 190415, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.uce.cn/", "_name": "UC Express", "_name_zh-cn": "优速快递", "_name_zh-hk": "優速快遞", "_group": "12"}, {"key": 191316, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.uc-express.cn/", "_name": "UCE", "_name_zh-cn": "优科跨境", "_name_zh-hk": "優科跨境", "_group": "12"}, {"key": 190674, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ucorreos.com/", "_name": "uCorreos", "_name_zh-cn": "环球通", "_name_zh-hk": "環球通", "_group": "12"}, {"key": 190852, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 86-24-31515566", "_url": "http://www.ucsus.net/", "_name": "UCS", "_name_zh-cn": "合众速递", "_name_zh-hk": "合眾速遞", "_group": "12"}, {"key": 190341, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.udalogistic.com/", "_name": "uda International", "_name_zh-cn": "韵达国际", "_name_zh-hk": "韻達國際", "_group": "12"}, {"key": 191039, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ************", "_url": "http://www.u-del.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "优递", "_name_zh-hk": "優遞", "_group": "12"}, {"key": 191304, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.u-del.com/", "_name": "UDEL （AU）", "_name_zh-cn": "UDEL （AU）", "_name_zh-hk": "UDEL （UK）", "_group": "12"}, {"key": 191302, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.u-del.com/", "_name": "UDEL （CA）", "_name_zh-cn": "UDEL （CA）", "_name_zh-hk": "UDEL （UK）", "_group": "12"}, {"key": 191303, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.u-del.com/", "_name": "UDEL （EU）", "_name_zh-cn": "UDEL （EU）", "_name_zh-hk": "UDEL （UK）", "_group": "12"}, {"key": 191305, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.u-del.com/", "_name": "UDEL （NZ）", "_name_zh-cn": "UDEL （NZ）", "_name_zh-hk": "UDEL （UK）", "_group": "12"}, {"key": 191301, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.u-del.com/", "_name": "UDEL （UK）", "_name_zh-cn": "UDEL （UK）", "_name_zh-hk": "UDEL （UK）", "_group": "12"}, {"key": 191356, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.u-del.com/", "_name": "UDEL （US）", "_name_zh-cn": "UDEL （US）", "_name_zh-hk": "UDEL （US）", "_group": "12"}, {"key": 191081, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "https://www.udsau.cn", "_name": "UDS", "_name_zh-cn": "UDS", "_name_zh-hk": "UDS", "_group": "12"}, {"key": 21011, "_country": 2101, "_country_iso": "UG", "_email": "", "_tel": "", "_url": "http://www.ugapost.co.ug/", "_name": "Uganda Post", "_name_zh-cn": "Uganda Post", "_name_zh-hk": "Uganda Post", "_group": ""}, {"key": 100050, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.ukmail.com/", "_name": "UK Mail", "_name_zh-cn": "UK Mail", "_name_zh-hk": "UK Mail", "_group": "11"}, {"key": 21021, "_country": 2102, "_country_iso": "UA", "_email": "", "_tel": "", "_url": "http://ukrposhta.ua/", "_name": "Uk<PERSON>osh<PERSON>", "_name_zh-cn": "Uk<PERSON>osh<PERSON>", "_name_zh-hk": "Uk<PERSON>osh<PERSON>", "_group": ""}, {"key": 100426, "_country": 304, "_country_iso": "CA", "_email": "<EMAIL>", "_tel": "******-358-2581", "_url": "https://ulala.ca/", "_name": "ULALA", "_name_zh-cn": "ULALA", "_name_zh-hk": "ULALA", "_group": "11"}, {"key": 191296, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 13480811810", "_url": "https://www.unihom.cn/", "_name": "UniHom", "_name_zh-cn": "壹家物流", "_name_zh-hk": "壹家物流", "_group": "12"}, {"key": 190997, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.uniment.net/", "_name": "Uniment", "_name_zh-cn": "易盟", "_name_zh-hk": "易盟", "_group": "12"}, {"key": 100839, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-858-9889", "_url": "https://www.unisco.com/", "_name": "Unis", "_name_zh-cn": "Unis", "_name_zh-hk": "Unis", "_group": "11"}, {"key": 100217, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.uniteddeliveryservice.com/", "_name": "United Delivery Service", "_name_zh-cn": "United Delivery Service", "_name_zh-hk": "United Delivery Service", "_group": "11"}, {"key": 100134, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "**************", "_url": "https://uniuni.com/", "_name": "UniUni", "_name_zh-cn": "UniUni", "_name_zh-hk": "UniUni", "_group": "11"}, {"key": 100788, "_country": 801, "_country_iso": "HK", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.un-line.com/", "_name": "Un-line（Global Un-line Express）", "_name_zh-cn": "Un-line（Global Un-line Express）", "_name_zh-hk": "Un-line（Global Un-line Express）", "_group": "11"}, {"key": 191360, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.uofexp.com/", "_name": "UOF International logistics", "_name_zh-cn": "骏佳国际物流", "_name_zh-hk": "駿佳國際物流", "_group": "12"}, {"key": 100002, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "****************", "_url": "https://www.ups.com/", "_name": "UPS", "_name_zh-cn": "UPS", "_name_zh-hk": "UPS", "_group": "11"}, {"key": 100398, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+1 （800） 500-2224", "_url": "https://www.upsmailinnovations.com/", "_name": "UPS Mail Innovations", "_name_zh-cn": "UPS Mail Innovations", "_name_zh-hk": "UPS Mail Innovations", "_group": "11"}, {"key": 100475, "_country": 1925, "_country_iso": "CH", "_email": "", "_tel": "", "_url": "http://globaltracktrace.ptc.post/gtt.web/", "_name": "UPU", "_name_zh-cn": "UPU", "_name_zh-hk": "UPU", "_group": "11"}, {"key": 190930, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.uralsvip.com/", "_name": "URAL", "_name_zh-cn": "优拉", "_name_zh-hk": "優拉", "_group": "12"}, {"key": 100252, "_country": 1913, "_country_iso": "SG", "_email": "", "_tel": "+65 6476 7472", "_url": "https://www.urbanfox.asia/", "_name": "UrbanFox", "_name_zh-cn": "UrbanFox", "_name_zh-hk": "UrbanFox", "_group": "11"}, {"key": 100293, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "+34 914554088 ", "_url": "https://www.urvaam.es/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190122, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.uskyexpress.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "12"}, {"key": 190092, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.u-speedex.com/", "_name": "U-Speed", "_name_zh-cn": "优时派", "_name_zh-hk": "優時派", "_group": "12"}, {"key": 21051, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+****************", "_url": "https://www.usps.com/", "_name": "USPS", "_name_zh-cn": "USPS", "_name_zh-hk": "USPS", "_group": ""}, {"key": 190797, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.shysbj.com/", "_name": "UstEx", "_name_zh-cn": "UstEx", "_name_zh-hk": "UstEx", "_group": "12"}, {"key": 190109, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.utec.info/", "_name": "UTEC", "_name_zh-cn": "UTEC", "_name_zh-hk": "UTEC", "_group": "12"}, {"key": 190966, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "", "_url": " http://www.utscchina.com/   ", "_name": "Uton", "_name_zh-cn": "优通国际", "_name_zh-hk": "優通國際", "_group": "12"}, {"key": 190238, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.uvan56.com/", "_name": "UVAN", "_name_zh-cn": "UVAN", "_name_zh-hk": "UVAN", "_group": "12"}, {"key": 190248, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ux-exp.com/", "_name": "UXTX", "_name_zh-cn": "邮行天下", "_name_zh-hk": "郵行天下", "_group": "12"}, {"key": 21031, "_country": 2103, "_country_iso": "UZ", "_email": "", "_tel": "", "_url": "http://www.pochta.uz/", "_name": "Uzbekistan Post", "_name_zh-cn": "Uzbekistan Post", "_name_zh-hk": "Uzbekistan Post", "_group": ""}, {"key": 100442, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "", "_url": "https://www.valleytms.com/", "_name": "Valley TMS", "_name_zh-cn": "Valley TMS", "_name_zh-hk": "Valley TMS", "_group": "11"}, {"key": 100403, "_country": 902, "_country_iso": "IN", "_email": "", "_tel": "+91-22-48934295", "_url": "https://www.vamaship.com/", "_name": "Vamaship", "_name_zh-cn": "Vamaship", "_name_zh-hk": "Vamaship", "_group": "11"}, {"key": 100330, "_country": 9001, "_country_iso": "", "_email": "", "_tel": "", "_url": "https://tracking.vamox.io/", "_name": "Vamox", "_name_zh-cn": "Vamox", "_name_zh-hk": "Vamox", "_group": "11"}, {"key": 191192, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "+86 13657012618", "_url": "https://vanjexpress.com/", "_name": "VANJ Express", "_name_zh-cn": "万加国际", "_name_zh-hk": "萬加國際", "_group": "12"}, {"key": 22021, "_country": 2202, "_country_iso": "VU", "_email": "", "_tel": "", "_url": "http://www.vanuatupost.vu/", "_name": "Vanuatu Post", "_name_zh-cn": "Vanuatu Post", "_name_zh-hk": "Vanuatu Post", "_group": ""}, {"key": 100363, "_country": 1610, "_country_iso": "PT", "_email": "", "_tel": "", "_url": "https://www.vaspexpresso.pt/", "_name": "Vasp Expresso", "_name_zh-cn": "Vasp Expresso", "_name_zh-hk": "Vasp Expresso", "_group": "11"}, {"key": 100218, "_country": 1208, "_country_iso": "LT", "_email": "", "_tel": "", "_url": "https://venipak.lt/en/", "_name": "Venipak", "_name_zh-cn": "Venipak", "_name_zh-hk": "Venipak", "_group": "11"}, {"key": 190518, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.verykship.com/", "_name": "VerykShip", "_name_zh-cn": "唯客物流", "_name_zh-hk": "唯客物流", "_group": "12"}, {"key": 100136, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://vestovoy.ru/", "_name": "Vestovoy", "_name_zh-cn": "Vestovoy", "_name_zh-hk": "Vestovoy", "_group": "11"}, {"key": 191258, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 15397119097", "_url": "https://www.vistaglobal.group/", "_name": "VGL", "_name_zh-cn": "惟达物流", "_name_zh-hk": "惟達物流", "_group": "12"}, {"key": 100535, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "", "_url": "https://viacargo.com.ar/", "_name": "Via Cargo", "_name_zh-cn": "Via Cargo", "_name_zh-hk": "Via Cargo", "_group": "11"}, {"key": 100950, "_country": 1918, "_country_iso": "ES", "_email": "<EMAIL>", "_tel": "+34-902 307 907", "_url": "https://www.viaxpress.es/", "_name": "ViaXpress ", "_name_zh-cn": "ViaXpress ", "_name_zh-hk": "ViaXpress ", "_group": "11"}, {"key": 22043, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "", "_url": "https://ems.com.vn/", "_name": "VietNam EMS", "_name_zh-cn": "VietNam EMS", "_name_zh-hk": "VietNam EMS", "_group": ""}, {"key": 22041, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "", "_url": "http://www.vnpost.vn/", "_name": "VietNam Post", "_name_zh-cn": "VietNam Post", "_name_zh-hk": "VietNam Post", "_group": ""}, {"key": 100611, "_country": 2204, "_country_iso": "VN", "_email": "", "_tel": "+84 ************", "_url": "https://viettelpost.com.vn/", "_name": "Viettel Post", "_name_zh-cn": "Viettel Post", "_name_zh-hk": "Viettel Post", "_group": "11"}, {"key": 101020, "_country": 605, "_country_iso": "FR", "_email": "", "_tel": "", "_url": "https://vintedgo.com/", "_name": "Vinted Go", "_name_zh-cn": "Vinted Go", "_name_zh-hk": "Vinted Go", "_group": "11"}, {"key": 191369, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "86-021-57680127", "_url": "http://www.vimana.world/", "_name": "VMN EXPRESS", "_name_zh-cn": "维马纳国际物流", "_name_zh-hk": "維馬納國際物流", "_group": "12"}, {"key": 190432, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.winlinklogistics.com/", "_name": "Vnlin", "_name_zh-cn": "合联国际", "_name_zh-hk": "合聯國際", "_group": "12"}, {"key": 100792, "_country": 1803, "_country_iso": "RU", "_email": "<EMAIL>", "_tel": "****** 705 97 79", "_url": "https://vozovoz.ru/", "_name": "Vozovoz", "_name_zh-cn": "Vozovoz", "_name_zh-hk": "Vozovoz", "_group": "11"}, {"key": 190796, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18028713730", "_url": "https://eparcel56.com/", "_name": "VYANG", "_name_zh-cn": "维央", "_name_zh-hk": "維央", "_group": "12"}, {"key": 101061, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.wlexpress.net/", "_name": "W&L Express Inc", "_name_zh-cn": "W&L Express Inc", "_name_zh-hk": "W&L Express Inc", "_group": "11"}, {"key": 100183, "_country": 903, "_country_iso": "ID", "_email": "", "_tel": "", "_url": "https://wahana.com/", "_name": "<PERSON><PERSON>a Prestasi Logistik", "_name_zh-cn": "<PERSON><PERSON>a Prestasi Logistik", "_name_zh-hk": "<PERSON><PERSON>a Prestasi Logistik", "_group": "11"}, {"key": 100580, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://walkers-transport.co.uk/", "_name": "Walkers Transport", "_name_zh-cn": "Walkers Transport", "_name_zh-hk": "Walkers Transport", "_group": "11"}, {"key": 190733, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-27788535", "_url": "http://www.wallabyglobal.com/", "_name": "WALLABY EXPRESS", "_name_zh-cn": "小袋鼠", "_name_zh-hk": "小袋鼠", "_group": "12"}, {"key": 190890, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.wyt-express.com/", "_name": "<PERSON>", "_name_zh-cn": "万运通", "_name_zh-hk": "萬運通", "_group": "12"}, {"key": 190086, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.wanbexpress.com/", "_name": "Wanb Express", "_name_zh-cn": "Wanb Express", "_name_zh-hk": "Wanb Express", "_group": "12"}, {"key": 191118, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.wanyuexpress.cn/", "_name": "<PERSON><PERSON> ", "_name_zh-cn": "万宇运通", "_name_zh-hk": "萬宇運通", "_group": "12"}, {"key": 100920, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.wardtlc.com/", "_name": "Ward Transport & Logistics Corp", "_name_zh-cn": "Ward Transport & Logistics Corp", "_name_zh-hk": "Ward Transport & Logistics Corp", "_group": "11"}, {"key": 100878, "_country": 1602, "_country_iso": "PS", "_email": "<EMAIL>", "_tel": "970-1700974444", "_url": "https://www.wassel.ps/", "_name": "<PERSON><PERSON> (واص<PERSON>)", "_name_zh-cn": "<PERSON><PERSON> (واص<PERSON>)", "_name_zh-hk": "<PERSON><PERSON> (واص<PERSON>)", "_group": "11"}, {"key": 191364, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-4008807600", "_url": "http://wuchangls.com/", "_name": "WC", "_name_zh-cn": "物畅物联", "_name_zh-hk": "物暢物聯", "_group": "12"}, {"key": 190800, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-057985916618", "_url": "http://www.wcxex.com/", "_name": "WCX", "_name_zh-cn": "伟成国际", "_name_zh-hk": "伟成国际", "_group": "12"}, {"key": 191085, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 4000024300", "_url": "http://www.wdexpmama.com/", "_name": "WDEXPMAMA", "_name_zh-cn": "委达国际", "_name_zh-hk": "委達國際", "_group": "12"}, {"key": 100355, "_country": 322, "_country_iso": "CZ", "_email": "", "_tel": "", "_url": "https://www.wedo.cz/", "_name": "WEDO", "_name_zh-cn": "WEDO", "_name_zh-hk": "WEDO", "_group": "11"}, {"key": 190684, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 826-66821556", "_url": "https://www.wedoexpress.com/", "_name": "WEDO", "_name_zh-cn": "运德", "_name_zh-hk": "運德", "_group": "12"}, {"key": 191345, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.weijie-56.com/", "_name": "WEIJIE LOGISTICS", "_name_zh-cn": "威捷国际物流", "_name_zh-hk": "威捷國際物流", "_group": "12"}, {"key": 191050, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18839950005", "_url": "http://www.weiku.com.cn/", "_name": "WEIKU", "_name_zh-cn": "维库国际", "_name_zh-hk": "維庫國際", "_group": "12"}, {"key": 190853, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0311-67695285", "_url": "http://www.weisagroup.com/", "_name": "WEISA", "_name_zh-cn": "威飒国际", "_name_zh-hk": "威颯國際", "_group": "12"}, {"key": 190578, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.wtkjwl.com/", "_name": "Weitu Technology Logistics", "_name_zh-cn": "维途科技物流", "_name_zh-hk": "維途科技物流", "_group": "12"}, {"key": 190170, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.we-logistics.com/", "_name": "WEL", "_name_zh-cn": "WEL", "_name_zh-hk": "WEL", "_group": "12"}, {"key": 100701, "_country": 1305, "_country_iso": "MY", "_email": "<EMAIL>", "_tel": "+60 ********", "_url": "https://www.wepost.com.my/", "_name": "WePost", "_name_zh-cn": "WePost", "_name_zh-hk": "WePost", "_group": "11"}, {"key": 100113, "_country": 0, "_country_iso": "", "_email": "", "_tel": "+****************", "_url": "http://westbankcourier.com/", "_name": "West Bank", "_name_zh-cn": "West Bank", "_name_zh-hk": "West Bank", "_group": "11"}, {"key": 191216, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 021-********", "_url": "http://www.westocean.net/", "_name": "West Ocean", "_name_zh-cn": "上海嘉驰", "_name_zh-hk": "上海嘉馳", "_group": "12"}, {"key": 190675, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.westlinkintl.com/", "_name": "WESTLINK", "_name_zh-cn": "WESTLINK", "_name_zh-hk": "WESTLINK", "_group": "12"}, {"key": 191241, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 ***********", "_url": "http://weitongfba.com/", "_name": "Weton Logistics", "_name_zh-cn": " 纬通物流科技 ", "_name_zh-hk": "緯通物流科技", "_group": "12"}, {"key": 190998, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 ***********", "_url": "https://www.wexsu.com/", "_name": "WEXSU", "_name_zh-cn": "位像素", "_name_zh-hk": "位像素", "_group": "12"}, {"key": 191007, "_country": 801, "_country_iso": "HK", "_email": "", "_tel": "", "_url": " https://www.whatsship.com/", "_name": "Whats Ship", "_name_zh-cn": "Whats Ship", "_name_zh-hk": "Whats Ship", "_group": "12"}, {"key": 190784, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.wherexpress.com/", "_name": "WHER EXPRESS", "_name_zh-cn": "威盛快递", "_name_zh-hk": "威盛快遞", "_group": "12"}, {"key": 100159, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://trackmyitem.whistl.co.uk/", "_name": "Whistl", "_name_zh-cn": "Whistl", "_name_zh-hk": "Whistl", "_group": "11"}, {"key": 190102, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.whtexpress.com/", "_name": "WHT", "_name_zh-cn": "五和通", "_name_zh-hk": "五和通", "_group": "12"}, {"key": 191222, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "", "_url": "http://www.exwchina.cn/", "_name": "WHWDT", "_name_zh-cn": "武汉沃德通", "_name_zh-hk": "武漢沃德通", "_group": "12"}, {"key": 190615, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.wia-sourcing.com/", "_name": "WIA Fulfill", "_name_zh-cn": "维亚智通", "_name_zh-hk": "維亞智通", "_group": "12"}, {"key": 190336, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hzpdex.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "蓬达", "_name_zh-hk": "PD", "_group": "12"}, {"key": 191223, "_country": 301, "_country_iso": "CN", "_email": "lijing<PERSON>@willcang.com,<EMAIL>", "_tel": "", "_url": "https://www.willcang.com/", "_name": "Willcang", "_name_zh-cn": "未来云仓", "_name_zh-hk": "未來雲倉", "_group": "12"}, {"key": 100100, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "", "_url": "https://www.wing.ae/", "_name": "Wing", "_name_zh-cn": "Wing", "_name_zh-hk": "Wing", "_group": "11"}, {"key": 190284, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.winit.com.cn/", "_name": "WINIT", "_name_zh-cn": "万邑通", "_name_zh-hk": "萬邑通", "_group": "12"}, {"key": 190085, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.shwise.cn/", "_name": "Wise Express", "_name_zh-cn": "万色速递", "_name_zh-hk": "萬色速遞", "_group": "12"}, {"key": 100895, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.wiseloads.com/", "_name": "Wiseloads", "_name_zh-cn": "Wiseloads", "_name_zh-hk": "Wiseloads", "_group": "11"}, {"key": 191001, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.wiselution.com/ ", "_name": "Wiselution", "_name_zh-cn": "智方物流科技", "_name_zh-hk": "智方物流科技", "_group": "12"}, {"key": 190165, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.wishpost.cn/", "_name": "Wishpost", "_name_zh-cn": "Wish邮", "_name_zh-hk": "Wish郵", "_group": "12"}, {"key": 100490, "_country": 304, "_country_iso": "CA", "_email": "", "_tel": "+1 （844） 469-4966", "_url": "https://shipwizmo.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190440, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://wld-exp.com/", "_name": "WLD Express", "_name_zh-cn": "万立德国际", "_name_zh-hk": "萬立德國際", "_group": "12"}, {"key": 190403, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.wmycc.com/", "_name": "WM", "_name_zh-cn": "万盟云仓", "_name_zh-hk": "萬盟雲倉", "_group": "10"}, {"key": 100361, "_country": 704, "_country_iso": "DE", "_email": "", "_tel": "+44 1753 561262", "_url": "https://wndirect.com/", "_name": "wnDirect", "_name_zh-cn": "wnDirect", "_name_zh-hk": "wnDirect", "_group": "11"}, {"key": 191271, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://wokexp.cn/", "_name": "Wokexp", "_name_zh-cn": "威龙永凯", "_name_zh-hk": "威龍永凱", "_group": "12"}, {"key": 100965, "_country": 1105, "_country_iso": "KR", "_email": "by@banyan Logistics.com", "_tel": "+82 2-6956-6980", "_url": "https://www.banyanlogistics.com/", "_name": "Woojin Interlogis", "_name_zh-cn": "Woojin Interlogis", "_name_zh-hk": "Woojin Interlogis", "_group": "11"}, {"key": 191172, "_country": 301, "_country_iso": "CN", "_email": "xie<PERSON><PERSON>@jit-expressgroup.com", "_tel": "+86-21-60170788", "_url": "http://www.wooolink.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "上海领链", "_name_zh-hk": "上海領鏈", "_group": "12"}, {"key": 190511, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.worldwide-logistics.cn/", "_name": "worldwide logistics", "_name_zh-cn": "环世物流集团", "_name_zh-hk": "環世物流集團", "_group": "12"}, {"key": 190100, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gdwse.com/", "_name": "WSE Logistics", "_name_zh-cn": "威速易物流", "_name_zh-hk": "威速易物流", "_group": "12"}, {"key": 190178, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.360lion.com/", "_name": "WSH", "_name_zh-cn": "WSH", "_name_zh-hk": "WSH", "_group": "12"}, {"key": 190581, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.wst.group/", "_name": "WST", "_name_zh-cn": "万顺通", "_name_zh-hk": "萬順通", "_group": "12"}, {"key": 190313, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.weworldexpress.com/", "_name": "WWE", "_name_zh-cn": "WWE", "_name_zh-hk": "WWE", "_group": "12"}, {"key": 191082, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://wxy-express.com/", "_name": "WXYEXPRESS", "_name_zh-cn": "万鑫驿国际", "_name_zh-hk": "萬鑫驛國際", "_group": "12"}, {"key": 190693, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.wynn-express.com/", "_name": "Wynn Express", "_name_zh-cn": "永利运通", "_name_zh-hk": "永利運通", "_group": "12"}, {"key": 191176, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18938072363", "_url": "http://www.xbhgj56.com/  ", "_name": "XBHGJ", "_name_zh-cn": "鑫佰航国际", "_name_zh-hk": "鑫佰航國際", "_group": "12"}, {"key": 190751, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-4008086156", "_url": "http://www.xcexp.cn/", "_name": "XC international logistics", "_name_zh-cn": "湘诚国际物流", "_name_zh-hk": "湘诚国际物流", "_group": "12"}, {"key": 101007, "_country": 313, "_country_iso": "CO", "_email": "<EMAIL>", "_tel": "+57 316 1987694", "_url": "https://x-cargo.co/", "_name": "Xcargo", "_name_zh-cn": "Xcargo", "_name_zh-hk": "Xcargo", "_group": "11"}, {"key": 100835, "_country": 1607, "_country_iso": "PH", "_email": "<EMAIL>", "_tel": "+63-27-9596742 ", "_url": "https://www.xde.com.ph/", "_name": "XDE Logistics", "_name_zh-cn": "XDE Logistics", "_name_zh-hk": "XDE Logistics", "_group": "11"}, {"key": 100167, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "", "_url": "https://www.xdp.co.uk/express/", "_name": "XDP EXPRESS", "_name_zh-cn": "XDP EXPRESS", "_name_zh-hk": "XDP EXPRESS", "_group": "11"}, {"key": 191265, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13926809703", "_url": "https://www.xdyex.com/", "_name": "XDY", "_name_zh-cn": "行得远", "_name_zh-hk": "行得遠", "_group": "12"}, {"key": 190868, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-88886666 ", "_url": "https://shipeag.hk/", "_name": "X-Eagle", "_name_zh-cn": "老鹰跨境", "_name_zh-hk": "老鷹跨境", "_group": "12"}, {"key": 190633, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gzxhgj.com/", "_name": "XH", "_name_zh-cn": "XH", "_name_zh-hk": "XH", "_group": "12"}, {"key": 190907, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-28456331 ", "_url": "http://www.szxhy.hailei2018.com/ ", "_name": "XHY", "_name_zh-cn": "星鸿运", "_name_zh-hk": "星鴻運", "_group": "12"}, {"key": 100991, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.xindus.net/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190554, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xingsuexp.com/", "_name": "XINGSU", "_name_zh-cn": "星速货运", "_name_zh-hk": "星速貨運", "_group": "12"}, {"key": 190608, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+65 83477118", "_url": "https://www.xingtongjiyun.com/", "_name": "XingTongGlobal", "_name_zh-cn": "星通集运", "_name_zh-hk": "星通集運", "_group": "12"}, {"key": 190081, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://index.xyexp.com/", "_name": "XINGYUAN", "_name_zh-cn": "XINGYUAN", "_name_zh-hk": "XINGYUAN", "_group": "12"}, {"key": 191325, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://xinhongwuliu.cn/", "_name": "XinHong", "_name_zh-cn": "信弘国际", "_name_zh-hk": "信弘國際", "_group": "12"}, {"key": 190532, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xmtgyl.com/", "_name": "XinMaTai", "_name_zh-cn": "新马泰", "_name_zh-hk": "新馬泰", "_group": "12"}, {"key": 191306, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": " http://www.xmgjwl56.com/", "_name": "XinMiao Logistics", "_name_zh-cn": "鑫淼物流", "_name_zh-hk": "鑫淼物流", "_group": "12"}, {"key": 191012, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 (00852), +65568735   ", "_url": "https://www.xinransupply.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "欣染", "_name_zh-hk": "欣染", "_group": "12"}, {"key": 190604, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xin-logistics.cn/", "_name": "Xinyan international", "_name_zh-cn": "心妍物流", "_name_zh-hk": "心妍物流", "_group": "12"}, {"key": 191144, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://jiuay.com/", "_name": "XIONGBUY", "_name_zh-cn": "玖安怡", "_name_zh-hk": "玖安怡", "_group": "12"}, {"key": 191351, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-17621316622", "_url": "http://www.xiuexpress.cn/", "_name": "Xiuyuantianxia", "_name_zh-cn": "修远天下", "_name_zh-hk": "修遠天下", "_group": "12"}, {"key": 190919, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.xjd-exp.cn/", "_name": "XJD", "_name_zh-cn": "鑫佳达", "_name_zh-hk": "鑫佳達", "_group": "12"}, {"key": 190629, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xiongjiu168.com/", "_name": "XJWL", "_name_zh-cn": "雄九物流", "_name_zh-hk": "雄九物流", "_group": "12"}, {"key": 191254, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 0755-23094863-8064", "_url": "http://www.sz-xkp.com/", "_name": "XKP", "_name_zh-cn": "鑫鲲鹏物流", "_name_zh-hk": "鑫鯤鵬物流", "_group": "12"}, {"key": 100939, "_country": 115, "_country_iso": "AU", "_email": "", "_tel": "", "_url": "https://xlexpress.com.au/", "_name": "XL EXPRESS", "_name_zh-cn": "XL EXPRESS", "_name_zh-hk": "XL EXPRESS", "_group": "11"}, {"key": 101005, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "+44 07476947927", "_url": "https://www.xlcourier.com/", "_name": "XLCourier", "_name_zh-cn": "XLCourier", "_name_zh-hk": "XLCourier", "_group": "11"}, {"key": 100731, "_country": 1103, "_country_iso": "GB", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.xpertdelivery.co.uk/", "_name": "XPERT Delivery", "_name_zh-cn": "XPERT Delivery", "_name_zh-hk": "XPERT Delivery", "_group": "11"}, {"key": 100254, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "+****************", "_url": "https://www.xpo.com/", "_name": "XPO", "_name_zh-cn": "XPO", "_name_zh-hk": "XPO", "_group": "11"}, {"key": 100101, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 020 46608 105", "_url": "http://www.xpressbees.com/", "_name": "Xpressbees", "_name_zh-cn": "Xpressbees", "_name_zh-hk": "Xpressbees", "_group": "11"}, {"key": 190038, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xqkjwl.com/", "_name": "XQE", "_name_zh-cn": "星前物流", "_name_zh-hk": "星前物流", "_group": "12"}, {"key": 190477, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://xs-exp.com/", "_name": "XS Express", "_name_zh-cn": "新树物流", "_name_zh-hk": "新樹物流", "_group": "12"}, {"key": 190893, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 13164711624", "_url": "http://xslong.net/", "_name": "xslong", "_name_zh-cn": "兴胜隆", "_name_zh-hk": "興盛隆", "_group": "12"}, {"key": 190549, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xtl163.com/", "_name": "XT", "_name_zh-cn": "迅田国际", "_name_zh-hk": "迅田國際", "_group": "12"}, {"key": 191346, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://xtgjwl168.com/", "_name": "XTGJ", "_name_zh-cn": "心同国际", "_name_zh-hk": "心同國際", "_group": "12"}, {"key": 190630, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ct-scm.com/", "_name": "XTOOLS", "_name_zh-cn": "超兔供应链", "_name_zh-hk": "超兔供應鏈", "_group": "12"}, {"key": 191248, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>  ", "_tel": "", "_url": "http://www.szxuanchen.com/", "_name": "XUANCHEN", "_name_zh-cn": "轩宸国际", "_name_zh-hk": "軒宸國際", "_group": "12"}, {"key": 191281, "_country": 301, "_country_iso": "CN", "_email": "me<PERSON><EMAIL>", "_tel": "+86 020-86658801", "_url": "http://www.xuansiexpress.com/", "_name": "XUANSI ", "_name_zh-cn": "炫思速运", "_name_zh-hk": "炫思速運", "_group": "12"}, {"key": 191212, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-19007561915", "_url": "http://www.xuexpress.com/", "_name": "XuExpress", "_name_zh-cn": "新邮国际", "_name_zh-hk": "新郵國際", "_group": "12"}, {"key": 190671, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xunhefba.com/", "_name": "XUNHE", "_name_zh-cn": "讯合国际", "_name_zh-hk": "訊合國際", "_group": "12"}, {"key": 190406, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xxdexp.com/", "_name": "XXD", "_name_zh-cn": "上海享享达", "_name_zh-hk": "上海享享達", "_group": "10"}, {"key": 191300, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18058901800", "_url": "http://xingyou-guoji.com/", "_name": "XYEX", "_name_zh-cn": "星邮国际", "_name_zh-hk": "星郵國際", "_group": "12"}, {"key": 190717, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-0755-29231081", "_url": "http://www.xyexp.com/", "_name": "xyexp", "_name_zh-cn": "兴源供应链", "_name_zh-hk": "兴源供应链", "_group": "12"}, {"key": 191035, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 13760443712", "_url": "http://www.xyhwl.cn/", "_name": "XYH", "_name_zh-cn": "信永恒物流", "_name_zh-hk": "信永恆物流", "_group": "12"}, {"key": 190167, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.816kf.com/", "_name": "XYL", "_name_zh-cn": "兴远国际", "_name_zh-hk": "興遠國際", "_group": "12"}, {"key": 191036, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://szxypost.com/ ", "_name": "xypost", "_name_zh-cn": "贤邮供应链", "_name_zh-hk": "賢郵供應鏈", "_group": "12"}, {"key": 191072, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://xyslogistics.com/", "_name": "XYS", "_name_zh-cn": "XYS", "_name_zh-hk": "XYS", "_group": "12"}, {"key": 190701, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13426536761", "_url": "http://www.szxytkj.com/", "_name": "XYTKJ", "_name_zh-cn": "鑫运通跨境", "_name_zh-hk": "鑫運通跨境", "_group": "12"}, {"key": 190340, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.xingyunyi.cn/", "_name": "XYY", "_name_zh-cn": "XYY", "_name_zh-hk": "XYY", "_group": "12"}, {"key": 191374, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://xzd.coenboth.com/", "_name": "XZD", "_name_zh-cn": "小楂达", "_name_zh-hk": "小楂達", "_group": "12"}, {"key": 191109, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.xzx2021-express.com.cn/", "_name": "XZX", "_name_zh-cn": "象之形国际物流", "_name_zh-hk": "象之形國際物流", "_group": "12"}, {"key": 100062, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "", "_url": "http://www.kuronekoyamato.co.jp/", "_name": "<PERSON><PERSON><PERSON> (ヤマト運輸)", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100722, "_country": 801, "_country_iso": "HK", "_email": "<EMAIL>", "_tel": "+852 2829-2222", "_url": "https://www.yamatohk.com.hk/", "_name": "Yamato Logistics (HK)", "_name_zh-cn": "雅玛多运输（香港）", "_name_zh-hk": "雅瑪多運輸（香港）", "_group": "11"}, {"key": 100924, "_country": 1913, "_country_iso": "SG", "_email": "<EMAIL>", "_tel": "+65-1800 225 5888", "_url": "https://www.yamatosingapore.com/", "_name": "Yamato Transport Singapore", "_name_zh-cn": "Yamato Transport Singapore", "_name_zh-hk": "Yamato Transport Singapore", "_group": "11"}, {"key": 190012, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yw56.com.cn/", "_name": "YANWEN", "_name_zh-cn": "燕文物流", "_name_zh-hk": "燕文物流", "_group": "12"}, {"key": 190719, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "************", "_url": "https://yaohongguoji.com/", "_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "耀鸿国际", "_name_zh-hk": "耀鴻國際", "_group": "12"}, {"key": 191044, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 021-59770991", "_url": "http://www.yaresea.com/", "_name": "YARESEA", "_name_zh-cn": "豫海易境", "_name_zh-hk": "豫海易境", "_group": "12"}, {"key": 190496, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ybdexpress.com/", "_name": "YBD", "_name_zh-cn": "邮必达", "_name_zh-hk": "郵必達", "_group": "12"}, {"key": 190682, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-85252985", "_url": "http://yc-express.com.cn/", "_name": "YC LOGISTICS", "_name_zh-cn": "煜诚国际", "_name_zh-hk": "煜誠國際", "_group": "12"}, {"key": 190801, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ycex-cn.com/", "_name": "YCGJ", "_name_zh-cn": "永财国际", "_name_zh-hk": "永财国际", "_group": "12"}, {"key": 100892, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+886-(06)242-0206", "_url": "http://www.ycs-express.com/", "_name": "YCS Logistics", "_name_zh-cn": "音速国际物流", "_name_zh-hk": "音速國際物流", "_group": "11"}, {"key": 191292, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://gaugnzhouyingda.com/", "_name": "YD", "_name_zh-cn": "应达物流", "_name_zh-hk": "應達物流", "_group": "12"}, {"key": 190794, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+8615537466737 ", "_url": "http://www.ydcn666.com/", "_name": "YD LOGISTICS", "_name_zh-cn": "YD LOGISTICS", "_name_zh-hk": "YD LOGISTICS", "_group": "12"}, {"key": 190251, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yudiexp.com/", "_name": "YDGJ", "_name_zh-cn": "杭州宇地", "_name_zh-hk": "杭州宇地", "_group": "12"}, {"key": 190200, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ydhex.com/", "_name": "YDH", "_name_zh-cn": "YDH", "_name_zh-hk": "YDH", "_group": "12"}, {"key": 191186, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "+86 0755-21011561  ", "_url": "http://www.fba11.com/", "_name": "YDKJ", "_name_zh-cn": "易达跨境", "_name_zh-hk": "易達跨境", "_group": "12"}, {"key": 190529, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ydm-express.com/", "_name": "YDM", "_name_zh-cn": "壹斗米", "_name_zh-hk": "壹斗米", "_group": "12"}, {"key": 190243, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ydexp.com/", "_name": "YDS", "_name_zh-cn": "YDS", "_name_zh-hk": "YDS", "_group": "12"}, {"key": 190732, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-17317402439", "_url": "http://www.ydt-express.com/", "_name": "YDT", "_name_zh-cn": "易达通", "_name_zh-hk": "易達通", "_group": "12"}, {"key": 190531, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hopailogtech.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "易链供应链", "_name_zh-hk": "易鏈供應鏈", "_group": "12"}, {"key": 25011, "_country": 2501, "_country_iso": "YE", "_email": "", "_tel": "", "_url": "http://www.post.ye/", "_name": "Yemen Post", "_name_zh-cn": "Yemen Post", "_name_zh-hk": "Yemen Post", "_group": ""}, {"key": 190813, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yingshunexpress.com/", "_name": "YES", "_name_zh-cn": "YES", "_name_zh-hk": "YES", "_group": "12"}, {"key": 100711, "_country": 902, "_country_iso": "IN", "_email": "<EMAIL>", "_tel": "+91 9758866337", "_url": "http://www.yescourier.in/", "_name": "Yes Courier", "_name_zh-cn": "Yes Courier", "_name_zh-hk": "Yes Courier", "_group": "11"}, {"key": 190673, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yes-speed.com/", "_name": "Yes-speed", "_name_zh-cn": "元速航空", "_name_zh-hk": "元速航空", "_group": "12"}, {"key": 191240, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.yf-exprss.com/", "_name": "YF", "_name_zh-cn": "亿飞国际", "_name_zh-hk": "億飛國際", "_group": "12"}, {"key": 100439, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "******-642-6762", "_url": "https://www.yflogisticsllc.com/", "_name": "YF Logistics", "_name_zh-cn": "YF Logistics", "_name_zh-hk": "YF Logistics", "_group": "11"}, {"key": 190593, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.cargo518.com/", "_name": "YF518", "_name_zh-cn": "袁航电子", "_name_zh-hk": "袁航電子", "_group": "12"}, {"key": 190389, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yfhex.com/", "_name": "YFH", "_name_zh-cn": "原飞航", "_name_zh-hk": "原飛航", "_group": "12"}, {"key": 190410, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://htkjwl.cn/", "_name": "YFHT", "_name_zh-cn": "YFHT", "_name_zh-hk": "YFHT", "_group": "12"}, {"key": 190394, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.fmgjhy.com/", "_name": "YFM", "_name_zh-cn": "YFM", "_name_zh-hk": "YFM", "_group": "10"}, {"key": 190296, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yhe56.com/", "_name": "YHA", "_name_zh-cn": "YHA", "_name_zh-hk": "YHA", "_group": "10"}, {"key": 190114, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.eshippinggateway.com/", "_name": "YHT", "_name_zh-cn": "YHT", "_name_zh-hk": "YHT", "_group": "12"}, {"key": 190772, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-17620017089", "_url": "http://www.yhtcargo.com/", "_name": "YHT", "_name_zh-cn": "运货通", "_name_zh-hk": "運貨通", "_group": "12"}, {"key": 190614, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yunyi.world/", "_name": "YI", "_name_zh-cn": "韵翼", "_name_zh-hk": "韻翼", "_group": "12"}, {"key": 191329, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.100-ex.com/", "_name": "YIBAI", "_name_zh-cn": "易佰", "_name_zh-hk": "易佰", "_group": "12"}, {"key": 190825, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.styida.com.cn/", "_name": "YIDA", "_name_zh-cn": "宜达物流", "_name_zh-hk": "宜達物流", "_group": "12"}, {"key": 190292, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ydex.cn/", "_name": "YIDST", "_name_zh-cn": "YIDST", "_name_zh-hk": "YIDST", "_group": "12"}, {"key": 190459, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.szyifan56.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "毅帆", "_name_zh-hk": "毅帆", "_group": "12"}, {"key": 190857, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gzyfgyl.com/ ", "_name": "YIFAN", "_name_zh-cn": "一帆供应链", "_name_zh-hk": "一帆供應鏈", "_group": "12"}, {"key": 191137, "_country": 301, "_country_iso": "CN", "_email": "nic2597758@com", "_tel": "", "_url": "http://www.yiwuyifan.com/", "_name": "<PERSON><PERSON> international", "_name_zh-cn": "翼梵国际", "_name_zh-hk": "翼梵國際", "_group": "12"}, {"key": 190856, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 17769717661 ", "_url": "http://www.yfji.net/", "_name": "Yifeng", "_name_zh-cn": "一丰集运", "_name_zh-hk": "一豐集運", "_group": "12"}, {"key": 190957, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18898580199", "_url": "http://itepi.top/ ", "_name": "YIMI", "_name_zh-cn": "依米", "_name_zh-hk": "依米", "_group": "12"}, {"key": 190441, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.yimidida.com/", "_name": "YiMiDiDa", "_name_zh-cn": "壹米滴答", "_name_zh-hk": "壹米滴答", "_group": "12"}, {"key": 190972, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://cydwarehouse.com/", "_name": "Ying<PERSON><PERSON>", "_name_zh-cn": "应得物流", "_name_zh-hk": "應得物流", "_group": "12"}, {"key": 191327, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.yinghuiguoji.com/", "_name": "<PERSON><PERSON> ", "_name_zh-cn": "英荟国际", "_name_zh-hk": "英薈國際", "_group": "12"}, {"key": 191231, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://yitongda.cc/", "_name": "yitongda", "_name_zh-cn": "广州易通达", "_name_zh-hk": "廣州易通達", "_group": "12"}, {"key": 191091, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 17776272395", "_url": "http://www.ywhygj.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "华跃国际", "_name_zh-hk": "華躍國際", "_group": "12"}, {"key": 191094, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ywjd88.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "义乌杰达国际", "_name_zh-hk": "義烏傑達國際", "_group": "12"}, {"key": 191020, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  13436903389 ", "_url": "http://www.ywleyi.com/", "_name": "<PERSON><PERSON> ", "_name_zh-cn": "乐奕国际", "_name_zh-hk": "樂奕國際", "_group": "12"}, {"key": 191282, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://ywpy.rtb56.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "义乌鹏远", "_name_zh-hk": "義烏鵬遠", "_group": "12"}, {"key": 191062, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18257928004", "_url": "http://yygjwlfba.com/", "_name": "YIYA", "_name_zh-cn": "义亚国际物流", "_name_zh-hk": "義亞國際物流", "_group": "12"}, {"key": 190669, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yjwl-express.com/", "_name": "YJ", "_name_zh-cn": "运匠物流", "_name_zh-hk": "運匠物流", "_group": "12"}, {"key": 190106, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yanjin-gj.com/", "_name": "YJI", "_name_zh-cn": "YJI", "_name_zh-hk": "YJI", "_group": "12"}, {"key": 190252, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ylexp.com/", "_name": "YL", "_name_zh-cn": "YL", "_name_zh-hk": "YL", "_group": "12"}, {"key": 190253, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 4008261599", "_url": "http://www.ckeex.com/", "_name": "YL", "_name_zh-cn": "希凯易", "_name_zh-hk": "希凱易", "_group": "12"}, {"key": 190636, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gdylwl.com/", "_name": "YLE", "_name_zh-cn": "亚龙速运", "_name_zh-hk": "亞龍速運", "_group": "12"}, {"key": 190943, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 020-36085415 ", "_url": "http://www.ylt-global.com/ ", "_name": "YLT", "_name_zh-cn": "亿俐缇", "_name_zh-hk": "億俐緹", "_group": "12"}, {"key": 191107, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 86535253", "_url": "http://www.ylxdar.com/", "_name": "YLXD", "_name_zh-cn": "易联鑫达", "_name_zh-hk": "易聯鑫達", "_group": "12"}, {"key": 190565, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://tms.ym-trans.com/", "_name": "YME", "_name_zh-cn": "运盟国际", "_name_zh-hk": "運盟國際", "_group": "12"}, {"key": 191263, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.yimei56.com/", "_name": "YMGJ", "_name_zh-cn": "一美国际", "_name_zh-hk": "一美國際", "_group": "12"}, {"key": 190920, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 18823300570", "_url": "http://www.ymszgyl.com/", "_name": "YMGYL", "_name_zh-cn": "忆蔓供应链", "_name_zh-hk": "億蔓供應鏈", "_group": "12"}, {"key": 190096, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.2ezi-ymy.com/", "_name": "YMY", "_name_zh-cn": "YMY", "_name_zh-hk": "YMY", "_group": "12"}, {"key": 191218, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.ynynkd.com/", "_name": "YNYN", "_name_zh-cn": "优昵优昵", "_name_zh-hk": "優暱優暱", "_group": "12"}, {"key": 100017, "_country": 1103, "_country_iso": "GB", "_email": "", "_tel": "+0344 755 0117", "_url": "https://www.yodel.net.cn/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON>", "_group": "11"}, {"key": 100084, "_country": 0, "_country_iso": "", "_email": "", "_tel": "", "_url": "http://yona-express.com/", "_name": "Yona Express", "_name_zh-cn": "Yona Express", "_name_zh-hk": "Yona Express", "_group": "11"}, {"key": 191160, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.yotech.cc/", "_name": "YOTECH", "_name_zh-cn": "有寄物流", "_name_zh-hk": "有寄物流", "_group": "12"}, {"key": 100653, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://unitrade.youtrack.info/", "_name": "YOU TRACK", "_name_zh-cn": "YOU TRACK", "_name_zh-hk": "YOU TRACK", "_group": "11"}, {"key": 190517, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.youdaguoji.com/", "_name": "YOUDA", "_name_zh-cn": "优达国际", "_name_zh-hk": "優達國際", "_group": "12"}, {"key": 190446, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.gzymyt.com/", "_name": "YouMeiYuTong", "_name_zh-cn": "优美宇通", "_name_zh-hk": "優美宇通", "_group": "12"}, {"key": 190724, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ytgj188cn.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "优泰", "_name_zh-hk": "優泰", "_group": "12"}, {"key": 190622, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://yygj.youyiex.com/", "_name": "youyiex", "_name_zh-cn": "邮易仓", "_name_zh-hk": "郵易倉", "_group": "12"}, {"key": 191146, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 10 84847953-6117", "_url": "http://www.yoybuy.cn/", "_name": "Yoybuy", "_name_zh-cn": "北京优贝", "_name_zh-hk": "北京優貝", "_group": "12"}, {"key": 191239, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.yuanpeng56.com/", "_name": "YP", "_name_zh-cn": "远朋物流", "_name_zh-hk": "遠朋物流", "_group": "12"}, {"key": 190912, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ypld56.cn/", "_name": "YPLD", "_name_zh-cn": "优品联达", "_name_zh-hk": "優品聯達", "_group": "12"}, {"key": 100441, "_country": 2105, "_country_iso": "US", "_email": "", "_tel": "+1 （800） 610-6500", "_url": "https://yrc.com/", "_name": "<PERSON><PERSON> Freight", "_name_zh-cn": "<PERSON><PERSON> Freight", "_name_zh-hk": "<PERSON><PERSON> Freight", "_group": "11"}, {"key": 190319, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ysdgj56.com/", "_name": "YSD", "_name_zh-cn": "YSD", "_name_zh-hk": "YSD", "_group": "12"}, {"key": 190798, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.ysdpost.com/", "_name": "YSDPOST", "_name_zh-cn": "优时达", "_name_zh-hk": "優時達", "_group": "12"}, {"key": 191010, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.gzcloudsun.com/", "_name": "YSGJ logistics", "_name_zh-cn": "云山国际物流", "_name_zh-hk": "雲山國際物流", "_group": "12"}, {"key": 191211, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.yw-ysgyl.com/", "_name": "YSGYL", "_name_zh-cn": "宜速供应链", "_name_zh-hk": "宜速供應鏈", "_group": "12"}, {"key": 191315, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.zogjhy.com/", "_name": "YTAD", "_name_zh-cn": "易通安达", "_name_zh-hk": "易通安達", "_group": "12"}, {"key": 191268, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 0755-83212256 ", "_url": "http://www.ytdtexp.com/", "_name": "YTDT", "_name_zh-cn": "云腾道通", "_name_zh-hk": "雲騰道通", "_group": "12"}, {"key": 190988, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 17346910090", "_url": "http://www.szyuantu.cn/", "_name": "YTKJ", "_name_zh-cn": "远途跨境", "_name_zh-hk": "遠途跨境", "_group": "12"}, {"key": 190157, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yto.net.cn/", "_name": "YTO Express", "_name_zh-cn": "圆通速递", "_name_zh-hk": "圓通速遞", "_group": "12"}, {"key": 191289, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.ycgjlog.com/", "_name": "YUANCHUAN", "_name_zh-cn": "远传国际", "_name_zh-hk": "遠傳國際", "_group": "12"}, {"key": 190212, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.mzlyuanhao.com/", "_name": "YuanHao Logistics", "_name_zh-cn": "YuanHao Logistics", "_name_zh-hk": "YuanHao Logistics", "_group": "12"}, {"key": 190886, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.yzk56.com", "_name": "Yuanzk", "_name_zh-cn": "源之康物流", "_name_zh-hk": "源之康物流", "_group": "12"}, {"key": 190736, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18813578121", "_url": "http://www.yuchi2021.com/", "_name": "Yuchi International", "_name_zh-cn": "雨驰国际", "_name_zh-hk": "雨馳國際", "_group": "12"}, {"key": 191291, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13600158287", "_url": "http://www.yueangj.cn/", "_name": "Yue'an Logistics", "_name_zh-cn": "粤安物流", "_name_zh-hk": "粵安物流", "_group": "12"}, {"key": 190771, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18200797377", "_url": "http://yuegejing.com/", "_name": "Yuegejing", "_name_zh-cn": "越个境", "_name_zh-hk": "越個境", "_group": "12"}, {"key": 190830, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-************", "_url": "http://www.yuetuexpress.com/", "_name": "Yuetu", "_name_zh-cn": "跃兔", "_name_zh-hk": "躍兔", "_group": "12"}, {"key": 190561, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yuex-exp.com/", "_name": "YUEX LOGISTICS", "_name_zh-cn": "悦翔物流", "_name_zh-hk": "悅翔物流", "_group": "12"}, {"key": 190741, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-17349796235", "_url": "http://www.yuhongexpress.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "豫虹国际", "_name_zh-hk": "豫虹國際", "_group": "12"}, {"key": 190683, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 0755-27909225", "_url": "https://www.yunant56.com/", "_name": "YUNANT", "_name_zh-cn": "云蚁", "_name_zh-hk": "雲蟻", "_group": "12"}, {"key": 191197, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 021-39296500", "_url": "https://www.yunda56.com/", "_name": "Yunda Express", "_name_zh-cn": "韵达快运", "_name_zh-hk": "韻達快捷", "_group": "12"}, {"key": 101063, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.yunda.asia/", "_name": "YundaExpress (Asia)", "_name_zh-cn": "YundaExpress (Asia)", "_name_zh-hk": "YundaExpress (Asia)", "_group": "11"}, {"key": 101045, "_country": 2001, "_country_iso": "TW", "_email": "", "_tel": "+886-(02)2506-0089", "_url": "http://www.yundaex.com.tw/index.aspx", "_name": "YundaExpress (TW)", "_name_zh-cn": "YundaExpress (TW)", "_name_zh-hk": "YundaExpress (TW)", "_group": "11"}, {"key": 190008, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.yunexpress.com/", "_name": "YunExpress", "_name_zh-cn": "云途物流", "_name_zh-hk": "雲途物流", "_group": "12"}, {"key": 190607, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.yunhuipost.com/", "_name": "YunHui Logistics", "_name_zh-cn": "云晖物流", "_name_zh-hk": "雲暉物流", "_group": "12"}, {"key": 190917, "_country": 301, "_country_iso": "CN", "_email": " <EMAIL>", "_tel": "+86 020-86214321 ", "_url": "http://japan-cargo.com", "_name": "<PERSON><PERSON><PERSON> ", "_name_zh-cn": "广州云间国际", "_name_zh-hk": "廣州雲間國際", "_group": "12"}, {"key": 190618, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yunqishipping.com/", "_name": "YUNQI", "_name_zh-cn": "云旗国际", "_name_zh-hk": "雲旗國際", "_group": "12"}, {"key": 191275, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 19808047031", "_url": " http://aucm6xdsy3yunmdu.hk.rtb56.com/", "_name": "<PERSON><PERSON>", "_name_zh-cn": "云起国际", "_name_zh-hk": "雲起國際", "_group": "12"}, {"key": 190563, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.yunsexpress.com/", "_name": "YunS", "_name_zh-cn": "远尚物流", "_name_zh-hk": "遠尚物流", "_group": "12"}, {"key": 190668, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.yunwuj.com/", "_name": "YUNWUJIE", "_name_zh-cn": "运无界", "_name_zh-hk": "運無界", "_group": "12"}, {"key": 191133, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 15013482003", "_url": "http://www.yunxikejiwuliu.com/", "_name": "Yunxi", "_name_zh-cn": "云玺科技物流", "_name_zh-hk": "雲璽科技物流", "_group": "12"}, {"key": 100348, "_country": 2011, "_country_iso": "TR", "_email": "", "_tel": "", "_url": "https://www.yurticikargo.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 100686, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "+81 (03)6703-8111", "_url": "https://www.yusen-logistics.com/", "_name": "Yusen Logistics", "_name_zh-cn": "Yusen Logistics", "_name_zh-hk": "Yusen Logistics", "_group": "11"}, {"key": 190861, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yun-post.com/", "_name": "YUU", "_name_zh-cn": "云邮", "_name_zh-hk": "雲郵", "_group": "12"}, {"key": 190986, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "+86 1592147600 ", "_url": "http://www.ywbzexpress.com/", "_name": "YWBZ", "_name_zh-cn": "百洲货运", "_name_zh-hk": "百洲貨運", "_group": "12"}, {"key": 191278, "_country": 2105, "_country_iso": "US", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.yanwenexpress.com/", "_name": "YWE", "_name_zh-cn": "YWE", "_name_zh-hk": "YWE", "_group": "12"}, {"key": 190411, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.youwei-china.com/", "_name": "YWGJ", "_name_zh-cn": "有为国际", "_name_zh-hk": "有為國際", "_group": "12"}, {"key": 190484, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yxilogistics.com/", "_name": "YXIL", "_name_zh-cn": "悦希物流", "_name_zh-hk": "悅希物流", "_group": "12"}, {"key": 190941, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://yy-gjwl.com/", "_name": "YY", "_name_zh-cn": "YY", "_name_zh-hk": "YY", "_group": "12"}, {"key": 190294, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yyexpress.com/", "_name": "YYEXPRESS", "_name_zh-cn": "YYEXPRESS", "_name_zh-hk": "YYEXPRESS", "_group": "12"}, {"key": 190640, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.yypostal.com/", "_name": "YYpost", "_name_zh-cn": "YYpost", "_name_zh-hk": "YYpost", "_group": "12"}, {"key": 190367, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.ydmex.com/", "_name": "YYT", "_name_zh-cn": "易邮通", "_name_zh-hk": "易郵通", "_group": "10"}, {"key": 190729, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.z1express.com/", "_name": "Z1Express", "_name_zh-cn": "宙一物流", "_name_zh-hk": "宙一物流", "_group": "12"}, {"key": 101033, "_country": 503, "_country_iso": "AE", "_email": "", "_tel": "+971-80021111", "_url": "https://www.zajel.com/", "_name": "ZAJEL", "_name_zh-cn": "ZAJEL", "_name_zh-hk": "ZAJEL", "_group": "11"}, {"key": 100082, "_country": 1907, "_country_iso": "SA", "_email": "", "_tel": "+966 920000177", "_url": "https://zajil-express.com/", "_name": "Zajil Express", "_name_zh-cn": "Zajil Express", "_name_zh-hk": "Zajil Express", "_group": "11"}, {"key": 26011, "_country": 2601, "_country_iso": "ZM", "_email": "", "_tel": "", "_url": "http://www.zampost.com.zm/", "_name": "Zambia Post", "_name_zh-cn": "Zambia Post", "_name_zh-hk": "Zambia Post", "_group": ""}, {"key": 100419, "_country": 322, "_country_iso": "CZ", "_email": "<EMAIL>", "_tel": "+420 216 216 516", "_url": "https://www.zasilkovna.cz/", "_name": "Zasilkovna(Zásilkovna)", "_name_zh-cn": "Zasilkovna(Zásilkovna)", "_name_zh-hk": "Zasilkovna(Zásilkovna)", "_group": "11"}, {"key": 190258, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.geswl.com/", "_name": "ZCE", "_name_zh-cn": "上海翼速", "_name_zh-hk": "上海翼速", "_group": "12"}, {"key": 191018, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.zct56.com/", "_name": "ZCT LOGISTICS", "_name_zh-cn": "志诚通", "_name_zh-hk": "志誠通", "_group": "12"}, {"key": 190068, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zd-express.cn/", "_name": "ZDSD", "_name_zh-cn": "ZDSD", "_name_zh-hk": "ZDSD", "_group": "12"}, {"key": 190970, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>  ", "_tel": "", "_url": "http://www.zjgjhd.com/  ", "_name": "ZEJI GUOJI", "_name_zh-cn": "泽吉国际", "_name_zh-hk": "澤吉國際", "_group": "12"}, {"key": 100144, "_country": 1918, "_country_iso": "ES", "_email": "", "_tel": "(+34) 91 707 40 01", "_url": "https://www.zeleris.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "<PERSON><PERSON><PERSON>", "_name_zh-hk": "<PERSON><PERSON><PERSON>", "_group": "11"}, {"key": 190620, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zf-ex.com/", "_name": "ZFEX", "_name_zh-cn": "ZFEX", "_name_zh-hk": "ZFEX", "_group": "12"}, {"key": 191125, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://szzfgj56.com/", "_name": "ZFGJ", "_name_zh-cn": "卓飞物流", "_name_zh-hk": "卓飛物流", "_group": "12"}, {"key": 191256, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "http://www.zfyhy.com/", "_name": "ZFY", "_name_zh-cn": "众飞扬国际", "_name_zh-hk": "眾飛揚國際", "_group": "12"}, {"key": 191342, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://zh5566.cn/", "_name": "ZH", "_name_zh-cn": "中航供应链", "_name_zh-hk": "中航供應鏈", "_group": "12"}, {"key": 191027, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.meizansupply.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "湛美", "_name_zh-hk": "湛美", "_group": "12"}, {"key": 191045, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 15362814506", "_url": "http://www.szzmexp.com/", "_name": "<PERSON><PERSON><PERSON>", "_name_zh-cn": "哲马国际", "_name_zh-hk": "哲馬國際", "_group": "12"}, {"key": 190815, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-13689587039", "_url": "http://www.zc-yt.com/", "_name": "<PERSON><PERSON><PERSON><PERSON>", "_name_zh-cn": "智诚国际", "_name_zh-hk": "智誠國際", "_group": "12"}, {"key": 190759, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18312461453", "_url": "http://www.zjd123.cn/", "_name": "<PERSON><PERSON><PERSON>edaSupply<PERSON><PERSON><PERSON>", "_name_zh-cn": "智杰达供应链", "_name_zh-hk": "智傑達供應鏈", "_group": "12"}, {"key": 190648, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://1592597785qpa.scd.wezhan.cn/", "_name": "zhixian", "_name_zh-cn": "直线供应链", "_name_zh-hk": "直線供應鏈", "_group": "12"}, {"key": 190557, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zhmy.vip/", "_name": "ZHMY", "_name_zh-cn": "ZHMY", "_name_zh-hk": "ZHMY", "_group": "12"}, {"key": 190870, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 13005445155", "_url": "http://www.z-leading.com/", "_name": "ZHONGLING", "_name_zh-cn": "中领国际", "_name_zh-hk": "中領國際", "_group": "12"}, {"key": 190635, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zp56.com", "_name": "<PERSON><PERSON>peng", "_name_zh-cn": "中鹏供应链", "_name_zh-hk": "中鵬供應鏈", "_group": "12"}, {"key": 190748, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-020-36301996", "_url": "https://www.zs-sci.com/", "_name": "ZhongShu Supply Chain", "_name_zh-cn": "忠恕供应链", "_name_zh-hk": "忠恕供應鏈", "_group": "12"}, {"key": 191324, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86-020-34473741", "_url": "https://www.zhoubotong.com/", "_name": "ZHOUBOTONG", "_name_zh-cn": "洲博通", "_name_zh-hk": "洲博通", "_group": "12"}, {"key": 190439, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.lonfennerlogistic.com/", "_name": "ZHXT Express", "_name_zh-cn": "纵横迅通国际", "_name_zh-hk": "縱橫迅通國際", "_group": "12"}, {"key": 26021, "_country": 2602, "_country_iso": "ZW", "_email": "", "_tel": "", "_url": "http://www.zimpost.co.zw/", "_name": "Zimbabwe Post", "_name_zh-cn": "Zimbabwe Post", "_name_zh-hk": "Zimbabwe Post", "_group": ""}, {"key": 100038, "_country": 9001, "_country_iso": "", "_email": "<EMAIL>", "_tel": "", "_url": "https://zinc.io/", "_name": "Zinc", "_name_zh-cn": "Zinc", "_name_zh-hk": "Zinc", "_group": "11"}, {"key": 191130, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "https://www.zippy-express.com/", "_name": "Zippex", "_name_zh-cn": "Zippex", "_name_zh-hk": "Zippex", "_group": "12"}, {"key": 191041, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.zis-one.com/", "_name": "ZIS", "_name_zh-cn": "ZIS", "_name_zh-hk": "ZIS", "_group": "12"}, {"key": 191005, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86  13761515672", "_url": "http://www.zjrm56.com/", "_name": "ZJRM", "_name_zh-cn": "荣贸物流", "_name_zh-hk": "榮貿物流", "_group": "12"}, {"key": 191051, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://zhongkaihuoyun.com/", "_name": "ZKGJ", "_name_zh-cn": "ZKGJ", "_name_zh-hk": "ZKGJ", "_group": "12"}, {"key": 190642, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hsylian.com/", "_name": "ZL international", "_name_zh-cn": "纵联国际", "_name_zh-hk": "縱聯國際", "_group": "12"}, {"key": 190564, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.zim-logistics.com.cn/", "_name": "ZLC", "_name_zh-cn": "以星物流", "_name_zh-hk": "以星物流", "_group": "12"}, {"key": 191328, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.zlsgjgyl.com/", "_name": "ZLS", "_name_zh-cn": "智联盛", "_name_zh-hk": "智聯盛", "_group": "12"}, {"key": 100786, "_country": 908, "_country_iso": "IQ", "_email": "<EMAIL>", "_tel": "+964 ************", "_url": "https://www.zmcexpress.com/", "_name": "ZMC Express", "_name_zh-cn": "ZMC Express", "_name_zh-hk": "ZMC Express", "_group": "11"}, {"key": 191148, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.zmetaport.com/", "_name": "ZMETAPORT", "_name_zh-cn": "集合派", "_name_zh-hk": "集合派", "_group": "12"}, {"key": 100926, "_country": 2203, "_country_iso": "VE", "_email": "<EMAIL>", "_tel": "+58-0501 9666 000", "_url": "https://zoom.red/", "_name": "ZOOM", "_name_zh-cn": "ZOOM", "_name_zh-hk": "ZOOM", "_group": "11"}, {"key": 191158, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "+86 13923879944", "_url": "https://www.zmb-tech.com/", "_name": "ZOUMABANG", "_name_zh-cn": "走马帮", "_name_zh-hk": "走馬幫", "_group": "12"}, {"key": 190463, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zsda56.com/", "_name": "ZSDExpress", "_name_zh-cn": "转瞬达集运", "_name_zh-hk": "轉瞬達集運", "_group": "12"}, {"key": 190325, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://zsgjky.com/", "_name": "ZSGJ", "_name_zh-cn": "中速国际", "_name_zh-hk": "中速國際", "_group": "10"}, {"key": 190835, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zstgj.com/", "_name": "ZST", "_name_zh-cn": "ZST", "_name_zh-hk": "ZST", "_group": "12"}, {"key": 190338, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zt.hailei2018.com/", "_name": "ZT", "_name_zh-cn": "ZT", "_name_zh-hk": "ZT", "_group": "10"}, {"key": 191171, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86 18873995353", "_url": "http://www.ztjyun.com/", "_name": "ZTJYun", "_name_zh-cn": "中泰佳运", "_name_zh-hk": "中泰佳運", "_group": "12"}, {"key": 191326, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://ztgj.abc213.com/", "_name": "ZTO CWST", "_name_zh-cn": "中通云仓", "_name_zh-hk": "中通雲倉", "_group": "12"}, {"key": 190455, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.zto.com", "_name": "ZTO Express", "_name_zh-cn": "中通快递", "_name_zh-hk": "中通快遞", "_group": "12"}, {"key": 191106, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "https://www.zto56.com/", "_name": "ZTO Freight", "_name_zh-cn": "中通快运", "_name_zh-hk": "中通快運", "_group": "12"}, {"key": 190175, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.ztoglobal.com/", "_name": "ZTO International", "_name_zh-cn": "中通国际", "_name_zh-hk": "中通國際", "_group": "12"}, {"key": 190535, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://zhiteng.biz/", "_name": "ZTT", "_name_zh-cn": "ZTT", "_name_zh-hk": "ZTT", "_group": "12"}, {"key": 190646, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.ztwlexpress.com/", "_name": "ZTWL", "_name_zh-cn": "中塔物流", "_name_zh-hk": "中塔物流", "_group": "12"}, {"key": 190198, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zxlogs.ltd/", "_name": "ZXG", "_name_zh-cn": "ZXG", "_name_zh-hk": "ZXG", "_group": "12"}, {"key": 190960, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL> ", "_tel": "", "_url": "https://zy100-express.com/", "_name": "ZY", "_name_zh-cn": "中远快运", "_name_zh-hk": "中遠快運", "_group": "12"}, {"key": 190750, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-18588997445", "_url": "http://www.zy56gz.com/", "_name": "ZY56", "_name_zh-cn": "中运国际供应链", "_name_zh-hk": "中运国际供应链", "_group": "12"}, {"key": 190530, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.zyouexpress.com/", "_name": "ZYEX", "_name_zh-cn": "ZYEX", "_name_zh-hk": "ZYEX", "_group": "12"}, {"key": 190862, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "https://www.zyqq.cc/", "_name": "ZYQQ", "_name_zh-cn": "转运全球", "_name_zh-hk": "轉運全球", "_group": "12"}, {"key": 191276, "_country": 301, "_country_iso": "CN", "_email": "<EMAIL>", "_tel": "", "_url": "http://www.zhongyouyamao.com/", "_name": "ZYYM", "_name_zh-cn": "中邮亚贸", "_name_zh-hk": "中郵亞貿", "_group": "12"}, {"key": 100975, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "+7-(495) 995-08-09", "_url": "https://www.baikalsr.ru/", "_name": "Б<PERSON>йкал Сервис", "_name_zh-cn": "Б<PERSON>йкал Сервис", "_name_zh-hk": "Б<PERSON>йкал Сервис", "_group": "11"}, {"key": 100568, "_country": 217, "_country_iso": "BG", "_email": "", "_tel": "+359 0 7001 7300", "_url": "https://www.econt.com/", "_name": "Еконт (Econt)", "_name_zh-cn": "Еконт (Econt)", "_name_zh-hk": "Еконт (Econt)", "_group": "11"}, {"key": 100093, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://www.cse.ru/", "_name": "КСЭ (CSE)", "_name_zh-cn": "КСЭ (CSE)", "_name_zh-hk": "КСЭ (CSE)", "_group": "11"}, {"key": 100793, "_country": 1803, "_country_iso": "RU", "_email": "<EMAIL>", "_tel": "+7 (495) 660-11-11", "_url": "https://pecom.ru/", "_name": "ПЭК (PEC)", "_name_zh-cn": "ПЭК (PEC)", "_name_zh-hk": "ПЭК (PEC)", "_group": "11"}, {"key": 100546, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://sberlogistics.ru/", "_name": "СберЛогистика (SberLogistics)", "_name_zh-cn": "СберЛогистика (SberLogistics)", "_name_zh-hk": "СберЛогистика (SberLogistics)", "_group": "11"}, {"key": 100968, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "****** 700 7000", "_url": "https://nrg-tk.ru/", "_name": "ТК Энергия", "_name_zh-cn": "ТК Энергия", "_name_zh-hk": "ТК Энергия", "_group": "11"}, {"key": 100974, "_country": 1803, "_country_iso": "RU", "_email": "<EMAIL>", "_tel": "******-250-88-79", "_url": "https://tk-kit.com/", "_name": "Транспортная компания КИТ (KIT)", "_name_zh-cn": "Транспортная компания КИТ (KIT)", "_name_zh-hk": "Транспортная компания КИТ (KIT)", "_group": "11"}, {"key": 100583, "_country": 1803, "_country_iso": "RU", "_email": "", "_tel": "", "_url": "https://www.unitrade.su/", "_name": "<PERSON>н<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Unitrade)", "_name_zh-cn": "<PERSON>н<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Unitrade)", "_name_zh-hk": "<PERSON>н<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Unitrade)", "_group": "11"}, {"key": 100973, "_country": 1002, "_country_iso": "JP", "_email": "", "_tel": "+81-050-3116-2250", "_url": "https://www.ecohai.co.jp/", "_name": "エコ配 (ecohai)", "_name_zh-cn": "エコ配 (ecohai)", "_name_zh-hk": "エコ配 (ecohai)", "_group": "11"}, {"key": 100637, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+82 ************~3", "_url": "https://www.kunyoung.com/", "_name": "건영택배 (KUNYONG EXPRESS)", "_name_zh-cn": "건영택배 (KUNYONG EXPRESS)", "_name_zh-hk": "건영택배 (KUNYONG EXPRESS)", "_group": "11"}, {"key": 100569, "_country": 1105, "_country_iso": "KR", "_email": "", "_tel": "+82 1899-5368'", "_url": "https://kdexp.com/main.kd", "_name": "경동택배 (KYOUNGDONG)", "_name_zh-cn": "경동택배 (KYOUNGDONG)", "_name_zh-hk": "경동택배 (KYOUNGDONG)", "_group": "11"}, {"key": 190791, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.jzexp.com.cn/en/", "_name": "剑展物流", "_name_zh-cn": "剑展物流", "_name_zh-hk": "剑展物流", "_group": "12"}, {"key": 190550, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.otwex.com/", "_name": "埃德维OTW", "_name_zh-cn": "埃德维OTW", "_name_zh-hk": "埃德维OTW", "_group": "12"}, {"key": 190586, "_country": 2001, "_country_iso": "TW", "_email": "<EMAIL>", "_tel": "+886 (02)2511-0785/(02)2511-0735", "_url": "https://www.tsl-logistics.tw/", "_name": "天胜国际(TSL)", "_name_zh-cn": "天胜国际", "_name_zh-hk": "天勝國際", "_group": "12"}, {"key": 190647, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "", "_url": "http://www.hyfba.cn/", "_name": "环越国际专线", "_name_zh-cn": "环越国际专线", "_name_zh-hk": "環越國際專線", "_group": "12"}, {"key": 191034, "_country": 301, "_country_iso": "CN", "_email": "", "_tel": "+86-400-9989-256", "_url": "http://www.beteng.com/", "_name": "百腾物流", "_name_zh-cn": "百腾物流", "_name_zh-hk": "百騰物流", "_group": "12"}]
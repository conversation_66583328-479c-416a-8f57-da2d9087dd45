<?php

namespace App\Providers\FulfillAPI;

use App\Enums\AccessExternalApiType;
use App\Enums\ApiLogTypeEnum;
use App\Enums\CacheKeys;
use App\Enums\FulfillMappingEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\SupplierEnum;
use App\Jobs\Suppliers\CrawlProductsAndProductVariants;
use App\Jobs\Suppliers\CrawlProductVariants;
use App\Models\ApiLog;
use App\Models\FulfillMapping;
use App\Models\FulfillProduct;
use App\Models\LogColor;
use App\Models\OrderProduct;
use App\Models\ProductFulfillMapping;
use App\Models\ProductVariant;
use App\Providers\FulfillAPI\Common\CrawlProductVariantModel;
use App\Providers\FulfillAPI\Common\TokenRetriever;
use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Modules\OrderService\Models\RegionOrderProducts;
use Throwable;
use function DeepCopy\deep_copy;

/**
 * @property bool $cancel_order
 */
class ObjectFulfill
{
    public const NAME_FILE_CONFIG = 'supplier';
    public const PATH_FOLDER = '\App\Providers\FulfillAPI';
    public const PATH_PROVIDER = '\Provider';
    public const PATH_MODEL = '\Model';

    public const METHOD_CREATE_ORDER = 'CREATE_ORDER';
    public const METHOD_CANCEL_ORDER = 'CANCEL_ORDER';
    public const METHOD_CRAWL_ORDER = 'CRAWL_ORDER';
    public const METHOD_CRAWL_ORDER_TRACKING = 'CRAWL_ORDER_TRACKING';

    public const METHOD_CRAWL_PRODUCT = 'CRAWL_PRODUCT';
    public const METHOD_CRAWL_PRODUCT_VARIANT = 'CRAWL_PRODUCT_VARIANT';
    public const METHOD_CRAWL_SHIPPING_RULE = 'CRAWL_SHIPPING_RULE';
    public const METHOD_CRAWL_COLOR = 'CRAWL_COLOR';
    public const METHOD_UPLOAD_IMAGE = 'UPLOAD_IMAGE';
    public const METHOD_CRAWL_PRINT_SPACE = 'CRAWL_PRINT_SPACE';
    public const METHOD_CRAWL_ORDER_DETAILS = 'CRAWL_ORDER_DETAILS';
    public const METHOD_CRAWL_ORDERS = 'CRAWL_ORDERS';

    public const MODEL_RESPONSE = '\ResponseModel';
    public const MODEL_CREATE_ORDER = '\CreateOrderModel';
    public const MODEL_CANCEL_ORDER = '\CancelOrderModel';
    public const MODEL_CRAWL_PRODUCT = '\CrawlProductModel';
    public const MODEL_CRAWL_PRODUCT_VARIANT = '\CrawlProductVariantModel';
    public const MODEL_CRAWL_SHIPPING_RULE = '\CrawlShippingRuleModel';

    protected string $mode = 'dev';

    protected string $api;
    protected string $api_origin;
    protected array $headers = [];
    protected $supplier_id;
    protected string $token;
    protected string $path;
    protected string $method;
    protected $order_id = null;
    protected $hasProxy = false;
    protected $fulfill_order_id = null;
    protected string $method_url = 'POST';
    protected $params = [];

    // only crawl product (and variant) in this list for fulfill
    protected array $allow_crawl_products_name = [];

    // array attribute for crawl product & variant
    protected array $arr_product = [];
    protected array $arr_variant = [];
    protected array $arr_product_sku = [];
    protected array $arr_product_id_insert = [];
    protected array $arr_new_color = [];
    private ?string $webhook_token;

    public array $product_allow_refactor_option = [];

    public array $product_not_allow_translate_options = [];
    protected string $error_message = '';
    public bool $is_testing = false;
    /**
     * @return bool[]
     */
    public function curlOptions(): array
    {
        return [
            CURLOPT_SSL_VERIFYPEER => true
        ];
    }

    /**
     * @return string
     */
    public function proxyOption(): string
    {
        return '';
    }

    /**
     * @return bool
     */
    public function shouldHaveRequestBody(): bool
    {
        return true;
    }

    /**
     * @param $params
     *
     * @return string|false
     * @throws Throwable
     */
    public function withBody($params): bool|string
    {
        return json_encode($params, JSON_THROW_ON_ERROR);
    }

    protected function setHeader($key, $value): void
    {
        $this->headers[$key] = $value;
    }

    protected function setHeaders($arr): void
    {
        foreach ($arr as $key => $value) {
            $this->setHeader($key, $value);
        }
    }

    protected function setMode($mode): void
    {
        $this->mode = $mode;
    }

    protected function setIsTesting($isTesting): void
    {
        $this->is_testing = $isTesting;
    }

    protected function setHasProxy($hasProxy): void
    {
        $this->hasProxy = $hasProxy;
    }

    protected function setWebHookToken(?string $token): void
    {
        $this->webhook_token = $token;
    }

    protected function setApi($api): void
    {
        $this->api        = $api;
        $this->api_origin = $api;
    }

    protected function refreshApi(): void
    {
        $this->api = $this->api_origin;
    }

    protected function setApiWithEndpoint(string $extraStringUrl = ''): void
    {
        $this->api .= $extraStringUrl . $this->getEndpoint();
    }

    protected function getEndpoint(): string
    {
        return $this->getSelfConstant('ENDPOINT_' . $this->method);
    }

    protected function setToken($token): void
    {
        $this->token = $token;
    }

    protected function setPath($path): void
    {
        $this->path = $path;
    }

    protected function setMethod($method): void
    {
        $this->method = $method;
    }

    public function setParams($params): void // to extend
    {
    }

    public function getParams(): array
    {
        return $this->params;
    }

    public function getIsTesting () {
        return $this->is_testing;
    }

    public function setOrderId($orderId): void
    {
        $this->order_id = $orderId;
    }

    public function setSupplierId($supplierId): void
    {
        $this->supplier_id = $supplierId;
    }

    public function setErrorMessage($errorMessage): void
    {
        $this->error_message = $errorMessage;
    }

    public function getSupplierId()
    {
        return $this->supplier_id;
    }

    public function getSupplierName(): string
    {
        return SupplierEnum::getDescription($this->getSupplierId());
    }

    /**
     * @return string|null
     */
    public function getWebhookToken(): ?string
    {
        return $this->webhook_token;
    }

    /**
     * @return bool
     */
    public function getHasProxy(): bool
    {
        return $this->hasProxy;
    }

    protected static function getConfigProvider(): array
    {
        return config(self::NAME_FILE_CONFIG);
    }

    //    get self constant
    public function getSelfConstant($constantName): string
    {
        return constant(get_class($this) . '::' . $constantName);
    }

    //    get path to correct model
    public function getModel($stringModel): object
    {
        $pathModel = self::PATH_FOLDER . $this->path . self::PATH_MODEL . $stringModel;
        $object    = new $pathModel($this->supplier_id);
        $object->setSupplierId($this->supplier_id);

        return $object;
    }

    public function getProviderBySupplierId($supplierId): ?ObjectFulfill
    {
        $providers = self::getConfigProvider();
        $instance = null;
        foreach ($providers as $provider) {
            if ((int)$supplierId === (int)$provider['supplier_id'] && !empty($provider['path'])) {
                $instance = app(self::PATH_FOLDER . $provider['path'] . self::PATH_PROVIDER);
                if ($this->getIsTesting()) {
                    $instance->setIsTesting(true);
                }
                $instance->setProvider($provider);
                break;
            }
        }

        return $instance;
    }

    public function getErrorMessage () {
        return $this->error_message;
    }

    /**
     * @param    int    $supplierId
     *
     * @return ObjectFulfill|null
     */
    public static function provider(int $supplierId): ?ObjectFulfill
    {
        static $providers = [];

        if (! array_key_exists($supplierId, $providers)) {
            $providers[$supplierId] = self::singleton()->getProviderBySupplierId($supplierId);
        }

        return $providers[$supplierId];
    }

    /**
     * @return ObjectFulfill
     */
    public static function singleton(): ObjectFulfill
    {
        static $instance = [];

        return $instance[static::class] ??= static::newInstance();
    }

    /**
     * @return \App\Providers\FulfillAPI\ObjectFulfill
     */
    public static function newInstance(): ObjectFulfill
    {
        return new static;
    }

    /**
     * @return \App\Providers\FulfillAPI\Common\TokenRetriever
     */
    public function tokenRetriever(): TokenRetriever
    {
        return app(TokenRetriever::class);
    }

    public static function checkSupplierHaveAPI(string $type, string $supplierId): bool
    {
        $providers = self::getConfigProvider();

        foreach ($providers as $provider) {
            if ((int)$supplierId === $provider['supplier_id'] && !empty($provider['have_api'][$type])) {
                return true;
            }
        }

        return false;
    }

    public static function getSupplierIdsHaveAPI(string $type): array
    {
        $providers = self::getConfigProvider();

        $arrSupplierIds = [];
        foreach ($providers as $provider) {
            if (!empty($provider['have_api'][$type])) {
                $arrSupplierIds[] = $provider['supplier_id'];
            }
        }

        return $arrSupplierIds;
    }

    /**
     * @noinspection ReturnTypeCanBeDeclaredInspection
     * @noinspection PhpMissingReturnTypeInspection
     */
    protected function setProvider(array $provider)
    {
        $mode = 'production';
        if ($this->getIsTesting() || (!$this->getIsTesting() && !app()->environment($mode))) {
            $mode = 'dev';
        }

        if (isset($provider[$mode])) {
            foreach ($provider[$mode] as $key => $value) {
                $provider[$key] = $value;
            }
        }

        $this->setSupplierId($provider['supplier_id']);
        $this->setToken($provider['token'] ?? '');
        $this->setApi($provider['api']);
        $this->setPath($provider['path']);
        $this->setMode($mode);
        $this->setHasProxy($provider['proxy'] ?? false);
        $this->setWebHookToken($provider['webhook_token'] ?? null);
        foreach ($provider['have_api'] as $key => $bool) {
            $this->$key = $bool;
        }

        return $provider;
    }

    protected function checkAllowCrawlProduct($productName, string $category = ''): bool
    {
        // if in allowed category => all
        if (!empty($category) && !empty($this->allow_crawl_categories) && in_array($category, $this->allow_crawl_categories, true)) {
            return true;
        }

        // empty => all are allowed
        if (empty($this->allow_crawl_products_name)) {
            return true;
        }

        if (is_array($productName)) {
            $productName = $productName['name'];
        }

        return in_array($productName, $this->allow_crawl_products_name, true);
    }

    /**
     * @param $body
     * @param string|null $userId
     * @param $saveLog
     * @return array|mixed|null
     * @throws Throwable
     */
    public function sendData($body = null, ?string $userId = null, $saveLog = true)
    {
        try {
            if (is_null($body)) {
                $response = with(Http::withHeaders($this->headers), function (PendingRequest $http) {
                    $options = [
                        'curl' => $this->curlOptions()
                    ];
                    if ($this->proxyOption() !== '') {
                        $options['proxy'] = $this->proxyOption();
                    }
                    $http->withOptions($options);
                    if ($this->shouldHaveRequestBody()) {
                        $requestBody = $this->withBody($this->params);
                        $http->withBody($requestBody);
                    }
                    return $http->timeout($this->requestTimeout())
                        ->acceptJson()
                        ->send(
                            $this->method_url,
                            $this->api
                        );
                });
                $statusCode = $response->status();
                $body = $response->json();
                $body = $this->prepareResponse($body, $statusCode);
            }
            if ($saveLog) {
                $this->logRequestAPI($body, $userId);
            }
            return $body;
        } catch (Throwable $e) {
            if ($saveLog) {
                $this->logRequestAPI($body, $userId);
            }
            throw $e;
        }
    }

    /**
     * @return int
     */
    public function requestTimeout(): int
    {
        return 30;
    }

    /**
     * @param $body
     * @param $userId
     *
     * @return void
     */
    protected function logRequestAPI($body, $userId): void
    {
        ApiLog::query()->create([
            'name'             => $this->method,
            'from'             => $userId,
            'reference_id'     => $this->supplier_id,
            'reference_type'   => AccessExternalApiType::SUPPLIER,
            'order_id'         => $this->order_id,
            'fulfill_order_id' => $this->fulfill_order_id,
            'url'              => $this->api,
            'request_body'     => json_encode($this->params, JSON_UNESCAPED_SLASHES),
            'response_body'    => $body ? json_encode($body, JSON_UNESCAPED_SLASHES) : null,
            'type'             => ApiLogTypeEnum::API,
        ]);
    }

    /**
     * @param $body
     * @param $statusCode
     *
     * @return array
     */
    protected function prepareResponse($body, $statusCode): array
    {
        if (! is_array($body)) {
            $body = ['response' => $body];
        }

        $body['response_status_code'] = $statusCode;

        return $body;
    }

    public function createOrder($order, $reOrder): object
    {
        /** @var AbstractModel $object */
        $object = $this->getModel(self::MODEL_CREATE_ORDER);
        $object->setWebHookToken($this->webhook_token);
        $object->setMode($this->mode);
        $object->setConfigOrder($order);
        $object->setReOrder($reOrder);
        $object->setOrder($order);
        $this->setMethod(
            $this::METHOD_CREATE_ORDER
        );// use $this instead of self because some case Provider can overwrite method

        return $object;
    }

    /**
     * @throws Throwable
     */
    public function cancelOrder($fulfillOrderId, $orderId): bool
    {
        $object = $this->getModel(self::MODEL_CANCEL_ORDER);
        $object->setMode($this->mode);
        $object->setOrderProduct($fulfillOrderId, $orderId);
        $this->setMethod(
            $this::METHOD_CANCEL_ORDER
        );
        $this->setOrderId($orderId);
        $this->setParams($object);

        return $this->handleCancelOrderResponse(
            $this->sendData()
        );
    }

    /**
     * @throws Exception|Throwable
     */
    public function crawlOrder($fulfillOrderId, $orderId = null, $justCreated = false): array
    {
        $this->setMethod($this::METHOD_CRAWL_ORDER);
        $this->setParams($fulfillOrderId);
        $this->setOrderId($orderId);
        $responseModel = $this->getModel(self::MODEL_RESPONSE);
        $response      = $this->sendData();
        return $responseModel->mappingCrawlOrder($response, $fulfillOrderId);
    }

    /**
     * @throws Exception
     */
    public function crawlOrders($fulfillOrderIds): array
    {
        return [];
    }

    public function crawlProductsAndProductVariants(): void
    {
        CrawlProductsAndProductVariants::dispatch($this);
    }

    public function crawlProductsAndProductVariantsJob(): void
    {
        $objectCrawlProduct = deep_copy($this);
        $objectCrawlProduct->setMethod($this::METHOD_CRAWL_PRODUCT);

        $responseModel = $objectCrawlProduct->getModel(self::MODEL_RESPONSE);

        if (method_exists($responseModel, 'getParamsPagination')) {
            $params = $responseModel->getParamsPagination();
            $objectCrawlProduct->setParams($params);
        }

        $arr                 = [];
        $this->arr_new_color = [];
        $objectProductModel  = $objectCrawlProduct->getModel(self::MODEL_CRAWL_PRODUCT);
        while (true) {
            $response = $objectCrawlProduct->sendData(null, null, false);
            $responseModel->mappingCrawlProducts($response);
            $data = $responseModel->getResponseData();
            foreach ($data as $each) {
                try {
                    $object  = deep_copy($objectProductModel);
                    $product = $object->mapping($each);
                    if (!$this->checkAllowCrawlProduct($product)) {
                        continue;
                    }
                    $arr[$product['sku']] = $product;
                    if (!empty($object->getNewColors())) {
                        foreach ($object->getNewColors() as $color) {
                            $this->arr_new_color[] = $color;
                        }
                    }
                } catch (Throwable $e) {
                    logException($e, __FUNCTION__, 'fulfill_product', true);
                    continue;
                }
            }
            $nextPage = $responseModel->getNexPage();
            if (empty($nextPage)) {
                break;
            }
            $params = $responseModel->getParamsPagination();
            $objectCrawlProduct->setParams($params);
            $objectCrawlProduct->setApi($nextPage);
        }
        // $this->handleNewColor();
        $this->crawlProductVariants($arr);
    }

    public function crawlProductVariants($arr): void
    {
        $objectCrawlProductVariant = deep_copy($this);
        $responseModel             = $objectCrawlProductVariant->getModel(self::MODEL_RESPONSE);
        $objectCrawlProductVariant->setMethod($this::METHOD_CRAWL_PRODUCT_VARIANT);
        $objectProductVariantModel = $objectCrawlProductVariant->getModel(self::MODEL_CRAWL_PRODUCT_VARIANT);
        foreach ($arr as $sku => $product) {
            $objectCrawlProductVariant->setParams($sku);
            CrawlProductVariants::dispatch(
                $objectCrawlProductVariant,
                $objectProductVariantModel,
                $responseModel,
                $this->supplier_id,
                $sku,
                $product
            );
            // (new CrawlProductVariants(
            //     $objectCrawlProductVariant,
            //     $objectProductVariantModel,
            //     $responseModel,
            //     $this->supplier_id,
            //     $sku,
            //     $product
            // ))->handle();
        }
    }

    // public function crawlColor(): void
    // {
    //     $provider = $this->getProviderBySupplierId(4);
    //     $links    = \App\Models\TestColor::where('id', '>=', 97942)->get('link')->pluck('link');
    //     $provider->setMethod($provider::METHOD_CRAWL_COLOR);
    //     $responseModel = $provider->getModel(self::MODEL_RESPONSE);
    //     $arr           = [];
    //     foreach ($links as $link) {
    //         $provider->setParams($link);
    //         $response = $provider->sendData();
    //         $arr[]    = $responseModel->mappingCrawlColor($response);
    //     }
    //
    //     \App\Models\TestColor::upsert($arr, ['id']);
    // }

    public function uploadImage($url): string
    {
        // get file from url first
        $file = file_get_contents($url);

        $provider = $this;
        $provider->setMethod($provider::METHOD_UPLOAD_IMAGE);
        $responseModel = $provider->getModel(self::MODEL_RESPONSE);
        $provider->setParams($file);
        $response = $provider->sendData();

        return $responseModel->mappingUploadImage($response);
    }

    public function crawlPrintSpace($sku): void
    {
        $this->setMethod($this::METHOD_CRAWL_PRINT_SPACE);
        $responseModel = $this->getModel(self::MODEL_RESPONSE);
        $this->setParams($sku);
        $response = $this->sendData();
        $arr      = $responseModel->mappingCrawlPrintSpace($response);

        foreach ($arr as $each) {
            FulfillMapping::updateOrCreate(
                [
                    'object_id'   => $sku . ':' . strtolower(trim($each['print_space'])),
                    'supplier_id' => $this->supplier_id,
                    'type'        => FulfillMappingEnum::PRINT_SPACES,
                ],
                [
                    'external_id' => $each['id'],
                ]
            );
        }
    }

    public function mappingVariantKey(string $variantKey, $orderProduct, $fulfillProduct, $isManual): string
    {
        return $variantKey;
    }

    /**
     * Method này sẽ được override bởi các provider khác nếu cần can thiệp vào bước tạo variant key
     * todo có thời gian thì gộp 2 method này làm 1
     *
     * @param    array           $orderOptions
     * @param    OrderProduct|RegionOrderProducts    $op
     *
     * @return string
     */
    public function getVariantKey(array $orderOptions, OrderProduct|RegionOrderProducts $op): string
    {
        return getVariantKey($orderOptions);
    }

    /**
     * @param    OrderProduct|RegionOrderProducts    $op
     *
     * @return OrderProduct|RegionOrderProducts
     */
    public function correctOrderProduct(OrderProduct|RegionOrderProducts $op): OrderProduct|RegionOrderProducts
    {
        $options = json_decode($op->options, true);

        if (!empty($options['pack'])) {
            $op->quantity *= $this->getPackQuantity($options['pack']);
            if (!isset($op->full_printed) || $op->full_printed != ProductPrintType::HANDMADE) {
                unset($options['pack']);
            }
        }
        $op->options = $options;

        return $op;
    }

    /**
     * @param $pack
     *
     * @return int
     */
    protected function getPackQuantity($pack): int
    {
        $pack = Str::afterLast($pack, '_');

        if ($pack === 'single') {
            return 1;
        }

        // 3pcs => 3
        return (int) str_replace('pcs', '', $pack);
    }

    public function crawlOrderDetails($responseModel, $orderProductIds): void
    {
        $this->setMethod($this::METHOD_CRAWL_ORDER_DETAILS);
        $params = $responseModel->getParamsToCrawl();
        $this->setParams($params);

        try {
            $response = $this->sendData();
            $responseModel->mappingCrawlOrderDetails($response, $orderProductIds, $responseModel->getFulfillOrderId());
        } catch (Exception $e) {
            // nothing to do
        }
    }

    public function handleAfterCrawlProduct($updateStatus = true): void
    {
        // delete mapping
        $this->deleteProductFulfillMapping($this->supplier_id, $this->arr_product_id_insert);

        if ($updateStatus) {
            $this->deActiveOldFulfillProductIds($this->supplier_id, $this->arr_product_id_insert);

            $this->activeNewFulfillProductIds($this->supplier_id, $this->arr_product_id_insert);
        }

        unset($this->arr_product_sku);
    }

    /**
     * @param     int       $supplierID
     * @param     array     $productIDs
     *
     * @return void
     */
    public function deleteProductFulfillMapping(int $supplierID, array $productIDs): void
    {
        ProductFulfillMapping::query()
            ->whereNotIn('fulfill_product_id', $productIDs)
            ->where('supplier_id', $supplierID)
            ->delete();
    }

    /**
     * @param     int       $supplierID
     * @param     array     $newProductIDs
     *
     * @return void
     */
    public function deActiveOldFulfillProductIds(int $supplierID, array $newProductIDs): void
    {
        FulfillProduct::query()
            ->whereKeyNot($newProductIDs)
            ->where('supplier_id', $supplierID)
            ->update(['status' => ProductStatus::INACTIVE]);
    }

    /**
     * @param     int       $supplierID
     * @param     array     $newProductIDs
     *
     * @return void
     */
    public function activeNewFulfillProductIds(int $supplierID, array $newProductIDs): void
    {
        FulfillProduct::query()
            ->whereKey($newProductIDs)
            ->where('supplier_id', $supplierID)
            ->update(['status' => ProductStatus::ACTIVE]);
    }

    public function handleNewColor(): void
    {
        foreach (array_chunk($this->arr_new_color, 1000) as $colors) {
            LogColor::insertOrIgnore($colors);
        }
        unset($this->arr_new_color);
    }

    public function setVariants(array $variants): void
    {
        $this->arr_variant = $variants;
    }

    public function setProductIdInsert(array $arrProductId): void
    {
        $this->arr_product_id_insert = $arrProductId;
    }

    public function handleAfterCrawlVariant($deleteOnlyInserted = true): void
    {
        if ($deleteOnlyInserted) {
            $this->deleteVariantByProductIDs($this->arr_product_id_insert);
        } else {
            $this->deleteVariantBySupplierID($this->supplier_id);
        }

        foreach (array_chunk($this->arr_variant, 1000) as $variants) {
            $this->insertVariant($variants);
        }
        $this->clearCacheTemplate();
    }

    /**
     * @param     array     $variants
     *
     * @return void
     */
    public function insertVariant(array $variants): void
    {
        try {
            ProductVariant::insert($variants);
        } catch (Throwable $e) {
            ProductVariant::insertOrIgnore($variants);
            logToDiscord(
                'Insert variants failed.'
                . "\nSupplier:" . $this->supplier_id
                . "\nException:" . $e->getMessage()
                , 'fulfill_product'
                , true
            );
        }
    }

    /**
     * @param     array     $productIDs
     *
     * @return void
     */
    public function deleteVariantByProductIDs(array $productIDs): void
    {
        ProductVariant::query()
            ->whereIn('product_id', $productIDs)
            ->delete();
    }

    /**
     * @param     int       $supplierID
     *
     * @return void
     */
    public function deleteVariantBySupplierID(int $supplierID): void
    {
        ProductVariant::from('product_variant')
            ->join('product', 'product_variant.product_id', '=', 'product.id')
            ->where('product.supplier_id', $supplierID)
            ->delete();
    }

    public function clearCacheTemplate($supplierId = null, $fulfill_product_ids = []): void
    {
        $templateIds = ProductFulfillMapping::query()
            ->whereIn('fulfill_product_id', $fulfill_product_ids ?: $this->arr_product_id_insert)
            ->where('supplier_id', $supplierId ?: $this->supplier_id)
            ->pluck('product_id');


            $result = $templateIds->map(
                static fn($v) => CacheKeys::getTemplateFulfillProduct($v)
            )->all();

            syncClearCache(['tags' => $result], CacheKeys::CACHE_TYPE_ALTERNATIVE);
    }

    /**
     * @throws Throwable
     */
    public function crawlProductsWithVariants(): bool
    {
        try {
            $bool = false;
            $products = [];
            $exceptions = [];

            // do something before crawl
            $this->beforeCrawlProducts();

            foreach ($this->crawlProducts() as $lightweightProduct) {
                try {
                    array_push($products, ...$this->crawlVariants($lightweightProduct));
                } catch (Throwable $e) {
                    $exceptions[] = $e;
                }
            }

            // log or throw exception if you want
            $this->onCrawlProductsErrors($exceptions);

            // validate and prepare data before save
            $products = $this->beforeSaveFulfillProducts($products);

            // save data
            $bool = $this->saveFulfillProducts($products);

            // do something after save
            $this->afterSaveFulfillProducts($bool, $products);
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'fulfill_product', true);
        }

        return $bool;
    }

    /**
     * @return void
     */
    public function beforeCrawlProducts(): void
    {
        set_time_limit(0);

        $this->deActiveAllFulfillProductBySupplierId($this->getSupplierId());
    }

    /**
     * @throws Exception
     */
    protected function crawlProducts(): array
    {
        static $crawler, $rm;

        $crawler ??= clone $this;
        $crawler->setMethod(static::METHOD_CRAWL_PRODUCT);

        $rm ??= $crawler->getModel(static::MODEL_RESPONSE);

        $errors = [];
        $products = [];
        $productModel = $crawler->getModel(static::MODEL_CRAWL_PRODUCT);

        do {
            // Thiết lập thông tin api
            $crawler->setParams(
                $rm->getParamsPagination()
            );

            $rawResponse = $crawler->fetchData();

            // Lấy dữ liệu từ api và mapping dữ liệu về dạng array
            $rm->mappingCrawlProducts($rawResponse);

            // Lặp qua từng sản phẩm và mapping dữ liệu cho từng sản phẩm
            foreach ($rm->getResponseData() as $item) {
                try {
                    if ($this->skipProduct($item)) {
                        continue;
                    }

                    $this->transformRawFulfillProduct(
                        $products, $productModel->mapping($item)
                    );
                } catch (Throwable $e) {
                    $errors[] = $e;
                }
            }
        } while ($rm->getNexPage());

        $this->onCrawlProductsErrors($errors);

        return $this->afterCrawlAllProducts($products);
    }

    /**
     * @param     array     $errors
     *
     * @return void
     */
    protected function onCrawlProductsErrors(array $errors): void
    {
        array_map(static fn($e) => logException($e, __FUNCTION__, 'fulfill_product', true), $errors);
    }

    /**
     * @param    array    $item
     *
     * @return bool
     */
    protected function skipProduct(array $item): bool
    {
        return ! $this->checkAllowCrawlProduct($item['name']);
    }

    /**
     * @param     array     $products
     *
     * @return array
     */
    protected function afterCrawlAllProducts(array $products): array
    {
        return $products;
    }

    /**
     * @param     array     $products
     * @param     array     $product
     *
     * @return array
     */
    protected function transformRawFulfillProduct(array &$products, array $product): array
    {
        $products[] = $product;

        return $products;
    }

    /**
     * @param    string $productName
     *
     * @return bool
     */
    public function checkProductAllowRefactor(string $productName): bool
    {
        return in_array($productName, $this->product_allow_refactor_option);
    }

    /**
     * @param    string $productName
     *
     * @return bool
     */
    public function checkProductNotAllowTranslateOptions(string $productName): bool
    {
        return in_array($productName, $this->product_not_allow_translate_options);
    }

    /**
     * @throws Exception
     * @throws Throwable
     */
    protected function crawlVariants(array $product): array
    {
        static $crawler, $variantModel;

        /** @var \App\Providers\FulfillAPI\ObjectFulfill $crawler */
        $crawler ??= $this->newVariantCrawler();
        $crawler->setParams($product);

        $response = $crawler->fetchData();

        // Gắn thêm các trường sản phẩm từ response nếu cần thiết
        $product = $this->attachProductFieldIfNecessary($product, $response);

        /** @var \App\Providers\FulfillAPI\AbstractResponseModel $responseModel */
        $responseModel = $crawler->getModel(static::MODEL_RESPONSE);
        $responseModel->mappingCrawlProductVariants($response, $product, $this->getSupplierId());

        // Extract dữ liệu và mapping dữ liệu cho từng biến thể
        $variantModel ??= $crawler->getModel(static::MODEL_CRAWL_PRODUCT_VARIANT);
        $variants = $this->extractResponseAndMapVariantFields($responseModel, $product, $variantModel);
        // Nhân bản sản phẩm theo các tùy chọn đặc biệt
        $products = array_values(
            $this->duplicateProductBySeparateOptions($variants, $product)
        );

        return array_map([$this, 'prepareProduct'], $products);
    }

    protected function newVariantCrawler()
    {
        return tap(clone $this, static function (ObjectFulfill $c) {
            $c->setMethod($c::METHOD_CRAWL_PRODUCT_VARIANT);
        });
    }

    /**
     * @return array|mixed|null
     * @throws \Exception
     */
    protected function fetchData()
    {
        return $this->sendData(null, null, false) ?: [];
    }

    /**
     * @param     array     $product
     * @param     array     $response
     *
     * @return array
     */
    protected function attachProductFieldIfNecessary(array $product, array $response): array
    {
        return $product;
    }

    /**
     * @param array $variants
     * @param array $product
     *
     * @return array
     */
    public function duplicateProductBySeparateOptions(array $variants, array $product): array
    {
        $result = [];

        foreach ($variants as $variant) {
            // Các sản phẩm biến thể không có tùy chọn đặc biệt sẽ được gắn vào sản phẩm chính
            $key = 0;

            // Các sản phẩm biến thể có tùy chọn đặc biệt sẽ được gắn vào sản phẩm đặc biệt
            // ** bản chất sản phẩm đặc biệt chỉ là clone thông tin sản phẩm cha và thêm
            // các biến thể của nó vào
            if ( ! empty($variant['separate_options'])) {
                $key = implode('-', $variant['separate_options']);
            }

            $result[$key] ??= value(static function () use ($product, $key): array {
                $product['variants'] = [];

                // Mã sku hay tên sản phẩm sẽ được gắn thêm hậu tố là tên tùy chọn đặc biệt nếu không phải là sản phẩm gốc
                if ($key) {
                    $product['name'] .= ' - ' . $key;
                    $product['sku'] .= '-' . $key;
                }

                return $product;
            });

            $result[$key]['variants'][] = $variant;
        }

        return $result;
    }

    /**
     * @param array $product
     *
     * @return array
     */
    public function prepareProduct(array $product): array
    {
        // Trạng thái sản phẩm mặc định là active
        $product['status'] = ProductStatus::ACTIVE;

        // Từ danh sách biến thể sản phẩm
        $product['options'] = collect($product['variants'])

            // Gộp đệ quy tất cả tùy chọn của biển thể lại,
            // ví  dụ {color: black, size: s}, {color: white} => {color: [black, white], size: [s]}
            ->reduce(
                fn(Collection $result, $variant) => $result->mergeRecursive($variant['options']),
                collect([])
            )

            ->map(fn($item) => is_array($item) ? $item : [$item])

            // Loại bỏ các tùy chọn trùng lặp
            // Ví dụ: {color: [black, black, white], size: [s, s, m]} => {color: [black, white], size: [s, m]}
            ->map(fn(array $attrs) => collect($attrs)->unique()->values())

            // Tiền xử lí trước khi chuyển hoàn toàn các tùy chọn về dạng json
            ->pipe(function (Collection $options) use ($product){
                if (isset($product['name']) &&
                isset($product['supplier_id']) &&
                $this->checkProductAllowRefactor($product['name'])) {
                    return $options;
                }

                // Khi sản phẩm không có tùy chọn màu sắc thì mặc định sẽ có màu trắng
                if ($options->get('color', collect([]))->isEmpty()) {
                    $options->put('color', collect(['white']));
                }

                return $options;
            })
            ->toJson();

        // clear các trường không tồn tại trong bảng product_variant
        $product['variants'] = array_map(static function($variant) {
            unset($variant['separate_options'], $variant['options']);

            return $variant;
        }, $product['variants']);

        return $product;
    }

    /**
     * @param     array     $products
     *
     * @return array
     */
    public function beforeSaveFulfillProducts(array $products): array
    {
        return $products;
    }

    /**
     * @param     bool      $bool
     * @param     array     $products
     *
     * @return void
     */
    public function afterSaveFulfillProducts(bool $bool, array $products): void
    {
        $this->clearCacheTemplate();
    }

    /**
     * @param     array     $products
     *
     * @return bool
     */
    public function saveFulfillProducts(array $products): bool
    {
        try {
            $products = $this->save($products);

            // Danh sách id của các sản phẩm mới được thêm vào
            $productIDs = array_column($products, 'id');

            // Xoá các mapping sản phẩm cũ (mapping giữa template và sản phẩm của supplier)
            $this->deleteProductFulfillMapping($this->getSupplierId(), $productIDs);

            // Inactive các sản phẩm cũ
            $this->deActiveOldFulfillProductIds($this->getSupplierId(), $productIDs);

            // Active các sản phẩm mới
            $this->activeNewFulfillProductIds($this->getSupplierId(), $productIDs);

            // Xoá các biến thể cũ
            $this->deleteVariantByProductIDs($productIDs);

            // Insert variant
            collect($products)
                ->pluck('variants')
                ->flatten(1)
                ->chunk(1000)
                ->each(fn ($variants) => $this->insertVariant($variants->all()));

            // Xoá cache template
            $this->clearCacheTemplate($this->getSupplierId(), $productIDs);

            return true;
        } catch (Throwable $e) {
            logException($e, __FUNCTION__, 'fulfill_product', true);

            return false;
        }
    }

    /**
     * @param array $products
     *
     * @return array
     */
    public function save(array $products): array
    {
        foreach ($products as $idx => $p) {
            $insertedProduct = FulfillProduct::updateOrCreate([
                'sku'         => $p['sku'],
                'supplier_id' => $this->getSupplierId(),
            ], $p);

            $p['id'] = $insertedProduct->id;

            foreach ($p['variants'] as $key => $variant) {
                $p['variants'][$key]['product_id'] = $insertedProduct->id;
            }

            $products[$idx] = $p;
        }

        return $products;
    }

    /**
     * @param     \App\Providers\FulfillAPI\AbstractResponseModel               $rm
     * @param     array                                                         $product
     * @param     \App\Providers\FulfillAPI\Common\CrawlProductVariantModel     $vm
     *
     * @return array
     * @throws \Throwable
     */
    public function extractResponseAndMapVariantFields(AbstractResponseModel $rm, array $product, CrawlProductVariantModel $vm): array
    {
        $result = [];
        foreach ($rm->getResponseData() as $rawVariant) {
            if (!$this->isValidRawVariant($rawVariant, $product)) {
                $this->variantNotFoundSku($rawVariant, $product);
            } else {
                array_push($result, ...$vm->mapping($rawVariant, $product));
            }
        }
        return $result;
    }

    /**
     * @param     array     $rawVariant
     * @param     array     $product
     *
     * @return bool
     */
    public function isValidRawVariant(array $rawVariant, array $product): bool
    {
        return true;
    }

    /**
     * @param array $rawVariant
     * @param array $product
     *
     * @return void
     */
    public function variantNotFoundSku(array $rawVariant, array $product): void
    {
        echo sprintf('%s | %s <br>', $rawVariant['id'] ?? 'x', $product['name']);
    }

    /**
     * @param int $supplierId
     *
     * @return void
     */
    public function deActiveAllFulfillProductBySupplierId(int $supplierId): void
    {
        FulfillProduct::query()
            ->where('supplier_id', $supplierId)
            ->update(['status' => ProductStatus::INACTIVE]);
    }

    /**
     * @param $sendData
     *
     * @return bool
     *
     * @throws Throwable
     */
    protected function handleCancelOrderResponse($sendData): bool
    {
        return true;
    }

    /**
     *
     * @return bool
     */
    public function supplierAllowAdditionPrintSpace(): bool
    {
        $providers = self::getConfigProvider();

        foreach ($providers as $provider) {
            if (
                (int)$this->supplier_id === $provider['supplier_id'] &&
                !empty($provider['addition_print_space']) &&
                $provider['addition_print_space']
            ) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param $option
     * @return void
     */
    public function unsetOptions(&$options)
    {
        $providers = self::getConfigProvider();
        $config = collect($providers)->filter(function ($provider) {
            return $provider['supplier_id'] == $this->supplier_id;
        })->first();
        $unsetOptions = Arr::get($config, 'unset_options');
        if (empty($unsetOptions)) {
            unset($options['gift_wrap'], $options['box']);
            return;
        }
        foreach ($options as $key => $optionInput) {
            $configOption = Arr::get($unsetOptions, $key);
            if (!empty($configOption) && $configOption) {
                unset($options[$key]);
            }
        }
    }

    public function setupTestingConfig ($order, $supplierId) {
        $suppliersAllow = [
            SupplierEnum::PRINTIK
        ];
        try {
            if (!in_array($supplierId, $suppliersAllow) || empty($order)) {
                return;
            }
            $adminNote = $order?->admin_note;
            $supplierTestingNote = config('supplier.printik.testing_note');
            if (!empty($adminNote) && str_contains($adminNote, $supplierTestingNote)) {
                $this->setIsTesting(true);
            }
        } catch (\Exception $e) {
            logToDiscord('Error in setupTesting post order sup: ' . $e->getMessage(), 'fulfill_product', true);
        }
    }

    /**
     * @param $products
     * @return array
     */
    public function validateProductsPrepareToSupplier ($products) {
        return [
            'validate' => true,
            'message' => '',
            'data' => []
        ];
    }

    /**
     * @param $body
     * @return void
     */
    protected function handleAdditionalHeaders($body): void
    {
    }
}

<?php

namespace App\Providers\FulfillAPI;

use App\Enums\DiscordChannel;
use App\Enums\FileTypeEnum;
use App\Enums\FulfillMappingEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductOptionEnum;
use App\Enums\ProductType;
use App\Models\FulfillMapping;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Services\StoreService;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use RuntimeException;

abstract class AbstractModel
{
    protected const SUPPORT_PHONE = '16504107920';
    protected const SUPPORT_EMAIL = '<EMAIL>';
    public const ORDER_NOTE_TEXT = 'Note:';
    public const MAILBOX_NUMBER_TEXT = 'MBox #:';
    public const HOUSE_NUMBER_TEXT = 'House #:';
    protected string $mode = 'dev';
    protected bool $reOrder = false;

    protected $supplier_id;
    protected string $fulfill_order_id = '';
    protected string $order_id = '';
    protected string $product_type = ProductType::FULFILL_PRODUCT;
    protected array $mapped_shipping_methods = [];
    protected array $customer_name = [
        'first_name' => '',
        'last_name'  => '',
    ];
    protected array $customer_address = [
        'full'                  => '',
        'primary'               => '',
        'addition'              => '',
        'addition_original'     => '',
        'mailbox_number'        => '',
        'mailbox_number_text'   => '',
        'house_full_number'     => '',
        'house_number'          => '',
        'house_number_text'     => '',
        'house_number_addition' => '',
        'street'                => '',
        'state'                 => '',
        'state_code'            => '',
        'city'                  => '',
    ];
    protected string $currencyCode = 'USD';
    protected array $new_colors = [];
    protected array $arrKeySeparates = [];
    protected ?array $arrKeySeparatesClone = null;
    private ?string $webhook_token;


    /**
     * @param     string|null     $token
     *
     * @return void
     */
    public function setWebHookToken(?string $token): void
    {
        $this->webhook_token = $token;
    }

    /**
     * @return string|null
     */
    public function getWebhookToken(): ?string
    {
        return $this->webhook_token;
    }

    //    get public attributes
    public function getPublicAttrs(): array
    {
        $me = new class {
            public function getPublicAttrs($object): array
            {
                return get_object_vars($object);
            }
        };

        return $me->getPublicAttrs($this);
    }

    //    get variable's attributes
    public function getAttrs(): array
    {
        return get_object_vars($this);
    }

    public function setSupplierId($supplierId): void
    {
        $this->supplier_id = $supplierId;
    }

    protected function getByMapping(
        string $value,
        string $type,
        ?string $location = null,
        bool $strict = false
    ): string {
        $arrMapping = FulfillMapping::getAndCache($type);

        if (PrintSpaceEnum::checkIfSameWithFront($value)) {
            $value = 'front';
        }

        $arrFiltered = $arrMapping->where('object_id', $value)
            ->where('supplier_id', $this->supplier_id);

        $fulfillMapping = $arrFiltered->first(
            fn($item) => in_array($location, explode(',', $item->location)),
            $arrFiltered->firstWhere('location', null)
        );

        if (is_null($fulfillMapping)) {
            return $strict ? '' : $value;
        }

        return $fulfillMapping->external_id ?? '';
    }

    protected function getReferenceId($obj, $separateSymbol = null): string
    {
        if (!$this->reOrder && is_null($separateSymbol)) {
            return $obj->id;
        }

        $separateSymbol ??= '-';

        return $obj->id . $separateSymbol . time();
    }

    /**
     * @param     string     $id
     * @param     string     $separateSymbol
     *
     * @return string
     * @throws Exception
     */
    public function toReferenceId(string $id, string $separateSymbol = '-'): string
    {
        return $id . $separateSymbol . substr(
            md5(uniqid($id . $separateSymbol, true)), random_int(1, 9), 6
        );
    }

    public function setConfigOrder(Order $order): void
    {
        $this->setCustomerName($order);
        $this->setCustomerAddress($order);
        $this->setConfigShippingMethod($order);
        $this->setCurrencyCode($order);
    }

    protected function setCurrencyCode(Order $order): void
    {
        $this->currencyCode = $order->currency_code;
    }

    protected function setConfigShippingMethod(Order $order): void
    {
        // name:id
        $this->mapped_shipping_methods = explode(
            ':',
            $this->getByMapping(
                $order->shipping_method,
                FulfillMappingEnum::SHIPPING_METHOD,
                $order->country,
            )
        );
    }

    protected function setCustomerName(Order $order): void
    {
        // fix for case some supplier require the first name
        if (is_null($order->customer_name) && $order->type === OrderTypeEnum::FBA) {
            $order->customer_name = '_';
        }
        $name                              = explode(' ', $order->customer_name);
        $this->customer_name['first_name'] = array_shift($name);
        $this->customer_name['last_name']  = !empty($name) ? implode(' ', $name) : '_'; // _ if empty
    }

    public function resetCustomerAddress()
    {
        $this->customer_address = [
            'full'                  => '',
            'primary'               => '',
            'addition'              => '',
            'addition_original'     => '',
            'mailbox_number'        => '',
            'mailbox_number_text'   => '',
            'house_full_number'     => '',
            'house_number'          => '',
            'house_number_text'     => '',
            'house_number_addition' => '',
            'street'                => '',
            'state'                 => '',
            'state_code'            => '',
            'city'                  => '',
        ];
    }

    protected function setCustomerAddress(Order $order): void
    {
        $this->customer_address['state']      = Str::ascii($order->state ?? '');
        $this->customer_address['state_code'] = $this->getStateCode($order);
        $this->customer_address['city']       = Str::ascii($order->city);

        $address  = Str::ascii($order->address);
        $address2 = Str::ascii($order->address_2);
        $address  = preg_replace('/\s*[\'\"]\s*/', '', $address);
        $address2 = preg_replace('/\s*[\'\"]\s*/', '', $address2);

        // ex: floor2, 3A Times City
        $this->customer_address['full']     = !empty($address2) ? $address . ', ' . $address2 : $address;
        $this->customer_address['primary']  = $address;        // 3A Times City
        $this->customer_address['addition'] = $address2 ?? ''; // floor2
        $this->customer_address['addition_original'] = $address2 ?? ''; // floor2

        if (stripos($address, 'packstation') !== false) {
            // 20186945 Packstation 203
            $this->customer_address['street'] = 'Packstation';
            preg_match_all('/\d+/', $address, $matches);
            $this->customer_address['house_full_number']     = Arr::get($matches, '0.0', ''); // 20186945
            $this->customer_address['house_number']          = Arr::get($matches, '0.0', ''); // 20186945
            $this->customer_address['house_number_addition'] = Arr::get($matches, '0.1', ''); // 203
            return;
        }

        $this->customer_address['street'] = preg_replace(
            '/\d+/',
            '',
            $address
        ); // Times City

        $matches = explode(' ', $address);
        $matches = array_filter($matches, static function ($match) {
            return
                !empty($match)
                &&
                !in_array($match, [
                    ',',
                    '.',
                    '-',
                    '/',
                    '\\',
                    ' ',
                ]);
        });
        foreach ($matches as $index => $match) {
            if (preg_match('/\d+/', $match)) {
                $this->customer_address['house_full_number']     = $match; // 3A
                $this->customer_address['house_number']          = filter_var(
                    $match,
                    FILTER_SANITIZE_NUMBER_INT
                ); // 3
                $this->customer_address['house_number_addition'] = preg_replace(
                    '/[^a-zA-Z]+/',
                    '',
                    $match
                ); // A
                unset($matches[$index]);
                break;
            }
        }
        if (count($matches) > 1) {
            $this->customer_address['street'] = implode(' ', $matches); // Times City
        }
        $this->setHouseAndMailboxNumber($order);
    }

    /**
     * @param Order $order
     * @return void
     */
    public function setHouseAndMailboxNumber(Order $order): void
    {
        if (!empty($order->mailbox_number)) {
            $this->customer_address['mailbox_number'] = $order->mailbox_number;
            $this->customer_address['mailbox_number_text'] = self::MAILBOX_NUMBER_TEXT . $order->mailbox_number;
            $this->customer_address['full'] .= ' (' . $this->customer_address['mailbox_number_text'] . ')';
            $this->customer_address['addition'] .= ' (' . $this->customer_address['mailbox_number_text'] . ')';
        }
        if (!empty($order->house_number)) {
            $this->customer_address['house_number'] = filter_var($order->house_number, FILTER_SANITIZE_NUMBER_INT);
            $this->customer_address['house_full_number'] = $order->house_number;
            $this->customer_address['house_number_text'] = self::HOUSE_NUMBER_TEXT . $order->house_number;
            $this->customer_address['full'] .= ' (' . $this->customer_address['house_number_text'] . ')';
            $this->customer_address['addition'] .= ' (' . $this->customer_address['house_number_text'] . ')';
        }
        if (!empty($order->order_note)) {
            $this->customer_address['addition'] .= ' (' . self::ORDER_NOTE_TEXT . $this->getOrderNote($order) . ')';
        }
    }

    private array $state_US = [
        'Alabama'                                => 'AL',
        'Alaska'                                 => 'AK',
        'Arizona'                                => 'AZ',
        'Arkansas'                               => 'AR',
        'California'                             => 'CA',
        'Colorado'                               => 'CO',
        'Connecticut'                            => 'CT',
        'Delaware'                               => 'DE',
        'District Of Columbia'                   => 'DC',
        'Florida'                                => 'FL',
        'Georgia'                                => 'GA',
        'Hawaii'                                 => 'HI',
        'Idaho'                                  => 'ID',
        'Illinois'                               => 'IL',
        'Indiana'                                => 'IN',
        'Iowa'                                   => 'IA',
        'Kansas'                                 => 'KS',
        'Kentucky'                               => 'KY',
        'Louisiana'                              => 'LA',
        'Maine'                                  => 'ME',
        'Maryland'                               => 'MD',
        'Massachusetts'                          => 'MA',
        'Michigan'                               => 'MI',
        'Minnesota'                              => 'MN',
        'Mississippi'                            => 'MS',
        'Missouri'                               => 'MO',
        'Montana'                                => 'MT',
        'Nebraska'                               => 'NE',
        'Nevada'                                 => 'NV',
        'New Hampshire'                          => 'NH',
        'New Jersey'                             => 'NJ',
        'New Mexico'                             => 'NM',
        'New York'                               => 'NY',
        'North Carolina'                         => 'NC',
        'North Dakota'                           => 'ND',
        'Ohio'                                   => 'OH',
        'Oklahoma'                               => 'OK',
        'Oregon'                                 => 'OR',
        'Pennsylvania'                           => 'PA',
        'Rhode Island'                           => 'RI',
        'South Carolina'                         => 'SC',
        'South Dakota'                           => 'SD',
        'Tennessee'                              => 'TN',
        'Texas'                                  => 'TX',
        'Utah'                                   => 'UT',
        'Vermont'                                => 'VT',
        'Virginia'                               => 'VA',
        'Washington'                             => 'WA',
        'West Virginia'                          => 'WV',
        'Wisconsin'                              => 'WI',
        'Wyoming'                                => 'WY',
        'American Samoa'                         => 'AS',
        'Guam'                                   => 'GU',
        'Northern Mariana Islands'               => 'MP',
        'Puerto Rico'                            => 'PR',
        'U.S. Virgin Islands'                    => 'VI',
        'U.S. Minor Outlying Islands'            => 'UM',
        'Marshall Islands'                       => 'MH',
        'Micronesia'                             => 'FM',
        'Palau'                                  => 'PW',
        'U.S. Armed Forces – Americas'           => 'AA',
        'U.S. Armed Forces – Europe'             => 'AE',
        'U.S. Armed Forces – Pacific'            => 'AP',
        'Panama Canal Zone'                      => 'CZ',
        'Philippine Islands'                     => 'PI',
        'Trust Territory Of The Pacific Islands' => 'TT',
    ];

    private function getStateCode(Order $order): string
    {
        if (is_null($order->state)) {
            return '';
        }

        $state = ucwords(trim($order->state));
        if ($order->country === 'US' && isset($this->state_US[$state])) {
            return $this->state_US[$state] ?? '';
        }

        $state = strtoupper(substr($state, 0, 2));
        return Str::ascii($state) ?? '';
    }

    protected function addNewColor(array $color): void
    {
        if (empty($color['name'])) {
            return;
        }
        $arrColors = StoreService::systemColors();
        if (!$arrColors || empty($arrColors->firstWhere('name', $color['name']))) {
            $this->new_colors[] = [
                'name'              => $color['name'],
                'supplier_color_id' => $color['supplier_color_id'] ?? null,
                'supplier_id'       => $this->supplier_id,
                'hex_code'          => $color['hex_code'] ?? null,
                'link'              => $color['link'] ?? null,
            ];
        }
    }

    public const ARR_MAPPING = [
        ProductOptionEnum::SIZE => [
            'small'         => 's',
            'medium'        => 'm',
            'large'         => 'l',
            'xlarge'        => 'xl',
            '2xlarge'       => '2xl',
            '3xlarge'       => '3xl',
            '4xlarge'       => '4xl',
            'xxl'           => '2xl',
            'xxxl'          => '3xl',
            'xxxxl'         => '4xl',
            'xxxxxl'        => '5xl',
            '2x'            => '2xl',
            '3x'            => '3xl',
            '4x'            => '4xl',
            '5x'            => '5xl',
            'small30x40in'  => '30x40in',
            'medium50x60in' => '50x60in',
            'large60x80in'  => '60x80in',
            'xsin'          => 'xs',
            '56t'           => '5_6t',
            '5/6'           => '5_6t',
            56              => '5_6t',
            7               => '7t',
            // '3m'            => 'new born',
        ],
        ProductOptionEnum::COLOR => [
            'sport gray'        => 'sport grey',
            'sporty grey'       => 'sport grey',
            'heather grey'      => 'sport grey',
            'athletic heather'  => 'sport grey',
            'azaela'            => 'azalea',
            'true royal'        => 'royal',
            'royal blue'        => 'royal',
            'sky blue'          => 'light blue',
            'rot'               => 'red',
            'green'             => 'irish green',
            'arctic white'      => 'white',
            'jet black'         => 'black',
            'oxford navy'       => 'navy',
            'fire red'          => 'red',
            'kelly'             => 'kelly green',
            'navy blue'         => 'navy',
            'dark heather grey' => 'dark heather',
            'forest'            => 'forest green',
            'carolina blue'     => 'carolina',
            'white vanilla'     => 'white',
            'as design'         => null,
            'same design'       => null,
            'forestgreen'       => 'forest green',
            'sportgrey'         => 'sport grey',
            'lightpink'         => 'light pink',
            'militarygreen'     => 'military green',
            'royalblue'         => 'royal',
            'ashgrey'           => 'ash',
            'darkheather'       => 'dark heather',
            'lightblue'         => 'light blue',
            'baby blue'         => 'light blue',
        ],
    ];

    public static function mappingOptions($option, $type = 'all', $addition = '', $hadParsed = false): ?string
    {
        if (!$hadParsed) {
            $option = self::parseOption($option);
        }

        switch ($type) {
            // don't mapping
            case 'none':
                return $option;
            case ProductOptionEnum::COLOR:
                $arrMapping = self::ARR_MAPPING[$type];
                break;
            case ProductOptionEnum::SIZE:
                $arrMapping = self::ARR_MAPPING[$type];
                $option     = preg_replace(
                    [
                        '/\s+/',
                        '/round/',
                        '/inch/',
                        '/ines/',
                        '/inindiameter/',
                        '/months/',
                    ],
                    [
                        '',
                        '',
                        'in',
                        'in',
                        'in',
                        'm',
                    ],
                    $option
                );
                // 15"x15" => 15x15in. 15" => 15in. 15"x15"x15" => 15x15x15in
                // 15" => 15in
                // 15"x15"x15" => 15x15x15in
                if (
                    !Str::contains($option, 'xl')
                    && Str::contains($option, '"')
                    && Str::contains($option, 'x')
                ) {
                    $option = str_replace('"', '', $option);
                    $option = Str::finish($option, 'in');
                }
                break;
            default:
                $arrMapping = self::ARR_MAPPING;
                break;
        }

        if (is_numeric($option)) {
            $option += 0;
        }

        foreach ($arrMapping as $key => $each) {
            if (is_array($each) && isset($each[$option])) {
                return $each[$option];
            }
            if ($key === $option) {
                return $each;
            }
        }

        return $option;
    }

    public function setMode($mode): void
    {
        //change mode: debug or production
    }

    public function getFulfillOrderId(): string
    {
        return $this->fulfill_order_id;
    }

    public function getOrderId(): string
    {
        return $this->order_id;
    }

    public static function parseOption($option): string
    {
        // ex: blue-white red/ => blue white red
        // ex: 15" x 14" => 15"x14"
        // ex: 60.5 => 60-5
        $option = preg_replace(
            [
                '/\.$/',
                '/[-_]/',
                '/[.,]/',
                '/[^a-z0-9\" \-]/',
            ],
            [
                '',
                ' ',
                '-',
                '',
            ],
            strtolower(
                trim(
                    $option
                )
            )
        );

        return preg_replace(
            [
                '/\s+/',
            ],
            [
                ' ',
            ],
            $option
        );
    }

    public function getNewColors(): array
    {
        return $this->new_colors;
    }

    protected function getProductFulfill($product): object
    {
        $product = Product::query()
            ->select([
                'name',
                'sku',
            ])
            ->whereKey($product->fulfill_product_id)
            ->first();
        if (empty($product)) {
            throw new RuntimeException('Product not found');
        }

        return $product;
    }

    // availability of variant
    protected const IN_STOCK = "in_stock";

    protected const JIT = "jit";
    protected const ON_STOCK = "On Stock";
    protected const OUT_OF_STOCK = "out_of_stock";
    protected const DISCONTINUED = "discontinued";
    protected const BACKORDER = "backorder";
    protected const AT_RISK = "at_risk";
    protected const TEMPORARILY_UNAVAILABLE = "temporarily_unavailable";

    protected static function getOutOfStock(string $status): bool
    {
        switch ($status) {
            case true:
            case self::IN_STOCK:
            case self::ON_STOCK:
            case self::AT_RISK:
            case self::JIT:
                return 0;
            case '':
            case self::OUT_OF_STOCK:
            case self::BACKORDER:
            case self::TEMPORARILY_UNAVAILABLE:
            case self::DISCONTINUED:
                return 1;
            default:
                logToDiscord(
                    'Have new availability: ' . $status
                    , DiscordChannel::FULFILL_ORDER
                );
                return 1;
        }
    }

    public function setReOrder($reOrder): void
    {
        $this->reOrder = $reOrder;
    }

    protected function getDesignUrl($file): string
    {
        $url = $file->design_url;
        if ($this->reOrder) {
            $url .= (Str::contains($url, '?')) ? '&' : '?';
            $url .= 't=' . time();
        }
        return $url;
    }

    protected function getMockupUrl($file): string
    {
        $url = $file->mockup_url;
        if ($this->reOrder) {
            $url .= (Str::contains($url, '?')) ? '&' : '?';
            $url .= 't=' . time();
        }

        return $url;
    }

    protected function setAndGetOption(
        $model,
        $type,
        $option,
        $product,
        &$options = [],
        &$optionsProduct = []
    ): string {
        if (!isset($optionsProduct[$type])) {
            $optionsProduct[$type] = [];
        }

        $val = $model::mappingOptions(
            $option,
            $type,
            $product,
        );

        if (empty($val)) {
            return '';
        }


        if (!in_array($val, $options)) {
            $options[] = $val;
        }
        if (!in_array($val, $optionsProduct[$type])) {
            $optionsProduct[$type][] = $val;
        }

        return $val;
    }

    /**
     * @noinspection PhpSwitchStatementWitSingleBranchInspection
     * @noinspection DegradedSwitchInspection
     */
    protected static function convertMeasurementToPound(float $unit, $measurement = 'gram'): float
    {
        switch ($measurement) {
            case 'gram':
                return $unit * 0.00220462;
        }

        return $unit;
    }

    protected function getOptionSeparateProduct(&$options, ?string $productName = null): array
    {
        $optionSeparateProduct = [];
        $arrKeySeparates = $this->arrKeySeparatesClone ?? $this->arrKeySeparates;
        foreach ($arrKeySeparates as $key) {
            if (isset($options[$key])) {
                $optionSeparateProduct[$key] = $options[$key];
                unset($options[$key]);
            }
        }

        return $optionSeparateProduct;
    }

    /**
     * @param Collection $files
     *
     * @return array
     */
    public function getFrontBackDesign(Collection $files): array
    {
        return $files->reduce(static function($designs, $file) {
            if ($file->type === FileTypeEnum::DESIGN) {
                if (!$designs['front'] && $file->print_space === 'front') {
                    $designs['front'] = $file;
                }

                if (!$designs['back'] && $file->print_space === 'back') {
                    $designs['back'] = $file;
                }

                if (!$designs['left_sleeve'] && $file->print_space === 'left_sleeve') {
                    $designs['left_sleeve'] = $file;
                }

                if (!$designs['right_sleeve'] && $file->print_space === 'right_sleeve') {
                    $designs['right_sleeve'] = $file;
                }

                if (!$designs['right_chest'] && $file->print_space === 'right_chest') {
                    $designs['right_chest'] = $file;
                }

                if (!$designs['left_chest'] && $file->print_space === 'left_chest') {
                    $designs['left_chest'] = $file;
                }
            }
            return $designs;
        }, ['front' => null, 'back'=> null, 'left_sleeve' => null, 'right_sleeve' => null, 'right_chest' => null, 'left_chest' => null]);
    }

    /**
     * @param Order $order
     * @param bool $fullText
     * @return string
     */
    public function getOrderNote(Order $order, bool $fullText = false): string
    {
        if (empty($order->order_note)) {
            return '';
        }
        if (!$fullText) {
            return $order->order_note;
        }
        return '(' . self::ORDER_NOTE_TEXT . $order->order_note . ')';
    }

    /**
     * @param Collection $files
     *
     * @return array
     */
    public function getDesignOnPrintSpaces(Collection $files): array
    {
        return $files->reduce(static function($designs, $file) {
            if ($file->type === FileTypeEnum::DESIGN) {
                $user = User::query()->select('tags')->find($file?->user_id);
                if (isset($user) && !str_contains($user->tags, 'sleeve printspace') && in_array($file->print_space, PrintSpaceEnum::additionalPrintSpaces(), true)) {
                    return $designs;
                }

                if (!$designs['front'] && $file->print_space === 'front') {
                    $designs['front'] = $file;
                }

                if (!$designs['back'] && $file->print_space === 'back') {
                    $designs['back'] = $file;
                }

                if (!$designs['right_sleeve'] && $file->print_space === 'right_sleeve') {
                    $designs['right_sleeve'] = $file;
                }

                if (!$designs['left_sleeve'] && $file->print_space === 'left_sleeve') {
                    $designs['left_sleeve'] = $file;
                }
            }
            return $designs;
        }, ['front' => null, 'back'=> null, 'right_sleeve' => null, 'left_sleeve' => null]);
    }

    /**
     * @return string
     */
    public function getBaseWebhookUrl(): string
    {
        return rtrim(env('APP_URL', 'https://api-sg.senprints.net'), '/') . '/fulfill/order/%s';
    }
}

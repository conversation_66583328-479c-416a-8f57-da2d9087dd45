<?php

namespace App\Providers\FulfillAPI\Pentifine\Model;

use App\Enums\EnvironmentEnum;
use App\Enums\ShippingMethodEnum;
use App\Models\OrderProduct;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    public string $type = 'DTF';
    public array $payload = [];
    public const MAPPING_PRINT_SPACE = [
        'front' => 'FRONT',
        'back' => 'BACK',
        'left_sleeve' => 'LEFT_SLEEVE',
        'right_sleeve' => 'RIGHT_SLEEVE',
    ];

    /**
     * {
     * "address": {
     * "first_name": "Test", // string - required
     * "last_name": "Test", // string - required
     * "phone": "**********", // string - required
     * "email": "<EMAIL>", // string - required
     * "street1": "4160 E Warren Avenue", // string - required
     * "street2": "unit2", // string - optional
     * "city": "Denver", // string - required
     * "state": "CO", // string - required
     * "country": "US", // string - required
     * "zip": "80222" // string - required
     * },
     * "order_type": "ship-by-merchant", // string - required - enum: ship-by-merchant
     * "platform": "shopbase", // string - optional - enum: tiktok, amazon, etsy, ebay, shopify, shopbase, orderdesk, wordpress, website
     * "tax_number": "123", // string - optional - Add a TAX number for UK addresses or an IOSS number for EU addresses.
     * "tax_value": "5", // string - optional
     * "store_code": "STORE000008", // string - required
     * "reference_id": "Example", // string - required
     * "priority": "rush", // string - optional
     * "shipping_service": "standard", // string - required
     * "line_items": [ // array of objects - required
     * {
     * "item_variant_id": "18494", // string - required
     * "quantity": 1, // number - required
     * "reference_id": "test001", // string - optional
     * "print_type": "DTG", // string - required with Apparel - enum: DTF, DTG
     * "print_areas": [ // array of objects - required  - The item areas that you want to print on.
     * // - enum: FRONT, BACK, POCKET, LEFT_SLEEVE, RIGHT_SLEEVE, OUTSIDE_LABEL, NECK, FULL_BLEED.
     * {
     * "key": "FRONT",
     * "url": "https://..."
     * },
     * {
     * "key": "BACK",
     * "url": "https://..."
     * },
     * {
     * "key": "POCKET",
     * "url": "https://..."
     * },
     * {
     * "key": "LEFT_SLEEVE",
     * "url": "https://..."
     * },
     * {
     * "key": "RIGHT_SLEEVE",
     * "url": "https://..."
     * },
     * {
     * "key": "NECK",
     * "url": "https://..."
     * },
     * {
     * "key": "OUTSIDE_LABEL",
     * "url": "https://..."
     * }
     * ]
     * }
     * ]
     * }'
     */

    /**
     * @param $order
     *
     * @return void
     */
    public function setOrder($order): void
    {
        $shippingLabel = $order->shipping_label;
        $isLabelOrder = !empty($shippingLabel);
        $shippingLabelUrl = $isLabelOrder ? s3Url($shippingLabel) : "";
        $this->fulfill_order_id = $this->order_id = $this->getReferenceId($order);
        $environment = app()->environment(EnvironmentEnum::PRODUCTION) ? "production" : "dev";
        $this->payload = [
            'order_type' => 'ship-by-merchant',
            'reference_id' => app()->environment(EnvironmentEnum::PRODUCTION) ? $this->order_id : 'TEST-'.$this->order_id,
            'shipping_service' => 'standard',
            'store_code' => config("supplier.pentifine.$environment.store_code"),
            'address' => [
                'first_name' => $this->customer_name['first_name'],
                'last_name' => $this->customer_name['last_name'],
                'phone' => $order->customer_phone,
                'email' => $order->customer_email,
                'street1' => $this->customer_address['primary'],
                'street2' => $this->customer_address['addition'] ?? '',
                'city' => $this->customer_address['city'],
                'state' => $this->customer_address['state_code'],
                'country' => $order->country_info->code,
                'zip' => $order->postcode
            ],
        ];

        if ($isLabelOrder) {
            $this->payload['order_type'] = 'ship-by-label';
            $this->payload['shipping_label_url'] = $shippingLabelUrl;
            unset($this->payload['address']);
        }

        if (!empty($order->shipping_method) && $order->shipping_method === ShippingMethodEnum::EXPRESS) {
            $this->payload['priority'] = 'rush';
        }
    }

    /**
     * @param OrderProduct $product
     *
     * @return void
     */
    public function setItems(OrderProduct $product): void
    {
        $designs = $this->getFrontBackDesign($product->files);
        $lineItems = [
            "item_variant_id" => $product->fulfill_sku,
            "quantity" => $product->quantity,
            "print_type" => $this->type,
        ];
        $printAreas = [];
        foreach ($designs as $printSpace => $data) {
            $printSpaceToSup = data_get(self::MAPPING_PRINT_SPACE, $printSpace);
            if (empty($printSpaceToSup) || empty($data->design_url)) {
                continue;
            }
            $printAreas [] = [
                'key' => $printSpaceToSup,
                'url' => $data->design_url ?? '',
            ];
        }
        $lineItems['print_areas'] = $printAreas;
        $this->payload['line_items'][] = $lineItems;
    }
}

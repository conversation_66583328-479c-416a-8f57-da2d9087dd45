<?php

namespace App\Providers\FulfillAPI\Textildruck;

use App\Enums\SupplierEnum;
use App\Services\OrderService;
use Illuminate\Support\Str;

/**
 * Class CustomerAddress
 * @issue https://senprints.atlassian.net/browse/SDV-5051
 */
class CustomerAddress extends \App\Providers\FulfillAPI\CustomerAddress
{
    /**
     * Thứ tự ưu tiên head > last > middle
     * VD: 123 dia chi 456 nao do 789 => 123
     * VD: dia chi 456 nao do => 456
     * VD: 123 dia chi 456 nao do 789 => 789
     * VD: dia chi 456 nao do 789 nao do => 789 (ưu tiên last) nếu ko có head, last
     *
     * @return CustomerAddress
     */
    public function handleStreetAndNumber(): static
    {
        if (stripos($this->primary, 'packstation') !== false) {
            return $this->separateCasePackstation();
        }

        if (empty($this->house_number)) {
            $this->street = preg_replace('/\d+/', '', $this->primary);
            if (preg_match('/^(\S*\d+\S*)(?:\s.+|$)/', $this->primary, $matches)) {
                $this->house_full_number = $matches[1];
            } else if (preg_match('/(?:|.+\s)(\S*\d+\S*)$/', $this->primary, $matches)) {
                $this->house_full_number = $matches[1];
            } else if (preg_match('/(?:|.+\s)(\S*\d+\S*)(?:\s.+?|$)/', $this->primary, $matches)) {
                $this->house_full_number = $matches[1];
            }
            if ($this->house_full_number) {
                $this->house_number = preg_replace('/[a-zA-Z]/', '', $this->house_full_number);
                $this->house_number_addition = preg_replace('/[^a-zA-Z]+/', '', $this->house_full_number);
                $this->street = Str::of($this->primary)
                    ->explode(' ')
                    ->reject(static fn ($v) => empty($v))
                    ->reject(static fn ($v) => in_array($v, [',', '.', '-', '/', '\\', ' '], true))
                    ->reject(fn ($v) => $v === $this->house_full_number)
                    ->pipe(static fn($segments) => trim($segments->implode(' ')));
            }
        } else {
            $this->street = $this->primary;
            $houseNumberIndexInAddress = $this->isHouseNumberInAddress($this->house_number, $this->street);
            if (isset($houseNumberIndexInAddress)) {
                $oldStreet = $this->street;
                if ($houseNumberIndexInAddress > 0) {
                    $newStreet = substr($this->street, 0, $houseNumberIndexInAddress) . substr($this->street, $houseNumberIndexInAddress + strlen($this->house_number) + 1);
                } else {
                    $newStreet = substr($this->street, $houseNumberIndexInAddress + strlen($this->house_number));
                }
                $newStreet = trim($newStreet);
                $specialChars = ",. \t\n\r\0\x0B";
                $newStreet = trim($newStreet, $specialChars);
                $this->street = $newStreet;
                OrderService::logToDiscordHouseNumberInAddress($this->order->id,
                    SupplierEnum::TEXTILDRUCK,
                    "Detect house number included inside address. House number : $this->house_number. Old street : $oldStreet. New street : $newStreet");
            }
        }
        return $this;
    }
}

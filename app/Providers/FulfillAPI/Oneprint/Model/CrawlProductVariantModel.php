<?php

namespace App\Providers\FulfillAPI\Oneprint\Model;

use App\Enums\ProductOptionEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;

class CrawlProductVariantModel extends AbstractModel
{
    public static function mapping(array $variant, array $options): array
    {
        $color = data_get($options, 'color');
        $size = data_get($options, 'size');
        $pack = data_get($options, 'pack');

        $status = data_get($variant, 'status');
        $options = [];
        if (!empty($color)) {
            $options['color'] = static::mappingOptions($color, ProductOptionEnum::COLOR);
        }

        if (!empty($size)) {
            $options['size'] = $size;
        }

        if (!empty($pack)) {
            $options['pack'] = $pack;
        }
        return [
            'sku' => data_get($variant, 'sku'),
            'sku_bak' => data_get($variant, 'sku'),
            'base_cost' => data_get($variant, 'price'),
            'options' => $options,
            'variant_key' => getVariantKey($options),
            'out_of_stock' => $status && $status !== 'active',
        ];
    }
}

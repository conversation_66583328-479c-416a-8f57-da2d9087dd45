<?php

namespace App\Providers\FulfillAPI\Oneprint\Model;

use App\Enums\OrderProductFulfillStatus;
use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\OneprintWebhookRepository;

class ResponseModel extends AbstractResponseModel
{
    public const INCOMPLETE = 'INCOMPLETE';
    public const SHIPPED = 'SHIPPED';
    public const CANCELED = 'CANCELLED';
    public const PROCESSING = 'PROCESSING';
    public const UNPAID = 'UNPAID';

    protected string $orderExistedRegex = '/An order already exists with your order code\. Your order code: (\d+(?:-\d+)?)$/';

    /**
     * @param array $item
     *
     * @return array
     */
    private function findProduct(array $item): array
    {
        if (!array_key_exists('isGroup', $item)) {
            return [];
        }

        if ($item['isGroup'] === false) {
            return [$item];
        }

        if (!empty($item['variants'])) {
            return $this->variantsLooper($item['variants']);
        }

        return [];
    }

    /**
     * @param array $variants
     *
     * @return mixed
     */
    private function variantsLooper(array $variants): array
    {
        return array_reduce($variants, function ($carry, $item) {
            if (!is_array($item)) {
                return $carry;
            }

            return [...$carry, ...$this->findProduct($item)];
        }, []);
    }

    public function mappingCrawlProducts(array $array): void
    {
        $responseData = data_get($array, 'data');
        $this->response_data = $responseData;
    }

    public function mappingCrawlProductVariants(array $arr): void
    {
        $responseData = data_get($arr, 'data.variants');
        $this->response_data = $responseData;
    }

    /**
     * @return array
     */
    public function getParamsPagination(): array
    {
        return [];
    }

    /**
     * @return bool
     */
    public function getNexPage(): bool
    {
        return false;
    }

    /**
     * Hiện taị API bên này có vẻ chưa hoàn thiện, còn nhiều lỗi vặt,
     * response không giống doc. Cần theo dõi để bổ sung thêm
     *
     * @param $arr
     *
     * @return void
     */
    protected function setErrors($arr): void
    {
        if (!$arr ?? null) {
            $this->errors[] = 'Missing response';
            return;
        }
        $message = data_get($arr, 'message', 200);
        if (!in_array(data_get($arr, 'statusCode', 200), [200, 201]) && $message) {
            $this->errors[] = $message;
            return;
        }
        if (!$this->fulfill_order_id = (data_get($arr, 'id', '') || data_get($arr, 'data', ''))) {
            $this->errors[] = 'Fulfill Order ID not found !';
        }
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return (bool)data_get($arr, 'order_name', false) === true;
    }

    /**
     * @override AbstractResponseModel::mappingCreateOrder()
     *
     * @param array $arr
     * @param               $orderProductIds
     * @param               $fulfillOrderId
     *
     * @return void
     */
    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            data_get($arr, 'id'),
            OrderProductFulfillStatus::PENDING,
        );
    }

    /**
     * @param array $arr
     * @param $fulfillOrderId
     * @return array
     */
    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        if (!$this->handleError('Crawl Order', $arr, $fulfillOrderId)) {
            return [];
        }
        return (new OneprintWebhookRepository($arr, $this->supplier_id))
            ->withCrawlMode()
            ->get();
    }

    /**
     * @return string
     */
    public function getFulfillStatus(): string
    {
        if ($this->fulfill_status) {
            return $this->fulfill_status;
        }
        return parent::getFulfillStatus();
    }
}

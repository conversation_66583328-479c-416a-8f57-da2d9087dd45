<?php

namespace App\Providers\FulfillAPI\Oneprint\Model;

use App\Enums\EnvironmentEnum;
use App\Enums\ShippingMethodEnum;
use App\Models\OrderProduct;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    public string $type = 'DTF';
    public array $payload = [];

    /**
     * curl --location 'https://api.oneprint.io/v1/orders' \
     * --data-raw '{
     * "origin_id": "ORD-00005",
     * "source_id": "#OP-12938",
     * "shipping_info": {
     * "name": "<PERSON>",
     * "email": "<EMAIL>",
     * "phone": "**********",
     * "city": "Hanoi",
     * "state": "HN",
     * "address": "123 Main St",
     * "address2": "Apt 4B",
     * "postal_code": "100000",
     * "country": "VN",
     * "company": "Example Co"
     * },
     * "items": [
     * {
     * "quantity": 2,
     * "sku": "TshirtKid-White-2-3Years",
     * "print_positions": [
     * {
     * "position": "back",
     * "design_url": "https://image.shutterstock.com/image-photo/happy-young-pug-dog-panting-260nw-**********.jpg",
     * "mockup_url": null
     * }
     * ]
     * },
     * {
     * "quantity": 1,
     * "sku": "TshirtKid-White-3-4Years",
     * "print_positions": [
     * {
     * "position": "front",
     * "design_url": "https://image.shutterstock.com/image-photo/happy-young-pug-dog-panting-260nw-**********.jpg",
     * "mockup_url": null
     * }
     * ]
     * }
     * ]
     * }'
     */

    /**
     * @param $order
     *
     * @return void
     * * @throws \Exception
     */
    public function setOrder($order): void
    {
        $shippingLabel = $order->shipping_label;
        $isLabelOrder = !empty($shippingLabel);
        if ($isLabelOrder) {
            throw new \Exception('not support shipping label order');
        }
        $this->fulfill_order_id = $this->order_id = $this->getReferenceId($order);
        if (empty($order->customer_name) || empty($order->customer_phone)) {
            throw new \Exception('customer name or customer phone is empty');
        }
        $this->payload = [
            'origin_id' => $this->order_id,
            'source_id' => $this->order_id,
            'shipping_info' => [
                "name" => $order->customer_name,
                "email" => $order->customer_email,
                "phone" => $order->customer_phone,
                "city" => $this->customer_address['city'],
                "state" => $this->customer_address['state_code'],
                "address" => $this->customer_address['primary'],
                "address2" => $this->customer_address['addition'] ?? '',
                "postal_code" => $order->postcode,
                "country" => $order->country,
                "company" => ""
            ],
        ];
    }

    /**
     * @param OrderProduct $product
     *
     * @return void
     */
    public function setItems(OrderProduct $product): void
    {
        $designs = $this->getFrontBackDesign($product->files);
        $lineItems = [
            "sku" => $product->fulfill_sku,
            "quantity" => $product->quantity,
        ];
        $printAreas = [];
        foreach ($designs as $printSpace => $data) {
            if (empty($data->design_url)) {
                continue;
            }
            $printAreas [] = [
                'position' => $printSpace,
                'design_url' => $data->design_url ?? '',
                'mockup_url' => $data->mockup_url ?? null,
            ];
        }
        $lineItems['print_positions'] = $printAreas;
        $this->payload['items'][] = $lineItems;
    }
}

<?php

namespace App\Providers\FulfillAPI\Oneprint;

use App\Enums\ProductStatus;
use App\Models\FulfillProduct;
use App\Models\ProductVariant;
use App\Providers\FulfillAPI\ObjectFulfill;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use function DeepCopy\deep_copy;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/orders';
    protected const ENDPOINT_CANCEL_ORDER = '/orders';
    protected const ENDPOINT_CRAWL_ORDER = '/orders';
    protected const ENDPOINT_CRAWL_PRODUCT = '/variants';

    private $providerInfo = [];

    /**
     * @param $provider
     * @return void
     */
    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->providerInfo = $provider;
        $apiKey = Arr::get($provider, $this?->mode ?? 'dev')['api_key'];
        $this->setHeader('X-API-Key', $apiKey);
        $this->setHeader('Content-Type', 'application/json');
        $this->setHeader('Accept', 'application/json');

    }

    /**
     * @param $params
     * @return void
     */
    public function setParams($params = null): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->setApiWithEndpoint();
                $this->params = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->method_url = 'POST';
                break;

            case self::METHOD_CANCEL_ORDER:
                $this->setApiWithEndpoint();
                $this->api .= '/' . $params->id;
                $this->method_url = 'DELETE';
                break;

            case self::METHOD_CRAWL_ORDER:
                $this->setApiWithEndpoint();
                $this->api .= '/' . $params;
                $this->fulfill_order_id = $params;
                $this->method_url = 'GET';
                break;

            case self::METHOD_CRAWL_PRODUCT:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->method_url = 'GET';
                $this->api .= '?' . http_build_query($params);
                break;

            case self::METHOD_CRAWL_PRODUCT_VARIANT:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->method_url = 'GET';
                break;
        }

    }

    /**
     * @param $params
     *
     * @return bool|string
     * @throws \Throwable
     */
    public function withBody($params): bool|string
    {
        $payload = Arr::get($params, 'payload');
        if (isset($payload)) {
            return json_encode($payload, JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES);
        }
        return false;
    }

    /**
     * @param $body
     *
     * @return void
     */
    public function handleAdditionalHeaders($body): void
    {
        $bodyData = [];
        if ($this->shouldHaveRequestBody()) {
            $requestBody = $this->withBody($body);
            if ($requestBody) {
                $bodyData = $requestBody;
            }
        }

        $timestamp = now()->timestamp;
        $apiKey = Arr::get($this->providerInfo, $this?->mode ?? 'dev')['api_key'];
        $apiSecret = Arr::get($this->providerInfo, $this?->mode ?? 'dev')['api_secret'];
        $bodyForEncode = empty($bodyData) ? "{}" : $bodyData;
        $signature = hash_hmac('sha256', $this->method_url . $bodyForEncode . $apiKey . $timestamp, $apiSecret);
        $this->setHeader('X-Timestamp', $timestamp);
        $this->setHeader('X-Signature', $signature);
    }

    public function crawlProductsAndProductVariantsJob(): void
    {
        try {
            $objectCrawlProduct = deep_copy($this);
            $objectCrawlProduct->setMethod($this::METHOD_CRAWL_PRODUCT);
            $params = [
                'page' => 1,
                'per_page' => 20,
            ];
            $objectCrawlProduct->setParams($params);
            $response = $objectCrawlProduct->sendData(null, null, false);
            $responseCode = data_get($response, 'response_status_code');
            if ($responseCode !== 200 && $responseCode !== 201) {
                throw new \Exception('Error response from Oneprint API: ' . $responseCode);
            }
            $totalPages = data_get($response, 'total', 1000);
            $perPage = 200;
            $totalPages = ceil($totalPages / $perPage);
            $objectCrawlProductVariant = deep_copy($this);
            $objectProductVariantModel = $objectCrawlProductVariant->getModel(
                self::MODEL_CRAWL_PRODUCT_VARIANT
            );
            $arrProduct = [];
            foreach (range(1, $totalPages) as $currentPage) {
                $responseModel = $objectCrawlProduct->getModel(self::MODEL_RESPONSE);
                $params = [
                    'page' => $currentPage,
                    'per_page' => $perPage,
                ];
                $objectCrawlProduct->setParams($params);
                $response = $objectCrawlProduct->sendData(null, null, false);
                $responseModel->mappingCrawlProducts($response);
                $data = $responseModel->getResponseData();
                foreach ($data as $each) {
                    $variantSku = data_get($each, 'sku');
                    $skuData = explode('-', $variantSku);
                    $productSku = $skuData[0];
                    $options = [];
                    if (in_array($productSku, [
                        'Doormat',
                        'MetalSign',
                    ])) {
                        $options['size'] = $skuData[2] ?? '';
                    } else if (in_array($productSku, [
                        'PolyPillow',
                    ])) {
                        $options['pack'] = $skuData[1] ?? '';
                        $options['size'] = 'one_size';
                    } else if (in_array($productSku, [
                        'Canvas',
                    ])) {
                        $options['size'] = $skuData[1] ?? '';
                    } else {
                        $options['color'] = $skuData[1] ?? '';
                        $options['size'] = $skuData[2] ?? '';
                    }
                    $variant['options'] = $options;
                    $variant['sku'] = data_get($each, 'sku');
                    $variant['price'] = data_get($each, 'base_cost');
                    $productVariant = $objectProductVariantModel->mapping(
                        $variant,
                        $options
                    );
                    $variants = [
                        'options' => $options,
                        'sku' => data_get($variant, 'sku'),
                        'price' => data_get($variant, 'price'),
                        'product_variant' => $productVariant
                    ];

                    $arrProduct[$productSku]['variants'][] = $variants;
                    $arrProduct[$productSku]['options'][] = $options;
                    $arrProduct[$productSku]['name'] = data_get($each, 'product_name');
                    $arrProduct[$productSku]['sku'] = $productSku;

                }
            }


            foreach ($arrProduct as $sku => &$product) {
                sortArrayOptions($product['options']);
                $product['options'] = json_encode($product['options']);
                $product['sku'] = $productSku = $sku;
                $product['status'] = ProductStatus::ACTIVE;
                $productVariants = data_get($product, 'variants');
                unset($product['variants']);
                $parentProduct = FulfillProduct::updateOrCreate(
                    [
                        'sku' => $productSku,
                        'supplier_id' => $this->supplier_id,
                    ],
                    $product
                );

                foreach ($productVariants as &$variant) {
                    sortArrayOptions($variant['options']);
                    $variant['variant_key'] = getVariantKey($variant['options']);
                    // unset extra fields
                    unset($variant['options']);
                    $variant['product_id'] = $parentProduct->id;
                    $arr_variant[] = $variant;
                    unset($arr_variant['product_variant']);
                }
                ProductVariant::query()
                    ->where('product_id', $parentProduct->id)
                    ->delete();
                foreach (array_chunk($arr_variant, 1000) as $variants) {
                    try {
                        ProductVariant::insert($variants);
                    } catch (\Exception $e) {
                        ProductVariant::insertOrIgnore($variants);
                        logToDiscord(
                            'Insert variants failed.'
                            . "\nSupplier:" . $this->supplier_id
                            . "\nException:" . $e->getMessage()
                            , 'fulfill_product'
                            , true
                        );
                    }
                }
            }

            $this->clearCacheTemplate();
        } catch (\Exception $e) {
            logToDiscord(
                'Gooten crawl product error : '
                . "\nException:" . $e->getMessage()
                , 'fulfill_product'
                , true
            );
        }
    }

    /**
     * @param $body
     * @param string|null $userId
     * @param $saveLog
     * @return array|mixed|null
     * @throws Throwable
     */
    public function sendData($body = null, ?string $userId = null, $saveLog = true)
    {
        try {
            $this->handleAdditionalHeaders($this->params);
            if (is_null($body)) {
                $response = with(Http::withHeaders($this->headers), function (PendingRequest $http) {
                    $options = [
                        'curl' => $this->curlOptions()
                    ];
                    if ($this->proxyOption() !== '') {
                        $options['proxy'] = $this->proxyOption();
                    }
                    $http->withOptions($options);
                    if ($this->shouldHaveRequestBody()) {
                        $requestBody = $this->withBody($this->params);
                        if (!$requestBody) {
                            $requestBody = "{}";
                        }
                        $http->withBody($requestBody);
                    }
                    return $http->timeout($this->requestTimeout())
                        ->acceptJson()
                        ->send(
                            $this->method_url,
                            $this->api
                        );
                });
                $statusCode = $response->status();
                $body = $response->json();
                $body = $this->prepareResponse($body, $statusCode);
            }
            if ($saveLog) {
                $this->logRequestAPI($body, $userId);
            }
            return $body;
        } catch (Throwable $e) {
            if ($saveLog) {
                $this->logRequestAPI($body, $userId);
            }
            throw $e;
        }
    }
}

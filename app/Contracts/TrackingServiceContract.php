<?php

namespace App\Contracts;

interface TrackingServiceContract
{
    public static function register($trackingNumbers);

    public static function deleteTrack($trackingNumbers);

    public static function getTrackInfo($trackingNumbers);

    public static function carriers();

    public static function registerOrderTracking($trackingCodes);

    public static function statusesMapWithTrackingStatusEnum();

    public static function detectPatterns();

    public static function convertTrackingStatus($status);

    public static function isSkipCheck($code, $message = '');

    public static function isNeedRegister($code, $message = '');

    public static function detectCarrier($trackingNumber, $carrier = null, $supplier = null, $trackingUrl = null);

    public function postRequest($url, array $data = [], bool $handleExcept = false);

    public function verifySignature($request);

    public function handle($url, $trackingNumbers);
}

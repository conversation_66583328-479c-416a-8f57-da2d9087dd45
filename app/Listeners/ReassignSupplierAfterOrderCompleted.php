<?php

namespace App\Listeners;

use App\Enums\QueueName;
use App\Events\AfterOrderPaymentCompleted;
use App\Jobs\DetectOrderHasDesignCornerPlacement;
use App\Jobs\ProcessOrderReassignSupplier;
use App\Jobs\ValidateColorOfDesign;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Bus;
use Throwable;

class ReassignSupplierAfterOrderCompleted implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;
    /**
     * Handle the event.
     *
     * @param AfterOrderPaymentCompleted $event
     */
    public function handle(AfterOrderPaymentCompleted $event): void
    {
        if ($event->order->isServiceOrder() || $event->order->isCustomServiceOrder() || $event->order->isFulfillmentOrder()) {
            return;
        }
        Bus::chain([
            new DetectOrderHasDesignCornerPlacement($event->order->id),
            new ProcessOrderReassignSupplier($event->order->id)
        ])->catch(function (Throwable $e) {
            logException($e);
        })->onQueue(QueueName::ORDER)->delay(now()->addMinutes(10))->dispatch();
        ValidateColorOfDesign::dispatch($event->order->id)->delay(now()->addMinutes(10))->onQueue(QueueName::LOW_PRIORITY);
    }
}

<?php

namespace App\Listeners;

use App\Enums\DiscordChannel;
use App\Enums\OrderTypeEnum;
use App\Events\OrderFulfilled;
use App\Models\Currency;
use App\Services\StoreService;

class SendOrderFulfillNotification
{
    /**
     * Handle the event.
     *
     * @param OrderFulfilled $event
     * @return void
     */
    public function handle(OrderFulfilled $event)
    {
        $order = $event->order;
        if (!in_array($order->type, [
            OrderTypeEnum::REGULAR,
            OrderTypeEnum::CUSTOM,
        ], true)) {
            return;
        }

        $fulfillments = $order->getFulfillments(isCustomer: true);

        if (!$fulfillments) {
            logToDiscord('Order ID: ' . $order->id . ' | SendOrderFulfillNotification Error', DiscordChannel::FULFILL_ORDER);
            return;
        }

        // process fulfillments
        $unfulfilled = $fulfillments['unfulfilled'] ?? [];
        $fulfilled = [];
        foreach ($fulfillments as $key => $items) {
            if (strpos($key, 'fulfilled') !== false || strpos($key, 'on_delivery') !== false) {
                // get tracking_code & shipping carrier
                $item = collect($items)->first();
                $tracking_code = $item->tracking_code;
                $shipping_carrier = $item->shipping_carrier;
                $tracking_url = $item->tracking_url;
                $fulfilled[] = [
                    'tracking_code' => $tracking_code,
                    'shipping_carrier' => $shipping_carrier,
                    'tracking_url' => $tracking_url,
                    'items' => $items
                ];
            }
        }
        // sent email to buyer
        $customerEmail = $order->customer_email;
        $customerName = $order->customer_name;

        if (count($fulfilled) === 0 && count($unfulfilled) === 0) {
            logToDiscord([
                'order_id' => $order->id,
                'message' => 'Fulfillments is empty',
                'fulfillments' => $fulfillments
            ], DiscordChannel::FULFILL_ORDER);
        }

        $dataSendMailLog = [
            'sellerId' => $order->seller_id ?? null,
            'storeId' => $order->store_id ?? null,
            'orderId' => $order->id ?? null
        ];
        $currency = Currency::firstByCode($order->currency_code);
        $config = [
            'to' => $customerEmail,
            'template' => 'buyer.fulfillment_notification',
            'data' => [
                'subject' => 'Order #' . $order->order_number . ' has been fulfilled',
                'name' => $customerName,
                'order_number' => $order->order_number,
                'order_url' => $order->status_url,
                'currency' => $currency,
                'fulfillments' => [
                    'unfulfilled' => $unfulfilled,
                    'fulfilled' => $fulfilled
                ],
                'store_info' => StoreService::getStoreInfo($order->store_id)
            ],
            'sendMailLog' => $dataSendMailLog
        ];

        sendEmail($config);
    }
}

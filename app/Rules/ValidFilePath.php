<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Str;

class ValidFilePath implements Rule
{
    private $onlyUrl;

    public function __construct($onlyUrl = false)
    {
        $this->onlyUrl = $onlyUrl;
    }

    public function passes($attribute, $value)
    {
        if (empty($value)) {
            return true;
        }

        $allowedExtensions = ['jpeg', 'jpg', 'png', 'gif', 'webp'];

        // Kiểm tra nếu là URL hợp lệ
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            $path = parse_url($value, PHP_URL_PATH);
            $extension = $this->getExtension($path);
            if (in_array($extension, $allowedExtensions, true)) {
                return true;
            }
        }

        // Google Drive, Dropbox, Google Photos
        $trustedDomains = [
            'https://drive.google.com',
            'https://dropbox.com',
            'https://lh3.googleusercontent.com',

            //domain of seller
            'https://zaxcloud.com',
        ];
        if (Str::startsWith($value, $trustedDomains)) {
            return true;
        }

        if ($this->onlyUrl) {
            return false;
        }

        // Kiểm tra file có tồn tại trên server
        if (file_exists($value)) {
            return true;
        }

        // Kiểm tra đường dẫn lưu trữ nội bộ (S3 hoặc thư mục tạm thời)
        $tempsPath = [
            'p/',
            'o/',
            'tmp/',
            'u/',
            '/p/',
            '/o/',
            '/tmp/',
            '/u/',
        ];
        if (Str::startsWith($value, $tempsPath)) {
            $extension = $this->getExtension($value);
            if (in_array($extension, $allowedExtensions, true)) {
                return true;
            }
        }

        return false;
    }

    public function message()
    {
        return 'Your file path is invalid.';
    }

    private function getExtension($path): string
    {
        if (!$path) {
            return '';
        }

        // Trích xuất phần mở rộng bằng regex để tránh lỗi khi URL có dạng `/640x480.png/00bb99`
        if (preg_match('/\.(jpe?g|png|gif|webp)\b/i', $path, $matches)) {
            return Str::lower($matches[1]);
        }

        return '';
    }
}

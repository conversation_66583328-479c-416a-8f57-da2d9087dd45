<?php

namespace App\Jobs;

use App\Actions\Commons\ImgDirectLinkAction;
use App\Enums\DiscordChannel;
use App\Enums\DiscordUserIdEnum;
use App\Enums\FbaFulfillBy;
use App\Enums\FileTypeEnum;
use App\Enums\FulfillMappingEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\PersonalizedType;
use App\Enums\ProductPrintType;
use App\Enums\QueueName;
use App\Enums\TelegramUserIdEnum;
use App\Http\Controllers\UploadController;
use App\Library\UrlUploadedFile;
use App\Models\File;
use App\Models\FulfillMapping;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\ProductVariant;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Providers\FulfillAPI\ObjectFulfill;
use App\Services\AddressService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Throwable;

class ValidateSellerFulfillOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $order_id;
    /**
     * @var Order
     */
    protected $order;
    private bool $isOrderValid;
    private array $logs;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 1;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public bool $deleteWhenMissingModels = true;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public int                  $backoff = 5;
    private bool                $skipValidateAddress;
    private ImgDirectLinkAction $imgDirectLinkAction;

    /**
     * Create a new job instance.
     *
     * @param Order $order
     * @param bool $skipValidateAddress
     */
    public function __construct(Order $order, bool $skipValidateAddress = false)
    {
        $this->order_id = $order->id;
        $this->isOrderValid = true;
        $this->logs = [];
        $this->skipValidateAddress = $skipValidateAddress;
        $this->onQueue(QueueName::VALIDATE_FULFILL);
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        $this->imgDirectLinkAction = new ImgDirectLinkAction();
        $this->order = Order::query()
            ->whereKey($this->order_id)
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT,
                OrderStatus::ON_HOLD,
            ])
            ->with([
                'products',
                'products.template',
                'files',
            ])
            ->first();

        if (empty($this->order)) {
            logToDiscord('Validate fulfill order #' . $this->order_id . ' failed. Order not found.', 'job');
            return;
        }

        if ($this->skipValidateAddress) {
            $this->order->address_verified = OrderAddressVerifiedEnum::VERIFIED;
            $this->order->skip_validate_address = 1;
        } else if (!$this->order->isFBA()) {
            $this->verifyAddress();
        }

        $this->verifyImages();
        $this->validateProducts();
        $this->verifyCountry();
        $this->validateProductsPrepareToSupplier();
        try {
            $this->order->assignSupplier(false);
            // Validate shipping location after assign supplier
            $this->validateShippingLocation();
            $this->order->calculateFulfillDesignCost();
            $this->order->calculateOrder();
        } catch (Throwable $e) {
            $this->logs[] = "Assign supplier or calculate order error: " . $e->getMessage();
            $this->isOrderValid = false;
        }

        if (empty($this->order->total_shipping_amount)) {
            $this->isOrderValid = false;
            $this->logs[] = 'Calculate shipping cost error. Please contact support';
        }

        if (!$this->isOrderValid) {
            $this->order->fulfill_log = implode(';', $this->logs);
            $this->order->status = OrderStatus::DRAFT;
            $this->order->fulfill_status = OrderFulfillStatus::INVALID;
        } else {
            $this->order->fulfill_log = null;
            $this->order->status = OrderStatus::PENDING;
            $this->order->fulfill_status = OrderFulfillStatus::UNFULFILLED;
            ProcessOrderReassignSupplier::dispatch($this->order->id)->onQueue('order');
        }
        $this->order->push();
        OrderHistory::insertLog(
            $this->order,
            OrderHistoryActionEnum::VALIDATE,
        );
    }

    /**
     * @return void
     */
    private function validateShippingLocation()
    {
        $locationInfo = SystemLocation::findByCountryCodeThenSetForAssign($this->order);
        if (!$locationInfo) {
            $this->logs[] = "We don't ship to {$this->order->country}";
            $this->isOrderValid = false;
            return;
        }
        $excludeMappings = FulfillMapping::filterByExcludeLocation(
            FulfillMappingEnum::SHIPPING_EXCLUDE_LOCATION,
            $locationInfo,
            $this->order->shipping_method,
        );
        $noShip = false;
        if($excludeMappings->isNotEmpty()) {
            $this->order->products->each(function ($product) use ($excludeMappings, &$noShip) {
                if ($excludeMappings !== null && $excludeMappings->count() > 0) {
                    $suppliers_id = $excludeMappings->pluck('supplier_id')->filter(fn($v) => !empty($v))->values()->toArray();
                    $isSupplierExcluded = is_null($excludeMappings->first()->supplier_id) || (!empty($product->supplier_id) && !empty($suppliers_id) && in_array($product->supplier_id, $suppliers_id));
                    if ($isSupplierExcluded && !$noShip) {
                        $noShip = true;
                    }
                }
            });
        }
        if ($noShip) {
            $address = $locationInfo->address;
            if (!empty($this->order->address_2)) {
                $address .= ', ' . $this->order->address_2;
            }
            if (!empty($this->order->city)) {
                $address .= ', ' . $this->order->city;
            }
            if (!empty($this->order->state)) {
                $address .= ', ' . $this->order->state;
            }
            if (!empty($this->order->postcode)) {
                $address .= ', ' . $this->order->postcode;
            }
            if (!empty($locationInfo->name)) {
                $address .= ', ' . $locationInfo->name;
            }
            $this->logs[] = "We don't ship to {$address}";
            $this->isOrderValid = false;
        }
    }

    /**
     * @return void
     */
    private function verifyAddress(): void
    {
        if (isEnvTesting()) {
            return;
        }

        $addressVerifyLog = '';
        if ($this->order->address_verified === OrderAddressVerifiedEnum::UNVERIFIED) {
            if (AddressService::verify($this->order, $addressVerifyLog)) {
                $this->order->address_verified = OrderAddressVerifiedEnum::VERIFIED;
            } else {
                $this->order->address_verified = OrderAddressVerifiedEnum::INVALID;
            }
        }

        if ($this->order->address_verified === OrderAddressVerifiedEnum::INVALID) {
            if (empty($addressVerifyLog)) {
                $addressVerifyLog = 'Customer address is invalid';
            }
            $this->logs[] = $addressVerifyLog;
            $this->isOrderValid = false;
        }
    }

    /**
     * @throws \Exception
     */
    private function verifyImages(): void
    {
        if(isEnvTesting()) {
            return;
        }

        $products = $this->order->products;

        // Check each file by content type
        foreach ($this->order->files as $file) {
            if (empty($file->file_url)) {
                $this->missingFileUrl($file);
                continue;
            }

            if (!empty($file->file_url_2)) {
                continue;
            }

            /** @var OrderProduct $orderProduct */
            $orderProduct = $products->first(fn($item) => $item->id == $file->order_product_id);

            if (empty($orderProduct)) {
                $this->logs[] = 'Order product not found: ' . $file->order_product_id . ' - File Id: ' . $file->id;
                $this->isOrderValid = false;
                continue;
            }

            if (Str::startsWith($file->file_url, ['o/', 'u/'])) {
                $file->file_url_2 = $file->file_url;
                if (empty($orderProduct->thumb_url) || $file->type === FileTypeEnum::IMAGE || Str::startsWith($orderProduct->thumb_url, 'http')) {
                    $orderProduct->thumb_url = $file->file_url_2;
                }
                continue;
            }

            try {
                $message = 'Download file for Order ID #' . $this->order->id . "\r\n";
                $message .= 'Order Product Id: ' . $file->order_product_id . "\r\n";
                $message .= 'File Id: ' . $file->id . "\r\n";
                $message .= 'File Url: ' . $file->file_url . "\r\n";
                logToTelegram(TelegramUserIdEnum::THANGNM, $message);

                $directLink = $this->imgDirectLinkAction->handle($file->file_url);
                if (empty($directLink)) {
                    $this->logs[] = 'File invalid: ' . $file->file_url;
                    $this->isOrderValid = false;
                    continue;
                }
                $maxRetried = 1;
                while (true) {
                    $loadImage = UploadController::uploadS3FromDirectLink($directLink, 'o/' . $this->order->id);
                    if (!empty($loadImage) || $maxRetried > 5) {
                        break;
                    }
                    $maxRetried++;
                }

                if (empty($loadImage)) {
                    $this->logs[] = 'File invalid: ' . $file->file_url;
                    $this->isOrderValid = false;
                    continue;
                }

                if ($file->type === FileTypeEnum::DESIGN) {
                    $templateProduct = $orderProduct->template;
                    $fullPrinted = $orderProduct->template->full_printed;
                    $isFulfillPrint2D = in_array($fullPrinted, [ProductPrintType::PRINT_2D, ProductPrintType::PRINT_2D_FULL]);
                    if ($isFulfillPrint2D && !$this->validFulfill2DMimeFile($directLink)) {
                        $this->logs[] = 'File design must be png type: ' . $file->file_url;
                        $this->isOrderValid = false;
                        continue;
                    }
                    $templatePrintSpaces = json_decode($templateProduct->print_spaces);
                    $imgPrintSpace = $file->print_space;
                    $imgWidth = $loadImage['width'];
                    $imgHeight = $loadImage['height'];
                    $findPrintSpaceIdx = array_search($imgPrintSpace, array_column($templatePrintSpaces, 'name'));
                    $correctPrintSpace = $templatePrintSpaces[$findPrintSpaceIdx];
                    $printSpaceWidth = $correctPrintSpace->width;
                    $printSpaceHeight = $correctPrintSpace->height;

                    // validate image quality / DPI
                    $dpi = !empty($correctPrintSpace->DPI) ? $correctPrintSpace->DPI : 300;
                    $minDpi = !empty($correctPrintSpace->minDPI) ? $correctPrintSpace->minDPI : $dpi / 2;
                    if ($imgWidth / $printSpaceWidth < $minDpi / $dpi || $imgHeight / $printSpaceHeight < $minDpi / $dpi) {
                        $this->isOrderValid = false;
                        $minWidth = $printSpaceWidth * $minDpi / $dpi;
                        $minHeight = $printSpaceHeight * $minDpi / $dpi;
                        $this->logs[] = 'SKU ' . $orderProduct->sku . ' / design is low quality ' . $file->file_url .
                            '. Expect ' . $printSpaceWidth . 'x' . $printSpaceHeight . ' px , minimum ' . $minWidth . 'x' . $minHeight . ' px';
                    } // validate image size
                    else if ($fullPrinted && (abs($imgWidth / $printSpaceWidth - $imgHeight / $printSpaceHeight) * 100 > 1)) {
                        $this->isOrderValid = false;
                        $this->logs[] = 'SKU ' . $orderProduct->sku . ' / design size invalid: ' . $file->file_url . '. Expect ' . $printSpaceWidth . 'x' . $printSpaceHeight . ' px';
                    }
                }

                $file->file_url_2 = $loadImage['path'];
                if (empty($orderProduct->thumb_url) || Str::startsWith($orderProduct->thumb_url, 'http') || $file->type === FileTypeEnum::IMAGE) {
                    $orderProduct->thumb_url = $file->file_url_2;
                }
            } catch (\Throwable $e) {
                $this->logs[] = 'File invalid: ' . $file->file_url;
                $this->logs[] = 'Download error: ' . $e->getMessage();
                $this->isOrderValid = false;
            }
        }
    }

    /**
     * @return void
     */
    private function verifyCountry(): void
    {
        $disabledCountry = SystemConfig::getConfig('disable_country', '');
        $country = $this->order->country ?? '';
        if(!empty($disabledCountry) && !empty($country) && str_contains($disabledCountry, $country)) {
            $this->isOrderValid = false;
            $this->logs[] = 'Orders to this country ' . $country . ' are currently disabled.';
        }
    }

    /**
     * @return void
     */
    public function validateProducts(): void
    {
        $order = $this->order;
        $products = $this->order->products;
        $products->map(function (OrderProduct $product) use (&$order) {
            $campaign = null;
            if ($product->campaign_id) {
                $campaign = $product->campaign()->onSellerConnection($order->seller)->first();
            }
            $product->setRelation('campaign', $campaign);

            /** @var  \App\Models\Product $templateProduct */
            $templateProduct = $product->template;
            if(!$templateProduct) {
                $this->isOrderValid = false;
                $this->logs[] = 'Product ' . $product->product_name . ' / SKU ' . $product->sku . ' does not have product template';
                return;
            }
            $fullPrinted = $templateProduct->full_printed;
            // fba order validate
            if ($order->isFBA()) {
                if (empty($order->shipping_label)) {
                    $this->logs[] = 'FBA Order ' . $order->order_number . ' must have shipping label';
                    $this->isOrderValid = false;
                }

                if ($product->fulfill_fba_by === FbaFulfillBy::BY_AMAZON) {
                    $mod = $product->quantity % $templateProduct->quantity;
                    if ($mod !== 0) {
                        $this->logs[] = 'Product ' . $product->product_name . ' / SKU ' . $product->sku . ' quantity must be multiple of ' . $templateProduct->quantity;
                        $this->isOrderValid = false;
                    }

                    if (empty($product->barcode)) {
                        $this->logs[] = 'Product ' . $product->product_name . ' / SKU ' . $product->sku . ' must have barcode';
                        $this->isOrderValid = false;
                    }
                }

                if ($templateProduct->system_type !== ProductSystemTypeEnum::FULFILL_FBA) {
                    $this->logs[] = 'Product ' . $product->product_name . ' / SKU ' . $product->sku . ' invalid for FBA order';
                    $this->isOrderValid = false;
                }
            }

            $variantKey = 'none';
            // check variant price
            $productOptions = json_decode($product->options, true);
            if (!empty($productOptions)) {
                $variantKey = getVariantKey($productOptions);
                $product->color = $productOptions['color'] ?? 'N/A';
                $product->size = $productOptions['size'] ?? 'N/A';
            }
            $location = SystemLocation::findByCountryCode($order->country);
            $regionCodes = $location ? $location->getRegionCodes() : ['*'];
            $regionCodes = array_reverse($regionCodes);
            $templateVariant = ProductVariant::query()
                ->select('price', 'adjust_price', 'out_of_stock', 'base_cost')
                ->where([
                    'variant_key' => $variantKey,
                    'product_id' => $templateProduct->id,
                ])
                ->whereIn('location_code', $regionCodes)
                ->orderByRaw("FIELD(location_code,'" . implode("','", $regionCodes) . "') desc")
                ->first();

            if (is_null($templateVariant) || $templateVariant->out_of_stock) {
                $product->fulfill_status = OrderProductFulfillStatus::OUT_OF_STOCK;
                $this->logs[] = 'Product ' . $product->product_name . ' / SKU ' . $product->sku . ' / ' . $variantKey . ' is out of stock';
                $this->isOrderValid = false;
            }
            $extraCustomFee = 0;
            $commonExtraCustomFee = 0;
            $optionGroups = Str::isJson($product->custom_options) ? collect(json_decode($product->custom_options, true, 512, JSON_THROW_ON_ERROR)) : collect();
            if (($fullPrinted === ProductPrintType::HANDMADE || $product->personalized === PersonalizedType::CUSTOM_OPTION) && $optionGroups->isNotEmpty()) {
                $campaignExtraCustomFee = 0;
                if ($product->personalized === PersonalizedType::CUSTOM_OPTION && !empty($product->campaign->options)) {
                    $decodedOptions = Str::isJson($product->campaign->options) ? json_decode($product->campaign->options, true, 512, JSON_THROW_ON_ERROR) : [];
                    $campaignExtraCustomFee = !empty($decodedOptions['group']['extra_custom_fee']) ? $decodedOptions['group']['extra_custom_fee'] : 0;
                    $commonExtraCustomFee = !empty($decodedOptions['common_options']['extra_custom_fee']) ? (float) $decodedOptions['common_options']['extra_custom_fee'] : 0;
                } else if ($fullPrinted === ProductPrintType::HANDMADE && !empty($templateProduct->options)) {
                    $decodedOptions = Str::isJson($templateProduct->options) ? json_decode($templateProduct->options, true, 512, JSON_THROW_ON_ERROR) : [];
                    $campaignExtraCustomFee = !empty($decodedOptions['custom_options']['group']['extra_custom_fee']) ? $decodedOptions['custom_options']['group']['extra_custom_fee'] : 0;
                    $commonExtraCustomFee = !empty($decodedOptions['common_options']['extra_custom_fee']) ? (float) $decodedOptions['common_options']['extra_custom_fee'] : 0;
                }
                // Sử dụng idx > 0 để miễn phí cho nhóm đầu tiên
                if ($fullPrinted === ProductPrintType::HANDMADE) {
                    $optionGroups->each(function ($group, $idx) use ($campaignExtraCustomFee, &$extraCustomFee) {
                        if ($idx > 0) {
                            $group = collect($group);
                            if ($group->some(fn ($option) => isset($option['value']) && $option['value'] !== '')) {
                                $extraCustomFee += (float) $campaignExtraCustomFee;
                            }
                            $group->each(function ($option) use (&$extraCustomFee) {
                                if (isset($option['value'], $option['price']) && $option['value'] !== '' && (float) $option['price'] > 0) {
                                    $extraCustomFee += (float) $option['price'];
                                }
                            });
                        }
                    });
                } else {
                    $optionGroups->each(function ($group, $idx) use ($campaignExtraCustomFee, &$extraCustomFee) {
                        if ($idx > 0 && collect($group)->some(fn($option) => isset($option['value']) && $option['value'] !== '')) {
                            $extraCustomFee += (float) $campaignExtraCustomFee;
                        }
                    });
                }
                $commonOptions = $optionGroups->filter(function ($group) {
                    return collect($group)->where('g_type', 'common')->count() > 0;
                })->values();
                if ($commonOptions->isNotEmpty() && $commonOptions->some(fn($group) => collect($group)->some(fn($option) => isset($option['value']) && $option['value'] !== ''))) {
                    $extraCustomFee += (float) $commonExtraCustomFee;
                }
            }
            if (!is_null($templateVariant)) {
                $extraPrintCost = $templateProduct->calcExtraPrintCost($product->designs);
                $product->extra_print_cost = $extraPrintCost;
                $product->fulfill_status = OrderProductFulfillStatus::UNFULFILLED;
                $product->base_cost = $templateVariant->base_cost + $extraPrintCost;
                $product->price = $product->base_cost + $extraCustomFee;
                $product->base_shipping_cost = $templateProduct->shipping_cost;
                $product->fulfill_shipping_cost = $templateProduct->shipping_cost;
                $product->extra_custom_fee = $extraCustomFee;
            }
        });
    }

    /**
     * Handle the failing job.
     *
     * @param Throwable $exception
     * @return void
     */
    public function failed(Throwable $exception): void
    {
        logToDiscord('Validate fulfill order #' . $this->order_id . ' failed: ' . $exception->getMessage(), 'job');
    }

    /**
     * @param $file
     *
     * @return string
     */
    public function missingFileUrl(File $file): string
    {
        $message = ' is not a valid image';
        if ($file->type === FileTypeEnum::SHIPPING_LABEL) {
            $message = ' is not a valid shipping label';
        } elseif ($file->type === FileTypeEnum::BARCODE) {
            $message = ' is not a valid barcode';
        }
        $this->logs[] = 'File #' . $file->id . $message;
        $this->isOrderValid = false;

        return $message;
    }

    /**
     * @param    string    $directLink
     *
     * @return bool
     * @throws Exception
     */
    private function validFulfill2DMimeFile(string $directLink): bool
    {
        $mimeType = 'image/png';

        try {

            if (Http::head($directLink)->header('content-type') === $mimeType) {
                return true;
            }

            // @suggestion: ThangNM
            // todo optimize only check header instead of download file
            $file = UrlUploadedFile::createFileFromUrl($directLink);

            return optional($file)->getMimeType() === $mimeType;
        } catch (Throwable $e) {}

        return false;
    }

    private function validateProductsPrepareToSupplier () {
        $object      = new ObjectFulfill();
        $productsWithSuppliers = [];
        $this->order->products->each(function ($op) use (&$productsWithSuppliers) {
            if ($op->supplier_id && !array_key_exists($op->supplier_id, $productsWithSuppliers)) {
                $productsWithSuppliers[$op->supplier_id] = [$op];
            } else {
                $productsWithSuppliers[$op->supplier_id][] = $op;
            }
        });
        foreach ($productsWithSuppliers as $supplierId => $ops) {
            $objectProvider = $object->getProviderBySupplierId($supplierId);
            if (!$objectProvider || empty($ops)) {
                continue;
            }

            $validation = $objectProvider->validateProductsPrepareToSupplier($ops);
            if (!data_get($validation, 'validate')) {
                $products = $validation['data'];
                $message = '';
                foreach ($products as $productMessage) {
                    $message .= " - $productMessage\r\n";
                }
                $embedDesc = [
                    [
                        'description' => $message,
                        'color' => 16776960
                    ]
                ];
                logToDiscord((app()->isProduction() ? mentionDiscord(DiscordUserIdEnum::BIZDEV) : '') . "Supplier này không cho phép các sản phẩm Canvas, Posters, Airframes, Pillows trong cùng một label. Mọi người check nha.\r\nhttps://admin.senprints.com/order/detail/" . $this->order->id, channel: DiscordChannel::PTDT_CHANNEL, async: false, embeds: $embedDesc, threadId: 1336274120500973612);
                $this->logs[] = data_get($validation, 'message');
                $this->isOrderValid = false;
            }
        }


    }
}

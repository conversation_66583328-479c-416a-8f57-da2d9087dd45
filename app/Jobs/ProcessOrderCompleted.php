<?php

namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Enums\CustomOptionTypeEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\DiscordChannel;
use App\Enums\DiscordUserIdEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\ProductPrintType;
use App\Enums\QueueName;
use App\Events\AfterOrderPaymentCompleted;
use App\Models\Campaign;
use App\Models\Design;
use App\Models\File;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\PaymentGateway;
use App\Models\ProductFulfillMapping;
use App\Models\Store;
use App\Models\Supplier;
use App\Models\User;
use App\Services\FulfillmentService;
use App\Services\OrderService;
use App\Services\UserService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\Marketing\Services\MarketingServices;
use Modules\OrderService\Models\RegionOrders;
use Throwable;

class ProcessOrderCompleted implements ShouldQueue, ShouldBeUniqueUntilProcessing
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $orderId;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 3;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public bool $deleteWhenMissingModels = true;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public int $backoff = 15;

    /**
     * Create a new job instance.
     *
     * @param int $orderId
     */
    public function __construct(int $orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return 'handle-complete-order:' . $this->orderId;
    }

    /**
     * @return void
     * @throws Throwable
     */
    public function handle(): void
    {
        $order = Order::query()->with('products')->firstWhere('id', $this->orderId);
        if (!$order || $order->payment_status !== OrderPaymentStatus::PAID || $order->total_seller_profit > 0) {
            logToDiscord("Order status is invalid. OrderId: " . $this->orderId, 'error', true);
            return;
        }
        $seller = $order->seller;
        if ($order->type === OrderTypeEnum::SERVICE) {
            $order->status = OrderStatus::COMPLETED;
            $order->fulfill_status = OrderFulfillStatus::FULFILLED;
            $order->products->map(function (OrderProduct $product) {
                $product->fulfill_status = OrderProductFulfillStatus::FULFILLED;
            });
            $order->push();
            $original_order = Order::query()->whereKey($order->order_number_2)->first();
            if (!$original_order || !$seller) {
                return;
            }
            $message = 'Order ID: #' . $order->id . "\r\n";
            $message .= 'Order number: ' . $order->order_number . "\r\n";
            $message .= 'Seller email: ' . $seller->email . "\r\n";
            $message .= 'Original order number: ' . $original_order->order_number . "\r\n";
            $message .= 'Paid amount: ' . UserService::formatCurrency($order->total_amount) . "\r\n";
            $embedDesc = [
                [
                    'description' => $message,
                    'color' => 2123412
                ]
            ];
            if (app()->isProduction()) {
                logToDiscord(mentionDiscord(DiscordUserIdEnum::CS) . " https://admin.senprints.com/order/detail/{$order->id}\r\n[Platform] Đơn này đã được K thanh toán thành công. Team tiến hành xử lý đơn cho K nhé!", channel: DiscordChannel::ORDER_CUSTOM_SERVICE, embeds: $embedDesc);
            } else {
                logToDiscord("https://admin-v2.dev.senprints.net/order/detail/{$order->id}\r\n[Platform] Đơn này đã được K thanh toán thành công. Team tiến hành xử lý đơn cho K nhé!", DiscordChannel::DEV_DEBUG_LOGS, embeds: $embedDesc, threadId: 1397426618766459010);
            }

            return;
        }

        if ($order->isCustomServiceOrder()) {
            $order->status = OrderStatus::PROCESSING;
            $order->sen_fulfill_status = OrderSenFulfillStatus::PENDING;
            $order->save();
            $original_order = Order::query()->whereKey($order->order_number_2)->where('seller_id', $order->seller_id)->first();
            if (!$original_order || !$seller) {
                return;
            }
            $message = 'Order ID: #' . $order->id . "\r\n";
            $message .= 'Order number: ' . $order->order_number . "\r\n";
            $message .= 'Seller email: ' . $seller->email . "\r\n";
            $message .= 'Original order number: ' . $original_order->order_number . "\r\n";
            $message .= 'Paid amount: ' . UserService::formatCurrency($order->total_amount) . "\r\n";
            $embedDesc = [
                [
                    'description' => $message,
                    'color' => 15105570
                ]
            ];
            if (app()->isProduction()) {
                logToDiscord(mentionDiscord(DiscordUserIdEnum::CS) . " https://admin.senprints.com/order/detail/{$order->id}\r\n[Custom] Đơn này đã được K thanh toán thành công. Team tiến hành xử lý đơn cho K nhé!", channel: DiscordChannel::ORDER_CUSTOM_SERVICE, embeds: $embedDesc);
            } else {
                logToDiscord("https://admin-v2.dev.senprints.net/order/detail/{$order->id}\r\n[Custom] Đơn này đã được K thanh toán thành công. Team tiến hành xử lý đơn cho K nhé!", DiscordChannel::DEV_DEBUG_LOGS, embeds: $embedDesc, threadId: 1397426618766459010);
            }
            return;
        }

        if (!in_array($order->type, [OrderTypeEnum::CUSTOM, OrderTypeEnum::REGULAR], true)) {
            logToDiscord("Order type invalid. OrderId: " . $this->orderId, 'error', true);
            return;
        }

        if (empty($order->seller_id)) {
            logToDiscord("No seller found. OrderId " . $this->orderId, 'error', true);
            return;
        }
        $existsCompletedByAdmin = OrderHistory::query()
            ->where('order_id', $order->id)
            ->where('action', OrderHistoryActionEnum::PAID_BY_ADMIN)
            ->exists();
        if (!$existsCompletedByAdmin) {
            $order->calculateOrder();
            if (abs($order->total_paid - $order->total_amount) > 0.1) {
                $cache = Cache::store('database');
                $alertSyncKey = md5('alert_sync_order_' . $order->getRegion() . '_' . $order->getId());
                if (!$cache->get($alertSyncKey)) {
                    $order->status = OrderStatus::PENDING;
                    $order->payment_status = OrderPaymentStatus::UNPAID;
                    $order->paid_at = null;
                    $order->save();
                    RegionOrders::onRegion($order->getRegion())->where('access_token', $order->getAccessToken())->update(['synced_at' => null, 'updated_at' => now()->subMinutes(6)]);
                    logToDiscord("https://admin.senprints.com/order/detail/{$order->getId()}\r\nComplete order invalid #{$order->getId()}. Total paid $order->total_paid is different from total amount $order->total_amount.", 'admin_warning');
                    $cache->put($alertSyncKey, $order->getId(), CacheKeys::CACHE_12H);
                }
                graylogInfo("Complete order invalid #{$order->getId()}. Total paid $order->total_paid is different from total amount $order->total_amount.", [
                    'category' => 'order_payment_completed_invalid',
                    'order_id' => $this->orderId,
                    'source' => 'job',
                    'data_order' => json_encode($order, JSON_THROW_ON_ERROR)
                ]);
                return;
            }
        }
        $paid_at = $order->paid_at;
        try {
            DB::beginTransaction();

            // check and update order pay via seller payment gateway
            $paymentGateway = PaymentGateway::query()->select(['id', 'seller_id'])->find($order->payment_gateway_id);
            if (($paymentGateway && $paymentGateway->seller_id && $paymentGateway->seller_id !== User::SENPRINTS_SELLER_ID) || $order->type === OrderTypeEnum::CUSTOM) {
                $order->type = OrderTypeEnum::CUSTOM;
                $order->sen_fulfill_status = OrderSenFulfillStatus::PENDING;
            }

            $order->total_quantity = $order->countTotalQuantity();
            $order->calculateDesignCost();
            $order->calculateFulfillProcessingFee();
            $order->calculateSellerProfit();
            $order->calculateCustomerPaid();
            $order->updatePromotionUsed();
            $order->calcTotalShippingCostAndProfit();

            $totalSenPoints = 0;
            $seller = User::query()->find($order->seller_id);

            // update ref
            $order->ref_id = optional($seller)->ref_id;
            $order->products->map(function (OrderProduct $orderProduct) use ($order, $seller, &$totalSenPoints) {
                $orderProduct->ref_id = $order->ref_id;
                // update order size/color
                $orderProduct->options = correctOptionValue($orderProduct->options);
                $options = json_decode($orderProduct->options, true) ?? [];
                $orderProduct->color = $options['color'] ?? 'N/A';
                $orderProduct->size = $options['size'] ?? 'N/A';
                $orderProduct->sen_fulfill_status = $order->sen_fulfill_status;
                $productSystemType = $orderProduct->campaign_type;
                if (empty($productSystemType)) {
                    $eSeller = UserService::getSellerSharding($orderProduct->seller_id);
                    $productSystemType = Campaign::query()->onSellerConnection($eSeller)->whereKey($orderProduct->campaign_id)->value('system_type');
                    $orderProduct->campaign_type = $productSystemType ?? ProductSystemTypeEnum::REGULAR;
                }
                $isCampaignMockupCustom = in_array($productSystemType, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true);
                $hasCustomOptions = $orderProduct->personalized === PersonalizedType::CUSTOM_OPTION || $isCampaignMockupCustom;
                if ($hasCustomOptions) {
                    if ($isCampaignMockupCustom) {
                        $totalDesign = $orderProduct->getTotalDesign($seller);
                        if ($totalDesign === 0 || !empty($orderProduct->custom_options)) {
                            $orderProduct->fulfill_status = OrderProductFulfillStatus::DESIGNING;
                            $order->fulfill_status = OrderFulfillStatus::DESIGNING;
                        }
                    } else {
                        $orderProduct->fulfill_status = OrderProductFulfillStatus::DESIGNING;
                        $order->fulfill_status = OrderFulfillStatus::DESIGNING;
                    }
                }

                if ($order->type === OrderTypeEnum::REGULAR) {
                    $orderProduct->sen_points = $orderProduct->getSenPoints($order, $totalSenPoints);
                }
            });
            $estimatedDeliveryDates = $order->getEstimateDeliveryDates();
            $estimatedDeliveryTo = $estimatedDeliveryDates['to_date']->startOfDay()->toDateTimeString();
            $order->estimate_delivery_date = $estimatedDeliveryTo;
            $order->total_sen_points = $totalSenPoints;
            $order->push();

            $pendingOrders = Order::query()
                ->where([
                    'seller_id' => $order->seller_id,
                    'status' => OrderStatus::PENDING
                ])
                ->whereNotIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                ->where('updated_at', '>=', now()->subDays(30))
                ->count();

            // reward sms credit for seller
            if (!is_null($seller) && $seller->sms_credit <= $pendingOrders * 3) {
                $seller->sms_credit += 5;
                $seller->save();
            }
            OrderService::payProfitsForSellerArtist($order);
            DB::commit();
            // update sale expired at for seller
            if (!is_null($seller)) {
                $seller->updateSaleExpiredAt($order->type, $paid_at);
            }
            // Call event when after order completed
            AfterOrderPaymentCompleted::dispatch($order);
        } catch (Exception $exception) {
            DB::rollBack();
            logToDiscord('Process order completed failed - Order ID: ' . $order->id . ', Message: ' . $exception->getMessage(), channel: 'job', debug: true);
        }

        $logs = [];
        $this->validateAndRenderDesign($order, $logs);

        if ($order->address_verified === OrderAddressVerifiedEnum::INVALID) {
            $logs[] = 'Customer address is invalid';
        }

        if (count($logs) > 0) {
            $order->fulfill_log = implode(';', $logs);
            $order->push();
        }
        $store = Store::query()->find($order->store_id);
        if ($store) {
            $store->increment('total_orders');
        }

        // send order PB
        SendOrderToPBJob::dispatch($order)->onQueue(QueueName::ORDER);
        // update email marketing order tracking
        if ($order->ad_source === 'email_marketing' && !empty($order->ad_id)) {
            MarketingServices::saveTrackingOrder($order->ad_id, $order->id);
        }
        OrderFraudChecking::dispatch($order->id)->onQueue(QueueName::ORDER);
        ReviewOrderTrademark::dispatch($order->id)->onQueue(QueueName::ORDER);
        ProcessVerifyOrderPaymentGateway::dispatch($order->id)->onQueue(QueueName::ORDER);
        CheckOrderAtRisk::dispatch($order->id)->onQueue(QueueName::ORDER);
        dispatch(function () use ($order) {
            $products = $order->products->filter(function (OrderProduct $product) {
                return empty($product->shipping_cost) || empty($product->base_cost);
            })->groupBy('sku')->values();
            if ($products->isNotEmpty()) {
                $product_message = $products->map(function ($groupProducts) {
                    $product = $groupProducts->first();
                    return "- {$product->product_name} ({$product->sku})";
                })->join("\r\n");
                $embedDesc = [
                    [
                        'description' => $product_message,
                        'color' => 15548997
                    ]
                ];
                logToDiscord((app()->isProduction() ? mentionDiscord(DiscordUserIdEnum::BIZDEV) : '') . " Đơn này chưa có shipping cost hoặc base cost. Mọi người check nha.\r\nhttps://admin.senprints.com/order/detail/" . $order->id, channel: DiscordChannel::PTDT_CHANNEL, async: false, embeds: $embedDesc, threadId: 1336274120500973612);
            }
        })->onQueue(QueueName::ORDER);
        // Check and hold order of quantity or quantity large amount
        $order->checkMarkWarningOrder();
        // to check sale limit
        if ($order->payment_gateway_id) {
            /** @var PaymentGateway $model */
            $model = app(PaymentGateway::class);
            $model->holdPaymentGatewayIfSaleLimit($order->payment_gateway_id);
        }
        logToDiscord('Process order completed #' . $this->orderId . ' success', 'job');
    }

    /**
     * @param Order $order
     * @param $logs
     * @return void
     */
    public function validateAndRenderDesign(Order $order, &$logs): void
    {
        $thumbUrls = [];
        $orderFulfillStatus = $order->fulfill_status;
        $seller = User::query()->find($order->seller_id);
        $order->products->map(function ($orderProduct) use ($order, &$logs, &$thumbUrls, &$orderFulfillStatus, $seller) {
            if (!$orderProduct->product_id) {
                // check missing product id
                $logs[] = 'Item #' . $orderProduct->id . ' has no product id';
                $orderProduct->fulfill_status = OrderProductFulfillStatus::INVALID;
                $orderProduct->fulfill_exception_log = 'Miss product id. Please check.';
                $orderFulfillStatus = OrderFulfillStatus::INVALID;
                logToDiscord(
                    'Order #' . $order->id . ' has item #' . $orderProduct->id . ' has no product id',
                    'important'
                );
                return;
            }
            $productSystemType = $orderProduct->campaign_type ?? ProductSystemTypeEnum::REGULAR;
            if ($productSystemType !== ProductSystemTypeEnum::AI_MOCKUP) {
                // validate blanket, canvas
                /** @var OrderProduct $orderProduct */
                if ($orderProduct->personalized !== PersonalizedType::CUSTOM_OPTION && $orderProduct->full_printed && $orderProduct->fulfill_status !== OrderProductFulfillStatus::DESIGNING && !$orderProduct->isNoNeedDesignProductType()) {
                    $approved = OrderProduct::query()
                        ->join('order', 'order.id', '=', 'order_product.order_id')
                        ->select(['order_product.id', 'order_product.order_id'])
                        ->where('order_product.product_id', $orderProduct->product_id)
                        ->where('order_product.order_id', '!=', $orderProduct->order_id)
                        ->whereIn('order_product.fulfill_status', [OrderProductFulfillStatus::UNFULFILLED, OrderProductFulfillStatus::PROCESSING, OrderProductFulfillStatus::FULFILLED])
                        ->where('order.payment_status', OrderPaymentStatus::PAID)
                        ->where('order.stats_status', 1)
                        ->first();

                    // check if product design is approved before
                    if (empty($approved)) {
                        $logs[] = 'Item #' . $orderProduct->id . ' need to review the design';
                        $orderProduct->fulfill_status = OrderProductFulfillStatus::REVIEWING;
                        $orderProduct->fulfill_exception_log = 'Please review design safe area';
                        $orderFulfillStatus = OrderFulfillStatus::REVIEWING;
                    }
                }

                // validate custom text
                if ($orderProduct->personalized && $orderProduct->personalized !== PersonalizedType::CUSTOM_OPTION) {
                    if (!empty($orderProduct->custom_options)) {
                        $custom_options = json_decode(($orderProduct->custom_options));

                        if ($orderProduct->custom_options_regular) {
                            $custom_options = $orderProduct->custom_options_regular;
                        }

                        foreach ($custom_options as $text) {
                            if (preg_match('/[^a-zA-Z0-9 ]/', $text)) {
                                $logs[] = 'Item #' . $orderProduct->id . ' need to review the design';
                                $orderProduct->fulfill_status = OrderProductFulfillStatus::REVIEWING;
                                $orderProduct->fulfill_exception_log = 'Custom text contains special characters. Please review design.';
                                $orderFulfillStatus = OrderFulfillStatus::REVIEWING;
                                break;
                            }
                        }
                    } else {
                        $logs[] = 'Item #' . $orderProduct->id . ' has no custom text';
                        $orderProduct->fulfill_status = OrderProductFulfillStatus::INVALID;
                        $orderProduct->fulfill_exception_log = 'Miss custom text. Please check with customer.';
                        $orderFulfillStatus = OrderFulfillStatus::INVALID;
                    }
                }

                // check missing design
                if (!$orderProduct->full_printed) {
                    $count3d = File::query()
                        ->onSellerConnection($seller)
                        ->where('product_id', $orderProduct->product_id)
                        ->where('type', FileTypeEnum::DESIGN)
                        ->where('option', FileRenderType::RENDER_3D)
                        ->distinct('print_space')
                        ->count('print_space');

                    $countDesign = File::query()
                        ->onSellerConnection($seller)
                        ->where('product_id', $orderProduct->product_id)
                        ->where('type', FileTypeEnum::DESIGN)
                        ->where('option', '!=', '3d')
                        ->distinct('print_space')
                        ->count('print_space');

                    if ($count3d !== $countDesign) {
                        $logs[] = 'Item #' . $orderProduct->id . ' may miss design front/back';
                        $orderProduct->fulfill_status = OrderProductFulfillStatus::REVIEWING;
                        $orderProduct->fulfill_exception_log = 'Product may miss design front/back';
                        $orderFulfillStatus = OrderFulfillStatus::REVIEWING;
                    }
                }

                $totalDesign = $orderProduct->getTotalDesign($seller);
                if (!empty($orderProduct->custom_print_space) && $totalDesign < 2) {
                    $logs[] = 'Item #' . $orderProduct->id . ' may miss design front/back';
                    $orderProduct->fulfill_status = OrderProductFulfillStatus::REVIEWING;
                    $orderProduct->fulfill_exception_log = 'Product may miss design front/back';
                    $orderFulfillStatus = OrderFulfillStatus::REVIEWING;
                }

                // Move thumb image from tmp to main folder
                try {
                    $tempThumbUrl = $orderProduct->thumb_url;

                    if (isset($thumbUrls[$tempThumbUrl])) {
                        $newThumbUrl = $thumbUrls[$tempThumbUrl];
                    } else {
                        $newPath = 'o/' . $orderProduct->order_id;
                        $newThumbUrl = saveTempFileAws($tempThumbUrl, $newPath);
                        $thumbUrls[$tempThumbUrl] = $newThumbUrl;
                    }
                    $orderProduct->thumb_url = $newThumbUrl;
                } catch (Exception $e) {
                    logToDiscord('validateAndRenderDesign error: ' . $e->getMessage());
                }
            }
            $isCampaignMockupCustom = in_array($productSystemType, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true);
            $hasCustomOptions = $orderProduct->personalized === PersonalizedType::CUSTOM_OPTION || $isCampaignMockupCustom;
            if ($hasCustomOptions && Str::isJson($orderProduct->custom_options)) {
                $customOptions = json_decode($orderProduct->custom_options, true, 512, JSON_THROW_ON_ERROR);
                if (!empty($customOptions)) {
                    try {
                        foreach ($customOptions as $key => $option) {
                            foreach ($option as $kOp => $op) {
                                if ($op['type'] === CustomOptionTypeEnum::IMAGE && !empty($op['imagePath']) && str_starts_with($op['imagePath'], 'tmp/')) {
                                    if (isset($thumbUrls[$op['imagePath']])) {
                                        $newThumbUrl = $thumbUrls[$op['imagePath']];
                                    } else {
                                        $newPath = 'o/' . $orderProduct->order_id . '/custom_options';
                                        $newThumbUrl = copyS3TempToMain($op['imagePath'], $newPath);
                                        $thumbUrls[$op['imagePath']] = $newThumbUrl;
                                    }
                                    $customOptions[$key][$kOp]['imageTempPath'] = $op['imagePath'];
                                    $customOptions[$key][$kOp]['imagePath'] = $newThumbUrl;
                                }
                            }
                        }
                        $orderProduct->custom_options = json_encode($customOptions);
                    } catch (Exception $e) {
                        logToDiscord('saveCustomOptions error: ' . $e->getMessage());
                    }
                }
            }
            $orderProduct->save();
            // get custom color if product is full_printed
            $color = null;
            $size = null;
            if ($orderProduct->isFullPrintedType()) {
                $options = json_decode($orderProduct->options, true);
                if (isset($options['color'])) {
                    $color = correctOptionValue($options['color']);
                }
                if (isset($options['size'])) {
                    $size = correctOptionValue($options['size']);
                }
            }

            if ($orderProduct->full_printed !== ProductPrintType::PRINT_3D_FULL) {
                // Render design to print
                $files = File::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'product_id' => $orderProduct->product_id,
                        'type' => FileTypeEnum::DESIGN,
                        'option' => FileRenderType::PRINT
                    ])
                    ->when(!empty($size), function ($q) use ($size) {
                        // check if $size includes column print_space
                        return $q->where(function ($q) use ($size) {
                            return $q->whereRaw("INSTR('$size', `print_space`)")
                                ->orWhere('print_space', 'default');
                        });
                    })
                    ->whereNotNull('design_json')
                    ->get();

                $designs = Design::query()
                    ->where([
                        'type' => DesignTypeEnum::PRINT,
                        'order_product_id' => $orderProduct->id
                    ])
                    ->whereNull('file_url')
                    ->whereNotNull('design_json')
                    ->get();

                if ($files->isNotEmpty()) {
                    $files->map(function ($file) use ($order, $orderProduct, $color) {
                        if (!$orderProduct->personalized && $color && $color !== 'white' && $orderProduct->isFullPrintedType()) {
                            $designJson = json_decode($file->design_json);
                            $designJson->custom_background = color2hex($color);
                            $designJson->background = color2hex($color);
                            [$designJson,] = FulfillmentService::updateUrlOnDesignJson($designJson);
                            $design = Design::query()->create([
                                'order_id' => $order->id,
                                'product_id' => $orderProduct->product_id,
                                'order_product_id' => $orderProduct->id,
                                'type' => DesignTypeEnum::PRINT,
                                'print_space' => $file->print_space,
                                'design_json' => $designJson,
                            ]);
                            RenderPrintJob::dispatch($design, 'design')->onQueue('render');
                        } else if ($file->file_url_2 === null) {
                            $file->order_id = $order->id;
                            $file->order_product_id = $orderProduct->id;
                            RenderPrintJob::dispatch($file, 'file')->onQueue('render');
                        }
                    });
                }

                if ($designs->isNotEmpty()) {
                    // set design color
                    $designs->map(function ($design) use ($orderProduct, $color, &$orderFulfillStatus, &$logs) {
                        $designJson = json_decode($design->design_json, false);
                        if ($color && $color !== 'white' && $orderProduct->isFullPrintedType()) {
                            $designJson->background = color2hex($color);
                            [$designJson,] = FulfillmentService::updateUrlOnDesignJson($designJson);
                            $design->design_json = $designJson;
                            $design->save();
                        }
                        $objects = Str::isJson($designJson) ? data_get(json_decode($designJson, true, 512, JSON_THROW_ON_ERROR), 'objects') : $designJson->objects;
                        $isValid = true;
                        if (!empty($objects)) {
                            foreach ($objects as $obj) {
                                if ($obj->type === 'i-text' && empty($obj->fontUrl)) {
                                    $logs[] = 'Item #' . $orderProduct->id . ': design misses font URL';
                                    $orderProduct->fulfill_status = OrderProductFulfillStatus::INVALID;
                                    $orderProduct->fulfill_exception_log = 'Miss font URL. Check design JSON.';
                                    $orderFulfillStatus = OrderFulfillStatus::INVALID;
                                    break;
                                }
                            }
                        } else {
                            $logs[] = 'Item #' . $orderProduct->id . ': design JSON is invalid';
                            $orderProduct->fulfill_status = OrderProductFulfillStatus::INVALID;
                            $orderProduct->fulfill_exception_log = 'Design JSON is invalid';
                            $orderFulfillStatus = OrderFulfillStatus::INVALID;
                            $isValid = false;
                        }

                        if ($isValid) {
                            RenderPrintJob::dispatch($design, 'design')->onQueue('render');
                        }
                    });
                }
            }

            // check item limit for supplier and fulfill product
            $supplier = Supplier::getAndCacheHoldsById($orderProduct->supplier_id);

            if ($supplier && $supplier->max_items > 0 && (!$supplier->holds_until || Carbon::parse($supplier->holds_until)->lt(now()))) {
                $fulfillItemsLast24Hours = Supplier::fulfillItemsLast24Hours($orderProduct->supplier_id);
                if ($fulfillItemsLast24Hours >= $supplier->max_items) {
                    Supplier::query()
                        ->where('id', $supplier->id)
                        ->update([
                            'holds_until' => now()->addHour()
                        ]);
                    Supplier::getRefreshCacheById($orderProduct->supplier_id);
                }
            }

            $productFulfillMapping = ProductFulfillMapping::getAndCacheHoldsById($orderProduct->template_id, $orderProduct->fulfill_product_id);

            if ($productFulfillMapping && $productFulfillMapping->max_items > 0 && (!$productFulfillMapping->holds_until || Carbon::parse($productFulfillMapping->holds_until)->lt(now()))) {
                $fulfillItemsLast24Hours = ProductFulfillMapping::fulfillItemsLast24Hours($orderProduct->template_id, $orderProduct->fulfill_product_id);
                if ($fulfillItemsLast24Hours >= $productFulfillMapping->max_items) {
                    ProductFulfillMapping::query()
                        ->where('product_id', $orderProduct->template_id)
                        ->where('fulfill_product_id', $orderProduct->fulfill_product_id)
                        ->update([
                            'holds_until' => now()->addHour()
                        ])
                    ;
                    ProductFulfillMapping::getRefreshCacheById($orderProduct->template_id);
                }
            }
        });

        if ($order->fulfill_status === OrderFulfillStatus::UNFULFILLED || $order->fulfill_status === OrderFulfillStatus::DESIGNING) {
            $order->fulfill_status = $orderFulfillStatus;
        }
    }

    /**
     * Handle the failing job.
     *
     * @param Throwable $exception
     *
     * @return void
     */
    public function failed(Throwable $exception): void
    {
        logToDiscord('Process order completed #' . $this->orderId . ' failed: ' . $exception->getMessage(), 'job');
    }
}

<?php

namespace App\Jobs;

use App\Enums\AbandonedLogStatusEnum;
use App\Enums\OrderFraudStatus;
use App\Enums\OrderStatus;
use App\Enums\SendMail\Template;
use App\Enums\StorageDisksEnum;
use App\Enums\TempTypeEnum;
use App\Models\AbandonedLog;
use App\Models\ApiLog;
use App\Models\Design;
use App\Models\File;
use App\Models\IndexOrder;
use App\Models\IndexOrderProduct;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\SendMailLog;
use App\Models\SmsLog;
use App\Models\Temp;
use App\Models\TempEventLog;
use App\Models\TrademarkResult;
use App\Models\UserSessions;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Models\ImportCampaignsData;

class CleanDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $debug = false;
    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $startTime = round(microtime(true) * 1000);
            $limit = 500;
            // soft delete draft order after 35 days
            $draftOrderIds = Order::query()
                ->where('status', OrderStatus::DRAFT)
                ->where('updated_at', '<', now()->subDays(35))
                ->limit($limit)
                ->pluck('id');
            $flaggedOrderIds = Order::query()
                ->whereIn('status', [OrderStatus::PENDING])
                ->where('fraud_status', '!=', OrderFraudStatus::TRUSTED)
                ->where('updated_at', '<', now()->subDays(7))
                ->limit($limit)
                ->pluck('id');
            $softDeleteOrderIds = $draftOrderIds->merge($flaggedOrderIds);
            OrderProduct::query()
                ->whereIn('order_id', $softDeleteOrderIds)
                ->delete();
            Order::query()
                ->whereIn('id', $softDeleteOrderIds)
                ->delete();
            IndexOrderProduct::query()
                ->whereIn('order_id', $softDeleteOrderIds)
                ->delete();
            IndexOrder::query()
                ->whereIn('id', $softDeleteOrderIds)
                ->delete();
            unset($softDeleteOrderIds, $flaggedOrderIds, $draftOrderIds); // free ram
            //soft delete abandoned test orders
            Order::query()
                ->where('created_at', '<=', now()->subDay())
                ->where('order.status', '=', 'pending')
                ->where('order.payment_status', '=', 'unpaid')
                ->where('order.customer_email', '=', '<EMAIL>')
                ->delete();
            IndexOrder::query()
                ->where('created_at', '<=', now()->subDay())
                ->where('order.status', '=', 'pending')
                ->where('order.payment_status', '=', 'unpaid')
                ->where('order.customer_email', '=', '<EMAIL>')
                ->delete();

            // clean deleted order/order product
            $deleteOrderIds = Order::query()->withTrashed()->where('deleted_at', '<', now()->subDays(30))->limit($limit)->pluck('id');
            $deleteOrderProductIds = OrderProduct::query()->withTrashed()->where('deleted_at', '<', now()->subDays(30))->limit($limit)->pluck('id');
            if ($deleteOrderIds) {
                $deleteOrderIds->chunk(100)->each(function ($deleteOrderIdsChunk) {
                    File::query()->withoutGlobalScope('getActive')->whereIn('order_id', $deleteOrderIdsChunk)->delete();
                });
                Design::query()->withoutGlobalScope('getActive')->whereIn('order_id', $deleteOrderIds)->delete();
                OrderHistory::query()->whereIn('order_id', $deleteOrderIds)->delete();
                OrderProduct::query()->withTrashed()->whereIn('id', $deleteOrderIds)->forceDelete();
                IndexOrderProduct::query()->withTrashed()->whereIn('id', $deleteOrderIds)->forceDelete();
                Order::query()->withTrashed()->whereIn('id', $deleteOrderIds)->forceDelete();
                IndexOrder::query()->withTrashed()->whereIn('id', $deleteOrderIds)->forceDelete();
            }
            if ($deleteOrderProductIds) {
                $deleteOrderProductIds->chunk(100)->each(function ($deleteOrderProductIdsChunk) {
                    File::query()->withoutGlobalScope('getActive')->whereIn('order_product_id', $deleteOrderProductIdsChunk)->delete();
                });
                Design::query()->withoutGlobalScope('getActive')->whereIn('order_product_id', $deleteOrderProductIds)->delete();
                OrderProduct::query()->withTrashed()->whereIn('id', $deleteOrderProductIds)->forceDelete();
                IndexOrderProduct::query()->withTrashed()->whereIn('id', $deleteOrderProductIds)->forceDelete();
            }

            if ($deleteOrderIds->count() > 0) {
                foreach (StorageDisksEnum::activeStorages() as $storage) {
                    $disk = Storage::disk($storage);
                    foreach ($deleteOrderIds as $orderId) {
                        if ($disk->exists('o/' . $orderId)) {
                            $disk->deleteDirectory('o/' . $orderId);
                        }
                    }
                }
            }
            unset($deleteOrderIds, $deleteOrderProductIds); // free ram
            // delete api log after 90 days
            ApiLog::query()
                ->where('created_at', '<', now()->subDays(90))
                ->delete();
            ApiLog::query()
                ->where('name', 'CRAWL_ORDER')
                ->where('created_at', '<', now()->subDays(14))
                ->delete();

            TempEventLog::query()
                ->where('timestamp', '<', now()->subDays(7))
                ->delete();

            // delete old trademark result
            TrademarkResult::query()
                ->where('updated_at', '<', now()->subDays(90))
                ->delete();

            // delete old email logs, exclude buyer order confirmation
            $start = now()->subDays(60);
            $end = $start->clone()->subDays(7);
            $mail_log_ids = SendMailLog::query()
                ->select('id')
                ->whereBetween('created_at', [$end, $start])
                ->whereNotIn('template', [Template::BUYER_ORDER_CONFIRMATION, Template::BUYER_ORDER_INVALID_ADDRESS_NOTIFICATION])
                ->get()
                ->pluck('id')
                ->toArray();
            if (!empty($mail_log_ids)) {
                SendMailLog::query()->whereIn('id', $mail_log_ids)->delete();
            }

            // delete old email logs after 7 months
            $mail_log_ids = SendMailLog::query()
                ->select('id')
                ->where('created_at', '<=', now()->subMonths(7))
                ->get()
                ->pluck('id')
                ->toArray();
            if (!empty($mail_log_ids)) {
                foreach (array_chunk($mail_log_ids, 1000) as $chunk) {
                    SendMailLog::query()->whereIn('id', $chunk)->delete();
                }
            }

            // delete old sms logs after 7 months
            SmsLog::query()
                ->select('id')
                ->where('created_at', '<=', now()->subMonths(7))
                ->orderBy('created_at')
                ->chunk(1000, function ($logs) {
                    $result = [];
                    foreach ($logs as $log) {
                        $result[] = $log->id;
                    }
                    SmsLog::query()->whereIn('id', $result)->delete();
                });

            AbandonedLog::query()->where('status', AbandonedLogStatusEnum::CANCELLED)
                ->where('created_at', '<', now()->subDays(7))
                ->delete();

            ImportCampaignsData::query()->where('status', ImportCampaignStatusEnum::COMPLETED)
                ->where('created_at', '<', now()->subDays(90))
                ->forceDelete();

            ImportCampaignsData::query()->where('status', ImportCampaignStatusEnum::COMPLETED)
                ->where('created_at', '<=', now()->subDays(7))
                ->where('created_at', '>=', now()->subDays(7)->subHour())
                ->update(['mockups' => null]);

            Temp::query()
                ->where('type', TempTypeEnum::CLEAN_INDEX_EVENT_LOGS)
                ->where('status', 0)
                ->delete();

            // delete expired cache
            DB::table('cache')->where('expiration', '<', time())->delete();
            UserSessions::query()->whereNotNull('deleted_at')->forceDelete();
            if ($this->debug) {
                $endTime = round(microtime(true) * 1000);
                $time = $endTime - $startTime;
                Log::info("CleanDataJob : Clean data completed in $time ms");
            }
        } catch (\Throwable $e) {
            logException($e);
        }
    }
}

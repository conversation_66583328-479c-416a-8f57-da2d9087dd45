<?php

namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Enums\SystemConfigTypeEnum;
use App\Models\Product;
use App\Models\SystemConfig;
use App\Traits\ElasticClient;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class SyncProductsToSingleStoreAndMysqlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ElasticClient;

    protected string $queueName = 'sync-product-singlestore';
    protected int $limit;

    /**
     * @param int $limit
     */
    public function __construct($limit = 1000)
    {
        $this->limit = $limit;
        $this->onQueue($this->queueName);
    }

    /**
     * @throws \Throwable
     */
    public function handle()
    {
        try {
            $this->startProcess();
        } catch (\Throwable $e) {
            SystemConfig::setConfig(CacheKeys::SYNC_PRODUCTS_TO_SINGLESTORE, array(
                'value' => null,
                'status' => 1,
                'type' => SystemConfigTypeEnum::BACKEND
            ));
            logToDiscord("Sync products from Mysql to SingleStore error: {$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}");
        }
    }

    /**
     * @return void
     * @throws \Throwable
     */
    private function startProcess()
    {
        $to_connection = 'singlestore';
        $total = $this->syncProducts($to_connection);
        graylogInfo('Start sync products to ' . Str::upper($to_connection), [
            'category' => 'products_sync_log',
            'user_type' => 'system',
            'action' => "sync"
        ]);
        if ($total <= $this->limit) {
            graylogInfo('End sync products to ' . Str::upper($to_connection) . ': ' . $total . ' / ' . $this->limit, [
                'category' => 'products_sync_log',
                'user_type' => 'system',
                'action' => "sync"
            ]);
            SystemConfig::setConfig(CacheKeys::SYNC_PRODUCTS_TO_SINGLESTORE, array(
                'value' => null,
                'status' => 1,
                'type' => SystemConfigTypeEnum::BACKEND
            ));
            return;
        }
        dispatch(new self($this->limit));
    }

    /**
     * @return integer
     * @throws \Throwable
     */
    private function syncProducts($to_connection)
    {
        $size = 10; // number of job can dispatch
        $query = Product::query()->select('id')->where('sync_status', '=', Product::SYNC_DATA_TO_SINGLE_STORE)->withTrashed();
        $totalPending = $query->count();
        if ($totalPending === 0) {
            return 0;
        }
        if ($totalPending <= $this->limit) {
            return (new SyncProductsToSingleStoreWithId($query->get()->pluck('id')->toArray(), $to_connection, $this->limit))->handle();
        }
        $query->limit($this->limit * $size)->get()->chunk($this->limit)->each(function ($products) use ($to_connection, &$totalPending) {
            dispatch(new SyncProductsToSingleStoreWithId($products->pluck('id')->toArray(), $to_connection, $this->limit))->onQueue($this->queueName);
            $totalPending -= $products->count();
        });
        return $totalPending;
    }
}

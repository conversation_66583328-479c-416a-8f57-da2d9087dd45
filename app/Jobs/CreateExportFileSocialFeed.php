<?php
/** @noinspection TypeUnsafeComparisonInspection */

/** @noinspection FopenBinaryUnsafeUsageInspection */

namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Enums\FeedPlatformEnum;
use App\Enums\PricingModeEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\QueueName;
use App\Enums\ShippingMethodEnum;
use App\Enums\StorageDisksEnum;
use App\Http\Controllers\SystemConfigController;
use App\Models\Campaign;
use App\Models\Elastic;
use App\Models\ProductVariant;
use App\Models\ShippingRule;
use App\Models\SocialFeed;
use App\Models\SocialFeedImages;
use App\Models\Store;
use App\Models\SystemConfig;
use App\Models\Template;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use RuntimeException;
use function DeepCopy\deep_copy;

class CreateExportFileSocialFeed implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    public int $tries = 1;
    public $timeout = 21600;
    public bool $preDownloadImage = false;
    public int $totalNeedDownloadImage = 0;

    protected $feedId;
    protected string $jobId;
    protected string $queueName = QueueName::SOCIAL_FEED;
    protected ?SocialFeed $feed = null;
    protected object $store;
    protected array $path;
    protected array $fileSize = [
        'xml_file_size' => 0,
        'csv_file_size' => 0,
    ];

    public function __construct($feedId, $queueName = QueueName::SOCIAL_FEED)
    {
        $this->feedId = $feedId;
        $this->jobId = 'export_feed_job_' . $feedId;
        $this->queueName = $queueName;
        $this->onQueue($this->queueName);
    }

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        // set memory limit to unlimited
        ini_set("memory_limit", -1);
        cache()->tags([
            CacheKeys::SOCIAL_FEED,
        ])->put($this->jobId, 'Started', CacheKeys::CACHE_1H);

        $this->feed = SocialFeed::query()
            ->with([
                'seller' => function ($query) {
                    $query->select(
                        [
                            'id',
                            'market_location',
                            'name',
                        ]
                    );
                },
            ])
            ->findOrFail($this->feedId);

        $this->log('Start Job');

        $this->store = Store::query()
            ->select(
                [
                    'id',
                    'name',
                    'sub_domain',
                    'domain',
                    'domain_status',
                    'is_proxy',
                    'enable_product_name_export_feed',
                ]
            )
            ->addSelect([
                'custom_payment' => User::query()
                    ->select('custom_payment')
                    ->whereColumn('id', 'store.seller_id')
                    ->limit(1)
            ])
            ->where('id', $this->feed->store_id)
            ->firstOrFail()
            ->append([
                'base_url',
            ]);

        $lastProductId = null;
        $index = 1;
        $options = [];
        $templateVariants = [];
        $templateShippingRules = [];
        $templates = [];
        $pricing = UserService::getPricingBySellerId($this->feed->seller_id);
        $pricingByTemplate = collect();
        $filters = $this->feed->filters ?? [];
        $numberRecords = 0;
        if (!empty($filters['use_image_custom_domain']) && ($this->store->is_proxy || $this->store->custom_payment)) {
            $imageBaseUrl = 'https://images.' . $this->store->domain;
        } else {
            $imageBaseUrl = null;
        }

        $defaultMarketLocation = $this->feed->seller->market_location ?? '*';

        $queryVariant = ProductVariant::query()
            ->select(
                [
                    'variant_key',
                    'location_code',
                    'out_of_stock',
                    'price',
                    'adjust_price',
                ]
            );

        $queryShippingRule = ShippingRule::query()
            ->select(
                [
                    'shipping_cost',
                    'variant_key',
                ]
            )
            ->whereNull('seller_id')
            ->whereNull('store_id')
            ->whereNull('supplier_id')
            ->orderBy('variant_key');

        $this->path['csv'] = tempnam(sys_get_temp_dir(), 'feed_' . $this->feedId);
        $arrHeading = [
            'id',
            'title',
            'description',
            'link',
            'image_link',
            'availability',
            'price',
            'google_product_category',
            'product_type',
            'brand',
            'shipping',
            'identifier_exists',
            'condition',
            'age_group',
            'color',
            'gender',
            'material',
            'size',
            'item_group_id',
            'shipping_label',
        ];
        if (!empty($filters['include_mpn'])) {
            $arrHeading[] = 'mpn';
        }
        $arrHeading = array_merge($arrHeading, [
            'custom_label_0',
            'custom_label_1',
            'custom_label_2',
            'custom_label_3',
            'custom_label_4',
        ]);

        if ($this->feed->platform === FeedPlatformEnum::KLAVIYO) {
            $arrHeading = array_diff($arrHeading, [
                'google_product_category',
                'identifier_exists',
                'custom_label_0',
                'custom_label_1',
                'custom_label_2',
                'custom_label_3',
                'custom_label_4',
            ]);
        }

        $fileCSV = fopen($this->path['csv'], 'a');
        fputcsv($fileCSV, $arrHeading);

        $this->path['xml'] = tempnam(sys_get_temp_dir(), 'feed_' . $this->feedId);
        $fileXML = fopen($this->path['xml'], 'a');
        fwrite($fileXML, '<?xml version="1.0" encoding="utf-8"?>');
        fwrite($fileXML, '<rss version="2.0" xmlns:g="http://base.google.com/ns/1.0">');
        fwrite($fileXML, '<channel>');
        fwrite($fileXML, '<title>' . $this->feed->name . '</title>');
        fwrite($fileXML, '<link>' . $this->store->base_url . '</link>');
        fwrite($fileXML, '<description>' . $this->feed->name . '</description>');
        $default_gender = 0;
        if (!empty($filters['default_gender'])) {
            $default_gender = (int) $filters['default_gender'];
        }
        $genders = [
            1 => 'unisex',
            2 => 'male',
            3 => 'female'
        ];
        $isCombineTitle = !empty($filters['combine_title']);
        $isCombineDescription = !empty($filters['combine_description']);
        do {
            $this->checkKilled();
            $this->log('Start Loop', [
                'index' => $index,
            ]);

            $time = microtime(true);

            $retry = 3;
            for ($i = 0; $i < $retry; $i++) {
                $products = (new Elastic())->getProductWithDefaultFeed($filters, false, $lastProductId);
                if (!empty($products)) {
                    break;
                }
                sleep($this->getDelay());
            }

            if (empty($products)) {
                $this->handleComplete();
                break;
            }
            $countProducts = count($products);
            $this->log('Get Product Elastic', [
                'index' => $index,
                'time'  => microtime(true) - $time,
                'total' => $countProducts,
            ]);

            $arr = [];

            $time = microtime(true);
            $campaigns = [];
            collect($products)->groupBy('seller_id')->map(function ($productsGroup, $sellerId) use (&$campaigns) {
                $seller = UserService::getSellerSharding($sellerId);
                $campaigns[$sellerId] = Campaign::query()->onSellerConnection($seller)->select(['id', 'name', 'description', 'default_product_id', 'system_type'])->whereIn('id', $productsGroup->pluck('campaign_id'))->get();
            });
            foreach ($products as $key => $product) {
                if ($this->isRecordLimitExceeded($numberRecords, $filters)) {
                    break;
                }
                $campaignId = data_get($product, 'campaign_id', data_get($product, 'id'));
                $isExpressProduct = Arr::get($product, 'system_product_type') === ProductType::PRODUCT_TEMPLATE;
                $lastProductId = $isExpressProduct ? $product['express_product_id'] : $product['id'];
                $campaign = isset($campaigns[$product['seller_id']]) ? $campaigns[$product['seller_id']]->firstWhere('id', $product['campaign_id']) : null;
                if (!empty($product['system_type']) && $product['system_type'] === ProductSystemTypeEnum::MOCKUP) {
                    $lastProductId = $product['campaign_id'];
                    if ($campaign) {
                        $lastProductId = $product['campaign_id'] . '-' . data_get($campaign, 'default_product_id');
                    }
                }

                $pricingMode = Arr::get($product, 'pricing_mode');
                $options['colors'] = [];
                $options['sizes'] = [];

                $templateId = $product['template_id'];

                if (!isset($templates[$templateId])) {
                    $object = Template::findAndCacheByKey($templateId, false);
                    if (!$object || $object->status === ProductStatus::INACTIVE) {
                        $templates[$templateId] = false;
                    } else {
                        $templates[$templateId] = $object->toArray();
                        $pricingByTemplate[$templateId] = $pricing->filter(function ($each) use ($templateId) {
                            return $each->product_id == $templateId;
                        });
                    }
                }

                $template = $templates[$templateId];

                if (!$template) {
                    $this->log('Template not found', [
                        'product_id' => $product['id'],
                        'template'   => $templateId,
                    ], 'warning');
                    unset($products[$key]);
                    continue;
                }

                if (empty($product['slug'])) {
                    $this->log('Product slug not found', [
                        'product_id' => $product['id'],
                    ], 'warning');
                    unset($products[$key]);
                    continue;
                }

                $googleCategoryId = $template['google_category_id'];

                if (!$googleCategoryId) {
                    $this->log('Template dont have google category', [
                        'product_id' => $product['id'],
                        'template'   => $templateId,
                    ], 'warning');
                    unset($products[$key]);
                    continue;
                }

                $marketLocation = Arr::get($product, 'market_location', $defaultMarketLocation);
                $indexTemplate = $templateId . '-' . $marketLocation;

                if ($pricingMode === PricingModeEnum::CUSTOM_PRICE && !$isExpressProduct) {
                    $variants = $queryVariant->clone()
                        ->where('product_id', $product['id'])
                        ->where('location_code', $marketLocation)
                        ->get()
                        ->toArray();
                } else {
                    if (empty($templateVariants[$indexTemplate])) {
                        $templateVariants[$indexTemplate] = ProductVariant::findAndCacheByTemplate($templateId)
                            ->where('location_code', $marketLocation)
                            ->toArray();
                    }
                    $variants = deep_copy($templateVariants[$indexTemplate]);
                }

                if (empty($templateShippingRules[$indexTemplate])) {
                    $json = $queryShippingRule->clone()
                        ->where('product_id', $templateId)
                        ->where('location_code', $marketLocation)
                        ->where('shipping_method', ShippingMethodEnum::STANDARD)
                        ->get()
                        ->toJson();

                    $templateShippingRules[$indexTemplate] = json_decode($json);
                }
                $shippingRules = deep_copy($templateShippingRules[$indexTemplate]);

                $productOptions = $product['options'];

                // get match colors and sizes from filters
                if (!empty($filters['default_color_only'])) {
                    $productColors = [$product['default_option']];
                } else {
                    $productColors = Arr::get($productOptions, 'color', []);
                }
                if (!empty($filters['other_colors_only']) && !empty($productColors)) {
                    $productColors = array_diff($productColors, [$product['default_option']]);
                    $productColors = array_values($productColors);
                }

                if (!empty($filters['colors'])) {
                    foreach ($productColors as $productColor) {
                        if (in_array($productColor, $filters['colors'], true) && !in_array($productColor, $options['colors'], true)) {
                            $options['colors'][] = $productColor;
                        }
                    }

                    // if have filter then must have values
                    if (empty($options['colors'])) {
                        unset($products[$key]);
                        continue;
                    }
                } else {
                    $options['colors'] = $productColors;
                }
                $productOptions['color'] = $options['colors'];

                $productSizes = Arr::get($productOptions, 'size', []);

                if (!empty($filters['sizes'])) {
                    foreach ($productSizes as $productSize) {
                        if (in_array($productSize, $filters['sizes']) && !in_array($productSize, $options['sizes'])) {
                            $options['sizes'][] = $productSize;
                        }
                    }

                    // if have filter then must have values
                    if (empty($options['sizes'])) {
                        unset($products[$key]);
                        continue;
                    }
                } else {
                    $options['sizes'] = $productSizes;
                }
                $productOptions['size'] = $options['sizes'];

                sortArrayOptions($productOptions);
                $variantKeys = generateVariantKeysByProductOptions($productOptions, true);
                if (!empty($filters['other_colors_only']) && count($productOptions['color']) === 0) {
                    $this->log('Export other colors only but empty.', [
                        'product_id' => $product['id'],
                        'template'   => $templateId,
                    ], 'warning');
                    unset($products[$key]);
                    continue;
                }
                $gender = 'unisex';
                if ($default_gender === 0) {
                    foreach (Arr::get($product, 'category_slugs', []) as $categorySlug) {
                        if (str_contains($categorySlug, 'women')) {
                            $gender = 'female';
                            break;
                        }
                        if (str_contains($categorySlug, 'men')) {
                            $gender = 'male';
                            break;
                        }
                    }
                } else if (isset($genders[$default_gender])) {
                    $gender = $genders[$default_gender];
                }
                $this->preDownloadImage = !empty($filters['pre_download_image']);
                $time1 = microtime(true);
                foreach ($variantKeys as $each) {
                    $color = Arr::get($each, 'color', '');
                    $size = Arr::get($each, 'size', '');

                    // default
                    $price = $product['price'];
                    $availability = 'in stock';
                    $locationCode = '*';
                    $adjustPrice = 0;

                    // find match variant
                    foreach ($variants as $indexVariant => $variant) {
                        if ($variant['variant_key'] === $each['variant_key']) {
                            if ($pricingMode === PricingModeEnum::CUSTOM_PRICE) {
                                $price = $variant['price'];
                            } elseif ($pricingMode === PricingModeEnum::ADJUST_PRICE) {
                                $adjustPrice = ceil($variant['adjust_price']);
                                $price += $adjustPrice;
                            }
                            $availability = (!$variant['out_of_stock']) ? 'in stock' : 'out of stock';
                            $locationCode = $variant['location_code'];
                            unset($variants[$indexVariant]);
                            break;
                        }
                    }

                    $shippingCost = $template['shipping_cost'];
                    // default first rule
                    if (!empty($shippingRules)) {
                        $shippingRule = Arr::first($shippingRules);
                        $shippingCost = $shippingRule->shipping_cost;
                    }

                    // find match shipping rule
                    foreach ($shippingRules as $indexShippingRule => $shippingRule) {
                        if ($shippingRule->variant_key === $each['variant_key']) {
                            $shippingCost = $shippingRule->shipping_cost;
                            unset($shippingRules[$indexShippingRule]);
                            break;
                        }
                    }

                    $productPricing = $pricingByTemplate[$templateId]
                        ->filter(
                            function ($savedPrice) use ($pricingMode, $productOptions, $each) {
                                if ($pricingMode !== PricingModeEnum::CUSTOM_PRICE) {
                                    return false;
                                }

                                $variantKey = $each['variant_key'];

                                if (!empty($each['color']) && $each['color'] !== 'white') {
                                    $colorPosition = getPositionOfKeyInOptions('color', $productOptions);
                                    $explode = explode('-', $variantKey);
                                    $explode[$colorPosition] = 'white';

                                    $variantKey = implode('-', $explode);
                                }

                                return $savedPrice->variant_key === $variantKey;
                            })
                        ->sortBy('price')
                        ->first();

                    if ($productPricing) {
                        $rate = SystemConfigController::findOrDefaultCurrency($productPricing['currency_code'])->rate;
                        $productPricing['price'] /= $rate;

                        if ($pricingMode === PricingModeEnum::ADJUST_PRICE) {
                            $productPricing['price'] += $adjustPrice;
                        }

                        $price = round($productPricing['price'], 2);
                    }

                    if (empty($filters['id_only'])) {
                        $id = $lastProductId . '|' . $each['variant_key'] . '|' . $locationCode;
                    } else {
                        $id = $lastProductId;
                    }

                    if (empty($filters['include_mpn'])) {
                        $identifierExists = 'no';
                    } else {
                        $identifierExists = 'yes';
                    }
                    $link = getLinkProductOnStoreByOptions(
                        $this->store->base_url,
                        $product['slug'],
                        $product['name'],
                        $each
                    );
                    $urlParams = Arr::get($this->feed->custom_labels, 'url_params');
                    if ($urlParams) {
                        $link .= '&' . $urlParams;
                    }
                    $baseImgUrl = $imageBaseUrl ?? config('senprints.base_img_url');
                    $imageLink = imgUrl($product['thumb_url'], 'full_hd', $color, $baseImgUrl); // chú ý không đổi type ảnh
                    if ($this->preDownloadImage) {
                        $extension = strtolower(pathinfo($product['thumb_url'], PATHINFO_EXTENSION));
                        if (empty($extension)) {
                            $extension = 'jpg';
                        }
                        $imagePath = 'p/' . $campaignId . '/' . md5($imageLink) . '.' . $extension;
                        $oldPath = str_replace($baseImgUrl, '', $imageLink);
                        $feedImage = SocialFeedImages::query()->where('seller_id', $this->feed->seller_id)->where('old_path', $oldPath)->first();
                        if ($feedImage) {
                            $imagePath = $feedImage->new_path;
                        } else {
                            SocialFeedImages::query()->create([
                                'seller_id' => $this->feed->seller_id,
                                'feed_id' => $this->feedId,
                                'old_path' => $oldPath,
                                'new_path' => $imagePath
                            ]);
                            $this->totalNeedDownloadImage++;
                        }
                        $imageLink = imgUrl($imagePath, 'original', null, $baseImgUrl);
                    }

                    $suffix = $this->store->enable_product_name_export_feed ? $product['name'] : '';
                    $priceValue = $price . ' USD';
                    if ($this->feed->platform === FeedPlatformEnum::KLAVIYO && empty($filters['include_price_currency'])) {
                        $priceValue = $price;
                    }

                    // get product template 'atrributes'
                    $attributes = $template['attributes'];
                    $attributes = json_decode($attributes, true);
                    $googleFeedAttributes = $attributes['google_feed_attributes'] ?? [];

                    // get material from google feed attributes get first element has name 'material'
                    $material = Arr::first($googleFeedAttributes, function ($item) {
                        return $item['name'] === 'material';
                    })['value'] ?? '';

                    // $product['template_id']
                    $campaign_name = Arr::get($product, 'campaign_name', $campaign ? data_get($campaign, 'name', '') : '');
                    $title = $campaign_name . ' ' . $suffix;
                    $description = isset($product['description']) ? strip_tags($product['description']) : '';
                    $campaignDescription = $campaign ? data_get($campaign, 'description') : '';
                    if ($isCombineTitle) {
                        $title = $this->combineValues([
                            $this->store->name,
                            $gender,
                            $color,
                            $size,
                            $material,
                            $campaign_name,
                            $suffix
                        ]);
                    }
                    if ($isCombineDescription) {
                        $description = $this->combineValues([
                            $campaign_name,
                            $campaignDescription,
                            $description,
                        ]);
                        $description = strip_tags($description);
                    }
                    $data = [
                        'id'                      => $id,
                        'title'                   => trim($title),
                        'description'             => trim($description),
                        'link'                    => $link,
                        'image_link'              => $imageLink,
                        'availability'            => $availability,
                        'price'                   => $priceValue,
                        'google_product_category' => $googleCategoryId,
                        'product_type'            => $product['name'],
                        'brand'                   => $this->store->name,
                        'shipping'                => ($marketLocation === '*' ? '' : 'US:::') . $shippingCost . ' USD',
                        'identifier_exists'       => $identifierExists,
                        'condition'               => 'New',
                        'age_group'               => 'adult',
                        'color'                   => $color,
                        'gender'                  => $gender,
                        'material'                => $material,
                        'size'                    => $size,
                        'item_group_id'           => $id,
                    ];

                    if (!empty($filters['custom_label_with_collection'])) {
                        $customLabels = [
                            'custom_label_0' => Arr::get($product['collection_slugs'], '0', ''),
                            'custom_label_1' => Arr::get($product['collection_slugs'], '1', ''),
                            'custom_label_2' => Arr::get($product['collection_slugs'], '2', ''),
                            'custom_label_3' => Arr::get($product['collection_slugs'], '3', ''),
                            'custom_label_4' => Arr::get($product['collection_slugs'], '4', ''),
                        ];
                    } else {
                        $customLabels = $this->feed->custom_labels ?? [
                            'custom_label_0' => '',
                            'custom_label_1' => '',
                            'custom_label_2' => '',
                            'custom_label_3' => '',
                            'custom_label_4' => '',
                        ];
                    }
                    $data['shipping_label'] = Arr::get($this->feed->custom_labels, 'shipping_label', '');

                    if (!empty($filters['include_mpn'])) {
                        $data['mpn'] = $lastProductId . '|' . $each['variant_key'];
                    }

                    // If Klaviyo feed, remove custom labels
                    if ($this->feed->platform === FeedPlatformEnum::KLAVIYO) {
                        $customLabels = [];
                        $data = array_diff($data, [
                            'google_product_category',
                            'identifier_exists',
                            'custom_label_0',
                            'custom_label_1',
                            'custom_label_2',
                            'custom_label_3',
                            'custom_label_4',
                        ]);
                        $data['categories'] = implode(',', Arr::get($product, 'category_slugs', []));
                        $data['inventory_quantity'] = $availability === 'in stock' ? random_int(10, 1000) : 0;
                        $data['inventory_policy'] = 1;
                    }
                    $arr[] = array_merge($data, $customLabels);
                    $numberRecords++;
                    unset($data);
                }
                $this->log('Loop: Variant Keys', [
                    'product_id' => $product['id'],
                    'time'       => microtime(true) - $time1,
                ], 'debug');
                unset($productOptions, $variantKeys);
            }
            $this->log('Loop: Products', [
                'index' => $index,
                'time'  => microtime(true) - $time,
                'total' => $countProducts,
            ]);

            if (!empty($arr)) {
                $this->log('Write to file', [
                    'index' => $index,
                ]);

                foreach ($arr as $key => $each) {
                    $each = array_filter($each, function ($key) use ($arrHeading) {
                        return in_array($key, $arrHeading);
                    }, ARRAY_FILTER_USE_KEY);

                    fputcsv($fileCSV, $each);
                    fwrite($fileXML, '<item>');
                    foreach ($each as $field => $item) {
                        if ($field === 'shipping') {
                            $itemCountry = str_starts_with($item, 'US') ? 'US' : '';
                            $itemPrice = str_starts_with($item, 'US') ? Str::after($item, 'US:::') : $item;
                            fwrite($fileXML, "<g:shipping>");
                            fwrite($fileXML, "<g:country>". $itemCountry . "</g:country>");
                            fwrite($fileXML, "<g:service>Standard</g:service>");
                            fwrite($fileXML, '<g:price>' . $itemPrice . '</g:price>');
                            fwrite($fileXML, "<g:min_handling_time>3</g:min_handling_time>");
                            fwrite($fileXML, "<g:max_handling_time>5</g:max_handling_time>");
                            fwrite($fileXML, "<g:min_transit_time>5</g:min_transit_time>");
                            fwrite($fileXML, "<g:max_transit_time>7</g:max_transit_time>");
                            fwrite($fileXML, "</g:shipping>");
                            continue;
                        }
                        if (!empty($item)) {
                            fwrite($fileXML, "<g:$field>");
                            fwrite($fileXML, xml_entities($item));
                            fwrite($fileXML, "</g:$field>");
                            continue;
                        }

                        fwrite($fileXML, "<g:$field/>");
                    }
                    fwrite($fileXML, '</item>');
                    unset($arr[$key]);
                }
            }

            $countProductsReal = count($products);
            unset($products);
            $this->feed->total_products -= $countProducts;
            $this->feed->total_products_real += $countProductsReal;
            $this->updateExportedColumn();
            // check if have any left
            if ($this->feed->total_products <= 0 || $this->isRecordLimitExceeded($numberRecords, $filters)) {
                $this->handleComplete();
                break;
            }
            $index++;
        } while (true);
    }

    private function isRecordLimitExceeded(int $numberRecords, array $filters): bool
    {
        $limitRecords = Arr::get($filters, 'limit_records');
        if ($limitRecords) {
            return $numberRecords >= $limitRecords;
        }

        return false;
    }

    private function getPath(): string
    {
        return 'u/' . $this->feed->seller_id . '/feed/' . $this->feedId . '/1';
    }

    private function handleUploadFile(): void
    {
        $time = microtime(true);
        $fileCSV = fopen($this->path['csv'], 'r');
        $fileXML = fopen($this->path['xml'], 'a+');
        // close tag
        $stringXML = '</channel>';
        $stringXML .= '</rss>';
        fwrite($fileXML, $stringXML);

        $filePath = $this->getPath();
        $nameFileCSV = $filePath . '.csv';
        $nameFileXML = $filePath . '.xml';
        $paths = [
            $nameFileCSV,
            $nameFileXML,
        ];
        // Delete old file if exists
        foreach (StorageDisksEnum::activeStorages() as $disk) {
            foreach ($paths as $path) {
                $fileExists = Storage::disk($disk)->exists($path);
                if ($fileExists) {
                    Storage::disk($disk)->delete($path);
                }
            }
        }
        $storage = Storage::disk(StorageDisksEnum::DEFAULT);
        $storage->put($nameFileCSV, $fileCSV, 'public');
        $storage->put($nameFileXML, $fileXML, 'public');

        $this->fileSize['csv_file_size'] = convertBytes(filesize($this->path['csv']), 'MB');
        $this->fileSize['xml_file_size'] = convertBytes(filesize($this->path['xml']), 'MB');

        unlink($this->path['csv']);
        unlink($this->path['xml']);
        $this->log('Upload file', [
            'time' => microtime(true) - $time,
        ]);
    }

    private function updateExportedColumn(): void
    {
        SocialFeed::query()
            ->where('id', $this->feedId)
            ->update(
                [
                    'total_exported' => $this->feed->total_products_real ?? 0,
                ]
            );
    }

    private function handleComplete(): void
    {
        $this->handleUploadFile();
        $data = [
            'path'           => $this->getPath(),
            'total_products' => $this->feed->total_products_real ?? 0,
            'updated_at'     => DB::raw('CURRENT_TIMESTAMP')
        ];
        $data['status'] = 1;
        if ($this->preDownloadImage && $this->totalNeedDownloadImage > 0) {
            $data['status'] = 0;
        }
        $data = array_merge($data, $this->fileSize);
        SocialFeed::query()
            ->where('id', $this->feedId)
            ->update($data);

        clearHtmlCacheByTag('feed', $this->feedId);
        $this->log('End Job');
        cache()->forget($this->jobId);
    }

    private function checkKilled(): void
    {
        if (!SocialFeed::query()->whereKey($this->feedId)->exists()) {
            throw new RuntimeException('Deleted');
        }

        $config = SystemConfig::getCustomConfig('kill_feed_ids');
        if (is_null($config)) {
            return;
        }

        $arr = explode(',', $config->value);
        if (empty($arr)) {
            return;
        }

        if (in_array($this->feedId, $arr)) {
            throw new RuntimeException('Killed');
        }
    }

    private function log(string $message, array $array_merge = [], $level = 'info'): void
    {
        logSocialFeed(
            $message,
            [
                'id'     => $this->feedId,
                'name'   => optional($this->feed)->name,
                'seller' => optional($this->feed)->seller_id,
            ] + $array_merge,
            $level
        );
    }

    public function failed($exception): void
    {
        $this->log(
            'Failed: ' . $exception->getMessage(),
            [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'code' => $exception->getCode(),
                'trace' => $exception->getTraceAsString(),
            ],
            'error'
        );
        cache()->forget($this->jobId);
    }

    /**
     * @return int
     */
    private function getDelay()
    {
        if ($this->queueName === QueueName::SOCIAL_FEED) {
            return 10;
        }
        return 30;
    }

    /**
     * @param $values
     * @return string
     */
    private function combineValues($values)
    {
        return collect($values)->filter()->map(fn($item) => Str::headline($item))->join(' | ');
    }
}

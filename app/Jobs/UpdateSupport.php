<?php

namespace App\Jobs;

use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderSupportStatusEnum;
use App\Http\Controllers\SystemConfigController;
use App\Models\Order;
use App\Models\OrderHistory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class UpdateSupport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $orderId;
    private $support_status;
    private $note;
    private $column;
    private $assignee;
    private $email;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->orderId = $request->get('order_id');
        $this->support_status = $request->get('support_status');
        $this->assignee = $request->get('assignee');
        $this->note = $request->get('note');
        $this->column = $request->get('column');
        $this->email = $request->get('email');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $order = Order::selectForHistory($this->column)
                ->find($this->orderId);
            // support status
            $order->support_status = $this->support_status;

            // assignee
            // check if don't have
            if (is_null($order->assignee) && empty($this->assignee)) {
                // get current user
                $this->assignee = currentUser()->getUserId();
            }
            $supporter = SystemConfigController::supporters()->firstWhere('id', $this->assignee);
            $order->assignee = optional($supporter)->id;

            // note
            if (!is_null($this->note)) {
                if ($this->column === 'admin_note') {
                    $action = OrderHistoryActionEnum::ADD_ADMIN_NOTE;
                    $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN;
                    $order->{$this->column} .= "\n" . $this->note;
                } else {
                    $order->{$this->column} = $this->note;
                }
            }

            // check if changed
            if ($order->isDirty($this->column)) {
                if ($this->column === 'order_note') {
                    $action = OrderHistoryActionEnum::EDIT_CUSTOMER_NOTE;
                    $displayLevel = OrderHistoryDisplayLevelEnum::CUSTOMER;
                }
                $detail = $this->note;
            } elseif ($order->isDirty('support_status')) {
                $action = OrderHistoryActionEnum::CHANGE_SUPPORT_CATEGORY;
                $detail = !is_null($order->support_status) ? OrderSupportStatusEnum::getKey($order->support_status) : null;
                $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN;
            } elseif ($order->isDirty('assignee')) {
                $action = OrderHistoryActionEnum::ASSIGN_TO_STAFF;
                $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN;
                if (is_null($supporter)) {
                    $detail = 'unassigned';
                } else {
                    $detail = $supporter->name . '<' . $supporter->email . '>';
                }
            }

            if (isset($action, $detail, $displayLevel) && $order->isDirty()) {
                OrderHistory::insertLog(
                    $order,
                    $action,
                    $detail,
                    $displayLevel,
                    $this->email
                );
            }

            $order->save();
        } catch (Throwable $e) {
            logToDiscord('[Add admin note] Message: ' . $e->getMessage());
        }
    }
}

<?php
namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Enums\UserRoleEnum;
use App\Models\Product;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\UserService;
use App\Traits\ElasticClient;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Campaign\Traits\SyncProductsToElasticSearch;
use Modules\ShardingTable\Traits\InitSellerConnection;

class SyncProductsShardingToElasticSearchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ElasticClient, InitSellerConnection, SyncProductsToElasticSearch;

    protected $queueName = 'sync-product-sharding';
    protected $id;
    protected $limit;
    protected $wait;
    protected $enabledDebug = false;
    protected $debugDiscord = false;
    protected $isJob = false;
    protected $sellerId = null;

    /**
     * @param null $id
     * @param int $limit
     * @param bool $wait
     * @param bool $isJob
     * @param bool $debug
     * @param null $sellerId
     */
    public function __construct($id = null, $limit = 1000, bool $wait = false, $isJob = false, $debug = false, $sellerId = null)
    {
        $this->id = $id;
        $this->limit = $limit;
        $this->wait = $wait;
        $this->isJob = $isJob;
        $this->enabledDebug = $debug;
        $this->sellerId = $sellerId;
        $this->onQueue($this->queueName);
    }

    /**
     * @throws \Throwable
     */
    public function handle()
    {
        try {
            if ($this->isJob) {
                SystemConfig::setConfig(CacheKeys::SYNC_PRODUCTS_SHARDING_TO_ELASTICSEARCH, array(
                    'value' => now()->clone()->addMinutes(10)->toDateTimeString(),
                ));
            }
            if($this->enabledDebug) {
                $this->log('Sync products START: ' . $this->limit, [
                    'category' => $this->queueName,
                    'user_type' => 'system',
                    'action'  => "sync",
                    'sync_id' => $this->id,
                    'type' => $this->isJob ? 'Cronjob' : 'Manual'
                ]);
            }
            $total = $this->syncProductsToElasticSearch($this->id, $this->limit, $this->wait, sellerId: $this->sellerId);
            if($total <= $this->limit) {
                if ($this->isJob && empty($this->id)) {
                    SystemConfig::setConfig(CacheKeys::SYNC_PRODUCTS_SHARDING_TO_ELASTICSEARCH, array(
                        'value' => null,
                    ));
                    if($this->enabledDebug) {
                        $this->log('Reset sync products sharding to elasticsearch', [
                            'category' => $this->queueName,
                            'user_type' => 'system',
                            'action'  => "sync",
                            'sync_id' => $this->id,
                            'type' => $this->isJob ? 'Cronjob' : 'Manual'
                        ]);
                    }
                }
                if($total > 0 && $this->enabledDebug) {
                    $this->log('Sync products STOP: ' . $total . ' / ' . $this->limit, [
                        'category' => $this->queueName,
                        'user_type' => 'system',
                        'action'  => "sync",
                        'sync_id' => $this->id,
                        'type' => $this->isJob ? 'Cronjob' : 'Manual'
                    ]);
                }
                return;
            }
            if ($this->isJob && empty($this->id)) {
                self::dispatch(null, $this->limit, $this->wait, $this->isJob, $this->enabledDebug, $this->sellerId);
            }
        } catch (\Throwable $exception) {
            logException($exception);
        }
    }

    /**
     * @param null $id
     * @param int $limit
     * @param bool $wait
     * @param null $sellerId
     * @return int
     * @throws \Throwable
     */
    public function syncProductsToElasticSearch($id = null, int $limit = 1000, bool $wait = false, $sellerId = null)
    {
        $startTime = round(microtime(true) * 1000);
        $currentUser = currentUser();
        $this->initConnection();
        if (empty($id)) {
            $totalPending = 0;
            User::query()
                ->select(['id', 'status', 'custom_payment', 'sharding_status', 'db_connection'])
                ->where('role', '!=', UserRoleEnum::CUSTOMER)
                ->where('db_connection', '!=', config('database.default'))
                ->get()
                ->groupBy('db_connection')
                ->each(function ($users, $connection) use ($limit, &$totalPending) {
                    $sellerId = (int)str_replace(['mysql_', 'mysql', '_'], ['', '', ''], $connection);
                    $sellerIds = $sellerId > 0 ? [$sellerId] : $users->pluck('id')->toArray();
                    $limitSeller = 100;
                    if (count($sellerIds) > $limitSeller) {
                        $sellerTotalPending = 0;
                        foreach (array_chunk($sellerIds, $limitSeller) as $chunkSellerIds) {
                            $sellerTotalPending += Product::query()
                                ->withTrashed()
                                ->on($connection)
                                ->whereIn('seller_id', $chunkSellerIds)
                                ->where('sync_status', Product::SYNC_DATA_STATS_ENABLED)
                                ->count();
                        }
                    } else {
                        $sellerTotalPending = Product::query()
                            ->withTrashed()
                            ->on($connection)
                            ->whereIn('seller_id', $sellerIds)
                            ->where('sync_status', Product::SYNC_DATA_STATS_ENABLED)
                            ->count();
                    }
                    if ($sellerTotalPending === 0) {
                        return;
                    }
                    $totalPending += $sellerTotalPending;
                    if (count($sellerIds) > $limitSeller) {
                        foreach (array_chunk($sellerIds, $limitSeller) as $chunkSellerIds) {
                            Product::query()
                                ->withTrashed()
                                ->on($connection)
                                ->select(['id', 'seller_id'])
                                ->where('sync_status', Product::SYNC_DATA_STATS_ENABLED)
                                ->whereIn('seller_id', $chunkSellerIds)
                                ->limit($limit * 5)
                                ->get()
                                ->groupBy('seller_id')
                                ->each(function ($products, $sellerId) use ($limit, &$totalPending) {
                                    $totalPending -= $products->count();
                                    $products->chunk($limit)->each(function ($chunkProducts) use ($limit, $sellerId) {
                                        $productIds = $chunkProducts->pluck('id')->toArray();
                                        foreach (array_chunk($productIds, round($limit / 2)) as $chunkProductIds) {
                                            SyncProductsShardingToElasticSearchJob::dispatch($chunkProductIds, $limit, false, true, $this->enabledDebug, sellerId: $sellerId);
                                        }
                                    });
                                });
                        }
                    } else {
                        Product::query()
                            ->withTrashed()
                            ->on($connection)
                            ->select(['id', 'seller_id'])
                            ->where('sync_status', Product::SYNC_DATA_STATS_ENABLED)
                            ->whereIn('seller_id', $sellerIds)
                            ->limit($limit * 10)
                            ->get()
                            ->groupBy('seller_id')
                            ->each(function ($products, $sellerId) use ($limit, &$totalPending) {
                                $totalPending -= $products->count();
                                $products->chunk($limit)->each(function ($chunkProducts) use ($limit, $sellerId) {
                                    $productIds = $chunkProducts->pluck('id')->toArray();
                                    foreach (array_chunk($productIds, round($limit / 2)) as $chunkProductIds) {
                                        SyncProductsShardingToElasticSearchJob::dispatch($chunkProductIds, $limit, false, true, $this->enabledDebug, sellerId: $sellerId);
                                    }
                                });
                            });
                    }
                });
            return max($totalPending, 0);
        }
        $sellerId ??= $currentUser->getUserId();
        $seller = UserService::getSellerSharding($sellerId);
        $query = Product::query()
            ->withTrashed()
            ->onSellerConnection($seller, shardingCompleted: false)
            ->select('*')
            ->with(['sellers:id,name,custom_payment,status,db_connection,sharding_status']);

        if (is_array($id)) {
            $ids = array_filter_empty($id);
            if (count($ids) === 0) {
                return 0;
            }
            $ids = array_unique($ids);
            $total_ids = count($ids);
            if ($total_ids === 1) {
                $wait = true;
            }
            if ($total_ids > $limit) {
                foreach (array_chunk($ids, round($limit / 2)) as $chunk_ids) {
                    $this->syncProductsToElasticSearch($chunk_ids, $limit, $wait, sellerId: $sellerId);
                    $total_ids -= $limit;
                }
                return $total_ids;
            }
            $query->where(function ($q) use ($ids) {
                $q->orWhereIn('id', $ids);
                $q->orWhereIn('campaign_id', $ids);
            });
        } else {
            $wait = true;
            $query->where(function ($q) use ($id) {
                $q->orWhere('id', $id);
                $q->orWhere('campaign_id', $id);
            });
        }
        $pending_products = $query->get();
        $total = $pending_products->count();
        $endTime = round(microtime(true) * 1000);
        $esTime = $endTime - $startTime;
        if ($this->enabledDebug) {
            $full_sql = $query->toRawSql();
            $this->log("Start sync products to elasticsearch, total time get products to sync in $esTime ms", [
                'category' => $this->queueName,
                'user_type' => 'system',
                'action'  => "sync",
                'full_sql' => $full_sql,
                'total_sync' => $total,
                'run_time' => now()->toDateTimeString(),
                'type' => $this->isJob ? 'Cronjob' : 'Manual'
            ]);
        }
        // Check product is empty
        if ($total === 0) {
            return 0;
        }

        if ($total > $limit) {
            foreach ($pending_products->chunk($limit) as $chunk_products) {
                $this->handle_sync_to_elasticsearch($chunk_products, $wait, seller: $seller);
                $total -= $limit;
            }
            return max($total, 0);
        }
        return $this->handle_sync_to_elasticsearch($pending_products, $wait, seller: $seller);
    }
}

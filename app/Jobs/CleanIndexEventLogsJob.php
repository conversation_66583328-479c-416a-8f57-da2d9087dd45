<?php

namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Enums\SystemConfigTypeEnum;
use App\Enums\TempTypeEnum;
use App\Models\IndexEventLogs;
use App\Models\SystemConfig;
use App\Models\Temp;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Throwable;

class CleanIndexEventLogsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct()
    {
        $this->onQueue('default');
    }

    /**
     * @throws Throwable
     */
    public function handle()
    {
        try {
            $totalDelete = 0;
            $configDatestamp = SystemConfig::getCustomConfig(CacheKeys::DATESTAMP_FOR_CLEAN_INDEX_EVENT_LOGS, cacheResult: false);
            $configJob = json_decode($configDatestamp?->json_data, true) ?? [];
            $maxDate = data_get($configJob, 'maxDate', now()->subMonths(6));
            $delayJobInsert = data_get($configJob, 'delayJobInsert', 5);
            $debug = data_get($configJob, 'debug', false);
            $debug && $startTime = round(microtime(true) * 1000);
            $datestamp = $configDatestamp?->value ?? IndexEventLogs::query()->whereDate('datestamp', '<', $maxDate)->whereNull('total')->min('datestamp');
            if ($datestamp) {
                $datestampCarbon = Carbon::create($datestamp);
                $datestamp = $datestampCarbon->toDateString();

                $debug && $startTimeQuery = round(microtime(true) * 1000);
                $eventLogs = IndexEventLogs::query()
                    ->select([
                        DB::raw('MAX(ID) as id'),
                        DB::raw('APPROX_COUNT_DISTINCT(session_id) AS total'),
                    ])
                    ->whereDate('datestamp', $datestamp)
                    ->groupBy('seller_id', 'store_id', 'country', 'datestamp', 'type', 'device')
                    ->get();
                $debug && $this->logTimeQuery('select', $startTimeQuery, $datestamp, $eventLogs->count());

                if (data_get($configJob, 'runInsert', 1)) {
                    foreach(array_chunk($eventLogs->toArray(), 1000) as $chunk) {
                        $debug && $startTimeQuery = round(microtime(true) * 1000);
                        $dataInsert = array_map(fn ($item) => [
                            'id1' => $item['id'],
                            'id2' => $item['total'],
                            'status' => 1,
                            'timestamp' => $datestamp,
                            'type' => TempTypeEnum::CLEAN_INDEX_EVENT_LOGS,
                        ], $chunk);
                        Temp::query()->insert($dataInsert);
                        $debug && $this->logTimeQuery('insert', $startTimeQuery, $datestamp, array_column($chunk, 'id'));
                    }
                }

                if (data_get($configJob, 'runDelete', 1)) {
                    $query = IndexEventLogs::query()
                        ->whereDate('datestamp', $datestamp)
                        ->whereNotIn('id', $eventLogs->pluck('id'));
                    $debug && $startTimeQuery = round(microtime(true) * 1000);
                    $totalDelete = $query->delete();
                    $debug && $this->logTimeQuery('delete', $startTimeQuery, $datestamp, $totalDelete);
                }
                SystemConfig::setConfig(CacheKeys::DATESTAMP_FOR_CLEAN_INDEX_EVENT_LOGS, [
                    'value' => $datestampCarbon->addDay()->toDateString(),
                    'status' => (int)($datestampCarbon < $maxDate),
                    'json_data' => json_encode($configJob),
                    'type' => SystemConfigTypeEnum::BACKEND
                ]);
                if ($debug) {
                    $endTime = round(microtime(true) * 1000);
                    $time = $endTime - $startTime;
                    graylogInfo("CleanIndexEventLogsJob: Clean index_event_logs date ({$datestamp}) completed in $time ms", [
                        'category' => 'clean_index_event_logs',
                        'datestamp' => $datestamp,
                        'total_delete' => $totalDelete,
                    ]);
                }
                if ($datestampCarbon < $maxDate) {
                    self::dispatch()->delay(now()->addMinutes($delayJobInsert));
                }
            }
        } catch (Throwable $th) {
            logException($th, 'CleanIndexEventLogs@handle');
        }
    }

    private function logTimeQuery($typeQuery, $startTimeQuery, $datestamp, $data){
        $endTimeQuery = round(microtime(true) * 1000);
        $timeQuery = $endTimeQuery - $startTimeQuery;
        graylogInfo("CleanIndexEventLogsJob: Run query {$typeQuery} date ({$datestamp}) completed in $timeQuery ms", [
            'category' => 'clean_index_event_logs',
            'sub_category' => $typeQuery,
            'datestamp' => $datestamp,
            'data' => $data,
        ]);
    }

}

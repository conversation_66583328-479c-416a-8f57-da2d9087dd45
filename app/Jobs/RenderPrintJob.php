<?php

namespace App\Jobs;

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Throwable;

class RenderPrintJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 3;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public bool $deleteWhenMissingModels = true;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public int $backoff = 5;
    public $file;
    public string $type;

    public function __construct($file, $type = 'file')
    {
        $this->file = $file;
        $this->type = $type;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            if (!empty($this->file) && !empty($this->file->design_json)) {
                $this->log('Render print start: ' . $this->file->id);
                $url = config('senprints.render_canvas_2d_url');
                $renderParams = [
                    'productId' => $this->file->product_id,
                    'orderId' => $this->file->order_id,
                    'fileId' => $this->file->id,
                    'designJson' => $this->file->design_json
                ];
                $res = Http::withoutVerifying()->withOptions([
                    'proxy' => ''
                ])->timeout(60)->post($url, $renderParams);

                if ($res->failed()) {
                    $this->log('Render failed: ' . $this->file->id . ', body: ' . $res->body(), 'error');
                    $this->fail();
                    return;
                }

                $json = $res->json();
                if (empty($json['Location']) || empty($json)) {
                    $this->log('Render response empty: ' . $this->file->id, 'error');
                    $this->fail();
                    return;
                }

                $fileUrl = $json['Location'];
                $filePath = parse_url($fileUrl, PHP_URL_PATH);
                $filePath = ltrim($filePath, '/');

                // save print file path to file_url
                if ($this->type === 'file') {
                    $this->file->file_url_2 = $filePath;
                } else {
                    $this->file->file_url = $filePath;
                }
                $this->file->save();
                $this->log('Render success: ' . $this->file->id, 'success', imgUrl($filePath, 'preview_design'));
            } else {
                $this->log('Render print failed: ' . $this->type . ' #' . $this->file->id . ', No design json.', 'error');
            }
        } catch (Throwable $e) {
            $this->handleException($e);
            $this->fail();
        }
    }

    public function handleException($exception): void
    {
        $log = 'Render print failed: ' . $this->type . ' #' . $this->file->id;

        if ($this->file->order_id) {
            if ($this->file->order_product_id) {
                OrderProduct::query()->where([
                    'id' => $this->file->order_product_id,
                    'order_id' => $this->file->order_id,
                    'fulfill_status' => OrderProductFulfillStatus::UNFULFILLED
                ])->update([
                    'fulfill_status' => OrderProductFulfillStatus::INVALID,
                    'fulfill_exception_log' => $log
                ]);

                Order::query()->where([
                    'id' => $this->file->order_id,
                    'status' => OrderStatus::PROCESSING,
                    'fulfill_status' => OrderFulfillStatus::UNFULFILLED
                ])->update([
                    'fulfill_status' => OrderFulfillStatus::INVALID
                ]);
            }

            $log .= ' / OID #: ' . $this->file->order_id;
        }

        if ($this->file->product_id) {
            $log .= ' / PID #: ' . $this->file->product_id;
        }

        if ($exception) {
            $log .= ' / ' . $exception->getMessage();
        }
        $this->log($log, 'error');
    }

    private function log($message, $type = 'info', $fileUrl = null): void
    {
        graylogError($message, [
            'category' => 'render_design',
            'action' => "render",
            'type' => $type,
            'file_type' => $this->type,
            'file_id' => is_numeric($this->file->id) ? $this->file->id : 0,
            'actual_file_id' => $this->file->id,
            'file_url' => $fileUrl
        ]);
    }
}

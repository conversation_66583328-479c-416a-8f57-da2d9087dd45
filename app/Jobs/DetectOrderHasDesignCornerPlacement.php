<?php

namespace App\Jobs;

use App\Enums\DiscordChannel;
use App\Enums\DiscordUserIdEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\ProductCategoryEnum;
use App\Models\File;
use App\Models\Order;
use App\Models\ProductCategory;
use App\Models\User;
use App\Services\CampaignService;
use App\Services\FulfillmentService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class DetectOrderHasDesignCornerPlacement implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?Order $order;

    public function __construct(int $orderId)
    {
        $this->order = Order::query()->with(['products'])->whereKey($orderId)->first();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!$this->order || $this->order->isFulfillmentOrder() || $this->order->isCustomServiceOrder() || $this->order->isServiceOrder() || $this->order->products->isEmpty()) {
            return;
        }
        try {
            $files = [];
            $differentMockups = [];
            foreach ($this->order->products as $orderProduct) {
                $categoryIds = ProductCategory::query()->where('product_id', $orderProduct->template_id)->get()->pluck('category_id')->map(fn($item) => (int)$item)->toArray();
                if (!in_array(ProductCategoryEnum::APPAREL, $categoryIds, true)) {
                    continue;
                }
                $options = [];
                if (Str::isJson($orderProduct->options)) {
                    $options = json_decode($orderProduct->options, true, 512, JSON_THROW_ON_ERROR);
                }
                [$designs,] = FulfillmentService::getOrderProductFiles($orderProduct->id, $orderProduct->order_id, $orderProduct->product_id, null, Arr::get($options, 'color'));
                $seller = User::query()->whereKey($orderProduct->seller_id)->first();
                if (!$seller) {
                    continue;
                }
                if ($designs->isNotEmpty()) {
                    $productImages = File::query()
                        ->onSellerConnection($seller)
                        ->select('id')
                        ->where([
                            'campaign_id' => $orderProduct->campaign_id,
                            'product_id' => $orderProduct->product_id,
                            'type' => FileTypeEnum::IMAGE
                        ])
                        ->whereNotNull('design_id')
                        ->groupBy('print_space')
                        ->get();
                    if ($productImages->count() > $designs->count()) {
                        $differentMockups[$orderProduct->id] = [$productImages->count(), $designs->count(), 'https://' . $this->order->store_domain . $orderProduct->product_url];
                    }
                    foreach ($designs as $design) {
                        $design3d = File::query()
                            ->onSellerConnection($seller)
                            ->select('file_url')
                            ->where([
                                'product_id' => $orderProduct->product_id,
                                'campaign_id' => $orderProduct->campaign_id,
                                'type' => FileTypeEnum::DESIGN,
                                'option' => FileRenderType::RENDER_3D,
                                'print_space' => $design->print_space,
                            ])->orderByDesc('created_at')->first();
                        $fileDesign3dPath = $design3d?->file_url;
                        $fileDesignPath = $design->design_url ?? $design->file_url_2 ?? $design->file_url;
                        if ($fileDesignPath && $fileDesign3dPath && CampaignService::isDesignPrintFull($fileDesignPath) && !CampaignService::isDesignPrintFull($fileDesign3dPath)) {
                            $files[$orderProduct->id] = [s3Url($fileDesignPath), s3Url($fileDesign3dPath)];
                        }
                    }
                }
                if (isset($files[$orderProduct->id])) {
                    $orderProduct->is_corner_placement = 1;
                }
            }
            if (!empty($files)) {
                $this->order->is_corner_placement = 1;
                $this->order->push();
                $message = 'Order ID: #' . $this->order->id . "\r\n";
                $message .= 'Order Number: ' . $this->order->order_number . "\r\n";
                foreach ($files as $oid => $urls) {
                    $message .= '---------------------------' . "\r\n";
                    $message .= 'Order Product ID: #' . $oid . "\r\n";
                    $message .= 'Design URL: ' . data_get($urls, 0) . "\r\n";
                    $message .= 'Design 3D URL: ' . data_get($urls, 1) . "\r\n";
                }
                $embedDesc = [
                    [
                        'description' => $message,
                        'color' => 15105570,
                    ]
                ];
                logToDiscord((app()->isProduction() ? mentionDiscord(DiscordUserIdEnum::THANGNM) . " " . mentionDiscord(DiscordUserIdEnum::TUNGNT) : '') . " Đơn này thiết kế in nhỏ trên 1 góc của thiết kế, mọi người vào cập nhật lại thiết kế cho đúng với mockup nhé!\r\nhttps://admin.senprints.com/order/detail/" . $this->order->id, channel: DiscordChannel::OPERATION_TEAM_CHANNEL, embeds: $embedDesc, threadId: 1220272806034538496);
            }
            if (!empty($differentMockups)) {
                $message = 'Order ID: #' . $this->order->id . "\r\n";
                $message .= 'Order Number: ' . $this->order->order_number . "\r\n";
                foreach ($differentMockups as $oid => $differentMockup) {
                    $message .= '---------------------------' . "\r\n";
                    $message .= 'Order Product ID: #' . $oid . "\r\n";
                    $message .= 'Total mockup(s): ' . data_get($differentMockup, 0) . "\r\n";
                    $message .= 'Total design(s): ' . data_get($differentMockup, 1) . "\r\n";
                    $message .= 'Campaign URL: ' . data_get($differentMockup, 2) . "\r\n";
                }
                $embedDesc = [
                    [
                        'description' => $message,
                        'color' => 10181046,
                    ]
                ];
                logToDiscord((app()->isProduction() ? mentionDiscord(DiscordUserIdEnum::THANGNM) . " " . mentionDiscord(DiscordUserIdEnum::TUNGNT) : '') . " Đơn này số lượng mockup không khớp với số lượng thiết kế, mọi người vào kiểm tra và cập nhật lại mockup cho đúng với thiết kế nhé!\r\nhttps://admin.senprints.com/order/detail/" . $this->order->id, channel: DiscordChannel::OPERATION_TEAM_CHANNEL, embeds: $embedDesc, threadId: 1220272806034538496);
            }
        } catch (\Throwable $exception) {
            logException($exception);
        }
    }
}

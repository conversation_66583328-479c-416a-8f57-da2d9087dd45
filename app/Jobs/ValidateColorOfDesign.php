<?php

namespace App\Jobs;

use App\Enums\DiscordChannel;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductCategoryEnum;
use App\Models\Order;
use App\Models\ProductCategory;
use App\Models\SystemColor;
use App\Services\FulfillmentService;
use App\Services\ImageService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class ValidateColorOfDesign implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public $orderId) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $order = Order::query()->with([
                'products',
            ])->firstWhere('id', $this->orderId);
            if (!$order) {
                return;
            }
            $errors = [];
            /** @var ImageService $imageService */
            $imageService = app(ImageService::class);
            $templateIds = $order->products->pluck('template_id')->unique()->toArray();
            $productCategories = ProductCategory::query()->whereIn('product_id', $templateIds)->get();
            $processedItems = [];
            foreach ($order->products as $orderProduct) {
                $categoryIds = $productCategories->where('product_id', $orderProduct->template_id)->pluck('category_id')->map(fn($item) => (int)$item)->toArray();
                if (!in_array(ProductCategoryEnum::APPAREL, $categoryIds, true)) {
                    continue;
                }
                $productColor = $orderProduct->color;
                if (!$productColor) {
                    $productColor = Str::isJson($orderProduct->options) ? data_get(json_decode($orderProduct->options, false, 512, JSON_THROW_ON_ERROR), 'color') : null;
                }
                $colorData = SystemColor::query()->firstWhere('name', $productColor);
                if (!$colorData) {
                    continue;
                }
                $color = $colorData->hex_code ?? '';
                $_color = str_replace('#', '', $color);
                $processedItem = ($orderProduct->campaign_id ?? 0) . '_' . $_color;
                if (in_array($processedItem, $processedItems, true)) {
                    continue;
                }
                $processedItems[] = $processedItem;
                $orderProductId = $orderProduct->id;
                $productId = $orderProduct->product_id;
                $productFiles = FulfillmentService::getOrderProductFiles($orderProductId, $this->orderId, $productId, null, null, true);
                if (empty($productFiles[0])) {
                    continue;
                }
                $designs = reset($productFiles);
                foreach ($designs as $design) {
                    $designUrl = imgUrl($design['file_url'], 'download_design');
                    $printSpace = data_get($design, 'print_space', '');
                    $result = $imageService->validateColorSameWithDesign($designUrl, $color, $order->id);
                    if (!$result) {
                        $errors[] = [
                            'order_product_id' => $orderProductId,
                            'print_space' => ucfirst($printSpace),
                            'design_url' => $designUrl,
                            'color_url' => "https://dummyimage.com/800x800/$_color/$_color.png",
                        ];
                    }
                }
            }

            if (!empty($errors)) {
                $color = match ($order->type) {
                    OrderTypeEnum::CUSTOM => 16759808,
                    OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA => 708503,
                    default => 7503093,
                };
                $message = 'Order ID: #' . $order->id . "\r\n";
                $message .= 'Order Number: ' . $order->order_number . "\r\n";
                $message .= 'Order Type: ' . Str::headline($order->type) . "\r\n";
                $message .= str_repeat('-', 40) . "\r\n";
                foreach ($errors as $error) {
                    $message .= "- Order Product ID: {$error['order_product_id']}" . "\r\n";
                    $message .= "- Print Space: {$error['print_space']}" . "\r\n";
                    $message .= "- Design URL: {$error['design_url']}" . "\r\n";
                    $message .= "- Color Url: {$error['color_url']}" . "\r\n";
                }
                $embedDesc = [
                    [
                        'description' => $message,
                        'color' => $color
                    ]
                ];
                logToDiscord("https://admin.senprints.com/order/detail/{$order->id}\r\nĐơn này có màu design gần tương đồng với màu sản phẩm.", DiscordChannel::TECH_SUPPORT, embeds: $embedDesc, threadId: 1394518574130790600);
            }
        } catch (\Throwable $e) {
            graylogError('validate color of design error', [
                'order_id' => $this->orderId,
                'category' => 'validate_color_of_design_error',
                'error' => $e->getMessage(),
            ]);
        }
    }
}

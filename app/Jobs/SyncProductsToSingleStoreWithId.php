<?php

namespace App\Jobs;

use App\Models\IndexProduct;
use App\Models\Product;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class SyncProductsToSingleStoreWithId implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public array $syncIds, public string $to_connection = 'singlestore', public int $limit = 1000) {}

    /**
     * Execute the job.
     */
    public function handle(): int
    {
        if (count($this->syncIds) === 0) {
            return 0;
        }
        $startTime = round(microtime(true) * 1000);
        $products = Product::query()
            ->where('sync_status', '=', Product::SYNC_DATA_TO_SINGLE_STORE)
            ->withTrashed()
            ->whereIn('id', $this->syncIds)
            ->get();
        try {
            $total = $products->count();
            graylogInfo('Start sync products to ' . Str::upper($this->to_connection) . ': ' . $total . ' / ' . $this->limit, [
                'category' => 'products_sync_log',
                'user_type' => 'system',
                'action' => "sync"
            ]);
            $products = $products->map(function (Product $product) {
                $product->sync_status = Product::SYNC_DATA_STATS_DISABLED;
                $product->base_costs = !empty($product->base_costs) ? (string)json_encode($product->base_costs) : null;
                return $product;
            });
            IndexProduct::query()->whereIn('id', $this->syncIds)->withTrashed()->forceDelete();
            IndexProduct::query()->insert($products->toArray());
            Product::query()->whereIn('id', $this->syncIds)->withTrashed()->update(['sync_status' => Product::SYNC_DATA_STATS_DISABLED]);
            $endTime = round(microtime(true) * 1000);
            $esTime = $endTime - $startTime;
            graylogInfo('Sync-ed product to ' . Str::upper($this->to_connection) . ': ' . $total . ' / ' . $this->limit . ' in total ' . $esTime . ' ms', [
                'category' => 'products_sync_log',
                'user_type' => 'system',
                'result' => array(
                    'processed_ids' => !empty($this->syncIds) ? implode(', ', $this->syncIds) : '',
                ),
                'action' => "sync"
            ]);
            return $total;
        } catch (\Throwable $e) {
            if (!str_contains($e->getMessage(), 'SQLSTATE')) {
                logException($e, 'syncProducts', 'error', true);
            }
            graylogInfo('Sync failed', [
                'category' => 'products_sync_log',
                'user_type' => 'system',
                'error' => $e->getMessage(),
                'result' => array(
                    'processed_ids' => !empty($this->syncIds) ? implode(', ', $this->syncIds) : '',
                ),
                'action' => "sync"
            ]);
            return 0;
        }
    }
}

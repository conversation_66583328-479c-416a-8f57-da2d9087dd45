<?php

namespace App\Data;

class TrackingItemData extends DataObject
{
    public function __construct(
        public string $number,
        public ?string $carrier,
        public ?string $trackingStatus,
        public ?string $trackingSubStatus,
        public ?string $deliveredTime,
        public ?string $lastTrackingTime
    ) {}

    /**
     * @return string[]
     */
    public static function getFillableAttributes(): array
    {
        return [
            'number',
            'carrier',
            'trackingStatus',
            'trackingSubStatus',
            'deliveredTime',
            'lastTrackingTime',
        ];
    }

    /**
     * @return string[]
     */
    public static function mappingFields(): array
    {
        return [
            'number' => [
                'trackNo', // track123
                'number', // 17track,
                'trackingId', // trakow,
            ],
            'carrier' => [
                'localLogisticsInfo.courierCode', // track123
                'carrier', // 17track
                'courierName', // trakow
            ],
            'trackingStatus' => [
                'transitStatus', // track123
                'track_info.latest_status.status', // 17track
                'trackingSummary', // trakow
            ],
            'trackingSubStatus' => [
                'transitSubStatus', // track123
                'track_info.latest_status.sub_status', // 17track
            ],
            'deliveredTime' => [
                'deliveredTime', // track123
                'track_info.latest_event.time_utc', // 17track
                'lastTime', // trakow
            ],
            'lastTrackingTime' => [
                'lastTrackingTime', // track123
                'track_info.latest_event.time_utc', // 17track
                'lastTime', // trakow
            ],
        ];
    }
}

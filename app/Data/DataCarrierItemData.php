<?php

namespace App\Data;

class DataCarrierItemData extends DataObject
{
    public function __construct(
        public string $courierCode,
        public ?string $courierName,
        public ?string $courierUrl
    ) {}


    /**
     * @return string[]
     */
    public static function getFillableAttributes(): array
    {
        return [
            'courierCode',
            'courierName',
            'courierUrl',
        ];
    }

    /**
     * @return string[]
     */
    public static function mappingFields(): array
    {
        return [
            'courierCode' => [
                'courierCode', // track123
                'key' // 17track
            ],
            'courierName' => [
                'courierNameEN', // track123
                '_name' // 17track
            ],
            'courierUrl' => [
                'courierHomePage', // track123
                '_url' // 17track
            ],
        ];
    }
}

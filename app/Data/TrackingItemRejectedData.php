<?php

namespace App\Data;

class TrackingItemRejectedData extends DataObject
{
    public function __construct(
        public string $number,
        public ?string $errorCode,
        public ?string $errorMessage,
    ) {}

    /**
     * @return string[]
     */
    public static function mappingFields(): array
    {
        return [
            'number' => [
                'trackNo', // track123
                'number', // 17track,
                'trackingId', // trakow,
            ],
            'errorCode' => 'error.code', // track123, 17track, trakow
            'errorMessage' => [
                'error.msg', // track123
                'error.message', // 17track, trakow
            ],
        ];
    }

    /**
     * @return string[]
     */
    public static function getFillableAttributes(): array
    {
        return [
            'number',
            'errorCode',
            'errorMessage',
        ];
    }
}

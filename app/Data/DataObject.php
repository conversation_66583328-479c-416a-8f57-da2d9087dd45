<?php

namespace App\Data;

use ReflectionClass;
use <PERSON><PERSON>\LaravelData\Data;
use Illuminate\Support\Collection;

class DataObject extends Data
{
    /**
     * @return array
     */
    public static function getFillableAttributes(): array
    {
        $reflection = new ReflectionClass(static::class);
        $properties = $reflection->getProperties(\ReflectionProperty::IS_PUBLIC);
        return array_map(static fn($prop) => $prop->getName(), $properties);
    }

    /**
     * @param array $data
     * @return array
     */
    public static function removeNullValues(array $data): array {
        return array_filter($data, static function ($value) {
            return $value !== null && $value !== '';
        });
    }

    /**
     * @param array|Collection $items
     * @return Collection
     */
    public static function collection(array|Collection $items): Collection
    {
        return collect($items)->map(fn ($item) => self::from($item));
    }

    /**
     * @param array|Collection $items
     * @return Collection
     */
    public static function withMapping(array|Collection $items): Collection
    {
        $mappingFields = static::mappingFields();
        return collect($items)->map(function ($item) use ($mappingFields) {
            $data = [];
            foreach ($mappingFields as $key => $mapValues) {
                if (empty($mapValues)) {
                    $data[$key] = '';
                    continue;
                }
                if (is_string($mapValues)) {
                    $data[$key] = data_get($item, $mapValues, '');
                    continue;
                }
                if (is_array($mapValues)) {
                    foreach ($mapValues as $mapValue) {
                        if (data_get($item, $mapValue)) {
                            $data[$key] = data_get($item, $mapValue, '');
                            break;
                        }
                    }
                    continue;
                }
                $data[$key] = '';
            }
            return parent::from($data);
        });
    }
}

<?php
namespace App\Imports;

use App\Enums\DesignTypeEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\ProductPrintType;
use App\Enums\StorageDisksEnum;
use App\Library\UrlUploadedFile;
use App\Models\Design;
use App\Models\OrderProduct;
use App\Services\Link;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

HeadingRowFormatter::default('none');
class OrderDesignImport implements ToCollection, WithHeadingRow
{
    private function parseBeforeMapping(&$row): void
    {
        $row = $row->toArray();
        $row = array_map('trim', $row);
        $row = array_change_key_case($row);
    }

    /**
     * @param Collection $rows
     * @throws \Exception
     */
    public function collection(Collection $rows)
    {
        $userId = currentUser()->getUserId();
        $messages = [];
        try {
            foreach ($rows as $rowIndex => $row) {
                if (!$row->filter()->isNotEmpty()) {
                    continue;
                }
                $rowCount = ((int) $rowIndex + 2);
                $this->parseBeforeMapping($row);
                if (empty($row['order_product_id'])) {
                    $messages[] = 'Row #' . $rowCount . ': Order product id is invalid';
                    continue;
                }

                $order_product_id = $row['order_product_id'];
                $orderProduct = OrderProduct::query()->with(['order', 'template'])->where([
                    'id' => $order_product_id,
                    'seller_id' => $userId,
                    'fulfill_status' => OrderProductFulfillStatus::DESIGNING,
                ])->first();
                if(empty($orderProduct)) {
                    $messages[] = 'Row #' . $rowCount . ': Order product id is invalid';
                    continue;
                }
                if($orderProduct->order->fulfill_status === OrderFulfillStatus::FULFILLED || $orderProduct->order->fulfill_status === OrderFulfillStatus::CANCELLED) {
                    $messages[] = 'Row #' . $rowCount . ': Order do not need to upload the design';
                    continue;
                }
                $full_printed = $orderProduct->full_printed;
                $product_print_spaces = !empty($orderProduct->template->print_spaces) ? json_decode($orderProduct->template->print_spaces, true) : [];
                $print_spaces = [];
                array_map(static function ($item) use (&$print_spaces) {
                    if(!empty($item['name'])) {
                        $print_spaces[$item['name']] = Str::lower($item['name']);
                    }
                    return $item;
                }, $product_print_spaces);
                $print_spaces = array_values($print_spaces);
                $design_urls = array_filter($row, function ($value, $key) {
                    return Str::startsWith($key, 'design_') && !empty($value);
                }, ARRAY_FILTER_USE_BOTH);
                $valid_designs = [];
                foreach ($design_urls as $key => $design_url) {
                    if (empty($design_url)) {
                        $messages[] = 'Row #' . $rowCount . ': Design url is invalid';
                        continue 2;
                    }
                    $exp_key = explode('_', $key);
                    if(count($exp_key) !== 2) {
                        $messages[] = 'Row #' . $rowCount . ': Your design url invalid.';
                        continue 2;
                    }
                    $print_name = Str::lower(trim($exp_key[1]));
                    if($print_name !== 'default' && !Str::contains($print_name, $print_spaces)) {
                        continue 2;
                    }
                    if (str_contains($design_url, 'drive.google.com') || str_contains($design_url, 'dropbox.com')) {
                        $directLink = new Link($design_url);
                        $directLink = $directLink->toDirectLink();
                    } else {
                        $directLink = $design_url;
                    }
                    $valid_designs[$print_name]['name'] = $print_name;
                    $valid_designs[$print_name]['url'] = $directLink;
                }
                $product_id = $orderProduct->product_id;
                $campaign_id = $orderProduct->campaign_id;
                $order_id = $orderProduct->order_id;
                $size = $orderProduct->size ?? null;
                $design_data = [];
                $valid_designs = array_values($valid_designs);
                foreach ($valid_designs as $design) {
                    $actual_print_space = null;
                    if (count($print_spaces) >= 1) {
                        if ($design['name'] === 'default') {
                            if ($full_printed === ProductPrintType::PRINT_2D) {
                                $actual_print_space = $print_spaces[0];
                            } else {
                                foreach ($print_spaces as $print_space) {
                                    if (Str::contains($size, $print_space)) {
                                        $actual_print_space = $print_space;
                                        break;
                                    }
                                }
                            }
                        } else {
                            foreach ($print_spaces as $print_space) {
                                if (Str::contains($design['name'], $print_space)) {
                                    if ($full_printed === ProductPrintType::PRINT_2D || Str::contains($size, $print_space)) {
                                        $actual_print_space = $print_space;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    if ($actual_print_space === null) {
                        continue;
                    }
                    $tmpFile = UrlUploadedFile::createFileFromUrl($design['url'], 'design');
                    if ($tmpFile === null) {
                        //Try to create file from url one more time
                        $tmpFile = UrlUploadedFile::createFileFromUrl($design['url'], 'design');
                        if($tmpFile === null) {
                            $messages[] = 'Row #' . $rowCount . ': Can not download your design url.';
                            continue 2;
                        }
                    }
                    $name = $tmpFile->getClientOriginalName();
                    $file_url = $tmpFile->storePubliclyAs('o/' . $order_id . '/designs', $name, StorageDisksEnum::DEFAULT);
                    Design::query()->where(array(
                        'product_id' => $product_id,
                        'campaign_id' => $campaign_id,
                        'order_id' => $order_id,
                        'order_product_id' => $order_product_id,
                        'seller_id' => $userId,
                        'print_space' => $actual_print_space,
                    ))->delete();
                    $design_data[$actual_print_space] = [
                        'id' => generateUUID(),
                        'file_url' => $file_url,
                        'product_id' => $product_id,
                        'campaign_id' => $campaign_id,
                        'order_id' => $order_id,
                        'order_product_id' => $order_product_id,
                        'seller_id' => $userId,
                        'print_space' => $actual_print_space,
                        'type' => DesignTypeEnum::PRINT,
                    ];
                }
                Design::query()->insert(array_values($design_data));
            }
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage(), 403);
        }
        if (count($messages) > 0) {
            throw new \Exception(implode(';', $messages), 403);
        }
    }
}

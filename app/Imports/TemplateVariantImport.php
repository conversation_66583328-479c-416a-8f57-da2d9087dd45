<?php

namespace App\Imports;

use App\Enums\ProductPrintType;
use App\Enums\ProductType;
use App\Jobs\SyncStockStatusJob;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\SystemColor;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class TemplateVariantImport implements ToCollection, WithHeadingRow
{
    // fields: Product SKU,Options:Color,Options:Size,Out Of Stock,Out Of Stock format,Price,Base Cost,Base Cost:EU,Base Cost:US...
    public function collection(Collection $rows): void
    {
        $message = $this->validateRows($rows);
        if (!empty($message)) {
            throw new \LogicException($message);
        }
        $arr = [];
        $productsAvailable = [];
        foreach ($rows as $row) {
            $productSku = $row['product_sku'];
            $productTemplate = $productsAvailable[$productSku] ?? null;
            if (!$productTemplate) {
                $productTemplate = Product::query()
                    ->select('sku', 'id', 'full_printed', 'options')
                    ->where('sku', $productSku)
                    ->where('product_type', ProductType::TEMPLATE)
                    ->first();
            }
            if (!$productTemplate || empty($row['price'])) {
                continue;
            }
            $productsAvailable[$productSku] = $productTemplate;
            $parseOption = false;
            if ((int) $productTemplate->full_printed !== ProductPrintType::HANDMADE) {
                $parseOption = true;
            }
            $options = $this->getAndSetOptions($row, $arr[$productSku]['options'], $parseOption);
            $outOfStock = $this->getOutOfStock($row);
            $arr[$productSku]['price'] ??= $row['price']; //get min

            if ($arr[$productSku]['price'] > $row['price']) {
                $arr[$productSku]['price'] = $row['price'];
            }
            if (empty($arr[$productSku]['options'])) {
                $arr[$productSku]['options'] = [];
            }

            if (empty($arr[$productSku]['base_cost'])) {
                $arr[$productSku]['base_cost'] = 0;
            }

            if (empty($arr[$productSku]['base_costs'])) {
                $arr[$productSku]['base_costs'] = [];
            }

            $variantKey = getVariantKey($options);

            $sku             = $this->getSku($productSku, $variantKey);
            $arrLocationCode = $this->getAndSetLocationCode($row, $arr[$productSku]);

            foreach ($arrLocationCode as $locationCode => $baseCost) {
                $arr[$productSku]['variants'][] = [
                    'sku'                      => $sku,
                    'variant_key'              => $variantKey,
                    'variant_key_and_location' => $this->getVariantKeyAndLocation($variantKey, $locationCode),
                    'out_of_stock'             => $outOfStock[$locationCode] ?? false,
                    'price'                    => $row['price'],
                    'base_cost'                => $baseCost,
                    'location_code'            => $locationCode,
                ];
            }
        }
        // only update when product exists in DB
        $variants = [];
        $productsAvailable = array_values($productsAvailable);
        foreach ($productsAvailable as $product) {
            $productId  = $product->id;
            $productSku = $product->sku;
            $options = json_decode($product->options, true);
            $custom_options = [];
            if ($product->full_printed === ProductPrintType::HANDMADE && !empty($options['custom_options'])) {
                $custom_options = ['custom_options' => $options['custom_options']];
            }
            // ignore if price is 0 (not set)
            if (empty($arr[$productSku]['base_costs'])) {
                continue;
            }

            if (empty($arr[$productSku]['base_cost'])) {
                $arr[$productSku]['base_cost'] = min($arr[$productSku]['base_costs']);
            }

            // prevent don't have base cost
            if (empty($arr[$productSku]['base_cost'])) {
                continue;
            }
            // get variant don't exist in file Excel but exists in options and locations
            $arrVariantKeyAndLocation = generateVariantKeysByProductOptions($arr[$productSku]['options']);

            // unset option location then update
            unset($arr[$productSku]['options']['location_codes']);
            Product::query()->where('id', $productId)
                ->update(
                    [
                        'base_cost'  => $arr[$productSku]['base_cost'],
                        'base_costs' => $arr[$productSku]['base_costs'],
                        'options'    => array_merge($arr[$productSku]['options'], $custom_options),
                        'price'      => $arr[$productSku]['price'],
                    ]
                );

            foreach ($arr[$productSku]['variants'] as $variant) {
                $indexKey = array_search($variant['variant_key_and_location'], $arrVariantKeyAndLocation, true);
                unset($arrVariantKeyAndLocation[$indexKey], $variant['variant_key_and_location']);
                $locationCode = $variant['location_code'];

                // if empty => location code is worldwide => get base_cost
                $minBaseCost = $arr[$productSku]['base_costs'][$locationCode] ?? $arr[$productSku]['base_cost'];

                $variant['adjust_price'] = $variant['base_cost'] ? $variant['base_cost'] - $minBaseCost : 0;
                $variant['product_id']   = $productId;

                $variants[] = $variant;
            }

            // insert variant don't exist in file Excel but exists in options
            foreach ($arrVariantKeyAndLocation as $variantKeyAndLocation) {
                $arrString    = explode('-', $variantKeyAndLocation);
                $locationCode = array_pop($arrString);
                $variantKey   = implode('-', $arrString);
                if (empty($variantKey)) {
                    continue;
                }

                $variants[] = [
                    'sku'           => $this->getSku($productSku, $variantKey),
                    'variant_key'   => $variantKey,
                    'product_id'    => $productId,
                    'adjust_price'  => 0,
                    'out_of_stock'  => 1,
                    'price'         => 0,
                    'base_cost'     => 0,
                    'location_code' => strtoupper($locationCode),
                ];
            }
        }
        ProductVariant::query()->whereIn('product_id', collect($productsAvailable)->pluck('id'))->delete();

        foreach (array_chunk($variants, 1000) as $variant) {
            ProductVariant::query()->insert($variant);
        }
        $productIds = collect($productsAvailable)->pluck('id')->toArray();
        if ($productIds) {
            SyncStockStatusJob::dispatch($productIds);
        }
    }

    private function getAndSetOptions($row, &$arr, $parseOption = true): array
    {
        $column       = 'options';
        $stringLength = strlen($column);
        $options      = [];
        $colorValid = SystemColor::query()->distinct()->pluck('name')->toArray();

        foreach ($row as $key => $value) {
            if (strpos($key, $column) !== false) {
                $optionCode = substr($key, $stringLength);
                if (empty($arr[$optionCode])) {
                    $arr[$optionCode] = [];
                }

                // Special handling for color options to support multi-color
                if ($optionCode === 'color' && $parseOption) {
                    $value = $this->validateAndProcessColor($value, $colorValid);
                } else {
                    $value = AbstractModel::mappingOptions($value, $parseOption ? 'all' : 'none');
                }

                if (!in_array($value, $arr[$optionCode])) {
                    $arr[$optionCode][] = $value;
                }
                $options[] = $value;
            }
        }

        return $options;
    }

    private function getOutOfStock($row): array
    {
        $column       = 'out_of_stock';
        $stringLength = strlen($column);
        $output = [];
        foreach ($row as $key => $value) {
            if (str_contains($key, $column)) {
                $locationCode = substr($key, $stringLength);
                if ($locationCode === '') {
                    $locationCode = '*';
                }
                $locationCode = strtoupper($locationCode);
                $output[$locationCode] = !empty($value);
            }
        }

        return $output;
    }

    private function getAndSetLocationCode($row, &$arr): array
    {
        $column          = 'base_cost';
        $stringLength    = strlen($column);
        $arrLocationCode = [];
        foreach ($row as $key => $value) {
            if (strpos($key, $column) !== false) {
                $countryCode = strtoupper(substr($key, $stringLength));
                if ($countryCode === '') { //world wide is '*' => to slug ''
                    $countryCode = '*';
                    //set min base_cost
                    if ($arr['base_cost'] > $value) {
                        $arr['base_cost'] = $value;
                    }
                }

                //set min base_costs
                if (!empty($value)) {
                    $arr['base_costs'][$countryCode] ??= $value;
                    if ($arr['base_costs'][$countryCode] > $value) {
                        $arr['base_costs'][$countryCode] = $value;
                    }
                }

                $arrLocationCode[$countryCode]                  = $value;
                $arr['options']['location_codes'][$countryCode] ??= $countryCode;
            }
        }

        return $arrLocationCode;
    }

    private function getSku($productSku, $variantKey): string
    {
        return $productSku . '-' . $variantKey;
    }

    private function getVariantKeyAndLocation($variantKey, $locationCode): string
    {
        return $variantKey . '-' . strtolower($locationCode);
    }

    private function validateRows(Collection $rows): string
    {
        $message      = '';
        $messageColor = '';
        $colorValid = SystemColor::query()->distinct()->pluck('name')->toArray();
        $colorInvalid = [];
        $rowBaseCostInvalid = [];
        $productFullPrintedInvalid = Product::query()
            ->select('sku')
            ->whereIn('sku', $rows->pluck('product_sku')->unique())
            ->where('product_type', ProductType::TEMPLATE)
            ->whereNotIn('full_printed', [ProductPrintType::PRINT_2D, ProductPrintType::EMBROIDERY, ProductPrintType::HANDMADE])
            ->get()
            ->keyBy('sku');
        foreach ($rows as $index => $row) {
            $options = [];
            $outOfStocks = $this->getOutOfStock($row);
            foreach ($row as $key => $value) {
                if (str_contains($key, 'options')) {
                    if (str_contains($key, 'optionscolor')) {
                        // Handle color validation with multi-color support
                        $colorValue = $this->validateAndProcessColor($value, $colorValid);
                        $options[] = $colorValue;
                    } else {
                        $value = AbstractModel::mappingOptions($value);
                        $options[] = $value;
                    }
                }
                if (empty($value) && !in_array($index + 2, $rowBaseCostInvalid, true) && str_contains($key, 'base_cost')) {
                    $countryCode = strtoupper(substr($key, strlen('base_cost')));
                    if ($countryCode === '') {
                        $countryCode = '*';
                    }
                    if ($outOfStocks[$countryCode] ?? false) { // if out of stock then base cost can be empty
                        continue;
                    }

                    $rowBaseCostInvalid[] = $index + 2;
                }
            }

            $hasOptionColor = array_key_exists('optionscolor', $row->toArray());
            if ($hasOptionColor && !empty($options[0]) && !$this->isValidColor($options[0], $colorValid) && !in_array($options[0], $colorInvalid, true)) {
                $colorInvalid[] = $options[0];
                $messageColor .= $options[0] . ', ';
            }
            if  ($hasOptionColor && $productFullPrintedInvalid->has($row['product_sku']) && $options[0] === 'white') {
                $productFullPrintedInvalid->forget($row['product_sku']);
            }

        }

        if (!empty($messageColor)) {
            $message = 'Colors : ' . $messageColor . 'are not supported by system';
        }

        if (!empty($rowBaseCostInvalid)) {
            if (!empty($message)) {
                $message .= '<br>';
            }
            $message .= 'Row ' . implode(', ', $rowBaseCostInvalid) . ' base cost is empty';
        }

        if ($productFullPrintedInvalid->count()) {
            if (!empty($message)) {
                $message .= '<br>';
            }
            $message .= 'Product SKU: ' . implode(', ', $productFullPrintedInvalid->keys()->toArray()) . ' must have color white';
        }

        return $message;
    }

    /**
     * Validate and process color value, supporting both single and multi-color formats
     */
    private function validateAndProcessColor(string $colorValue, array $validColors): string
    {
        // First, try to find exact match (for multi-color like "White/Black")
        if (in_array($colorValue, $validColors, true)) {
            return $colorValue;
        }

        // If not found, try the standard mapping process for single colors
        $processedColor = AbstractModel::mappingOptions($colorValue);

        // Return the processed color (valid or invalid for error reporting)
        return $processedColor;
    }

    /**
     * Check if a color is valid (supports both single and multi-color)
     */
    private function isValidColor(string $colorValue, array $validColors): bool
    {
        // Direct match for exact color names
        if (in_array($colorValue, $validColors, true)) {
            return true;
        }

        // Check if processed single color is valid
        $processedColor = AbstractModel::mappingOptions($colorValue);
        return in_array($processedColor, $validColors, true);
    }
}

<?php

namespace App\Exports;

use App\Enums\ProductStatus;
use App\Models\ProductVariant;
use App\Models\Template;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;

class ExportBaseCostCatalog implements FromCollection, WithHeadings, ShouldAutoSize
{
    public function collection(): Collection
    {
        $templates = Template::query()
            ->select([
                'id',
                'sku',
                'name',
            ])
            ->where('status', ProductStatus::ACTIVE);

        return ProductVariant::query()
            ->select([
                'product.sku',
                'product.name',
                'product_variant.variant_key',
                'system_location.name',
                'product_variant.base_cost',
                'product_variant.out_of_stock',
            ])
            ->joinSub($templates, 'product', 'product_variant.product_id', '=', 'product.id')
            ->join('system_location', 'system_location.code', '=', 'product_variant.location_code')
            ->get();
    }

    public function headings(): array
    {
        return [
            'product_sku',
            'product_name',
            'variant_key',
            'location',
            'base_cost',
            'out_of_stock',
        ];
    }
}

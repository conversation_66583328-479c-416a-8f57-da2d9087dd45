<?php

namespace App\Exports\Admin\Orders;

use App\Enums\CacheTime;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderTypeEnum;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\SystemLocation;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class OrderDiffBaseCostExport implements FromArray, WithHeadings
{
    public function array(): array
    {
        $orders = cache()->remember('orders_21', CacheTime::CACHE_1W, function () {
            return Order::query()
                ->where('payment_status', OrderPaymentStatus::PAID)
                ->with([
                    'seller',
                    'order_products',
                    'order_products.template',
                ])
                ->where('type', '!=', OrderTypeEnum::SERVICE)
                ->whereBetween('paid_at', [
                    now()->subDays(1),
                    now()->subDays(0),
                ])
                ->get();
        });

        $arr = [];
        foreach ($orders as $order) {
            foreach ($order->order_products as $orderProduct) {
                $templateProduct = $orderProduct->template;
                $marketLocation  = getLocationByCode($order->country);
                $productBaseCost = getBaseCostsByLocation($templateProduct, $marketLocation);

                $variantKey      = getVariantKey($orderProduct['options']);
                $location        = SystemLocation::findByCountryCode($order->country);
                $regionCodes     = $location ? array_reverse($location->getRegionCodes()) : ['*'];
                $templateVariant = ProductVariant::query()
                    ->select([
                        'price',
                        'adjust_price',
                        'out_of_stock',
                        'base_cost',
                        'location_code',
                    ])
                    ->where([
                        'variant_key' => $variantKey,
                        'product_id'  => $templateProduct->id,
                    ])
                    ->whereIn('location_code', $regionCodes)
                    ->orderByRaw("FIELD(location_code,'" . implode("','", $regionCodes) . "') desc")
                    ->first();

                if (!is_null($templateVariant)) {
                    $productBaseCost = $templateVariant->base_cost;
                }

                $product = Product::query()
                    ->onSellerConnection($orderProduct->seller)
                    ->where('id', $orderProduct->product_id)
                    ->first();
                $orderProduct->setRelation('product', $product);

                if (!is_null($orderProduct->product)) {
                    $productBaseCost += $orderProduct->product->extra_print_cost;
                }

                if ($productBaseCost !== $orderProduct->base_cost) {
                    $fixedAmount = ($orderProduct->base_cost - $productBaseCost) * $orderProduct->quantity;

                    $arr[] = [
                        $order->id,
                        $order->created_at,
                        $order->type,
                        $order->country,
                        $order->seller->email,
                        $orderProduct->id,
                        $orderProduct->template->name,
                        $orderProduct->template_id,
                        $variantKey,
                        $orderProduct->quantity,
                        $orderProduct->base_cost,
                        $productBaseCost,
                        $fixedAmount,
                        $templateVariant->location_code,
                    ];
                }
            }
        }

        return $arr;
    }

    public function headings(): array
    {
        return [
            'order_id',
            'time',
            'order_type',
            'country',
            'seller_email',
            'order_product_id',
            'product_name',
            'template_id',
            'variant_key',
            'quantity',
            'base_cost',
            'correct_base_cost',
            'fix_amount',
            'base_cost_location',
        ];
    }
}

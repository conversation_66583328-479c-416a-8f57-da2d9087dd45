<?php
namespace App\Exports\Admin;

use App\Models\ProductReview;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class ProductReviewExport implements FromCollection, WithHeadings, WithMapping
{
    protected Collection $reviews;

    public function __construct(Collection $reviews)
    {
        $this->reviews = $reviews;
    }

    public function collection(): Collection
    {
        return $this->reviews;
    }

    public function map($row): array
    {
        $reviewImages = $row->productReviewFiles
            ->filter(fn($file) => $file->type === 'image')
            ->pluck('url')
            ->map(fn($url) => s3Url($url))
            ->implode('; ');
        $productThumb = $row->orderProduct?->thumb_url ? s3Url($row->orderProduct->thumb_url) : '';
        /** @var ProductReview $row */
        return [
            $row->order?->order_number,
            $row->order?->paid_at->format('M d, Y H:i'),
            $row->order?->customer_email,
            $row->order?->customer_name,
            $row->order?->country,
            optional($row->seller)->email ?? 'Unknown',
            optional($row->seller)->name ?? 'Unknown',
            $row->orderProduct?->product_name,
            $row->orderProduct?->sku,
            $productThumb,
            $row->orderProduct?->supplier_name,
            $row->comment,
            $reviewImages,
            $row->average_rating,
            $row->created_at->setTimezone('Asia/Ho_Chi_Minh')->format('M d, Y H:i'),
            optional($row->staff)->name ?? 'Unassigned',
            $row->finished_at?->setTimezone('Asia/Ho_Chi_Minh')->format('M d, Y H:i') ?? '',
            optional($row->staffSupport)->name ?? '',
        ];
    }

    public function headings(): array
    {
        return [
            'Order ID',
            'Order at',
            'Customer Email',
            'Customer Name',
            'Country',
            'Email Seller',
            'Seller Name',
            'Product Name',
            'Product Sku',
            'Product Thumb',
            'Supplier Name',
            'Review',
            'Review Images',
            'Rating',
            'Review at',
            'Assigned to',
            'Updated at',
            'Support',
        ];
    }
}

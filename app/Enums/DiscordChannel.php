<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class DiscordChannel extends Enum
{
    public const ORDER = 'order';
    public const ERROR = 'error';
    public const EMAIL = 'email';
    public const ALERT = 'alert';
    public const JOB = 'job';
    public const REPORTED = 'reported';
    public const ABANDONED_SUCCESS = 'abandoned.success';
    public const ABANDONED_ERROR = 'abandoned.error';
    public const FULFILL_ORDER = 'fulfill_order';
    public const SMS = 'sms';
    public const LOG_DEBUG = 'log_debug';
    public const SOCIAL_FEED = 'social_feed';
    public const ADMIN_ACTIVITY = 'admin_activity';
    public const DEV_CHAT = 'dev_chat';
    public const DEV_LOGS = 'dev_logs';
    public const TRACKING_STATUS_LOGS = 'tracking_status_logs';
    public const ERROR_CHECKOUT = 'error_checkout';
    public const ERROR_ELASTIC = 'error_elastic';
    public const FULFILL_PRODUCT = 'fulfill_product';
    public const PB = 'pb';
    public const TOPUP = 'topup';
    public const TOPUP_DEV = 'topup_dev';
    public const EMAIL_MARKETING = 'email_marketing';
    public const LOG_REQUEST = 'log_request';
    public const CLEAR_CACHE_HTML = 'clear_cache_html';
    public const ADMIN_WARNING = 'admin_warning';
    public const SALES_MILESTONE = 'sales_milestone';
    public const ADMIN_INFO = 'admin_info';
    public const AVATAR_API_LOG = 'avatar-api-log';
    public const PAYPAL_DISPUTE = 'paypal_dispute';
    public const IMPORTANT = 'important';
    public const SHOPIFY = 'shopify';
    public const SCAN_TRADEMARK = 'scan_trademark';
    public const ANALYTIC = 'analytic';
    public const CUSTOM_CAMPAIGN = 'custom_campaign';
    public const UPLOAD_FILE_FAILED = 'upload_file_failed';
    public const ORDER_REFUND = 'order_refund';
    public const IMPORT_FULFILL_ORDER_FAILED = 'import_fulfill_order_failed';
    public const BULK_UPLOAD = 'bulk_upload';
    public const ADDRESS_VALIDATE = 'address_validate';
    public const EMERGENCY = 'emergency';
    public const LONG_QUERIES = 'long-queries';
    public const THIENNT_LOG = 'thiennt_log';
    public const SUPPLIER_LOG = 'supplier_log';
    public const SHARDING_DATABASE = 'sharding_database';
    public const TECH_SUPPORT = 'tech_support';
    public const ORDER_AT_RISK = 'order_at_risk';
    public const ORDER_CUSTOM_SERVICE = 'order_custom_service';
    public const PTDT_CHANNEL = 'ptdt_channel';
    public const LOCK_PROCESS_CHANNEL = 'lock_process_channel';
    public const OPERATION_TEAM_CHANNEL = 'operation_team_channel';
    public const OPERATION_CHANNEL = 'operation_channel';
    public const DEV_DEBUG_LOGS = 'dev_debug_logs';
}

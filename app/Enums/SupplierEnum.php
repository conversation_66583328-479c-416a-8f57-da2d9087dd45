<?php
/** @noinspection SpellCheckingInspection */

namespace App\Enums;

use <PERSON>Samp<PERSON>\Enum\Enum;

final class SupplierEnum extends Enum
{
    public const PRINTLOGISTIC = 1;
    public const MONSTERDIGITAL = 2;
    public const DREAMSHIP = 3;
    public const SHIRTPLATFORM = 4;
    public const PRINTHIGH = 5;
    public const CUSTOMCAT = 6;
    public const JONDO = 7;
    public const INVITION = 8;
    public const MWW = 9;
    public const GOOTEN = 10;
    public const BURGERPRINTS = 11;
    public const SENPRINT_VN = 12;
    public const GEARBUBBLE = 13;
    public const PRINTGEEK = 14;
    public const TEXTILDRUCK = 15;
    public const PRINTFORIA = 16;
    public const YOYCOL = 17;
    public const PRINTWAY = 18;
    public const GEARMENT = 19;
    public const SHAO = 20;
    public const ZHOU = 21;
    public const PRIMA = 22;
    public const SWIFTPOD = 23;
    public const ONOS = 24;
    public const OTP = 25;
    public const LUXURY_PRO = 26;
    public const DUPLIUM = 27;
    public const QTCO = 28;
    public const PRINTLOGISTIC_V2 = 29;
    public const LENFUL = 30;
    public const PRINTWAY_V2 = 31;
    public const GEARMENT_LABEL = 32;
    public const WIDE_SHOP = 33;
    public const HUB_FULFILL = 35;
    public const FLASH_SHIP = 36;
    public const FLASH_SHIP_EXPEDITE = 37;
    public const SHINE_ON = 38;
    public const FLASH_SHIP_LABEL = 39;
    public const TO_ADD_IT = 40;
    public const BEE_FUL = 41;
    public const SENHUB = 42;
    public const GEARMENT_TWO_DAYS = 43;
    public const GEARMENT_TWO_DAYS_LABEL = 44;
    public const GOOTEN_TWO_DAYS = 45;
    public const DREAMSHIP_TWO_DAYS = 46;
    public const SWIFTPOD_TIKTOK = 47;
    public const SWIFTPOD_TIKTOK_LABEL = 48;
    public const PRINTIK = 49;
    public const BONDRE = 50;
    public const MERCHIZE = 51;
    public const BEEFUL_V2 = 52;
    public const PRINT_ARROW = 53;
    public const PRINT_ARROW_EMBROIDERY = 54;
    public const PRESSIFY = 55;
    public const CUSTOMCAT_LABEL = 57;
    public const PRINTLOGISTIC_UK = 58;
    public const NLTP = 59;
    public const PRINTLOGISTIC_US = 60;
    public const FM_EASY_CUSTOM = 61;
    public const BEEFUL_UK = 62;
    public const PENTIFINE = 63;

    public const ONEPRINT = 64;
    public const CASPER_VN = 65;

}

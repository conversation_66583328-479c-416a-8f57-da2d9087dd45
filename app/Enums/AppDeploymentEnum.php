<?php declare(strict_types=1);

namespace App\Enums;

use BenSampo\Enum\Enum;

final class AppDeploymentEnum extends Enum
{
    public const MASTER = 'master';
    public const DEVELOP = 'develop';
    public const BETA = 'beta';

    public static function isMaster(): bool
    {
        return config('app.deployment') === self::MASTER;
    }

    public static function isDevelop(): bool
    {
        return config('app.deployment') === self::DEVELOP;
    }

    public static function isBeta(): bool
    {
        return config('app.deployment') === self::BETA;
    }

    public static function isDevelopment(): bool
    {
        return self::isDevelop() || self::isBeta();
    }
}

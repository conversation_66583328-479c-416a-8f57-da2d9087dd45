<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class OrderStatus extends Enum
{
    public const ARCHIVED = 'archived';
    public const DRAFT = 'draft';
    public const PENDING = 'pending';
    public const PROCESSING = 'processing';
    public const COMPLETED = 'completed';
    public const ON_HOLD = 'on_hold';
    public const REFUNDED = 'refunded';
    public const CANCELLED = 'cancelled';
    public const DELETED = 'deleted';
    public const PENDING_PAYMENT = 'pending_payment';
    public const SUSPENDED = 'suspended';

    /**
     * @return string[]
     */
    public static function withoutDraft(): array
    {
        return [
            self::PENDING,
            self::PROCESSING,
            self::COMPLETED,
            self::ON_HOLD,
            self::REFUNDED,
            self::CANCELLED,
            self::DELETED,
            self::PENDING_PAYMENT,
            self::SUSPENDED,
        ];
    }

    /**
     * @return string[]
     */
    public static function canNotShowForSupplier(): array
    {
        return [
            self::ON_HOLD,
            self::REFUNDED,
            self::DELETED,
        ];
    }

    /**
     * @return string[]
     */
    public static function listingStatusForSupplier(): array
    {
        return [
            self::PROCESSING,
            self::COMPLETED,
            self::ON_HOLD,
        ];
    }

    /**
     * @return string[]
     */
    public static function doNotSync(): array
    {
        return [
            self::CANCELLED,
            self::COMPLETED,
            self::REFUNDED,
            self::SUSPENDED,
            self::DELETED,
        ];
    }
}

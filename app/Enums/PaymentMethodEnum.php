<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use Illuminate\Support\Str;

final class PaymentMethodEnum extends Enum
{
    public const STRIPE = 'stripe';
    public const PAYPAL = 'paypal';
    public const MOMO = 'momo';
    public const TAZAPAY = 'tazapay';

    public static function isStripe($value): bool
    {
        return Str::startsWith($value, self::STRIPE);
    }

    public static function isPaypal($value): bool
    {
        return $value === self::PAYPAL;
    }
}

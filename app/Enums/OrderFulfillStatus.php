<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class OrderFulfillStatus extends Enum
{
    public const UNFULFILLED = 'unfulfilled';
    public const FULFILLED = 'fulfilled';
    public const PARTIALLY_FULFILLED = 'partial_fulfilled';
    public const CANCELLED = 'cancelled';
    public const PROCESSING = 'processing';
    public const ON_HOLD = 'on_hold';
    public const INVALID = 'invalid';
    public const NO_SHIP = 'no_ship';
    public const REVIEWING = 'reviewing';
    public const DESIGNING = 'designing';
    public const ON_DELIVERY = 'on_delivery';

    /**
     * @return string[]
     */
    public static function isHoldPaymentAndCharge(): array
    {
        return [
            self::INVALID,
            self::REVIEWING,
            self::DESIGNING,
        ];
    }
    /**
     * @return string[]
     */
    public static function canConfirmAddress(): array
    {
        return [
            ...self::isHoldPaymentAndCharge(),
            self::ON_HOLD,
            self::UNFULFILLED,
        ];
    }

    public static function canSync(): array
    {
        return [
            self::UNFULFILLED,
            self::NO_SHIP,
        ];
    }
}

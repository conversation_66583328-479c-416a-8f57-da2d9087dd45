<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class <PERSON><PERSON><PERSON><PERSON>s extends Enum
{
    public const SYNC_CLEAR_CACHE = 'senprints_sync_clear_cache';
    public const ALL_CACHE = 'all_cache';
    public const CACHE_5m = 300;
    public const CACHE_1H = 3600;
    public const CACHE_30m = 1800;
    public const CACHE_4H = 3600 * 4;
    public const CACHE_24H = 86400;
    public const CACHE_30D = 86400 * 30;
    public const CACHE_1W = 86400 * 7;
    public const CACHE_6H = 3600 * 6;
    public const CACHE_12H = 3600 * 12;
    public const CACHE_TYPE_ALTERNATIVE = 'altredis';
    public const CACHE_TYPE_DEFAULT = 'default';

    public const CHECK_EMAIL_SETTING = 'check_email_setting';

    // public const SYSTEM_CONFIGS = 'system_configs';
    public const BLOCKED_CAMP_IDS = 'blocked_camp_ids';
    public const SYSTEM_CONFIGS_BACKEND = 'system_configs_backend';
    public const SYSTEM_CONFIGS_FRONTEND = 'system_configs_frontend';
    public const SYSTEM_ELASTIC_LOG = 'elasticlog';
    public const SYSTEM_PRODUCT_TEMPLATES = 'system_product_templates'; // group by category
    public const LIST_PRODUCT_TEMPLATES = 'list_product_templates4';
    public const LIST_PRODUCT_TEMPLATES_FOR_FULFILL = 'list_product_templates_for_fulfill';
    public const LIST_VARIANT_BY_TEMPLATE = 'list_variant_by_template4';
    public const LIST_VARIANT_BY_PRODUCT_CUSTOM_PRICE = 'list_variant_by_product_custom_price';
    public const SYSTEM_EXPRESS_PRODUCT_TEMPLATE = 'system_express_product_templates';
    public const SYSTEM_SUPPLIERS = 'system_suppliers';
    public const SEPARATE_NAME_SYSTEM_SUPPLIERS = 'separate_name_system_suppliers';
    public const GENERAL_SETTINGS = 'general_settings';
    public const CURRENCIES = 'system_currencies';
    public const CATEGORIES = 'system_categories';
    public const COLORS = 'system_colors';
    public const LANGUAGES = 'system_languages';
    public const LOCATIONS = 'system_locations';
    public const TIMEZONES = 'system_timezones';
    public const COUNTRIES = 'system_countries';
    public const SUPPORTERS = 'supporters';
    public const ALL_STAFF = 'all_staff';
    public const SIZE_GUIDE = 'product_size_guide';
    public const SHARD_COUNT = 'shard_count';
    public const FULFILL_MAPPING = 'fulfillment_mappings';
    public const FAQ = 'faq';
    public const TEMPLATES = 'system_templates';
    public const PRICING_STATS = 'pricing_stats';
    public const DOMAIN_MANAGEMENT_API = 'domain_manager_api';
    public const SYSTEM_CONFIG_ENCRYPT_KEY = 'system_config_encrypt_key';
    public const PINGPONGX_AUTHORIZATION = 'pingpongx_authorization';
    public const PINGPONGX_SUBSCRIBE_NOTIFICATION = 'pingpongx_subscribe_notification';
    public const PINGPONGX_PAYOUT_WHITELIST_ENABLED = 'pingpongx_payout_whitelist_enabled';
    public const PINGPONGX_PAYOUT_QUICK_TESTING_ENABLED = 'pingpongx_payout_quick_testing_enabled';
    public const PINGPONG_PAYOUT_INSUFFICIENT_BALANCE = 'pingpong_payout_insufficient_balance';
    public const SYNC_PRODUCTS_TO_ELASTICSEARCH = 'sync_products_to_elasticsearch';
    public const SYNC_PRODUCTS_SHARDING_TO_ELASTICSEARCH = 'sync_products_sharding_to_elasticsearch';
    public const CREATE_BULK_DRAFT_CAMPAIGNS = 'create_bulk_draft_campaigns';
    public const SYNC_SLUG = 'sync_slug';
    public const UPDATE_SELLER_ID_UPSELL = 'update_seller_id_upsell';
    public const SYSTEM_PAYMENT_GATEWAYS = 'system_payment_gateways_';
    public const AVERAGE_PROCESSING_DAY = 'average_processing_day';
    public const SHIPPING_DAY = 'shipping_day';
    public const PAYOUT_PAYPAL = 'payout_paypal';
    public const PAYOUT_PAYPAL_QUICK_TEST = 'payout_paypal_quick_test';
    public const SOCIAL_FEED = 'social_feed';
    public const SYNC_PRODUCTS_TO_SINGLESTORE = 'sync_products_to_singlestore';
    public const SYNC_ORDERS_TO_SINGLESTORE = 'sync_orders_to_singlestore';
    public const SYNC_EVENT_LOGS_TO_SINGLESTORE = 'sync_event_logs_to_singlestore';
    public const LAST_DATETIME_SCHEDULE_RUN = 'last_time_schedule_run';
    public const LAST_DATETIME_SCHEDULE_RAN = 'last_time_schedule_ran';
    public const SHIPPING_CARRIERS = 'shipping_carriers';

    // prefix
    public const TEMPLATE_PRODUCT_PREFIX = 'template_product_';
    public const TEMPLATE_FULFILL_PRODUCT_PREFIX = 'template_fulfill_product_';
    public const TEMPLATE_PRODUCT_CATEGORY_PREFIX = 'template_product_category_';
    public const TEMPLATE_PRODUCT_CATALOG_PREFIX = 'template_product_catalog_';
    public const STORE_BEST_SELLER_IDS_PREFIX = 'store_best_seller_ids_';
    public const UPSELL_PREFIX = 'upsell_';
    public const CAMPAIGN_ANALYTIC_PREFIX = 'campaign_analytic_';
    public const SELLER_ANALYTIC_PREFIX = 'seller_analytic_';
    public const PRODUCT_PREFIX = 'product_';
    public const TOP_PRODUCT_PREFIX = 'top_product_';
    public const STORE_PREFIX = 'store_';
    public const STORE_ID_PREFIX = 'STORE_ID_';
    public const TOKEN_SUPPLIER_PREFIX = 'token_';
    public const SELLER_PRICING_PREFIX = 'seller_pricing_';
    public const STOREFRONT_INDEX_PRODUCT_PREFIX = 'storefront_index_product_';
    public const STOREFRONT_FILTER_PRODUCT_PREFIX = 'storefront_filter_product_';
    public const TEST_PRICE_ORDER_PRODUCT = 'test_price_order_product_';
    public const STATS_PREFIX = 'stats_';
    public const BUNDLE_EXCLUDE_IDS = 'bundle_exclude_ids2_'; // use ids2 to prevent old cache
    public const BUNDLE_DISCOUNT_PREFIX = 'bundle_discount_';
    public const STOREFRONT_PRODUCT_SIMILAR_PREFIX = 'storefront_product_similar_';
    public const STOREFRONT_PROMOTION_PREFIX = 'storefront_promotion_';
    public const STOREFRONT_PRODUCT_REVIEW_PREFIX = 'storefront_product_review_';
    public const STOREFRONT_PRODUCT_REVIEW_SUMMARY_PREFIX = 'storefront_product_review_summary_';
    public const STOREFRONT_PRODUCT_REVIEW_STATS_PREFIX = 'storefront_product_review_stats_';
    public const STORE_VISITS_PREFIX = 'store_visits_';
    public const SENDMAIL_FAIL_PREFIX = 'sendmail_fail_';
    public const SHOPIFY_APP_CONFIG = 'shopify_app_settings';
    public const TIKTOK_SHOP_CONFIG = 'tiktok_shop_settings';
    public const CHECKOUT_FORM_CONFIG = 'checkout_form_config';
    public const CHECKOUT_PAYMENT_GATEWAYS = 'checkout_payment_gateways';
    public const CRISP_FILTER_KEYWORDS = 'crisp_filter_keywords';
    public const CRISP_ENABLED = 'crisp_enabled';

    // klaviyo cache keys
    public const KLAVIYO_SYNC_LOCK = 'klaviyo_sync_lock_';

    // cache tags
    public const SENDMAIL_FAILED = 'sendmail_failed';

    public const FEATURED_INFO = 'featured_information';
    public const ANALYTIC_PERCENT_PLATFORM = 'analytic_percent_platform';
    public const ANALYTIC_PAYMENT_METHOD = 'analytic_payment_method';

    public const SUPPLIER_HOLDS_PREFIX = 'supplier_holds';

    public const PRODUCT_FULFILL_MAPPING_HOLDS_PREFIX = 'product_fulfill_mapping_holds';

    public const CAMPAIGN_ID_PREFIX = 'CAMPAIGN_ID_';

    public const PRODUCT_ID_BY_CATEGORY = 'product_id_by_category_';

    public const POOL_PENDING_IMPORT_CAMPAIGN_ROWS = 'pool_pending_import_campaign_rows';
    public const SEVENTEEN_TRACK_QUOTA = 'seventeen_track_quota';

    public const HOME_PAGE_COLLECTION_BANNER = 'home_page_collection_banner';

    public const SELLER_CONNECTION = 'seller_connection';
    public const INTERACT_TYPES = 'interact_types';

    public const DATESTAMP_FOR_CLEAN_INDEX_EVENT_LOGS = 'datestamp_for_clean_index_event_logs';

    /**
     * @return string
     */
    public static function poolPendingImportCampaignRowsLock(): string
    {
        return self::POOL_PENDING_IMPORT_CAMPAIGN_ROWS . '_lock_1';
    }

    public static function getStoreDomainKey($domain): string
    {
        return self::STORE_PREFIX . $domain;
    }

    public static function getProductCacheKey($slug): string
    {
        return self::PRODUCT_PREFIX . $slug;
    }

    public static function getUpsellName($type, $productId, $storeId = ''): string
    {
        return self::UPSELL_PREFIX . $type . '_' . $productId . '_' . $storeId; //upsell_{type}_{productId}_{storeId}
    }

    public static function getBestSellerIdsByStore($storeId, $column): string
    {
        return self::STORE_BEST_SELLER_IDS_PREFIX . $storeId . '_' . $column;
    }

    public static function getTemplateProductCategory($templateId): string
    {
        return self::TEMPLATE_PRODUCT_CATEGORY_PREFIX . $templateId;
    }

    public static function getTemplateFulfillProduct($templateId): string
    {
        return self::TEMPLATE_FULFILL_PRODUCT_PREFIX . $templateId;
    }

    public static function getHomeListingType($type, $storeId, $collectionId = null): string
    {
        $string = self::STORE_PREFIX . $type . '_' . $storeId;
        if (!empty($collectionId)) {
            $string .= '_' . $collectionId;
        }

        return $string;
    }

    public static function getCampaignAnalyticBySortAndDateRange($sort, $dateRange, $storeId): string
    {
        $userId = currentUser()->getUserId();
        return self::CAMPAIGN_ANALYTIC_PREFIX . $userId . '_' . $storeId . '_' . $sort . '_' . $dateRange;
    }

    public static function getSellerAnalyticBySortAndDateRange($sort, $dateRange): string
    {
        return self::SELLER_ANALYTIC_PREFIX . $sort . '_' . $dateRange; //seller_analytic_{sort}_{dateRange}
    }

    public static function getTokenSupplier($supplierId): string
    {
        return self::TOKEN_SUPPLIER_PREFIX . $supplierId; //token_{supplierId}
    }

    public static function getShardCount($shardId, $modelName): string
    {
        return $shardId . '_' . self::SHARD_COUNT . '_' . $modelName; // {$shardId}_shard_count_{modelName}
    }

    public static function getStats($type, $dateType, $dateRangeType, $storeId = '', $isV2 = false): string
    {
        $type   = ($isV2) ? $type . '_v2' : $type;
        $date   = getTodayTimeZone()->$dateType;
        $user   = currentUser();
        $userId = ($user->isSeller()) ? $user->getUserId() : 'admin';

        // stats_{type}_yesterday_{day}...
        // stats_{type}_last_week_{week}...
        // stats_{type}_last_month_{month}...
        return self::STATS_PREFIX . $type . '_' . $dateRangeType . '_' . $date . '_' . $userId . '_' . $storeId;
    }

    public static function getSettingKey($user_id, $key)
    {
        return $user_id . '_' . $key;
    }

    public static function getStorefrontProductReview($campaignId, $templateId, $filter, $page, $perPage): string
    {
        return self::STOREFRONT_PRODUCT_REVIEW_PREFIX . $campaignId . '_' . $templateId . '_' . $filter . '_' . $page . '_' . $perPage;
    }

    public static function getCachesTemplate($templateId): array
    {
        return [
            self::getTemplateProductCategory($templateId),
            self::getTemplateFulfillProduct($templateId),
            self::getTemplateCatalog($templateId),
        ];
    }

    public static function getFulfillMapping($type): string
    {
        return self::FULFILL_MAPPING . '_' . $type;
    }

    public static function getAverageProcessingDay($templateId, $supplierId): string
    {
        return self::AVERAGE_PROCESSING_DAY . '_' . $templateId . '_' . $supplierId;
    }

    public static function getShippingTime($country, $shippingMethod): string
    {
        return self::SHIPPING_DAY . '_' . $country . '_' . $shippingMethod;
    }

    public static function getTemplateCatalog($templateId): string
    {
        return self::TEMPLATE_PRODUCT_CATALOG_PREFIX . '_' . $templateId;
    }

    public static function getSupplierHoldCacheKey($supplierId): string
    {
        return self::SUPPLIER_HOLDS_PREFIX . '_' . $supplierId;
    }

    public static function getProductFulfillMappingHoldCacheKey($templateId, $fulfillProductId): string
    {
        return self::PRODUCT_FULFILL_MAPPING_HOLDS_PREFIX . '_' . $templateId . '_' . $fulfillProductId;
    }

    public static function getSsChartStats($dateRangeType, $staffId): string
    {
        return self::STATS_PREFIX . 'sschart_' . $dateRangeType . '_' . $staffId;
    }

    public static function getSsReport($dateRangeType, $fromDate = null, $toDate = null, $isSS = false): string
    {
        return self::STATS_PREFIX . 'sstable_' . $dateRangeType . '_' . ($fromDate ?? '_') . ($toDate ?? '') . ($isSS ? 1 : 0);
    }
    public static function getNewSsReport($dateRangeType, $fromDate = null, $toDate = null, $saleStaffId = null): string
    {
        return self::STATS_PREFIX . 'new_ss_table_' . $dateRangeType . '_' . ($fromDate ?? '_') . ($toDate ?? '_') . ($saleStaffId ?? '');
    }

    public static function getSaleDiffReport($dateRangeType1, $dateRangeType2, $fromDate1 = null, $toDate1 = null, $fromDate2 = null, $toDate2 = null): string
    {
        return self::STATS_PREFIX . 'sale_diff_' . $dateRangeType1 . '_' . $dateRangeType2 . '_' . ($fromDate1 ?? '_') . ($toDate1 ?? '_') . ($fromDate2 ?? '_') . ($toDate2 ?? '_');
    }

    public static function getNewSellerSaleMarketingReport($dateRangeType, $fromDate = null, $toDate = null, $saleStaffId = null): string
    {
        return self::STATS_PREFIX . 'new_seller_sale_marketing_table_' . $dateRangeType . '_' . ($fromDate ?? '_') . ($toDate ?? '_') . ($saleStaffId ?? '');
    }
    public static function getNewSaleItemsReport($dateRangeType, $fromDate = null, $toDate = null, $isSS = false): string
    {
        return self::STATS_PREFIX . 'sstable_new_sale_items_' . $dateRangeType . '_' . ($fromDate ?? '_') . ($toDate ?? '') . ($isSS ? 1 : 0);
    }

    public static function getVariantsByTemplate($templateId): string
    {
        return self::LIST_VARIANT_BY_TEMPLATE . '_' . $templateId;
    }

    public static function getVariantsByProductCustomPrice($productId): string
    {
        return self::LIST_VARIANT_BY_PRODUCT_CUSTOM_PRICE . '_' . $productId;
    }

     /**
     * @param $sellerId
     * @param string $keywords
     * @param $storeId
     * @param $collectionId
     * @param $categoryId
     * @return string
     * @throws Exception
     */
    public static function getAffiliateListingType($sellerId, $keywords, $storeId, $collectionId = null, $categoryId = null): string
    {
        return self::STORE_ID_PREFIX . $storeId . '_' . $sellerId . '_' . $keywords . '_(' . ($collectionId ?? 'null') . ')_(' . ($categoryId ?? 'null') . ')';
    }

    public static function getStoreId($storeId): string
    {
        return self::STORE_ID_PREFIX . $storeId;
    }

    public static function getCampaignId($campaignId): string
    {
        return self::CAMPAIGN_ID_PREFIX . $campaignId;
    }

    public static function get17TrackQuota () {
        return self::SEVENTEEN_TRACK_QUOTA;
    }

    public static function getTemplateProductByTemplateId($templateId): string
    {
        return self::TEMPLATE_PRODUCT_PREFIX . $templateId;
    }

    public static function getProductTemplateTags($templateId): array
    {
        $tag1 = self::SYSTEM_PRODUCT_TEMPLATES; // big tag all over template product
        $tag2 = self::getTemplateProductByTemplateId($templateId);

        return [
            $tag1, $tag2
        ];
    }

    public static function getStoreTag($storeId): string
    {
        return self::STORE_ID_PREFIX . $storeId;
    }

    public static function getStoreIdKey($id): string
    {
        return self::STORE_PREFIX . $id;
    }
}

<?php

namespace App\Logging;

use App\Services\StoreService;
use Illuminate\Log\Logger;
use Illuminate\Support\Str;

/**
 * Same from SenHub Backend
 */
class WithDebugContext
{
    /**
     * @param Logger $logger
     * @return void
     * @throws \Throwable
     */
    public function __invoke(Logger $logger): void
    {
        $logger->withContext(self::debugContext());
    }

    /**
     * @return array
     * @throws \Throwable
     */
    public static function debugContext(): array
    {
        $request = request();
        $context = [
            'env' => app()->environment(),
            'app_name' => config('app.name'),
            'host_name' => gethostname(),
            'server_info' => getenv('SERVER_INFO'),
            'application_name' => 'backend-api',
        ];
        $request_params = $request ? json_encode($request->all(), JSON_THROW_ON_ERROR) : [];
        $request_route_params = $request ? json_encode($request->route()?->parameters(), JSON_THROW_ON_ERROR) : [];
        $store_domain = StoreService::getDomain();
        $request_ip_address = $request ? getIp($request) : '';
        $request_user_agent = $request ? $request->userAgent() : '';
        $request_path = $request ? $request->path() : '';
        $userId = currentUser()->getUserId();
        if (!empty($request_params)) {
            $context['request_params'] = $request_params;
        }
        if (!empty($request_route_params)) {
            $context['request_route_params'] = $request_route_params;
        }
        if (!empty($store_domain)) {
            $context['store_domain'] = $store_domain;
        }
        if (!empty($request_ip_address)) {
            $context['request_ip_address'] = $request_ip_address;
        }
        if (!empty($request_user_agent)) {
            $context['request_user_agent'] = $request_user_agent;
        }
        if (!empty($request_path)) {
            $context['request_path'] = $request_path;
        }
        if (!empty($userId)) {
            $context['user_id'] = $userId;
        }
        return $context;
    }

    /**
     * @return string
     * @throws \Throwable
     */
    public static function debugContextAsMessage(): string
    {
        $context = self::debugContext();
        $message = '';
        foreach ($context as $key => $value) {
            $message .= Str::headline($key) . ': ' . $value . PHP_EOL;
        }
        return $message;
    }
}

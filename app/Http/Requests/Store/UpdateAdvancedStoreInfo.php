<?php

namespace App\Http\Requests\Store;

use App\Enums\CollectionModeEnum;
use App\Models\Collection;
use Illuminate\Foundation\Http\FormRequest;

class UpdateAdvancedStoreInfo extends FormRequest
{
    public function rules(): array
    {
        return [
            'list_all_my_campaigns' => 'nullable|boolean',
            'random_popular' => 'nullable|boolean',
            'market_place_listing' => 'nullable|boolean',
            'market_place_upsell' => 'nullable|boolean',
            'option_label_enable' => 'nullable|boolean',
            'enable_search' => 'nullable|boolean',
            'search_return_x_page' => 'nullable|boolean',
            'enable_add_to_cart' => 'nullable|boolean',
            'enable_product_name_after' => 'nullable|boolean',
            'enable_product_name_export_feed' => 'nullable|boolean',
            'enable_contract_form' => 'nullable|boolean',
            'enable_crisp_support' => 'nullable|boolean',
            'disable_promotion' => 'nullable|boolean',
            'disable_related_product' => 'nullable|boolean',
            'disable_related_collection' => 'nullable|boolean',
            'disable_pre_discount' => 'nullable|boolean',
            'auto_sitemap' => 'nullable|boolean',
            'enable_payment_ssl_norton' => 'nullable|boolean',
            'enable_deliver_to' => 'nullable|boolean',
            'enable_insurance_fee' => 'nullable|boolean',
            'product_select_type' => 'nullable|string',
            'show_countdown' => 'nullable|integer',
            'countdown_end_time' => 'nullable',
            'show_product_stats' => 'nullable|boolean',
            'best_seller_collection_id' => [
                'nullable',
                'integer',
                $this->customCollectionValidationRule(),
            ],
            'feature_collection_id' => 'nullable|integer|exists:collection,id',
            'featured_collection_ids' => 'nullable|array',
            'new_arrival_collection_id' => [
                'nullable',
                'integer',
                $this->customCollectionValidationRule(),
            ],
            'feature_text' => 'nullable|string',
        ];
    }

    private function customCollectionValidationRule(): \Closure
    {
        return static function ($attribute, $value, $fail) {
            if (!in_array((int) $value, [CollectionModeEnum::DISABLED, CollectionModeEnum::AUTO], true) && !Collection::query()->where('id', $value)->exists()) {
                $fail($attribute.' is invalid.');
            }
        };
    }
}

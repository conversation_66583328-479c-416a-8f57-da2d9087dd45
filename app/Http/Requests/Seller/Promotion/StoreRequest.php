<?php

namespace App\Http\Requests\Seller\Promotion;

use App\Enums\PromotionTypeEnum;
use App\Http\Controllers\PromotionController;
use App\Rules\IsValidCountryRule;
use App\Rules\Seller\PromotionStartTimeRule;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    protected $hideLog = true;

    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'is_edit'                         => [
                'nullable',
                'in:true,false',
            ],
            'target_promotion_id'             => [
                'required_if:is_edit,true',
                'string',
            ],
            'type'                            => [
                'required',
                'string',
                'in:' . implode(',', PromotionTypeEnum::asArray()),
            ],
            'discount_code'                   => [
                'required',
                'string',
                Rule::unique('promotion_rule')->where(
                    function ($query) {
                        // if edit will ignore itself
                        if ($this->is_edit === 'true') {
                            $query->where('id', '!=', $this->target_promotion_id);
                        }
                        // each seller have unique
                        return $query->where('seller_id', currentUser()->getUserId());
                    }
                ),
            ],
            'start_time'                      => [
                'nullable',
                'date',
                new PromotionStartTimeRule($this->is_edit, $this->target_promotion_id),
            ],
            'end_time'                        => [
                'nullable',
                'date',
                'after:start_time',
            ],
            'minimum_requirement'             => [
                'nullable',
                'string',
                'in:none,amount,quantity',
            ],
            'minimum_requirement_value'       => [
                'integer',
                Rule::requiredIf(
                    function () {
                        return PromotionController::checkMinimumRequirement($this->minimum_requirement, $this->type);
                    }
                ),
                'min:1'
            ],
            'applies_to'                      => [
                'nullable',
                'string',
                'in:default,collection,store,campaign',
            ],
            'applies_to_value'                => [
                Rule::requiredIf(
                    function () {
                        return PromotionController::checkAppliesTo($this->applies_to, $this->type);
                    }
                ),
                'integer',
            ],

            // PERCENT_DISCOUNT
            'discount_percentage'             => [
                Rule::requiredIf(
                    function () {
                        return $this->type === PromotionTypeEnum::PERCENT_DISCOUNT;
                    }
                ),
                'integer',
                'min:1',
                'max:100',
            ],

            // FIXED_DISCOUNT
            'discount_amount'                 => [
                Rule::requiredIf(
                    function () {
                        return $this->type === PromotionTypeEnum::FIXED_DISCOUNT;
                    }
                ),
                'integer',
                'min:1',
            ],

            // FREE_SHIPPING
            'countries'                       => [
                Rule::requiredIf(
                    function () {
                        return $this->type === PromotionTypeEnum::FREE_SHIPPING;
                    }
                ),
                new IsValidCountryRule(),
                'string',
            ],

            // BUY_X_GET_Y
            'bxgy_get_quantity'               => [
                Rule::requiredIf(
                    function () {
                        return $this->type === PromotionTypeEnum::BUY_X_GET_Y;
                    }
                ),
                'integer',
                'min:1',
            ],
            'bxgy_get_discount_percentage'    => [
                Rule::requiredIf(
                    function () {
                        return $this->type === PromotionTypeEnum::BUY_X_GET_Y;
                    }
                ),
                'integer',
                'min:1',
                'max:100',
            ],
            'bxgy_get_applies_to'             => [
                'nullable',
                'string',
                'in:default,collection,store,product,campaign',
            ],
            'bxgy_get_applies_to_value'       => [
                Rule::requiredIf(
                    function () {
                        return $this->bxgy_get_applies_to
                            && $this->bxgy_get_applies_to !== 'default'
                            && $this->type === PromotionTypeEnum::BUY_X_GET_Y;
                    }
                ),
                'integer',
            ],

            // BUNDLE_DISCOUNT
            'bd_get_discount_percentage'      => [
                'nullable',
                'integer',
                'min:0',
                'max:100',
            ],
            'bd_get_applies_to'               => [
                'nullable',
                'string',
                'in:default,same_campaign,campaign,collection',
            ],
            'bd_get_applies_to_value'         => [
                Rule::requiredIf(
                    function () {
                        return $this->bd_get_applies_to
                            && !in_array(
                                $this->bd_get_applies_to,
                                [
                                    'default',
                                    'same_campaign',
                                ]
                            )
                            && $this->type === PromotionTypeEnum::BUNDLE_DISCOUNT;
                    }
                ),
                'integer',
            ],
            'bd_get_templates_to_offer'       => [
                'nullable',
                'string',
                'in:default,template',
            ],
            'bd_get_templates_to_offer_value' => [
                Rule::requiredIf(
                    function () {
                        return $this->bd_get_templates_to_offer
                            && $this->bd_get_templates_to_offer !== 'default'
                            && $this->type === PromotionTypeEnum::BUNDLE_DISCOUNT;
                    }
                ),
                'string',
            ],
            'bd_order_by'                     => [
                Rule::requiredIf(
                    function () {
                        return $this->type === PromotionTypeEnum::BUNDLE_DISCOUNT;
                    }
                ),
                'string',
                'in:auto,best_seller,newest',
            ],
            'bd_quantity'                     => [
                Rule::requiredIf(
                    function () {
                        return $this->type === PromotionTypeEnum::BUNDLE_DISCOUNT;
                    }
                ),
                'integer',
                'in:1,2,3',
            ],

            // TIERS_DISCOUNT
            'tiers'                           => [
                Rule::requiredIf(
                    function () {
                        return $this->type === PromotionTypeEnum::TIERS_DISCOUNT;
                    }
                ),
                'array',
            ],
            'tiers.*.quantity'                => [
                'required_with:tiers',
                'integer',
                'min:1',
            ],
            'tiers.*.discount'                => [
                'required_with:tiers',
                'integer',
                'min:1',
                'max:100',
            ],
        ];
    }

    protected function prepareForValidation(): void
    {
        // convert to array to validate child
        if ($this->has('tiers')) {
            $this->merge(
                [
                    'tiers' => json_decode($this->tiers, true),
                ]
            );
        }
    }
}

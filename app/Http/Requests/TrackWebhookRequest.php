<?php

namespace App\Http\Requests;

use App\Enums\TrackingServiceEnum;
use Illuminate\Foundation\Http\FormRequest;

class TrackWebhookRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'source' => 'required|string|in:' . implode(',', TrackingServiceEnum::asArray()),
            'data' => 'required|array',
        ];
    }
}

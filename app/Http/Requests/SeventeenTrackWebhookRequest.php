<?php

namespace App\Http\Requests;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SeventeenTrackWebhookRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'event' => 'required|string',
            'data' => 'required|array',
            'data.number' => 'required|string',
            'data.track_info.latest_status.status' => Rule::requiredIf($this->input('event') === 'TRACKING_UPDATED')
        ];
    }

    /**
     * @param $sign
     * @param $data
     * @return bool
     */
    public static function verifySignature($sign, $data): bool
    {
        try {
            if (empty($sign)) {
                return false;
            }
            $originalSign = $data . '/' . config('services.tracking.17track.secret_key');
            return $sign === hash('sha256', $originalSign);
        } catch (\Exception $e) {
            logToDiscord('[17Track] Failed to verify signature: ' . $sign . ', message: ' . $e->getMessage(), 'tracking_status_logs');
            return false;
        }
    }
}

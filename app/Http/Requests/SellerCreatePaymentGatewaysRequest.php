<?php

namespace App\Http\Requests;

use App\Enums\CurrencyEnum;
use App\Enums\PaymentGatewayStatusEnum;
use App\Enums\PaymentMethodEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SellerCreatePaymentGatewaysRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $rules = [
            'gateway' => ['required', 'string', Rule::in([
                PaymentMethodEnum::STRIPE,
                PaymentMethodEnum::PAYPAL,
            ])],
            'active' => ['required', Rule::in(PaymentGatewayStatusEnum::getValues())],
            'account_id' => ['required', 'email'],
            'store_id' => ['nullable', 'integer'],
            'checkout_domain' => ['nullable', 'integer'],
            'enable_card' => [
                'nullable',
                'integer',
            ]
        ];

        // when updating the payment gateway, $this->input() is empty,
        // so we need to check request() as well
        if (empty($this->input('paypal_merchant_id')) && empty(request('paypal_merchant_id'))) {
            $rules = array_merge($rules, [
                'config' => ['required', 'array'],
                'config.clientId' => ['required_if:gateway,paypal', 'string'],
                'config.secret' => ['required_if:gateway,paypal', 'string'],
            ]);
        }

        $rulesForStripe = [
            'config' => ['required', 'array'],
            'config.secret_key' => ['required_if:gateway,stripe', 'string'],
            'config.publishable_key' => ['required_if:gateway,stripe', 'string'],
            'options' => [
                'array',
            ],
            'options.other_payments' => [
                'required_if:gateway,stripe',
                'boolean',
            ],
            "options.crypto_payments" => [
                'nullable',
                'boolean',
            ],
            'options.klarna_currency' => [
                'nullable',
                Rule::in([
                    CurrencyEnum::EUR,
                    CurrencyEnum::USD,
                ]),
            ],
            'statement_descriptor_prefix' => [
                'nullable',
                'string',
                'min:2',
                'max:10',
            ],
            'weight' => [
                'nullable',
                'integer',
                'min:0',
                'max:100',
            ],
            'sale_limit' => [
                'nullable',
                'integer',
                'min:0',
            ],
        ];

        if ($this->input('gateway') === PaymentMethodEnum::STRIPE || request('gateway') === PaymentMethodEnum::STRIPE) {
            return array_merge($rules, $rulesForStripe);
        }

        return $rules;
    }
}

<?php

namespace App\Http\Controllers;

use App\Actions\Commons\RejectOrdersAction;
use App\Enums\ChargeOrderTypeEnum;
use App\Enums\CheckScamStatusEnum;
use App\Enums\CustomOptionTypeEnum;
use App\Enums\DateRangeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderAssigneeEnum;
use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderExportStatus;
use App\Enums\OrderFraudStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderSupportStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentGatewayRefundStatusEnums;
use App\Enums\PaymentMethodEnum;
use App\Enums\PersonalizedType;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductPrintType;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Enums\SellerHistoryActionEnum;
use App\Enums\SellerTeamRoleEnum;
use App\Enums\SendMail\LogStatus as SendMailLogStatus;
use App\Enums\SendMail\Template;
use App\Enums\StorageDisksEnum;
use App\Enums\TradeMarkStatusEnum;
use App\Enums\UserStatusEnum;
use App\Events\KlaviyoOrderCancelled;
use App\Events\KlaviyoOrderRefunded;
use App\Events\OrderPaymentCompleted;
use App\Events\OrderRefundedEvent;
use App\Events\OrderUpdated;
use App\Exports\Admin\Orders\NoShipOrderExporter;
use App\Exports\Admin\Orders\OrderApproved;
use App\Exports\Admin\Orders\OrderResumed;
use App\Exports\ExportCustomOrderToExcel;
use App\Exports\ExportFulfillOrderProductToExcel;
use App\Exports\ExportFulfillOrderSummaryToExcel;
use App\Exports\ExportOrderToExcel;
use App\Http\Controllers\Storefront\PaypalController;
use App\Http\Controllers\Storefront\StripeController;
use App\Http\Requests\Admin\AdminCreateChargeOrderRequest;
use App\Http\Requests\Admin\Order\UpdateNoteRequest;
use App\Http\Requests\Admin\Order\UpdateSupportRequest;
use App\Http\Requests\BulkUpdateFraudStatusRequest;
use App\Http\Requests\BulkUpdateTrademarkStatusRequest;
use App\Http\Requests\Order\ExportOrderRequest;
use App\Http\Requests\Order\UpdateOrderRequest;
use App\Http\Requests\RefundOrderRequest;
use App\Http\Requests\SellerImportFulfillOrdersRequest;
use App\Http\Requests\SellerImportOrderDesignRequest;
use App\Http\Requests\UpdateFraudStatusRequest;
use App\Imports\OrderDesignImport;
use App\Imports\SellerFulfillOrders\ImportCSVMultiPlatform;
use App\Jobs\OrderCostStatisticsJob;
use App\Jobs\ProcessRefundJob;
use App\Jobs\ScanCompleteOrderPaymentJob;
use App\Jobs\SendBuyerOrderConfirmationJob;
use App\Jobs\SendOrderToPBJob;
use App\Jobs\SendSellerBalanceUpdateNotification;
use App\Jobs\UpdateSupport;
use App\Models\Campaign;
use App\Models\Customer;
use App\Models\CustomerAddress;
use App\Models\Design;
use App\Models\DeviceInfo;
use App\Models\Elastic;
use App\Models\File;
use App\Models\IpInfo;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\OrderExport;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\OrderProductLog;
use App\Models\PaymentGateway;
use App\Models\PaymentGatewayRefund;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductVariant;
use App\Models\SellerBilling;
use App\Models\SellerCustomer;
use App\Models\SellerHistory;
use App\Models\SendMailLog;
use App\Models\Staff;
use App\Models\User;
use App\Rules\CheckExistsIdRule;
use App\Services\OrderService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use App\Traits\Encrypter;
use App\Traits\ScopeFilterSupplierDashboardTrait;
use App\Traits\SetOrderCustomerInfo;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\OrderService\Jobs\DatabaseSync\SyncOrderToRegion;
use Modules\OrderService\Models\RegionOrderHistory;
use Modules\OrderService\Models\RegionOrderProducts;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Services\RegionOrderService;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;
use RuntimeException;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Throwable;

class OrderController extends Controller
{
    use ApiResponse, Encrypter, ScopeFilterSupplierDashboardTrait, SetOrderCustomerInfo;

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'direction' => ['nullable', 'string', 'in:asc,desc']
        ]);

        $auth = currentUser();
        $auth->hasPermissionOrAbort('get_orders');

        $query = Order::query();
        $query->select(
            [
                'order.seller_id',
                'order.customer_id',
                'order.customer_email',
                'order.customer_name',
                'order.store_domain',
                'order.fraud_status',
                'order.fulfill_status',
                'order.id',
                'order.order_number',
                'order.order_number_2',
                'order.paid_at',
                'order.payment_method',
                'order.shipping_method',
                'order.type',
                'order.status',
                'order.total_amount',
                'order.tip_amount',
                'order.insurance_fee',
                'order.total_quantity',
                'order.total_seller_profit',
                'order.processing_fee',
                'order.total_fulfill_fee',
                'order.fulfill_fee_paid',
                'order.created_at',
                'order.updated_at',
                'order.session_id',
                'order.country',
                'order.transaction_id',
                'order.personalized',
                'order.device',
                'order.device_detail',
                'order.address_verified',
                'order.fulfill_log',
                'order.order_note',
                'order.payment_log',
                'order.payment_status',
                'order.ad_campaign',
                'order.flag_log',
                'order.payment_gateway_id',
            ]
        );

        $query->excludeTest();

        $keywords = $request->get('q');
        $status = $request->get('status');
        $type = $request->get('type', OrderTypeEnum::REGULAR);
        $isShowOnlyFBA = $request->boolean('only_fba');
        if ($isShowOnlyFBA) {
            $type = OrderTypeEnum::FBA;
        }
        $fraudStatus = $request->get('fraud_status');
        $isFraud = $request->boolean('is_fraud');
        $fulfillStatus = $request->get('fulfill_status');
        $paymentMethod = $request->get('payment_method');
        $dateRange = $request->get('time');
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $sortBy = $request->get('sort');
        $country = $request->get('country');
        $sortDirection = $request->get('direction');
        $sellerId = $request->query('seller_id');
        $campaignId = $request->query('campaign_id');
        $storeId = $request->query('store_id');
        $shippingMethod = $request->get('shipping_method');
        $sortableFields = ['total_amount', 'total_quantity', 'updated_at', 'paid_at', 'exported_at', 'id', 'created_at'];
        $trademarkStatus = $request->query('tm_status');
        $supportStatus = $request->query('support_status', OrderSupportStatusEnum::ALL);
        $assignee = (int) $request->query('assignee', OrderAssigneeEnum::ALL);
        $custom_store = $request->boolean('custom_store');
        $seller_paid = $request->query('seller_paid');
        $supplier_id = $request->query('supplier_id');
        $payment_gateway_id = $request->query('payment_gateway_id');
        $gatewayType = $request->query('gateway_type');
        $cp_invalid_address_order = $request->boolean('cp_invalid_address_order');
        $is_invalid_address = $request->boolean('is_invalid_address');
        $is_cancel_order = $request->boolean('is_cancel_order');
        $pendingProfit = $request->boolean('pending_profit');

        $userId = $auth->isAdmin() ? User::SENPRINTS_SELLER_ID : $auth->getUserId();
        if (($cp_invalid_address_order || $is_cancel_order) && $auth->isSeller() && empty($auth->getInfoAccess()->custom_payment)) {
            return $this->errorResponse('You do not have permission to view this page', 403);
        }

        $query->with(['order_products' => static function ($q) use ($userId, $auth) {
            $q->select(
                [
                    'id',
                    'order_id',
                    'campaign_title',
                    'thumb_url',
                    'product_name',
                    'product_url',
                    'personalized',
                    'full_printed',
                    'custom_options',
                    'color',
                    'tm_status'
                ]
            );
            if ($auth->isAdmin()) {
                $q->addSelect([
                    'supplier_id',
                    'supplier_name',
                    'fulfill_order_id',
                ]);
            }
            $q->when($userId !== User::SENPRINTS_SELLER_ID, function ($q) use ($userId) {
                $q->where('seller_id', $userId);
            })->orderBy('id');
        }]);

        if ($auth->isAdmin()) {
            $query->with('payment_gateway:id,name,account_id,seller_id');
            $query->join('user', 'user.id', '=', 'order.seller_id');
            $query->addSelect([
                'user.name as seller_name',
                'user.email as seller_email',
                'order.admin_note',
                'order.support_status',
                'order.assignee',
                'order.tm_status',
                'order.sen_fulfill_status',
                'order.ip_address',
                'order.ip_location',
                'order.device_id',
            ]);

            if ($sellerId) {
                $query->where('order.seller_id', $sellerId);
            }

            if ($trademarkStatus) {
                $query->where('order.tm_status', $trademarkStatus);
            }

            $query->filterSupport($supportStatus, $assignee);
        } else {
            $query->where('order.seller_id', $userId);
        }
        if (!empty($gatewayType)) {
            switch (strtolower($gatewayType)) {
                case 'senprints':
                    $query->whereHas('payment_gateway', function ($q) {
                        $q->whereNull('seller_id');
                    });
                    break;
                case 'custom':
                    $query->whereHas('payment_gateway', function ($q) {
                        $q->whereNotNull('seller_id');
                    });
                    break;
                case 'paypal':
                case 'stripe':
                    $query->whereHas('payment_gateway', function ($q) use ($gatewayType) {
                        $q->where('gateway', strtolower($gatewayType));
                    });
                    break;
            }
        }
        if (!empty($fraudStatus)) {
            $query->where('order.fraud_status', $fraudStatus);
        }

        $query->when(empty($keywords) && ($isFraud || $fraudStatus === OrderFraudStatus::TRUSTED), function ($q) {
            $q->whereNotNull('order.flag_log');
        });

        $seller = User::whereId($userId)->first();
        $is_user_custom_payment = optional($seller)->custom_payment === 1;
        if (!empty($storeId)) {
            $query->where('order.store_id', $storeId);
        }

        if ($is_user_custom_payment) {
            $payment_gateways_filter = $this->getFilterPaymentGateways();
        }

        if (!empty($payment_gateway_id) && $is_user_custom_payment) {
            $query->where('order.payment_gateway_id', $payment_gateway_id);
        }

        if (!empty($custom_store)) {
            $query->where('order.type', OrderTypeEnum::CUSTOM);
        }

        if ($seller_paid === 'yes') {
            $query->where('order.sen_fulfill_status', OrderSenFulfillStatus::YES);
        }

        if ($seller_paid === 'no') {
            $query->where('order.sen_fulfill_status', '!=', OrderSenFulfillStatus::YES);
        }

        if ($auth->isSeller()) {
            $store_ids = get_team_seller_stores($auth->getUserId(), $auth->getAuthorizedAccountId());
            if (!empty($store_ids)) {
                $query->whereIn('order.store_id', $store_ids);
            }
        }

        if ($cp_invalid_address_order || $is_cancel_order) {
            $fulfillStatus = OrderFulfillStatus::ON_HOLD;
        }

        if (!empty($fulfillStatus)) {
            if ($fulfillStatus === OrderFulfillStatus::ON_HOLD) {
                $query->whereNotIn('order.fulfill_status', [OrderFulfillStatus::PROCESSING, OrderFulfillStatus::ON_DELIVERY, OrderFulfillStatus::FULFILLED]);
            }
            $query->where(function ($query) use ($fulfillStatus, $is_invalid_address) {
                $query->when($is_invalid_address, function ($query) {
                    $query->where('order.address_verified', OrderAddressVerifiedEnum::INVALID);
                    $query->where('order.status', OrderStatus::PROCESSING);
                    $query->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM]);
                    $query->whereHas('order_products', function ($q) {
                        return $q->whereNotNull(['product_id', 'campaign_id']);
                    });
                }, function ($query) use ($fulfillStatus) {
                    $query->when($fulfillStatus === OrderFulfillStatus::ON_HOLD, function ($query) {
                        $query->where('order.status', OrderStatus::PROCESSING);
                    });
                    $query->where(function ($query) use ($fulfillStatus) {
                        $query->where(function ($query) use ($fulfillStatus) {
                            $query->where('order.fulfill_status', $fulfillStatus)->orWhereHas('order_products', function ($q) use ($fulfillStatus) {
                                return $q->where('fulfill_status', $fulfillStatus);
                            });
                        });
                        $query->when($fulfillStatus === OrderFulfillStatus::ON_HOLD, function ($query) {
                            $query->orWhere(function ($query) {
                                $query->where('order.address_verified', OrderAddressVerifiedEnum::INVALID);
                                $query->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM]);
                                $query->whereHas('order_products', function ($q) {
                                    return $q->whereNotNull(['product_id', 'campaign_id']);
                                });
                            });
                        });
                    });
                });
            });
            if ($cp_invalid_address_order) {
                $query->invalidAddressOrderConditions();
            }

            if ($is_cancel_order) {
                $query->customerCancelOrderConditions();
            }

            if ($fulfillStatus === OrderFulfillStatus::ON_HOLD) {
                $query->whereIn('order.payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                    ->where('order.total_paid', '>', 0)
                    ->whereNotNull('order.paid_at');
            }
        }

        if (!empty($paymentMethod)) {
            $query->where('order.payment_method', $paymentMethod);
        }

        if (!empty($country)) {
            $query->where('order.country', $country);
        }

        if (!empty($shippingMethod)) {
            $query->where('order.shipping_method', $shippingMethod);
        }

        // get all orders of campaign (for Admin Dashboard)
        if ($campaignId) {
            $query->whereHas('products', function (Builder $q) use ($campaignId) {
                $q->where('campaign_id', $campaignId);
            });
        }

        if (!empty($supplier_id)) {
            $query->whereHas('order_products', function (Builder $q) use ($supplier_id) {
                $q->where('supplier_id', $supplier_id);
            });
        }

        if ($pendingProfit && ($auth->isAdmin() || empty($auth->getInfoAccess()->custom_payment))) {
            $holdHours = UserService::calculateHoldHours($auth->getInfoAccess());
            $query->where(function ($q) use ($userId, $holdHours) {
                $q->where(function ($q) use ($userId, $holdHours) {
                    $q->whereHas('sellerBilling', function ($q) use ($userId, $holdHours) {
                        $q->where([
                            'seller_id' => $userId,
                            'type' => SellerBillingType::COMMISSION
                        ]);
                        $q->where('created_at', '>=', now()->subHours($holdHours));
                    });
                    $q->whereNot(function ($q) {
                        $q->where(function ($q) {
                            $q->where('fraud_status', '!=', OrderFraudStatus::TRUSTED);
                            $q->orWhereIn('tm_status', [TradeMarkStatusEnum::FLAGGED, TradeMarkStatusEnum::VIOLATED]);
                        });
                        $q->where('type', OrderTypeEnum::REGULAR);
                        $q->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                        $q->where('paid_at', '>=', now()->subDays(90));
                    });
                    $q->whereNot(function ($q) {
                        $q->where('type', OrderTypeEnum::REGULAR);
                        $q->where('status', OrderStatus::PROCESSING);
                        $q->where('fulfill_status', OrderFulfillStatus::DESIGNING);
                        $q->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                        $q->where('paid_at', '>=', now()->subDays(5));
                    });
                });
                $q->orWhere(function ($q) {
                    $q->where(function ($q) {
                        $q->where('fraud_status', '!=', OrderFraudStatus::TRUSTED);
                        $q->orWhereIn('tm_status', [TradeMarkStatusEnum::FLAGGED, TradeMarkStatusEnum::VIOLATED]);
                    });
                    $q->where('type', OrderTypeEnum::REGULAR);
                    $q->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                    $q->where('paid_at', '>=', now()->subDays(90));
                });
                $q->orWhere(function ($q) {
                    $q->where('type', OrderTypeEnum::REGULAR);
                    $q->where('status', OrderStatus::PROCESSING);
                    $q->where('fulfill_status', OrderFulfillStatus::DESIGNING);
                    $q->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED]);
                    $q->where('paid_at', '>=', now()->subDays(5));
                });
            });
        }

        $excludeStatus = [
            OrderStatus::DRAFT,
            OrderStatus::DELETED,
        ];
        if ($type === OrderTypeEnum::FULFILLMENT) {
            $excludeStatus = [OrderStatus::DELETED];
        }

        if ($status !== OrderStatus::PENDING && ($type === OrderTypeEnum::REGULAR || $type === OrderTypeEnum::CUSTOM)) {
            $excludeStatus[] = OrderStatus::PENDING;
        }

        if ($fulfillStatus && in_array($fulfillStatus, [OrderFulfillStatus::DESIGNING, OrderFulfillStatus::REVIEWING], true) && $auth->isSeller()) {
            $excludeStatus[] = OrderStatus::CANCELLED;
            $excludeStatus[] = OrderStatus::REFUNDED;
        }

        $ref = $request->get('ref');

        if ($ref === 'untracking') {
            $query->where('order.fulfill_status', '!=', OrderFulfillStatus::FULFILLED);
            $query->whereNotNull('order.export_id');
        }

        $query->whereNotIn('order.status', $excludeStatus);

        if (!empty($status) && $status !== 'all') {
            $query->where('order.status', $status);
        }
        $orderType = [];
        if ($type && in_array(strtolower($type), OrderTypeEnum::getValues(), true)) {
            if ($type === OrderTypeEnum::REGULAR) {
                $orderType = [
                    OrderTypeEnum::REGULAR,
                    OrderTypeEnum::CUSTOM,
                    OrderTypeEnum::SERVICE,
                ];
            } elseif ($type === OrderTypeEnum::FULFILLMENT) {
                $orderType = [
                    OrderTypeEnum::FULFILLMENT,
                    OrderTypeEnum::FBA,
                ];
            } else {
                $orderType = [$type];
            }
        }
        if ((int) $supportStatus === OrderSupportStatusEnum::WARNING_LARGE_QUANTITY_AMOUNT || (int) $supportStatus === OrderSupportStatusEnum::NEED_REASSIGN_SUPPLIER_MANUALLY) {
            $orderType[] = OrderTypeEnum::FULFILLMENT;
            $orderType[] = OrderTypeEnum::FBA;
        }
        if (!empty($orderType) && !$cp_invalid_address_order && !$is_cancel_order) {
            $query->whereIn('order.type', array_unique($orderType));
        }

        $query->filterDateRange($dateRange, $startDate, $endDate);

        if (!empty($keywords)) {
            if (is_numeric($keywords)) {
                if (strlen((string) $keywords) === 4) {
                    $query->where('last4_card_number', (int) $keywords);
                } else if ($auth->isSeller()){
                    $query->where(function ($q) use ($keywords) {
                        $q->where('order.order_number', 'like', "%-{$keywords}")
                            ->orWhere('order.order_number_2', 'like', "%-{$keywords}")
                            ->orWhere('order.id', (int) $keywords);
                    });
                } else {
                    $query->where('order.id', (int) $keywords);
                    $perPage = 1;
                }
            } elseif (validateEmail($keywords)) {
                $query->where('order.customer_email', $keywords);
            } elseif (preg_match('/^[a-zA-Z]+\d*-\d+$/', $keywords)) {
                $query->where(function ($q) use ($keywords) {
                    $q->where('order.order_number', $keywords)->orWhere('order.order_number_2', $keywords);
                });
            } else {
                $query = $query->where(function (Builder $q) use ($keywords) {
                    $exactMatchColumns = [
                        'order.order_number',
                        'order.order_number_2',
                        'order.transaction_id',
                        'order.access_token',
                        'order.ip_address',
                        'order.device_detail',
                        'order.device_id',
                    ];
                    foreach ($exactMatchColumns as $column) {
                        $q->orWhere($column, $keywords);
                    }
                    $q->orWhere('order.customer_name', 'like', "%{$keywords}%");
                    $q->orWhereHas('order_products', function (Builder $q) use ($keywords) {
                        $q->where('campaign_title', 'like', "%{$keywords}%");
                    });
                });
            }
        }

        if (!is_null($sortBy) && in_array($sortBy, $sortableFields)) {
            $query->orderBy($sortBy, $sortDirection);
        } elseif ($type === OrderTypeEnum::FULFILLMENT) {
            $query->orderByDesc('created_at');
        } else {
            $query->orderByDesc('paid_at');
        }

        $perPage ??= $request->query('per_page', 15);
        $result = $query->paginate((int)$perPage);
        $emails = array_unique(array_column($result->items(), 'customer_email'));
        $emails = array_filter($emails, static function ($email) {
            return !empty($email);
        });
        if (count($emails) > 200) {
            $customers = collect();
            foreach (array_chunk($emails, round($perPage / 2)) as $list) {
                $users = Customer::query()
                    ->select(['id', 'email'])
                    ->whereIn('email', $list)
                    ->get()->toArray();
                $customers = $customers->merge(!empty($users) ? $users : []);
            }
        } else {
            $customers = Customer::query()
                ->select(['id', 'email'])
                ->whereIn('email', $emails)
                ->get();
        }
        $customers = $customers->groupBy('email')->toArray();
        $result->getCollection()->transform(function ($order) use ($customers, $auth) {
            $order->order_products = $order->order_products->map(fn ($product) => self::setCustomOptions($product));
            $customer_id = $customers[$order->customer_email][0]['id'] ?? null;
            $customer_email = $order->customer_email;
            $customer_name = $order->customer_name;
            if ($auth->hasExactlyRole(SellerTeamRoleEnum::MANAGER_LITE)) {
                $customer_email = null;
                $order->customer_email = $customer_email;
                $order->status_url = null;
            }
            $order->customer = [
                'id' => $customer_id,
                'email' => $customer_email,
                'name' => $customer_name,
            ];
            return $order;
        });

        if ($type !== OrderTypeEnum::FULFILLMENT) {
            $response = json_decode(json_encode($result));
            $response->payment_gateways_filter = $payment_gateways_filter ?? null;
            return new JsonResponse($response);
        }
        // Get mockup / design for fulfill orders
        $orderIds = array_column($result->items(), 'id');
        $images = File::query()
            ->select([
                'id',
                'file_url',
                'file_url_2',
                'type',
                'order_id',
                'order_product_id'
            ])
            ->whereIn('order_id', $orderIds)
            ->get();
        $response = json_decode(json_encode($result));
        $response->images = $images;
        $response->payment_gateways_filter = $payment_gateways_filter ?? null;
        return new JsonResponse($response);
    }

    private function getFilterPaymentGateways()
    {
        $payment_gateways = PaymentGateway::query()
            ->select(['id', 'gateway', 'account_id'])
            ->where('seller_id', currentUser()->getUserId())
            ->get();

        if ($payment_gateways->isEmpty()) {
            return null;
        }

        $separator = '-';
        return $payment_gateways->transform(function ($gateway) use ($separator) {
            return [
                'label' => $gateway->id . $separator . $gateway->gateway . $separator . $gateway->account_id,
                'value' => $gateway->id,
            ];
        });
    }

    public function getResumed(Request $request)
    {
        $request->validate([
            'direction' => ['nullable', 'string', 'in:asc,desc']
        ]);

        $supportStatus = $request->query('support_status', OrderSupportStatusEnum::ALL);
        $fulfillStatuses = checkAndConvertToArray($request->input('array_fulfill_status', []));
        $assignee = (int) $request->query('assignee', OrderAssigneeEnum::ALL);
        $isExport = $request->boolean('export', false);

        $keywords = $request->get('q');
        $sortBy = $request->get('sort');
        $sortDirection = $request->get('direction');
        $dateRange = $request->get('time');
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $perPage = $request->query('per_page', 15);

        $query = Order::query()
            ->select([
                'order.id',
                'order.order_number',
                'order.country',
                'order.customer_email',
                'order.total_amount',
                'order.total_quantity',
                'order.type',
                'order.personalized',
                'order.address_verified',
                'order.status',
                'order.fulfill_status',
                'order.order_note',
                'order.admin_note',
                'order.support_status',
                'order.assignee',
                'order.updated_at',
                'order.fulfilled_at',
                'order.seller_id'
            ])
            ->excludeTest()
            ->filterSupport($supportStatus, $assignee)
            ->filterDateRange($dateRange, $startDate, $endDate, 'order.fulfilled_at');

        $query->where('order.fulfill_status', '!=', OrderFulfillStatus::ON_HOLD);
        if (!empty($fulfillStatuses)) {
            $query->whereIn('order.fulfill_status', $fulfillStatuses);
        }

        if (!empty($keywords)) {
            if (is_numeric($keywords)) {
                if (strlen((string) $keywords) === 4) {
                    $query->where('last4_card_number', (int) $keywords);
                } else {
                    $query->where('order.id', (int)$keywords);
                    $perPage = 1;
                }
            } elseif (validateEmail($keywords)) {
                $query->where('order.customer_email', $keywords);
            } else {
                $query = $query->where(function ($q) use ($keywords) {
                    $q->where('order.order_number', $keywords)
                        ->orWhere('order.order_number_2', $keywords)
                        ->orWhere('order.transaction_id', $keywords)
                        ->orWhere('order.access_token', $keywords)
                        ->orWhere('order.ip_address', $keywords)
                        ->orWhere('order.device_detail', $keywords)
                        ->orWhere('order.device_id', $keywords)
                        ->orWhere('order.customer_name', 'like', "%{$keywords}%");
                });
            }
        }

         $query
             ->whereHas('order_history', function ($q) {
                 $q->whereIn('action', [OrderHistoryActionEnum::RESUME_FULFILL, OrderHistoryActionEnum::FULFILLMENT_CREATED]);
             })
             ->whereHas('order_history', function ($q) {
                 $q->whereIn('action', [OrderHistoryActionEnum::HOLD_FULFILL, OrderHistoryActionEnum::REJECT_FULFILL]);
                 $q->orWhere(function ($q) {
                     $q->where('action', OrderHistoryActionEnum::CHANGE_SUPPORT_CATEGORY)->whereIn('support_status', ['WRONG_ADDRESS']);
                 });
                 $q->orWhere(function ($q) {
                     $q->where('action', OrderHistoryActionEnum::REVALIDATE_CUSTOMER_ADDRESS)->where('admin_detail', 'Customer address is invalid');
                 });
             })
             ->whereHas('order_products', function ($q) {
                 $q->whereNotIn('fulfill_status', [OrderProductFulfillStatus::REJECTED, OrderProductFulfillStatus::EXCEPTION]);
             })
             ->with(['latest_manual_resume_order_history' => function ($query) {
                 $query->select([
                     'order_id',
                     'action',
                     'created_at',
                     'updated_by'
                 ]);
             }])
             ->with(['latest_auto_resume_order_history' => function ($query) {
                 $query->select([
                     'order_id',
                     'action',
                     'created_at',
                     'updated_by'
                 ]);
             }])
            ->with('seller:id,name,email')
             ->when($isExport, function ($q) {
                 $q->with('staff');
             })
            ->with(['first_order_product'=> function ($query) {
                $query->select('order_id', 'thumb_url');
            }]);
            if (!empty($sortBy)) {
                $query->orderBy($sortBy, $sortDirection);
            } else {
                $query->orderByDesc('order.fulfilled_at');
            }
        $result = $isExport ? $query->get() : $query->paginate($perPage);

        $ordersCollection = $isExport ? $result : $result->getCollection();
        $emails = $ordersCollection->pluck('customer_email')->unique()->filter()->all();
        $staffEmails = $ordersCollection->pluck('latest_manual_resume_order_history.updated_by')->unique()->filter()->all();

        if (count($emails) > 200) {
            $customers = collect();
            foreach (array_chunk($emails, round($perPage / 2)) as $list) {
                $users = Customer::query()
                    ->select(['id', 'email'])
                    ->whereIn('email', $list)
                    ->get()->toArray();
                $customers = $customers->merge(!empty($users) ? $users : []);
            }
        } else {
            $customers = Customer::query()
                ->select(['id', 'email'])
                ->whereIn('email', $emails)
                ->get();
        }
        $customers = $customers->keyBy('email')->toArray();
        $staffs = Staff::query()
            ->select(['email', 'name'])
            ->whereIn('email', $staffEmails)
            ->get()
            ->keyBy('email')
            ->toArray();

        $ordersCollection->transform(function ($order) use ($customers, $staffs) {
            $order->customer = [
                'id' => $customers[$order->customer_email][0]['id'] ?? null,
                'email' => $order->customer_email,
                'name' => $order->customer_name,
            ];
            $order->resumed_at = !empty($order->latest_manual_resume_order_history) ? $order->latest_manual_resume_order_history->created_at : optional($order->latest_auto_resume_order_history)->created_at;
            $order->resumed_by = !empty($order->latest_manual_resume_order_history) ? $staffs[$order->latest_manual_resume_order_history->updated_by]['name'] : "System";
            $order->thumb_url = $order->first_order_product->thumb_url ?? null;
            unset($order->first_order_product, $order->latest_manual_resume_order_history, $order->latest_auto_resume_order_history);
            return $order;
        });

        if ($isExport) {
            return Excel::download(new OrderResumed($ordersCollection), 'orders.csv');
        }

        return $this->successResponse($result->setCollection($ordersCollection));
    }

    public function getApproved(Request $request)
    {
        $request->validate([
            'direction' => ['nullable', 'string', 'in:asc,desc']
        ]);

        $supportStatus = $request->query('support_status', OrderSupportStatusEnum::ALL);
        $fulfillStatuses = checkAndConvertToArray($request->input('array_fulfill_status', []));
        $assignee = (int) $request->query('assignee', OrderAssigneeEnum::ALL);
        $approvedBy = (int) $request->query('approved_by');
        $isExport = $request->boolean('export');

        $keywords = $request->get('q');
        $sortBy = $request->get('sort');
        $sortDirection = $request->get('direction');
        $dateRange = $request->get('time');
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $perPage = $request->query('per_page', 15);

        $query = Order::query()
            ->select([
                'order.id',
                'order.order_number',
                'order.country',
                'order.customer_email',
                'order.total_amount',
                'order.total_quantity',
                'order.type',
                'order.personalized',
                'order.address_verified',
                'order.status',
                'order.fulfill_status',
                'order.order_note',
                'order.admin_note',
                'order.support_status',
                'order.assignee',
                'order.updated_at',
                'order.fulfilled_at',
                'order.approved_at',
                'order.seller_id'
            ])
            ->excludeTest()
            ->filterSupport($supportStatus, $assignee)
            ->filterDateRange($dateRange, $startDate, $endDate, 'order.approved_at');

        if (!empty($fulfillStatuses)) {
            $query->whereIn('order.fulfill_status', $fulfillStatuses);
        }

        if (!empty($keywords)) {
            if (is_numeric($keywords)) {
                if (strlen((string) $keywords) === 4) {
                    $query->where('last4_card_number', (int) $keywords);
                } else {
                    $query->where('order.id', (int)$keywords);
                    $perPage = 1;
                }
            } elseif (validateEmail($keywords)) {
                $query->where('order.customer_email', $keywords);
            } else {
                $query = $query->where(function ($q) use ($keywords) {
                    $q->where('order.order_number', $keywords)
                        ->orWhere('order.order_number_2', $keywords)
                        ->orWhere('order.transaction_id', $keywords)
                        ->orWhere('order.access_token', $keywords)
                        ->orWhere('order.ip_address', $keywords)
                        ->orWhere('order.device_detail', $keywords)
                        ->orWhere('order.device_id', $keywords)
                        ->orWhere('order.customer_name', 'like', "%{$keywords}%");
                });
            }
        }

        $approved_email = null;
        if ($approvedBy > 0) {
            $approved_email = Staff::query()->select(['email'])->where('id', $approvedBy)->value('email');
        }

        $query->whereHas('latest_order_history', function ($q) use ($approved_email) {
            $q->where('action', OrderHistoryActionEnum::FULFILL_UPDATED)->where('admin_detail', 'like', '%updated order is valid');
            $q->when($approved_email, function ($q) use ($approved_email) {
                $q->where('updated_by', $approved_email);
            });
        })->whereHas('order_products', function ($q) {
            $q->whereNotIn('fulfill_status', [OrderProductFulfillStatus::REJECTED, OrderProductFulfillStatus::EXCEPTION]);
        })->with(['latest_order_history' => function ($q) {
            $q->where('action', OrderHistoryActionEnum::FULFILL_UPDATED)->where('admin_detail', 'like', '%updated order is valid');
        }])->with('seller:id,name,email')->when($isExport, function ($q) {
            $q->with('staff');
        })->with(['first_order_product' => function ($query) {
            $query->select('order_id', 'thumb_url');
        }]);
        if (!empty($sortBy)) {
            $query->orderBy($sortBy, $sortDirection);
        } else {
            $query->orderByDesc('order.approved_at');
        }
        $result = $isExport ? $query->get() : $query->paginate($perPage);

        $ordersCollection = $isExport ? $result : $result->getCollection();
        $emails = $ordersCollection->pluck('customer_email')->unique()->filter()->all();
        $staffEmails = $ordersCollection->pluck('latest_order_history.updated_by')->unique()->filter()->all();

        if (count($emails) > 200) {
            $customers = collect();
            foreach (array_chunk($emails, round($perPage / 2)) as $list) {
                $users = Customer::query()
                    ->select(['id', 'email'])
                    ->whereIn('email', $list)
                    ->get()->toArray();
                $customers = $customers->merge(!empty($users) ? $users : []);
            }
        } else {
            $customers = Customer::query()
                ->select(['id', 'email'])
                ->whereIn('email', $emails)
                ->get();
        }
        $customers = $customers->keyBy('email')->toArray();
        $staffs = Staff::query()
            ->select(['email', 'name'])
            ->whereIn('email', $staffEmails)
            ->get()
            ->keyBy('email')
            ->toArray();
        $ordersCollection->transform(function ($order) use ($customers, $staffs) {
            $order->customer = [
                'id' => $customers[$order->customer_email][0]['id'] ?? null,
                'email' => $order->customer_email,
                'name' => $order->customer_name,
            ];
            $order->updated_by = $order->latest_order_history ? $staffs[$order->latest_order_history->updated_by]['name'] : "System";
            $order->thumb_url = $order->first_order_product->thumb_url ?? null;
            return $order;
        });

        if ($isExport) {
            return Excel::download(new OrderApproved($ordersCollection), 'orders_' . time() . '.csv');
        }
        return $this->successResponse($result->setCollection($ordersCollection));
    }

    public function supplierGetOrders(Request $request): JsonResponse
    {
        $keywords = $request->get('q');
        $fulfillStatuses = checkAndConvertToArray($request->input('array_fulfill_status'));
        $perPage = $request->get('per_page', '15');
        $dateRange = $request->get('time');
        $startDate = $request->get('start_date', '');
        $endDate = $request->get('end_date', '');
        $isGetAll = false;

        switch ($dateRange) {
            case DateRangeEnum::TODAY:
                $startDate = now()->toDateString();
                $endDate = now()->toDateString();
                break;
            case DateRangeEnum::YESTERDAY:
                $startDate = now()->subDay()->toDateString();
                $endDate = now()->subDay()->toDateString();
                break;

            case DateRangeEnum::LAST_7_DAYS:
                $startDate = now()->subDays(7)->toDateString();
                $endDate = now()->toDateString();
                break;
            case DateRangeEnum::LAST_WEEK:
                $startDate = now()->subWeek()->startOfWeek();
                $endDate = now()->subWeek()->endOfWeek();
                break;
            case DateRangeEnum::THIS_WEEK:
                $startDate = now()->startOfWeek();
                $endDate = now()->endOfWeek();
                break;

            case DateRangeEnum::THIS_MONTH:
                $startDate = now()->startOfMonth();
                $endDate = now()->endOfMonth();
                break;

            case DateRangeEnum::LAST_MONTH:
                $startDate = now()->subMonth()->startOfMonth();
                $endDate = now()->subMonth()->endOfMonth();
                break;
            case DateRangeEnum::LAST_30_DAYS:
                $startDate = now()->subDays(30)->toDateString();
                $endDate = now()->toDateString();
                break;
            case DateRangeEnum::CUSTOM:
                $startDate = Carbon::parse(trim($startDate, "'"));
                $endDate = Carbon::parse(trim($endDate, "'"));
                break;
            default:
                $startDate = now()->subMonth()->startOfMonth();
                $endDate = now()->endOfMonth();
                $isGetAll = true;
                break;
        }

        if (count($fulfillStatuses) === 0) {
            $fulfillStatuses = Order::SUPPLIER_ORDER_STATUS;
        }
        $supplierId = currentUser()->getUserId();
        $query = Order::query()->with(['order_products' => function ($query) use ($supplierId, $fulfillStatuses, $startDate, $endDate, $isGetAll) {
            $query->select([
                'id',
                'order_id',
                'thumb_url',
                'product_name',
                'product_url',
                'custom_options',
                'color',
                'supplier_id',
                'fulfill_order_id',
                'personalized',
                'tracking_code',
                'tracking_url',
                'quantity',
                'order_product.fulfill_status',
                'options',
                'product_id',
                'template_id',
                'supplier_exported_at',
                'assigned_supplier_at',
                'barcode',
                'full_printed',
                'total_reprint'
            ])->with('template:id,thumb_url,options,print_spaces,name')
            ->where('supplier_id', $supplierId)
            ->whereIn('order_product.fulfill_status', $fulfillStatuses);
            $this->filterQueryDateSupplierOrderProduct($query, $startDate, $endDate, $isGetAll);
            $this->filterCancelledSupplierOrderProduct($query);
        }])
        ->whereHas('order_products', function ($query) use ($supplierId, $fulfillStatuses, $startDate, $endDate, $isGetAll) {
            $query->where('supplier_id', $supplierId)->whereIn('fulfill_status', $fulfillStatuses);
            $this->filterQueryDateSupplierOrderProduct($query, $startDate, $endDate, $isGetAll);
            $this->filterCancelledSupplierOrderProduct($query);
            $query->whereNotNull('supplier_id');
            $query->whereIn('tm_status', [TradeMarkStatusEnum::UNVERIFIED, TradeMarkStatusEnum::VERIFIED, TradeMarkStatusEnum::FLAGGED]);
        });

        $query->select([
            'order.fulfill_status',
            'order.id',
            'order.order_number',
            'order.order_number_2',
            'order.total_quantity',
            'order.created_at',
            'order.updated_at',
            'order.address',
            'order.address_2',
            'order.city',
            'order.state',
            'order.postcode',
            'order.country',
            'order.customer_name',
            'order.customer_email',
            'order.customer_phone',
            'order.order_note',
            'order.shipping_label',
            'order.status'
        ]);
        $query->excludeTest()
          ->whereIn('order.status', OrderStatus::listingStatusForSupplier())
          ->whereIn('order.fulfill_status', Order::SUPPLIER_ORDER_STATUS);
        $this->filterUnfulfilledSupplierOrder($query);
        $query->where('order.sen_fulfill_status', OrderSenFulfillStatus::YES);
        $query->where(function ($query) {
            $query->whereDoesntHave('request_cancel')->orWhereHas('request_cancel', function ($query) {
                $query->whereNotIn('status', [OrderCancelRequestStatus::PROCESSING, OrderCancelRequestStatus::CONFIRMED, OrderCancelRequestStatus::COMPLETED]);
            });
        });

        if (!empty($keywords)) {
            if (is_numeric($keywords)) {
                $query->where('order.id', (int)$keywords);
                $perPage = 1;
            } else {
                $query->where(function ($query) use ($keywords) {
                    $query->where('order.order_number', $keywords)
                        ->orWhere('order.order_number_2', $keywords);
                });
            }
        }
        $result = $query
            ->orderByRaw("CASE
                WHEN fulfill_status = '" . OrderProductFulfillStatus::UNFULFILLED . "' THEN 1
                WHEN fulfill_status = '" . OrderProductFulfillStatus::PROCESSING . "' THEN 2
                WHEN fulfill_status = '" . OrderProductFulfillStatus::ON_DELIVERY . "' THEN 3
                WHEN fulfill_status = '" . OrderProductFulfillStatus::FULFILLED . "' THEN 4
                ELSE 5 END, updated_at DESC
            ")->paginate((int)$perPage);

        $result->getCollection()->map(function ($order) {
            $order->order_products->map(function ($product) {
                $product->assigned_supplier_at = $product->getAssignedSupplierAt();
                if ($product->isNoNeedDesignProductType()) {
                    $product->thumb_url = $product->thumb_url ?? $product->template->thumb_url;
                }
                $product = self::setCustomOptions($product);
                unset($product->order);
                return $product;
            });
            return $order;
        });
        $response = json_decode(json_encode($result));
        $response->countOrderProduct = $this->countOrderProductByStatus($supplierId, $startDate, $endDate, $isGetAll);
        return new JsonResponse($response);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function supplierRejectOrderProducts(Request $request): JsonResponse
    {
        $request->validate([
            'product_ids' => 'required|array|min:1',
            'fulfill_status' => 'required|string',
        ]);
        $productIds = $request->input('product_ids');
        $fulfillStatus = $request->input('fulfill_status');
        if ($fulfillStatus !== OrderProductFulfillStatus::REJECTED) {
            return $this->errorResponse('Invalid fulfill status');
        }
        $supplierId = currentUser()->getUserId();
        try {
            $order_products = OrderProduct::query()
                ->where('supplier_id', $supplierId)
                ->whereIn('id', $productIds)
                ->get();
            if ($order_products->isNotEmpty()) {
                $orderProductIds = $order_products->pluck('id')->toArray();
                $orderIds = $order_products->pluck('order_id')->toArray();
                /** @var RejectOrdersAction $action */
                $action = app(RejectOrdersAction::class);
                return $this->successResponse($action->handle($orderIds, $orderProductIds));
            }
            return $this->errorResponse('Not found order product');
        } catch (Throwable $e) {
            return $this->errorResponse('Bulk update status failed');
        }
    }

    private function countOrderProductByStatus($supplierId, $startDate, $endDate, $isGetAll)
    {
        $status = Order::SUPPLIER_ORDER_STATUS;
        $querySelectRaw = 'COUNT(*) all_order_products,';
        foreach ($status as $index => $st) {
            $querySelectRaw .= 'SUM(order_product.fulfill_status = "' . $st . '") as "' . $st . '"';

            if ($index !== count($status) - 1) {
                $querySelectRaw .= ', ';
            }
        }
        $query = OrderProduct::query()->selectRaw($querySelectRaw)->where('order_product.supplier_id', $supplierId)->whereIn('order_product.fulfill_status', $status);
        $this->filterQueryDateSupplierOrderProduct($query, $startDate, $endDate, $isGetAll);
        $query->join('order', 'order.id', '=', 'order_product.order_id');
        $query->where(function ($query) {
            $query->where(function ($q) {
                $q->where('order.customer_email', '!=', '<EMAIL>');
                $q->Where('order.customer_email', '!=', '<EMAIL>');
                $q->orWhereNull('order.customer_email');
            });
        })
        ->whereNotNull('order_product.supplier_id')
        ->whereIn('order_product.tm_status', [TradeMarkStatusEnum::UNVERIFIED, TradeMarkStatusEnum::VERIFIED, TradeMarkStatusEnum::FLAGGED])
        ->where('order.sen_fulfill_status', OrderSenFulfillStatus::YES)
        ->whereIn('order.fulfill_status', $status)
        ->whereIn('order.status', OrderStatus::listingStatusForSupplier());
        $this->filterUnfulfilledSupplierOrder($query);
        $query->leftJoin('order_cancel_request', 'order_cancel_request.order_id', '=', 'order.id');
        $query->where(function ($query) {
            $query->whereNull('order_cancel_request.id')->orWhereNotIn('order_cancel_request.status', [OrderCancelRequestStatus::PROCESSING, OrderCancelRequestStatus::CONFIRMED, OrderCancelRequestStatus::COMPLETED]);
        });
        return $this->filterCancelledSupplierOrderProduct($query)->first();
    }

    public static function setCustomOptions($product)
    {
        if (!empty($product->custom_options) && ($product->personalized === PersonalizedType::CUSTOM_OPTION || $product->isNoNeedDesignProductType())) {
            $groups = json_decode($product->custom_options, true) ?? [];
            foreach ($groups as $kG => $group) {
                if (isset($group['text']) || isset($group['dropdown']) || isset($group['image'])) {
                    $newGroup = [];

                    foreach ($group as $kOp => $op) {
                        if ($op['active']) {
                            $op['type'] = $kOp;
                            $newGroup[] = $op;
                        }
                    }

                    $groups[$kG] = $newGroup;
                }
            }

            $product->custom_options = json_encode($groups);
        }

        return $product;
    }

    public function show($id, Request $request): JsonResponse
    {
        $getListProduct = $request->get('include_list_product', 0);
        $getListProductCategory = $request->get('include_list_product_category', 0);

        $user = currentUser();
        $user->hasPermissionOrAbort('get_orders');

        $userId = $user->getUserId();

        try {
            $orderFieldSelectForSeller = [
                "order.id",
                "order.order_number",
                "order.paid_at",
                "order.status",
                "order.fulfill_status",
                "order.type",
                "order.status_url",
                "order.access_token",
                "order.skip_validate_address",
                "order.shipping_label",
                "order.payment_status",
                "order.total_product_base_cost",
                "order.total_product_amount",
                "order.total_shipping_amount",
                "order.insurance_fee",
                "order.total_discount",
                "order.discount_code",
                "order.payment_discount",
                "order.total_tax_amount",
                "order.tip_amount",
                "order.processing_fee",
                "order.total_amount",
                "order.total_paid",
                "order.payment_method",
                "order.transaction_id",
                "order.total_refund",
                "order.processing_fee_paid",
                "order.fulfill_fee_paid",
                "order.total_fulfill_fee",
                "order.total_artist_profit",
                "order.total_seller_profit",
                "order.customer_name",
                "order.customer_email",
                "order.customer_phone",
                "order.address",
                "order.address_2",
                "order.city",
                "order.postcode",
                "order.state",
                "order.country",
                "order.store_domain",
                "order.device",
                "order.device_detail",
                "order.ad_campaign",
                "order.address_verified",
                "order.seller_id",
                "order.shipping_method",
                "order.currency_code",
                "order.currency_rate",
                "order.sen_fulfill_status",
                "order.mailbox_number",
                "order.house_number"
            ];

            $additionalFieldForAdmin = [
                "order.region",
                "order.created_at",
                "order.tm_status",
                "order.admin_note",
                "order.last4_card_number",
                "order.order_note",
                "order.support_status",
                "order.fraud_status",
                "order.sen_fulfill_status",
                "order.stats_status",
                "order.total_sen_points",
                "order.assignee",
                "order.review_request_status",
                "order.cross_shipping",
                "order.total_fulfill_profit",
                "order.flag_log"
            ];

            if ($user->isAdmin()) {
                $orderFieldSelect = array_merge($orderFieldSelectForSeller, $additionalFieldForAdmin);
            } else {
                $orderFieldSelect = $orderFieldSelectForSeller;
            }
            $query = Order::query()->select($orderFieldSelect);

            if ($user->isSeller()) {
                $query->where('seller_id', $userId);
            }

            $orderProductFields = [
                'id',
                'sku',
                'campaign_id',
                'campaign_title',
                'options',
                'custom_options',
                'color',
                'order_id',
                'seller_id',
                'template_id',
                'product_id',
                'product_name',
                'product_url',
                'price',
                'quantity',
                'thumb_url',
                'total_amount',
                'discount_amount',
                'seller_profit',
                'fulfill_status',
                'supplier_id',
                'supplier_name',
                'fulfill_sku',
                'tracking_code',
                'shipping_carrier',
                'tracking_url',
                'tracking_status',
                'personalized',
                'full_printed',
                'size',
                'artist_profit',
                'base_cost',
                'design_cost',
                'barcode',
                'fulfill_fba_by',
                'combo_id',
                'campaign_type',
                'design_by',
            ];
            if ($user->isAdmin()) {
                $orderProductFields[] = 'shipping_cost';
                $orderProductFields[] = 'supplier_id';
                $orderProductFields[] = 'fulfill_product_id';
                $orderProductFields[] = 'fulfill_base_cost';
                $orderProductFields[] = 'fulfill_shipping_cost';
                $query->with([
                    'order_disputes',
                    'order_disputes.submit_cases',
                    'order_disputes.reachouts'
                ]);
            }
            $query->with([
                'seller:id,name,email,sharding_status,db_connection',
                'products' => function ($query) use ($orderProductFields) {
                    $query->select($orderProductFields)
                        ->with(['template:id,thumb_url,print_spaces', 'customOptionDesigns:id,order_product_id'])
                        ->orderBy('updated_at', 'desc');
                },
                'issues' => function ($query) {
                    return $query->with(['staff', 'supplier']);
                }
            ]);

            $query->with(['request_cancel' => function ($query) {
                $query->select([
                    'order_id',
                    'status'
                ]);
            }]);

            $query->addSelect([
                'account_id' => PaymentGateway::query()->select('account_id')
                    ->whereColumn('payment_gateway_id', 'payment_gateways.id')
                    ->limit(1)
            ]);

            $order = $query->firstWhere('id', $id);

            if (!$order) {
                return $this->errorResponse();
            }

            // Disable campaign info of cross-sale campaign
            if (!$order->isCustomServiceOrder() && !$order->isServiceOrder() && ($user->isSeller() || $user->isAdmin())) {
                $sellerProducts = collect();
                $sellerPrintDesigns = collect();
                $order->products->groupBy('seller_id')->each(function ($productGroup, $sellerId) use ($order, &$sellerProducts, &$sellerPrintDesigns) {
                    $seller = $order->seller_id !== $sellerId ? User::query()->find($sellerId) : $order->seller;
                    $sellerProducts = $sellerProducts->merge(
                        Product::query()
                            ->select(['id', 'system_type', 'seller_id'])
                            ->onSellerConnection($seller)
                            ->whereIn('id', $productGroup->pluck('product_id'))
                            ->get()
                    );
                    $sellerPrintDesigns = $sellerPrintDesigns->merge(
                        File::query()
                            ->onSellerConnection($seller)
                            ->where('type', FileTypeEnum::DESIGN)
                            ->where('option', FileRenderType::PRINT)
                            ->whereIn('product_id', $productGroup->pluck('product_id'))
                            ->get()
                    );
                });
                $order->products->map(function ($product) use ($order, $userId, $user, $sellerProducts, $sellerPrintDesigns) {
                    if ($product->combo_id) {
                        $seller = User::query()->find($order->seller_id);
                        $combo_campaign = Campaign::query()->onSellerConnection($seller)->where('seller_id', $order->seller_id)->where('id', $product->campaign_id)->first();
                        $product->setRelation('campaign_combo', $combo_campaign);
                    }
                    $product->setRelation('product', $sellerProducts->first(fn ($item) => $item->seller_id === $product->seller_id && $item->id === $product->product_id));
                    $product->setRelation('printDesigns', $sellerPrintDesigns->filter(fn ($item) => $item->seller_id === $product->seller_id && $item->product_id === $product->product_id));
                    $product = OrderService::getAndRemoveUnusedCustomOptions($product, $order->type);
                    if ($user->isSeller() && $userId !== User::SENPRINTS_SELLER_ID && $product->seller_id !== $order->seller_id) {
                        $product->campaign_title = 'Cross-sell campaign';
                        $product->thumb_url = $product->template->thumb_url;
                    }

                    $product->options = correctOptionValue($product->options);
                    if ($product->isFullPrintedType()) {
                        $product->printDesigns = $product->printDesigns->filter(function ($designs) use ($product) {
                            $options = json_decode($product->options, true) ?? [];
                            return (!empty($options['size']) && str_contains($options['size'], $designs->print_space)) || $designs->print_space === PrintSpaceEnum::DEFAULT;
                        })->values();
                    }
                    $product->print_spaces = $product->printDesigns->pluck('print_space')->toArray();
                    if (!empty($product->product)) {
                        $product->system_type = $product->product->system_type;
                        if ($product->product->system_type === ProductSystemTypeEnum::CUSTOM || ($product->personalized === PersonalizedType::CUSTOM_OPTION && $product->full_printed === ProductPrintType::EMBROIDERY)) {
                            $product->designs = $product->printDesigns->values();
                            if ($product->designs->isEmpty()) {
                                $product->designs = $product->getPrintDesigns();
                            }
                        }
                    }
                    $product->setRelation('printDesigns', null);
                });
            }

            $fulfillments = $order->getFulfillments(isSeller: $user->isSeller());

            if (empty($getListProduct) || (int)$getListProduct !== 1) {
                $order->setRelation('products', null);
            }

            $fulfillments = array_map(function ($s) {
                return array_map(function ($p) {
                    return self::setCustomOptions($p);
                }, $s);
            }, $fulfillments);

            $dataResponse = [
                'fulfillments' => $fulfillments
            ];

            if (!empty($getListProductCategory) || (int)$getListProductCategory === 1) {
                $templateIds = $order->products()->pluck('template_id')->toArray();
                $categories = ProductCategory::getCategoriesForSyncByProductIds($templateIds);
                $dataResponse['categories'] = $categories;
                $order->products->map(function ($product) use ($categories) {
                    $product['categories'] = $categories[$product->template_id]['name'] ?? [];
                    return $product;
                });
            }
            //https://senprints.atlassian.net/browse/SDV-3473
            if (!$user->isAdmin()) {
                $isShowAddressDetail = !$user->getInfo()->custom_payment || ($order->sen_fulfill_status === OrderSenFulfillStatus::YES && $order->processing_fee_paid > 0);
                if (!$isShowAddressDetail || $user->hasExactlyRole(SellerTeamRoleEnum::MANAGER_LITE)) {
                    $order->address = null;
                    $order->address_2 = null;
                    $order->city = null;
                    $order->state = null;
                    $order->postcode = null;
                    $order->mailbox_number = null;
                    $order->house_number = null;
                }
                if ($user->hasExactlyRole(SellerTeamRoleEnum::MANAGER_LITE)) {
                    $order->customer_email = null;
                    $order->access_token = null;
                    $order->status_url = null;
                }
            } else {
                $order->append('sen_profit');
                $order->is_paid_commission = 1;
                if ($order->isRegularOrder()) {
                    $order->is_paid_commission = (int) SellerBilling::query()->where('order_id', $order->id)->where('type', SellerBillingType::COMMISSION)->where('balance_type', SellerBalanceTypeEnum::DEFAULT)->where('status', SellerBillingStatus::COMPLETED)->exists();
                }
            }
            foreach ($order->order_disputes as $orderDispute) {
                $orderDispute->dispute_due_date = isset($orderDispute->dispute_due_date) ? Carbon::parse($orderDispute->dispute_due_date)->format('Y-m-d') : '';
                $orderDispute->dispute_created_at = isset($orderDispute->dispute_created_at) ? Carbon::parse($orderDispute->dispute_created_at)->format('Y-m-d') : '';
                OrderService::checkAllowAddDisputeAction($orderDispute);
            }
            $order->append( 'platform_fee');
            $dataResponse['order_detail'] = $order;
            return $this->successResponse($dataResponse);
        } catch (Exception $ex) {
            return $this->errorResponse($ex->getMessage());
        }
    }

    public function supplierGetDetailOrder($id): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        try {
            $query = Order::query()->select([
                'order.id',
                'order.order_number',
                'order.order_number_2',
                'order.total_quantity',
                'order.created_at',
                'order.updated_at',
                'order.address',
                'order.address_2',
                'order.city',
                'order.state',
                'order.postcode',
                'order.country',
                'order.customer_name',
                'order.customer_email',
                'order.customer_phone',
                'order.order_note',
                'order.fulfill_status',
                'order.shipping_label',
                'order.mailbox_number',
                'order.house_number',
            ]);
            $orderProductFields = [
                'id',
                'sku',
                'options',
                'custom_options',
                'color',
                'order_id',
                'template_id',
                'product_id',
                'product_name',
                'product_url',
                'quantity',
                'thumb_url',
                'total_amount',
                'fulfill_status',
                'supplier_id',
                'supplier_name',
                'fulfill_sku',
                'tracking_code',
                'shipping_carrier',
                'tracking_url',
                'tracking_status',
                'personalized',
                'full_printed',
                'size',
                'supplier_id',
                'fulfill_product_id',
                'supplier_exported_at',
                'assigned_supplier_at',
                'barcode',
                'seller_id',
            ];

            $query->with([
                'products' => function ($query) use ($orderProductFields, $userId) {
                    $query->select($orderProductFields)
                        ->where('supplier_id', $userId)
                        ->with(['template:id,thumb_url,print_spaces', 'customOptionDesigns:id,order_product_id', 'printDesigns', 'product:id,system_type'])
                        ->orderBy('updated_at', 'desc');
                }
            ]);
            $query->whereIn('order.fulfill_status', Order::SUPPLIER_ORDER_STATUS);
            $order = $query->firstWhere('id', $id);
            if (!$order || count($order->products) === 0) {
                return $this->errorResponse();
            }

            $fulfillments = $order->getFulfillments();
            if (count($fulfillments) === 0) {
                return $this->errorResponse();
            }
            $sellerProducts = collect();
            $sellerPrintDesigns = collect();
            $sellerMap = User::query()
                ->whereIn('id', $order->products->pluck('seller_id')->unique())
                ->get();

            $order->products->groupBy('seller_id')->each(function ($productGroup, $sellerId) use ($sellerMap, $order, &$sellerProducts, &$sellerPrintDesigns) {
                $seller = $order->seller_id !== $sellerId ? $sellerMap->where('id', $sellerId)->first() : $order->seller;
                $sellerProducts = $sellerProducts->merge(
                    Product::query()
                        ->select(['id', 'system_type', 'seller_id'])
                        ->onSellerConnection($seller)
                        ->whereIn('id', $productGroup->pluck('product_id'))
                        ->get()
                );
                $sellerPrintDesigns = $sellerPrintDesigns->merge(
                    File::query()
                        ->onSellerConnection($seller)
                        ->where('type', FileTypeEnum::DESIGN)
                        ->where('option', FileRenderType::PRINT)
                        ->whereIn('product_id', $productGroup->pluck('product_id'))
                        ->get()
                );
            });
            $order->products = $order->products->map(function (OrderProduct $item) use ($sellerMap, $sellerProducts, $sellerPrintDesigns) {
                $seller = $sellerMap->where('id', $item->seller_id)->first();
                $item->setRelation('product', $sellerProducts->first(fn ($sellerProduct) => $sellerProduct->seller_id === $item->seller_id && $sellerProduct->id === $item->product_id));
                $item->setRelation('printDesigns', $sellerPrintDesigns->filter(fn ($sellerProduct) => $sellerProduct->seller_id === $item->seller_id && $sellerProduct->product_id === $item->product_id));
                $options = json_decode($item->options);
                $variant = ProductVariant::query()
                    ->onSellerConnection($seller)
                    ->where('product_id', $item->fulfill_product_id)
                    ->where('variant_key', getVariantKey($options))
                    ->first();
                if (!empty($variant)) {
                    $item['supplier_sku'] = $variant->sku ?? '';
                }
                $item->assigned_supplier_at = $item->getAssignedSupplierAt();
                return $item;
            });

            $fulfillments = array_map(function ($s) {
                return array_map(function ($p) {
                    return self::setCustomOptions($p);
                }, $s);
            }, $fulfillments);

            $dataResponse = [
                'fulfillments' => $fulfillments
            ];

            $dataResponse['order_detail'] = $order;
            return $this->successResponse($dataResponse);
        } catch (Exception $ex) {
            return $this->errorResponse($ex->getMessage());
        }
    }

    /**
     * @param UpdateOrderRequest $request
     * @return JsonResponse
     */
    public function update(UpdateOrderRequest $request): JsonResponse
    {
        $orderId = $request->post('order_id');
        $products = $request->post('products');
        $reason = $request->post('reason');

        try {
            DB::beginTransaction();
            $order = Order::query()
                ->where('id', $orderId)
                ->firstOrFail();

            //map products & re-calculate order
            $order->products->filter(function ($product) use ($products) {
                $productIdx = array_search($product['id'], array_column($products, 'order_product_id'));
                return !array_key_exists('deleted', $products[$productIdx]);
            })->map(function ($product) use ($products) {
                $productIdx = array_search($product['id'], array_column($products, 'order_product_id'));

                if ($productIdx >= 0) {
                    $product['quantity'] = $products[$productIdx]['quantity'];

                    if ($product['quantity'] === $product['fulfilled_quantity']) {
                        $product['fulfill_status'] = OrderProductFulfillStatus::FULFILLED;
                    } else if ($product['quantity'] > $product['fulfilled_quantity']) {
                        $product['fulfill_status'] = OrderProductFulfillStatus::PARTIALLY_FULFILLED;
                    }
                }

                return $product;
            });

            $deletedProductIds = array_column(array_filter($products, static function ($product) {
                if (isset($product['deleted']) && $product['deleted'] === 1) {
                    return $product;
                }

                return null;
            }), 'order_product_id');

            $order->calculateOrder();

            // order status
            if ($order->isOrderCompleted()) {
                $order->status = OrderStatus::COMPLETED;
            }

            // payment status
            if (
                $order->payment_status === OrderPaymentStatus::PAID
                && $order->total_paid < $order->total_amount
            ) {
                $order->payment_status = OrderPaymentStatus::PARTIALLY_PAID;
            }

            // create order history log
            $updateReason = 'Order updated';

            if (!is_null($reason)) {
                $updateReason .= ': ' . $reason;
            }

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::UPDATED,
                $updateReason,
            );

            $orderProducts = array_map(function ($product) {
                return $product;
            }, $order->products->toArray());

            //save order & products
            batch()->update(new OrderProduct([], true), $orderProducts, 'id');

            OrderProduct::query()
                ->whereIn('id', $deletedProductIds)
                ->delete();

            $order->save();

            DB::commit();
            clearHtmlCacheByTag('order', $orderId);
            return $this->successResponse(null, 'Update order success');
        } catch (Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Update order failed', 400);
        }
    }

    /**
     * @param Request $request
     * @return Builder|Model
     */
    public function recalculateOrder(Request $request)
    {
        $orderId = $request->post('order_id');
        $products = $request->post('products');
        $user = currentUser();

        if (!$user->isAdmin()) {
            abort(401);
        }

        $order = Order::query()
            ->where('id', $orderId)
            ->firstOrFail();

        $order->products = $order->products->filter(function ($product) use ($products) {
            $productIdx = array_search($product['id'], array_column($products, 'order_product_id'));
            return !array_key_exists('deleted', $products[$productIdx]);
        })->map(function ($product) use ($products) {
            $productIdx = array_search($product['id'], array_column($products, 'order_product_id'));

            if ($productIdx >= 0) {
                if (array_key_exists('deleted', $products[$productIdx])) {
                    $product['quantity'] = 0;
                } else {
                    $product['quantity'] = $products[$productIdx]['quantity'];
                }
            }

            return $product;
        });

        $order->calculateOrder();

        return $order;
    }

    /**
     * @param Request $request
     * @return array
     */
    public function calculateRefund(Request $request): array
    {
        $orderId = $request->post('order_id');
        $products = $request->post('products');
        $user = currentUser();

        if (!$user->isAdmin()) {
            abort(401);
        }

        $order = Order::query()
            ->where('id', $orderId)
            ->firstOrFail();

        $itemsTotal = 0;
        $tax = 0;
        $shipping = 0;
        $sellerRefunds = [];

        $order->products->map(function ($product) use ($products, &$itemsTotal, &$tax, &$shipping, &$sellerRefunds) {
            $productIdx = array_search($product['id'], array_column($products, 'order_product_id'));

            if ($productIdx >= 0 && $products[$productIdx]['refund_quantity']) {
                $product['refund_quantity'] = $products[$productIdx]['refund_quantity'];
                $itemsTotal += $product->calculateTotalRefund();
                $shipping += $product->calculateShipping();
                $tax += $product->calculateTax();

                // Calculate seller profit to refund
                if (array_key_exists($product['seller_id'], $sellerRefunds)) {
                    $sellerRefunds[$product['seller_id']] += $product['seller_profit'] * $product['refund_quantity'] / $product['quantity'];
                } else {
                    $sellerRefunds[$product['seller_id']] = $product['seller_profit'] * $product['refund_quantity'] / $product['quantity'];
                }
            }
        });

        $sellerIds = array_keys($sellerRefunds);
        $sellers = User::query()
            ->select('id', 'email')
            ->whereIn('id', $sellerIds)
            ->get()
            ->map(function ($seller) use ($sellerRefunds) {
                $seller->refund_amount = $sellerRefunds[$seller->id];
                return $seller;
            })
            ->toArray();

        $refundTotal = $itemsTotal + $tax + $shipping;

        return [
            'items_total' => $itemsTotal,
            'tax' => $tax,
            'shipping' => $shipping,
            'refund_total' => $refundTotal,
            'sellers' => $sellers
        ];
    }

    /**
     * @param RefundOrderRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function refundOrder(RefundOrderRequest $request): JsonResponse
    {
        $orderId = $request->post('order_id');
        $refundAmount = $request->post('refund_amount');
        $user = currentUser();
        $user->hasPermissionOrAbort('update_order');

        if (!$user->isAdmin()) {
            abort(401);
        }

        try {
            DB::beginTransaction();
            $order = Order::query()
                ->where('id', $orderId)
                ->firstOrFail();

            $refundableAmount = $order->total_paid - $order->total_refund;

            $refundAmount = (float)$refundAmount;
            if ($refundAmount == 0) {
                $refundAmount = $refundableAmount;
            }

            $order->total_refund += $refundAmount;

            if ($refundAmount === $refundableAmount) {
                $order->status = OrderStatus::REFUNDED;
                $order->payment_status = OrderPaymentStatus::REFUNDED;
                $order->fulfill_status = OrderFulfillStatus::CANCELLED;

                if ($order->type === OrderTypeEnum::REGULAR || $order->type === OrderTypeEnum::CUSTOM) {
                    $profits = [];
                    if ($order->type === OrderTypeEnum::REGULAR) {
                        $profits[$order->seller_id] = $order->total_seller_profit;
                    }

                    if ($order->type === OrderTypeEnum::REGULAR || ($order->type === OrderTypeEnum::CUSTOM && $order->processing_fee_paid > 0)) {
                        $order->products->map(function ($orderProduct) use (&$profits, $order) {
                            $orderProduct->save();
                            // get profits
                            if (
                                !empty($orderProduct->seller_id)
                                && $orderProduct->seller_id !== $order->seller_id
                                && $orderProduct->artist_profit
                            ) {
                                if (empty($profits[$orderProduct->seller_id])) {
                                    $profits[$orderProduct->seller_id] = $orderProduct->artist_profit;
                                } else {
                                    $profits[$orderProduct->seller_id] += $orderProduct->artist_profit;
                                }
                            }
                        });
                    }

                    foreach ($profits as $sellerId => $profit) {
                        $seller = User::query()->firstWhere('id', $sellerId);
                        $seller?->updateBalance(-$profit, SellerBillingType::REFUND, 'Refund order #' . $order->order_number, $order->id);
                    }
                }
            } else if ($refundAmount < $refundableAmount) {
                $order->payment_status = OrderPaymentStatus::PARTIALLY_REFUNDED;
            } else if ($refundAmount > $refundableAmount) {
                return $this->errorResponse('Refund amount must be lower than total amount', 400);
            }

            $reason = 'Order refunded $' . $refundAmount;

            if (!is_null($request->post('reason'))) {
                $reason .= ': ' . $request->post('reason');
            }

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::REFUNDED,
                'Reason:' . $reason,
                OrderHistoryDisplayLevelEnum::CUSTOMER,
            );
            $order->save();
            Db::commit();
            // Call event Order Update
            OrderUpdated::dispatch($orderId); // Refund order

            return $this->successResponse(null, 'Refund order success');
        } catch (Exception $e) {
            DB::rollBack();
            logException($e);
            return $this->errorResponse('Refund order failed');
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function markOrderAsPaid(Request $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('update_order');

        $orderId = $request->post('order_id');

        try {
            $order = Order::query()->findOrFail($orderId);

            if ($order->status === OrderStatus::PENDING || $order->status === OrderStatus::PENDING_PAYMENT) {
                $order->status = OrderStatus::PROCESSING;
                $order->payment_status = OrderPaymentStatus::PAID;
            } else {
                return $this->errorResponse('Order status is invalid');
            }

            if ($order->total_paid == 0) {
                $order->total_paid = $order->total_amount;
            }

            if ($order->paid_at == null) {
                $order->paid_at = Carbon::now();
            }

            DB::beginTransaction();

            $order->save();
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::PAID_BY_ADMIN,
                'Order marked as paid manually'
            );
            DB::commit();
            if (in_array($order->type, [OrderTypeEnum::CUSTOM, OrderTypeEnum::REGULAR, OrderTypeEnum::SERVICE], true)) {
                OrderPaymentCompleted::dispatch($order);
                SyncOrderToRegion::dispatch($order, $order->getRegion(), syncOrderProducts: false)->onQueue('sync_order_region');
            }
            return $this->successResponse(['order_id' => $orderId], 'Mark order as paid success!');
        } catch (Exception $e) {
            logException($e, __FUNCTION__, 'error', true);
            DB::rollBack();
            return $this->errorResponse('Mark order as paid failed');
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function payCommission(Request $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('update_order');
        $orderId = $request->post('order_id');
        try {
            $order = Order::query()->findOrFail($orderId);
            if ($order->payment_status !== OrderPaymentStatus::PAID || $order->status === OrderStatus::CANCELLED || !$order->isRegularOrder()) {
                return $this->errorResponse('Order is invalid');
            }
            $is_paid = SellerBilling::query()->where('order_id', $order->id)->where('type', SellerBillingType::COMMISSION)->where('balance_type', SellerBalanceTypeEnum::DEFAULT)->where('status', SellerBillingStatus::COMPLETED)->exists();
            if ($is_paid) {
                return $this->errorResponse('Commission has been paid');
            }
            OrderService::payProfitsForSellerArtist($order);
            return $this->successResponse(['order_id' => $orderId], 'Commission has been paid');
        } catch (Exception $e) {
            logException($e, __FUNCTION__, 'error', true);
            DB::rollBack();
            return $this->errorResponse('Pay commission failed');
        }
    }

    public function updateNote(UpdateNoteRequest $request, $orderId): JsonResponse
    {
        $note = $request->post('note');
        $column = $request->post('column');

        $order = Order::selectForHistory($column)->find($orderId);

        if (is_null($order)) {
            return $this->errorResponse();
        }

        $order->$column = $note;

        // check if changed
        if ($order->isDirty()) {
            $order->save();

            if ($column === 'admin_note') {
                $action = OrderHistoryActionEnum::ADD_ADMIN_NOTE;
                $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN;
            } else {
                $action = OrderHistoryActionEnum::EDIT_CUSTOMER_NOTE;
                $displayLevel = OrderHistoryDisplayLevelEnum::CUSTOMER;
            }

            OrderHistory::insertLog(
                $order,
                $action,
                $note,
                $displayLevel
            );
        }

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @param $order
     * @return JsonResponse
     */
    public function updateFulfillStatus(Request $request, &$order): JsonResponse
    {
        $currentUser = currentUser();
        $currentUser->hasPermissionOrAbort('update_order');
        $isFromBackend = $order instanceof Order;
        if ($isFromBackend) {
            $orderId = $order->id;
        } else {
            $order = Order::query()->whereKey($orderId = $order)->first();
        }

        if (is_null($order)) {
            return $this->errorResponse('Order not found');
        }

        $fulfillStatus = $requestFulfillStatus = $request->post('status');
        $checkValidStatus = false;
        $invalidOnHoldStatus = [
            OrderFulfillStatus::PROCESSING,
            OrderFulfillStatus::ON_DELIVERY,
            OrderFulfillStatus::FULFILLED,
            OrderFulfillStatus::CANCELLED,
            OrderFulfillStatus::ON_HOLD,
        ];
        $detail = '[' . ($currentUser->getName() ?? $currentUser->getEmail()) . ']';
        $changedAddressValid = false;
        switch ($fulfillStatus) {
            case OrderFulfillStatus::ON_HOLD:
                $action = OrderHistoryActionEnum::HOLD_FULFILL;
                $checkValidStatus = !in_array($order->fulfill_status, $invalidOnHoldStatus, true);
                break;
            case OrderFulfillStatus::PROCESSING:
                $action = OrderHistoryActionEnum::RESUME_FULFILL;
                $checkValidStatus = in_array($order->fulfill_status, [...OrderFulfillStatus::isHoldPaymentAndCharge(), OrderFulfillStatus::ON_HOLD], true) || $order->address_verified === OrderAddressVerifiedEnum::INVALID;
                $isOnHoldOrPendingOrWithin24Hours = $checkValidStatus || $order->sen_fulfill_status === OrderSenFulfillStatus::PENDING || now()->lte(Carbon::parse($order->paid_at)->addHours(24));
                if ($isOnHoldOrPendingOrWithin24Hours) {
                    $fulfillStatus = OrderFulfillStatus::UNFULFILLED;
                }
                if ($order->address_verified === OrderAddressVerifiedEnum::INVALID) {
                    $order->address_verified = OrderAddressVerifiedEnum::VERIFIED;
                    $changedAddressValid = true;
                }
                break;
        }
        if ($order->fulfill_status !== $fulfillStatus) {
            $detail .= ' Changed fulfill status from ' . $order->fulfill_status . ' to ' . $fulfillStatus;
        }
        if ($changedAddressValid) {
            $detail .= ' - Changed address verified status from ' . OrderAddressVerifiedEnum::INVALID . ' to ' . OrderAddressVerifiedEnum::VERIFIED;
        }
        if (!$checkValidStatus) {
            if ($isFromBackend) {
                throw new RuntimeException('Order fulfill status is invalid for update');
            }
            return $this->errorResponse('Invalid order status');
        }

        $order->fulfill_status = $fulfillStatus;
        $order->save();

        // update order product fulfill status
        if ($requestFulfillStatus === OrderFulfillStatus::PROCESSING && $fulfillStatus === OrderFulfillStatus::UNFULFILLED) {
            OrderProduct::query()->where('order_id', $orderId)->where('fulfill_status', OrderProductFulfillStatus::ON_HOLD)->update([
                'fulfill_status' => OrderProductFulfillStatus::UNFULFILLED
            ]);
            $needCharge = $order->calculateAndSaveOrderServiceAndFulfillFee();
            $order = $order->refresh();
            if ($needCharge) {
                OrderService::processCustomOrder($order);
            }
        }

        if (!empty($action)) {
            OrderHistory::insertLog(
                $order,
                $action,
                $detail
            );
            // Cancel order request cancel order if the status changed back to resume fulfill
            if ($action === OrderHistoryActionEnum::RESUME_FULFILL) {
                OrderRefundedEvent::dispatch($orderId);
                $order->products->map(function (OrderProduct $product) use ($order) {
                    if (!$product->isDesignBySenPrints()) {
                        return;
                    }
                    if ($order->isFulfillmentOrder() && $product->fulfillOrderMockups->isNotEmpty() && $product->isNotEnoughFulfillmentDesigns()) {
                        $product->fulfill_status = OrderProductFulfillStatus::DESIGNING;
                        return;
                    }
                    if ($product->isAiMockupOrder() && $product->isNotEnoughAiDesigns()) {
                        $product->fulfill_status = OrderProductFulfillStatus::DESIGNING;
                        return;
                    }
                    if ($product->mockups->isNotEmpty() && $product->designs->isEmpty()) {
                        $product->fulfill_status = OrderProductFulfillStatus::DESIGNING;
                    }
                });
                $hasDesigningProducts = $order->products->some(function (OrderProduct $product) {
                    return $product->isDesignBySenPrints() && $product->fulfill_status === OrderProductFulfillStatus::DESIGNING;
                });
                if ($hasDesigningProducts) {
                    $order->fulfill_status = OrderFulfillStatus::DESIGNING;
                    $order->push();
                    OrderHistory::insertLog(
                        $order,
                        OrderHistoryActionEnum::VALIDATE,
                        'Order need the designer to make designs for products',
                    );
                }
            }
        }

        return $this->successResponse();
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function updateResumeStatus($id): JsonResponse
    {
        $order = Order::selectForHistory()
            ->whereKey($id)
            ->where('status', OrderStatus::SUSPENDED)
            ->first();

        if (is_null($order)) {
            return $this->errorResponse('Invalid order status');
        }

        $order->status = OrderStatus::PROCESSING;
        $order->save();

        OrderHistory::insertLog(
            $order,
            OrderHistoryActionEnum::RESUME_ORDER,
        );

        return $this->successResponse();
    }

    /**
     * List order exports
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listExport(Request $request): JsonResponse
    {
        $search = $request->get('q');
        $filterStatus = $request->get('status');
        $data = OrderExport::query()
            ->when($search, function ($query, $search) {
                $query->where('export_name', 'like', '%' . $search . '%');
            })
            ->when($filterStatus, function ($query, $filterStatus) {
                $query->where('status', strtolower($filterStatus));
            })
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return $this->successResponse($data);
    }

    /**
     * Count orders
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function countUnexportedOrders(Request $request): JsonResponse
    {
        try {
            $date = $request->input('date');
            $count = OrderProduct::selectExportFields($date)
                ->distinct('order_id')
                ->count();

            return $this->successResponse([
                'count' => $count
            ]);
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse|BinaryFileResponse
     */
    public function previewExport(Request $request)
    {
        try {
            $date = $request->input('date');
            $data = OrderProduct::selectExportFields($date)->get();
            $export = new ExportOrderToExcel($data);

            return Excel::download($export, 'export.csv');
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * Export orders
     *
     * @param ExportOrderRequest $request
     * @return JsonResponse
     */
    public function exportOrders(ExportOrderRequest $request): JsonResponse
    {
        try {
            $date = $request->post('date');
            $exportName = $request->post('export_name');
            $orders = OrderProduct::selectExportFields($date)->get();
            $fileName = 'export/order_export_' . Carbon::parse($date)->format('Y-m-d') . '.csv';
            $export = new ExportOrderToExcel($orders);

            Excel::store($export, $fileName, StorageDisksEnum::DEFAULT, \Maatwebsite\Excel\Excel::CSV, [
                'visibility' => 'public',
            ]);
            $downloadUrl = cdnUrl() . '/' . $fileName;

            $exportId = OrderExport::query()->insertGetId([
                'export_name' => $exportName,
                'total_orders' => $orders->count(),
                'download_url' => $downloadUrl,
                'status' => OrderExportStatus::COMPLETED
            ]);

            $orderIds = $orders->pluck('id')->unique();

            Order::query()
                ->whereIn('id', $orderIds)
                ->update([
                    'export_id' => $exportId,
                    'exported_at' => Carbon::now(),
                    'fulfill_status' => OrderFulfillStatus::PROCESSING
                ]);

            OrderProduct::query()
                ->whereIn('order_id', $orderIds)
                ->update([
                    'fulfill_status' => OrderProductFulfillStatus::PROCESSING
                ]);

            return $this->successResponse([
                'export_id' => $exportId,
                'export_name' => $exportName,
                'download_url' => $downloadUrl
            ]);
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function sellerImportFulfillOrders(SellerImportFulfillOrdersRequest $request): JsonResponse
    {
        $file = $request->file('file');
        $skipValidateAddress = $request->input('skip_validate_address', '0');


        if ($skipValidateAddress === '1') {
            $skipValidateAddress = true;
            $currentUser = currentUser()->getUserId();
            $data = User::query()
                ->select([
                    'name',
                    'nickname',
                    'contest_name',
                    'email',
                    'tags'
                ])
                ->firstWhere('id', $currentUser);
            if (!$data) {
                return $this->errorResponse('You do not have any permission', 403);
            }

            $tags = $data->tags;
            if (!empty($tags)) {
                $isExistSkipValidateTag = strpos($data->tags, 'skip validate address');
                if ($isExistSkipValidateTag === false) {
                    return $this->errorResponse('You do not have permission to skip validate address', 403);
                }
            } else {
                return $this->errorResponse('You do not have any permission', 403);
            }
        } else {
            $skipValidateAddress = false;
        }

        try {
            Excel::import(new ImportCSVMultiPlatform($skipValidateAddress), $file);
            return $this->successResponse();
        } catch (Exception $e) {
            $code = $e->getCode();

            if ($code === 2002) {
                return $this->errorResponse('Import failed', 403);
            }

            if ($code === 23000) {
                return $this->errorResponse('Duplicate entry', 403);
            }

            logToDiscord($code . ': Seller import fulfill orders failed' . json_encode($e->getMessage()), 'import_fulfill_order_failed');
            return $this->errorResponse($e->getMessage(), $code > 0 ? $code : 403);
        }
    }

    public function sellerExportFulfillOrders(Request $request)
    {
        $currentUserId = currentUser()->getUserId();

        $option = $request->input('option');
        $keywords = $request->input('q');
        $orderStatus = $request->input('status');
        $fulfillStatus = $request->input('fulfill_status');
        $time = $request->input('time');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $sortBy = $request->input('sort');
        $sortDirection = $request->input('direction', 'desc');
        $timeZone = $request->input('timezone');

        $query = OrderProduct::query()
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->select([
                'order_product.id as id',
                'order.order_number as sen_order_number',
                'order.order_number_2 as order_number',
                'order_product.sku as product_sku',
                'order_product.product_name as product_name',
                'order_product.quantity as quantity',
                'order.customer_name as customer_name',
                'order_product.options as options',
                'order.customer_email as customer_email',
                'order.customer_phone as customer_phone',
                'order.address as address',
                'order.address_2 as address_2',
                'order.city as city',
                'order.state as state',
                'order.postcode as postcode',
                'order.order_note as order_note',
                'order.store_domain as store_domain',
                'order.store_name as store_name',
                'order_product.base_cost  as base_cost ',
                'order_product.shipping_cost as shipping_fee',
                'order.status as order_status',
                'order.fulfill_status as fulfill_status',
                'order_product.shipping_carrier as shipping_carrier',
                'order_product.tracking_code as tracking_code',
                'order_product.tracking_url as tracking_url',
                'order.created_at as created_at',
            ])
            ->where([
                'order.seller_id' => $currentUserId,
            ])
            ->whereIn('order.type', [
                OrderTypeEnum::FULFILLMENT,
                OrderTypeEnum::FBA
            ]);

        if ($option !== 'export_filtered') {
            $query->where('order_product.fulfill_status', OrderProductFulfillStatus::FULFILLED)
                ->whereNull('order_product.exported_at')
                ->whereNotNull('order_product.tracking_code');
        }

        if ($option === 'export_filtered') {
            $query->with('fulfillOrderMockups');
            $query->with('fulfillOrderDesigns');
            $keywords = trim($keywords);

            if (!empty($keywords)) {
                $query->where('order.order_number_2', 'like', '%' . $keywords . '%')
                    ->orWhere('order.customer_name', 'like', '%' . $keywords . '%')
                    ->orWhere('order.customer_email', 'like', '%' . $keywords . '%');
            }

            if (!empty($orderStatus)) {
                $query->where('order.status', $orderStatus);
            }

            if (!empty($fulfillStatus)) {
                $query->where('order.fulfill_status', $fulfillStatus);
            }

            if (!empty($time)) {
                $query->filterDateRange($time, $startDate, $endDate, 'order.created_at');
            }

            if (!empty($sortBy)) {
                $query->orderBy('order.' . $sortBy, $sortDirection);
            }
        }

        $result = $query->get();

        if ($result->isEmpty()) {
            return $this->errorResponse('No orders available to export', 403);
        }

        if ($option === 'export') {
            $orderProductIds = $result->pluck('id');

            OrderProduct::query()
                ->whereIn('id', $orderProductIds)
                ->update([
                    'exported_at' => Carbon::now()
                ]);
        }

        $result = $result->toArray();
        $extraKeys = [];
        $result = array_map(function ($product) use ($timeZone, &$extraKeys) {
            $designs = $product['fulfill_order_designs'] ?? [];
            $mockups = $product['fulfill_order_mockups'] ?? [];
            $options = json_decode($product['options']);
            $createdAt = new Carbon($product['created_at']);
            $product['created_at'] = Carbon::parse($createdAt)->setTimezone($timeZone)->toDateTimeString();
            unset($product['id'], $product['options']);

            if (!empty($options)) {
                $optionsKey = get_object_vars($options);
                foreach ($optionsKey as $key => $value) {
                    $product['option:' . $key] = $value;
                }
            }

            foreach ($designs as $design) {
                $key = 'design:' . $design['print_space'];
                $url = $design['file_url_2'] ?? $design['file_url'];
                $product[$key] = s3Url($url);
                if (!in_array($key, $extraKeys)) {
                    $extraKeys[] = $key;
                }
            }

            foreach ($mockups as $mockup) {
                $key = 'mockup:' . $mockup['print_space'];
                $url = $mockup['file_url_2'] ?? $mockup['file_url'];
                $product[$key] = s3Url($url);
                if (!in_array($key, $extraKeys)) {
                    $extraKeys[] = $key;
                }
            }
            unset($product['fulfill_order_designs']);
            unset($product['fulfill_order_mockups']);

            return $product;
        }, $result);
        $headings = array_keys($result[0]);
        foreach ($extraKeys as $key) {
            if (!in_array($key, $headings)) {
                $headings[] = $key;
            }
        }
        $fileName = 'Senprints_fulfill_order_export_' . Carbon::now()->toDateTimeLocalString() . '.csv';
        $export = new ExportFulfillOrderProductToExcel($result, $headings);


        return Excel::download($export, $fileName);
    }

    /**
     * @param Request $request
     * @return JsonResponse|BinaryFileResponse
     */
    public function sellerExportFulfillOrderSummary(Request $request)
    {
        $orderIds = checkAndConvertToArray($request->input('order_ids'));
        $currentUserId = currentUser()->getUserId();

        $orders = Order::query()
            ->select([
                'id',
                'order_number',
                'order_number_2',
                'customer_name',
                'customer_email',
                'customer_phone',
                'address',
                'address_2',
                'city',
                'state',
                'postcode',
                'country',
                'total_quantity',
                'shipping_method',
                'total_product_amount',
                'total_shipping_amount',
                'processing_fee',
                'total_amount',
                'created_at',
            ])
            ->whereIn('id', $orderIds)
            ->where([
                'order.seller_id' => $currentUserId,
            ])
            ->whereIn('order.type', [
                OrderTypeEnum::FULFILLMENT,
                OrderTypeEnum::FBA
            ])
            ->get()
            ->toArray();

        if (empty($orders)) {
            return $this->errorResponse(null, 404);
        }

        foreach ($orders as $idx => $order) {
            unset($orders[$idx]['id']);
            $orders[$idx]['created_at'] = Carbon::parse($order['created_at'])->format('Y-m-d');
        }

        $fileName = 'Senprints_fulfill_order_summary_export_' . Carbon::now()->toDateTimeLocalString() . '.csv';
        $export = new ExportFulfillOrderSummaryToExcel($orders);

        return Excel::download($export, $fileName);
    }

    /**
     * @param AdminCreateChargeOrderRequest $request
     * @return JsonResponse
     */
    public function adminCreateChargeOrder(AdminCreateChargeOrderRequest $request): JsonResponse
    {
        $orderId = $request->input('order_id');
        $amount = $request->input('amount');
        $reason = $request->input('reason');
        $sellerAmount = $request->input('seller_amount', 0);
        $createWithSenstore = $request->input('createWithSenstore');

        $order = Order::query()->find($orderId);

        if (empty($order)) {
            return $this->errorResponse('Order not exists', 403);
        }

        if ($order->isCustomOrder()) {
            if (empty($sellerAmount)) {
                return $this->errorResponse('Seller amount is required for custom order', 400);
            }

            if ($createWithSenstore) {
                return $this->errorResponse('You can not create charge with senstore domain for custom order', 400);
            }
        }

        $newServiceOrder = OrderService::createServiceOrder($amount, $reason, $order, $createWithSenstore, $sellerAmount);
        $checkoutUrl = $newServiceOrder->getCheckoutUrl();

        return $this->successResponse([
            'checkout_url' => $checkoutUrl
        ]);
    }

    public function calculateChargeInfo (Request $request) {
        $data = $request->input('data');
        $orderId = $request->input('order_id');
        $type = $request->input('type', ChargeOrderTypeEnum::CHANGE_OPTION);
        try {
            $response = app(OrderService::class)->calculateChargeBaseAndShippingCost($data, $orderId, $type);
            return $this->successResponse($response);
        } catch (\Throwable $e) {
            logToDiscord('[Calculate charge error] ' . $e->getMessage());
            return $this->errorResponse($e->getMessage());
        }
    }

    public function indexHistory($orderId): JsonResponse
    {
        try {
            $query = OrderHistory::query()
                ->select(
                    [
                        'order_id',
                        'action',
                        'order_status',
                        'fulfill_status',
                        'support_status',
                        'assignee',
                        'detail',
                        'updated_by',
                        'created_at',
                    ]
                )
                ->where('order_id', $orderId);

            $user = currentUser();

            if ($user->isAdmin()) {
                $query->addSelect('admin_detail');
            } elseif ($user->isSeller()) {
                $query
                    ->where('display_level', OrderHistoryDisplayLevelEnum::CUSTOMER)
                    ->whereHas('order', function (Builder $builder) use ($orderId) {
                        $builder->where('id', $orderId);
                    });
            } else {
                // block
                abort(404);
            }
            $data = $query->orderByDesc('id')->get();
            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    public function updateSupportStatus(Request $request): JsonResponse
    {
        try {
            $orderId = $request->get('order_id');

            $order = Order::selectForHistory()->find($orderId);

            if (is_null($order)) {
                return $this->errorResponse('Order not found.');
            }

            $order->support_status = $request->get('support_status');

            // check if changed
            if ($order->isDirty()) {
                // check if don't have
                if (is_null($order->assignee)) {
                    // get current user
                    $supporter = SystemConfigController::supporters()
                        ->firstWhere('id', currentUser()->getUserId());

                    if (!is_null($supporter)) {
                        $order->assignee = $supporter->id;
                    }
                }
                $order->save();

                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::CHANGE_SUPPORT_CATEGORY,
                    !is_null($order->support_status) ? OrderSupportStatusEnum::getKey($order->support_status) : null,
                );
            }

            return $this->successResponse($order->getChanges());
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateAssignee(Request $request): JsonResponse
    {
        try {
            $orderId = $request->get('order_id');

            $order = Order::selectForHistory()->find($orderId);

            if (is_null($order)) {
                return $this->errorResponse('Order not found.');
            }

            $order->assignee = $request->get('assignee');

            // check if changed
            if ($order->isDirty()) {
                $order->save();
                $supporter = SystemConfigController::supporters()->firstWhere('id', $order->assignee);
                if (is_null($supporter)) {
                    $detail = 'unassigned';
                } else {
                    $detail = $supporter->name . '<' . $supporter->email . '>';
                }

                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::ASSIGN_TO_STAFF,
                    $detail,
                );
            }

            return $this->successResponse($order->getChanges());
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateSupport(UpdateSupportRequest $request): JsonResponse
    {
        if (!currentUser()->isAdmin()) {
            return $this->errorResponse('current user is not admin');
        }

        try {
            $orderId = $request->get('order_id');
            $column = $request->post('column');

            $order = Order::selectForHistory($column)
                ->find($orderId);

            if (is_null($order)) {
                return $this->errorResponse('Order not found.');
            }

            // support status
            $order->support_status = $request->get('support_status');

            // assignee
            $assignee = $request->get('assignee');
            // check if don't have
            if (is_null($order->assignee) && empty($assignee)) {
                // get current user
                $assignee = currentUser()->getUserId();
            }
            $supporter = SystemConfigController::supporters()->firstWhere('id', $assignee);
            $order->assignee = optional($supporter)->id;

            // note
            $note = $request->post('note');
            if ($request->filled('note')) {
                if ($column === 'admin_note') {
                    $action = OrderHistoryActionEnum::ADD_ADMIN_NOTE;
                    $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN;
                    $order->$column .= "\n" . $note;
                } else {
                    $order->$column = $note;
                }
            }

            // check if changed
            if ($order->isDirty($column)) {
                if ($column === 'order_note') {
                    $action = OrderHistoryActionEnum::EDIT_CUSTOMER_NOTE;
                    $displayLevel = OrderHistoryDisplayLevelEnum::CUSTOMER;
                }
                $detail = $note;
            } elseif ($order->isDirty('support_status')) {
                $action = OrderHistoryActionEnum::CHANGE_SUPPORT_CATEGORY;
                $detail = !is_null($order->support_status) ? OrderSupportStatusEnum::getKey($order->support_status) : null;
                $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN;
            } elseif ($order->isDirty('assignee')) {
                $action = OrderHistoryActionEnum::ASSIGN_TO_STAFF;
                $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN;
                if (is_null($supporter)) {
                    $detail = 'unassigned';
                } else {
                    $detail = $supporter->name . '<' . $supporter->email . '>';
                }
            }

            if (isset($action, $detail, $displayLevel) && $order->isDirty()) {
                OrderHistory::insertLog(
                    $order,
                    $action,
                    $detail,
                    $displayLevel
                );
            }

            $order->save();

            if ($request->filled('status')) {
                $this->updateFulfillStatus($request, $order);
            }

            return $this->successResponse($order);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function receivedOrders(Request $request)
    {
        $search = $request->input('q');
        $time = $request->input('time');
        $perPage = $request->input('per_page', 25);
        $orderBy = $request->input('sort', 'received_at');
        $direction = $request->input('direction', 'desc');
        $sellerId = $request->input('seller_id');

        $query = Order::query()
            ->select([
                'order.id',
                'order.order_number',
                'order.country',
                'order.customer_name',
                'order.customer_email',
                'order.received_at',
                'order.seller_id',
                'order.total_amount',
                'order.total_quantity',
                'order.created_at',
                'order.access_token',
                'user.name as seller_name',
                'user.email as seller_email',
            ])
            ->join('user', 'user.id', '=', 'order.seller_id')
            ->with(['order_products' => function ($query) {
                $query->select([
                    'order_product.order_id',
                    'order_product.tracking_url',
                    'order_product.tracking_code',
                    'order_product.shipping_carrier',
                ]);
            }])
            ->whereNotNull('order.received_at');

        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        if ($search) {
            if (is_numeric($search)) {
                $query->where('order.id', (int)$search);
                $perPage = 1;
            } elseif (validateEmail($search)) {
                $query->where('customer_email', $search);
            } else {
                $query->where('order_number', $search);
            }
        }

        return $query
            ->filterReceivedDateRange($time)
            ->orderBy($orderBy, $direction)
            ->paginate($perPage);
    }

    public function getVisitLog(Request $request, $id): JsonResponse
    {
        try {
            $user = currentUser();
            if ($user->hasExactlyRole(SellerTeamRoleEnum::MANAGER_LITE)) {
                abort(403);
            }
            $query = Order::query()
                ->where('id', $id);

            if ($user->isSeller()) {
                $query->where('seller_id', $user->getUserId());
            }

            $sessionId = $query->value('session_id');

            if (empty($sessionId)) {
                throw new RuntimeException('Order not found.');
            }

            $filters = [];
            $filters['session_id'] = $sessionId;
            $filters['is_full'] = $request->boolean('is_full');
            $log = (new Elastic())->getVisitLog($filters);

            return $this->successResponse($log);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateFraudStatus(UpdateFraudStatusRequest $request, $orderId): JsonResponse
    {
        $currentUser = currentUser();
        $currentUser->hasPermissionOrAbort('update_order');
        $fraudStatus = $request->input('fraud_status');
        $flag_log = $request->input('flag_log');
        $order = Order::query()->findOrFail($orderId);
        if ($fraudStatus === OrderFraudStatus::FRAUD) {
            $this->updateScamStatus($order);
        } else if ($fraudStatus === OrderFraudStatus::FLAGGED) {
            if (in_array($order->fraud_status, [OrderFraudStatus::FRAUD, OrderFraudStatus::FLAGGED], true)) {
                return $this->successResponse();
            }
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::CHANGE_FRAUD_STATUS,
                'Changed order fraud status from ' . Str::upper($order->fraud_status ?? 'N/A') . ' to ' . Str::upper($fraudStatus),
            );
            $order->fill(['flag_log' => $flag_log]);
        } else {
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::CHANGE_FRAUD_STATUS,
                'Changed order fraud status from ' . Str::upper($order->fraud_status ?? 'N/A') . ' to ' . Str::upper($fraudStatus),
            );
        }
        $order->fill(['fraud_status' => $fraudStatus])->update();
        return $this->successResponse();
    }

    /**
     * @param BulkUpdateFraudStatusRequest $request
     * @return JsonResponse
     */
    public function bulkUpdateFraudStatus(BulkUpdateFraudStatusRequest $request): JsonResponse
    {
        $currentUser = currentUser();
        $currentUser->hasPermissionOrAbort('update_order');
        $orderIDs = $request->input('order_ids');
        $fraudStatus = $request->input('fraud_status');
        $orders = Order::query()->whereIn('id', $orderIDs)->get();
        if ($orders->isEmpty()) {
            return $this->errorResponse('Orders not found');
        }
        if ($fraudStatus === OrderFraudStatus::FRAUD) {
            foreach ($orders as $order) {
                $order->update(['fraud_status' => OrderFraudStatus::FRAUD]);
                $this->updateScamStatus($order);
            }
        } else {
            foreach ($orders as $order) {
                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::CHANGE_FRAUD_STATUS,
                    'Changed order fraud status from ' . Str::upper($order->fraud_status ?? 'N/A') . ' to ' . Str::upper($fraudStatus),
                );
                $order->update(['fraud_status' => $fraudStatus]);
            }
        }

        return $this->successResponse();
    }

    /**
     * @param Order $order
     * @return void
     */
    private function updateScamStatus(Order $order): void
    {
        $currentUser = currentUser();
        if ($order->ip_address) {
            IpInfo::query()->updateOrCreate(['ip_address' => $order->ip_address], ['status' => CheckScamStatusEnum::FLAGGED]);
        }

        if ($order->device_id) {
            DeviceInfo::query()->updateOrCreate(['device_id' => $order->device_id], ['status' => CheckScamStatusEnum::FLAGGED]);
        }
        $staff_name = $currentUser->getName() ?? $currentUser->getEmail();
        OrderHistory::insertLog(
            $order,
            OrderHistoryActionEnum::CHANGE_FRAUD_STATUS,
            'Changed order fraud status from ' . Str::upper($order->fraud_status ?? 'N/A') . ' to ' . Str::upper(OrderFraudStatus::FRAUD),
        );
        sendLogOrderFraudToDiscord($order, OrderFraudStatus::FRAUD, $staff_name);
        if ($order->seller_id) {
            SellerHistory::query()->insert(array(
                'seller_id' => $order->seller_id,
                'action' => SellerHistoryActionEnum::MARK_FRAUD_ORDER,
                'details' => "The staff member \"" . $staff_name . "\" has marked fraud Order Id #" . $order->id,
                'staff_id' => $currentUser->getUserId(),
            ));
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function sendOrderConfirmation(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => ['required', 'exists:App\Models\Order,id']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $orderId = $request->post('order_id');
        $order = Order::where('id', $orderId)->first();

        if ($order === null) {
            return $this->errorResponse('Get order failed');
        }

        // (new EmailController())->orderConfirmationEmail($order);
        SendBuyerOrderConfirmationJob::dispatchAfterResponse($order);

        return $this->successResponse();
    }

    public function updateShippingMethod(Request $request, $id): JsonResponse
    {
        try {
            $order = Order::query()
                ->filterFulfill(isListing: true)
                ->findOrFail($id);
            $old_shipping_method = $order->shipping_method;
            $order->shipping_method = $request->get('shipping_method');
            $order->save();

            OrderHistory::insertLog(
                $order->refresh(),
                OrderHistoryActionEnum::ADMIN_ACTION,
                currentUser()->getInfo()->name . ' changed shipping method from ' . $old_shipping_method . ' to ' . $order->shipping_method,
            );

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function sendOrderToPB(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'order_id' => ['required', 'exists:App\Models\Order,id']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $orderId = $request->post('order_id');
        $order = Order::find($orderId);
        SendOrderToPBJob::dispatch($order);

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function adminCalculateAutoRefund(Request $request): JsonResponse
    {
        $user = currentUser();

        if (!$user->isAdmin()) {
            abort(401);
        }

        $orderId = $request->input('order_id');
        $products = $request->post('products');
        $fullRefund = $request->boolean('full_refund');

        $order = Order::query()->where('id', $orderId)
            ->with([
                'products' => function ($query) use ($user) {
                    $query->onSellerConnection($user);
                    $query->whereNotIn('order_product.fulfill_status', [
                        OrderProductFulfillStatus::CANCELLED,
                    ]);
                }
            ])
            ->first();
        if (empty($order)) {
            return $this->errorResponse('Order not found');
        }
        if (!in_array($order->payment_status, [
            OrderPaymentStatus::PARTIALLY_REFUNDED,
            OrderPaymentStatus::PAID,
        ], true)) {
            return $this->errorResponse('Order status invalid');
        }
        if ($order->payment_status === OrderPaymentStatus::PARTIALLY_REFUNDED) {
            $order->calculateOrder();
            $order->calculateAndSaveOrderServiceAndFulfillFee(false);
        }
        $order->rememberOldData();

        foreach ($order->products as $product) {
            $productIdx = array_search($product->id, array_column($products, 'id'));
            if (is_numeric($productIdx) && $productIdx >= 0) {
                $product->quantity -= $products[$productIdx]['refund_quantity'];
            }
        }

        $order->calculateOrder();
        $order->calculateAndSaveOrderServiceAndFulfillFee(false);

        foreach ($order->products as $product) {
            $productIdx = array_search($product->id, array_column($products, 'id'));
            if (is_numeric($productIdx) && $productIdx >= 0) {
                $refundToArtist = $product->old_artist_profit - $product->artist_profit;
                switch ($order->type) {
                    case OrderTypeEnum::REGULAR:
                    case OrderTypeEnum::CUSTOM:
                        $products[$productIdx]['artist_refund'] = $refundToArtist;
                        break;
                }
            }
        }

        $customerRefund = 0;
        $sellerRefund = 0;

        switch ($order->type) {
            case OrderTypeEnum::REGULAR:
                $customerRefund = $order->old_total_amount - $order->total_amount;
                $sellerRefund = $order->old_total_seller_profit - $order->total_seller_profit;
                break;
            case OrderTypeEnum::CUSTOM:
                $customerRefund = $order->old_total_amount - $order->total_amount;
                if ($order->sen_fulfill_status === OrderSenFulfillStatus::YES) {
                    $sellerRefund = $order->old_total_fulfill_fee - $order->total_fulfill_fee + $order->old_processing_fee - $order->processing_fee;
                }
                break;
            case OrderTypeEnum::FULFILLMENT:
            case OrderTypeEnum::FBA:
                $sellerRefund = $order->old_total_amount - $order->total_amount;
                break;
        }

        if ((float)$sellerRefund < 0 || (float)$customerRefund < 0) {
            return $this->errorResponse('Refund amount invalid', customData: [
                'customer_refund' => $customerRefund,
                'seller_refund' => $sellerRefund,
            ]);
        }

        if ($fullRefund) {
            $customerRefund += $order->tip_amount + $order->insurance_fee;
        }

        return $this->successResponse([
            'customer_refund' => $customerRefund,
            'seller_refund' => $sellerRefund,
            'products' => $products,
        ]);
    }

    /**
     * Make auto refund
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function adminCreateAutoRefund(Request $request): JsonResponse
    {
        $user = currentUser();

        if (!$user->isAdmin()) {
            abort(401);
        }

        $currentUserEmail = $user->getEmail();
        $orderId = $request->input('order_id');
        $refundToGateway = $request->input('refund_to_gateway');
        $products = array_filter_empty($request->post('products'));
        $refundToCustomer = (float)$request->post('refund_to_customer');
        $refundToSeller = $request->post('refund_to_seller');
        $fullRefund = $request->boolean('full_refund');
        $order = Order::query()
            ->where('id', $orderId)
            ->with([
                'products' => function ($query) use ($user) {
                    $query->onSellerConnection($user);
                    $query->whereNotIn('order_product.fulfill_status', [
                        OrderProductFulfillStatus::CANCELLED,
                    ]);
                }
            ])
            ->firstOrFail();

        if (!in_array($order->payment_status, [
            OrderPaymentStatus::PARTIALLY_REFUNDED,
            OrderPaymentStatus::PAID,
        ], true)) {
            return $this->errorResponse('Order status invalid');
        }

        DB::beginTransaction();
        try {
            $artistRefund = [];
            $refundDetail = '';
            $countRefundItems = 0;

            $cloneOrder = $order->replicate();
            if ($cloneOrder->payment_status === OrderPaymentStatus::PARTIALLY_REFUNDED) {
                $cloneOrder->calculateOrder();
                $cloneOrder->calculateAndSaveOrderServiceAndFulfillFee(false);
            }
            $cloneOrder->rememberOldData();

            foreach ($cloneOrder->products as $product) {
                $productIdx = array_search($product->id, array_column($products, 'id'));

                if (is_numeric($productIdx) && $productIdx >= 0) {
                    $product->quantity -= $products[$productIdx]['refund_quantity'];
                    $countRefundItems += $products[$productIdx]['refund_quantity'];
                }
            }

            $cloneOrder->calculateOrder();
            $cloneOrder->calculateAndSaveOrderServiceAndFulfillFee(false);

            $trackingInfo = [];

            foreach ($cloneOrder->products as $product) {
                $productIdx = array_search($product->id, array_column($products, 'id'));

                if (is_numeric($productIdx) && $productIdx >= 0) {
                    $quantityLeft = $product->quantity;
                    $refundToArtist = !empty($products[$productIdx]) ? $products[$productIdx]['artist_refund'] : 0;
                    $refundQuantity = !empty($products[$productIdx]) ? $products[$productIdx]['refund_quantity'] : 0;

                    if (!in_array($cloneOrder->type, [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA], true)) {
                        $artistRefund[$product->seller_id] = $refundToArtist;
                    }

                    $product->artist_profit = $product->old_artist_profit - $refundToArtist;

                    if (!is_null($product->tracking_code) || !is_null($product->shipping_carrier)) {
                        $trackingInfo[$product->id] = [
                            'tracking_code' => $product->tracking_code,
                            'shipping_carrier' => $product->shipping_carrier,
                            'tracking_url' => $product->tracking_url,
                        ];
                    }

                    if ($quantityLeft > 0) {
                        $newProduct = $product->replicate(['id'])->fill([
                            'seller_profit' => $product->old_seller_profit - $product->seller_profit,
                            'artist_profit' => $product->old_artist_profit - $product->artist_profit,
                            'shipping_cost' => $product->old_shipping_cost - $product->shipping_cost,
                            'discount_amount' => $product->old_discount_amount - $product->discount_amount,
                            'quantity' => $product->old_quantity - $product->quantity,
                            'fulfill_status' => OrderProductFulfillStatus::CANCELLED,
                            'tracking_code' => null,
                            'shipping_carrier' => null,
                            'tracking_url' => null
                        ]);
                        $newProduct->removeOldData();
                        $newProduct->save();

                        if (!empty($trackingInfo[$product->id])) {
                            $trackingInfo[$newProduct->id] = $trackingInfo[$product->id];
                            unset($trackingInfo[$product->id]);
                        }
                    } else {
                        $product->seller_profit = $product->old_seller_profit;
                        $product->quantity = $product->old_quantity;
                        $product->artist_profit = $product->old_artist_profit;
                        $product->shipping_cost = $product->old_shipping_cost;
                        $product->total_amount = $product->old_total_amount;
                        $product->discount_amount = $product->old_discount_amount;
                        $product->fulfill_status = OrderProductFulfillStatus::CANCELLED;
                        $product->tracking_code = null;
                        $product->shipping_carrier = null;
                        $product->tracking_url = null;
                    }

                    $product->removeOldData();
                    $product->save();

                    $refundDetail .= $product->product_name .
                        ' - ' . 'qty: ' . $refundQuantity
                        . ($quantityLeft === 0 ? ' - cancelled' : '')
                        . ((!empty($artistRefund[$product->seller_id]) && $artistRefund[$product->seller_id] > 0) ? ' - artist refund $' . $artistRefund[$product->seller_id] : '')
                        . ($productIdx < count($products) - 1 ? ' - ' : '');
                }
            }

            $refundDetail .= ($refundToSeller > 0 ? ' | Seller refund $' . $refundToSeller : '') . ($refundToCustomer > 0 ? ' | Customer refund $' . $refundToCustomer : '');

            $amountDiffRefundable = $order->total_amount / 1000;

            if (abs($refundToCustomer - $cloneOrder->old_total_amount) <= $amountDiffRefundable) {
                $refundToCustomer = $cloneOrder->old_total_amount;
            } else if ($refundToCustomer > $cloneOrder->old_total_amount) {
                return $this->errorResponse('Refund amount invalid');
            }

            foreach ($artistRefund as $sellerId => $refundAmount) {
                $refundAmount = number_format($refundAmount, 2);
                $artist = User::query()->firstWhere('id', $sellerId);

                if ($artist) {
                    $artist->updateBalance(-$refundAmount, SellerBillingType::REFUND, 'Refund order #' . $cloneOrder->order_number, $order->id);
                }
            }

            $seller = User::query()->firstWhere('id', $cloneOrder->seller_id);

            if (!$seller) {
                return $this->errorResponse('Seller not found');
            }

            $correctSellerRefund = 0;
            switch ($cloneOrder->type) {
                case OrderTypeEnum::REGULAR:
                    $correctSellerRefund = $cloneOrder->old_total_seller_profit - $cloneOrder->total_seller_profit;
                    break;
                case OrderTypeEnum::CUSTOM:
                    if ($cloneOrder->sen_fulfill_status === OrderSenFulfillStatus::YES) {
                        $correctSellerRefund = $cloneOrder->old_total_fulfill_fee - $cloneOrder->total_fulfill_fee + $cloneOrder->old_processing_fee - $cloneOrder->processing_fee;
                    }
                    break;
                case OrderTypeEnum::FULFILLMENT:
                case OrderTypeEnum::FBA:
                    $correctSellerRefund = $cloneOrder->old_total_amount - $cloneOrder->total_amount;
                    break;
            }

            if (abs($refundToSeller - $correctSellerRefund) <= $correctSellerRefund / 1000) {
                $refundToSeller = $correctSellerRefund;
            }

            if (is_null($refundToSeller)) {
                DB::rollBack();
                return $this->errorResponse('Refund amount invalid');
            }
            $refundReason = 'Refund order #' . $cloneOrder->order_number;
            $refundAmountValue = $refundToSeller;
            switch ($cloneOrder->type) {
                case OrderTypeEnum::CUSTOM:
                    if ($cloneOrder->sen_fulfill_status === OrderSenFulfillStatus::YES) {
                        $seller->updateBalance($refundToSeller, SellerBillingType::REFUND, $refundReason, $order->id);
                    } else {
                        // recalculate processing & fulfill fee
                        $order->processing_fee = $cloneOrder->processing_fee;
                        $order->total_fulfill_fee = $cloneOrder->total_fulfill_fee;
                    }
                    break;
                case OrderTypeEnum::FULFILLMENT:
                case OrderTypeEnum::FBA:
                    $seller->updateBalance($refundToSeller, SellerBillingType::REFUND, $refundReason, $order->id);
                    break;
                default:
                    $refundAmountValue = -$refundToSeller;
                    $seller->updateBalance(-$refundToSeller, SellerBillingType::REFUND, $refundReason, $order->id);
                    break;
            }
            // Send email to seller when balance updated
            $subject = 'Balance updated: ';
            if ($refundAmountValue > 0) {
                $subject .= ' +';
            }
            $subject .= UserService::formatCurrency($refundAmountValue);
            SendSellerBalanceUpdateNotification::dispatchAfterResponse([
                'email' => $seller->email,
                'subject' => $subject,
                'amount' => UserService::formatCurrency($refundAmountValue),
                'balance' => UserService::formatCurrency($seller->balance),
                'reason' => $refundReason
            ]);

            $totalRefund = in_array($cloneOrder->type, [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA], true) ? $refundToSeller : $refundToCustomer;

            if (empty($cloneOrder->total_refund)) {
                $cloneOrder->total_refund = $totalRefund;
            } else {
                $cloneOrder->total_refund += $totalRefund;
            }

            if ($cloneOrder->total_seller_profit < 0) {
                $cloneOrder->total_seller_profit = round($cloneOrder->total_seller_profit, 2);
            }

            $countProductNotCancelled = $cloneOrder->products
                ->filter(fn ($product) => $product->fulfill_status !== OrderProductFulfillStatus::CANCELLED)
                ->count();

            if ($countProductNotCancelled === 0) {
                $cloneOrder->payment_status = OrderPaymentStatus::REFUNDED;
                $cloneOrder->status = OrderPaymentStatus::REFUNDED;
                if ($cloneOrder->fulfill_status !== OrderFulfillStatus::FULFILLED) {
                    $cloneOrder->fulfill_status = OrderFulfillStatus::CANCELLED;
                }
            } else if (($countRefundItems === 0 && $refundToCustomer > 0) || $countRefundItems > 0) {
                $cloneOrder->payment_status = OrderPaymentStatus::PARTIALLY_REFUNDED;
            }

            if (($countRefundItems === 0 && $refundToSeller > 0)) {
                $cloneOrder->total_seller_profit -= $refundToSeller;
            }

            // if order is designing, check if order is invalid or reviewing
            if ($cloneOrder->fulfill_status === OrderFulfillStatus::DESIGNING) {
                $invalidOrderProducts = $cloneOrder->products->filter(fn ($product) => $product->fulfill_status === OrderProductFulfillStatus::INVALID);
                $reviewingOrderProducts = $cloneOrder->products->filter(fn ($product) => $product->fulfill_status === OrderProductFulfillStatus::REVIEWING);
                $orderFulfillStatus = OrderFulfillStatus::UNFULFILLED;
                if ($invalidOrderProducts->isNotEmpty()) {
                    $orderFulfillStatus = OrderFulfillStatus::INVALID;
                } else if ($reviewingOrderProducts->isNotEmpty()) {
                    $orderFulfillStatus = OrderFulfillStatus::REVIEWING;
                }
                $cloneOrder->fulfill_status = $orderFulfillStatus;
            }
            $reason = '';

            if (!is_null($request->post('reason'))) {
                $reason = $request->post('reason');
            }

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::REFUNDED,
                'Reason: ' . $reason . ' | ' . $refundDetail,
                OrderHistoryDisplayLevelEnum::CUSTOMER,
            );

            if (number_format($totalRefund, 2) > number_format($cloneOrder->old_total_amount, 2)) {
                DB::rollBack();
                return $this->errorResponse('Refund amount invalid');
            }

            $order->total_refund = $cloneOrder->total_refund;
            $order->total_seller_profit = $cloneOrder->total_seller_profit;
            $order->payment_status = $cloneOrder->payment_status;
            $order->status = $cloneOrder->status;
            $order->fulfill_status = $cloneOrder->fulfill_status;
            $order->save();

            if (!empty($refundToGateway) && (int)$refundToGateway === 1 && !in_array($cloneOrder->type, [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA]) && $totalRefund > 0) {
                if (abs($totalRefund - $cloneOrder->old_total_amount) <= $amountDiffRefundable) {
                    $totalRefund = $cloneOrder->old_total_amount;
                } else if ($totalRefund > $cloneOrder->old_total_amount) {
                    return $this->errorResponse('Refund amount invalid');
                }

                if (empty($order->payment_gateway_id)) {
                    DB::rollBack();
                    return $this->errorResponse('Order payment gateway not found');
                }

                PaymentGatewayRefund::query()->create([
                    'payment_gateway_id' => $order->payment_gateway_id,
                    'order_id' => $order->id,
                    'seller_id' => $order->seller_id,
                    'store_id' => $order->store_id,
                    'staff_id' => $user->getUserId(),
                    'refund_amount' => $totalRefund,
                    'is_full_refund' => (int) $fullRefund,
                    'reason' => $request->post('reason')
                ]);
            }

            DB::commit();

            foreach ($trackingInfo as $orderProductId => $tracking) {
                $order->admin_note .= 'OID: ' . $orderProductId
                    . ' Tracking code: ' . $tracking['tracking_code']
                    . ' - Shipping carrier: ' . $tracking['shipping_carrier']
                    . ' - Tracking url: ' . $tracking['tracking_url'] . "\n";
            }

            $request = new Request();
            $request->merge(
                [
                    'order_id' => $order->id,
                    'support_status' => 0,
                    'column' => 'admin_note',
                    'note' => $order->admin_note,
                    'assignee' => 0,
                    'email' => $currentUserEmail
                ]
            );
            // Call event Order Update
            OrderUpdated::dispatch($orderId); // Refund order
            OrderRefundedEvent::dispatch($orderId);
            UpdateSupport::dispatch($request);

            // Call event KlaviyoOrderRefunded for tracking Refund Order
            KlaviyoOrderRefunded::dispatch($orderId);

            // Call event KlaviyoOrderCancelled for tracking Cancel Order
            KlaviyoOrderCancelled::dispatch($orderId);

            // Tính toán lại giá vận chuyển và lợi nhuận của sup cho đơn hàng
            OrderCostStatisticsJob::dispatchAfterResponse($order->id);

            return $this->successResponse(null, 'Refund order success');
        } catch (Exception $exception) {
            DB::rollBack();
            logException($exception);
            return $this->errorResponse('Refund order failed');
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function adminCreateManualRefund(Request $request) {
        $user = currentUser();
        if (!$user->isAdmin()) {
            abort(401);
        }

        $currentStaffEmail = $user->getEmail();
        $orderId = $request->input('order_id');
        $refundToCustomer = (float)$request->post('refund_to_customer');
        $refundToSeller = (float)$request->post('refund_to_seller');
        $products = array_filter_empty($request->post('products'));
        $order = Order::query()
            ->where('id', $orderId)
            ->with([
                'products' => function ($query) use ($user) {
                    $query->onSellerConnection($user);
                    $query->whereNotIn('order_product.fulfill_status', [
                        OrderProductFulfillStatus::CANCELLED,
                    ]);
                }
            ])
            ->firstOrFail();

        if (!in_array($order->payment_status, [
            OrderPaymentStatus::PARTIALLY_REFUNDED,
            OrderPaymentStatus::PAID,
        ], true)) {
            return $this->errorResponse('Order status invalid');
        }

        if ($order->isServiceOrder() || $order->isCustomServiceOrder()) {
            return $this->errorResponse('Order invalid');
        }

        if ($refundToCustomer < 0) {
            return $this->errorResponse('refund to seller and customer are invalid');
        }

        DB::beginTransaction();
        try {
            $cloneOrder = $order->replicate();
            $cloneOrder->rememberOldData();

            if ($cloneOrder->total_seller_profit < 0) {
                $cloneOrder->total_seller_profit = round($cloneOrder->total_seller_profit, 2);
            }

            // if order is designing, check if order is invalid or reviewing
            if ($cloneOrder->fulfill_status === OrderFulfillStatus::DESIGNING) {
                $invalidOrderProducts = $cloneOrder->products->filter(fn ($product) => $product->fulfill_status === OrderProductFulfillStatus::INVALID);
                $reviewingOrderProducts = $cloneOrder->products->filter(fn ($product) => $product->fulfill_status === OrderProductFulfillStatus::REVIEWING);
                $orderFulfillStatus = OrderFulfillStatus::UNFULFILLED;
                if ($invalidOrderProducts->isNotEmpty()) {
                    $orderFulfillStatus = OrderFulfillStatus::INVALID;
                } else if ($reviewingOrderProducts->isNotEmpty()) {
                    $orderFulfillStatus = OrderFulfillStatus::REVIEWING;
                }
                $cloneOrder->fulfill_status = $orderFulfillStatus;
            }

            $reason = '';
            if (!is_null($request->post('reason'))) {
                $reason = $request->post('reason');
            }
            $refundDetail = ($refundToSeller > 0 ? ' | Seller refund $' . $refundToSeller : '') . ($refundToCustomer > 0 ? ' | Customer refund $' . $refundToCustomer : '') . ($refundToSeller < 0 ? ' | Charge seller profit -$' . abs($refundToSeller) : '');
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::REFUNDED,
                'Reason: ' . $reason . ' | ' . $refundDetail,
                OrderHistoryDisplayLevelEnum::CUSTOMER,
            );
            /** @var OrderService $orderService */
            $orderService = app(OrderService::class);
            if ($refundToSeller !== 0) {
                $orderService->refundToSeller(
                    $order,
                    $cloneOrder,
                    $refundToSeller,
                    $products
                );
            }

            if ($refundToCustomer > 0) {
                $amountDiffRefundable = $order->total_amount / 1000;

                if (abs($refundToCustomer - $cloneOrder->old_total_amount) <= $amountDiffRefundable) {
                    $refundToCustomer = $cloneOrder->old_total_amount;
                } else if ($refundToCustomer > $cloneOrder->old_total_amount) {
                    return $this->errorResponse('Refund amount invalid');
                }

                if (empty($cloneOrder->total_refund)) {
                    $cloneOrder->total_refund = $refundToCustomer;
                } else {
                    $cloneOrder->total_refund += $refundToCustomer;
                }

                if (number_format($refundToCustomer, 2) > number_format($cloneOrder->old_total_amount, 2)) {
                    DB::rollBack();
                    return $this->errorResponse('Refund amount invalid');
                }

                if (empty($order->payment_gateway_id)) {
                    DB::rollBack();
                    return $this->errorResponse('Order payment gateway not found');
                }

                $orderService->refundToCustomer(
                    $order,
                    $user,
                    $refundToCustomer,
                    $reason
                );
            }

            $order->total_refund = $cloneOrder->total_refund;
            $order->payment_status = OrderPaymentStatus::PARTIALLY_REFUNDED;
            $order->fulfill_status = $cloneOrder->fulfill_status;
            $order->save();

            DB::commit();

            // Call event Order Update
            OrderUpdated::dispatch($orderId); // Refund order
            $orderService->updateSupportOnManualRefund($order, $currentStaffEmail, $refundToSeller, $refundToCustomer, $reason);
            return $this->successResponse(null, 'Refund order success');
        } catch (Exception $exception) {
            DB::rollBack();
            logException($exception);
            return $this->errorResponse('Refund order manual failed');
        }
    }

    public function sellerExportCustomOrders(Request $request)
    {
        $currentUserId = currentUser()->getUserId();

        $keywords = $request->get('q');
        $status = $request->get('status');
        $type = $request->get('type', OrderTypeEnum::REGULAR);
        $fraudStatus = $request->get('fraud_status');
        $fulfillStatus = $request->get('fulfill_status');
        $dateRange = $request->get('time');
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $sortBy = $request->get('sort');
        $country = $request->get('country');
        $payment_gateway_id = $request->get('payment_gateway_id');
        $sortDirection = $request->get('direction');

        $query = OrderProduct::query()
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->select([
                'order.order_number as order_number',
                'order_product.id as order_product_id',
                'order_product.sku as product_sku',
                'order_product.campaign_title as campaign_title',
                'order_product.product_name as product_name',
                'order_product.quantity as quantity',
                'order.store_domain as store_domain',
                'order.customer_name as customer_name',
                'order.customer_email as customer_email',
                'order.customer_phone as customer_phone',
                'order.address as address',
                'order.address_2 as address_2',
                'order.city as city',
                'order.state as state',
                'order.postcode as postcode',
                'order.country as country',
                'order.order_note as order_note',
                'order.status as order_status',
                'order_product.fulfill_status as fulfill_status',
                'order_product.shipping_carrier as shipping_carrier',
                'order_product.tracking_code as tracking_code',
                'order_product.tracking_url as tracking_url',
                'order.paid_at as paid_at',
                'order_product.product_url as product_url',
                'order_product.thumb_url as mockup_url',
                'order_product.options as options',
                'order_product.custom_options as custom_options',
                'order_product.personalized as personalized',
                'order.ad_campaign',
                'order.store_domain',
                'order.transaction_id'
            ])
            ->selectRaw('IF(order.store_id > 0, CONCAT("https://", order.store_domain, order_product.product_url), null) as campaign_url')
            ->where([
                'order.seller_id' => $currentUserId
            ]);

        $keywords = trim($keywords);
        if (!empty($keywords)) {
            $query->where(function ($q) use ($keywords) {
                return $q->orWhere('order_product.campaign_title', 'like', '%' . $keywords . '%')
                    ->orWhere('order.order_number', 'like', '%' . $keywords . '%')
                    ->orWhere('order.order_number_2', 'like', '%' . $keywords . '%')
                    ->orWhere('order.customer_name', 'like', '%' . $keywords . '%')
                    ->orWhere('order.customer_email', 'like', '%' . $keywords . '%');
            });
        }

        $excludeStatus = [
            OrderStatus::DRAFT,
            OrderStatus::DELETED,
        ];
        if ($type === OrderTypeEnum::FULFILLMENT) {
            $excludeStatus = [OrderStatus::DELETED];
        }

        $includePending = $status === OrderStatus::PENDING && $type !== OrderTypeEnum::REGULAR && $type !== OrderTypeEnum::CUSTOM;
        if (!$includePending) {
            $excludeStatus[] = OrderStatus::PENDING;
        }

        $query->whereNotIn('order.status', $excludeStatus);

        if (!empty($status) && $status !== 'all') {
            $query->where('order.status', $status);
        }
        $query->when(!empty($payment_gateway_id), function ($q) use ($payment_gateway_id) {
            return $q->where('order.payment_gateway_id', $payment_gateway_id);
        });
        if ($type && in_array(strtolower($type), OrderTypeEnum::getValues(), true)) {
            if ($type === OrderTypeEnum::REGULAR) {
                $query->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM]);
            } else {
                $query->where('order.type', $type);
            }
        }

        if (!is_null($sortBy)) {
            $query->orderBy($sortBy, $sortDirection);
        } else if ($type === OrderTypeEnum::FULFILLMENT) {
            $query->orderByDesc('order.created_at');
        } else {
            $query->orderByDesc(Order::FILTER_COLUMN_DATE);
        }

        if (!empty($fraudStatus)) {
            $query->where('order.fraud_status', $fraudStatus);
        }

        if (!empty($fulfillStatus)) {
            $query->where('order.fulfill_status', $fulfillStatus);
        }

        if (!empty($country)) {
            $query->where('order.country', $country);
        }

        $query->filterDateRangePaidAt($dateRange, $startDate, $endDate, false);
        $result = $query->get();

        if ($result->count() === 0) {
            return $this->errorResponse('No orders available to export', 403);
        }
        $totalOrder = $result->pluck('order_number')->unique()->count();
        if ($totalOrder > 3000) {
            return $this->errorResponse('The number of orders to export is too large, orders must be less than 3000', 403);
        }
        $result = $result->toArray();
        $result = array_map(function ($product) {
            $options = json_decode($product['options']);
            $customOptions = json_decode($product['custom_options']);
            $paidAt = new Carbon($product['paid_at']);
            $product['paid_at'] = $paidAt->toDateString();
            $product['product_url'] = 'https://' . $product['store_domain'] . $product['product_url'];
            $product['mockup_url'] = imgUrl($product['mockup_url']);
            unset($product['options']);
            if (!empty($options)) {
                $optionsKey = get_object_vars($options);
                foreach ($optionsKey as $key => $value) {
                    $product['option:' . $key] = $value;
                }
            }
            $product['custom_options'] = '';

            if (!empty($customOptions) && $product['personalized'] === PersonalizedType::CUSTOM_OPTION) {
                foreach ($customOptions as $cIdx => $cOption) {
                    if (!empty($cOption)) {
                        if (isset($cOption->text) || isset($cOption->dropdown) || isset($cOption->image)) {
                            $nOption = [];

                            foreach ($cOption as $kOp => $op) {
                                if ($op->active) {
                                    $op->type = $kOp;
                                    $nOption[] = $op;
                                }
                            }

                            $cOption = $nOption;
                        }

                        foreach ($cOption as $dOp) {
                            $value = $dOp->type === CustomOptionTypeEnum::IMAGE ? (!empty($dOp->imagePath) ? s3Url($dOp->imagePath) : '') : $dOp->value;
                            if (is_array($value)) {
                                $value = implode(' | ', $value);
                            }
                            $product['custom_options'] .= $dOp->label . ' ' . ($cIdx + 1) . ': ' . $value . " \r\n";
                        }
                    }
                }
            }

            unset($product['personalized']);
            return $product;
        }, $result);

        $headings = array_keys($result[0]);
        $fileName = 'Senprints_custom_order_export_' . Carbon::now()->toDateTimeLocalString() . '.csv';
        $export = new ExportCustomOrderToExcel($result, $headings);

        return Excel::download($export, $fileName);
    }

    public function sellerExportCustomOrdersV2(Request $request)
    {
        $keywords = $request->get('q');
        $status = $request->get('status');
        $type = $request->get('type');
        $fulfillStatus = $request->get('fulfill_status');
        $dateRange = $request->get('time', DateRangeEnum::THIS_MONTH);
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $country = $request->get('country');
        $payment_gateway_id = $request->get('payment_gateway_id');
        $storeId = $request->get('store_id');
        $limit = 100000;

        if (empty($dateRange)) {
            $dateRange = DateRangeEnum::LIFE_TIME;
        }
        if (!in_array($dateRange, [DateRangeEnum::CUSTOM, DateRangeEnum::TODAY, DateRangeEnum::YESTERDAY, DateRangeEnum::THIS_WEEK, DateRangeEnum::LAST_WEEK, DateRangeEnum::THIS_MONTH, DateRangeEnum::LAST_MONTH, DateRangeEnum::LIFE_TIME], true)) {
            return $this->errorResponse('Date range invalid', 403);
        }
        $user = currentUser();
        $currentUserId = $user->getUserId();
        $excludeStatus = [
            OrderStatus::DRAFT,
            OrderStatus::DELETED,
        ];

        $query = OrderProduct::query()
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->select([
                'order.order_number as order_number',
                'order.id as order_id',
                'order.customer_name as customer_name',
                'order.customer_email as customer_email',
                'order.customer_phone as customer_phone',
                'order.address as address',
                'order.country as country',
                'order.city as city',
                'order.state as state',
                'order.postcode as postcode',
                'order.status as order_status',
                'order.paid_at as paid_at',
                'order_product.product_name as product_name',
                'order_product.campaign_title as campaign_title',
                'order_product.sku as sku',
                'order_product.price as price',
                'order_product.quantity as quantity',
                'order_product.total_amount as total_amount',
                'order_product.discount_amount as discount_amount',
                'order_product.seller_profit as total_profit',
                'order_product.artist_profit',
                'order_product.fulfill_status as fulfill_status',
                'order.insurance_fee as order_insurance_fee',
                'order.tip_amount as order_tip_amount',
                'order.ad_campaign',
                'order.store_domain',
                'order.transaction_id',
                'order.discount_code',
                'order_product.tracking_code',
                'order_product.shipping_carrier',
            ])
            ->selectRaw('IF(order.store_id > 0, CONCAT("https://", order.store_domain, order_product.product_url), null) as campaign_url')
            ->where('order.seller_id', $currentUserId);

        $includePending = $status === OrderStatus::PENDING && $type !== OrderTypeEnum::REGULAR && $type !== OrderTypeEnum::CUSTOM;
        if (!$includePending) {
            $excludeStatus[] = OrderStatus::PENDING;
        }

        $keywords = trim($keywords);
        if (!empty($keywords)) {
            $query->where(function ($q) use ($keywords) {
                return $q->orWhere('order.order_number', 'like', '%' . $keywords . '%')
                    ->orWhere('order.order_number_2', 'like', '%' . $keywords . '%')
                    ->orWhere('order.customer_name', 'like', '%' . $keywords . '%')
                    ->orWhere('order.customer_email', 'like', '%' . $keywords . '%')
                    ->orWhere('order_product.campaign_title', 'like', '%' . $keywords . '%');
            });
        }
        $query->when(!empty($payment_gateway_id), function ($q) use ($payment_gateway_id) {
            return $q->where('order.payment_gateway_id', $payment_gateway_id);
        });
        $query->whereNotIn('order.status', $excludeStatus);
        if (!empty($status) && $status !== 'all') {
            $query->where('order.status', $status);
        }

        if ($type && in_array(strtolower($type), OrderTypeEnum::getValues(), true)) {
            if ($type === OrderTypeEnum::REGULAR) {
                $query->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM]);
            } else {
                $query->where('order.type', $type);
            }
        }

        if (!empty($fulfillStatus)) {
            $query->where('order_product.fulfill_status', $fulfillStatus);
        }

        if (!empty($storeId)) {
            $query->where('order.store_id', $storeId);
        }

        if (!empty($country)) {
            $query->where('order.country', $country);
        }

        $result = $query->filterDateRangePaidAt($dateRange, $startDate, $endDate)->orderByDesc('order_id')->limit($limit)->get();
        if ($result->count() === 0) {
            return $this->errorResponse('No orders available to export');
        }
        $totalOrder = $result->pluck('order_number')->unique()->count();
        if ($totalOrder > 5000) {
            return $this->errorResponse('The number of orders to export is too large, orders must be less than 5000', 403);
        }
        $result = $result->toArray();
        $sellerUTCOffset = get_utc_offset_by_user_or_store(currentUser()->getUserId());
        $result = array_map(function ($product) use ($sellerUTCOffset) {
            $paidAt = new Carbon($product['paid_at']);
            $product['paid_at'] =  $paidAt->addRealHours($sellerUTCOffset)->format('m/d/Y g:i A');

            if (!empty($product['total_amount'])) {
                $product['total_amount'] = round((float)$product['total_amount'], 2);
            }

            if (!empty($product['discount_amount'])) {
                $product['discount_amount'] = round((float)$product['discount_amount'], 2);
            }

            if (!empty($product['total_profit'])) {
                $product['total_profit'] = round((float)$product['total_profit'], 2);
            }

            return $product;
        }, $result);
        $headings = array_keys($result[0]);
        $fileName = 'Senprints_custom_order_export_' . Carbon::now()->toDateTimeLocalString() . '.csv';
        OrderService::mapHandlefeeOnExportOrder($result, [
            'order_insurance_fee',
            'order_tip_amount'
        ]);
        $export = new ExportCustomOrderToExcel($result, $headings);
        return Excel::download($export, $fileName);
    }

    /**
     * @param SellerImportOrderDesignRequest $request
     * @return JsonResponse
     */
    public function sellerImportDesign(SellerImportOrderDesignRequest $request): ?JsonResponse
    {
        $file = $request->file('file');
        try {
            Excel::import(new OrderDesignImport(), $file);
            return $this->successResponse();
        } catch (Exception $e) {
            $code = $e->getCode();

            if ($code === 2002) {
                return $this->errorResponse('Import failed', 403);
            }

            if ($code === 23000) {
                return $this->errorResponse('Duplicate entry', 403);
            }

            logToDiscord($code . ': Seller import order design failed' . json_encode($e->getMessage()));
            return $this->errorResponse($e->getMessage(), $code > 0 ? $code : 403);
        }
    }

    /**
     * @param BulkUpdateTrademarkStatusRequest $request
     * @return JsonResponse
     */
    public function bulkUpdateTrademarkStatus(BulkUpdateTrademarkStatusRequest $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('review_tm');

        $orderIds = $request->input('order_ids');
        $status = $request->input('tm_status');

        DB::beginTransaction();
        try {
            Order::query()
                ->whereIn('id', $orderIds)
                ->whereIn('tm_status', [TradeMarkStatusEnum::FLAGGED, TradeMarkStatusEnum::VIOLATED, TradeMarkStatusEnum::VERIFIED])
                ->update(['tm_status' => $status]);

            OrderProduct::query()
                ->whereIn('order_id', $orderIds)
                ->whereIn('tm_status', [TradeMarkStatusEnum::FLAGGED, TradeMarkStatusEnum::VIOLATED, TradeMarkStatusEnum::VERIFIED])
                ->update(['tm_status' => $status]);

            if ($status === TradeMarkStatusEnum::VIOLATED) {
                $sellers = User::query()
                    ->with('seller_orders', function ($query) use ($orderIds) {
                        $query->whereIn('order.id', $orderIds);
                    })
                    ->whereHas('seller_orders', function ($query) use ($orderIds) {
                        $query->whereIn('order.id', $orderIds);
                    })
                    ->get();

                if ($sellers->isNotEmpty()) {
                    $sellers->each(function ($seller) {
                        $first_order_id = !empty($seller->seller_orders[0]) ? $seller->seller_orders[0]->id : null;
                        $seller->fill([
                            'status' => UserStatusEnum::FLAGGED,
                            'flag_log' => 'Trademark violation - Order #' . $first_order_id
                        ])->update();
                        $seller->seller_orders->each(function ($order) use ($seller) {
                            SellerHistory::query()->insert(array(
                                'seller_id' => $seller->id,
                                'seller_status' => UserStatusEnum::FLAGGED,
                                'order_id' => $order->id,
                                'details' => 'Trademark violation - Order #' . $order->id,
                            ));
                        });
                    });
                }
            }

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @return LengthAwarePaginator
     */
    public function getOrderCancelRequests(Request $request): LengthAwarePaginator
    {
        $status = $request->input('status');
        $gatewayType = $request->input('gateway_type');
        $keyword = $request->input('q');
        $perPage = $request->input('per_page', 15);
        $query = OrderCancelRequest::query();

        $selectFields = [
            'order_cancel_request.id',
            'order_cancel_request.order_id',
            'order_cancel_request.status',
            'order_cancel_request.created_at',
            'order_cancel_request.updated_at',
            'order_cancel_request.sent_email',
            'order_cancel_request.error_log',
        ];

        $query->select($selectFields);
        $query->join('order', 'order.id', '=', 'order_cancel_request.order_id');

        if (!empty($keyword)) {
            $query->where('order.customer_name', 'like', '%' . $keyword . '%')
                ->orWhere('order.customer_email', 'like', '%' . $keyword . '%')
                ->orWhere('order_cancel_request.order_id', 'like', '%' . $keyword . '%')
                ->orWhere('order.order_number', 'like', '%' . $keyword . '%');
        }

        if (!empty($status)) {
            $query->where('order_cancel_request.status', $status);
        }

        if (!empty($gatewayType)) {
            $query->where('order.payment_method', $gatewayType);
        }

        $query->with([
            'order' => function ($subQuery) {
                $subQuery->with(['store', 'seller']);
            }
        ]);

        $query->orderBy('order_cancel_request.updated_at', 'desc');
        return $query->paginate($perPage);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateStatusOrderCancelRequests(Request $request): JsonResponse
    {
        $ids = $request->input('ids');
        $status = $request->input('status');
        $availableStatus = [OrderCancelRequestStatus::PROCESSING, OrderCancelRequestStatus::CANCELLED];

        if (!in_array($status, $availableStatus, true)) {
            return $this->errorResponse('Invalid status');
        }
        DB::beginTransaction();
        try {
            $query = OrderCancelRequest::query()
                ->whereIn('id', $ids);
            if ($status === OrderCancelRequestStatus::PROCESSING) {
                $query->whereIn('status', [
                    OrderCancelRequestStatus::PENDING,
                    OrderCancelRequestStatus::CONFIRMED,
                    OrderCancelRequestStatus::ERROR,
                ]);
            } else {
                $query->whereNotIn('status', [
                    OrderCancelRequestStatus::CANCELLED,
                    OrderCancelRequestStatus::COMPLETED,
                    OrderCancelRequestStatus::ERROR,
                ]);
            }
            $orders = $query->get();
            if ($orders->isNotEmpty()) {
                foreach ($orders as $order) {
                    OrderHistory::insertLog(
                        $order,
                        $status === OrderCancelRequestStatus::CANCELLED ? OrderHistoryActionEnum::RESUME_FULFILL : OrderHistoryActionEnum::APPROVE_REFUND,
                        $status === OrderCancelRequestStatus::CANCELLED ? 'Staff resume order' : 'Staff approve cancel order request'
                    );
                }
            }
            $countUpdated = $query->update([
                'status' => $status,
                'updated_at' => now()
            ]);
            DB::commit();
            return $this->successResponse([
                'count_updated' => $countUpdated
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Bulk update requests status failed');
        }
    }

    public function scanCompleteOrders(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => ['required'],
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        $order_id = $request->input('order_id');
        $order_id = array_map('trim', explode(',', $order_id));
        $order_id = array_filter($order_id, function ($id) {
            return (int)$id > 0;
        });
        $query = Order::query()
            ->whereIn('id', $order_id)
            ->whereNotNull('payment_gateway_id')
            ->where('customer_email', '!=', '<EMAIL>')
            ->where('payment_method', 'like', 'stripe%')
            ->orderBy('id');
        $pendingPaymentOrders = $query->get();
        if ($pendingPaymentOrders->isEmpty()) {
            graylogInfo('Dont have orders pending payment need to complete.', [
                'category' => 'pending_payment_order',
                'action' => 'manual',
            ]);
            return $this->errorResponse('Dont have orders pending payment need to complete');
        }
        foreach ($pendingPaymentOrders as $order) {
            dispatch(new ScanCompleteOrderPaymentJob($order));
        }
        return $this->successResponse('Start scan this order, please reload the page again.', [
            'order_id' => $pendingPaymentOrders->pluck('id')->toArray(),
        ]);
    }

    public function getMailContent(Request $request, int $orderId): JsonResponse
    {
        $template = $request->get('template');
        $ALLOW = [
            Template::BUYER_ORDER_CONFIRMATION,
            Template::BUYER_ORDER_INVALID_ADDRESS_NOTIFICATION
        ];
        if (!in_array($template, $ALLOW)) {
            return $this->errorResponse('Template not found!');
        }

        try {
            $mailLog = SendMailLog::query()
                ->select('content')
                ->where('order_id', $orderId)
                ->whereIn('status', [SendMailLogStatus::SENT, SendMailLogStatus::RESEND])
                ->where('template', $template)
                ->orderBy('sent_at', 'desc')
                ->first();
            if (is_null($mailLog)) {
                return $this->errorResponse('Mail Log: Content not found!');
            }
            $mailContent = UserService::addUserIdForUrlInMailContent($mailLog->content);
            return $this->successResponse([
                'content' => $mailContent
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function changeFulfillStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => ['required'],
            'from_order_status' => ['required', Rule::in(OrderStatus::getValues())],
            'to_order_status' => ['nullable', Rule::in(OrderStatus::getValues())],
            'from_fulfill_status' => ['required', Rule::in(OrderProductFulfillStatus::getValues())],
            'to_fulfill_status' => ['required', Rule::in(OrderProductFulfillStatus::getValues())],
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        try {
            $order_ids = $request->input('order_id');
            $order_ids = array_map('trim', explode(',', $order_ids));
            $order_ids = array_filter($order_ids, function ($id) {
                return (int)$id > 0;
            });
            if (empty($order_ids)) {
                return $this->errorResponse('Order id invalid');
            }
            $order_ids = Order::query()->select('id')
                ->whereIn('id', $order_ids)
                ->where('status', $request->input('from_order_status'))
                ->where('fulfill_status', $request->input('from_fulfill_status'))
                ->whereNull('deleted_at')
                ->whereNotNull('paid_at')
                ->pluck('id');
            if (empty($order_ids)) {
                return $this->errorResponse('Orders invalid');
            }
            $data['fulfill_status'] = $request->input('to_fulfill_status');
            $to_order_status = $request->input('to_order_status');
            if (!empty($to_order_status)) {
                $data['status'] = $to_order_status;
            }
            $updated = Order::query()->whereIn('id', $order_ids)->update($data);
            if ($updated) {
                return $this->successResponse('Change fulfill status success');
            }
            return $this->errorResponse('Change fulfill status failed');
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param int $orderId
     * @return JsonResponse
     */
    public function updateTrackingStatus($orderId, Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tracking_code' => ['required']
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        $tracking_code = $request->input('tracking_code');
        try {
            OrderService::updateOrderTrackingStatus($orderId, $tracking_code, false);
            return $this->successResponse();
        } catch (\Exception $e) {
            logToDiscord('[17Track] Get tracking info failed - Message: ' . $e->getMessage(), 'tracking_status_logs');
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateFulfilledStatus(Request $request) {
        $validator = Validator::make($request->all(), [
            'delivered.*.order_id' => ['required', 'integer', new CheckExistsIdRule('order')],
            'delivered.*.received_at' => ['required'],
            'delivered.*.tracking_code' => ['required', 'string'],
        ], [
            'delivered.*.order_id.required' => 'The order id field is required.',
            'delivered.*.received_at.required' => 'The received at field is required.',
            'delivered.*.tracking_code.required' => 'The tracking code field is required.',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        try {
            $now = now();
            $delivered = $request->input('delivered');
            $delivered = collect($delivered);
            $orderIds = $delivered->pluck('order_id');
            foreach ($delivered as $item) {
                $received_at = !empty($item['received_at']) ? $item['received_at'] : $now;
                Order::query()->where('id', $item['order_id'])->update(['received_at' => $received_at]);
                OrderProduct::query()->where('tracking_code', $item['tracking_code'])->update([
                    'received_at' => $received_at,
                    'fulfill_status' => OrderProductFulfillStatus::FULFILLED,
                    'shipping_day' => DB::raw('timestampdiff(second, fulfilled_at, now()) / (24 * 60 * 60)'),
                ]);
            }
            $productsOfDeliveredOrders = OrderProduct::query()->whereIn('order_id', $orderIds)->get();
            if ($productsOfDeliveredOrders->isNotEmpty()) {
                $orderIdsHaveAllFulfilledProducts = [];
                $productsOfDeliveredOrders->groupBy('order_id')->each(function ($order, $orderId) use (&$orderIdsHaveAllFulfilledProducts) {
                    $fulfillStatuses = $order->pluck('fulfill_status')->unique();
                    if ($fulfillStatuses->count() === 1 && $fulfillStatuses->first() === OrderProductFulfillStatus::FULFILLED) {
                        $orderIdsHaveAllFulfilledProducts[] = $orderId;
                    }
                });
                if (!empty($orderIdsHaveAllFulfilledProducts)) {
                    Order::query()->whereIn('id', $orderIdsHaveAllFulfilledProducts)->update(['fulfill_status' => OrderFulfillStatus::FULFILLED]);
                }
            }
            return $this->successResponse();
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function refundToGateway(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => [
                Rule::requiredIf(function() use ($request) {
                    return empty($request->get('order_id'));
                })
            ],
            'order_id' => [
                Rule::requiredIf(function() use ($request) {
                    return empty($request->get('id'));
                })
            ]
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        $refund_id = $request->input('id');
        $order_id = $request->input('order_id');
        $refund = PaymentGatewayRefund::query()
            ->with(['order'])
            ->when(!empty($refund_id), function ($q) use ($refund_id) {
                $q->where('id', $refund_id);
            })
            ->when(!empty($order_id), function ($q) use ($order_id) {
                $q->where('order_id', $order_id);
            })
            ->whereIn('status', [PaymentGatewayRefundStatusEnums::CANCELLED, PaymentGatewayRefundStatusEnums::ERROR])
            ->orderByDesc('created_at')
            ->first();

        if(empty($refund)) {
            return $this->errorResponse('Refund not found');
        }
        ProcessRefundJob::dispatchSync($refund);
        return $this->successResponse();
    }

    /**
     * @param     \Illuminate\Http\Request     $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNoShipOrders(Request $request): JsonResponse
    {
        try {
            $result = Order::queryNoShipOrders($request->all(), getHourOffsetBySeller())
                ->paginate($request->get('per_page', 15));

            return $this->successResponse(
                tap($result, static function(LengthAwarePaginator $result) use($request) {
                    $result->withPath($request->get('path', '/'));
                    $result->onEachSide(1);
                })
            );
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getLateProductionOrders(Request $request): JsonResponse
    {
        try {
            $result = Order::queryLateProductionOrders($request->all(), getHourOffsetBySeller());

            $orderIds = $result->pluck('id')->toArray();
            $pagination = $result->paginate($request->get('per_page', 15));

            return $this->successResponse(
                [
                    "orderIds" => $orderIds,
                    "pagination" => tap($pagination, static function(LengthAwarePaginator $pagination) use($request) {
                        $pagination->withPath($request->get('path', '/'));
                        $pagination->onEachSide(1);
                    })
                ]
            );
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param     \Illuminate\Http\Request     $request
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\JsonResponse
     */
    public function exportNoShipOrders(Request $request): BinaryFileResponse|JsonResponse
    {
        try {
            $params = $request->all();
            if (! empty($params['ids'])) {
                $params = ['ids' => to_list_int($params['ids'])];
            }

            if (! currentUser()->isAdmin()) {
                $params['seller_id'] = currentUser()->getUserId();
            }

            $params = array_filter($params, static function($value) {
                if (is_array($value)) {
                    return ! empty($value);
                }

                if (is_string($value)) {
                    return ! empty(trim($value));
                }

                return ! is_null($value);
            });

            $result = Order::queryNoShipOrders($params, getHourOffsetBySeller())->get();
            $fileName = 'no_ship_orders_' . Str::uuid() . '_' . now()->format('Y-m-d') . '.csv';

            return Excel::download(new NoShipOrderExporter($result), $fileName);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function editAddress (Request $request, $orderId): JsonResponse
    {
        $isCustomStore = currentUser()->getInfoAccess()->custom_payment ?? false;
        if (!$isCustomStore) {
            return $this->errorResponse('You do not have permission to edit address');
        }
        $request->validate([
            'customer_email' => 'nullable|email',
            'customer_name' => 'required|string',
            'address' => 'required|string',
            'address_2' => 'nullable|string'
        ]);

        try {
            $order = Order::selectForHistory([
                'id',
                'customer_email',
                'customer_name',
                'customer_phone',
                'address',
                'address_2',
                'city',
                'state',
                'postcode',
                'country',
                'skip_validate_address',
                'processing_fee_paid',
                'sen_fulfill_status',
                'fulfill_status',
                'address_verified',
                'type',
                'house_number',
                'mailbox_number'
            ])->with(['request_cancel' => function ($query) {
                $query->select([
                    'order_id',
                    'status'
                ]);
            }])->find($orderId);
            if (is_null($order) ||
                ($order->address_verified !== OrderAddressVerifiedEnum::INVALID) ||
                ($order->type !== OrderTypeEnum::CUSTOM) ||
                ($order->processing_fee_paid <= 0) ||
                ($order->sen_fulfill_status !== OrderSenFulfillStatus::YES) ||
                ($order->request_cancel && in_array($order->request_cancel->status, [OrderCancelRequestStatus::PROCESSING, OrderCancelRequestStatus::CONFIRMED, OrderCancelRequestStatus::COMPLETED], true))
            ) {
                return $this->errorResponse('This order can not edit address');
            }

            //replicate order
            $oldOrder = $order->toArray();
            //info
            $order->customer_email = $request->input('customer_email', $order->customer_email);
            self::setCustomerInfo($request, $order);
            $order->skip_validate_address = 1;
            $order->address_verified = OrderAddressVerifiedEnum::VERIFIED;
            $order->fulfill_status = OrderFulfillStatus::UNFULFILLED;

            if ($order->isDirty()) {
                $result = $order->save();
                if ($result) {
                    OrderHistory::insertLog(
                        $order,
                        OrderHistoryActionEnum::EDIT_ADDRESS,
                        $order->getChangesDetail2($oldOrder),
                        OrderHistoryDisplayLevelEnum::CUSTOMER
                    );
                }
            }
            return $this->successResponse();
        } catch(Exception $e) {
            return $this->errorResponse('Failed to edit address');
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function reSyncOrdersUsSg(Request $request)
    {
        $user = currentUser();
        $hourOffset = 7;
        $now = now()->addRealHours($hourOffset);
        $validator = Validator::make($request->all(), [
            'start' => [
                Rule::requiredIf(function () use ($request){
                    return empty($request->input('order_id'));
                }),
                'date_format:Y-m-d H:i:s',
                function ($attribute, $value, $fail) use ($now) {
                    $sixHoursAgo = $now->clone()->subHours(6);
                    if (Carbon::parse($value)->lte($sixHoursAgo)) {
                        $fail('The start date must be greater than ' . $sixHoursAgo->toDateTimeString());
                    }
                },
            ],
            'end' => [
                'nullable',
                'date_format:Y-m-d H:i:s',
                function ($attribute, $value, $fail) use ($now) {
                    if (Carbon::parse($value)->gt($now)) {
                        $fail('The end date must be less than or equal to ' . $now->clone()->toDateTimeString());
                    } else {
                        $start = Carbon::parse(request('start'));
                        if (Carbon::parse($value)->lte($start)) {
                            $fail('The end date must be greater than the start date');
                        }
                    }
                },
            ],
            'order_id' => [
                Rule::requiredIf(function () use ($request){
                    return empty($request->input('start'));
                })
            ],
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        $start = $request->input('start');
        $end = $request->input('end');
        if ($start) {
            $start = Carbon::parse($start);
            $start = $start->subRealHours($hourOffset);
            if ($end) {
                $end = Carbon::parse($end);
                $end = $end->subRealHours($hourOffset);
            } else {
                $end = $now->clone();
            }
        }
        $orderId = $request->input('order_id');
        if ($orderId) {
            if (!is_array($orderId)) {
                $orderId = explode(',', $orderId);
            }
        } else {
            $orderId = [];
        }
        try {
            if (empty($orderId)) {
                logToDiscord(($user->getName() ?? $user->getEmail()). " - Confirmed to start sync orders from US to SG: {$request->input('start')} - {$request->input('end')}.", 'admin_warning');
            }
            OrderService::syncDataOrdersFromUsToSg($start, $end, $orderId);
            return $this->successResponse([
                'sub_real_hours' => $hourOffset,
                'actual_start' => $request->input('start'),
                'actual_end' => $request->input('end'),
                'start' => $start->toDateTimeString(),
                'end' => $end?->toDateTimeString(),
                'order_id' => $orderId,
            ]);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function reSyncOrdersFromRegion(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'region' => 'required|string|in:us,sg,eu',
            'order_number' => 'required|array|max:10',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        $region = $request->input('region');
        $orderNumbers = $request->input('order_number');
        try {
            $regionOrders = RegionOrders::onRegion($region)->withTrashed()->whereIn('order_number', $orderNumbers)->get();
            $syncedOrder = [];
            $now = now();
            foreach ($regionOrders as $regionOrder) {
                $regionOrderProducts = RegionOrderProducts::onRegion($region)->where('order_id', $regionOrder->id)->get();
                $regionOrder->setRelation('products', $regionOrderProducts);
                $order = Order::query()->with(['products', 'order_history'])->findByAccessToken($regionOrder->getAccessToken(), true)->first();
                if (!$order) {
                    $order = Order::setRegionCheckout($this->region ?? $regionOrder->region)->firstOrCreate([
                        'access_token' => $regionOrder->getAccessToken(),
                        'order_number' => $regionOrder->getOrderNumber(),
                        'region' => $regionOrder->getRegion(),
                    ]);
                }
                // skip if order is not unfulfilled
                if ($order->getFulfillStatus() !== OrderFulfillStatus::UNFULFILLED) {
                    continue;
                }
                $orderStatusBefore = $order->getStatus();
                $paymentStatusBefore = $order->getPaymentStatus();
                $order->setAccessToken($regionOrder->getAccessToken());
                $order->setAdCampaign($regionOrder->getAdCampaign());
                $order->setAdId($regionOrder->getAdId());
                $order->setAdMedium($regionOrder->getAdMedium());
                $order->setAdSource($regionOrder->getAdSource());
                $order->setCompanyId($regionOrder->getCompanyId());
                $order->setCountry($regionOrder->getCountry());
                $order->setCurrencyCode($regionOrder->getCurrencyCode());
                $order->setDevice($regionOrder->getDevice());
                $order->setDeviceDetail($regionOrder->getDeviceDetail());
                $order->setDeviceId($regionOrder->getDeviceId());
                $order->setFulfillStatus($regionOrder->getFulfillStatus());
                $order->setIpAddress($regionOrder->getIpAddress());
                $order->setIpLocation($regionOrder->getIpLocation());
                $order->setPromotionRule($regionOrder->getPromotionRule());
                $order->setPromotionRuleId($regionOrder->getPromotionRuleId());
                $order->setRefId($regionOrder->getRefId());
                $order->setSessionId($regionOrder->getSessionId());
                $order->setShippingMethod($regionOrder->getShippingMethod());
                $order->setStoreDomain($regionOrder->getStoreDomain());
                $order->setStoreId($regionOrder->getStoreId());
                $order->setSellerId($regionOrder->getSellerId());
                $order->setStoreName($regionOrder->getStoreName());
                $order->setTotalAmount($regionOrder->getTotalAmount());
                $order->setTotalDiscount($regionOrder->getTotalDiscount());
                $order->setDiscountCode($regionOrder->getDiscountCode());
                $order->setTotalPaid($regionOrder->getTotalPaid());
                $order->setRegion($regionOrder->getRegion());
                $order->setOrderNumber($regionOrder->getOrderNumber());
                $order->setTransactionId($regionOrder->getTransactionId());
                $order->setPaymentGatewayId($regionOrder->getPaymentGatewayId());
                $order->setPaymentMethod($regionOrder->getPaymentMethod());
                $order->setPaymentStatus($regionOrder->getPaymentStatus());
                $order->setPaymentFee($regionOrder->getPaymentFee());
                $order->setProcessingFee($regionOrder->getProcessingFee());
                $order->setInsuranceFee($regionOrder->getRawInsuranceFee());
                $order->setTotalFulfillFee($regionOrder->getTotalFulfillFee());
                $order->setTotalQuantity($regionOrder->getTotalQuantity());
                $order->setTotalProductAmount($regionOrder->getTotalProductAmount());
                $order->setTotalShippingAmount($regionOrder->getTotalShippingAmount());
                $order->setTipAmount($regionOrder->getTipAmount());
                $order->setTotalTaxAmount($regionOrder->getTotalTaxAmount());
                $order->setTotalRefund($regionOrder->getTotalRefund());
                $order->setTotalProductCost($regionOrder->getTotalProductCost());
                $order->setTotalProductBaseCost($regionOrder->getTotalProductBaseCost());
                $order->setTotalShippingCost($regionOrder->getTotalShippingCost());
                $order->setTotalSellerProfit($regionOrder->getTotalSellerProfit());
                $order->setTotalProfit($regionOrder->getTotalProfit());
                $order->setBillingAddress($regionOrder->getBillingAddress());
                $order->setShippingAddress($regionOrder->getShippingAddress());
                $order->setProcessingFeePaid($regionOrder->getProcessingFeePaid());
                $order->setFulfillFeePaid($regionOrder->getFulfillFeePaid());
                $order->setRemarketingStatus($regionOrder->getRemarketingStatus());
                $order->setStatus($regionOrder->getStatus());
                $order->setStatusUrl($regionOrder->getStatusUrl());
                $order->setPaymentLog($regionOrder->getPaymentLog());
                $order->setFraudStatus($regionOrder->getFraudStatus());
                $order->setStatsStatus($regionOrder->getStatsStatus());
                $order->setType($regionOrder->getType());
                $order->setAddressVerified($regionOrder->getAddressVerified());
                $order->setOrderNote($regionOrder->getOrderNote());
                $order->setCreatedAt($regionOrder->getCreatedAt());
                $order->setUpdatedAt($regionOrder->getUpdatedAt());
                $order->setFulfilledAt($regionOrder->getFulfilledAt());
                $order->setPaidAt($regionOrder->getPaidAt());
                $order->setDeliveredAt($regionOrder->getDeliveredAt());
                $order->setReceivedAt($regionOrder->getReceivedAt());
                $order->setCurrencyRate($regionOrder->getCurrencyRate());
                $order->setFulfillLog($regionOrder->getFulfillLog());
                $order->setFlagLog($regionOrder->getFlagLog());
                $order->setSenFulfillStatus($regionOrder->getSenFulfillStatus());
                $order->setDatestamp($regionOrder->getDatestamp());
                $order->setOrderNumber($regionOrder->getOrderNumber());
                $order->setOrderNumber2($regionOrder->getOrderNumber2());
                $order->setPersonalized($regionOrder->getPersonalized());
                $order->setLast4CardNumber($regionOrder->getLast4CardNumber());
                $order->setVisitInfo($regionOrder->getVisitInfo());
                $order->setAddress($regionOrder->getAddress());
                $order->setAddress2($regionOrder->getAddress2());
                $order->setCity($regionOrder->getCity());
                $order->setState($regionOrder->getState());
                $order->setPostcode($regionOrder->getPostcode());
                $order->setCustomerEmail($regionOrder->getCustomerEmail());
                $order->setCustomerName($regionOrder->getCustomerName());
                $order->setCustomerPhone($regionOrder->getCustomerPhone());
                $order->setRegionSyncedAt($now);
                $order->save();
                $order->products()->delete();
                $orderId = (int)data_get($order, 'id');
                foreach ($regionOrder->products as $orderProduct) {
                    $orderProduct->setOrderId($orderId);
                    $additional_attributes = $orderProduct->additional_attributes;
                    $newOrderProduct = $order->products()->create($orderProduct->makeHidden(['id', 'additional_attributes'])->toArray());
                    $orderProductId = data_get($newOrderProduct, 'id');
                    if (!empty($additional_attributes) && $orderProductId) {
                        $additional_attributes = Str::isJson($additional_attributes) ? json_decode($additional_attributes, true, 512, JSON_THROW_ON_ERROR) : [];
                        foreach ($additional_attributes as $key => $values) {
                            $values = array_filter(array_map(static function ($value) use ($orderId, $orderProductId, $key) {
                                if (empty($value)) {
                                    return null;
                                }
                                if ($key === 'design_files') {
                                    $value = array_merge($value, ['id' => generateUUID(), 'order_id' => $orderId]);
                                }
                                return array_merge($value, ['order_product_id' => $orderProductId]);
                            }, $values));
                            if ($key === 'design_files') {
                                Design::query()->insert($values);
                            }
                            if ($key === 'no_ship_logs') {
                                OrderProductLog::query()->insert($values);
                            }
                        }
                    }
                }
                if ($order->getCustomerEmail()) {
                    $customer = Customer::query()->where('email', $order->getCustomerEmail())->first();

                    if (!$customer) {
                        $customer = Customer::query()->make();
                        $customer->setEmail($order->getCustomerEmail());
                    }

                    $customer->setName($order->getCustomerName());
                    $customer->save();

                    $address = CustomerAddress::query()->make();
                    $address->setUserId($customer->getId());
                    $address->setName($order->getCustomerName());
                    $address->setPhone($order->getCustomerPhone());
                    $address->setAddress($order->getAddress());
                    $address->setAddress2($order->getAddress2());
                    $address->setCity($order->getCity());
                    $address->setState($order->getState());
                    $address->setPostcode($order->getPostcode());
                    $address->setCountry($order->getCountry());

                    if (!$address->checkAddressExisted()) {
                        $address->setId(generateUUID());
                        $address->save();
                    }

                    // We sync customer id to order
                    $order->setCustomerId($customer->getId());
                    $order->save();

                    // We sync customer to seller
                    $seller = $order->seller;
                    $sellerCustomer = SellerCustomer::query()->where('customer_id', $customer->getId())->where('seller_id', $seller->id)->first();
                    if ($seller && !$sellerCustomer) {
                        SellerCustomer::query()->insertOrIgnore([
                            'seller_id' => $seller->id,
                            'store_id' => $order->store_id,
                            'customer_id' => $customer->getId(),
                        ]);
                    }
                }
                $regionOrder->setSyncAt($now);
                $regionOrder->setUpdatedAt($now);
                $regionOrder->setRegionSyncedAt($now);
                $regionOrder->saveQuietly();
                if (in_array($orderStatusBefore, [OrderStatus::DRAFT, OrderStatus::PENDING], true)) {
                    OrderHistory::query()->where('order_id', $order->id)->delete();
                    $orderHistoriesRegion = RegionOrderHistory::onRegion($order->region)->where('order_id', $regionOrder->getId())->get();
                    foreach ($orderHistoriesRegion as $orderHistory) {
                        if ($orderHistory->getAction() === OrderHistoryActionEnum::PAID_BY_CUSTOMER && $regionOrder->getPaymentSummary()) {
                            OrderHistory::query()->create([
                                'id' => generateUUID(),
                                'order_id' => $order->getId(),
                                'action' => OrderHistoryActionEnum::PAYMENT_SUMMARY,
                                'display_level' => OrderHistoryDisplayLevelEnum::ADMIN,
                                'order_status' => $orderHistory->getOrderStatus(),
                                'fulfill_status' => $orderHistory->getFulfillStatus(),
                                'support_status' => $orderHistory->getSupportStatus(),
                                'admin_detail' => $orderHistory->getAdminDetail(),
                                'assignee' => $orderHistory->getAssignee(),
                                'detail' => $regionOrder->getPaymentSummary(),
                                'created_at' => $orderHistory->getCreatedAt(),
                            ]);
                        }
                        OrderHistory::query()->create([
                            'id' => generateUUID(),
                            'order_id' => $order->getId(),
                            'action' => $orderHistory->getAction(),
                            'display_level' => $orderHistory->getDisplayLevel(),
                            'order_status' => $orderHistory->getOrderStatus(),
                            'fulfill_status' => $orderHistory->getFulfillStatus(),
                            'support_status' => $orderHistory->getSupportStatus(),
                            'admin_detail' => $orderHistory->getAdminDetail(),
                            'assignee' => $orderHistory->getAssignee(),
                            'detail' => $orderHistory->getDetail(),
                            'created_at' => $orderHistory->getCreatedAt(),
                        ]);
                    }
                }
                $paymentStatusAfter = $order->getPaymentStatus();
                $changePaymentStatusToPaid = false;
                if ($paymentStatusBefore !== $paymentStatusAfter && $paymentStatusAfter === OrderPaymentStatus::PAID) {
                    $changePaymentStatusToPaid = true;
                }
                if ($changePaymentStatusToPaid) {
                    OrderPaymentCompleted::dispatch($order);
                }
                $syncedOrder[] = $regionOrder->order_number;
            }
            return $this->successResponse([
                'synced' => $syncedOrder
            ], "Synced order successfully.");
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param $orderId
     * @return JsonResponse
     */
    public function getTransactionDetail($orderId)
    {
        try {
            $order = Order::query()
                ->where('id', $orderId)
                ->whereNotNull(['payment_gateway_id', 'transaction_id', 'payment_method'])
                ->first();
            if (!$order) {
                return $this->errorResponse('Order not found');
            }
            $paymentGatewayId = $order->payment_gateway_id;
            $paymentGateway = PaymentGateway::query()->where('id', $paymentGatewayId)->first();
            if (!$paymentGateway) {
                return $this->errorResponse('Payment gateway not found');
            }
            $transactionId = $order->transaction_id;
            $cacheKey = md5('transaction_detail_' . $paymentGatewayId . '_' . $transactionId);
            if (cache()->has($cacheKey)) {
                return $this->successResponse(cache()->get($cacheKey));
            }
            $verifyResult = null;
            if (PaymentMethodEnum::isStripe($order->payment_method)) {
                $verifyResult = (new StripeController())->verifyPaymentIntent($paymentGatewayId, $transactionId);
            } else if ($order->payment_method === PaymentMethodEnum::PAYPAL) {
                $verifyResult = (new PaypalController())->getOrderDetailFromCapture($paymentGatewayId, $transactionId);
            }
            if (!$verifyResult) {
                return $this->errorResponse('Transaction not found');
            }
            cache()->put($cacheKey, $verifyResult, now()->addHour());
            return $this->successResponse($verifyResult);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}

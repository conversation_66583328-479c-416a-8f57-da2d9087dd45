<?php

namespace App\Http\Controllers;

use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\TypeMockup3DFiles;
use App\Models\File;
use Cloudinary\Api\Exception\ApiError;
use Cloudinary\Cloudinary;
use Exception;

class SyncFileController extends Controller
{
    public static function syncMockupToCloudinary(): void
    {
        $files = File::query()
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => FileRenderType::RENDER_3D
            ])
            ->whereNull('file_url_2')
            ->limit(5)
            ->get();

        if ($files->isEmpty()) {
            return;
        }

        foreach ($files as $file) {
            try {
                $file_url = $file->file_url;
                $file->file_url_2 = self::copyFileToCloudinary($file_url);

                if (!empty($file->design_json)) {
                    $designJson = json_decode($file->design_json, true); // to arr

                    if (isset($designJson[TypeMockup3DFiles::COLOR])) {
                        $designJson[TypeMockup3DFiles::COLOR . TypeMockup3DFiles::CDN] = self::copyFileToCloudinary(
                            $designJson[TypeMockup3DFiles::COLOR]
                        );
                    }

                    if (isset($designJson[TypeMockup3DFiles::SHADOW])) {
                        $designJson[TypeMockup3DFiles::SHADOW . TypeMockup3DFiles::CDN] = self::copyFileToCloudinary(
                            $designJson[TypeMockup3DFiles::SHADOW]
                        );
                    }

                    if (isset($designJson[TypeMockup3DFiles::CROP])) {
                        $designJson[TypeMockup3DFiles::CROP . TypeMockup3DFiles::CDN] = self::copyFileToCloudinary(
                            $designJson[TypeMockup3DFiles::CROP]
                        );
                    }

                    $file->design_json = json_encode($designJson);
                }
            } catch (Exception $e) {
                logException($e);
                $file->file_url_2 = '';
            }

            $file->save();
            logToDiscord('Sync S3 to CDN | File ID = ' . $file->id);
        }
    }

    public static function copyFileToCloudinary($s3Path)
    {
        $url = s3Url($s3Path);
        $publicId = preg_replace('/\.([a-z]+)$/', '', $s3Path);
        $config = config('senprints.cloudinary_config');
        $cloudinary = new Cloudinary($config);
        try {
            $cloudinary
                ->uploadApi()
                ->upload(
                    $url,
                    [
                        'public_id' => $publicId,
                        'resource_type' => 'image',
                        'chunk_size' => 6000000, // 6MB
                    ]
                );

            return $publicId;
        } catch (ApiError $e) {
            logException($e);
            return '';
        }
    }
}

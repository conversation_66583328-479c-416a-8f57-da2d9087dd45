<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\CampaignPublicStatusEnum;
use App\Enums\OrderStatus;
use App\Enums\ProductReviewAllowSharingEnum;
use App\Enums\ProductReviewDisplayEnum;
use App\Enums\ProductReviewEnum;
use App\Enums\ProductReviewFileTypeEnum;
use App\Enums\ProductReviewFilterEnum;
use App\Enums\StorageDisksEnum;
use App\Exports\Admin\ProductReviewExport;
use App\Http\Requests\Admin\Review\AddKeywordProductReviewRequest;
use App\Http\Requests\Admin\Review\ReviewProductKeywordRequest;
use App\Http\Requests\ImportReviewsRequest;
use App\Http\Requests\ProductReview\CreateProductReviewRequest;
use App\Http\Requests\UpdateProductReviewSharingStatusRequest;
use App\Http\Requests\UpdateProductReviewStatusRequest;
use App\Http\Resources\ProductReviewDetailResource;
use App\Http\Resources\ProductReviewShowResource;
use App\Imports\Seller\ProductReview\ImportProductReview;
use App\Jobs\CrawlEmailAvatar;
use App\Models\Campaign;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductReview;
use App\Models\ProductReviewFile;
use App\Models\ProductReviewKeyword;
use App\Models\Store;
use App\Models\User;
use App\Services\ProductReviewService;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use RuntimeException;
use Throwable;

class ProductReviewController extends Controller
{
    use ApiResponse;

    protected ProductReviewService $productReviewService;

    /**
     * Constructor for ProductReviewController.
     *
     * @param ProductReviewService $productReviewService Service for handling product review operations
     */
    public function __construct(ProductReviewService $productReviewService)
    {
        $this->productReviewService = $productReviewService;
    }

    /**
     * Create a new product review for a completed order.
     *
     * This method handles the creation of product reviews including:
     * - Validating the order is completed
     * - Calculating review scores and ratings
     * - Handling file uploads for review assets
     * - Setting sharing permissions based on product status
     * - Triggering email avatar crawling for customers
     *
     * @param CreateProductReviewRequest $request The validated request containing review data
     * @return JsonResponse JSON response with success/error status and review data
     * @throws Exception When database operations fail
     */
    public function create(CreateProductReviewRequest $request): JsonResponse
    {
        $now = now();
        $assets = $request->input('assets');
        $review = $request->only([
            'order_product_id',
            'print_quality_rating',
            'product_quality_rating',
            'customer_support_rating',
            'comment',
        ]);

        $orderProduct = OrderProduct::query()
            ->with([
                'order:id,seller_id,store_id,customer_id,shipping_address',
                'order.customer:id,email,avatar,crawled_email_avatar_at',
            ])
            ->whereHas('order', function ($order) {
                $order->where('order.status', OrderStatus::COMPLETED);
            })
            ->find($request['order_product_id']);

        if (!$orderProduct) {
            return $this->errorResponse('Failed to review. Order is incomplete.');
        }
        $product = Product::query()
            ->withTrashed()
            ->onSellerConnection($orderProduct->seller)
            ->select(['id', 'public_status'])
            ->find($orderProduct->product_id);
        $orderProduct->setRelation('product', $product);

        if (!$product) {
            return $this->errorResponse('Failed to review. Order is incomplete.');
        }

        $review['average_rating'] = ($review['print_quality_rating'] + $review['product_quality_rating'] + $review['customer_support_rating']) / 3;
        $review['score'] = ProductReviewService::scoreCalc($review['average_rating'], $review['comment'], $assets, $now->timestamp);
        $allowSharing = $orderProduct->product->public_status === CampaignPublicStatusEnum::APPROVED ? ProductReviewAllowSharingEnum::SHARE_CAMPAIGN : ProductReviewAllowSharingEnum::SHARE_REVIEW;
        $keywords = [];
        if (empty($review['comment'])) {
            $allowSharing = ProductReviewAllowSharingEnum::DENY;
        } else {
            $keywords = ProductReviewKeyword::query()->select('id')->whereRaw('? REGEXP keyword', [$review['comment']])->pluck('id')->toArray();
        }
        $referenceFields = [
            'order_id' => $orderProduct->order_id,
            'campaign_id' => $orderProduct->campaign_id,
            'product_id' => $orderProduct->product_id,
            'template_id' => $orderProduct->template_id,
            'seller_id' => $orderProduct->order->seller_id,
            'store_id' => $orderProduct->order->store_id,
            'customer_id' => $orderProduct->order->customer_id,
            'customer_address_id' => $orderProduct->order->shipping_address,
            'allow_sharing' => $allowSharing
        ];

        DB::beginTransaction();
        try {
            $review = $orderProduct->productReview()->create(array_merge($review, $referenceFields));
            $review->productReviewKeywords()->sync($keywords);
            if (!empty($assets)) {
                $storagePath = 'prf/' . $review->id;
                $insertRecords = [];

                foreach ($assets as $file) {
                    $record = [
                        'id' => generateUUID(),
                        'product_review_id' => $review->id,
                        'type' => $file['type'] === ProductReviewFileTypeEnum::VIDEO ? ProductReviewFileTypeEnum::VIDEO : ProductReviewFileTypeEnum::IMAGE,
                        'url' => saveTempFileAws($file['url'], $storagePath),
                        'thumb_url' => $file['type'] === ProductReviewFileTypeEnum::VIDEO ? saveTempFileAws($file['thumb'], $storagePath) : null,
                        'token' => fileTokenGenerator(),
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];

                    $insertRecords[] = $record;
                }

                if (!empty($insertRecords)) {
                    ProductReviewFile::query()->insert($insertRecords);
                }
            }

            if (
                !empty($orderProduct->order->customer)
                && !empty($orderProduct->order->customer->email)
                && empty($orderProduct->order->customer->avatar)
                && empty($orderProduct->order->customer->crawled_email_avatar_at)
            ) {
                CrawlEmailAvatar::dispatch($orderProduct->order->customer->email, false);
            }

            DB::commit();
            return $this->successResponse(['average_rating' => $review->average_rating]);
        } catch (Exception $e) {
            DB::rollBack();
            logException($e);
            return $this->errorResponse('Cannot share your review. Please try again.');
        }
    }

    /**
     * Get product review summary statistics and files for a template/campaign.
     *
     * This method retrieves aggregated review data including:
     * - Average rating and review counts by star rating
     * - Associated review files (images/videos)
     * - Cached results for performance
     * - Store-specific filtering based on display settings
     *
     * @param Request $request HTTP request containing campaignId parameter
     * @param string|int $templateId The template ID to get reviews for
     * @return JsonResponse JSON response with review summary and files
     * @throws RuntimeException When store information cannot be found
     */
    public function summary(Request $request, $templateId): JsonResponse
    {
        try {
            $storeInfo = StoreService::getCurrentStoreInfo();
            if (is_null($storeInfo)) {
                throw new RuntimeException('Store not found');
            }

            $storeId = $storeInfo->id;
            $campaignId = $request->input('campaignId');

            $cache = cacheAlt();

            if ($storeId) {
                $tag = CacheKeys::getStoreId($storeId);
                $cache->tags([$tag]);
            }

            $cacheKey = CacheKeys::STOREFRONT_PRODUCT_REVIEW_SUMMARY_PREFIX . $campaignId . '_' . $templateId;

            [$stats, $files] = $cache->remember($cacheKey, CacheTime::CACHE_24H, function () use ($storeInfo, $storeId, $campaignId, $templateId) {
                $sellerId = $storeInfo->seller_id;
                $productReviewDisplay = $storeInfo->product_review_display;

                if ($productReviewDisplay === ProductReviewDisplayEnum::DISABLE) {
                    return [null, []];
                }

                $stats = ProductReview::query()
                    ->on('mysql_main_us')
                    ->select([
                        DB::raw('TRUNCATE(COALESCE(AVG(average_rating), 0), 1) as average_rating'),
                        DB::raw('COUNT(*) as review_count'),
                        DB::raw("SUM(CASE WHEN average_rating = 5 THEN 1 ELSE 0 END) AS five_star_count"),
                        DB::raw("SUM(CASE WHEN average_rating >= 4 AND average_rating < 5 THEN 1 ELSE 0 END) AS four_star_count"),
                        DB::raw("SUM(CASE WHEN average_rating >= 3 AND average_rating < 4 THEN 1 ELSE 0 END) AS three_star_count"),
                        DB::raw("SUM(CASE WHEN average_rating >= 2 AND average_rating < 3 THEN 1 ELSE 0 END) AS two_star_count"),
                        DB::raw("SUM(CASE WHEN average_rating >= 1 AND average_rating < 2 THEN 1 ELSE 0 END) AS one_star_count"),
                    ])
                    ->fromRaw('product_reviews USE INDEX (idx_review_coverage_v1)')
                    ->where(function ($query) use ($templateId, $campaignId) {
                        $query->where('template_id', (int)$templateId);

                        if (!empty($campaignId)) {
                            $query->orWhere('campaign_id', (int)$campaignId);
                        }
                    })
                    ->when(
                        $productReviewDisplay === ProductReviewDisplayEnum::ENABLE_ONLY_STORE,
                        function ($query) use ($storeId) {
                            $query->where('store_id', (int)$storeId);
                        }
                    )
                    ->where(function ($query) use ($sellerId) {
                        $query->where('seller_id', (int)$sellerId)
                            ->orWhere('allow_sharing', '!=', ProductReviewAllowSharingEnum::DENY);
                    })
                    ->where('status', ProductReviewEnum::ACTIVE)
                    ->get()
                    ->first();

                $files = ProductReviewFile::query()
                    ->on('mysql_main_us')
                    ->select(['url', 'thumb_url', 'type'])
                    ->whereHas('productReview', function ($review) use (
                        $templateId,
                        $sellerId,
                        $productReviewDisplay,
                        $storeId,
                        $campaignId
                    ) {
                        $review
                            ->where(function ($query) use ($templateId, $campaignId) {
                                $query->where('template_id', (int)$templateId);

                                if (!empty($campaignId)) {
                                    $query->orWhere('campaign_id', (int)$campaignId);
                                }
                            })
                            ->where(function ($query) use ($sellerId, $storeId) {
                                $query->where('seller_id', (int)$sellerId)
                                    ->orWhere('allow_sharing', ProductReviewAllowSharingEnum::SHARE_CAMPAIGN)
                                    ->when(
                                        $storeId != Store::SENPRINTS_STORE_ID,
                                        function ($query) use ($storeId) {
                                            $query->orWhere('store_id', (int)$storeId);
                                        }
                                    );
                            })
                            ->when(
                                $productReviewDisplay === ProductReviewDisplayEnum::ENABLE_ONLY_STORE,
                                function ($query) use ($storeId) {
                                    $query->where('store_id', (int)$storeId);
                                }
                            )
                            ->where('status', ProductReviewEnum::ACTIVE);
                    })
                    ->get()
                    ->map(fn($item) => [
                        'type' => $item->type,
                        'thumb' => $item->thumb_url ?? $item->url,
                        'src' => $item->url,
                    ]);

                return [$stats, $files];
            });

            $headers = generateHeadersCacheHTMLTagByCampaignIds([$campaignId]);

            return $this->successResponse([
                'summary' => $stats,
                'files' => $files
            ])->withHeaders($headers);
        } catch (Exception $e) {
            return $this->successResponse([
                'summary' => [
                    'average_rating' => '0.0',
                    'review_count' => 0,
                    'five_star_count' => 0,
                    'four_star_count' => 0,
                    'three_star_count' => 0,
                    'two_star_count' => 0,
                    'one_star_count' => 0,
                ],
                'files' => []
            ]);
        }
    }

    /**
     * Get paginated product reviews for a template/campaign with filtering and sorting.
     *
     * This method retrieves detailed review data including:
     * - Paginated review list with related data (files, order info, customer info)
     * - Filtering by star rating (newest, helpful, specific star counts)
     * - Store-specific visibility rules
     * - Cached results for performance
     * - Proper sorting based on filter type and seller ownership
     *
     * @param Request $request HTTP request containing campaignId, filter, perPage, page parameters
     * @param string|int $templateId The template ID to get reviews for
     * @return JsonResponse JSON response with paginated review data
     * @throws Throwable When any error occurs during processing
     */
    public function review(Request $request, $templateId): JsonResponse
    {
        try {
            $storeInfo = StoreService::getCurrentStoreInfo();
            if (is_null($storeInfo)) {
                return $this->errorResponse('Store not found');
            }
            $campaignId = (int)$request->input('campaignId');
            if ($campaignId === 0 && !is_numeric($request->input('campaignId'))) {
                return $this->errorResponse('Campaign is invalid');
            }
            $filter = $request->input('filter', ProductReviewFilterEnum::HELPFUL);
            $perPage = $request->input('perPage', 5);
            $page = $request->input('page', 1);

            $storeId = $storeInfo->id;

            $cache = cacheAlt();

            if ($storeId) {
                $tag = CacheKeys::getStoreId($storeId);
                $cache->tags([$tag]);
            }
            $reviews = $cache
                ->remember(
                    CacheKeys::getStorefrontProductReview($campaignId, $templateId, $filter, $page, $perPage),
                    CacheTime::CACHE_24H,
                    function () use ($storeInfo, $storeId, $campaignId, $templateId, $filter, $perPage) {
                        $sellerId = $storeInfo->seller_id;
                        $productReviewDisplay = $storeInfo->product_review_display;
                        $isRandomPopular = $storeInfo->random_popular;
                        $orderByRaw = $filter === ProductReviewFilterEnum::NEWEST ? 'created_at desc' : "score + if(seller_id = " . $sellerId . " or allow_sharing='share_campaign', 10, 0) desc";
                        $reviews = ProductReview::query()
                            ->with('productReviewFiles', function ($files) {
                                $files->select([
                                    'url', 'thumb_url', 'type', 'product_review_id'
                                ])->orderByDesc('created_at');
                            })
                            ->with('order', function ($order) {
                                $order->select([
                                    'id', 'customer_name', 'customer_email', 'country'
                                ])->with('country_info:code,name');
                            })
                            ->with('orderProduct:id,product_name,product_url,options,thumb_url')
                            ->with('template:id,name')
                            ->with('customer:id,avatar')
                            ->where(function ($query) use ($templateId, $campaignId) {
                                $query->where('template_id', (int)$templateId);

                                if (!empty($campaignId)) {
                                    $query->orWhere('campaign_id', (int)$campaignId);
                                }
                            })
                            ->where(function ($query) use ($sellerId) {
                                $query->where('seller_id', (int)$sellerId)
                                    ->orWhere('allow_sharing', '!=', ProductReviewAllowSharingEnum::DENY);
                            })
                            ->where(function ($query) use ($productReviewDisplay, $storeId) {
                                switch ($productReviewDisplay) {
                                    case ProductReviewDisplayEnum::DISABLE:
                                        $query->whereRaw('1 != 1');
                                        break;
                                    case ProductReviewDisplayEnum::ENABLE_ONLY_STORE:
                                        $query->where('store_id', (int)$storeId);
                                        break;
                                }
                            })
                            ->when(!empty($filter), function ($query) use ($filter) {
                                switch ($filter) {
                                    case ProductReviewFilterEnum::FIVE_STAR:
                                        $query->where('average_rating', 5);
                                        break;
                                    case ProductReviewFilterEnum::FOUR_STAR:
                                        $query->where('average_rating', '>=', 4)
                                            ->where('average_rating', '<', 5);
                                        break;
                                    case ProductReviewFilterEnum::THREE_STAR:
                                        $query->where('average_rating', '>=', 3)
                                            ->where('average_rating', '<', 4);
                                        break;
                                    case ProductReviewFilterEnum::TWO_STAR:
                                        $query->where('average_rating', '>=', 2)
                                            ->where('average_rating', '<', 3);
                                        break;
                                    case ProductReviewFilterEnum::ONE_STAR:
                                        $query->where('average_rating', '>=', 1)
                                            ->where('average_rating', '<', 2);
                                }
                            })
                            ->where('status', ProductReviewEnum::ACTIVE)
                            ->orderByRaw($orderByRaw);

                        $reviews = $reviews->paginate($perPage);
                        $reviews->getCollection()->transform(
                            fn($review) => ProductReviewShowResource::make($review)
                                ->additional([
                                    'storeId' => $storeId,
                                    'isRandomPopular' => $isRandomPopular
                                ])
                                ->resolve()
                        );

                        return $reviews;
                    }
                );

            $headers = generateHeadersCacheHTMLTagByCampaignIds([$campaignId]);

            return $this->successResponse($reviews)->withHeaders($headers);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get paginated list of product reviews for admin interface.
     *
     * This method provides comprehensive review management including:
     * - Filtering by status, date range, keywords, assignee, store, etc.
     * - Sorting by various columns (created_at, updated_at, finished_at, average_rating)
     * - Loading related data (staff, order, product, store, seller, files, keywords, template)
     * - Campaign data loading with proper seller connections
     * - Status-based totals for dashboard statistics
     *
     * @param Request $request HTTP request with filtering and pagination parameters
     * @return JsonResponse JSON response with paginated reviews and status totals
     */
    public function index(Request $request)
    {
        $perPage = $request->input('per_page', 15);
        $status = $request->input('status');
        $orderBy = $this->validateOrderBy($request->input('sort'));
        $direction = $request->input('direction', 'DESC');
        $currentUser = currentUser();

        $reviews = $this->applyFilterReviewList($request, $currentUser);
        $clone = clone $reviews;
        $total = $clone
            ->select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->get();

        $reviews = $reviews
            ->select([
                'id',
                'order_product_id',
                'order_id',
                'campaign_id',
                'store_id',
                'seller_id',
                'product_id',
                'customer_id',
                'average_rating',
                'comment',
                'allow_sharing',
                'status',
                'created_at',
                'updated_at',
                'finished_at',
                'assignee',
                'support',
                'customer_name',
                'customer_email',
                'country',
                'type',
                'review_date',
                'review_options',
                'template_id'
            ])
            ->with('staff:id,name')
            ->with('order:id,order_number,store_domain,customer_name,customer_email,country')
            ->with('orderProduct:id,product_name,product_url,options,thumb_url')
            ->with('store:id,name')
            ->with('seller:id,name,email')
            ->with('productReviewFiles:id,product_review_id,url,thumb_url,type')
            ->with('productReviewKeywords:id,keyword')
            ->with('template:id,name')
            ->when($status, function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->orderBy($orderBy, $direction)
            ->paginate($perPage);
        $sellerCampaignIds = $reviews->getCollection()->groupBy('seller_id')->map(function ($reviewGroup) {
            return $reviewGroup->pluck('campaign_id')->unique()->toArray();
        })->toArray();
        $campaigns = collect();
        if (count($sellerCampaignIds) > 0) {
            foreach ($sellerCampaignIds as $sellerId => $campaignIds) {
                $seller = User::query()->find($sellerId);
                $campaigns->put($sellerId, Campaign::query()->onSellerConnection($seller)->select(['id', 'name'])->whereIn('id', $campaignIds)->get());
            }
        }
        $reviews->getCollection()->transform(function ($review) use ($campaigns) {
            if ($campaigns->has($review->seller_id)) {
                $review->setRelation('campaign', $campaigns[$review->seller_id]->firstWhere('id', $review->campaign_id));
            } else {
                $review->setRelation('campaign', null);
            }
            return ProductReviewDetailResource::make($review)->resolve();
        });
        return $this->successResponse([
            'reviews' => $reviews,
            'total' => $total,
        ]);
    }

    /**
     * Validate and sanitize the order by column for review queries.
     *
     * @param string|null $orderBy The requested order by column
     * @return string Valid column name, defaults to 'created_at' if invalid
     */
    private function validateOrderBy($orderBy)
    {
        $allowedColumns = [
            'created_at',
            'updated_at',
            'finished_at',
            'average_rating',
        ];

        if (!in_array($orderBy, $allowedColumns)) {
            return 'created_at';
        }

        return $orderBy;
    }

    /**
     * Apply comprehensive filtering to product review queries.
     *
     * This method handles various filter types including:
     * - Date range filtering (time, start_date, end_date)
     * - Keyword search (campaign name, store name, seller name, customer info)
     * - Review content search (review_keyword)
     * - Staff assignment filtering (assignee)
     * - Store and seller-specific filtering
     * - Support staff filtering
     * - User role-based access control
     *
     * @param Request $request HTTP request containing filter parameters
     * @param mixed $currentUser Current authenticated user for role-based filtering
     * @return \Illuminate\Database\Eloquent\Builder Filtered query builder instance
     */
    private function applyFilterReviewList(Request $request, $currentUser)
    {
        $time = $request->input('time');
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $keyword = $request->input('keyword') ?? $request->input('q');
        $reviewKeyword = $request->input('review_keyword');
        $assignee = $request->input('assignee');
        $sellerId = $currentUser->isSeller() ? $currentUser->getUserId() : null;
        $storeId = $request->input('store_id');
        $support = $request->input('support');
        $templateId = $request->input('template_id', null);
        $supplierId = $request->input('supplier_id', null);
        $supplierId = !$supplierId ? null : $supplierId;
        $orderBy = $this->validateOrderBy($request->input('sort'));

        $reviews = ProductReview::query()
            ->when(!empty($time), function ($query) use ($time, $startDate, $endDate, $orderBy) {
                $query->filterDateRange($time, $startDate, $endDate, $orderBy);
            })
            ->when(!empty($keyword), function ($query) use ($keyword, $sellerId) {
                $query->where(function ($sQuery) use ($keyword, $sellerId) {
                    if ($sellerId) {
                        $sQuery->whereHas('order', function ($campaign) use ($keyword) {
                            $campaign->where('order.customer_name', 'LIKE', '%' . $keyword . '%')
                                ->orWhere('order.customer_email', 'LIKE', '%' . $keyword . '%');
                        });
                    } else {
                        $sQuery->whereHas('campaign', function ($campaign) use ($keyword) {
                            $campaign->where('name', 'LIKE', '%' . $keyword . '%');
                        })
                            ->orWhereHas('store', function ($store) use ($keyword) {
                                $store->where('name', 'LIKE', '%' . $keyword . '%');
                            })
                            ->orWhereHas('seller', function ($seller) use ($keyword) {
                                $seller->where('name', 'LIKE', '%' . $keyword . '%');
                            });
                    }
                    $sQuery->orWhereHas('order', function ($q) use ($keyword) {
                        $q->where('order.order_number', 'LIKE', '%' . $keyword . '%')
                            ->orWhere('order.id', 'LIKE', '%' . $keyword . '%');
                    })->orWhereHas('customer', function ($customer) use ($keyword) {
                        $customer->where('name', 'LIKE', '%' . $keyword . '%')
                            ->orWhere('email', 'LIKE', '%' . $keyword . '%');
                    });
                });
            })
            ->when(!empty($sellerId), function ($query) use ($sellerId) {
                $query->where('seller_id', (int)$sellerId)
                    ->whereIn('status', [ProductReviewEnum::ACTIVE, ProductReviewEnum::INACTIVE]);
            })
            ->when(!empty($storeId), function ($query) use ($storeId) {
                $query->where('store_id', (int)$storeId);
            })
            ->when(isset($reviewKeyword), function ($query) use ($reviewKeyword) {
                $query->where('comment', 'like', '%' . $reviewKeyword . '%');
            })
            ->when(!empty($assignee), function ($query) use ($assignee) {
                $query->whereHas('staff', function ($assigneeQuery) use ($assignee) {
                    $assigneeQuery->where('id', $assignee);
                });
            })
            ->when(!empty($templateId), function ($query) use ($templateId) {
                $query->where('template_id', (int)$templateId);
            })
            ->when(!empty($supplierId), function ($query) use ($supplierId) {
                $query->whereHas('orderProduct', function ($orderProductQuery) use ($supplierId) {
                    $orderProductQuery->where('supplier_id', (int)$supplierId);
                });
            });
        if ($assignee == 0 && $assignee !== null) {
            $reviews->whereNull('assignee');
        }
        if ($support) {
            $reviews->where('support', $support);
        }

        if ($currentUser->isSeller()) {
            $storeIds = get_team_seller_stores($currentUser->getUserId(), $currentUser->getAuthorizedAccountId());

            if (!empty($storeIds)) {
                $reviews->whereIn('store_id', $storeIds);
            }
        }

        return $reviews;
    }

    /**
     * Export product reviews to CSV file.
     *
     * This method generates a CSV export of filtered product reviews including:
     * - Same filtering capabilities as the index method
     * - Essential review data and related information
     * - Staff and support assignments
     * - Order and seller details
     * - Timestamped filename for organization
     *
     * @param Request $request HTTP request with filtering parameters
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse CSV file download response
     */
    public function export(Request $request)
    {
        $status = $request->input('status');
        $orderBy = $this->validateOrderBy($request->input('sort'));
        $direction = $request->input('direction', 'DESC');
        $currentUser = currentUser();
        $fileName = 'product_reviews_' . date('Y-m-d H:i:s') . '.csv';
        $reviews = $this->applyFilterReviewList($request, $currentUser);
        $reviews = $reviews->select([
            'id',
            'order_id',
            'order_product_id',
            'seller_id',
            'average_rating',
            'comment',
            'status',
            'created_at',
            'updated_at',
            'finished_at',
            'assignee',
            'support'
        ])
            ->with('staff:id,name')
            ->with('order:id,order_number,paid_at,customer_name,customer_email,country')
            ->with('orderProduct:id,product_name,thumb_url,supplier_name,sku')
            ->with('seller:id,name,email')
            ->with('staffSupport:id,name')
            ->with('productReviewFiles')
            ->when(!empty($status), function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->orderBy($orderBy, $direction)
            ->get();
        return Excel::download(new ProductReviewExport($reviews), $fileName);
    }

    /**
     * Assign a staff member to a product review.
     *
     * @param Request $request HTTP request containing assignee ID
     * @param string|int $id Product review ID
     * @return JsonResponse JSON response confirming assignment update
     */
    public function setAssignee(Request $request, $id): JsonResponse
    {
        $assignee = $request->input('assignee');
        if (!$this->validateSupporters($assignee)) {
            $assignee = null;
        }

        $review = ProductReview::query()->findOrFail($id);
        $review->assignee = $assignee;
        $review->save();
        return $this->successResponse('Assignee updated successfully.');
    }

    /**
     * Validate if a user ID belongs to the list of valid supporters.
     *
     * @param int|null $id User ID to validate
     * @return bool True if valid supporter, false otherwise
     */
    private function validateSupporters($id)
    {
        $supporters = SystemConfigController::supporters()->pluck('id')->toArray();
        if (!in_array($id, $supporters)) {
            return false;
        }

        return true;
    }

    /**
     * Assign a support staff member to a product review.
     *
     * @param Request $request HTTP request containing support staff ID
     * @param string|int $id Product review ID
     * @return JsonResponse JSON response confirming support assignment update
     */
    public function setSupport(Request $request, $id): JsonResponse
    {
        $support = $request->input('support');
        if (!$this->validateSupporters($support)) {
            $support = null;
        }
        $review = ProductReview::query()->findOrFail($id);
        $review->support = $support;
        $review->save();
        return $this->successResponse('Support updated successfully.');
    }

    /**
     * Get detailed information for a specific product review.
     *
     * This method retrieves comprehensive review data including:
     * - Review details and ratings
     * - Associated order and product information
     * - Store and seller information
     * - Review files (images/videos)
     * - Campaign information with proper seller connection
     *
     * @param string|int $id Product review ID
     * @return JsonResponse JSON response with detailed review data
     * @throws Exception When review is not found or database error occurs
     */
    public function show($id): JsonResponse
    {
        try {
            $review = ProductReview::query()
                ->select([
                    'id',
                    'order_product_id',
                    'order_id',
                    'campaign_id',
                    'store_id',
                    'seller_id',
                    'product_id',
                    'customer_id',
                    'average_rating',
                    'comment',
                    'allow_sharing',
                    'status',
                    'updated_at',
                    'assignee',
                    'support',
                    'created_at'
                ])
                ->with('order:id,order_number,store_domain,customer_name,customer_email,country')
                ->with('orderProduct:id,product_name,product_url,options,thumb_url')
                ->with('store:id,name')
                ->with('seller:id,name,sharding_status,db_connection')
                ->with('productReviewFiles:id,product_review_id,url,thumb_url,type')
                ->findOrFail($id);

            $review->setRelation('campaign', Campaign::query()->onSellerConnection($review->seller)->select(['id', 'name'])->where('id', $review->campaign_id)->first());
            return $this->successResponse(ProductReviewDetailResource::make($review)->resolve());
        } catch (Exception $ex) {
            logToDiscord($ex->getMessage());
            return $this->errorResponse();
        }
    }

    /**
     * Delete a product review and its associated files.
     *
     * This method performs complete cleanup including:
     * - Removing review files from storage (both default and S3)
     * - Deleting file records from database
     * - Deleting the review record
     * - Transaction safety for data integrity
     *
     * @param string|int $id Product review ID to delete
     * @return JsonResponse JSON response confirming deletion success/failure
     * @throws Exception When deletion fails
     */
    public function destroy($id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $removeFiles = [];
            ProductReviewFile::where('product_review_id', $id)
                ->get(['url', 'thumb_url'])
                ->each(function ($item) use (&$removeFiles) {
                    $removeFiles[] = $item->url;

                    if (!empty($item->thumb_url)) {
                        $removeFiles[] = $item->thumb_url;
                    }
                });

            ProductReviewFile::where('product_review_id', $id)->delete();
            ProductReview::where('id', $id)->delete();
            Storage::disk(StorageDisksEnum::DEFAULT)->delete($removeFiles);
            if (StorageDisksEnum::DEFAULT !== StorageDisksEnum::S3) {
                Storage::disk(StorageDisksEnum::S3)->delete($removeFiles);
            }

            DB::commit();

            return $this->successResponse('Delete product review success.');
        } catch (Exception $ex) {
            DB::rollBack();
            return $this->errorResponse('Delete product review failed.');
        }
    }

    /**
     * Update the status of multiple product reviews.
     *
     * This method allows bulk status updates including:
     * - Setting review status (pending, active, inactive)
     * - Automatically setting finished_at timestamp for non-pending statuses
     * - Transaction safety for bulk operations
     *
     * @param UpdateProductReviewStatusRequest $request Validated request with status and review IDs
     * @return JsonResponse JSON response confirming status update success/failure
     * @throws Exception When update operation fails
     */
    public function updateStatus(UpdateProductReviewStatusRequest $request): JsonResponse
    {
        $status = $request->input('status');
        $reviewIds = $request->input('review_ids');
        $finishedAt = $status === ProductReviewEnum::PENDING ? null : now();

        DB::beginTransaction();
        try {
            ProductReview::query()->whereIn('id', $reviewIds)
                ->update([
                    'status' => $status,
                    'finished_at' => $finishedAt
                ]);

            DB::commit();
            return $this->successResponse('Update product review status success.');
        } catch (Exception $ex) {
            DB::rollBack();
            return $this->errorResponse('Update product review status failed.');
        }
    }

    /**
     * Update the sharing status of multiple product reviews.
     *
     * This method controls review visibility including:
     * - Setting sharing permissions (deny, share_review, share_campaign)
     * - Bulk operations for multiple reviews
     * - Transaction safety
     *
     * @param UpdateProductReviewSharingStatusRequest $request Validated request with sharing status and review IDs
     * @return JsonResponse JSON response confirming sharing status update success/failure
     * @throws Exception When update operation fails
     */
    public function updateSharingStatus(UpdateProductReviewSharingStatusRequest $request): JsonResponse
    {
        $status = $request->input('status');
        $reviewIds = $request->input('review_ids');

        DB::beginTransaction();
        try {
            ProductReview::whereIn('id', $reviewIds)->update(['allow_sharing' => $status]);

            DB::commit();
            return $this->successResponse('Update product review sharing status success.');
        } catch (Exception $ex) {
            DB::rollBack();
            return $this->errorResponse('Update product review sharing status failed.');
        }
    }

    /**
     * Get all product review keywords with filtering and pagination.
     *
     * @param ReviewProductKeywordRequest $request Validated request with filtering parameters
     * @return JsonResponse JSON response with keyword list
     * @throws \Exception When service operation fails
     */
    public function getKeywords(ReviewProductKeywordRequest $request): JsonResponse
    {
        try {
            $response = $this->productReviewService::loadAllKeyWord($request);
            return $this->successResponse($response);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Add a new product review keyword.
     *
     * @param AddKeywordProductReviewRequest $request Validated request with keyword data
     * @return JsonResponse JSON response confirming keyword creation
     * @throws \Exception When service operation fails
     */
    public function addKeywords(AddKeywordProductReviewRequest $request): JsonResponse
    {
        try {
            $response = $this->productReviewService::addKeyWord($request);
            return $this->successResponse($response);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Update an existing product review keyword.
     *
     * @param AddKeywordProductReviewRequest $request Validated request with updated keyword data
     * @return JsonResponse JSON response confirming keyword update
     * @throws \Exception When service operation fails
     */
    public function updateKeywords(AddKeywordProductReviewRequest $request): JsonResponse
    {
        try {
            $response = $this->productReviewService::updateKeyWord($request);
            return $this->successResponse($response);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Delete a product review keyword.
     *
     * @param string|int $keywordId Keyword ID to delete
     * @return JsonResponse JSON response confirming keyword deletion
     * @throws \Exception When service operation fails
     */
    public function deleteKeywords($keywordId): JsonResponse
    {
        try {
            $response = $this->productReviewService::deleteKeyWord($keywordId);
            return $this->successResponse($response);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Get a specific product review keyword by ID.
     *
     * @param string|int $keywordId Keyword ID to retrieve
     * @return JsonResponse JSON response with keyword data
     * @throws \Exception When service operation fails
     */
    public function getKeyword($keywordId): JsonResponse
    {
        try {
            $response = $this->productReviewService::loadKeyWord($keywordId);
            return $this->successResponse($response);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Import product reviews from uploaded file.
     *
     * This method handles bulk review imports including:
     * - Excel/CSV file processing
     * - Data validation and error handling
     * - Duplicate entry detection
     * - User-specific import tracking
     * - Comprehensive error reporting
     *
     * @param ImportReviewsRequest $request Validated request with uploaded file
     * @return JsonResponse JSON response confirming import success/failure
     * @throws Exception When import operation fails
     */
    public function importReviews(ImportReviewsRequest $request)
    {
        $file = $request->file('file');
        $userId = currentUser()->getUserId();
        try {
            Excel::import(new ImportProductReview($userId), $file);
            return $this->successResponse();
        } catch (Exception $e) {
            $code = $e->getCode();

            if ($code === 2002) {
                return $this->errorResponse('Import failed', 403);
            }

            if ($code === 23000) {
                return $this->errorResponse('Duplicate entry', 403);
            }

            logToDiscord($code . ': Seller import product reviews failed' . json_encode($e->getMessage()));
            return $this->errorResponse($e->getMessage(), $code > 0 ? $code : 403);
        }
    }
}

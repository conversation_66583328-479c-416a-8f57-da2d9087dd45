<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CustomOptionTypeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\MOCKUP_LAYERS;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\StorageDisksEnum;
use App\Facades\ProductTemplateFacade;
use App\Http\Requests\CrawlVariantStockRequest;
use App\Http\Requests\DeleteProductExpress2MockupRequest;
use App\Http\Requests\Product\CreateProductRequest;
use App\Http\Requests\Product\DeleteProductMockupRequest;
use App\Http\Requests\Product\SortFulFillProductRequest;
use App\Http\Requests\Product\UpdateProductRequest;
use App\Http\Requests\Product\UpdateShippingRuleRequest;
use App\Http\Requests\Product\UpdateVariantsRequest;
use App\Http\Requests\SaveMockupDesignRequest;
use App\Http\Requests\SellerSaveProductVariantsRequest;
use App\Models\Campaign;
use App\Models\Category;
use App\Models\Elastic;
use App\Models\File;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductFulfillMapping;
use App\Models\ProductPoint;
use App\Models\ProductPromotion;
use App\Models\ProductVariant;
use App\Models\ShippingRule;
use App\Models\Slug;
use App\Models\SystemColor;
use App\Models\Template;
use App\Models\User;
use App\Rules\CheckExistsIdRule;
use App\Services\FileUploadService;
use App\Services\FulfillmentService;
use App\Traits\CanUseSingleStoreConnection;
use App\Traits\ElasticClient;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator as Paginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use InvalidArgumentException;
use League\Flysystem\FilesystemException;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Throwable;

class ProductController extends Controller
{
    use ElasticClient, CanUseSingleStoreConnection;

    public static function index(Request $request, string $type = ProductType::TEMPLATE)
    {
        if ($type === ProductType::TEMPLATE) {
            currentUser()->hasPermissionOrAbort('get_products');
        }

        $keywords = $request->get('q');
        $categoryId = $request->get('category_id');
        $status = $request->get('status');
        $systemType = $request->get('system_type');
        $sortField = $request->get('sort');
        $sortDirection = $request->get('direction');
        $sortableFields = ['updated_at', 'base_cost', 'shipping_cost', 'created_at'];
        $currentPage = $request->get('page', 1);
        $currentPage = ($currentPage > 0) ? $currentPage : 1;
        $perPage = $request->get('per_page', 15);
        $noLimit = $request->boolean('no-limit');
        $onPromotion = $request->get('on_promotion');
        $includeTotal = true;
        $queryElastic = [];
        $queryElastic['type'] = $type;

        if (!empty($keywords)) {
            $queryElastic['search'] = cleanSpecialCharacters($keywords);
        }

        if (!is_null($status)) {
            $queryElastic['status'] = $status;
        }

        if (!empty($systemType)) {
            $queryElastic['system_type'] = $systemType;
        }

        if (!is_null($categoryId)) {
            $productIds = ProductCategory::query()
                ->where('category_id', $categoryId)
                ->pluck('product_id');

            $queryElastic['ids'] = $productIds;
        }

        if (!is_null($sortField) && in_array($sortField, $sortableFields, true)) {
            $queryElastic['sort'] = [
                $sortField => $sortDirection,
            ];
        }

        $queryElastic['from'] = ($currentPage - 1) * $perPage;
        $queryElastic['limit'] = $perPage;

        if ((int)$onPromotion === 1) {
            $queryElastic['from'] = 0;
            $queryElastic['limit'] = 1000;
        }

        if (
            $noLimit
            &&
            currentUser()->isAdmin()
        ) {
            $perPage = 10000;
            $includeTotal = false;
        }

        if ($includeTotal) {
            [$products, $total] = (new Elastic())->getProduct($queryElastic);
        } else {
            $products = (new Elastic())->getProduct($queryElastic, false);
        }
        if (currentUser()->isAdmin() && !empty($products)) {
            $productIds = Arr::pluck($products, 'id');
            $productIds = array_reverse($productIds);
            $productPoints = ProductPoint::query()
                ->select([
                    'points',
                    'product_id',
                ])
                ->whereIn('product_id', $productIds)
                ->orderByRaw('FIELD(product_id,' . implode(',', $productIds) . ') desc')
                ->get();
            $products_sku = Product::query()->select(['sku', 'id'])->whereIn('id', $productIds)->get();
            foreach ($products as &$product) {
                $product['points'] = 1;
                foreach ($productPoints as $key => $productPoint) {
                    if ($productPoint->product_id === $product['id']) {
                        $product['points'] = $productPoint->points;
                        $productPoints->forget($key);
                        break;
                    }
                }
                foreach ($products_sku as $key => $product_sku) {
                    if ($product_sku->id === $product['id'] && empty($product['sku'])) {
                        $product['sku'] = $product_sku->sku;
                        $products_sku->forget($key);
                        break;
                    }
                }
            }
            if ((int)$onPromotion === 1) {
                // Get products on promotion
                $onPromotion = ProductPromotion::query()
                    ->select('product_id', 'start_time', 'end_time', 'point')
                    ->where('end_time', '>=', now())
                    ->get();

                $promotionMap = $onPromotion->keyBy('product_id');

                // Filter products
                $filteredProducts = array_filter($products, function ($product) use ($promotionMap) {
                    return isset($promotionMap[$product['id']]);
                });

                //get promotion points
                foreach ($filteredProducts as &$product) {
                    $product['promotion_point'] = Arr::get($promotionMap[$product['id']], 'point', 0);

                    $startTime = Arr::get($promotionMap[$product['id']], 'start_time');
                    $endTime = Arr::get($promotionMap[$product['id']], 'end_time');
                    $product['start_time'] = Carbon::parse($startTime)->format('Y-m-d');
                    $product['end_time'] = Carbon::parse($endTime)->format('Y-m-d');
                }

                // Get total count of filtered products
                $total = count($filteredProducts);
                // Paginate filtered products
                $products = array_slice($filteredProducts, ($currentPage - 1) * $perPage, $perPage);

                return new Paginator(
                    $products,
                    $total,
                    $perPage,
                    $currentPage
                );
            }
        }

        if ($includeTotal) {
            return new Paginator(
                $products,
                $total,
                $perPage,
                $currentPage
            );
        }

        return $products;
    }

    /**
     * @param CreateProductRequest $request
     * @return JsonResponse
     */
    public function store(CreateProductRequest $request): JsonResponse
    {
        $categoryId = $request->post('category_id');
        $newProduct = [
            'name' => $request->post('name'),
            'description' => $request->post('description'),
            'sku' => $request->post('sku'),
            'price' => $request->post('price'),
            'base_cost' => $request->post('base_cost'),
            'shipping_cost' => $request->post('shipping_cost'),
            'extra_print_cost' => $request->post('extra_print_cost'),
            'product_type' => ProductType::TEMPLATE,
        ];

        try {
            Db::beginTransaction();
            $productId = Product::query()->insertGetId($newProduct);

            if (!is_null($categoryId)) {
                (new ProductCategory())->insertProductCategories((int)$categoryId, $productId);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'product_id' => $productId
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @param $productId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|Product
     */
    public function detail($productId)
    {
        currentUser()->hasPermissionOrAbort('get_products');

        $product = Product::query()
            ->where('id', $productId)
            ->with([
                'mockups' => function ($query) {
                    return $query->withoutGlobalScope('getActive');
                },
                'category',
                'product_categories',
                'videos' => function ($query) {
                    return $query->withoutGlobalScope('getActive');
                },
            ])
            ->firstOrFail();
        $product->videos = $product->videos->map(function (File $video) {
            $option = json_decode($video->option, true);
            $video->thumbnail = Arr::get($option, 'thumbnail');
            unset($video->option);
            return $video;
        });

        $all = $product->product_categories;
        $ids = $all->pluck('id')->toArray();
        $parentIds = $all->pluck('parent_id')->toArray();
        $leafNodeIds = array_diff($ids, $parentIds);

        $product->setRelation('product_categories', $all->whereIn('id', $leafNodeIds)->flatten());

        $thumb = File::query()
            ->where('product_id', $productId)
            ->where('type', FileTypeEnum::THUMBNAIL)
            ->orderBy('created_at', 'desc')
            ->first();

        $product->setRelation('thumbnail_img', $thumb);

        return $product;
    }

    /**
     * @param UpdateProductRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function update(UpdateProductRequest $request): JsonResponse
    {
        $productId = $request->post('product_id');
        $categoryIds = $request->post('category_ids');
        $systemType = $request->post('system_type');
        $categoryIds = explode(',', $categoryIds);
        $quantity = $request->post('quantity');
        $full_printed = $request->post('full_printed');
        if ((int)$full_printed === ProductPrintType::HANDMADE) {
            $rules = array(
                'options.custom_options.group.name' => 'max:120',
                'options.custom_options.group.limit' => 'required|min:1|max:20',
                'options.custom_options.group.extra_custom_fee' => 'nullable|numeric|min:0',
                'options.custom_options.options' => [
                    'required',
                    'array'
                ],
                'options.custom_options.options.*.type' => [
                    'required',
                    Rule::in(CustomOptionTypeEnum::getValues())
                ],
                'options.custom_options.options.*.label' => 'required|max:120',
                'options.custom_options.options.*.price' => 'nullable|numeric|min:0.1',
                'options.custom_options.options.*.unrequired' => 'nullable|boolean',
                'options.custom_options.options.*.max_length' => function ($attribute, $value, $fail) use ($request) {
                    $type = $request->input(str_replace('.value', '.type', $attribute));
                    if ($value !== '' && $type === CustomOptionTypeEnum::TEXT && (int)$value < 1) {
                        $fail(__('The value must be greater than 0.'));
                    }
                },
                'options.custom_options.options.*.value' => function ($attribute, $value, $fail) use ($request) {
                    $type = $request->input(str_replace('.value', '.type', $attribute));
                    if ($type === CustomOptionTypeEnum::TEXT && strlen($value) > 120) {
                        $fail(__('The value may not be greater than 120 characters.'));
                    }
                    if ($type === CustomOptionTypeEnum::DROPDOWN) {
                        if (empty($value)) {
                            $fail(__('The select options field is required.'));
                        }
                        if (!is_array($value)) {
                            $fail(__('The select options must be an array.'));
                        }
                    }
                },
            );
            $validator = Validator::make(['options' => json_decode($request->post('options'), true)], $rules, [
                'options.custom_options.group.name.required' => 'The group name field is required.',
                'options.custom_options.group.name.limit.required' => 'The group limit field is required.',
                'options.custom_options.group.limit.min' => 'The group limit must be at least :min.',
                'options.custom_options.group.limit.max' => 'The group limit may not be greater than :max.',
                'options.custom_options.group.name.limit.min' => 'The group name must be at least :min.',
                'options.custom_options.group.name.limit.max' => 'The group name may not be greater than :max.',
                'options.custom_options.group.extra_custom_fee.numeric' => 'The extra custom fee must be a number.',
                'options.custom_options.group.extra_custom_fee.min' => 'The extra custom fee must be at least :min.',
                'options.custom_options.options.required' => 'Custom option product needs at least one option.',
                'options.custom_options.options.array' => 'The options must be an array.',
                'options.custom_options.options.*.type.required' => 'The type field is required.',
                'options.custom_options.options.*.type.in' => 'The selected type is invalid.',
                'options.custom_options.options.*.label.required' => 'The label field is required.',
                'options.custom_options.options.*.label.max' => 'The label may not be greater than :max characters.',
                'options.custom_options.options.*.price.min' => 'The price must be at least :min.',
            ]);
            if ($validator->fails()) {
                return $this->errorResponse($validator->getMessageBag());
            }
        }

        $newProduct = [
            'name' => $request->post('name'),
            'description' => $request->post('description'),
            'sku' => $request->post('sku'),
            'price' => $request->post('price'),
            'base_cost' => $request->post('base_cost'),
            'shipping_cost' => $request->post('shipping_cost'),
            'print_spaces' => $request->post('print_spaces'),
            'extra_print_cost' => $request->post('extra_print_cost'),
            'priority' => (int)$request->post('priority'),
            'attributes' => $request->post('attributes'),
            'options' => $request->post('options'),
            'full_printed' => $request->post('full_printed'),
            'pricing_mode' => $request->post('pricing_mode'),
            'market_location' => $request->post('market_location'),
            'sync_status' => 0,
        ];

        if (in_array($systemType, ProductSystemTypeEnum::getValues())) {
            $newProduct['system_type'] = $systemType;
        }
        if ($systemType === ProductSystemTypeEnum::FULFILL_FBA) {
            if (!is_null($quantity)) {
                $newProduct['quantity'] = $quantity;
            } else {
                return $this->errorResponse('Quantity is required');
            }
        }
        if (!empty($categoryIds)) {
            ProductCategory::query()
                ->where('product_id', $productId)
                ->delete();

            foreach ($categoryIds as $categoryId) {
                (new ProductCategory())->insertProductCategories((int)$categoryId, (int)$productId);
            }
        }

        Product::query()
            ->where('id', $productId)
            ->update($newProduct);

        $tags = CacheKeys::getProductTemplateTags($productId);

        foreach ($categoryIds as $categoryId) {
            syncClearCache([
                'tags' => $tags,
                CacheKeys::getTemplateProductByTemplateId($productId),
                CacheKeys::PRODUCT_ID_BY_CATEGORY . (int)$categoryId,
            ], CacheKeys::CACHE_TYPE_ALTERNATIVE);
        }

        return $this->successResponse();
    }

    public function saveVideo(Request $request): JsonResponse
    {
        $request->validate([
            'product_id' => 'required|integer',
            'video' => 'required',
            'thumbnail' => 'required',
            'file_id' => 'nullable|integer',
            'status' => 'in:' . implode(',', FileStatusEnum::getValues()),
        ]);

        $productId = $request->get('product_id');
        $fileId = $request->get('file_id');
        $position = $request->get('position');
        $status = $request->get('status');
        $video = $request->get('video');
        if (empty($video) && $fileId) {
            File::query()
                ->where('id', $fileId)
                ->withoutGlobalScope('getActive')
                ->delete();
            return $this->successResponse();
        }

        $link = saveTempFileAws($request->get('video'), 'p/' . $productId . '/video');
        $thumbnail = saveTempFileAws($request->get('thumbnail'), 'p/' . $productId . '/video_thumbnail');

        $data = [
            'product_id' => $productId,
            'file_url' => $link,
            'type' => FileTypeEnum::VIDEO,
            'position' => $position,
            'status' => $status,
            'option' => json_encode(['thumbnail' => $thumbnail]),
        ];

        if ($fileId) {
            File::query()
                ->whereKey($fileId)
                ->withoutGlobalScope('getActive')
                ->update($data);
        } else {
            File::query()
                ->create($data);
        }

        return $this->successResponse();
    }

    /**
     * @param DeleteProductMockupRequest $request
     * @return JsonResponse
     */
    public function deleteFileMockup(DeleteProductMockupRequest $request): JsonResponse
    {
        $productId = $request->input('product_id');
        $fileId = $request->input('file_id');
        $file = File::query()
            ->firstWhere([
                'id' => $fileId,
                'product_id' => $productId
            ]);

        if (is_null($file)) {
            return $this->errorResponse('File not found.');
        }

        // Keep file to render old mockup
        // delete_s3_files([$file->toArray()]);

        try {
            if ($file->delete()) {
                return $this->successResponse();
            }

            return $this->errorResponse();
        } catch (Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Deletes the mockup files for a product.
     *
     * @param DeleteProductExpress2MockupRequest $request The request object containing the product_id, file_id, and type.
     * @return JsonResponse Returns a JSON response indicating success or failure of the operation.
     * @throws Exception If an error occurs during the operation.
     */
    public function deleteFilesMockupExpress2(DeleteProductExpress2MockupRequest $request): JsonResponse
    {
        $productId = $request->input('product_id');
        $fileId = $request->input('file_id');
        $type = $request->input('type');
        $designJson = File::query()
            ->select('design_json')
            ->firstWhere([
                'id' => $fileId,
                'product_id' => $productId
            ]);

        if (is_null($designJson)) {
            return $this->errorResponse('File not found.');
        }

        $designObject = json_decode($designJson);
        $partObject = $designObject->design_json;
        $fileObject = json_decode($partObject);
        $fileObject->$type = '';
        $partObject = $fileObject;

        try {
            $updated = File::query()
                ->where([
                    'id' => $fileId,
                    'product_id' => $productId
                ])
                ->update(['design_json' => json_encode($partObject)]);

            if ($updated) {
                return $this->successResponse();
            }
        } catch (Exception $e) {
            // noop
        }
        return $this->errorResponse();
    }

    /**
     * Deletes the mockup files for a product.
     * @param DeleteProductMockupRequest $request
     * @return JsonResponse
     * @throws InvalidArgumentException
     */
    public function deleteFilesMockup3D(DeleteProductMockupRequest $request): JsonResponse
    {
        $productId = $request->input('product_id');
        $fileId = $request->input('file_id');
        $type = $request->input('type');
        $designJson = File::query()
            ->select('design_json')
            ->firstWhere([
                'id' => $fileId,
                'product_id' => $productId
            ]);

        if (is_null($designJson)) {
            return $this->errorResponse('File not found.');
        }

        $designObject = json_decode($designJson);
        $partObject = $designObject->design_json;
        $fileObject = json_decode($partObject);
        $fileObject->$type = '';
        $partObject = $fileObject;

        try {
            $updated = File::query()
                ->where([
                    'id' => $fileId,
                    'product_id' => $productId
                ])
                ->update(['design_json' => json_encode($partObject)]);

            if ($updated) {
                return $this->successResponse();
            }
        } catch (Exception $e) {
            // noop
        }
        return $this->errorResponse();
    }

    /**
     * Duplicate template product
     *
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     * @throws FilesystemException|Throwable
     */
    public function duplicate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|integer',
            'sku' => 'required|unique:product,sku',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first(),
            ]);
        }

        ProductTemplateFacade::getProductTemplate($request->input('product_id'))
            ->withSku($request->input('sku'))
            ->withMockups()
            ->withVariants()
            ->withVideos();

        $pendingRecords = [];
        try {
            DB::beginTransaction();
            ProductTemplateFacade::cloneProduct()
                ->cloneAttributes();
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        } catch (Throwable $e) {
        }


        $mockups = ProductTemplateFacade::getMockups();
        foreach ($mockups as $mockup) {
            $fillData = ProductTemplateFacade::cloneMockup($mockup);
            $pendingRecords[] = $mockup->replicate()->fill($fillData);
        }

        $videos = ProductTemplateFacade::getVideos();
        if ($videos && $videos->count() > 0) {
            foreach ($videos as $video) {
                $fillData = ProductTemplateFacade::cloneVideo($video);
                $pendingRecords[] = $video->replicate()->fill($fillData);
            }
        }

        $thumbnails = ProductTemplateFacade::getThumbnails();
        if ($thumbnails && $thumbnails->count() > 0) {
            foreach ($thumbnails as $thumbnail) {
                $fillData = ProductTemplateFacade::cloneThumbnail($thumbnail);
                $pendingRecords[] = $thumbnail->replicate()->fill($fillData);
            }
        }

        $result = ProductTemplateFacade::copyPendingFiles();
        if (!$result) {
            ProductTemplateFacade::deleteNewProduct();
            return response()->json([
                'success' => false,
                'message' => 'Copy pending files failed',
            ]);
        }

        $success = false;
        try {
            DB::beginTransaction();
            $result = ProductTemplateFacade::cloneCategory();
            if ($result === false) {
                throw new Exception('Clone category failed');
            }
            $result = ProductTemplateFacade::cloneShippingRules();
            if ($result === false) {
                throw new Exception('Clone shipping rules failed');
            }
            if (count($pendingRecords) > 0) {
                $result = File::insert(collect($pendingRecords)->toArray());
                if ($result === false) {
                    throw new Exception('Insert pending records failed');
                }
            }
            $result = ProductTemplateFacade::cloneVariants();
            if ($result === false) {
                throw new Exception('Clone variants failed');
            }
            $result = ProductTemplateFacade::cloneDesignMapping();
            if ($result === false) {
                throw new Exception('Clone design mapping failed');
            }
            $result = ProductTemplateFacade::cloneFulfillMapping();
            if ($result === false) {
                throw new Exception('Clone fulfill mapping failed');
            }
            $result = ProductTemplateFacade::cloneProductPoints();
            if ($result === false) {
                throw new Exception('Clone product points failed');
            }
            $result = ProductTemplateFacade::cloneProductPromotion();
            if ($result === false) {
                throw new Exception('Clone product promotion failed');
            }
            $result = ProductTemplateFacade::cloneProductSizeGuide();
            if ($result === false) {
                throw new Exception('Clone product size guide failed');
            }
            DB::commit();
            $success = true;
        } catch (Exception $e) {
            graylogError('Duplicate product failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'old_product_id' => $request->input('product_id'),
                'category' => 'product_template_service',
            ]);
            DB::rollBack();
        }

        if ($success === false) {
            ProductTemplateFacade::deleteNewProduct();
            ProductTemplateFacade::deleteFolder();
            return response()->json([
                'success' => false,
                'message' => 'Duplicate product failed',
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Duplicate product success',
            'data' => [
                'product_id' => ProductTemplateFacade::getNewProduct()->id,
            ]
        ]);
    }

    public function duplicateMockup(Request $request): jsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_id' => 'required|integer',
            'file_id' => 'required|integer',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()
            ]);
        }

        $productId = $request->get('product_id');
        $fileId = $request->get('file_id');
        $file = File::query()
            ->where('id', $fileId)
            ->where('product_id', $productId)
            ->where('render_type', FileRenderType::RENDER_3D)
            ->withoutGlobalScope('getActive')
            ->first();
        if (!$file) {
            return $this->errorResponse('File not found.');
        }
        $folder = 'p/' . $productId;
        $hash = md5(Str::random(9) . now());
        $newFileUrl = FileUploadService::duplicateFile($file->file_url, $folder . '/' . substr($hash, 0, 12));
        if (!$newFileUrl) {
            return $this->errorResponse('Duplicate file failed.');
        }

        $designJson = json_decode($file->design_json);
        $colorPath = $designJson->color ?? null;
        $shadowPath = $designJson->shadow ?? null;
        $cropPath = $designJson->crop ?? null;
        $shadowHeather = $designJson->shadow_heather ?? null;
        $newDesignJson = [];
        if ($colorPath) {
            $colorPath = FileUploadService::duplicateFile($colorPath, $folder . '/' . $this->genMockupLayerFileName(MOCKUP_LAYERS::COLOR));
            if ($colorPath) {
                $newDesignJson['color'] = $colorPath;
            }
        }
        if ($shadowPath) {
            $shadowPath = FileUploadService::duplicateFile($shadowPath, $folder . '/' . $this->genMockupLayerFileName(MOCKUP_LAYERS::SHADOW));
            if ($shadowPath) {
                $newDesignJson['shadow'] = $shadowPath;
            }
        }
        if ($cropPath) {
            $cropPath = FileUploadService::duplicateFile($cropPath, $folder . '/' . $this->genMockupLayerFileName(MOCKUP_LAYERS::CROP));
            if ($cropPath) {
                $newDesignJson['crop'] = $cropPath;
            }
        }
        if ($shadowHeather && $shadowPath) {
            $shadowHeather = FileUploadService::duplicateFile($shadowHeather, $folder . '/' . $this->genShadowHeatherFileName($newDesignJson['shadow']));
            $newDesignJson['shadow_heather'] = $shadowHeather;
        }

        $newFile = $file->replicate()
            ->fill([
                'file_url' => $newFileUrl,
                'file_url_2' => null,
                'render_type' => FileRenderType::EXPRESS,
                'design_json' => json_encode($newDesignJson),
            ]);
        if ($newFile->save()) {
            return $this->successResponse($newFile);
        }

        return $this->errorResponse('Duplicate file failed.');
    }

    private function genMockupLayerFileName($layer): string
    {
        $hash = md5(Str::random(9) . now());
        $name = substr($hash, 0, 12);
        return $name . '_' . substr($layer, 0, 2);
    }

    private function genShadowHeatherFileName($shadowFileName): string
    {
        $offset = strrpos($shadowFileName, '/') + 1;
        $length = strrpos($shadowFileName, '_sh') - $offset;
        return substr($shadowFileName, $offset, $length) . '_sdh';
    }

    public function updateImageMockup(Request $request): JsonResponse
    {
        $request->validate([
            'files' => 'required',
            'files.*' => 'required|image',
            'type' => 'string',
            'file_id' => 'required|exists:file,id',
            'product_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product'),
            ]
        ]);
        $files = $request->file('files');
        $fileId = $request->post('file_id');
        $productId = $request->post('product_id');
        $type = strtolower($request->post('type'));
        $cdnMode = $request->post('cdn');

        try {
            DB::beginTransaction();
            $file = $files[0];
            $hash = md5_file($file);
            $name = substr($hash, 0, 6);
            $fileName = $name . '.' . $file->extension();
            $folder = 'p/' . $productId;

            if (in_array($type, ['glb', 'crop', 'shadow', 'color'])) {
                $folder .= '/3d/' . $fileId;
            }

            $cdnPath = null;
            if ($cdnMode) {
                // $cdnPath = UploadController::uploadToCloudinary($file, $folder, $name);
            }

            $path = $file->storePubliclyAs(
                $folder,
                $fileName,
                StorageDisksEnum::DEFAULT
            );
            File::query()
                ->where([
                    'id' => $fileId,
                    'product_id' => $productId
                ])
                ->update([
                    'file_url' => $path,
                    'file_url_2' => $cdnPath,
                    'file_name' => $file->getClientOriginalName(),
                    'file_size' => $file->getSize(),
                    'product_id' => $productId,
                    'type' => 'mockup',
                ]);

            DB::commit();

            return response()->json([
                'images' => $path,
                'success' => true
            ]);
        } catch (Exception $exception) {
            DB::rollBack();
            return response()->json([
                'message' => $exception->getMessage(),
                'success' => true
            ]);
        }
    }

    /**
     * @param DeleteProductMockupRequest $request
     * @return JsonResponse
     */
    public function deleteFileThumbnail(DeleteProductMockupRequest $request): JsonResponse
    {
        $productId = $request->input('product_id');
        $fileId = $request->input('file_id');
        $file = File::query()
            ->firstWhere([
                'id' => $fileId,
                'product_id' => $productId
            ]);

        if (is_null($file)) {
            return $this->errorResponse('File not found.');
        }

        delete_s3_files([$file->toArray()]);

        try {
            $deleted = $file->delete();
            $updated = Product::query()
                ->where('id', $productId)
                ->update(['thumb_url' => null]);

            if ($deleted && $updated) {
                return $this->successResponse();
            }

            return $this->errorResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getShippingRules(Request $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('get_products');

        $productId = $request->get('product_id');
        $product = Product::query()
            ->where('id', $productId)
            ->with('shipping_rules')
            ->firstOrFail()
            ->toArray();

        return response()->json([
            'success' => !is_null($product['shipping_rules']),
            'data' => !is_null($product['shipping_rules']) ? $product['shipping_rules'] : []
        ]);
    }

    public function getPromotion(Request $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('get_products');

        try {
            $productId = $request->get('product_id');
            $promotion = ProductPromotion::query()
                ->where('product_id', $productId)
                ->first();


            //check if collection empty
            if (!empty($promotion)) {
                $promotion->start_time = Carbon::parse($promotion->start_time)->format('Y-m-d');
                $promotion->end_time = Carbon::parse($promotion->end_time)->format('Y-m-d');

                return $this->successResponse($promotion->toArray());
            }
            return $this->successResponse([]);
        } catch (Exception $e) {

            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateOrInsertPromotion(Request $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('update_product');

        $validator = Validator::make($request->all(), [
            'product_id' => 'required|integer',
            'start_time' => 'required|date',
            'end_time' => 'required|date',
            'point' => 'required|numeric'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()
            ]);
        }

        $productId = $request->post('product_id');
        $startTime = $request->post('start_time');
        $endTime = $request->post('end_time');
        $point = $request->post('point');

        if ($productId === 0) {
            return response()->json([
                'success' => false,
                'message' => 'Product id is required'
            ]);
        }

        $startTime = Carbon::parse($startTime)->format('Y-m-d H:i:s');
        //set endTime to 23:59:59 (admin is on UTC+7) so must convert to UTC+0
        $endTime = Carbon::parse($endTime)->endOfDay()->subRealHours(7)->format('Y-m-d H:i:s');

        try {
            DB::beginTransaction();
            $result = ProductPromotion::query()
                ->withoutTrashed()
                ->updateOrInsert(
                    ['product_id' => $productId],
                    [
                        'id' => Str::uuid(),
                        'start_time' => $startTime,
                        'end_time' => $endTime,
                        'point' => $point,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]
                );

            if ($result) {
                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => 'Update promotion success'
                ]);
            }
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Update promotion failed'
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function deletePromotion(Request $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('update_product');

        $productId = $request->get('product_id');
        try {
            DB::beginTransaction();
            $result = ProductPromotion::query()
                ->where('product_id', $productId)
                ->delete();

            if ($result) {
                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => 'Delete promotion success'
                ]);
            }
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Delete promotion failed'
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getVariants(Request $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('get_products');

        $productId = $request->get('product_id');
        $product = Product::query()
            ->select('id', 'options', 'base_cost', 'supplier_id')
            ->where('id', $productId)
            ->with('variants')
            ->firstOrFail()
            ->toArray();

        if ($product) {
            $cfg = suppliers()->first(fn($cfg) => $cfg['supplier_id'] === $product['supplier_id']);
            $product['manual_update_stock'] = (bool)data_get($cfg, 'sync_oss_variant');
        }

        return response()->json([
            'success' => !is_null($product['variants']),
            'data' => $product
        ]);
    }

    /**
     * @param \App\Http\Requests\CrawlVariantStockRequest $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function crawlVariantStock(CrawlVariantStockRequest $request): JsonResponse
    {
        try {
            $cfg = suppliers()->firstWhere(
                'supplier_id', $request->input('supplier_id')
            );
            $handlerClass = data_get($cfg, 'sync_oss_variant');
            $variant = ProductVariant::query()
                ->where('sku', $request->input('variant_sku'))
                ->where('product_id', $request->input('product_id'))
                ->firstOrFail();

            /** @var \App\Providers\FulfillAPI\AbstractSyncVariantOOS $handler */
            $handler = app($handlerClass, [
                'supplierConfig' => $cfg,
                'variants' => collect([$variant])
            ]);

            $qty = optional($handler->handle()->first())->quantity;
            if (is_null($qty)) {
                return $this->errorResponse('Invalid response');
            }

            return $this->successResponse(
                ['quantity' => $qty],
                'Sync variant OOS success'
            );
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param UpdateShippingRuleRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function updateShippingRules(UpdateShippingRuleRequest $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('update_product');

        $productId = $request->post('product_id');
        $rules = $request->post('rules');

        $rules = array_map(static function ($rule) use ($productId) {
            $rule['product_id'] = $productId;
            return $rule;
        }, $rules);

        try {
            DB::beginTransaction();
            ShippingRule::query()
                ->where('product_id', $productId)
                ->delete();

            $result = ShippingRule::query()->insert($rules);

            if ($result > 0) {
                DB::commit();

                return $this->successResponse(); // Use ApiResponse traits
            }

            DB::rollBack();
            return $this->errorResponse('Update shipping rule failed');
        } catch (Exception $e) {
            DB::rollBack();
            return $this->errorResponse('Update shipping rule failed');
        }
    }

    /**
     * @param UpdateVariantsRequest $request
     * @return JsonResponse
     */
//    public function updateVariants(UpdateVariantsRequest $request): JsonResponse
//    {
//        $productId = $request->post('product_id');
//        $variants = $request->post('variants');
//
//        try {
//            DB::beginTransaction();
//            ProductVariant::query()
//                ->where('product_id', $productId)
//                ->delete();
//
//            $result = ProductVariant::query()->insert($variants);
//
//            if ($result > 0) {
//                DB::commit();
//                return $this->successResponse(); // Use ApiResponse traits
//            }
//
//            DB::rollBack();
//            return $this->errorResponse('Update variant failed');
//        } catch (Exception $e) {
//            DB::rollBack();
//            return $this->errorResponse('Update variant failed');
//        }
//    }

    /**
     * @param Request $request
     * @return \Illuminate\Database\Eloquent\HigherOrderBuilderProxy|mixed
     */
    public function getByCategory(Request $request)
    {
        $categoryId = $request->get('category_id');
        $query = Category::query()
            ->select(['id', 'name', 'full_name'])
            ->with(['products' => function ($query) {
                $query
                    ->where([
                        'product_type' => ProductType::TEMPLATE,
                        'status' => ProductStatus::ACTIVE
                    ])
                    ->orderBy('updated_at', 'DESC');
            }]);

        if ($categoryId) {
            $query->where('id', $categoryId);
        }

        return $query->first()->products;
    }


    /**
     * Get a mockup list
     * @param int $productId
     * @param Request $request
     * @return JsonResponse
     */
    public function getMockupList(int $productId, Request $request): JsonResponse
    {
        $product = Product::query()
            ->where('id', $productId)->first();
        if (!$product) {
            return $this->errorResponse('Product not found');
        }
        $printSpace = $request->get('print_space');
        $systemType = $request->get('system_type');

        $query = File::query()
            ->select([
                'id',
                'file_url',
                'design_json',
                'option',
                'type',
                'type_detail',
                'color_fillable',
                'print_space',
                'position',
            ])
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'product_id' => $productId,
                'status' => FileStatusEnum::ACTIVE,
            ])
            ->orderBy('position');

        if (!empty($printSpace)) {
            $query->where('print_space', $printSpace);
        }

        if ((int)$product->full_printed === ProductPrintType::HANDMADE) {
            $query->where('render_type', FileRenderType::NONE);
        } else {
            $accessInfo = currentUser()->getInfoAccess();
            $query->when(isset($accessInfo) && $accessInfo instanceof User && !in_array('sleeve printspace', $accessInfo?->getTags() ?? []), function ($q) {
                $q->whereNotIn('print_space', PrintSpaceEnum::additionalPrintSpaces());
            });
            // this for new express render type
            if ($systemType === ProductSystemTypeEnum::EXPRESS_2) {
                $query->where('render_type', FileRenderType::EXPRESS);
            } else {
                $query->where('render_type', FileRenderType::RENDER_3D);
            }
        }

        $mockups = $query->get();

        if ($mockups->count() === 0) {
            return $this->errorResponse('Mockups not found');
        }

        // add default state for Vue data
        $mockups->each(fn($item) => $item->selected = false);

        return $this->successResponse($mockups);
    }

    public function getMockupListFromListTemplateId(Request $request): JsonResponse
    {
        $templateIds = $request->input('template_ids', []);
        $user = currentUser();
        if ($user->isSeller()) {
            $user->hasPermissionOrAbort('get_campaigns');
        }

        $mockups = File::query()
            ->select([
                'id',
                'product_id',
                'file_url',
                'design_json',
                'option',
                'type',
                'type_detail',
                'color_fillable',
                'print_space',
                'position'
            ])
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => FileRenderType::RENDER_3D,
                'status' => FileStatusEnum::ACTIVE,
            ])->when($templateIds, function ($query, $templateIds) {
                $query->whereIn('product_id', $templateIds);
            })
            ->orderBy('position')
            ->get();

        if ($mockups->count() === 0) {
            return $this->errorResponse('Empty mockups');
        }

        return $this->successResponse($mockups);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getMockupDetail(Request $request): JsonResponse
    {
        $fileId = $request->get('file_id');
        $productId = $request->get('product_id');
        $image = File::query()
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'id' => $fileId,
                'product_id' => $productId
            ])
            ->withoutGlobalScope('getActive')
            ->firstOrFail();

        $product = Product::query()
            ->where('id', $productId)
            ->firstOrFail();

        $printSpaces = $product->print_spaces;
        $options = $product->options;

        return $this->successResponse([
            'image' => $image,
            'print_spaces' => json_decode($printSpaces),
            'options' => json_decode($options),
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function getFileMockup(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file_id' => 'required|integer|exists:file,id'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        $fileId = $request->get('file_id');
        $image = File::query()
            ->select(['id', 'file_url', 'file_name', 'print_space', 'product_id', 'render_type', 'type_detail', 'design_json'])
            ->where('type', FileTypeEnum::MOCKUP)
            ->when($fileId, function ($query) use ($fileId) {
                $query->where('id', $fileId);
            })
            ->withoutGlobalScope('getActive')
            ->first();

        if (!$image) {
            return $this->errorResponse();
        }
        $productId = $image->product_id;
        $file = [];
        $file['id'] = $image->id;
        $file['file_name'] = $image->file_name;
        $file['file_url'] = $image->file_url;
        $file['print_space'] = $image->print_space;
        $file['product_id'] = $productId;
        $file['render_type'] = $image->render_type;
        $file['type_detail'] = $image->type_detail;
        $design_json = Str::isJson($image->design_json) ? json_decode($image->design_json, true, 512, JSON_THROW_ON_ERROR) : [];
        if (count($design_json)) {
            foreach ($design_json as $key => $value) {
                $file['layer_' . $key] = $value;
            }
        }
        $printSpaces = [];
        $options = [];
        if ($productId) {
            $product = Product::query()
                ->select(['print_spaces', 'options'])
                ->where('id', $productId)
                ->first();
            if ($product) {
                $printSpaces = Str::isJson($product->print_spaces) ? json_decode($product->print_spaces, true, 512, JSON_THROW_ON_ERROR) : [];
                $options = Str::isJson($product->options) ? json_decode($product->options, true, 512, JSON_THROW_ON_ERROR) : [];
            }
        }

        return $this->successResponse([
            'mockup' => $file,
            'print_spaces' => $printSpaces,
            'options' => $options,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function getFileImageUrl(Request $request): JsonResponse
    {
        $pattern = '^\/?thumb\/(\d+)-?(sub|\d+)?\.jpg$';
        $validator = Validator::make($request->all(), [
            'path' => [
                'required',
                "regex:/$pattern/",
            ]
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 400);
        }
        $fileId = 0;
        $connection = '';
        $path = $request->get('path');
        if (preg_match("#$pattern#", $path, $matches)) {
            $fileId = (int) $matches[1];
            $connection = $matches[2] ?? '';
        }
        if (empty($fileId)) {
            return $this->errorResponse('Invalid file', 400);
        }
        if (str_starts_with($connection, '0')) {
            $connection = (int) $connection;
        }
        $connections = array_keys(config('database.connections'));
        $connection = rtrim('mysql_' . $connection, '_');
        if (!in_array($connection, $connections, true)) {
            return $this->errorResponse('Invalid connection', 400);
        }
        $fileUrl = File::query()->on($connection)->where('id', $fileId)->value('file_url_2');
        if (!$fileUrl) {
            return $this->errorResponse('File not found', 404);
        }
        return $this->successResponse($fileUrl);
    }

    public function getCampaignMockups(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'slug' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag(), 500);
        }
        $slug = $request->get('slug');
        $slugInfo = Slug::query()->where('slug', $slug)->first();
        if (!$slugInfo) {
            return $this->errorResponse('Campaign not found');
        }
        $seller = User::query()->find($slugInfo->seller_id);
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select(['id', 'slug'])
            ->where('slug', $slug)
            ->whereIn('product_type', [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
            ])
            ->where('status', ProductStatus::ACTIVE)
            ->with([
                'products' => function ($query) {
                    $query->select([
                        'id',
                        'name',
                        'campaign_id'
                    ]);
                    $query->with(['images' => function ($query) {
                        $query->selectRaw('id,product_id,print_space,file_url,type,file_url_2,mockup_id');
                        $query->where(function ($query) {
                            $query->where(function ($query) {
                                $query->where('type', FileTypeEnum::IMAGE)->whereNotNull('design_id');
                            });
                            $query->orWhere(function ($query) {
                                $query->where('type', FileTypeEnum::DESIGN)->where('option', FileRenderType::PRINT);
                            });
                        });
                    }]);
                }
            ])
            ->first();
        if (!$campaign) {
            return $this->errorResponse('Campaign not found');
        }
        $campaign->products->map(function ($product) {
            $mockups = collect();
            $designs = collect();
            if ($product->images->isNotEmpty()) {
                $product->images->map(function ($image) use (&$mockups, &$designs) {
                    if ($image->type === FileTypeEnum::IMAGE) {
                        unset($image->type, $image->file_url);
                        $mockups->push($image);
                    } else {
                        unset($image->type, $image->file_url_2, $image->mockup_id);
                        $designs->push($image);
                    }
                });
            }
            $product->setRelation('mockups', $mockups);
            $product->setRelation('designs', $designs);
            unset($product->images);
            return $product;
        });
        return $this->successResponse($campaign);
    }

    /**
     * @param SaveMockupDesignRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function saveMockupDesign(SaveMockupDesignRequest $request): JsonResponse
    {
        $fileId = $request->post('file_id');
        $fileUrl = $request->post('file_url');
        $productId = $request->post('product_id');
        $designJson = $request->post('design_json');
        $printSpace = $request->post('print_space');
        $type_detail = $request->post('type_detail');
        $renderType = $request->post('render_type');
        $option = $request->post('option');
        $position = $request->post('position');
        $status = $request->post('status');
        $file_name = $request->post('file_name');
        $allowAdditionalPrintSpace = $request->post('allow_additional_print_space') ?? false;

        try {
            [$designJson,] = FulfillmentService::updateUrlOnDesignJson($designJson);
            $query = File::query();
            DB::beginTransaction();
            if (empty($fileId)) {
                $timestamp = time();
                $hash = md5((string)$timestamp);
                $fileName = substr($hash, 0, 6);
                $fileId = $query->insertGetId([
                    'file_name' => !empty($file_name) ? $file_name : $fileName,
                    'file_url' => $fileUrl,
                    'file_url_2' => $fileUrl,
                    'product_id' => $productId,
                    'type' => FileTypeEnum::MOCKUP,
                    'design_json' => $designJson,
                    'print_space' => $printSpace,
                    'type_detail' => $type_detail,
                    'render_type' => $renderType,
                    'option' => $option,
                    'position' => $position,
                    'status' => $status,
                ]);
            } else {
                $data = [
                    'design_json' => $designJson,
                    'print_space' => $printSpace,
                    'type_detail' => $type_detail,
                    'render_type' => $renderType,
                    'option' => $option,
                    'position' => $position,
                    'status' => $status,
                    'allow_additional_print_space' => $allowAdditionalPrintSpace
                ];
                if (!empty($file_name)) {
                    $data['file_name'] = $file_name;
                }
                $query->withoutGlobalScope('getActive')->where('id', $fileId)->update($data);
            }
            DB::commit();

            return $this->successResponse(
                ['file_id' => $fileId],
                'Save mockup design success'
            );
        } catch (Exception $exception) {
            DB::rollback();
            return $this->errorResponse('Save mockup design failed');
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function changeTemplateProductStatus(Request $request): JsonResponse
    {
        $productId = $request->input('product_id');
        $status = $request->input('status');
        if (!in_array($status, ProductStatus::getValues())) {
            return $this->errorResponse('Status not valid!');
        }

        try {
            DB::beginTransaction();
            $product = Product::query()->find($productId);

            if (empty($product)) {
                return $this->errorResponse('Product not valid!');
            }

            switch ($status) {
                case ProductStatus::ACTIVE:
                    $product->status = ProductStatus::ACTIVE;
                    break;
                case ProductStatus::INACTIVE:
                    $product->status = ProductStatus::INACTIVE;
                    break;
                case ProductStatus::ARCHIVED:
                    $product->status = ProductStatus::ARCHIVED;
                    break;
            }

            $product->sync_status = Product::SYNC_DATA_STATS_ENABLED;
            $product->save();
            DB::commit();
            syncClearCache([
                'tags' => CacheKeys::getProductTemplateTags($productId)
            ], CacheKeys::CACHE_TYPE_ALTERNATIVE);
            return $this->successResponse(['product_id' => $productId]);
        } catch (Exception $e) {
            DB::rollback();
            return $this->errorResponse('Change product status failed!');
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function deleteTemplateProduct(Request $request): JsonResponse
    {
        currentUser()->hasPermissionOrAbort('update_product');
        $productId = $request->input('product_id');

        if (empty($productId)) {
            return $this->errorResponse('Delete product template failed.');
        }

        try {
            $product = Product::query()->find($productId);
            if ($product->status !== ProductStatus::INACTIVE) {
                return $this->errorResponse('Template must be inactive first.');
            }
            $templateIds = Product::query()
                ->where('template_id', $productId)
                ->get()
                ->pluck('id');

            $count = OrderProduct::query()
                ->whereIn('product_id', $templateIds)
                ->count();

            if ($count > 0) {
                return $this->errorResponse('Product template in use.');
            }

            DB::beginTransaction();
            Template::query()->where('template_id', $productId)->update([
                'sync_status' => Product::SYNC_DATA_STATS_ENABLED,
                'deleted_at' => now()
            ]);
            $product->sync_status = Product::SYNC_DATA_STATS_ENABLED;
            $product->delete();
            DB::commit();
            syncClearCache([
                'tags' => CacheKeys::getProductTemplateTags($productId),
            ], CacheKeys::CACHE_TYPE_ALTERNATIVE);
        } catch (Exception $e) {
            DB::rollback();
            return $this->errorResponse($e->getMessage());
        }

        return $this->successResponse('Delete product success');
    }

    public function getPrintSpaces(): JsonResponse
    {
        return $this->successResponse(PrintSpaceEnum::ARR);
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function clearCacheTemplate(Request $request): JsonResponse
    {
        try {
            if ($templateId = $request->input('template_id')) {
                syncClearCache(
                    ['tags' => CacheKeys::getProductTemplateTags($templateId)],
                    CacheKeys::CACHE_TYPE_ALTERNATIVE
                );
            }

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function attachFulfillProduct(Request $request): JsonResponse
    {
        try {
            $templateId = $request->template_id;
            $fulfillProductId = $request->fulfill_product_id;
            $supplierId = $request->supplier_id;

            ProductFulfillMapping::insert(
                [
                    'product_id' => $templateId,
                    'fulfill_product_id' => $fulfillProductId,
                    'supplier_id' => $supplierId,
                ]
            );
            syncClearCache(['tags' => [CacheKeys::getTemplateFulfillProduct($templateId)]], CacheKeys::CACHE_TYPE_ALTERNATIVE);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function detachFulfillProduct(Request $request): JsonResponse
    {
        try {
            $templateId = $request->template_id;
            $fulfillProductId = $request->fulfill_product_id;
            ProductFulfillMapping::where(
                [
                    'product_id' => $templateId,
                    'fulfill_product_id' => $fulfillProductId,
                ]
            )->delete();
            syncClearCache(['tags' => [CacheKeys::getTemplateFulfillProduct($templateId)]], CacheKeys::CACHE_TYPE_ALTERNATIVE);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function sortFulFillProduct(SortFulFillProductRequest $request)
    {
        try {
            ProductFulfillMapping::query()
                ->where('product_id', $request->template_id)
                ->delete();

            foreach ($request->items as $index => $item) {
                ProductFulfillMapping::query()->create($item);
            }

            $cacheKeys['tags'] = [CacheKeys::getTemplateFulfillProduct($request->template_id)];
            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function checkFulfillProducts(): JsonResponse
    {
        $templates = Product::query()
            ->where([
                'product_type' => ProductType::TEMPLATE,
                'status' => ProductStatus::ACTIVE
            ])
            ->get();

        $missedOptionProducts = [];
        $templates->map(function ($product) use (&$missedOptionProducts) {
            $fulfillProducts = Product::query()
                ->where([
                    'product_type' => ProductType::FULFILL_PRODUCT,
                    'template_id' => $product->id,
                    'status' => ProductStatus::ACTIVE
                ])
                ->with(['supplier:id,name'])
                ->get();

            if ($fulfillProducts->isNotEmpty()) {
                $fulfillProducts->map(function ($fulfillProduct) use ($product, &$missedOptionProducts) {
                    $options = (array)json_decode($product->options, true);
                    $fulfillOptions = (array)json_decode($fulfillProduct->options, true);
                    $missedOptions = '';
                    foreach ($options as $key => $values) {
                        $fulfillOptionValues = $fulfillOptions[$key];
                        foreach ($values as $value) {
                            if (!in_array($value, $fulfillOptionValues)) {
                                $missedOptions .= $key . ':' . $value . ',';
                            }
                        }
                    }

                    if (!empty($missedOptions)) {
                        $missedOptionProducts[] = [
                            $product->sku,
                            $fulfillProduct->id,
                            $fulfillProduct->name,
                            $fulfillProduct->supplier->name,
                            $missedOptions
                        ];
                    }
                });
            }
        });

        return $this->successResponse($missedOptionProducts);
    }

    public function sellerGetProductVariants(Request $request): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $productId = $request->get('product_id');
        $locationCode = $request->get('location_code');
        $product = Product::query()
            ->onSellerConnection($seller)
            ->where('id', $productId)
            ->firstOrFail();

        $selectFields = [
            'product_id',
            'base_cost',
            'price',
            'old_price',
            'sku',
            'out_of_stock',
            'variant_key',
            'location_code',
        ];

        $productVariants = ProductVariant::query()
            ->onSellerConnection($seller)
            ->where('product_id', $productId)
            ->where('location_code', $locationCode)
            ->get($selectFields);

        ProductVariant::findAndCacheByTemplate($product->template_id)
            ->each(
                static function ($each) use (
                    $locationCode,
                    &$productVariants,
                    $selectFields
                ) {
                    if ($each->location_code !== $locationCode) {
                        return;
                    }

                    $productVariants->push($each->only($selectFields));
                }
            );
        if ($product && $product->full_printed === ProductPrintType::HANDMADE) {
            $templateOptions = json_decode($product->options, false, 512, JSON_THROW_ON_ERROR);
            $product->custom_options = null;
            $product->common_options = null;
            if (!empty($templateOptions->custom_options)) {
                $product->custom_options = $templateOptions->custom_options;
                $product->template_custom_options = $templateOptions->custom_options;
                unset($templateOptions->custom_options);
            }
            if (!empty($templateOptions->common_options)) {
                $product->common_options = $templateOptions->common_options;
                unset($templateOptions->common_options);
            }
            $product->options = json_encode($templateOptions, JSON_THROW_ON_ERROR);
        }
        return $this->successResponse([
            'product' => $product,
            'variants' => $productVariants,
        ]);
    }

    public function sellerSaveProductVariants(SellerSaveProductVariantsRequest $request): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $productId = $request->input('product_id');
        // $locationCode = $request->input('location_code');
        $variants = $request->input('variants');

        DB::beginTransaction();
        try {
            // remove old variants
            ProductVariant::query()
                ->onSellerConnection($seller)
                ->where([
                    'product_id' => $productId
                    //    'location_code' => $locationCode
                ])
                ->delete();

            //save new variants
            $count = ProductVariant::query()
                ->onSellerConnection($seller)
                ->insert($variants);

            $cacheKeys = CacheKeys::getVariantsByTemplate($productId);
            syncClearCache([
                'tags' => [
                    CacheKeys::SYSTEM_PRODUCT_TEMPLATES,
                    CacheKeys::LIST_VARIANT_BY_TEMPLATE
                ],
                'key' => $cacheKeys
            ], CacheKeys::CACHE_TYPE_ALTERNATIVE);

            DB::commit();
            return $this->successResponse([
                'count_inserted' => $count
            ]);
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    public static function getTemplate(Request $request): array
    {
        $data = Template::getAndCache();
        $response = [];
        foreach ($data as $each) {
            if ($each->status !== ProductStatus::ACTIVE) {
                continue;
            }

            if (
                $request->has('ids')
                &&
                !in_array($each->id, $request->input('ids'))
            ) {
                continue;
            }

            if (
                $request->boolean('is_feed')
                &&
                empty($each->google_category_id)
            ) {
                continue;
            }

            if (
                !$request->boolean('has_fulfill')
                &&
                $each->system_type === ProductSystemTypeEnum::FULFILL
            ) {
                continue;
            }

            // get only fields need
            $arr = $each->toArray();
            foreach ($arr as $key => $row) {
                if (!in_array($key, Elastic::ARR_FIELD_LISTING_PRODUCT)) {
                    unset($arr[$key]);
                }
            }

            $response[] = $arr;
        }

        return $response;
    }

    public function getListStockStatus(Request $request): Paginator
    {
        try {
            $search = $request->get('q');
            $limit = (int)$request->query('per_page', 15);
            $currentPage = $request->get('page', 1);
            $inStock = $request->get('in_stock', 1);
            $productId = $request->get('product_id');
            $color = $request->get('color');
            $size = $request->get('size');
            $box = $request->get('box');
            $printSize = $request->get('print_size');
            $pack = $request->get('pack');
            $ringSize = $request->get('ring_size');

            // filter in Elastic
            $arrFilterElastic = [
                'in_stock' => $inStock
            ];

            // filter by search
            if (!empty($search)) {
                $search = cleanSpecialCharacters($search);
                $arrFilterElastic['search'] = $search;
            }
            if (!empty($productId)) {
                $arrFilterElastic['product_id'] = $productId;
            }
            if (!empty($color)) {
                $arrFilterElastic['color'] = ucwords($color);
            }
            if (!empty($size)) {
                $arrFilterElastic['size'] = ucwords($size);
            }
            if (!empty($box)) {
                $arrFilterElastic['box'] = $box;
            }
            if (!empty($pack)) {
                $arrFilterElastic['pack'] = $pack;
            }
            if (!empty($ringSize)) {
                $arrFilterElastic['ring_size'] = $ringSize;
            }
            if (!empty($printSize)) {
                $arrFilterElastic['print_size'] = $printSize;
            }
            [$products, $total] = (new Elastic())->getListStockStatus(
                $arrFilterElastic,
                $limit,
                $currentPage,
            );

            $colors = SystemColor::query()->pluck('name', 'hex_code');
            foreach ($products as &$product) {
                if (isset($product['color'])) {
                    $product['color_hex'] = $colors->search(strtolower($product['color'])) ?: '#FFFFFF';
                }
            }
            // paginate
            $uri = preg_replace("/page=\d+/", '', $request->getRequestUri());

            return new Paginator(
                $products,
                $total,
                $limit,
                $currentPage,
                ['path' => $uri]
            );
        } catch (Exception $exception) {
            logException($exception);
            return new Paginator(
                [],
                0,
                15,
                1
            );
        }
    }
}

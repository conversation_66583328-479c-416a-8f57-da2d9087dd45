<?php

namespace App\Http\Controllers;

use App\Enums\CurrencyEnum;
use App\Enums\DateRangeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\PaymentAccountStatus;
use App\Enums\PaymentAccountTypeEnum;
use App\Enums\PayoutStatusEnum;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Enums\UserStatusEnum;
use App\Events\PayoutCreated;
use App\Exports\ExportProcessingPayout;
use App\Exports\SellerPayoutsExport;
use App\Http\Requests\AdminBulkUpdatePayoutStatusRequest;
use App\Http\Requests\Seller\Payout\StoreRequest;
use App\Models\Currency;
use App\Models\IndexOrder;
use App\Models\PaymentAccount;
use App\Models\SellerBilling;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\UserService;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;
use Modules\SellerAccount\Models\UserBalance;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Throwable;

class PayoutController extends Controller
{
    use ApiResponse;

    private const PAYOUT_FEE_DOLLAR = 5;

    public function index(Request $request): JsonResponse
    {
        $perPage = $request->get('per_page', 15);
        $searchQuery = $request->get('q');
        $dateRange = $request->get('date_range', DateRangeEnum::LAST_30_DAYS);
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $status = $request->get('status');
        $type = $request->get('type');
        $balanceType = $request->get('balance_type', SellerBalanceTypeEnum::DEFAULT);
        $user = currentUser();
        $userId = $user->getUserId();
        $user = User::query()->find($userId);

        $query = SellerBilling::query()
            ->where('seller_id', $userId)
            ->when($searchQuery, function ($q) use ($searchQuery) {
                if (is_numeric($searchQuery)) {
                    $q->where('order_id', (int)$searchQuery);
                } else {
                    $q->where('detail', 'like', '%' . $searchQuery . '%');
                }
            })
            ->when($status, function ($q, $status) {
                $q->where('status', $status);
            })
            ->when($type, function ($q, $type) {
                $q->where('type', $type);
            })
            ->when($balanceType, function ($q, $balanceType) {
                $q->where('balance_type', $balanceType);
            });

        $transactionHistory = $query
            ->filterDateRange($dateRange, $startDate, $endDate)
            ->latest('id')
            ->paginate($perPage);

        $paymentAccounts = PaymentAccount::query()
            ->whereIn('seller_id', [$userId, 0])
            ->whereIn('status', [
                PaymentAccountStatus::VERIFIED,
                PaymentAccountStatus::USED
            ])
            ->where('account_id', '!=', SellerBillingType::ADS_TOPUP)
            ->get();

        $holdProfit = UserService::getHoldProfit($user);
        $holdToDate = $user->hold_to && $user->hold_to->gt(now()) ? $user->hold_to->format('d/m/Y') : null;

        $availableBalance = self::getAvailableBalance($user, $holdProfit);

        $sellerBalance = UserBalance::query()
            ->where('seller_id', $userId)
            ->get();


        return $this->successResponse([
            'transactionHistory' => $transactionHistory,
            'paymentAccounts' => $paymentAccounts,
            'currentBalance' => $user->getCurrentBalance(),
            'availableBalance' => $availableBalance,
            'holdProfit' => $holdProfit,
            'holdToDate' => $holdToDate,
            'maxValue' => $this->getMaxValue($availableBalance),
            'minValue' => (float) SystemConfig::getConfig('payout_minimum', 50),
            'withdrawFee' => (float) SystemConfig::getConfig('bank_withdraw_fee', 5),
            'sellerBalance' => $sellerBalance
        ]);
    }

    public function store(StoreRequest $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $seller = User::query()->find($userId);

        if (!$seller) {
            return $this->errorResponse('Seller not found');
        }

        $accountStatus = $user->getInfo()->status;

        if (UserStatusEnum::checkLimitedStatus($accountStatus)) {
            return $this->errorResponse('Account status: ' . $accountStatus . '. Please contact support.');
        }

        $amount = (float)$request->post('amount');

        $paymentAccountId = $request->post('payment_account_id');
        $paymentAccount = PaymentAccount::query()->firstWhere('id', $paymentAccountId);

        if (!$paymentAccount) {
            return $this->errorResponse();
        }

        $isSenPrintsUserAccount = $paymentAccount->payment_type === PaymentAccountTypeEnum::SENPRINTS && $paymentAccount->account_id !== SellerBillingType::ADS_TOPUP;
        $isAdsTopUp = $paymentAccount->payment_type === PaymentAccountTypeEnum::SENPRINTS && $paymentAccount->account_id === SellerBillingType::ADS_TOPUP;
        if ($isAdsTopUp) {
            return $this->errorResponse('We do not support withdraw to Ads Topup account anymore.');
        }
        if ($isSenPrintsUserAccount && $accountStatus !== UserStatusEnum::TRUSTED) {
            return $this->errorResponse('Only trusted sellers can withdraw to SenPrints account.');
        }

        $paymentName = $paymentAccount->payment_name;

        if ($paymentAccount->payment_type === PaymentAccountTypeEnum::BANK) {
            $additionalInfo = $paymentAccount->additional_info;
            $withdrawFee = (float)SystemConfig::getConfig('bank_withdraw_fee', 5);
            $amount += $withdrawFee;

            if (!empty($additionalInfo)) {
                $jsonData = json_decode($additionalInfo, true, 512, JSON_THROW_ON_ERROR);
                $bankName = $jsonData['bank_name'];
                $message = "Withdraw to {$paymentName} / {$paymentAccount->account_id} / {$bankName} / Withdrawal fee: {$withdrawFee}$";
            } else {
                $message = "Withdraw to {$paymentName} / {$paymentAccount->account_id} / Withdrawal fee: {$withdrawFee}$";
            }
        } else if ($paymentAccount->payment_type === PaymentAccountTypeEnum::SENPRINTS) {
            $message = "Send money to {$paymentAccount->account_id}";
        } else if ($paymentAccount->payment_type === PaymentAccountTypeEnum::PAYPAL) {
            $paypal_withdraw_settings = SystemConfig::getCustomConfig('paypal_withdraw_settings');
            $paypal_withdraw_settings = optional($paypal_withdraw_settings)->json_data;
            $withdrawPercentFee = 2;
            $withdrawFee = 60;
            if (!empty($paypal_withdraw_settings)) {
                $paypal_withdraw_settings = json_decode($paypal_withdraw_settings, true);
                $withdrawPercentFee = $paypal_withdraw_settings['percent_charge'] ?? 2;
                $withdrawFee = $paypal_withdraw_settings['max_fee'] ?? 60;
            }
            $actualWithdrawFee = calcPercentOfAmount($withdrawPercentFee, $amount);
            if ($actualWithdrawFee <= $withdrawFee) {
                $withdrawFee = $actualWithdrawFee;
            }
            $message = "Withdraw to {$paymentName} / {$paymentAccount->account_id} / Withdrawal fee: {$withdrawFee}$";
        } else {
            $message = "Withdraw to {$paymentName} / {$paymentAccount->account_id}";
        }

        $payoutStatus = SellerBillingStatus::PENDING;
        $transactionType = SellerBillingType::PAYOUT;

        if ($seller->status === UserStatusEnum::FLAGGED) {
            $payoutStatus = SellerBillingStatus::ON_HOLD;
        }

        if ($paymentAccount->payment_type === PaymentAccountTypeEnum::SENPRINTS) {
            $payoutStatus = SellerBillingStatus::COMPLETED;
            $transactionType = SellerBillingType::OTHER;
            if ($paymentAccount->account_id === SellerBillingType::ADS_TOPUP) {
                $payoutStatus = SellerBillingStatus::PENDING;
                $transactionType = SellerBillingType::ADS_TOPUP;
                $message = 'Ads Topup';
            }
        }

        // check if user has enough money
        $balance = self::getAvailableBalance($seller);

        if ($amount > $balance) {
            return $this->errorResponse('Balance is not enough');
        }

        // update user balance
        try {
            DB::beginTransaction();

            $payout = $seller->updateBalance(
                (0 - $amount), // convert to negative number (https://stackoverflow.com/a/1438143)
                $transactionType,
                $message,
                null,
                $payoutStatus,
                $paymentAccountId
            );

            if ($paymentAccount->payment_type === PaymentAccountTypeEnum::SENPRINTS && $paymentAccount->account_id !== SellerBillingType::ADS_TOPUP) {
                $receiverAccount = User::query()
                    ->where('email', $paymentAccount->account_id)
                    ->first();

                if ($receiverAccount) {
                    $receiverMessage = 'Receive money from ' . $seller->email;
                    $receiverAccount->updateBalance(
                        $amount,
                        SellerBillingType::OTHER,
                        $receiverMessage,
                        null,
                        SellerBillingStatus::COMPLETED,
                        $paymentAccountId
                    );
                }
            }

            DB::commit();

            if ($payout) {
                PayoutCreated::dispatch($seller, $payout);
                return $this->successResponse();
            }

            return $this->errorResponse();
        } catch (Exception $e) {
            DB::rollBack();
            return $this->errorResponse();
        }
    }

    /**
     * Calculate the available balance
     */
    public static function getAvailableBalance(User $user, float $holdProfit = null): float
    {
        $balance = $user->balance;

        if (!$balance) {
            return 0;
        }

        if ($holdProfit === null) {
            $holdProfit = UserService::getHoldProfit($user);
        }

        $availableBalance = $balance - $holdProfit - $user->hold_amount;

        return $availableBalance > 0 ? $availableBalance : 0;
    }

    /**
     * Seller cannot request to withdraw more than $10.000
     *
     * @param int|float $availableBalance
     * @return int|float
     */
    protected function getMaxValue($availableBalance)
    {
        $max = (float) SystemConfig::getConfig('payout_maximum', 10000);

        if ($availableBalance < $max) {
            $max = $availableBalance;
        }

        return $max;
    }

    public function adminIndex(Request $request): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $perPage = $request->get('per_page', 50);
        $email = $request->get('email');
        $status = $request->get('status');
        $fromDate = $request->get('from_date');
        $toDate = $request->get('to_date');
        $paymentType = $request->input('payment_type');
        $query = SellerBilling::query();

        if ($status) {
            $query->where('seller_billing.status', $status);
        }

        if ($fromDate && $toDate) {
            $createdAt = DB::raw('DATE(seller_billing.created_at)');
            $query->whereBetween($createdAt, [
                $fromDate,
                $toDate
            ]);
        }

        if ($email) {
            $query->where('email', 'like', '%' . $email . '%');
        }

        if ($paymentType) {
            $query->whereHas('payment_account', function ($query) use ($paymentType) {
                $query->where('payment_type', $paymentType);
            });
        }

        $query
            ->select([
                'seller_billing.seller_id',
                'seller_billing.id',
                'seller_billing.amount',
                'seller_billing.payment_account_id',
                'seller_billing.detail',
                'seller_billing.balance',
                'seller_billing.type',
                'seller_billing.status as payout_status',
                'seller_billing.created_at',
                'seller_billing.comment',
                'user.email',
                'user.name',
                'user.status as seller_status',
                'seller_billing.transaction_key as transaction_key',
            ])
            ->whereIn('type', [SellerBillingType::PAYOUT, SellerBillingType::ADS_TOPUP])
            ->with('payment_account')
            ->join('user', 'user.id', '=', 'seller_billing.seller_id')
            ->orderBy('seller_billing.created_at', 'desc');

        return $query->paginate($perPage);
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function adminDetail($id): JsonResponse
    {
        $result = SellerBilling::query()
            ->with(['payment_account', 'seller'])
            ->find($id);

        if (empty($result) || !in_array($result->type, [
                SellerBillingType::PAYOUT,
                SellerBillingType::ADS_TOPUP,
            ], true)) {
            return $this->errorResponse('Forbidden');
        }
        if ($result->payment_account->payment_type === 'bank') {
            $amount = abs($result->amount);
            $amount_vnd = 0;

            if ($amount > self::PAYOUT_FEE_DOLLAR) {
                $currency = Currency::query()->firstWhere([
                    'code' => CurrencyEnum::VND
                ]);

                if (!$currency) {
                    return $this->errorResponse('Currency not found');
                }
                $conversion_fee = SystemConfig::getConfig('conversion_fee_percent', 1);
                $amount_with_fee = $amount - self::PAYOUT_FEE_DOLLAR;
                $amount_vnd_with_conversion_fee = convertCurrency($amount_with_fee, $currency->rate) * (1 - $conversion_fee / 100);
                $amount_vnd = round($amount_vnd_with_conversion_fee, -3);
            }

            $result->payment_account['amount_vnd'] = $amount_vnd;
        }
        return $this->successResponse($result);
    }

    public function adminUpdate(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => ['required', 'string'],
            'comment' => ['nullable', 'string'],
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $status = $request->json('status');
        $comment = $request->json('comment');

        if (!PayoutStatusEnum::hasValue($status)) {
            return $this->errorResponse();
        }

        $updated = SellerBilling::query()
            ->select('status')
            ->firstWhere('id', $id);

        if (is_null($updated)) {
            return $this->errorResponse('Cannot find current payout status');
        }

        $currentStatus = $updated->status;
        $allowedStatus = [
            PayoutStatusEnum::PENDING => [
                PayoutStatusEnum::PROCESSING,
                PayoutStatusEnum::ON_HOLD,
                PayoutStatusEnum::CANCELLED
            ],
            PayoutStatusEnum::PROCESSING => [
                PayoutStatusEnum::COMPLETED,
                PayoutStatusEnum::ON_HOLD,
                PayoutStatusEnum::CANCELLED
            ],
            PayoutStatusEnum::ON_HOLD => [
                PayoutStatusEnum::PROCESSING,
                PayoutStatusEnum::CANCELLED
            ],
            PayoutStatusEnum::CANCELLED => [],
            PayoutStatusEnum::COMPLETED => [],
        ];

        // If status allowed changing
        if (!in_array($status, $allowedStatus[$currentStatus], true)) {
            return $this->errorResponse('Status not allowed');
        }

        try {
            $query = SellerBilling::query()
                ->where('id', $id);
            $transaction = $query->first();

            if (is_null($transaction)) {
                return $this->errorResponse();
            }

            if ($status === PayoutStatusEnum::CANCELLED) {
                $seller = User::query()->firstWhere('id', $transaction->seller_id);

                if (is_null($seller)) {
                    return $this->errorResponse('Cannot find seller');
                }

                $seller->updateBalance(
                    (0 - (float)$transaction->amount),
                    $transaction->type,
                    'Cancel ' . $transaction->type . ' on ' . $transaction->created_at->toFormattedDateString(),
                    null,
                    SellerBillingStatus::COMPLETED,
                    $transaction->payment_account_id
                );
            } else if ($status === PayoutStatusEnum::PROCESSING) {
                $seller = User::query()->firstWhere('id', $transaction->seller_id);

                if (is_null($seller)) {
                    return $this->errorResponse('Cannot find seller');
                }

                if (in_array($seller->status, UserStatusEnum::getHoldPayoutStatus())) {
                    return $this->errorResponse('Cannot update payout due to user status');
                }

                if ($seller->balance < 0) {
                    $transaction->status = PayoutStatusEnum::CANCELLED;
                    $transaction->save();
                    $seller->updateBalance(
                        (0 - (float)$transaction->amount),
                        $transaction->type,
                        'Cancel ' . $transaction->type . ' on ' . $transaction->created_at->toFormattedDateString(),
                        null,
                        SellerBillingStatus::COMPLETED,
                        $transaction->payment_account_id
                    );
                    return $this->errorResponse('Seller balance is negative');
                }
            }
            $query->update([
                'status' => $status,
                'comment' => $comment
            ]);

            return $this->successResponse();
        } catch (Exception $ex) {
            return $this->errorResponse('Cannot update status of this payout');
        }
    }

    /**
     * @param AdminBulkUpdatePayoutStatusRequest $request
     * @return JsonResponse
     */
    public function adminBulkUpdateStatus(AdminBulkUpdatePayoutStatusRequest $request): JsonResponse
    {
        $status = $request->input('status');
        $ids = $request->input('ids');
        $comment = $request->input('comment');
        $allowedStatus = [
            PayoutStatusEnum::PENDING => [
                PayoutStatusEnum::PROCESSING,
                PayoutStatusEnum::ON_HOLD,
                PayoutStatusEnum::CANCELLED
            ],
            PayoutStatusEnum::PROCESSING => [
                PayoutStatusEnum::COMPLETED,
                PayoutStatusEnum::ON_HOLD,
                PayoutStatusEnum::CANCELLED
            ],
            PayoutStatusEnum::ON_HOLD => [
                PayoutStatusEnum::PROCESSING,
                PayoutStatusEnum::CANCELLED
            ],
            PayoutStatusEnum::CANCELLED => [],
            PayoutStatusEnum::COMPLETED => [],
        ];

        $query = SellerBilling::query()
            ->select(['seller_billing.*'])
            ->whereIn('seller_billing.type', [SellerBillingType::PAYOUT, SellerBillingType::ADS_TOPUP])
            ->whereIn('seller_billing.id', $ids);

        if (in_array($status, [
            PayoutStatusEnum::PROCESSING,
            PayoutStatusEnum::COMPLETED,
        ])) {
            $query->join('user', 'user.id', '=', 'seller_billing.seller_id')
                ->whereNotIn('user.status', UserStatusEnum::getHoldPayoutStatus());
        }


        $isLocal = app()->environment('local', 'development');

        if ($isLocal) {
            if (
                POMassPayOutIsEnabled()
                && POMassPayOutQuickTestingEnabled()
            ) {
                $query->with('payment_account');
            }
        }

        $payouts = $query->get();

        if (count($ids) !== $payouts->count()) {
            return $this->errorResponse('Cannot change payouts status');
        }
        $invalidIds = [];
        $messages = [];
        $payoutIds = [];
        try {
            foreach ($payouts as $payout) {
                if (!in_array($status, $allowedStatus[$payout->status])) {
                    $invalidIds[] = $payout->id;
                    $messages[] = "Payout #{$payout->id} cannot change status to {$status}";
                    continue;
                }
                $seller = User::query()->firstWhere('id', $payout->seller_id);

                if ($seller === null) {
                    $invalidIds[] = $payout->id;
                    $messages[] = "Payout #{$payout->id} is invalid";
                    continue;
                }

                if ($status === PayoutStatusEnum::CANCELLED) {
                    $seller = User::query()->firstWhere('id', $payout->seller_id);
                    $seller->updateBalance(
                        (0 - (float)$payout->amount),
                        $payout->type,
                        'Cancel ' . $payout->type . ' on ' . Carbon::now()->toFormattedDateString(),
                        null,
                        SellerBillingStatus::COMPLETED,
                        $payout->payment_account_id
                    );
                    $payout->status = $status;
                    $payout->comment = $comment;
                    $payout->save();
                    continue;
                }

                if ($status === PayoutStatusEnum::PROCESSING && $seller->balance < 0) {
                    $invalidIds[] = $payout->id;
                    $messages[] = "Payout #{$payout->id} invalid: Seller balance is negative";
                    $payout->status = PayoutStatusEnum::CANCELLED;
                    $payout->comment = "Payout #{$payout->id} invalid: Seller balance is negative";
                    $payout->save();
                    $seller->updateBalance(
                        (0 - (float)$payout->amount),
                        $payout->type,
                        'Cancel ' . $payout->type . '  on ' . Carbon::now()->toFormattedDateString(),
                        null,
                        SellerBillingStatus::COMPLETED,
                        $payout->payment_account_id
                    );
                }

                if ($isLocal) {
                    if (
                        POMassPayOutIsEnabled()
                        && POMassPayOutQuickTestingEnabled()
                        && $status === PayoutStatusEnum::PROCESSING
                        && isset($payout->payment_account)
                        && $payout->payment_account->status === PaymentAccountStatus::VERIFIED
                        && !is_null($payout->payment_account->additional_info)
                    ) {
                        $additional_info = json_decode($payout->payment_account->additional_info, true);
                        if (!is_null($additional_info) && isset($additional_info['payoneer_id'])) {
                            $payoutIds[] = $payout->id;
                        }
                    }
                }
            }

            if ($isLocal) {
                if (
                    POMassPayOutIsEnabled()
                    && POMassPayOutQuickTestingEnabled()
                    && !POMassPayOutQuickTestingFiveMinutesEnabled()
                    && !empty($payoutIds)
                ) {
                    Artisan::call('po-payout:process');
                }
            }

            if (!empty($invalidIds)) {
                return $this->errorResponse($messages);
            }

            $query->update([
                'seller_billing.status' => $status,
                'comment' => $comment,
            ]);
            return $this->successResponse();
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function adminBulkChangePendingToProcessing(Request $request): JsonResponse
    {
        $comment = $request->input('comment');

        $query = SellerBilling::query()
            ->select(['seller_billing.*'])
            ->where('seller_billing.status', PayoutStatusEnum::PENDING)
            ->whereIn('seller_billing.type', [SellerBillingType::PAYOUT, SellerBillingType::ADS_TOPUP])
            ->join('user', 'user.id', '=', 'seller_billing.seller_id')
            ->whereNotIn('user.status', UserStatusEnum::getHoldPayoutStatus());

        $payouts = $query->get();
        if ($payouts->isEmpty()) {
            return $this->errorResponse('All valid payouts are already processing');
        }

        $messages = [];
        foreach ($payouts as $payout) {
            $seller = User::query()->firstWhere('id', $payout->seller_id);

            if ($seller === null) {
                $messages[] = "Payout #{$payout->id} is invalid";
                continue;
            }

            if ($seller->balance < 0) {
                $messages[] = "Payout #{$payout->id} invalid: Seller balance is negative";
                $payout->status = PayoutStatusEnum::CANCELLED;
                $payout->comment = "Payout #{$payout->id} invalid: Seller balance is negative";
                $payout->save();
                $seller->updateBalance(
                    (0 - (float)$payout->amount),
                    $payout->type,
                    'Cancel ' . $payout->type . '  on ' . Carbon::now()->toFormattedDateString(),
                    null,
                    SellerBillingStatus::COMPLETED,
                    $payout->payment_account_id
                );
            }
        }

        if (!empty($messages)) {
            return $this->errorResponse($messages);
        }

        $query->update([
            'seller_billing.status' => PayoutStatusEnum::PROCESSING,
            'comment' => $comment,
        ]);

        return $this->successResponse('Success update ' . $payouts->count() . ' payouts');
    }

    public function adminTransactions(Request $request, int $sellerId): JsonResponse
    {
        $page = $request->get('page', 1);
        $limitPerPage = 15;
        $offset = ($page - 1) * $limitPerPage;

        $transactions = SellerBilling::query()
            ->where('seller_id', $sellerId)
            ->where('balance_type', SellerBalanceTypeEnum::DEFAULT)
            ->orderBy('id', 'desc')
            ->limit($limitPerPage + 1) // get one more extra to check balance, remove later
            ->offset($offset)
            ->get();

        if ($transactions->isNotEmpty()) {
            foreach ($transactions as $key => $transaction) {
                $invalidCode = 0; // default

                // check signature
                $stringHash = $transaction['seller_id'] . $transaction['balance'] . $transaction['amount'] . $transaction['type'];

                if (!Hash::check($stringHash, $transaction->signature)) {
                    $invalidCode = 1;
                } else {
                    // check balance
                    $newBalance = $transaction->balance;
                    $newAmount = $transaction->amount;
                    // default 0 if there is no older transaction => first transaction
                    $oldBalance = $transactions[$key + 1]->balance ?? 0;

                    // You shouldn't compare floating point numbers using the == operator.
                    // https://stackoverflow.com/a/5271114/9029340
                    if (abs($newBalance - ($oldBalance + $newAmount)) >= 0.0001) {
                        $invalidCode = 2;
                    }
                }

                $transaction->invalid_code = $invalidCode;

                if (
                    $transaction->type === SellerBillingType::PAYOUT
                    && $transaction->status === SellerBillingStatus::COMPLETED
                ) {
                    $transaction->invalid_code = -1;
                    break;
                }
            }
        }

        $total = $transactions->count();

        if ($total === $limitPerPage + 1) { //remove extra if over limit
            $transactions->pop(); // remove last one
        }

        $totalAll = SellerBilling::query()
            ->where('seller_id', $sellerId)
            ->count();

        $transactions = new LengthAwarePaginator(
            $transactions,
            $totalAll,
            $limitPerPage,
            $page
        );

        return $this->successResponse($transactions);
    }

    public static function updateSignatureAllTransactions(): void
    {
        $transactions = SellerBilling::query()
            ->get();

        if ($transactions->isNotEmpty()) {
            foreach ($transactions as $transaction) {
                $transaction->signature = Hash::make($transaction['seller_id'] . $transaction['balance'] . $transaction['amount'] . $transaction['type']);
                $transaction->save();
            }
        }
    }

    public function updateSignature(Request $request): void
    {
        $id = $request->get('id');
        $transaction = SellerBilling::find($id);
        $transaction->signature = Hash::make($transaction['seller_id'] . $transaction['balance'] . $transaction['amount'] . $transaction['type']);
        $transaction->save();
    }

    /**
     * @return JsonResponse|BinaryFileResponse
     */
    public function adminExportProcessingPayouts()
    {
        $processingPayouts = SellerBilling::query()
            ->join('payment_accounts', 'payment_accounts.id', '=', 'seller_billing.payment_account_id', 'left')
            ->select([
                'payment_accounts.account_id as email_po',
                'seller_billing.amount as amount',
                'seller_billing.id as payment_id',
                'seller_billing.detail as description',
                'seller_billing.created_at as created_at'
            ])
            ->where([
                'seller_billing.type' => SellerBillingType::PAYOUT,
                'seller_billing.status' => SellerBillingStatus::PROCESSING,
                'payment_accounts.payment_type' => PaymentAccountTypeEnum::PAYONEER
            ])
            ->get();

        if ($processingPayouts->isEmpty()) {
            return $this->errorResponse('No processing payouts', 403);
        }

        $sheet = new ExportProcessingPayout($processingPayouts->toArray());
        $fileName = 'Processing payouts ' . Carbon::now()->toFormattedDateString() . '.csv';

        return Excel::download($sheet, $fileName);
    }

    public function adminListAllTransactions(Request $request): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $perPage = $request->get('per_page', 15);
        $status = $request->get('status');
        $dateRange = $request->get('date_range', DateRangeEnum::LAST_30_DAYS);
        $type = $request->get('type');
        $keywords = $request->get('q');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $balanceType = $request->get('balance_type');
        $manualTransaction = $request->boolean('manual_transactions', false);

        $query = SellerBilling::query();

        if (!empty($keywords)) {
            $query->where(function ($query) use ($keywords) {
                $query->orWhere('seller_billing.order_id', 'like', '%' . $keywords . '%');
                $query->orWhere('seller_billing.detail', 'like', '%' . $keywords . '%');
                $query->orWhere('user.email', 'like', '%' . $keywords . '%');
            });
        }

        if (!empty($type)) {
            $query->where('seller_billing.type', $type);
        }

        if (!empty($balanceType)) {
            $query->where('seller_billing.balance_type', $balanceType);
        }

        if (!empty($status)) {
            $query->where('seller_billing.status', $status);
        }

        if ($manualTransaction) {
            $query->whereNotNull('seller_billing.staff_id');
        }

        $query
            ->select([
                'seller_billing.seller_id',
                'seller_billing.id',
                'seller_billing.amount',
                'seller_billing.payment_account_id',
                'seller_billing.detail',
                'seller_billing.balance',
                'seller_billing.type',
                'seller_billing.balance_type',
                'seller_billing.status',
                'seller_billing.created_at',
                'seller_billing.log',
                'user.email',
                'user.name'
            ])
            ->with('payment_account')
            ->join('user', 'user.id', '=', 'seller_billing.seller_id')
            ->filterDateRange($dateRange, $startDate, $endDate)
            ->latest('id')
            ->orderByDesc('seller_billing.created_at');

        return $query->paginate($perPage);
    }

    public function sellerExportPayouts(Request $request)
    {
        $q = $request->get('q');
        $dateRange = $request->get('date_range', DateRangeEnum::LAST_30_DAYS);
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $status = $request->get('status');
        $type = $request->get('type');

        $currentUserId = currentUser()->getUserId();

        $query = SellerBilling::query()
            ->select([
                'id',
                'created_at',
                'amount',
                'balance',
                'type',
                'detail',
                'status',
            ])
            ->where('seller_id', $currentUserId);

        if (!empty($q)) {
            $query->where('detail', 'like', '%' . $q . '%');
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        if (!empty($type)) {
            $query->where('type', $type);
        }

        $transactionHistory = $query
            ->filterDateRange($dateRange, $startDate, $endDate)
            ->latest('id')
            ->get();

        if ($transactionHistory->isEmpty()) {
            return $this->errorResponse('No payout found', 404);
        }

        $data = $transactionHistory->toArray();
        $data = array_map(function ($payout) {
            $payout['amount'] = number_format($payout['amount'], 2);
            $payout['balance'] = number_format($payout['balance'], 2);
            $payout['status'] = ucfirst($payout['status']);
            $payout['type'] = ucfirst($payout['type']);
            $payout['created_at'] = Carbon::parse($payout['created_at'])->format('Y-m-d H:i:s');
            return $payout;
        }, $data);

        $headings = [
            'ID',
            'Date',
            'Amount',
            'Balance',
            'Type',
            'Detail',
            'Status'
        ];

        $export = new SellerPayoutsExport($data, $headings);
        $fileName = 'Payouts_export_' . date('Y-m-d H:i:s') . '.csv';
        return Excel::download($export, $fileName);
    }

    public function getTotalAmount(Request $request): JsonResponse
    {
        try {
            $status = $request->get('status');
            $dateRange = $request->get('date_range', DateRangeEnum::LAST_30_DAYS);
            $type = $request->get('type');
            $balanceType = $request->get('balance_type');
            $keywords = $request->get('q');
            $startDate = $request->get('start_date');
            $endDate = $request->get('end_date');

            $query = SellerBilling::query();

            if (!empty($keywords)) {
                $query->where('seller_billing.detail', 'like', '%' . $keywords . '%')
                    ->orWhere('user.email', 'like', '%' . $keywords . '%');
            }

            if (!empty($type)) {
                $query->where('seller_billing.type', $type);
            }

            if (!empty($balanceType)) {
                $query->where('seller_billing.balance_type', $balanceType);
            }

            if (!empty($status)) {
                $query->where('seller_billing.status', $status);
            }

            $obj = $query
                ->selectRaw('sum(if(seller_billing.amount >= 0, seller_billing.amount, 0)) as total_incre')
                ->selectRaw('sum(if(seller_billing.amount < 0, seller_billing.amount, 0)) as total_decre')
                ->join('user', 'user.id', '=', 'seller_billing.seller_id')
                ->filterDateRange($dateRange, $startDate, $endDate)
                ->first();

            return $this->successResponse($obj);
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Action cancel payout by seller
     */
    public function sellerCancelPayout(Request $request): JsonResponse
    {
        $sellerId = currentUser()->getUserId();
        $payoutId = $request->input('payout_id');

        $payout = SellerBilling::query()
            ->whereIn('type', [SellerBillingType::PAYOUT, SellerBillingType::ADS_TOPUP])
            ->where([
                'seller_id' => $sellerId,
                'id' => $payoutId
            ])
            ->whereNotIn('status', [
                SellerBillingStatus::COMPLETED,
                SellerBillingStatus::PROCESSING,
                SellerBillingStatus::CANCELLED
            ])
            ->first();

        if (empty($payout)) {
            return $this->errorResponse('Request invalid');
        }

        $update = $payout->update([
            'status' => SellerBillingStatus::CANCELLED,
            'comment' => 'Cancelled by seller'
        ]);

        if ($update) {
            $seller = currentUser()->getInfo();

            if ($seller) {
                $seller->updateBalance(
                    (0 - (float)$payout->amount),
                    $payout->type,
                    'Cancel ' . $payout->type . ' on ' . $payout->created_at->toFormattedDateString(),
                    null,
                    SellerBillingStatus::COMPLETED,
                    $payout->payment_account_id
                );

                return $this->successResponse(null, 'Payout cancelled');
            }
        }

        return $this->errorResponse('Cancel payout failed');
    }

    public function renderChart(Request $request): JsonResponse
    {

        $user = currentUser();
        $userId = $user->getUserId();
        $hourOffset = getHourOffsetBySeller($userId);
        $offsetFormatted = sprintf('%+03d:00', $hourOffset);
        $rawData = SellerBilling::query()
            ->select([
                DB::raw("DATE(CONVERT_TZ(created_at, '+00:00', '{$offsetFormatted}')) as date"),
                DB::raw("SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as amount_in"),
                DB::raw("SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END) as amount_out")
            ])
            ->where([
                'seller_id' => $userId,
                'status' => SellerBillingStatus::COMPLETED
            ])
            ->filterDateRange(DateRangeEnum::LAST_30_DAYS)
            ->groupBy(DB::raw("DATE(CONVERT_TZ(created_at, '+00:00', '{$offsetFormatted}'))"))
            ->get()
            ->keyBy('date');
        $rawDataOrder = IndexOrder::query()
            ->select([
                DB::raw("DATE(CONVERT_TZ(paid_at, '+00:00', '{$offsetFormatted}')) as date"),
                DB::raw("SUM(processing_fee + total_fulfill_fee) AS total_fee"),
            ])
            ->where('seller_id', $userId)
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->whereNotIn('status', [
                OrderStatus::CANCELLED,
                OrderStatus::REFUNDED,
            ])
            ->filterDateRange(DateRangeEnum::LAST_30_DAYS)
            ->groupBy(DB::raw("DATE(CONVERT_TZ(paid_at, '+00:00', '{$offsetFormatted}'))"))
            ->get()
            ->keyBy('date');
        $data = [];
        $today = now()->addRealHours($hourOffset);
        for($i = 0; $i < 30; $i++) {
            $date = $today->clone()->subDays(29 - $i);
            $key = $date->format('Y-m-d');
            $data['dates'][] = $date->format('d/m');
            $data['amount_in'][] = $rawData->get($key)?->amount_in ?? 0;
            $data['amount_out'][] = $rawData->get($key)?->amount_out ?? 0;
            $data['total_fee'][] = $rawDataOrder->get($key)?->total_fee ?? 0;
        }

        return $this->successResponse($data);
    }
}

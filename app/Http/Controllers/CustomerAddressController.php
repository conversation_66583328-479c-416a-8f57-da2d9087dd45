<?php

namespace App\Http\Controllers;

use App\Enums\OrderHistoryActionEnum;
use App\Http\Requests\Customer\UpdateCustomerAddressRequest;
use App\Models\CustomerAddress;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerAddressController extends Controller
{
    use ApiResponse;

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function detail(Request $request): JsonResponse
    {
        $address = CustomerAddress::query()
            ->firstWhere('id', $request->get('address_id'));

        return $address
            ? $this->successResponse($address)
            : $this->errorResponse();
    }

    /**
     * @param UpdateCustomerAddressRequest $request
     * @return JsonResponse
     */
    public function update(UpdateCustomerAddressRequest $request): JsonResponse
    {
        $addressId = $request->input('address_id');
        $orderId = $request->input('order_id');

        $address = [
            'name' => $request->input('name'),
            'phone' => $request->input('phone'),
            'address' => $request->input('address'),
            'city' => $request->input('city'),
            'state' => $request->input('state'),
            'postcode' => $request->input('postcode'),
            'country' => $request->input('country'),
        ];

        try {
            $object = CustomerAddress::query()->find($addressId);
            $object->fill($address);
            if($object->isDirty()){
                $object->save();
                $order = Order::query()
                    ->selectForHistory()
                    ->find($orderId);

                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::EDIT_ADDRESS,
                    $object->getChangesDetail()
                );
            }
            return $this->successResponse([
                'address_id' => $addressId
            ], 'Customer address updated!');
        } catch (\Exception $e) {
            return $this->errorResponse('Update address failed');
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Http\Requests\SaveBlogRequest;
use App\Http\Requests\UpdateBlogStatusRequest;
use App\Models\Blog;
use App\Traits\ApiResponse;
use App\Services\BlogService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of blogs
     */
    public function index(Request $request): JsonResponse
    {
        $blogs = Blog::query()
            ->with(['seller:id,name', 'category:id,name'])
            ->filterSellerOwner()
            ->filterQuery($request->all())
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 25));

        return $this->successResponse($blogs, 'Blogs retrieved successfully');
    }

    public function publicIndex(Request $request): JsonResponse
    {
        $blogs = Blog::query()
            ->with(['seller:id,name', 'category:id,name'])
            // ->where('status', true)
            ->filterStoreOwner()
            ->filterQuery($request->all())
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 14));

        $blogService = new BlogService();
        $blogService->setCollectionsDataForBlogs($blogs);

        return $this->successResponse($blogs, 'Blogs retrieved successfully');
    }

    /**
     * Store a newly created blog or update existing one
     */
    public function save(SaveBlogRequest $request): JsonResponse
    {
        try {
            $currentUser = currentUser();
            $data = $request->validated();
            $data['seller_id'] = $currentUser->getUserId();

            // Check if updating existing blog
            $blog = null;
            if (isset($data['id']) && $data['id']) {
                $blog = Blog::query()
                    ->where('id', $data['id'])
                    ->filterSellerOwner(currentUser())
                    ->first();

                if (!$blog) {
                    return $this->errorResponse('Blog not found or unauthorized', 404);
                }
            }

            if (!isset($data['slug'])) {
                $data['slug'] = Str::slug($data['title']);
            }

            if (isset($data['collections'])) {
                $data['collections'] = json_encode($data['collections']);
            }

            // Create or update blog
            if ($blog) {
                $blog->update($data);
            } else {
                $blog = Blog::create($data);
            }
            return $this->successResponse($blog);
        } catch (Exception $e) {
            return $this->errorResponse('Failed to save blog: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Display the specified blog
     */
    public function show($id): JsonResponse
    {
        $blog = Blog::with(['store:id,name', 'seller:id,name', 'category:id,name'])
            ->whereKey($id)
            ->filterSellerOwner()
            ->firstOrFail();

        return $this->successResponse($blog, 'Blog retrieved successfully');
    }

    public function publicShow($slug): JsonResponse
    {
        $blog = Blog::with(['store:id,name', 'seller:id,name', 'category:id,name'])
            ->where('slug', $slug)
            ->filterStoreOwner()
            ->firstOrFail();

        $blogService = new BlogService();
        $blogService->setCollectionsDataForBlogs($blog);
        $blogService->parsingShortCode($blog);

        return $this->successResponse($blog, 'Blog retrieved successfully');
    }

    public function getSuggestions($slug): JsonResponse
    {
        $blog = Blog::where('slug', $slug)
            ->filterStoreOwner()
            ->firstOrFail();

        $blogs = Blog::query()
            ->select('id', 'title', 'slug', 'category_id', 'created_at')
            ->where('slug', '!=', $slug)
            ->where('status', true)
            ->when($blog->category_id, function ($query) use ($blog) {
                $query->where('category_id', $blog->category_id);
            })
            ->filterStoreOwner()
            ->limit(4)
            ->orderBy('created_at', 'desc')
            ->get();

        return $this->successResponse($blogs, 'Suggestions retrieved successfully');
    }

    /**
     * Update blog status (active/inactive)
     */
    public function updateStatus(UpdateBlogStatusRequest $request, $id): JsonResponse
    {
        $data = $request->validated();

        $blog = Blog::query()
            ->where('id', $id)
            ->filterSellerOwner()
            ->firstOrFail();

        $blog->update(['status' => (bool) $data['status']]);

        return $this->successResponse($blog, 'Blog status updated successfully');
    }

    /**
     * Remove the specified blog
     */
    public function destroy($id): JsonResponse
    {
        $blog = Blog::query()
            ->whereKey($id)
            ->filterSellerOwner()
            ->firstOrFail();

        $blog->delete();
        return $this->successResponse(null, 'Blog deleted successfully');
    }
}

<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\CampaignPublicStatusEnum;
use App\Enums\CampaignRenderModeEnum;
use App\Enums\CampaignStatusEnum;
use App\Enums\CurrencyEnum;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\MockupTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PricingModeEnum;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\SmartRemarketingEnum;
use App\Enums\UserInfoKeyEnum;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Controllers\SystemConfigController;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\File;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Slug;
use App\Models\Store;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserInfo;
use App\Services\CampaignService;
use App\Services\ExpressMockupRenderService;
use App\Services\FulfillmentService;
use App\Services\ProductReviewService;
use App\Services\StoreService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use InvalidArgumentException;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Illuminate\Support\Arr;
use App\Http\Controllers\CampaignController as StorefrontCampaignController;
use Modules\ShardingTable\Jobs\InsertCampaignJob;

class CampaignController extends Controller
{
    use ApiResponse;
    use ElasticClient;

    /**
     * @param Request $request
     * @param string $slug
     * @return JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \Throwable
     */
    public function show(Request $request, string $slug): JsonResponse
    {
        $slug = strtolower($slug);
        $productSlug = $request->get('product');
        $productName = null;
        if (isset($productSlug)) {
            if (is_array($productSlug)) {
                $productSlug = Arr::first($productSlug);
            }
            $productSlug = str_replace('-', ' ', $productSlug);
            $productName = Str::title(is_array($productSlug) ? Arr::first($productSlug) : $productSlug);
        }
        $parent = $this;
        $cacheKey = CacheKeys::getProductCacheKey($slug);
        $cache = (string)$request->header('fcache');
        if ($cache === '1') {
            // *** CLEAR BOTH CACHE
            cache()->forget($cacheKey);
            cacheAlt()->forget($cacheKey);
        }
        if ($cache === '0') {
            $cacheKeys = [];
            $cacheKeys[] = $cacheKey;
            // SYNC CLEAR BOTH CACHE
            syncClearCache($cacheKeys);
            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
        }
        $campaign = null;
        try {
            $hasCached = hasCached($cacheKey);
            if ($hasCached) {
                $campaign = cache()->get($cacheKey);
            }

            if (is_null($campaign)) {
                $hasCached = hasCached($cacheKey, CacheKeys::CACHE_TYPE_ALTERNATIVE);
                if ($hasCached) {
                    $campaign = cacheAlt()->get($cacheKey);
                }
            }

            if (is_null($campaign)) {
                $sellerId = Slug::query()->where('slug', $slug)->value('seller_id') ?? $request->get('seller_id');
                $seller = UserService::getSellerSharding($sellerId);
                $campaign = cacheAlt()->remember($cacheKey, CacheKeys::CACHE_1W, function () use ($slug, $cache, $parent, $productName, $seller) {
                    return $this->fetchCampaignData($slug, $cache, $parent, $productName, $seller);
                });
            }
        } catch (\Exception $e) {
            logException($e, 'Storefront/CampaignController@show');
            return $this->errorResponse('Load product failed.');
        }
        if (is_string($campaign)) {
            $campaign = json_decode($campaign);
        }
        $storeInfo = StoreService::getCurrentStoreInfo();
        if (is_null($campaign)) {
            return $this->errorResponse('Campaign not found.');
        }
        /* @var Campaign $campaign */
        if (!$campaign->seller) {
            return $this->errorResponse('Campaign seller not found.');
        }
        if (empty($storeInfo)) {
            return $this->errorResponse('Store information not found.');
        }
        if ($campaign->status === ProductStatus::BLOCKED) {
            return $this->errorResponse(ProductStatus::BLOCKED);
        }

        $smartRemarketing = $storeInfo->smart_remarketing ?? SmartRemarketingEnum::DISABLED;
        $isSellerCustomPayment = !empty($campaign->seller->custom_payment);
        $isCurrentSellerCustomPayment = !empty($storeInfo->custom_payment);
        $isSellerNotTrusted = $campaign->seller->status !== UserStatusEnum::TRUSTED;
        $isMarketPlaceStore = $storeInfo->id === Store::SENPRINTS_STORE_ID;
        $isPublicCampaign = in_array($campaign->public_status, [CampaignPublicStatusEnum::YES, CampaignPublicStatusEnum::APPROVED], true);
        $isDifferentSeller = $campaign->seller_id !== $storeInfo->seller_id && $storeInfo->seller_id !== User::SENPRINTS_SELLER_ID;
        $isCanNotListCrossSell = $isSellerCustomPayment && $isSellerNotTrusted && ($isMarketPlaceStore || $isDifferentSeller);
        $isCurrentSellerCustomPaymentConflict = $isCurrentSellerCustomPayment && $storeInfo->seller_id !== $campaign->seller_id;
        $isSellerIsNotEnabledUpsellMP = $smartRemarketing > 0 && $smartRemarketing !== SmartRemarketingEnum::ENABLED_WITH_UPSELL_MP;
        if ($isDifferentSeller) {
            if ($isSellerIsNotEnabledUpsellMP || !$isPublicCampaign) {
                return $this->errorResponse('Product access denied: Cross-seller product not available or not public.');
            }
        }
        if (!$isPublicCampaign && ($isCanNotListCrossSell || $isCurrentSellerCustomPaymentConflict)) {
            return $this->errorResponse('Product access denied: Private product with payment restrictions.');
        }
        self::mappingCorrectPricing($campaign);
        if (($campaign->personalized === PersonalizedType::CUSTOM_OPTION) && !empty($campaign->options->options)) {
            if (isset($campaign->options->options->text) || isset($campaign->options->options->dropdown) || isset($campaign->options->options->image)) {
                $options = [];
                foreach ($campaign->options->options as $kOption => $option) {
                    if ($option->active) {
                        $option->type = $kOption;
                        $options[] = $option;
                    }
                }
                $campaign->options->options = $options;
            }
        }
        $campaign->store_name = $storeInfo->name;
        $campaign->reviews = ProductReviewService::getProductReviewStats($campaign->id, $campaign->template_id, $campaign->seller_id);
        if ($cache !== '') {
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaign->id, sellerId: $campaign->seller_id);
        }
        // Hide collections on preview domain
        $preview_domain = SystemConfig::getConfig('preview_domain', 'senstores.com');
        if ($preview_domain === $storeInfo->domain) {
            $campaign->collections = null;
        }
        CampaignService::overrideCountdownStatus($campaign, $storeInfo);
        return $this->successResponse($campaign)->withHeaders([
            'Cache-Tags' => 'campaign=' . $campaign->id,
            'Cache-Expire-Time' => CacheTime::CACHE_1W
        ])->withCached($hasCached);
    }

    private function getVideos($templateIds): array
    {
        $files = File::query()
            ->whereIn('product_id', $templateIds)
            ->where('type', FileTypeEnum::VIDEO)
            ->where('status', FileStatusEnum::ACTIVE)
            ->whereNotNull('option')
            ->orderBy('position')
            ->get();

        $files = $files->groupBy('product_id');

        return $files->map(function ($fileGroup, $templateId) {
            return collect($fileGroup)->map(function ($video) use ($templateId) {
                $thumbnail = json_decode($video['option'], true)['thumbnail'] ?? null;

                return [
                    'template_id' => $templateId,
                    'url' => s3Url($video['file_url']),
                    'thumb' => $thumbnail,
                    'position' => $video['position']
                ];
            });
        })->toArray();
    }

    /**
     * @param $slug
     * @param $cache
     * @param $parent
     * @param $productName
     * @param $seller
     * @return string|null
     * @throws \Throwable
     */
    public function fetchCampaignData($slug, $cache, $parent, $productName, $seller): ?string
    {
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'name',
                'slug',
                'description',
                'end_time',
                'show_countdown',
                'seller_id',
                'default_product_id',
                'product_type as type',
                'tracking_code',
                'mockup_type',
                'personalized',
                'market_location',
                'currency_code',
                'status',
                'product_type',
                'template_id',
                'options',
                'default_option',
                'system_type',
                'thumb_url',
                'public_status',
                'attributes',
                'pricing_mode',
                'combo_price'
            ])
            ->where('slug', $slug)
            ->withoutGlobalScope('campaign_type')
            ->whereIn('product_type', [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
            ])
            ->whereIn('status', [ProductStatus::ACTIVE, ProductStatus::BLOCKED])
            ->with([
                'products' => function ($query) {
                    $query->select([
                        'id',
                        'name',
                        'thumb_url',
                        'options',
                        'default_option',
                        'currency_code',
                        'price',
                        'old_price',
                        'campaign_id',
                        'template_id',
                        'personalized',
                        'full_printed',
                        'pricing_mode',
                        'market_location'
                    ]);
                    $query->with(['template' => function ($query) {
                        $query->on(config('database.default'));
                        $query->select(['id', 'description', 'attributes', 'status', 'full_printed', 'base_costs', 'base_cost', 'options']);
                    }]);
                    $query->orderBy('priority', 'ASC');
                },
                'defaultProduct:id,name,template_id',
                'images',
                'seller:id,name,nickname,status,custom_payment,tags,sharding_status,db_connection',
            ])
            ->first();
        if (is_null($campaign)) {
            return null;
        }

        $campaign->load(['collections' => function ($query) use ($campaign) {
            $query->select(['id', 'name', 'slug']);
            $query->where('product_collection.seller_id', $campaign->seller_id);
        }]);

        if ($cache === '0') {
            // clear cache
            $cacheKeys = [];
            $cacheKeys['tags'][] = CacheKeys::getCampaignId($campaign->id);
            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
        }
        if (!$campaign->seller) {
            return null;
        }
        if ($campaign->product_type === ProductType::CAMPAIGN_EXPRESS || $campaign->system_type === ProductSystemTypeEnum::MOCKUP) {
            unset($campaign->products);
            $campaign->load([
                'expressOrMockupProducts' => function ($query) use ($seller) {
                    $query->onSellerConnection($seller)->select([
                        'id',
                        'name',
                        'options',
                        'default_option',
                        'currency_code',
                        'price',
                        'old_price',
                        'campaign_id',
                        'template_id',
                        'personalized',
                        'full_printed',
                        'pricing_mode',
                        'market_location',
                        'thumb_url'
                    ])
                        ->with(['template:id,description,attributes,status,full_printed,base_costs,base_cost'])
                        ->orderBy('priority');
                }
            ]);
            if ($campaign->expressOrMockupProducts && $seller && $seller->hasPrivateConnection() && $campaign->expressOrMockupProducts->isEmpty()) {
                $old_connection = UserInfo::getSetting($seller->id, UserInfoKeyEnum::OLD_CONNECTION, UserInfo::getSetting($seller->id, UserInfoKeyEnum::OLD_CONNECTION_DELETED));
                InsertCampaignJob::dispatch((int)$campaign->template_id, $seller, $old_connection)->onQueue('sharding-table-insert');
            }
            $campaign->setRelation('products', $campaign->expressOrMockupProducts);
            unset($campaign->expressOrMockupProducts);
        }

        //load campaign variants
        $productTemplateIds = [];
        $variantProductIds = [];
        $conditionsWhereFiles = [];
        $variantDefaultTemplateId = null;
        $currentProductId = null;
        $campaignProductIds = [];
        foreach ($campaign->products as $key => $product) {
            $campaignProductIds [] = $product->id;
            $product->custom_options ??= null;
            $product->common_options ??= null;
            $product->template_custom_options ??= null;
            if ($product->template) {
                if (!in_array($product->template->status, [ProductStatus::ACTIVE, ProductStatus::ARCHIVED], true)) {
                    $campaign->products->forget($key);
                    continue;
                }

                $productTemplateIds[] = $product->template->id;
                $variantProductIds[] = $product->template->id;
                $product->description = $product->template->description;
                $product->attributes = $product->template->attributes;
                if ($product->template->full_printed === ProductPrintType::HANDMADE) {
                    $conditionsWhereFiles[$product->template->id] = [
                        'product_id' => $product->template->id,
                        'render_type' => FileRenderType::NONE
                    ];
                } else {
                    $conditionsWhereFiles[$product->template->id] = [
                        'product_id' => $product->template->id,
                        'render_type' => FileRenderType::IMAGE
                    ];
                }
                if (!isset($productName) && $product->id == $campaign->default_product_id) {
                    $variantDefaultTemplateId = $product->template->id;
                    $currentProductId = $product->id;
                }
                $templateOptions = Str::isJson($product->template->options) ? json_decode($product->template->options, false, 512, JSON_THROW_ON_ERROR) : [];
                if ($product->full_printed === ProductPrintType::HANDMADE) {
                    if (!empty($templateOptions->custom_options)) {
                        $product->custom_options = $templateOptions->custom_options;
                        $product->template_custom_options = $templateOptions->custom_options;
                        unset($templateOptions->custom_options);
                    }
                    if (!empty($templateOptions->common_options)) {
                        $product->common_options = $templateOptions->common_options;
                        unset($templateOptions->common_options);
                    }
                    $product->options = json_encode($templateOptions, JSON_THROW_ON_ERROR);
                }
            }
            if ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                $variantProductIds[] = $product->id;
            }
            // update campaign ID for express camp
            if ($campaign->product_type === ProductType::CAMPAIGN_EXPRESS || $campaign->system_type === ProductSystemTypeEnum::MOCKUP) {
                $product->campaign_id = $campaign->id;
            }
            unset($product->template);
        }
        $isCustomPrice = false;
        if (isset($productName)) {
            $productsQuery = Product::query()
                ->onSellerConnection($seller)
                ->select(['id', 'name', 'template_id', 'pricing_mode'])
                ->where('campaign_id', $campaign->id)
                ->where('product_type', ProductType::PRODUCT)
                ->where('status', ProductStatus::ACTIVE)
                ->get();
            foreach ($productsQuery as $product) {
                if (isset($product, $product->template) && $productName === str_replace('-', ' ', $product->name)) {
                    if ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                        $variantDefaultTemplateId = $product->id;
                        $isCustomPrice = true;
                    } else {
                        $variantDefaultTemplateId = $product->template->id;
                    }
                    $currentProductId = $product->id;
                }
            }
        }
        if ($variantDefaultTemplateId) {
            $variants = $parent->getVariantsOfCampaign($campaign, $variantDefaultTemplateId, $isCustomPrice);
        } else {
            $variants = $parent->getVariants($campaign, $variantProductIds);
        }

        $campaign->variants = $variants;
        $campaign->current_product_id = $currentProductId;
        if ($campaign->product_type === ProductType::CAMPAIGN_EXPRESS) {
            $newImagesArray = $parent->processMockupForExpress2Campaign($campaign, $productTemplateIds);
        } else {
            $templateImages = File::query()
                ->select([
                    'id',
                    'campaign_id',
                    'file_url',
                    'file_url_2',
                    'product_id',
                    'option',
                ])
                ->whereIn('product_id', $productTemplateIds)
                ->when(!empty($conditionsWhereFiles), function ($q) use ($conditionsWhereFiles) {
                    $q->where(function ($q) use ($conditionsWhereFiles) {
                        foreach ($conditionsWhereFiles as $condition) {
                            $q->orWhere(function ($q) use ($condition) {
                                $q->where($condition);
                            });
                        }
                    });
                }, function ($q) {
                    $q->where('render_type', FileRenderType::IMAGE);
                })
                ->where('type', FileTypeEnum::MOCKUP)
                ->orderBy('position')
                ->get();
            $newImagesArray = $campaign->images->concat($templateImages);
            $newImagesArray = $newImagesArray->map(function ($image) use ($campaign) {
                if (isset($image->mockup->allow_additional_print_space) && !$image->mockup->allow_additional_print_space && in_array($image->print_space, PrintSpaceEnum::additionalPrintSpaces(), true)) {
                    return null;
                }
                if ($campaign->system_type === ProductSystemTypeEnum::CUSTOM) {
                    if (str_starts_with($image->file_url, 'p/') && str_starts_with($image->file_url_2, 'http')) {
                        $image->file_url_2 = $image->file_url;
                    } else if (str_starts_with($image->file_url_2, 'p/') && str_starts_with($image->file_url, 'http')) {
                        $image->file_url = $image->file_url_2;
                    }
                } else if (empty($image->file_url)) {
                    $image->file_url = $image->file_url_2;
                    unset($image->file_url_2);
                }
                return $image;
            });
        }
        $newImagesArray = $newImagesArray->filter(function ($item) {
            return $item !== null;
        });
        unset($campaign->images);
        $newImagesAfterResolvePrintSpace = [];
        $designFiles = File::query()
            ->onSellerConnection($seller)
            ->select(['id', 'campaign_id', 'file_url', 'file_url_2', 'product_id', 'option', 'print_space'])
            ->where('product_id', $campaignProductIds)
            ->whereNotNull('file_url')
            ->get();
        foreach ($newImagesArray as $newImage) {
            $collect = $designFiles->filter(function ($design) use ($newImage) {
                return $design->print_space == $newImage['print_space'] && $design->product_id == $newImage['product_id'];
            });
            if (in_array($newImage['print_space'], PrintSpaceEnum::additionalPrintSpaces(), true) && $collect->isEmpty()) {
                continue;
            }
            $newImagesAfterResolvePrintSpace[] = $newImage;
        }
        $campaign->images = $newImagesAfterResolvePrintSpace;

        $templateIds = array_unique([$campaign->template_id, ...$campaign->products->pluck('template_id')->toArray()]);
        $videos = $this->getVideos($templateIds);
        foreach ($campaign->products as $product) {
            $product->videos = $videos[$product->template_id] ?? null;
        }

        // sort product
        $products = $campaign->products->values();
        unset($campaign->products);
        $campaign->products = $parent->sortTopProducts($products, $campaign->seller_id);

        if (!$campaign->seller->nickname) {
            $campaign->seller->nickname = $campaign->seller->name;
        }
        $campaign->seller->slug = $campaign->seller->getSlugNameWithHash();
        $campaign->seller->via = str_replace(['mysql_', 'mysql', '_'], ['m', '', ''], $campaign->seller->getPrivateConnection());
        if ($campaign->public_status !== CampaignPublicStatusEnum::APPROVED && (!empty($campaign->seller->deleted_at) || $campaign->seller->status === UserStatusEnum::SOFT_BLOCKED || $campaign->seller->status === UserStatusEnum::HARD_BLOCKED)) {
            $campaign->status = CampaignStatusEnum::BLOCKED;
        }

        $campaignOptions = Str::isJson($campaign->options) ? json_decode($campaign->options, true, 512, JSON_THROW_ON_ERROR) : [];
        $campaign->common_options = null;
        $campaign->options = null;
        if (!empty($campaignOptions)) {
            if (isset($campaignOptions['common_options'])) {
                $campaign->common_options = $campaignOptions['common_options'];
                unset($campaignOptions['common_options']);
            }
            $campaign->options = $campaignOptions;
        }
        // update thumb url for custom & mockup campaign on seller used Sen payment
        if (empty($seller->custom_payment) && in_array($campaign->system_type, [ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::CUSTOM], true)) {
            $collectProducts = collect($campaign->products);
            $templateIds = $collectProducts->pluck('template_id')->unique()->toArray();
            $uniqueProductsThumb = $collectProducts->pluck('thumb_url')->unique()->values();
            if (!empty($templateIds) && ($uniqueProductsThumb->isEmpty() || $uniqueProductsThumb->count() === 1)) {
                $mockups = File::query()
                    ->select(['id', 'product_id', 'campaign_id', 'design_json', 'file_url', 'file_url_2', 'type', 'status', 'render_type'])
                    ->whereIn('product_id', $templateIds)
                    ->where('type', FileTypeEnum::MOCKUP)
                    ->where('status', FileStatusEnum::ACTIVE)
                    ->where('print_space', PrintSpaceEnum::FRONT)
                    ->where('type_detail', MockupTypeEnum::GHOST)
                    ->where('render_type', FileRenderType::RENDER_3D)
                    ->whereRaw("file_url NOT LIKE 'https://%'")
                    ->orderBy('id')
                    ->get();
                $campaign->products = array_map(function ($product) use ($mockups) {
                    $currentProductMockup = $mockups->firstWhere('product_id', $product->template_id);
                    if (empty($currentProductMockup)) {
                        return $product;
                    }
                    $needUpdateThumb = false;
                    if (empty($product->thumb_url) || preg_match('/^p\/\d+\/[a-z0-9]+\.(png|jpg|jpeg|gif)$/i', $product->thumb_url) || !preg_match('/\/s4\/l_p:[^\/]+\/co_rgb:[^\/]+,e_colorize:\d+/i', $product->thumb_url)) {
                        $needUpdateThumb = true;
                    }
                    if ($needUpdateThumb) {
                        $product->thumb_url = StorefrontCampaignController::getCdnMockupImage($currentProductMockup, null, '');
                    }
                    return $product;
                }, $campaign->products);
            }
        }
        return $campaign->toJson();
    }

    /**
     * Process mockup for Express 2 Campaign.
     * @param Campaign $campaign
     * @param array $productTemplateIds
     * @return Collection<array-key, mixed>
     * @throws InvalidArgumentException
     */
    public function processMockupForExpress2Campaign(Campaign $campaign, array $productTemplateIds)
    {
        $seller = User::query()->find($campaign->seller_id);
        $fileDesign = File::query()
            ->onSellerConnection($seller)
            ->where([
                'campaign_id' => $campaign->id,
                'type' => FileTypeEnum::DESIGN
            ])
            ->first();

        // Get resized file path from design_json
        if ($fileDesign && !empty($fileDesign->design_json)) {
            $designJson = json_decode($fileDesign->design_json, true);
            if (isset($designJson['resized_file_path'])) {
                $fileDesign->file_url = $designJson['resized_file_path'];
            }
        }

        $templateImages = File::query()
            ->select([
                'id',
                'campaign_id',
                'file_url',
                'file_url_2',
                'product_id',
                'option',
                'token',
                'type_detail',
                'print_space',
                'design_json'
            ])
            ->where([
                'render_type' => FileRenderType::EXPRESS,
                'type' => FileTypeEnum::MOCKUP,
                'status' => FileStatusEnum::ACTIVE,
            ])
            ->where('type_detail', $campaign->mockup_type)
            // ->where('print_space', $printSpace) // printspace
            ->whereIn('product_id', $productTemplateIds)
            ->orderBy('position')
            ->get();

        return $templateImages->map(function ($mockup) use ($fileDesign, $campaign) {
            $templatePrintSpace = $mockup->print_space;
            $templateProduct = $mockup->product;
            $product = $campaign->products->firstWhere('template_id', $templateProduct->id);
            $defaultColor = '000000';
            if ($product !== null) {
                $defaultColor = color2hex($product->default_option);
                $defaultColor = substr($defaultColor, 1);
            }
            $renderMode = ($campaign->render_mode == $product->template->render_mode) ? $campaign->render_mode : CampaignRenderModeEnum::NATURE;
            // Express Mockup Render Service
            $renderService = new ExpressMockupRenderService();
            if ($campaign->default_option === $templatePrintSpace) {
                $mockup->file_url = $renderService->prepareOptionsForGenerateImageUrl($mockup, $fileDesign, $defaultColor, $renderMode);
            } else {
                $mockup->file_url = $renderService->prepareOptionsForGenerateImageUrl($mockup, $fileDesign, $defaultColor, $renderMode, true);
            }

            unset($mockup->product);
            if (!empty($product) && empty($product->thumb_url) && $templatePrintSpace === $campaign->default_option) {
                $product->thumb_url = $mockup->file_url;
            }

            return $mockup;
        });
    }


    public function sortTopProducts($products, $seller_id): array
    {
        $topSellerTemplateIds = topSellerTemplateIds($seller_id);

        $sortProducts = [];
        foreach ($topSellerTemplateIds as $templateId) {
            foreach ($products as $key => $product) {
                if ($product->template_id == $templateId) {
                    $sortProducts[] = $product;
                    $products->forget($key);
                }
            }
        }
        foreach ($products as $key => $product) {
            $product->custom_options ??= null;
            if ($product->full_printed === ProductPrintType::HANDMADE) {
                $options = json_decode($product->options, true);
                if (!empty($options['custom_options'])) {
                    $product->custom_options = $options['custom_options'];
                    unset($options['custom_options']);
                    $product->options = json_encode($options);
                }
            }
            $sortProducts[] = $product;
            $products->forget($key);
        }

        return $sortProducts;
    }

    public function getVariants($campaign, $variantProductIds): array
    {
        $variants = ProductVariant::query()
            ->select([
                'product_id',
                'variant_key',
                'out_of_stock',
                'adjust_price',
                'price',
                'old_price',
                'location_code'
            ])
            ->whereIn('product_id', $variantProductIds)
            ->where('location_code', $campaign->market_location)
            ->where(
                function ($q) {
                    $q->where('out_of_stock', 1)
                        ->orWhere('adjust_price', '>', 0)
                        ->orWhere('campaign_id', '>', 0);
                }
            )
            ->get();

        $productVariants = [];
        $currencyRate = 1;
        $pricing = UserService::getPricingBySellerId($campaign->seller_id)->first();
        $currencyCode = isset($pricing) ? $pricing->currency_code : $campaign->currency_code;
        if ($currencyCode !== CurrencyEnum::USD) {
            $currency = SystemConfigController::findOrDefaultCurrency($currencyCode);
            if ($currency) {
                $currencyRate = $currency->rate;
            }
        }

        foreach ($campaign->products as $product) {
            $options = json_decode($product->options);
            $product->custom_options ??= null;
            if ($product->full_printed === ProductPrintType::HANDMADE && !empty($options->custom_options)) {
                $product->custom_options = $options->custom_options;
                unset($options->custom_options);
                $product->options = json_encode($options);
            }
            $validVariantKeys = generateVariantKeysByProductOptions($options);
            foreach ($variants as $key => $variant) {
                if ($variant->product_id === $product->template_id) {
                    if (in_array($variant->variant_key, $validVariantKeys)) {
                        $variant->adjust_price = ceil($variant->adjust_price * $currencyRate);
                        if ($product->pricing_mode === PricingModeEnum::FIXED_PRICE) {
                            $variant->price = $product->price;
                            $variant->old_price = $product->old_price;
                        } else if ($product->pricing_mode === PricingModeEnum::ADJUST_PRICE) {
                            $variant->price = round($product->price + $variant->adjust_price, 2);
                            $variant->old_price = $product->old_price > 0 ? calculateCompareAtPrice($variant->price) : 0;
                        } else if ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                            $variant->price *= $currencyRate;
                            foreach ($variants as $key2 => $variant2) {
                                if ($variant2->product_id === $product->id && $variant2->variant_key === $variant->variant_key) {
                                    $variant->price = $variant2->price;
                                    $variants->forget($key2);
                                    break;
                                }
                            }
                            $variant->old_price = $product->old_price > 0 ? calculateCompareAtPrice($variant->price) : 0;
                        }
                        $variant->product_id = $product->id;
                        $productVariants[] = $variant->toArray();
                    }
                    $variants->forget($key);
                }
            }
            if ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                foreach ($variants as $key => $variant) {
                    if ($variant->product_id === $product->id) {
                        $productVariants[] = $variant->toArray();
                        $variants->forget($key);
                    }
                }
            }
        }
        return $productVariants;
    }

    /**
     * @param $campaign
     * @param $variantProductId
     * @param bool $loadFromSharding
     * @return array
     * @throws \JsonException
     */
    private function getVariantsOfCampaign($campaign, $variantProductId, $loadFromSharding = false): array
    {
        $sellerId = $campaign->seller_id;
        $seller = User::query()->find($sellerId);
        if ($loadFromSharding && $seller->db_connection !== 'mysql') {
            $variants = ProductVariant::findAndCacheByProductSharding($variantProductId, $seller)->all();
        } else {
            $variants = ProductVariant::findAndCacheByTemplate($variantProductId)->all();
        }
        $variants = collect($variants);
        $totalVariants = $variants->toArray();
        $productVariants = [];
        $currencyRate = 1;
        $pricing = UserService::getPricingBySellerId($campaign->seller_id)->first();
        $currencyCode = isset($pricing) ? $pricing->currency_code : $campaign->currency_code;
        if ($currencyCode !== CurrencyEnum::USD) {
            $currency = SystemConfigController::findOrDefaultCurrency($currencyCode);
            if ($currency) {
                $currencyRate = $currency->rate;
            }
        }

        foreach ($campaign->products as $product) {
            $options = json_decode($product->options);
            $product->custom_options ??= null;
            if ($product->full_printed === ProductPrintType::HANDMADE && !empty($options->custom_options)) {
                $product->custom_options = $options->custom_options;
                unset($options->custom_options);
                $product->options = json_encode($options);
            }
            $validVariantKeys = generateVariantKeysByProductOptions(json_decode($product->options));
            foreach ($variants as $key => $variant) {
                if ($variant->product_id === $product->template_id) {
                    if (in_array($variant->variant_key, $validVariantKeys)) {
                        $variant->adjust_price = ceil($variant->adjust_price * $currencyRate);

                        if ($product->pricing_mode === PricingModeEnum::FIXED_PRICE) {
                            $variant->price = $product->price;
                            $variant->old_price = $product->old_price;
                        } else if ($product->pricing_mode === PricingModeEnum::ADJUST_PRICE) {
                            $variant->price = round($product->price + $variant->adjust_price, 2);
                            // $variant->price = round($product->price, 2);
                            $variant->old_price = $product->old_price > 0 ? calculateCompareAtPrice($variant->price) : 0;
                        } else if ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                            $variant->price *= $currencyRate;
                            $variants2 = ProductVariant::findAndCacheByTemplate($product->id)->all();
                            if (!isset($variants2)) {
                                $variants2 = $variants;
                            }
                            foreach ($variants2 as $key2 => $variant2) {
                                if ($variant2->product_id === $product->id && $variant2->variant_key === $variant->variant_key) {
                                    $variant->price = $variant2->price;
                                    $variants->forget($key2);
                                    break;
                                }
                            }
                            $variant->old_price = $product->old_price > 0 ? calculateCompareAtPrice($variant->price) : 0;
                        }
                        $variant->product_id = $product->id;
                        $variantResult = $variant->toArray();
                        if ($variantResult['base_cost'] == 0) {
                            $lowestBaseCostVariant = CampaignService::getLowestBaseCostVariant($variantResult['variant_key'], $variantResult['location_code'], $totalVariants);
                            if (isset($lowestBaseCostVariant['base_cost'])) {
                                $variantResult['base_cost'] = $lowestBaseCostVariant['base_cost'];
                            }
                        }
                        $productVariants[] = $variantResult;
                    }
                    $variants->forget($key);
                }
            }
            if ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                foreach ($variants as $key => $variant) {
                    if ($variant->product_id === $product->id) {
                        $productVariants[] = $variant->toArray();
                        $variants->forget($key);
                    }
                }
            }
        }
        return $productVariants;
    }

    private function getCampaignBySlug(string $slug)
    {
        // check if we can get ID from cache
        $cacheKey = CacheKeys::getProductCacheKey($slug);
        $campaign = getDataByDefaultCache($cacheKey);
        if (is_null($campaign)) {
            $campaign = cacheAlt()->get($cacheKey);

            if (is_null($campaign)) {
                $slug = Slug::query()->where('slug', $slug)->first();
                $seller = User::query()->find($slug?->seller_id);
                $campaign = Campaign::query()
                    ->onSellerConnection($seller)
                    ->select(['id', 'mockup_type'])
                    ->firstWhere('id', $slug?->campaign_id);
            }
        }

        if (is_string($campaign)) {
            $campaign = json_decode($campaign);
        }

        return $campaign;
    }

    public function getCampaignImages(string $campaignSlug): JsonResponse
    {
        $campaign = $this->getCampaignBySlug($campaignSlug);
        $seller = User::query()->find($campaign->seller_id);
        $images = [];

        if (!is_null($campaign)) {
            $images = File::query()
                ->onSellerConnection($seller)
                ->select(['id', 'file_url', 'file_url_2', 'file_name', 'product_id', 'option', 'token'])
                ->where([
                    'campaign_id' => $campaign->id,
                    'type' => FileTypeEnum::IMAGE
                ])
                ->orderBy('position')
                ->orderBy('id')
                ->get();


            if ($images->count() > 0) {
                foreach ($images as $image) {
                    if (empty($image->file_url)) {
                        $image->file_url = $image->file_url_2;
                    }
                }
            }
        }

        return response()->json([
            'campaign_slug' => $campaignSlug,
            'images' => $images
        ]);
    }

    /**
     * @param string $slug
     * @return JsonResponse
     */
    public function getCustomDesigns(string $slug): JsonResponse
    {
        $storeInfo = currentStoreInfo();

        if (empty($storeInfo)) {
            return $this->errorResponse('Store information not found for custom designs.');
        }

        $campaign = $this->getCampaignBySlug($slug);

        if (is_null($campaign)) {
            return $this->errorResponse('Campaign not found for custom designs.');
        }

        $productIds = [];
        $productTemplateIds = [];
        foreach ($campaign->products as $product) {
            $productIds[] = $product->id;
            $productTemplateIds[] = $product->template_id;
        }

        $seller = User::query()->find($campaign->seller_id);
        $pbArtworks = File::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'file_url',
                'token',
                'type',
                'type_detail AS pb_artwork_id',
                'design_json',
                'print_space',
                'product_id',
                'campaign_id',
            ])
            ->where([
                'campaign_id' => $campaign->id,
                'option' => FileRenderType::PB,
                'type' => FileTypeEnum::DESIGN,
            ])
            ->whereIn('product_id', $productIds)
            ->whereNotNull('type_detail')
            ->get();

        $customDesign = File::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'file_url',
                'token',
                'type',
                'design_json',
                'print_space',
                'product_id',
                'campaign_id',
            ])
            ->where([
                'campaign_id' => $campaign->id,
                'option' => FileRenderType::CUSTOM,
                'type' => FileTypeEnum::DESIGN,
            ])
            ->whereIn('product_id', $productIds)
            ->get();

        $threeDMockups = File::query()
            ->select([
                'id',
                'file_url',
                'file_url_2',
                'token',
                'type',
                'design_json',
                'print_space',
                'option',
                'product_id',
                'campaign_id',
            ])
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => FileRenderType::RENDER_3D,
            ])
            ->where(function ($query) use ($campaign) {
                $query->where('type_detail', $campaign->mockup_type)
                    ->orWhereNull('type_detail');
            })
            ->whereIn('product_id', $productTemplateIds)
            ->get();

        $baseMockups = File::query()
            ->select([
                'id',
                'file_url_2',
                'token',
                'type',
                'design_json',
                'print_space',
                'option',
                'product_id',
                'campaign_id',
            ])
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => FileRenderType::BASE,
            ])
            ->where(function ($query) use ($campaign) {
                $query->where('type_detail', $campaign->mockup_type)
                    ->orWhereNull('type_detail');
            })
            ->whereIn('product_id', $productTemplateIds)
            ->get();

        $customDesign = $customDesign->map(function ($design) {
            if (isset($design->design_json)) {
                [$designJson,] = FulfillmentService::updateUrlOnDesignJson($design->design_json);
                $design->design_json = $designJson;
            }
            return $design;
        });

        return $this->successResponse([
            'custom_designs' => $customDesign,
            'base_mockup' => $baseMockups,
            'pb_artworks' => $pbArtworks,
            'three_d_mockups' => $threeDMockups,
        ])->withHeaders([
            'Cache-Tags' => 'campaign=' . $campaign->id,
            'Cache-Expire-Time' => CacheTime::CACHE_1W
        ]);
    }

    /**
     * @param $campaign
     * @param bool $setPricingForProductVariant
     * @return void
     * @throws \JsonException
     */
    public static function mappingCorrectPricing(&$campaign, bool $setPricingForProductVariant = false): void
    {
        // Skip pricing for combo campaigns
        if ($campaign->system_type === ProductSystemTypeEnum::COMBO) {
            return;
        }
        $pricing = UserService::getPricingBySellerId($campaign->seller_id);
        if ($pricing->isNotEmpty()) {
            $campaignCurrency = SystemConfigController::findOrDefaultCurrency($campaign->currency_code);
            $pricingCurrency = SystemConfigController::findOrDefaultCurrency($pricing->first()->currency_code);
            $campaignVariantCollection = collect($campaign->variants);
            CampaignService::getProductsCampaignExtraPrintCost($campaign->products);
            foreach ($campaign->products as $product) {
                $productPricing = $pricing->where('product_id', $product->template_id);
                if ($productPricing->isNotEmpty()) {
                    $extraPrintCost = data_get($product, 'extra_print_cost');
                    $mappedVariants = $campaignVariantCollection->where('product_id', $product->id);
                    if ($product->pricing_mode === PricingModeEnum::FIXED_PRICE || $product->pricing_mode === PricingModeEnum::ADJUST_PRICE) {
                        $productPricing = $productPricing->first();
                        $productPricingCurrency = $productPricing->currency_code;
                        $productPricing->applied_test_price = $productPricing->test_price != 0 ? ($productPricing->price + $productPricing->test_price) : 0;
                        $productPricing->applied_compare_test_price = $productPricing->test_price != 0 ? ($productPricing->compare_price + $productPricing->test_price) : 0;
                        $product->currency_code = isset($productPricingCurrency) ? $productPricingCurrency : $campaign->currency_code;
                        $product->price = round($productPricing->price + ($extraPrintCost ?? 0), 2);
                        $product->old_price = round($productPricing->compare_price + ($extraPrintCost ?? 0), 2);

                        if ($productPricing->applied_test_price != 0) {
                            $product->r_price = round($productPricing->applied_test_price, 2);
                            $product->r_old_price = round($productPricing->applied_compare_test_price, 2);
                        }

                        $replaceVariant = $mappedVariants->map(function ($variant) use ($product, $productPricing, $extraPrintCost) {
                            $variant = (array)$variant;
                            $variant['adjust_price'] = $product->pricing_mode === PricingModeEnum::ADJUST_PRICE ? ceil($variant['adjust_price']) : 0;
                            $variant['price'] = round($productPricing->price + $variant['adjust_price'] + ($extraPrintCost ?? 0), 2);
                            $variant['old_price'] = round($productPricing->compare_price + $variant['adjust_price'] + ($extraPrintCost ?? 0), 2);
                            if ($productPricing->applied_test_price != 0) {
                                $variant['r_price'] = round($productPricing->applied_test_price + $variant['adjust_price'], 2);
                                $variant['r_old_price'] = round($productPricing->applied_compare_test_price + $variant['adjust_price'], 2);
                            }
                            return $variant;
                        });
                        $campaignVariantCollection = $campaignVariantCollection->replace($replaceVariant);
                    } elseif ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                        $productVariantCollection = collect();
                        $outOfStockVariants = $mappedVariants->where('out_of_stock', 1);
                        $options = json_decode($product->options, true) ?? [];
                        if ($product->full_printed === ProductPrintType::HANDMADE && !empty($options['custom_options'])) {
                            unset($options['custom_options']);
                        }
                        if ($product->full_printed === ProductPrintType::HANDMADE && !empty($options['common_options'])) {
                            unset($options['common_options']);
                        }
                        $generateVariants = generateVariantKeysByProductOptions($options, true);
                        foreach ($generateVariants as $newVariant) {
                            $variantPricing = $productPricing->filter(function ($savedPrice) use ($newVariant) {
                                $color = !empty($newVariant['color']) ? str_replace(' ', '_', $newVariant['color']) : null;
                                $variant = !empty($color) ? str_replace($color, 'white', $newVariant['variant_key']) : $newVariant['variant_key'];
                                return $savedPrice->variant_key == $variant;
                            })->first();
                            if ($variantPricing) {
                                $variantPricing = $variantPricing->toArray();
                                $variantPricing['applied_test_price'] = $variantPricing['test_price'] != 0 ? $variantPricing['price'] + $variantPricing['test_price'] : 0;
                                $variantPricing['applied_compare_test_price'] = $variantPricing['test_price'] != 0 ? $variantPricing['compare_price'] + $variantPricing['test_price'] + $variantPricing['test_price'] : 0;

                                if ($campaign->currency_code !== $variantPricing['currency_code']) {
                                    $variantPricing['price'] = round(($variantPricing['price'] + ($extraPrintCost ?? 0)) / $pricingCurrency->rate * $campaignCurrency->rate, 2);
                                    $variantPricing['compare_price'] = round($variantPricing['compare_price'] + ($extraPrintCost ?? 0) / $pricingCurrency->rate * $campaignCurrency->rate, 2);
                                    $variantPricing['applied_test_price'] = round($variantPricing['applied_test_price'] / $pricingCurrency->rate * $campaignCurrency->rate, 2);
                                    $variantPricing['applied_compare_test_price'] = round($variantPricing['applied_compare_test_price'] / $pricingCurrency->rate * $campaignCurrency->rate, 2);
                                }

                                $oldVariant = (array)$mappedVariants->firstWhere('variant_key', $newVariant['variant_key']);
                                if ($setPricingForProductVariant) {
                                    $newCollectVariant = $mappedVariants->map(function ($variant) use ($productPricing, $outOfStockVariants, $oldVariant, $product, $extraPrintCost) {
                                        $variant = (array)$variant;
                                        $outOfStockVariantsArr = $outOfStockVariants->toArray();
                                        $colorPosition = getPositionOfKeyInOptions('color', json_decode($product->options, true) ?? []);
                                        $explode = explode('-', $variant['variant_key']);
                                        if (Arr::get($explode, $colorPosition) && Arr::get($explode, $colorPosition) !== 'white') {
                                            $explode[$colorPosition] = 'white';
                                        }

                                        $pricingData = $productPricing->where('variant_key', implode('-', $explode))->first();

                                        if (isset($pricingData)) {
                                            $variant['price'] = round($pricingData->price + ($extraPrintCost ?? 0), 2);
                                            $variant['old_price'] = round($pricingData->compare_price + ($extraPrintCost ?? 0), 2);
                                            $mappedVariantOutOfStock = array_filter($outOfStockVariantsArr, function ($q) use ($variant) {
                                                return $q['variant_key'] == $variant['variant_key'] && $q['location_code'] == $variant['location_code'];
                                            });
                                            $variant['out_of_stock'] = !empty($mappedVariantOutOfStock) ? 1 : 0;
                                            $variant['adjust_price'] = !empty($oldVariant) ? $oldVariant['adjust_price'] : 0;
                                        }

                                        return $variant;
                                    });
                                    $productVariantCollection = $newCollectVariant;
                                } else {
                                    $newCollectVariant = [
                                        'product_id' => $product->id,
                                        'variant_key' => $newVariant['variant_key'],
                                        'out_of_stock' => $outOfStockVariants->contains('variant_key', $newVariant['variant_key']) ? 1 : 0,
                                        'adjust_price' => !empty($oldVariant) ? $oldVariant['adjust_price'] : 0,
                                        'price' => $variantPricing['price'] + ($extraPrintCost ?? 0),
                                        'old_price' => $variantPricing['compare_price'] + ($extraPrintCost ?? 0),
                                    ];

                                    if ($variantPricing['applied_test_price'] != 0) {
                                        $newCollectVariant['r_price'] = $variantPricing['applied_test_price'];
                                        $newCollectVariant['r_old_price'] = $variantPricing['applied_compare_test_price'];
                                    }
                                    $productVariantCollection->push($newCollectVariant);
                                }
                            }
                        }
                        if ($productVariantCollection->isNotEmpty()) {
                            $campaignVariantCollection->forget($mappedVariants->keys()->toArray());
                            $campaignVariantCollection = $campaignVariantCollection->merge($productVariantCollection);

                            $defaultPricing = $productVariantCollection->sortBy('price')->first();
                            $product->price = $defaultPricing['price'] + ($extraPrintCost ?? 0);
                            $product->old_price = ($defaultPricing['old_price'] ?? 0) + ($extraPrintCost ?? 0);

                            if (isset($defaultPricing['r_price']) && $defaultPricing['r_price'] != 0) {
                                $product->r_price = $defaultPricing['r_price'];
                                $product->r_old_price = $defaultPricing['r_old_price'];
                            }
                        }
                    }
                }
                unset($product->extra_print_cost);
            }

            $campaign->variants = $campaignVariantCollection->toArray();
        }
    }

    /**
     * @param Request $request
     * @param $productId
     * @return JsonResponse
     * @throws \JsonException
     */
    public function getCampaignProductVariant(Request $request, $productId): JsonResponse
    {
        $sellerId = $request->get('seller_id') ?? currentStoreInfo()?->seller_id;
        $seller = User::query()->find($sellerId);
        $campaignProduct = Product::query()
            ->onSellerConnection($seller)
            ->select([
                'id',
                'template_id',
                'campaign_id',
                'pricing_mode',
                'currency_code',
                'price',
                'old_price',
                'options',
                'full_printed',
                'market_location'
            ])
            ->with([
                'campaign',
                'templateVariants' => function ($query) {
                    $query->on(config('database.default'));
                }
            ])
            ->where('id', $productId)
            ->whereIn('product_type', [ProductType::PRODUCT, ProductType::FULFILL_PRODUCT, ProductType::PRODUCT_TEMPLATE])
            ->first();
        if (empty($campaignProduct)) {
            return $this->errorResponse('Campaign product not found.');
        }
        $campaignTemplate = $campaignProduct->template_id;
        $pricingMode = $campaignProduct->pricing_mode;
        if ($pricingMode === PricingModeEnum::CUSTOM_PRICE) {
            $campaignTemplate = $campaignProduct->id;
        }
        if (!isset($campaignProduct->campaign, $campaignProduct->templateVariants)) {
            return $this->errorResponse('Campaign or template variants not found for product.');
        }

        $campaign = $campaignProduct->campaign;
        $campaignProduct->templateVariants->each(function ($variant) use ($productId) {
            $variant->product_id = (int)$productId;
        });
        $campaign->variants = $campaignProduct->templateVariants->toArray();
        $campaign->products = collect();
        $campaign->products[] = $campaignProduct;
        $variants = $this->getVariantsOfCampaign($campaign, $campaignTemplate, $pricingMode === PricingModeEnum::CUSTOM_PRICE);
        unset($campaign->variants);
        $campaign->variants = $variants;
        self::mappingCorrectPricing($campaign, true);
        $variantsResponse = $campaign->variants;
        return $this->successResponse($variantsResponse);
    }

    /**
     * @param $variantId
     * @return JsonResponse
     */
    public function getCurrentVariant($variantId): JsonResponse
    {
        $productVariants = ProductVariant::query()
            ->select([
                'product_id',
                'variant_key',
                'base_cost',
                'location_code',
                'adjust_price',
                'price',
                'sku'
            ])
            ->where('product_id', $variantId)
            ->get();

        return $this->successResponse($productVariants);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function checkCampaignExists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => [
                'required',
                "regex:/\/?(p|o)\/(\d+)\/(\w+).(jpg|png|jpeg)$/",
            ]
        ]);
        $result = [
            'can_delete' => false,
        ];
        if ($validator->fails()) {
            return $this->errorResponse(implode("\n", $validator->getMessageBag()->all()), 400, customData: $result);
        }
        $campaignId = 0;
        $path = $request->get('path');
        if (preg_match("#^/?p/(\d+)/(\w+).(jpg|png|jpeg)$#", $path, $matches)) {
            $campaignId = (int) $matches[1];
        }
        if (empty($campaignId)) {
            return $this->successResponse($result, 'Skip check exists');
        }
        $connections = array_keys(config('database.connections'));
        $connections = array_values(array_filter($connections, function ($connection) {
            $unwanted_suffixes = ['_us', '_sg', '_eu', '_seller'];
            if (!str_starts_with($connection, 'mysql')) {
                return false;
            }
            foreach ($unwanted_suffixes as $suffix) {
                if (str_ends_with($connection, $suffix)) {
                    return false;
                }
            }
            return true;
        }));
        $exists = false;
        foreach ($connections as $connection) {
            $exists = Campaign::query()->on($connection)->whereKey($campaignId)->exists();
            if ($exists) {
                $result['connection'] = $connection;
                $result['campaign_id'] = $campaignId;
                break;
            }
        }
        if (!$exists) {
            $result['can_delete'] = true;
            $result['campaign_id'] = $campaignId;
            return $this->errorResponse('Campaign not found', 404, customData: $result);
        }
        return $this->successResponse($result);
    }
}

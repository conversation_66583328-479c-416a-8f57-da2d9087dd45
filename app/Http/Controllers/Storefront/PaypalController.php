<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CurrencyEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\PaymentMethodEnum;
use App\Http\Controllers\Controller;
use App\Jobs\GeneralRegionOrderJob;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\PaymentGateway;
use App\Models\PaypalPartnerReferral;
use App\Services\OrderService;
use App\Services\PayPal;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use App\Traits\Encrypter;
use App\Traits\OrderDescription;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\OrderService\Models\RegionOrderProducts;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Services\RegionOrderService;

class PaypalController extends Controller
{
    use ApiResponse, OrderDescription, Encrypter;

    private $gateway;
    private $gatewayId;
    private $isSandbox = false;
    private $orderId = null;
    private $storeName = null;
    private $merchantId = null;
    private $failedReason = null;

    private $initGateway = false;

    private function initPaymentGateway($paymentGatewayId): void
    {
        // load config
        $gateway = PaymentGateway::query()
            ->where([
                'id' => $paymentGatewayId,
                'gateway' => PaymentMethodEnum::PAYPAL
            ])
            ->first(['config', 'encrypted', 'paypal_merchant_id']);

        if (is_null($gateway)) {
            logToDiscord('init Paypal failed: Cannot load config (gateway ID: ' . $paymentGatewayId . ' - order ID: ' . $this->orderId . ')', 'important');
            return;
        }

        if (is_null($gateway->paypal_merchant_id)) {
            $paypalConfig = self::safeLoadConfig($gateway);

            // convert to array
            try {
                $config = json_decode($paypalConfig, true, 512, JSON_THROW_ON_ERROR);
            } catch (\Throwable $e) {
                logToDiscord('Cannot decode Paypal config: ' . $e->getMessage() . ' - Gateway ID: ' . $paymentGatewayId . ' - Order ID: ' . $this->orderId, 'important');
                return;
            }

            if (!isset($config['clientId'], $config['secret'])) {
                logToDiscord('Paypal config is invalid: ' . $paypalConfig . ' - Gateway ID: ' . $paymentGatewayId . ' - Order ID: ' . $this->orderId, 'important');
                return;
            }
        } else {
            // PayPal: Complete Payment Platform
            $clientId = config('services.paypal.client_id');
            $secret = config('services.paypal.secret');
            $testMode = config('services.paypal.test_mode');

            if (empty($clientId) || empty($secret)) {
                logToDiscord('Master Paypal config was not set.', 'important');
                return;
            }

            $bnCode = config('services.paypal.bn_code');

            $config = [
                'clientId' => $clientId,
                'secret' => $secret,
                'testMode' => $testMode,
                'bnCode' => $bnCode,
            ];

            $this->merchantId = $gateway->paypal_merchant_id;
        }

        if (!isset($config['testMode']) && !app()->isProduction()) {
            $config['testMode'] = true;
        }

        if (isset($config['testMode'])) {
            $this->isSandbox = (bool)$config['testMode'];
        }

        $this->gatewayId = $paymentGatewayId;
        $this->gateway = new PayPal($config, $this->merchantId);

        if ($this->gateway->getAccessToken()) {
            $this->initGateway = true;
        }
    }

    public function createOrder(Request $request): JsonResponse
    {
        $postData = $request->validate(['order_token' => 'bail|required|string', 'master' => 'nullable|integer']);
        $orderToken = $postData['order_token'];
        $orderRegion = 'sg';
        if (!data_get($postData, 'master', false)) {
            $orderRegion = RegionOrderService::getAppRegion($orderToken);
        }
        $order = $this->getOrderByToken($orderToken, $orderRegion);
        if (!$order) {
            return $this->errorResponse('Order not found.');
        }

        $this->initPaymentGateway($order->payment_gateway_id);

        if (!$this->initGateway) {
            return $this->errorResponse('Cannot initialize gateway.');
        }

        /* @var PayPal $gateway */
        $gateway = $this->gateway;
        $params = $this->buildParams($order, $request);
        $purchaseUnits = $this->getPurchaseUnits($order, $params['description']);
        $isReturnOrderId = $request->boolean('return_order_id');
        if ($order instanceof RegionOrders) {
            $order->setPaymentSummary($params['description']);
        }
        try {
            $result = $gateway->createOrder(
                $purchaseUnits,
                $params['returnUrl'],
                $params['cancelUrl'],
                $order->store_name,
                'en-US',
                $isReturnOrderId
            );
            if ($order->isDirty()) {
                $order->save();
            }
            graylogInfo('Paypal create payment for Order Id: ' . $order->id, [
                'category' => 'checkout_create_order',
                'order_id' => $order->id,
                'order_data' => json_encode($order, JSON_THROW_ON_ERROR),
                'paypal_params' => json_encode($params, JSON_THROW_ON_ERROR),
                'paypal_result' => json_encode($result, JSON_THROW_ON_ERROR),
                'payment_gateway_id' => $order->payment_gateway_id,
                'request_data' => json_encode($request->all(), JSON_THROW_ON_ERROR),
            ]);
        } catch (\Throwable $e) {
            $this->failedReason = $e->getMessage();
            logToDiscord('Paypal Error: ' . $this->failedReason . ' (order ID: ' . $order->id . ')' . ' - Gateway ID: ' . $order->payment_gateway_id, 'important');
            silent(function () use ($order, $params, $request) {
                graylogError('Paypal create payment failed. ' . $this->failedReason . '. Order Id: ' . $order->id, [
                    'category' => 'checkout_create_order',
                    'order_id' => $order->id,
                    'order_data' => $order ? json_encode($order, JSON_THROW_ON_ERROR) : '',
                    'paypal_params' => !empty($params) ? json_encode($params, JSON_THROW_ON_ERROR) : '',
                    'payment_gateway_id' => $order->payment_gateway_id,
                    'request_data' => json_encode($request->all(), JSON_THROW_ON_ERROR),
                ]);
            });
            if (!app()->isProduction()) {
                return $this->errorResponse($this->failedReason);
            }

            return $this->errorResponse();
        }

        if (empty($result)) {
            graylogError('Paypal create payment failed. Empty result after create. Order Id: ' . $order->id, [
                'category' => 'checkout_create_order_paypal_empty',
                'order_id' => $order->id,
                'order_data' => $order ? json_encode($order, JSON_THROW_ON_ERROR) : '',
                'paypal_params' => !empty($params) ? json_encode($params, JSON_THROW_ON_ERROR) : '',
                'payment_gateway_id' => $order->payment_gateway_id,
                'request_data' => json_encode($request->all(), JSON_THROW_ON_ERROR),
                'purchase_unit' => $purchaseUnits,
                'return_url' => $params['returnUrl'],
                'cancel_url' => $params['cancelUrl'],
                'is_return_order_id' => $isReturnOrderId
            ]);
            return $this->errorResponse('Cannot create order.');
        }

        return $this->successResponse($result);
    }

    private static function formatCurrency(?float $value, float $fallback = 0): string
    {
        return number_format($value ?: $fallback, 2, '.', '');
    }

    public function getPurchaseUnits(Order|RegionOrders $order, string $description): array
    {
        $currencyCode = OrderService::checkAcceptableCurrency($order->currency_code, PaymentMethodEnum::PAYPAL);
        $currencyRate = $order->currency_rate;

        $items = [];
        $manualItemTotal = 0;

        foreach ($order->order_products as $product) {
            $unitAmount = self::getAmountByCurrency(
                $product->price, /* @phpstan-ignore-line */
                $currencyCode,
                $currencyRate
            );

            $items[] = [
                'name' => $product->product_name, /* @phpstan-ignore-line */
                'unit_amount' => [
                    'currency_code' => $currencyCode,
                    'value' => $unitAmount,
                ],
                'quantity' => $product->quantity,/* @phpstan-ignore-line */
            ];

            $manualItemTotal += $unitAmount * $product->quantity;
        }

        // Calculate total amount
        $itemTotal = self::getAmountByCurrency($order->total_product_amount, $currencyCode, $currencyRate);

        // fix: ITEM_TOTAL_MISMATCH
        if ((float)$manualItemTotal !== (float)$itemTotal) {
            $itemTotal = (string)$manualItemTotal;
        }

        $shippingTotal = self::getAmountByCurrency($order->total_shipping_amount, $currencyCode, $currencyRate);
        $taxTotal = self::getAmountByCurrency($order->total_tax_amount, $currencyCode, $currencyRate);
        $discount = self::getAmountByCurrency($order->total_discount, $currencyCode, $currencyRate);
        $insurance = self::getAmountByCurrency($order->insurance_fee, $currencyCode, $currencyRate);
        $tip = self::getAmountByCurrency($order->tip_amount, $currencyCode, $currencyRate);

        $totalValue = bcadd(bcadd(bcadd(bcadd($itemTotal, $shippingTotal, 2), $taxTotal, 2), $insurance, 2), $tip, 2);
        $totalValue = bcsub($totalValue, $discount, 2); // Subtract any discount

        // limit description length to 127 characters
        $description = Str::limit($description, 124); // 3 characters for '...'

        return [
            [
                'reference_id' => $order->id,
                'invoice_id' => $order->id,
                'amount' => [
                    'currency_code' => $currencyCode,
                    'value' => $totalValue,
                    'breakdown' => [
                        'item_total' => [
                            'currency_code' => $currencyCode,
                            'value' => $itemTotal,
                        ],
                        'shipping' => [
                            'currency_code' => $currencyCode,
                            'value' => $shippingTotal,
                        ],
                        'tax_total' => [
                            'currency_code' => $currencyCode,
                            'value' => $taxTotal,
                        ],
                        'discount' => [
                            'currency_code' => $currencyCode,
                            'value' => $discount,
                        ],
                        'insurance' => [
                            'currency_code' => $currencyCode,
                            'value' => $insurance,
                        ],
                        'handling' => [
                            'currency_code' => $currencyCode,
                            'value' => $tip,
                        ],
                    ],
                ],
                'items' => $items,
                'shipping' => [
                    'address' => [
                        'address_line_1' => $order->address,
                        'address_line_2' => $order->address_2,
                        'admin_area_1' => $order->state,
                        'admin_area_2' => $order->city,
                        'postal_code' => $order->postcode,
                        'country_code' => $order->country,
                    ],
                ],
                'description' => $description,
                'payee' => ['merchant_id' => $this->merchantId]
            ],
        ];
    }

    /**
     * @param Request $request
     * @param $orderToken
     * @return JsonResponse
     */
    public function verifyPurchase(Request $request, $orderToken): JsonResponse
    {
        $callbackUrl = $request->fullUrl();
        $transactionReference = $request->query('transactionReference');
        $orderRegion = $request->query('region', RegionOrderService::getAppRegion());
        $gateId = $request->query('gateId');
        $order = $this->getOrderByToken($orderToken, $orderRegion);

        if (!$order) {
            graylogInfo("Order not found", ['category' => 'verify_paypal', 'order_token' => $orderToken, 'callback_url' => $callbackUrl, 'region' => $orderRegion]);
            return $this->errorResponse('Order not found.');
        }

        if (!empty($gateId) && (int) $gateId !== (int) $order->payment_gateway_id) {
            $order->payment_gateway_id = $gateId;
        }

        graylogInfo("Verify Paypal purchase - Order ID: $order->id - Gateway ID: $order->payment_gateway_id", ['category' => 'verify_paypal', 'order_id' => $order->id, 'callback_url' => $callbackUrl, 'region' => $orderRegion]);
        GeneralRegionOrderJob::dispatch(
            OrderService::class,
            'storePaymentGatewaySimpleLogHandler',
            [PaymentMethodEnum::PAYPAL, 'CALLBACK.VERIFY_PURCHASE', $order->id, $order->payment_gateway_id, $request->all()]
        );
        $this->initPaymentGateway($order->payment_gateway_id);

        if (!$this->initGateway) {
            return $this->errorResponse('Cannot initialize gateway.');
        }

        $paypalOrderId = $request->query('token');

        if (is_null($paypalOrderId)) {
            return $this->errorResponse('Invalid token parameter.');
        }

        // capture and verify payment
        $gateway = $this->gateway;

        try {
            /* @var PayPal $gateway */

            // check if the order is created and not captured
            $orderDetail = $gateway->getOrder($paypalOrderId);

            if (data_get($orderDetail, 'intent') !== 'CAPTURE') {
                return $this->errorResponse('Invalid intent.');
            }

            // check order status
            $status = data_get($orderDetail, 'status');

            if (!in_array($status, ['APPROVED', 'CREATED', 'COMPLETED'], true)) {
                return $this->errorResponse('Invalid status.');
            }

            if ($status === 'COMPLETED') {
                $captureResponse = data_get($orderDetail, 'purchase_units.0.payments.captures.0');
                $transactionReference = data_get($captureResponse, 'id', $paypalOrderId);
            } else {
                $captureResponse = $gateway->capturePayment($paypalOrderId);
                $transactionReference = data_get($captureResponse, 'purchase_units.0.payments.captures.0.id', $paypalOrderId);
            }
            $status = data_get($captureResponse, 'status');

            GeneralRegionOrderJob::dispatch(
                PaypalController::class,
                'storePaymentGatewayLogHandler',
                [$request->all(), $captureResponse, $orderDetail, $order]
            );

            if ($status === 'COMPLETED') {
                if (!empty($order->region) && $orderRegion !== config('app.region') && config('app.region') !== config('app.region_master')) {
                    GeneralRegionOrderJob::dispatch(
                        OrderService::class,
                        'paymentCompleted',
                        [$order, $transactionReference]
                    );
                } else {
                    $order->paymentCompleted($order->total_amount, $transactionReference, false, null, null, $order->payment_gateway_id);
                }
                return $this->successResponse();
            }

            $statusDetailReason = data_get($orderDetail, 'status_details.reason');
            $pendingReview = $status === 'PENDING' && $statusDetailReason && strtoupper($statusDetailReason) === 'PENDING_REVIEW';

            if ($pendingReview) {
                if (!empty($order->region) && $orderRegion !== config('app.region') && config('app.region') !== config('app.region_master')) {
                    GeneralRegionOrderJob::dispatch(
                        OrderService::class,
                        'paymentPendingReview',
                        [$order, $transactionReference]
                    );
                } else {
                    $order->paymentPendingReview($transactionReference);
                }
                return $this->successResponse();
            }
            if (!empty($order->region) && $orderRegion !== config('app.region') && config('app.region') !== config('app.region_master')) {
                GeneralRegionOrderJob::dispatch(
                    OrderService::class,
                    'paymentFailed',
                    [$order, $transactionReference, $statusDetailReason, $order->payment_gateway_id]
                );
            } else {
                $order->paymentFailed($statusDetailReason, $transactionReference, $order->payment_gateway_id);
            }
            return $this->errorResponse();
        } catch (\Throwable $e) {
            $this->failedReason = $e->getMessage();
            graylogError($this->failedReason . '. Order Token: ' . $order->access_token, [
                'category' => 'paypal_webhook_error',
                'order_id' => $order->id,
                'trxId' => $paypalOrderId,
                'payment_gateway_id' => $order->payment_gateway_id,
                'error' => $e,
                'request' => $request->all(),
                'order_region' => $orderRegion,
                'order_token' => $orderToken,
                'current_region' => config('app.region')
            ]);
            $order->paymentFailed($this->failedReason, $transactionReference);

            if (!app()->isProduction()) {
                return $this->errorResponse($this->failedReason);
            }

            return $this->errorResponse();
        }
    }

    /**
     * @param $token
     * @param $orderRegion
     * @return Order|RegionOrders|null
     */
    private function getOrderByToken($token, $orderRegion)
    {
        $instance = RegionOrderService::regionOrderModelInstance($token, $orderRegion);
        /** @var Order|RegionOrders $orderQuery */
        $orderQuery = data_get($instance, 'order');
        /** @var OrderProduct|RegionOrderProducts $orderProductQuery */
        $orderProductQuery = data_get($instance, 'order_products');
        $order = $orderQuery->firstWhere('access_token', $token);
        if (isset($order)) {
            $this->orderId = $order->id;
            $this->storeName = $order->store_name;
            $orderProduct = $orderProductQuery->select([
                'order_id',
                'product_name',
                'options',
                'quantity',
                'price'
            ])->where('order_id', $order->id)->get();
            $order->setRelation('order_products', $orderProduct);
        }
        return $order;
    }

    /**
     * Build params for Omnipay
     *
     * @param $order
     * @param Request $request
     * @return array|null
     */
    private function buildParams($order, Request $request): ?array
    {
        if (is_null($order)) {
            return null;
        }

        $urlEncodedToken = urlencode($order->access_token);

        $domain = StoreService::getDomain();
        $storeInfo = StoreService::getCurrentStoreInfo();
        $orderRegion = RegionOrderService::getAppRegion($order->access_token, $order->region);

        if (!str_contains($domain, '.')) {
            $domain .= '.' . getStoreBaseDomain();
        }

        $baseUrl = 'https://' . $domain . '/';

        $cartKey = $request->get('cartKey');
        $payment_gateway_id = $order->payment_gateway_id ?? null;
        $cartKeyParam = $cartKey ? "&cartKey={$cartKey}" : '';
        $gateId = $payment_gateway_id ? "&gateId={$payment_gateway_id}" : '';

        if ($order->store_domain && $request->has('safe_redirect')) {
            $callbackDomain = urlencode(base64_encode($order->store_domain));
            $origin = self::getRequestOrigin($request);
            $callbackUrl = $origin . '/payment-callback.html?orderToken=' . $urlEncodedToken . '&ref=' . $callbackDomain;
            $returnUrl = $callbackUrl . '&type=return' . $cartKeyParam . $gateId;
            $cancelUrl = $callbackUrl . '&type=cancel';
        } else {
            $returnUrl = $baseUrl . 'checkout/paypal?orderToken=' . $urlEncodedToken . $cartKeyParam . $gateId;
            $cancelUrl = $baseUrl . 'checkout/' . $urlEncodedToken;
        }

        $returnUrl .= '&region=' . $orderRegion;
        if ($orderRegion === 'sg') {
            graylogInfo('Paypal build params in SG: ' . $order->access_token, [
                'category' => 'paypal_build_params_sg',
                'order_id' => empty($order?->id) ? $order->id : '',
                'order_region' => $orderRegion,
                'return_url' => $returnUrl,
                'store_info' => $storeInfo,
                'request' => $request->all(),
                'order' => $order,
                'current_server' => config('app.region')
            ]);
        }

        //  check if acceptedCurrency
        $currencyCode = OrderService::checkAcceptableCurrency($order->currency_code, PaymentMethodEnum::PAYPAL);
        // if not USD, multi to rate
        $amount = OrderService::getAmountByCurrency($order->total_amount, $currencyCode, $order->currency_rate);

        try {
            $description = self::getOrderDescription($order);

            if (Str::startsWith($description, 'Order null')) {
                logToDiscord('PaypalController - description error. Order : ' . json_encode($order), 'important');
            }
        } catch (\Throwable $e) {
            $description = $order->access_token;
        }

        return [
            'amount' => roundAmountPayment($amount),
            'currency' => $currencyCode,
            'issuer' => generateCustomerId($order->customer_email),
            'description' => $description,
            'transactionId' => $order->order_number,
            'clientIp' => getIp($request),
            'returnUrl' => $returnUrl,
            'cancelUrl' => $cancelUrl,
            'brandName' => $this->storeName, // TODO: don't work with PayPal_Rest
        ];
    }

    private static function getAmountByCurrency($amount, $currencyCode, $currencyRate)
    {
        if ($amount === null) {
            return '0';
        }

        $amount = OrderService::getAmountByCurrency($amount, $currencyCode, $currencyRate);
        return self::formatCurrency($amount);
    }

    private static function getRequestOrigin(Request $request): string
    {
        if ($request->has('origin')) {
            return 'https://' . $request->post('origin');
        }

        $ref = $request->headers->get('referer');
        $origin = $request->headers->get('origin', $ref);

        if ($origin) {
            return 'https://' . parse_url($origin, PHP_URL_HOST);
        }

        return 'https://senprints.com';
    }

    public function updateTracking(Order $order, array $tracking): void
    {
        // fix: TRACKING_NUMBER_IS_MISSING
        if (!isset($tracking['tracking_code'])) {
            graylogError('Tracking code is missing', [
                'category' => 'update_tracking_paypal',
                'order_id' => $order->id
            ]);
            return;
        }

        $this->orderId = $order->id;
        $this->initPaymentGateway($order->payment_gateway_id);

        if (!$this->initGateway) {
            return;
        }

        $tracker = [
            'transaction_id' => $order->transaction_id,
            'tracking_number' => $tracking['tracking_code'],
            'status' => 'SHIPPED'
        ];

        $carrier = self::mapCarrier($tracking['shipping_carrier']);

        // If the following global or country-specific tables do not contain your shipping carrier
        // set carrier to OTHER and set carrier name in carrier_name_other.
        // https://developer.paypal.com/docs/tracking/reference/carriers/
        if (self::isSupportedCarrier($carrier)) {
            $tracker['carrier'] = $carrier;
        } else {
            $tracker['carrier'] = 'OTHER';
            $tracker['carrier_name_other'] = $carrier;
        }

        $items = [];
        $orderProducts = OrderProduct::query()
            ->select('product_name', 'quantity', 'sku', 'thumb_url')
            ->where('order_id', $order->id)
            ->get();

        if ($orderProducts->isEmpty()) {
            graylogError('Order products is empty', [
                'category' => 'update_tracking_paypal',
                'order_id' => $order->id
            ]);
            return;
        }

        foreach ($orderProducts as $orderProduct) {
            $items[] = [
                'quantity' => $orderProduct->quantity,
                'sku' => $orderProduct->sku,
                'name' => $orderProduct->product_name,
                'image_url' => $orderProduct->thumb_url,
            ];
        }

        $gateway = $this->gateway;

        /* @var PayPal $gateway */
        try {
            $gateway->addTracking(
                $tracker['transaction_id'],
                $tracker['tracking_number'],
                $tracker['carrier'],
                false,
                $items
            );
        } catch (\Throwable $e) {
            $message = 'Update Tracking paypal failed'
                . "\nException: " . $e->getMessage()
                . "\nOrder Id: " . $order->id
                . "\nTracker: " . json_encode($tracker);
            logToDiscord(
                $message,
                'error_checkout'
            );
            graylogError('Update Tracking paypal failed', [
                'category' => 'update_tracking_paypal',
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'tracker' => $tracker
            ]);
        }
    }

    private static function mapCarrier($carrier): ?string
    {
        if (empty($carrier)) {
            return 'Unknown';
        }

        $key = strtoupper($carrier);
        $map = config('paypal.carrier_map');

        try {
            $result = array_key_exists($key, $map) ? $map[$key] : $carrier;

            return $result ?: $carrier;
        } catch (\Exception $e) {
            return $carrier;
        }
    }

    private static function isSupportedCarrier($carrier): bool
    {
        $carriers = config('paypal.carriers');
        return in_array(strtoupper($carrier), $carriers);
    }

    /**
     * @param Order $order
     * @param $amount
     * @param int $fullRefund
     * @return array
     */
    public function refundOrderToGateway(Order $order, $amount = null, int $fullRefund = 0): array
    {
        if ($amount && (float)$amount > (float)$order->total_amount) {
            return [
                'success' => false,
                'message' => 'Refund amount cannot be greater than total amount',
            ];
        }

        $refundAmount = $amount ?? $order->total_amount;
        if ($order->currency_code === CurrencyEnum::EUR) {
            $refundAmount = $refundAmount * $order->currency_rate;
        }
        $refundAmount = number_format($refundAmount, 2);

        $transactionId = $order->transaction_id;

        $paymentGatewayId = $order->payment_gateway_id;
        try {
            $this->initPaymentGateway($paymentGatewayId);

            /* @var PayPal $gateway */
            $gateway = $this->gateway;
            $response = $gateway->refund($transactionId, $refundAmount, $order->currency_code, '', '', [], $fullRefund, true);
            graylogInfo('Refund to gateway transaction id: ' . $transactionId, [
                'category' => 'refund_to_gateway',
                'order_id' => $order->id,
                'user_id' => optional(currentUser())->getUserId(),
                'transaction_id' => $order->transaction_id,
                'payment_method' => 'paypal',
                'payload' => json_encode(array(
                    'refund_amount' => $refundAmount,
                    'currency_code' => $order->currency_code,
                    'full_refund' => $fullRefund,
                    'payment_gateway_id' => $paymentGatewayId,
                    'response' => json_encode($response, JSON_THROW_ON_ERROR),
                ), JSON_THROW_ON_ERROR)
            ]);
            return [
                'success' => !empty($response) && is_array($response) && $response['status'] === 'COMPLETED',
                'message' => 'Refund success',
            ];
        } catch (\Throwable $throwable) {
            return [
                'success' => false,
                'message' => $throwable->getMessage(),
            ];
        }
    }

    /**
     * Handle POST request from PayPal
     */
    public function handleWebhook(Request $request, $gateId = null): JsonResponse
    {
        $event = $request->all();
        if (!$request->has('event_type')) {
            return response()->json([
                'success' => false,
                'message' => 'event_type is missing'
            ]);
        }

        $eventType = $request->json('event_type');
        $orderNumber = $request->json('resource.invoice_number');
        $orderId = $request->json('resource.invoice_id');
        $transactionId = $request->json('resource.id');
        $order = null;
        if (!empty($orderNumber)) {
            $order = Order::query()->where('order_number', $orderNumber)->first();
            $orderId = optional($order)->id;
            if (empty($gateId)) {
                $gateId = optional($order)->payment_gateway_id;
            }
        }
        GeneralRegionOrderJob::dispatch(
            OrderService::class,
            'storePaymentGatewaySimpleLogHandler',
            [PaymentMethodEnum::PAYPAL, 'WEBHOOK.' . $eventType, $orderId, $gateId, $event]
        );
        $context = [
            'category' => 'paypal_webhook',
            'order_id' => $orderId,
            'payment_obj' => $request->json('resource'),
            'event_type' => $eventType,
            'gateway_id' => $gateId,
        ];
        graylogInfo(json_encode($event), $context);
        switch ($eventType) {
            case 'CUSTOMER.DISPUTE.CREATED':
                $message = ':warning: New dispute created for order #' . $request->json('resource.invoice_number', 'Unknown') . ' | Reason: ' . $request->json('resource.reason', 'Unknown') . ' | Dispute ID: ' . $request->json('resource.dispute_id', 'Unknown') . ' | Payment ID: ' . $gateId;
                logToDiscord($message, 'paypal_dispute');
                break;

            case 'PAYMENT.SALE.COMPLETED':
            case 'PAYMENT.CAPTURE.COMPLETED':
                if (empty($orderNumber) || empty($transactionId)) {
                    graylogError('Invoice number or transaction ID is missing', [
                        'category' => 'paypal_webhook',
                        'order_number' => $orderNumber,
                        'transaction_id' => $transactionId
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'invoice_number is missing'
                    ]);
                }

                // check if we have this order and payment is failed
                if ($order && in_array($order->payment_status, [OrderPaymentStatus::UNPAID, OrderPaymentStatus::PENDING, OrderPaymentStatus::FAILED], true)) {
                    $order->paymentCompleted($order->total_amount, $transactionId, false, null, null, $gateId);
                    graylogInfo('Payment completed via webhook', [
                        'category' => 'paypal_webhook',
                        'event_type' => $eventType,
                        'order_number' => $orderNumber
                    ]);
                }
                break;

            case 'MERCHANT.ONBOARDING.COMPLETED':
                // merchant_id is the tracking_id
                // ref: https://developer.paypal.com/docs/multiparty/seller-onboarding/before-payment/#link-redirectsellertoareturnurl
                $merchantId = $request->json('resource.merchant_id');

                if ($merchantId) {
                    PaypalPartnerReferral::query()
                        ->where('tracking_id', $merchantId)
                        ->update(['status' => 'completed']);
                }

                break;

            case 'MERCHANT.PARTNER-CONSENT.REVOKED':
                $merchantId = $request->json('resource.merchant_id');

                if ($merchantId) {
                    PaypalPartnerReferral::query()
                        ->where('tracking_id', $merchantId)
                        ->update(['status' => 'revoked']);
                }

                break;
        }

        return response()->json(['success' => true]);
    }

    /**
     * Verify capture transaction payment
     *
     * @param $paymentGatewayId
     * @param $paypalOrderId
     * @return false
     */
    public function verifyCapture($paymentGatewayId, $paypalOrderId)
    {
        try {
            $this->initPaymentGateway($paymentGatewayId);
            return $this->gateway->showCaptureDetail($paypalOrderId);
        } catch (\Throwable $e) {
            $message = 'Verify capture PayPal failed.'
                . "\nException: " . $e->getMessage()
                . "\nTransaction ID: " . $paypalOrderId
                . "\nPayment Gateway Id: " . $paymentGatewayId;
            logToDiscord($message, 'error_checkout');
            return false;
        }
    }

    /**
     * Verify order transaction payment
     *
     * @param $paymentGatewayId
     * @param $paypalOrderId
     * @param Order|null $order
     * @param bool $forceUpdateTransactionId
     * @return array|false|mixed
     */
    public function verifyOrder($paymentGatewayId, $paypalOrderId, Order $order = null, bool $forceUpdateTransactionId = true)
    {
        if (empty($paypalOrderId) || $paypalOrderId === 'null') {
            return false;
        }

        try {
            $this->initPaymentGateway($paymentGatewayId);
            $orderDetail = $this->gateway->getOrder($paypalOrderId);
            if (!empty($orderDetail) && $forceUpdateTransactionId) {
                $captureDetail = data_get($orderDetail, 'purchase_units.0.payments.captures.0');
                $transactionId = data_get($captureDetail, 'id');
                $actualCaptureId = $paypalOrderId;
                if (!empty($transactionId) && $transactionId !== 'null') {
                    $actualCaptureId = $transactionId;
                }
                if ($actualCaptureId !== $paypalOrderId) {
                    $orderId = data_get($captureDetail, 'invoice_id');
                    if ($orderId) {
                        $orderQuery = Order::query();
                        if ($order && $order->region && $order->access_token) {
                            $orderQuery = RegionOrderService::regionOrderModelInstance($order->access_token, $order->region)['order'];
                        }
                        $orderQuery->when($order->access_token, function ($q) use ($order) {
                            $q->where('access_token', $order->access_token);
                        }, function ($q) use ($orderId) {
                            $q->where('id', $orderId);
                        })->update([
                            'transaction_id' => $actualCaptureId,
                        ]);
                    }
                }
                return $captureDetail;
            }
            return $orderDetail;
        } catch (\Throwable $e) {
            return $this->verifyCapture($paymentGatewayId, $paypalOrderId);
        }
    }

    /**
     * @param Order $order
     * @return array
     */
    public function verifyOrderPurchase(Order $order)
    {
        if ($order->payment_status === OrderPaymentStatus::PAID || !in_array($order->status, [OrderStatus::PENDING, OrderStatus::PENDING_PAYMENT], true)) {
            return [
                'success' => false,
                'message' => 'Order is not pending or paid already.'
            ];
        }
        $transactionReference = $order->transaction_id;
        try {
            $orderDetail = $this->verifyOrder($order->payment_gateway_id, $transactionReference, $order);
            if (empty($orderDetail)) {
                return [
                    'success' => false,
                    'message' => 'Not found paypal order detail.'
                ];
            }
            $status = data_get($orderDetail, 'status');
            if ($status === 'COMPLETED') {
                $order->paymentCompleted($order->total_amount, $transactionReference, false, null, null, $order->payment_gateway_id);
                return [
                    'success' => true,
                    'message' => 'Order paid successfully.'
                ];
            }
            $statusDetailReason = data_get($orderDetail, 'status_details.reason');
            $pendingReview = $status === 'PENDING' && $statusDetailReason && strtoupper($statusDetailReason) === 'PENDING_REVIEW';
            if ($pendingReview) {
                $order->paymentPendingReview($transactionReference);
                return [
                    'success' => true,
                    'message' => 'Order is pending review.'
                ];
            }
            $order->paymentFailed('', $transactionReference);
            return [
                'success' => false,
                'message' => 'Order payment failed.'
            ];
        } catch (\Throwable $e) {
            $this->failedReason = $e->getMessage();
            $order->paymentFailed($this->failedReason, $transactionReference);
            return [
                'success' => false,
                'message' => $this->failedReason,
            ];
        }
    }

    /**
     * @param $request
     * @param $captureResponse
     * @param $orderDetail
     * @param $order
     * @return void
     * @throws \Exception
     */
    public static function storePaymentGatewayLogHandler($request, $captureResponse, $orderDetail, $order): void {
        try {
            $status = data_get($captureResponse, 'status');
            storePaymentGatewayWebhookLogs(PaymentMethodEnum::PAYPAL, 'VERIFY_PURCHASE.' . data_get($orderDetail, 'intent') . '.' . $status, $order->id, $order->payment_gateway_id, array(
                'request' => $request,
                'capture' => $captureResponse,
                'orderDetail' => $orderDetail,
            ));
        } catch (\Exception $e) {
            logException($e, 'storePaymentGatewayLogHandler');
        }
    }

    /**
     * Get order detail from capture
     *
     * @param $paymentGatewayId
     * @param $transactionId
     * @return mixed
     */
    public function getOrderDetailFromCapture($paymentGatewayId, $transactionId): mixed
    {
        try {
            $this->initPaymentGateway($paymentGatewayId);
            $captureDetail = $this->gateway->showCaptureDetail($transactionId);
            if ($captureDetail) {
                $paypalOrderId = data_get($captureDetail, 'supplementary_data.related_ids.order_id');
                if ($paypalOrderId) {
                    return $this->gateway->getOrder($paypalOrderId);
                }
                return $captureDetail;
            }
            return false;
        } catch (\Throwable $e) {
            $message = 'Get order detail from capture failed.'
                . "\nException: " . $e->getMessage()
                . "\nTransaction ID: " . $transactionId
                . "\nPayment Gateway Id: " . $paymentGatewayId;
            logToDiscord($message, 'error_checkout');
            return false;
        }
    }
}

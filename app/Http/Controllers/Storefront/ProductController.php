<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\DateRangeEnum;
use App\Enums\EventLogsTypeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\ProductType;
use App\Enums\ShippingMethodEnum;
use App\Enums\ShippingTime;
use App\Http\Controllers\Controller;
use App\Http\Requests\Storefront\Product\IndexRequest;
use App\Http\Requests\Storefront\Product\SimilarRequest;
use App\Http\Requests\Storefront\Product\StatsRequest;
use App\Jobs\Storefront\LogSearchKeywordJob;
use App\Models\Collection;
use App\Models\Elastic;
use App\Models\File;
use App\Models\Product as ProductModel;
use App\Models\ProductSizeGuide;
use App\Models\StoreCollection;
use App\Models\SystemConfig;
use App\Models\TempEventLog;
use App\Models\TrademarkList;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use App\Traits\Product;
use Illuminate\Database\Eloquent\HigherOrderBuilderProxy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request as RequestFacade;
use Illuminate\Support\Str;
use RuntimeException;
use Throwable;

class ProductController extends Controller
{
    use ApiResponse, ElasticClient, Product;

    public const NUMBER_PER_PAGE = 24;

    public function index(IndexRequest $request)
    {
        $input = $request->all();
        $store = StoreService::getCurrentStoreInfo();
        if (empty($store)) {
            return $this->errorResponse();
        }

        // Check for trademark keywords in search query (only for custom payment stores)
        if (!empty($input['s']) && !$store->custom_payment && $this->containsTrademark($input['s'])) {
            return $this->errorResponse('Search contains trademark keywords');
        }

        $dataCached = true;
        $cacheKey = CacheKeys::STOREFRONT_INDEX_PRODUCT_PREFIX . $store->id . md5(json_encode($input));
        $tags = [];

        if ($store->id) {
            $tags[] = CacheKeys::getStoreId($store->id);
        }

        $response = getDataByDefaultCache($cacheKey, $tags);

        if (is_null($response)) {
            $response = cacheAlt()->tags($tags)->remember($cacheKey, CacheKeys::CACHE_5m, function () use ($input, $store, &$dataCached) {
                $dataCached = false;
                $products = [];
                $bannerUrl = null;
                $this->logSearchQuery($input, $store);
                $elasticResponse = $this->elasticListingProduct($input, false, $store);
                if (empty($elasticResponse)) {
                    return [
                        'products' => $products,
                        'total' => 0,
                        'campaign_ids' => [],
                        'banner_url' => $bannerUrl,
                    ];
                }
                if (empty($elasticResponse['hits']['hits'])) {
                    return [
                        'products' => $products,
                        'total' => 0,
                        'campaign_ids' => [],
                        'banner_url' => $bannerUrl,
                    ];
                }
                $products = Arr::pluck($elasticResponse['hits']['hits'], '_source');
                $campaignIds = $this->getCampaignIds($products);
                if (isset($input['collection_slug'])) {
                    $bannerUrl = $this->getCollectionBanner($input['collection_slug'], $store->id);
                }

                $store->search_return_x_page === 1 ? $total = count($products) : $total = $elasticResponse['aggregations']['unique_design']['value'];
                return [
                    'products' => $products,
                    'total' => $total,
                    'campaign_ids' => $campaignIds,
                    'banner_url' => $bannerUrl,
                ];
            });
        }

        $products = $response['products'] ?? [];
        StoreService::getVariantOfProducts($products);
        $headers = generateHeadersCacheHTMLTagByCampaignIds($response['campaign_ids'] ?? []);
        $total = $response['total'] ?? 0;
        self::mappingCorrectPricing($products);
        $uri = preg_replace("/page=\d+/", '', RequestFacade::getRequestUri());
        $numberPerPage = Arr::get($input, 'limit', self::NUMBER_PER_PAGE);
        $currentPage = Arr::get($input, 'page');
        $currentPage = max($currentPage, 1);
        $maxPage = $store->random_popular ? 3 : 10;
        $currentPage = fmod($currentPage - 1, $maxPage) + 1;
        $result = new LengthAwarePaginator(
            $products,
            $total,
            $numberPerPage,
            $currentPage,
            [
                'path' => $uri,
            ]
        );
        if (!empty($response['banner_url'])) {
            $result = $result->toArray();
            $result['banner_url'] = $response['banner_url'];
        }
        $result = response()->json($result);
        return $result->withHeaders($headers)->withCached($dataCached);
    }

    private function logSearchQuery(&$input, $store): void
    {
        if (empty($input['s'])) {
            unset($input['s']);
            return;
        }

        try {
            LogSearchKeywordJob::dispatch($input, $store);
        } catch (Throwable $e) {
            //no logging
        }
    }

    public function getFilter(IndexRequest $request)
    {
        $input = $request->all();
        $store = StoreService::getCurrentStoreInfo();
        if (empty($store)) {
            return $this->errorResponse();
        }

        if (!$store->enable_search) {
            return $this->errorResponse('This feature is not available for current store');
        }
        $this->logSearchQuery($input, $store);

        $cacheKey = CacheKeys::STOREFRONT_FILTER_PRODUCT_PREFIX . $store->id . md5(json_encode($input));
        $data = getDataByDefaultCache($cacheKey);
        if (is_null($data)) {
            return cacheAlt()->remember($cacheKey, CacheKeys::CACHE_5m, function () use ($input, $store) {
                return $this->elasticGetFilterProduct($input, false, $store);
            });
        }
        return $data;
    }

    /**
     * @param string $slug
     * @param int $storeId
     * @return HigherOrderBuilderProxy|mixed|null
     */
    private function getCollectionBanner(string $slug, int $storeId)
    {
        $collection = Collection::where('slug', $slug)->first();

        if ($collection) {
            $collectionId = $collection->id;
            // get banner url of collection
            $storeCollection = StoreCollection::where('store_id', $storeId)
                ->where('collection_id', $collectionId)
                ->first();

            if ($storeCollection) {
                return $storeCollection->banner_url;
            }
        }

        return null;
    }

    /**
     * @param StatsRequest $request
     * @return JsonResponse
     */
    public function getStats(StatsRequest $request): JsonResponse
    {
        try {
            $store = StoreService::getCurrentStoreInfo();
            if ($store && !$store->show_product_stats) {
                return $this->successResponse();
            }
            $arrEvents = [
                EventLogsTypeEnum::ADD_TO_CART,
                EventLogsTypeEnum::VISIT,
            ];
            $dateRanges['type'] = DateRangeEnum::LAST_7_DAYS;
            $data = TempEventLog::query()
                ->select('type')
                ->selectRaw('count(distinct(session_id))')
                ->where('campaign_id', $request->post('campaign_id'))
                ->whereIn('type', $arrEvents)
                ->addFilterAnalytic([], $dateRanges)
                ->groupBy('type')
                ->get();
            if (empty($data)) {
                return $this->errorResponse();
            }
            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    public function getSimilar(SimilarRequest $request): JsonResponse
    {
        try {
            $store = StoreService::getCurrentStoreInfo();
            if (is_null($store)) {
                throw new RuntimeException('Store not found');
            }

            $campaignId = $request->input('id');
            $cacheKey = CacheKeys::STOREFRONT_PRODUCT_SIMILAR_PREFIX . $campaignId;
            $tags = [];
            if ($store->id) {
                $tags[] = CacheKeys::getStoreId($store->id);
            }
            $campaigns = getDataByDefaultCache($cacheKey, $tags);

            if (is_null($campaigns)) {
                $campaigns = cacheAlt()->remember(CacheKeys::STOREFRONT_PRODUCT_SIMILAR_PREFIX . $campaignId, CacheTime::CACHE_24H,
                    function () use ($campaignId, $store) {
                        $filters = [];
                        $filters['id'] = $campaignId;

                        return (new Elastic())->getSimilarCampaigns($filters, $store);
                    }
                );
            }

            $headers = generateHeadersCacheHTMLTagByCampaignIds([$campaignId], CacheTime::CACHE_1W);

            return $this->successResponse($campaigns)->withHeaders($headers);
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Public API get template product info for catalog
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function catalogGetTemplateProductInfo(Request $request): JsonResponse
    {
        try {
            $templateProductId = $request->get('id');

            $product = ProductModel::query()->select([
                'id',
                'name',
                'thumb_url',
                'sku',
                'description',
                'market_location',
                'print_spaces',
                'attributes',
                'options',
                'full_printed',
            ])
            ->where('product_type', ProductType::TEMPLATE)
            ->findOrFail($templateProductId);

            $cacheKey = CacheKeys::getTemplateCatalog($templateProductId);
            $tags = CacheKeys::getProductTemplateTags($templateProductId);
            $response = getDataByDefaultCache($cacheKey, $tags);

            if (is_null($response)) {
                $response = cacheAlt()->tags($tags)->remember(
                    $cacheKey,
                    CacheTime::CACHE_24H,
                    function () use ($product) {
                        $product->load([
                            'variants' => function ($q) use ($product) {
                                $q->select([
                                    'product_id',
                                    'variant_key',
                                    'base_cost',
                                    'location_code',
                                    'out_of_stock',
                                    'adjust_price',
                                    'price',
                                    'old_price',
                                    'weight',
                                    'quantity',
                                    'check_quantity',
                                    'sku',
                                ]);
                                $q->when(!empty($product->market_location), function ($q) use ($product) {
                                    $q->where('location_code', $product->market_location);
                                });
                            }
                        ]);
                        $product->load([
                            'shipping_rules' => function ($q) use ($product) {
                                $q->select([
                                    'product_id',
                                    'variant_key',
                                    'shipping_method',
                                    'location_code',
                                    'shipping_cost',
                                    'extra_cost',
                                ]);
                                $q->when(!empty($product->market_location), function ($q) use ($product) {
                                    $q->where('location_code', $product->market_location);
                                });
                                $q->orderBy('variant_key');
                            }
                        ]);

                        $sizeGuides = ProductSizeGuide::allSizeGuides();
                        $sizeGuidesArr = [];

                        if ($sizeGuides) {
                            $sizeGuidesArr = $sizeGuides->where('product_id', $product->id)->toArray();
                        }

                        $options = Str::isJson($product->options) ? json_decode($product->options) : null;
                        $colors = [];
                        if ($options && isset($options->color) && count($options->color) > 0) {
                            foreach ($options->color as $color) {
                                $colors[] = [
                                    'name' => $color,
                                    'hex_code' => color2hex($color)
                                ];
                            }
                        }

                        return [
                            'name' => $product->name,
                            'thumb_url' => $product->thumb_url,
                            'sku' => $product->sku,
                            'variants' => $product->variants,
                            'shipping_rules' => $product->shipping_rules,
                            'description' => $product->description,
                            'market_location' => $product->market_location,
                            'colors' => count($colors) ? $colors : null,
                            'print_spaces' => Str::isJson($product->print_spaces) ? json_decode($product->print_spaces) : null,
                            'attributes' => Str::isJson($product->attributes) ? json_decode($product->attributes) : null,
                            'options' => $options,
                            'size_guides' => array_values($sizeGuidesArr),
                            'shipping_times' => $this->getShippingStaticByLocations($product->market_location),
                            'processing_time' => $this->getProcessingStaticByTemplate($product->id),
                        ];
                    }
                );
            }

            if ($request->attributes->get('is_senhub')) {
                $response['designs'] = File::query()
                    ->select([
                        'id',
                        'design_json',
                        'type',
                        'file_url',
                        'position',
                    ])
                    ->where([
                        'type'        => FileTypeEnum::MOCKUP,
                        'render_type' => FileRenderType::RENDER_PSD,
                        'product_id'  => $templateProductId,
                        'status'      => FileStatusEnum::ACTIVE,
                    ])
                    ->get();
            }

            return $this->successResponse($response);
        } catch (Throwable $e) {
            return $this->errorResponse('Product template not found', 404);
        }
    }

    private function getShippingStaticByLocations($market_location = null): array
    {
        $rate = 2;
        $data = [];
        $keys = [
            '150',
            '*',
            'US',
            'CA',
        ];
        if (!empty($market_location)) {
            $keys = [
                $market_location,
            ];
        }
        $shippingMethods = ShippingMethodEnum::getValues();
        $defaultShippingTimes = ShippingTime::SHIPPING_TIME;

        foreach ($shippingMethods as $shippingMethod) {
            foreach ($keys as $key) {
                $shippingTimes = getShippingTime($key, $shippingMethod);
                if (empty($shippingTimes)) {
                    $shippingTimes = $defaultShippingTimes[$key];
                }
                $max = (int)round($shippingTimes[1] * $rate);
                if ($shippingMethod === ShippingMethodEnum::EXPRESS) {
                    $max = (int)round($max * 0.7);
                }
                $min = (int)round($shippingTimes[0]);
                if ($min < 1) {
                    $min = 1;
                }

                $data[$shippingMethod][$key] = [
                    'min' => $min,
                    'max' => $max,
                ];
            }
        }

        return $data;
    }

    private function getProcessingStaticByTemplate($templateId): int
    {
        $rate = SystemConfig::getConfig('printing_time_rate', 1.5);
        $avg = getAverageProcessingDay($templateId);

        return (int)round($avg * $rate);
    }

    private function containsTrademark(string $searchQuery): bool
    {
        try {
            $trademarks = Cache::remember('trademarks', 86400 * 30, function () {
                return TrademarkList::query()->get()->toArray();
            });

            if (empty($trademarks)) {
                return false;
            }

            // Filter trademarks that have block_text or block_logo enabled
            $trademarks = collect($trademarks)
                ->filter(fn($item) => !empty($item['block_text']) || !empty($item['block_logo']))
                ->pluck('text')
                ->unique()
                ->values()
                ->map(fn($item) => strtolower($item))
                ->toArray();

            if (empty($trademarks)) {
                return false;
            }

            $searchQuery = strtolower($searchQuery);

            // Check if search query contains any trademark keywords
            foreach ($trademarks as $trademark) {
                if (str_contains($searchQuery, $trademark)) {
                    return true;
                }
            }

            return false;
        } catch (Throwable $e) {
            // Log error but don't block the search in case of error
            logException($e);
            return false;
        }
    }
}

<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\HomeListingEnum;
use App\Enums\SystemConfigCrispKeyEnum;
use App\Http\Controllers\Controller;
use App\Jobs\SubscribeCustomerJob;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Product;
use App\Models\ProductSizeGuide;
use App\Models\Store;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Services\StoreService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use App\Traits\GetStoreDomain;
use App\Traits\Product as ProductTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class StoreController extends Controller
{
    use ApiResponse, ElasticClient, ProductTrait, GetStoreDomain;

    private $isSubDomain = false;

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function show(Request $request): JsonResponse
    {
        $store = StoreService::getCurrentStoreInfo();
        return $store
            ? $this->successResponse($store)
            : $this->errorResponse();
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws \JsonException
     */
    public function storeInfo(Request $request): JsonResponse
    {
        $storeInfo = StoreService::getCurrentStoreInfo();
        if (!$storeInfo) {
            return $this->errorResponse();
        }

        $affiliateId = $request->header('spsid');
        if ($affiliateId) {
            $affiliateTrackingCode = UserService::getSellerTrackingCode($affiliateId);
            if ($affiliateTrackingCode) {
                $storeInfo['trackingCode'] = $affiliateTrackingCode;
            }
        }
        $generalSettings = $this->loadGeneralSettings();
        return $this->successResponse(compact(
            'storeInfo',
            'generalSettings'
        ));
    }

    /**
     * Display the specified resource.
     *
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function home(Request $request): JsonResponse
    {
        $store = StoreService::getCurrentStoreInfo();
        if (!$store) {
            return $this->errorResponse('Store is invalid');
        }
        $data = [];
        $data['other'] = [];
        $campaignIds = [];
        $arr = HomeListingEnum::asArray();
        foreach ($arr as $key) {
            $data[$key] = $this->getProductsHomeListing($key, $store, $campaignIds);
        }
        $featuredCollectionIds = $store->featured_collection_ids;
        if (!empty($featuredCollectionIds) && $featuredCollectionIds !== '[]') {
            $collectionIds = explode(',', $store->featured_collection_ids);

            if (is_array($collectionIds) && !empty($collectionIds)) {
                $collectionsMap = Collection::query()
                    ->select('id', 'name', 'slug')
                    ->whereIn('id', $collectionIds)
                    ->get()
                    ->keyBy('id');

                foreach ($collectionIds as $collectionId) {
                    $collection = $collectionsMap[$collectionId] ?? null;

                    $products = $this->getProductsHomeListing(
                        HomeListingEnum::FEATURED,
                        $store,
                        $campaignIds,
                        $collectionId,
                    );
                    $data['other'][] = [
                        'id' => $collectionId,
                        'name' => $collection?->name,
                        'slug' => $collection?->slug,
                        'products' => $products,
                    ];
                }
            }
        }
        $headers = generateHeadersCacheHTMLTagByCampaignIds($campaignIds);
        return $this->successResponse($data)->withHeaders($headers);
    }

    /**
     * @param $key
     * @param $store
     * @param $campaignIds
     * @param $collectionId
     * @return array
     * @throws \Exception
     */
    private function getProductsHomeListing($key, $store, &$campaignIds, $collectionId = null): array
    {
        $products = $this->elasticHomeListingProductsRaw($key, $store, $collectionId);
        if (empty($products)) {
            return [];
        }
        self::mappingCorrectPricing($products);
        $campaignIds = array_merge($campaignIds, $this->getCampaignIds($products));
        StoreService::getVariantOfProducts($products);
        return $products;
    }

    /**
     * Get general settings
     * @return JsonResponse
     * @throws \JsonException
     */
    public function getGeneralSettings(): JsonResponse
    {
        $settings = $this->loadGeneralSettings();
        return $this->successResponse($settings);
    }

    /**
     * @throws \JsonException
     */
    private function loadGeneralSettings()
    {
        $cacheKey = CacheKeys::GENERAL_SETTINGS;
        $settings = getDataByDefaultCache($cacheKey);

        if (is_null($settings)) {
            $settings = cacheAlt()->remember(CacheKeys::GENERAL_SETTINGS, CacheTime::CACHE_24H, static function () {
                $cacheKey = CacheKeys::CATEGORIES;
                $categoriesMenu = getDataByDefaultCache($cacheKey);
                if (is_null($categoriesMenu)) {
                    $categoriesMenu = cacheAlt()->remember(CacheKeys::CATEGORIES, CacheTime::CACHE_30D, static function () {
                        $categories = Category::query()
                            ->select(['id', 'name', 'slug', 'parent_id'])
                            ->where('popularity', '>', 0)
                            ->whereNotIn('id', [104]) // exclude some categories
                            ->orderBy('parent_id', 'ASC')
                            ->get();
                        $menus = [];
                        $menus2 = [];
                        foreach ($categories as $nav) {
                            if ($nav->parent_id !== null) { // if parent id !== null: child
                                $parentId = $nav->parent_id;
                                foreach ($categories as $nav2) { // find parent
                                    // nav2 = parent
                                    if ($nav2->id === $parentId) {
                                        $nav2->addChildMenu($nav);
                                        break;
                                    }
                                }
                            } else {
                                $menus[] = $nav; // parent
                            }
                        }

                        foreach ($menus as $menu) {
                            $menus2[] = $menu->toArray();
                        }

                        return $menus2;
                    });
                }

                $settings['categories'] = $categoriesMenu;

                $settings['colors'] = StoreService::systemColors();

                $settings['languages'] = [
                    [
                        'code' => 'en',
                        'name' => 'English'
                    ],
                    [
                        'code' => 'fr',
                        'name' => 'France'
                    ],
                    [
                        'code' => 'vi',
                        'name' => 'Vietnamese'
                    ]
                ];

                $settings['countries'] = SystemLocation::systemCountries();
                $settings['size_guides'] = ProductSizeGuide::allSizeGuides();
                $settings['paypal_discount'] = SystemConfig::getConfig('paypal_discount', 0);
                $settings['country_disabled_checkout'] = SystemConfig::getConfig('disable_country', '');
                $settings[CacheKeys::CHECKOUT_FORM_CONFIG] = SystemConfig::getCheckoutFormConfig();
                if (!empty($settings['country_disabled_checkout'])) {
                    $settings['country_disabled_checkout'] = array_map('trim', explode(',', $settings['country_disabled_checkout']));
                }

                $cacheKey = CacheKeys::TEMPLATES;
                $tag = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;
                $settings['templates'] = getDataByDefaultCache($cacheKey, $tag);

                if (is_null($settings['templates'])) {
                    $settings['templates'] = cacheAlt()->tags([$tag])->remember($cacheKey, CacheTime::CACHE_30D, function () {
                        $sortedTemplateIds = topTemplateIds();

                        return Product::query()
                            ->select(['id', 'name'])
                            ->whereIn('id', $sortedTemplateIds)
                            ->get()
                            ->sortBy(fn ($template) => array_search($template->id, $sortedTemplateIds))
                            ->values();
                    });
                }

                return json_encode($settings, JSON_THROW_ON_ERROR);
            });
        }
        if (is_null($settings)) {
            $settings = [];
        } else if (Str::isJson($settings)) {
            $settings = json_decode($settings, true, 512, JSON_THROW_ON_ERROR);
        }
        // set currency out of cache to avoid cache issue:
        // rate in product detail page is not same as rate in checkout page
        $settings['currencies'] = StoreService::systemCurrencies();

        return $settings;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function subscribe(Request $request): JsonResponse
    {
        $sessionId = $request->header('x-session-id');
        $clientIp = getIp($request);
        $csrfToken = $request->get('token');
        $email = $request->post('email');

        if (!$sessionId) {
            return $this->errorResponse('Session ID is required.');
        }

        if (!verifyCsrfToken($csrfToken, $sessionId, $clientIp)) {
            return $this->errorResponse('CSRF Token is required.');
        }

        if (empty($email)) {
            return $this->errorResponse('Email is invalid.');
        }

        $store = StoreService::getCurrentStoreInfo();
        if (!isset($store->seller_id)) {
            return $this->errorResponse('Store not found.');
        }
        if (is_array($email)) {
            $emails = array_map('trim', array_values($email));
            foreach ($emails as $email) {
                if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    return $this->errorResponse('Email is invalid.');
                }
                SubscribeCustomerJob::dispatch($email, $store->seller_id, $store->id);
            }
            return $this->successResponse();
        }
        SubscribeCustomerJob::dispatch($email, $store->seller_id, $store->id);
        return $this->successResponse();
    }

    public function getCollectionBanners(): JsonResponse
    {
        $banners = cache()->remember(CacheKeys::HOME_PAGE_COLLECTION_BANNER, CacheKeys::CACHE_24H, static function () {
            return Store::query()
                ->select('id')
                ->with(['collection_banners'])
                ->where('id', 1)
                ->first();
        });
        return $this->successResponse($banners);
    }

    public function isEnableLiveChat(): JsonResponse
    {
        // cache 5min
        $enabled = cache()->remember(CacheKeys::CRISP_ENABLED, CacheTime::CACHE_5m, function () {
            $config = SystemConfig::getCustomConfig(SystemConfigCrispKeyEnum::KEY, false);
            return $config ? intval($config->value) : 0;
        });
        return $this->successResponse($enabled);
    }
}

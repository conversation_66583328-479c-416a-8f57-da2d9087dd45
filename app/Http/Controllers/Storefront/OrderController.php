<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\CacheTime;
use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\ProductStatus;
use App\Enums\ShippingMethodEnum;
use App\Http\Controllers\Controller;
use App\Jobs\SendBuyerOrderConfirmationJob;
use App\Models\Campaign;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\SendMailLog;
use App\Models\ShippingRule;
use App\Models\Staff;
use App\Models\SystemLocation;
use App\Models\User;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use App\Traits\StripePaymentDescriptors;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\OrderService\Jobs\DatabaseSync\SyncOrderToRegion;

class OrderController extends Controller
{
    use ApiResponse, StripePaymentDescriptors;

    /**
     * Get data for order status page
     *
     * @param string $accessToken
     * @return JsonResponse
     * @throws \Throwable
     */
    public function getDetail(string $accessToken): JsonResponse
    {
        $order = Order::query()
            ->select([
                'id',
                'access_token',
                'discount_code',
                'order_number',
                'payment_gateway_id',
                'payment_method',
                'promotion_rule_id',
                'shipping_method',
                'total_amount',
                'total_discount',
                'payment_discount',
                'tip_amount',
                'total_product_amount',
                'total_quantity',
                'total_shipping_amount',
                'insurance_fee',
                'total_tax_amount',
                'country',
                'paid_at',
                'updated_at',
                'fulfilled_at',
                'delivered_at',
                'status',
                'fraud_status',
                'fulfill_status',
                'customer_name',
                'customer_email',
                'customer_phone',
                'address',
                'address_2',
                'city',
                'state',
                'postcode',
                'currency_code',
                'mailbox_number',
                'house_number',
                'currency_rate',
                'received_at',
            ])
            ->where('access_token', $accessToken)
            ->whereNotIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT
            ])
            ->with(['products' => function ($query) {
                $query->select([
                    'id',
                    'campaign_id',
                    'template_id',
                    'supplier_id',
                    'campaign_title',
                    'options',
                    'custom_options',
                    'color',
                    'size',
                    'order_id',
                    'seller_id',
                    'price',
                    'product_id',
                    'product_name',
                    'product_url',
                    'quantity',
                    'thumb_url',
                    'total_amount',
                    'discount_amount',
                    'fulfill_status',
                    'tracking_code',
                    'shipping_carrier',
                    'tracking_url',
                    'tracking_status',
                    'personalized',
                    'full_printed',
                    'received_at',
                    'combo_id',
                ]);
                $query->with(['productReview' => function ($review) {
                    $review->on('mysql_main_us');
                    $review->select(['order_product_id', 'average_rating']);
                }])->orderByDesc('updated_at');
            }])
            ->with(['request_cancel' => function ($query) {
                $query->select([
                    'order_id',
                    'sent_email',
                    'status'
                ])->whereNotIn('status', [
                    OrderCancelRequestStatus::CANCELLED,
                    OrderCancelRequestStatus::COMPLETED,
                ]);
            }])
            ->first();

        if (is_null($order)) {
            return $this->errorResponse();
        }

        $shippingMethods = $order->availableShippingMethods();

        $campaigns = [];
        $sellers = [];
        $order->products->map(function (OrderProduct $product) use (&$campaigns, &$sellers) {
            if (!isset($sellers[$product->seller_id])) {
                $sellers[$product->seller_id] = User::query()->find($product->seller_id);
            }
            $product->loadMissing(['product' => function ($query) use ($product, $sellers) {
                $query->onSellerConnection($sellers[$product->seller_id]);
                $query->select(['id', 'options']);
            }]);
            if ($product->combo_id && $product->campaign_id) {
                $campaignId = $product->campaign_id;
                if (!isset($campaigns[$campaignId])) {
                    $campaign = Campaign::query()->onSellerConnection($sellers[$product->seller_id])->select(['id', 'name', 'thumb_url'])->find($campaignId);
                    if ($campaign) {
                        $campaigns[$campaignId] = $campaign;
                    }
                }
                if (isset($campaigns[$campaignId])) {
                    $product->setRelation('campaign', $campaigns[$campaignId]);
                }
            }
        });

        $fulfillments = $order->getFulfillments(isCustomer: true);
        $timeframe = $order->getTimeframe();
        $order->is_notify_china_delivery_late = $order->isNotifyChinaDeliveryLate();
        $order->setRelation('products', null);

        $response = [
            'order' => $order,
            'fulfillments' => $fulfillments,
            'timeframe' => $timeframe,
            'shipping_methods' => $shippingMethods,
        ];

        if (!self::isStripePaymentMethod($order->payment_method)) {
            return $this->successResponse($response);
        }

        try {
            $statementDescriptor = self::getFullStatementDescriptor(
                $order->payment_gateway_id,
                $order->order_number
            );

            if ($statementDescriptor) {
                $response['statement_descriptor'] = $statementDescriptor;
            }
            if ($order->isPaid() && $order->isDateAfter('updated_at', $order->getRegionSyncedAt())) {
                SyncOrderToRegion::dispatch($order, $order->getRegion(), syncOrderProducts: false)->onQueue('sync_order_region');
            }
        } catch (\Throwable $e) {
            // do nothing
        }

        return $this->successResponse($response)->withHeaders([
            'Cache-Tags' => 'order=' . $order->id,
            'Cache-Expire-Time' => CacheTime::CACHE_1W
        ]);
    }

    public function getToken(Request $request): JsonResponse
    {
        $request->validate([
            'order_number' => 'bail|required|string',
            'email' => 'required|email'
        ]);

        $order = Order::query()
            ->select('access_token')
            ->whereNotIn('status', [
                OrderStatus::DRAFT,
                OrderStatus::PENDING
            ])
            ->firstWhere([
                'order_number' => $request->post('order_number'),
                'customer_email' => $request->post('email')
            ]);

        return is_null($order)
            ? $this->errorResponse()
            : $this->successResponse($order->access_token);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function checkOrderForReview(Request $request): JsonResponse
    {
        $request->validate([
            'order_number' => 'required|string',
            'email' => 'required|email'
        ]);
        $storeInfo = StoreService::getCurrentStoreInfo();
        if ($storeInfo === null) {
            return $this->errorResponse();
        }
        $order = Order::query()
            ->select([
                'id',
                'order_number',
                'total_quantity',
                'total_discount',
                'total_amount',
                'store_id',
                'status',
                'access_token',
                'customer_email',
                'customer_name'
            ])
            ->with([
                'products:id,order_id,campaign_title,product_name,product_url,thumb_url,quantity'
            ])
            ->whereNotIn('status', [
                OrderStatus::DRAFT,
                OrderStatus::PENDING
            ])
            ->firstWhere([
                'store_id' => $storeInfo->id,
                'order_number' => $request->post('order_number'),
                'customer_email' => $request->post('email')
            ]);

        return is_null($order)
            ? $this->errorResponse()
            : $this->successResponse($order);
    }

    public function resendOrderConfirmationEmail(Request $request): JsonResponse
    {
        $input = $request->validate(['email' => 'required|email']);

        // get last order paid by this email
        $order = Order::query()
            ->where([
                'customer_email' => $input['email'],
                'payment_status' => OrderPaymentStatus::PAID
            ])
            ->whereNotIn('status', [
                OrderStatus::DRAFT,
                OrderStatus::PENDING,
                OrderStatus::REFUNDED,
                OrderStatus::CANCELLED,
                OrderStatus::DELETED,
                OrderStatus::PENDING_PAYMENT,
                OrderStatus::SUSPENDED,
            ])
            ->latest('paid_at')
            ->first();

        if ($order === null) {
            // always return success to prevent email enumeration
            return $this->successResponse();
        }
        SendBuyerOrderConfirmationJob::dispatchAfterResponse($order);

        return $this->successResponse();
    }

    public function bulkResendOrdersConfirmationNotSent(Request $request): JsonResponse
    {
        $input = $request->validate(['day' => 'required|integer']);
        $day = data_get($input, 'day', 1);
        $endDate = now()->subMinutes(5);
        $startDate = now()->subDays($day);
        $orderIds = Order::query()
            ->select('id')
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->whereNotIn('status', [
                OrderStatus::DRAFT,
                OrderStatus::PENDING,
                OrderStatus::REFUNDED,
                OrderStatus::CANCELLED,
                OrderStatus::DELETED,
                OrderStatus::PENDING_PAYMENT,
                OrderStatus::SUSPENDED,
            ])
            ->where('paid_at', '<=', $endDate)
            ->where('paid_at', '>=', $startDate)
            ->get()
            ->pluck('id')
            ->toArray();
        $sentOrderIds = SendMailLog::query()
            ->selectRaw('DISTINCT order_id')
            ->whereIn('order_id', $orderIds)
            ->whereIn('status', ['pending', 'sent'])
            ->where('template', 'buyer.order_confirmation')
            ->get()
            ->pluck('order_id')
            ->toArray();
        $notSendOrderIds = array_diff($orderIds, $sentOrderIds);
        if (empty($notSendOrderIds)) {
            return $this->successResponse();
        }
        $notSendOrderIds = array_values($notSendOrderIds);
        foreach ($notSendOrderIds as $orderId) {
            $order = Order::query()->firstWhere('id', $orderId);
            if (!$order || $order->isFulfillmentOrder() || $order->isCustomServiceOrder() || $order->isServiceOrder()) {
                continue;
            }
            SendBuyerOrderConfirmationJob::dispatch($order, false);
        }
        return $this->successResponse([
            'day' => $day,
            'start_date' => $startDate->toDateTimeString(),
            'end_date' => $endDate->toDateTimeString(),
            'total' => count($notSendOrderIds),
            'order_ids' => $notSendOrderIds,
        ]);
    }

    public function bulkResendOrderConfirmationEmail(Request $request): JsonResponse
    {
        $request->validate(['ids' => 'required|string']);
        $ids = explode(',', $request->post('ids'));
        if (empty($ids)) {
            return $this->errorResponse('Invalid order ids');
        }
        $result = [];
        foreach ($ids as $id) {
            $order = Order::query()
                ->whereKey($id)
                ->where([
                    'payment_status' => OrderPaymentStatus::PAID
                ])
                ->whereNotIn('status', [
                    OrderStatus::DRAFT,
                    OrderStatus::PENDING,
                    OrderStatus::REFUNDED,
                    OrderStatus::CANCELLED,
                    OrderStatus::DELETED,
                    OrderStatus::PENDING_PAYMENT,
                    OrderStatus::SUSPENDED,
                ])
                ->latest('paid_at')
                ->first();
            if ($order === null) {
                continue;
            }
            SendBuyerOrderConfirmationJob::dispatchAfterResponse($order, false);
            $result[] = $order->id;
        }
        return $this->successResponse($result);
    }

    /**
     * @param Request $request
     * @param string $accessToken
     * @return JsonResponse
     */
    public function cancelOrder(Request $request, string $accessToken): JsonResponse
    {
        $region = 'sg';
        $userIds = Arr::get($request->header(), 'x-user-id');
        $userId = !empty($userIds) ? reset($userIds) : null;
        $order = Order::query()
            ->where('access_token', $accessToken)
            ->whereNotIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT
            ])
            ->first();

        if (is_null($order)) {
            return $this->errorResponse('Your order does not exist.');
        }

        $exists = OrderCancelRequest::query()
            ->where('order_id', $order->id)
            ->where('status', '!=', OrderCancelRequestStatus::CANCELLED)
            ->exists();

        if ($exists) {
            return $this->successResponse();
        }

        if ($order->shipping_method !== ShippingMethodEnum::STANDARD || Carbon::parse($order->paid_at)->addHours(12) <= now()) {
            return $this->successResponse();
        }

        try {
            $staff = Staff::query()->find($userId);
            $displayLevel = isset($staff) ? OrderHistoryDisplayLevelEnum::ADMIN : OrderHistoryDisplayLevelEnum::CUSTOMER;
            $action = isset($staff) ? OrderHistoryActionEnum::ADMIN_ACTION : OrderHistoryActionEnum::CUSTOMER_ACTION;

            $staffEmail = $staff?->email;
            OrderCancelRequest::onRegion($region)->create([
                'id' => generateUUID(),
                'order_id' => $order->id
            ]);
            OrderHistory::insertLog(
                $order,
                $action,
                OrderHistoryActionEnum::REQUEST_CANCEL_12H_CREATED,
                $displayLevel,
                $staffEmail
            );
        } catch (\Throwable $e) {
            return $this->errorResponse('Something went wrong. Please try again later.');
        }

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @param string $accessToken
     * @return JsonResponse
     */
    public function deleteCancelOrder(Request $request, string $accessToken): JsonResponse
    {
        $region = 'sg';
        $userIds = Arr::get($request->header(), 'x-user-id');
        $userId = !empty($userIds) ? reset($userIds) : null;

        $order = Order::onRegion($region)
            ->where('access_token', $accessToken)
            ->whereNotIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT
            ])
            ->first();

        if (empty($order)) {
            return $this->errorResponse('Your order does not exist.');
        }

        $requestCancel = OrderCancelRequest::onRegion($region)
            ->where('order_id', $order->id)
            ->with('order')
            ->whereIn('status', [
                OrderCancelRequestStatus::PENDING,
                OrderCancelRequestStatus::CONFIRMED,
            ])
            ->first();

        if (empty($requestCancel)) {
            return $this->successResponse();
        }

        if ($order->shipping_method !== ShippingMethodEnum::STANDARD) {
            return $this->successResponse();
        }
        $requestCancel->status = OrderCancelRequestStatus::CANCELLED;
        $requestCancel->updated_at = now();
        $requestCancel->save();
        if ($order->fulfill_status === OrderFulfillStatus::ON_HOLD) {
            $order->fulfill_status = OrderFulfillStatus::UNFULFILLED;
            $order->admin_note = null;
            $order->save();
        }
        $staff = Staff::query()->find($userId);
        $staffEmail = $staff?->email;
        $displayLevel = isset($staff) ? OrderHistoryDisplayLevelEnum::ADMIN : OrderHistoryDisplayLevelEnum::CUSTOMER;
        OrderHistory::insertLog(
            $order,
            OrderHistoryActionEnum::RESUME_FULFILL,
            'Automatic cancellation of cancel order request - Customer click resume order',
            $displayLevel,
            $staffEmail
        );
        $order = $order->refresh();
        SyncOrderToRegion::dispatch($order, $order->getRegion(), syncOrderProducts: false)->onQueue('sync_order_region');
        $storeInfo = StoreService::getStoreInfo($order->store_id);
        $dataSendMailLog = [
            'sellerId' => $order->seller_id,
            'storeId' => $order->store_id,
            'orderId' => $order->id,
        ];
        $config = [
            'to' => $order->customer_email,
            'template' => 'buyer.resume_order',
            'data' => [
                'subject' => "Your order #{$order->order_number} was resumed",
                'name' => $order->customer_name,
                'email' => $order->customer_email,
                'store_info' => $storeInfo,
                'order' => $order,
            ],
            'sendMailLog' => $dataSendMailLog
        ];
        sendEmail($config);
        return $this->successResponse();
    }

    /**
     * @param string $token
     * @return \Illuminate\Contracts\Foundation\Application|JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function confirmCancelOrder(string $token)
    {
        $region = 'sg';
        $request = OrderCancelRequest::query()
            ->with('order')
            ->whereKey($token)
            ->first();

        if (is_null($request)) {
            return $this->errorResponse('Your request does not exist.');
        }

        $storeInfo = StoreService::getStoreInfo($request->order->store_id);

        if (is_null($storeInfo)) {
            return $this->errorResponse('Your store does not exist.');
        }

        if (empty($storeInfo->domain)) {
            $storeUrl = 'https://' . config('senprints.base_url_seller');
        } else {
            $storeUrl = 'https://' . $storeInfo->domain;
        }

        $orderToken = $request->order->access_token;
        $orderStatusUrl = $storeUrl . '/order/status/' . $orderToken;

        if (now()->gt(Carbon::parse($request->expired_at))) {
            if (!empty($request->order)) {
                OrderHistory::insertLog(
                    $request->order,
                    OrderHistoryActionEnum::CUSTOMER_ACTION,
                    'Customer click link confirm but confirm url is expired'
                );
            }
            return redirect($orderStatusUrl . '?error=2');
        }

        if ($request->order->shipping_method !== ShippingMethodEnum::STANDARD) {
            return redirect($orderStatusUrl);
        }

        if ($request->status !== OrderCancelRequestStatus::PENDING) {
            return redirect($orderStatusUrl);
        }

        if ($request->order->fulfill_status === OrderFulfillStatus::PROCESSING) {
            OrderCancelRequest::onRegion($region)
                ->whereKey($token)
                ->where('status', '!=', OrderCancelRequestStatus::CANCELLED)
                ->update([
                    'status' => OrderCancelRequestStatus::CANCELLED,
                    'updated_at' => now()
                ]);

            if (!empty($request->order)) {
                OrderHistory::insertLog(
                    $request->order,
                    OrderHistoryActionEnum::RESUME_FULFILL,
                    'Customer click link confirm but order fulfill status in processing'
                );
            }
            return redirect($orderStatusUrl . '?error=1');
        }
        OrderCancelRequest::onRegion($region)
            ->where('id', '!=', $request->id)
            ->where('order_id', $request->order_id)
            ->where('status', '!=', OrderCancelRequestStatus::CANCELLED)
            ->update([
                'status' => OrderCancelRequestStatus::CANCELLED,
                'updated_at' => now()
            ]);
        $updated = OrderCancelRequest::onRegion($region)
            ->whereKey($token)
            ->update([
                'status' => OrderCancelRequestStatus::CONFIRMED,
                'updated_at' => now()
            ]);

        $orderQuery = Order::onRegion($region)->whereId($request->order->id);
        $orderQuery->whereNotIn('fulfill_status', [
            OrderFulfillStatus::FULFILLED,
            OrderFulfillStatus::CANCELLED
        ]);
        $order = $orderQuery->first();
        if ($order && $updated) {
            $note = OrderHistoryActionEnum::REQUEST_CANCEL_12H_CONFIRMED;
            $order->fulfill_status = OrderFulfillStatus::ON_HOLD;
            $order->admin_note = $note;
            $order->save();
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::HOLD_FULFILL,
                $note
            );
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::CUSTOMER_ACTION,
                $note,
                OrderHistoryDisplayLevelEnum::CUSTOMER
            );
            SyncOrderToRegion::dispatch($order, $order->getRegion(), syncOrderProducts: false)->onQueue('sync_order_region');
            $dataSendMailLog = [
                'sellerId' => $order->seller_id,
                'storeId' => $order->store_id,
                'orderId' => $order->id,
            ];
            $config = [
                'to' => $order->customer_email,
                'template' => 'buyer.confirmed_cancel_order',
                'data' => [
                    'subject' => "We have received your cancellation request of order #{$order->order_number}",
                    'name' => $order->customer_name,
                    'email' => $order->customer_email,
                    'store_info' => $storeInfo,
                    'order' => $order,
                ],
                'sendMailLog' => $dataSendMailLog
            ];
            if (!sendEmail($config)) {
                logToDiscord([
                    'email' => 'confirmed_cancel_order',
                    'order_id' => $order->id
                ], 'email');
            }
        }

        return redirect($orderStatusUrl);
    }

    public function preCalculateShipping(Request $request): JsonResponse
    {
        $productInfo = $request->productInfo;
        $location = (object)$request->location;
        $productIds = array_column($productInfo ?? [], 'productId'); // Extract product ids

        if (empty($productIds)) {
            return $this->errorResponse('Product ids is required.');
        }

        try {
            $productsDB = Product::query()
                ->whereIn(
                    'id',
                    $productIds
                )
                ->filterActiveProduct()
                ->with([
                    'template:id,base_cost,base_costs,shipping_cost,full_printed,sku',
                    'campaign:id,options'
                ])
                ->whereHas('template', function ($q) {
                    return $q->whereIn('status', [ProductStatus::ACTIVE, ProductStatus::ARCHIVED]);
                })
                ->orderByRaw(DB::raw("FIELD(id, " . implode(',', array_fill(0, count($productIds), '?')) . ") DESC"), $productIds)
                ->get();
        } catch (\Throwable $e) {
            return $this->errorResponse('Calculate shipping failed');
        }

        $productsDB->map(function ($product) use ($productInfo) {
            foreach ($productInfo as $info) {
                if ($product->id == $info['productId']) {
                    $product->quantity = $info['quantity'];
                    break;
                }
            }
            return $product;
        });
        $totalShippingAmount = 0;
        try {
            $locationInfo = SystemLocation::findByCountryCodeThenSetForAssign($location);
            $products = $productsDB->sortByDesc('base_shipping_cost');


            $productIds = [];
            $totalAmount = 0;
            foreach ($products as $product) {
                $productIds[] = $product->template_id;
                $totalAmount += $product->quantity * $product->price;
            }

            $insuranceFee = max((int)(($totalAmount * 2 / 100) / 0.5) * 0.5 - 0.02, Order::ORDER_INSURANCE_FEE);

            $locationCodes = ["*"];
            if (!is_null($locationInfo)) {
                $locationCodes = $locationInfo->getRegionCodes();
            }
            $rules = ShippingRule::query()
                ->whereIn('product_id', $productIds)
                ->whereIn('location_code', $locationCodes)
                ->where('shipping_method', ShippingMethodEnum::STANDARD)
                ->orderByDesc('supplier_id') // get not null first
                ->get();
            $arr = [];
            $arr2 = [];
            foreach ($products as $product) {
                $quantity = $product->quantity;
                $rule = $this->matchLocation($rules, $locationCodes, $product);

                if ($rule === null) {
                    $shippingCost = $product->base_shipping_cost;
                    $totalProductShippingAmount = ($shippingCost * $quantity);
                } else {
                    $supplierId = $product->supplier_id ?? 0;
                    $templateId = $product->template_id;
                    $shippingCost = $rule->shipping_cost;
                    $shippingExtraCost = !empty($rule->extra_cost) ? $rule->extra_cost : $shippingCost;

                    if (empty($arr2[$templateId]) && (empty($arr[$supplierId]) || $supplierId === 0)) {
                        $arr[$supplierId] = $shippingCost + ($shippingExtraCost * ($quantity - 1));
                    } else {
                        $arr[$supplierId] = ($shippingExtraCost * $quantity);
                    }

                    $totalProductShippingAmount = $arr[$supplierId];
                    $arr2[$templateId] = $totalProductShippingAmount;

                }
                $product->shipping_cost = $totalProductShippingAmount;
                $totalShippingAmount += $product->shipping_cost;
            }
            return $totalShippingAmount === 0
                ? $this->errorResponse('Calculate shipping failed')
                : $this->successResponse([
                    'shipping_amount' => round($totalShippingAmount, 2),
                    'insurance_fee' => round($insuranceFee, 2),
                ]);
        } catch (\Exception $exception) {
            return $this->errorResponse('Calculate shipping failed');
        }
    }

    /**
     * @return JsonResponse
     */
    public function completedPaypalOrderPaymentFailed()
    {
        $orders = Order::query()
            ->where('payment_method', PaymentMethodEnum::PAYPAL)
            ->where('payment_status', OrderPaymentStatus::FAILED)
            ->where('payment_log', 'like', '%is not allowed')
            ->get();

        if ($orders->isEmpty()) {
            return $this->errorResponse('Order not found');
        }
        $orderIds = [];
        foreach ($orders as $order) {
            $payment_log = $order->payment_log;
            if (empty($payment_log)) {
                continue;
            }
            $transaction_id = Str::of($payment_log)->explode('|')->first();
            $order->paymentCompleted($order->total_amount, $transaction_id);
            $orderIds[] = $order->id;
        }
        return $this->successResponse($orderIds);
    }

    private function matchLocation($rules, $locationCodes, $product): ?object
    {
        foreach ($locationCodes as $locationCode) {
            // check location code
            foreach ($rules as $rule) {
                // match product or template
                if ($rule->product_id === $product->id || $rule->product_id === $product->template_id) {
                    if (
                        $rule->location_code === $locationCode
                        && (
                            // check supplier if exist
                            is_null($rule->supplier_id) || $product->supplier_id === $rule->supplier_id
                        )
                        && (
                            // check variant key if exist
                            is_null($rule->variant_key) || $rule->variant_key === getVariantKey($product->options, $product->full_printed)
                        )
                    ) {
                        return $rule;
                    }
                }
            }
        }

        return null;
    }
}

<?php

namespace App\Http\Controllers;

use App\Enums\DateRangeEnum;
use App\Enums\OrderIssueStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Exports\Admin\OrderIssue\ExportOrderIssueToExcel;
use App\Exports\InvoicesExport;
use App\Http\Requests\CreateOrderIssueRequest;
use App\Http\Requests\UpdateOrderIssueRequest;
use App\Models\Order;
use App\Models\OrderIssue;
use App\Models\SellerBilling;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class OrderIssueController extends Controller
{
    use ApiResponse;

    public function index(Request $request)
    {
        $keywords = $request->input('q');
        $status = $request->input('status');
        $chargeType = $request->input('charge_type');
        $refundType = $request->input('refund_type');
        $customerRequest = $request->input('customer_request');
        $issueCategory = $request->input('issue_category');
        $supplierId = $request->input('supplier_id');
        $perPage = $request->input('per_page', 35);
        $dateRange = $request->input('date_range', DateRangeEnum::LAST_30_DAYS);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $query = OrderIssue::query()
            ->select(['order_issue.*', 'order.order_number as order_number'])
            ->with([
                'order' => function ($query) {
                    return $query->select('id', 'order_number', 'order_number_2', 'created_at');
                },
                'staff' => function ($query) {
                    return $query->select('id', 'name');
                },
                'seller' => function ($query) {
                    return $query->select('id', 'name', 'email');
                },
                'supplier' => function ($query) {
                    return $query->select('id', 'name');
                },
            ])->join('order', 'order.id', '=', 'order_issue.order_id');

        if (!empty($keywords)) {
            $query->where('detail', 'like', '%' . $keywords . '%')
                ->orWhere('order_number', 'like', '%' . $keywords . '%')
                ->orWhere('order_id', 'like', '%' . $keywords . '%');
        }

        if (!empty($status)) {
            $query->where('order_issue.status', $status);
        }

        if (!empty($chargeType)) {
            $query->where('charge_type', $chargeType);
        }

        if (!empty($refundType)) {
            $query->where('refund_type', $refundType);
        }

        if (!empty($customerRequest)) {
            $query->where('customer_request', $customerRequest);
        }

        if (!empty($issueCategory)) {
            $query->where('issue_category', $issueCategory);
        }

        if (!empty($supplierId) && (int)$supplierId !== 0) {
            $query->where('supplier_id', $supplierId);
        }

        $query = $query->orderByDesc('created_at')->filterDateRange($dateRange, $startDate, $endDate);
        $totalChargeAmount = $query->sum('charge_amount');
        $totalRefundAmount = $query->sum('refund_amount');
        $result = $query->paginate($perPage)->toArray();
        $result['total_charge_amount'] = $totalChargeAmount;
        $result['total_refund_amount'] = $totalRefundAmount;

        return $result;
    }

    /**
     * @param CreateOrderIssueRequest $request
     * @return JsonResponse
     */
    public function store(CreateOrderIssueRequest $request): JsonResponse
    {
        $currentUserId = currentUser()->getUserId();

        $orderId = $request->input('order_id');
        $issueCategory = $request->input('issue_category');
        $customerRequest = $request->input('customer_request');
        $chargeType = $request->input('charge_type');
        $refundType = $request->input('refund_type');
        $chargeAmount = $request->input('charge_amount');
        $refundAmount = $request->input('refund_amount');
        $detail = $request->input('detail');
        $supplierId = $request->input('supplier_id');
        $productCategory = $request->input('product_category');

        $sellerId = Order::query()
            ->where('id', $orderId)
            ->value('seller_id');

        if (empty($sellerId)) {
            return $this->errorResponse('Order not found');
        }

        try {
            $created = OrderIssue::query()->create([
                'order_id' => $orderId,
                'seller_id' => $sellerId,
                'issue_category' => $issueCategory,
                'customer_request' => $customerRequest,
                'charge_type' => $chargeType,
                'refund_type' => $refundType,
                'charge_amount' => $chargeAmount,
                'refund_amount' => $refundAmount,
                'detail' => $detail,
                'supplier_id' => $supplierId,
                'product_category' => $productCategory,
                'created_by' => $currentUserId
            ]);

            if ($created->wasRecentlyCreated) {
                return $this->successResponse();
            }

            return $this->errorResponse();
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param UpdateOrderIssueRequest $request
     * @return JsonResponse
     */
    public function update(UpdateOrderIssueRequest $request): JsonResponse
    {
        $issueId = $request->input('id');
        $issueCategory = $request->input('issue_category');
        $customerRequest = $request->input('customer_request');
        $chargeType = $request->input('charge_type');
        $refundType = $request->input('refund_type');
        $chargeAmount = $request->input('charge_amount');
        $refundAmount = $request->input('refund_amount');
        $detail = $request->input('detail');
        $supplierId = $request->input('supplier_id');
        $productCategory = $request->input('product_category');

        $updated = OrderIssue::query()->where('id', $issueId)->update([
            'issue_category' => $issueCategory,
            'customer_request' => $customerRequest,
            'charge_type' => $chargeType,
            'refund_type' => $refundType,
            'charge_amount' => $chargeAmount,
            'refund_amount' => $refundAmount,
            'detail' => $detail,
            'supplier_id' => $supplierId,
            'product_category' => $productCategory,
        ]);

        if ($updated) {
            return $this->successResponse();
        } else {
            return $this->errorResponse();
        }
    }

    /**
     * @param $id
     * @return JsonResponse
     */
    public function detail($id): JsonResponse
    {
        $issue = OrderIssue::query()->with([
            'order',
            'staff' => function ($query) {
                return $query->select('id', 'name');
            },
            'seller',
            'supplier' => function ($query) {
                return $query->select('id', 'name');
            },
        ])->firstWhere('id', $id);

        if (empty($issue)) {
            return $this->errorResponse('Issue not found');
        }

        $orderTransactions = SellerBilling::query()->where([
            'order_id' => $issue->order_id,
        ])
            ->with([
                'seller' => function ($query) {
                    $query->select(['id', 'name', 'email']);
                }
            ])
            ->get()
            ->makeHidden(['signature']);

        return $this->successResponse([
            'issue' => $issue,
            'transactions' => $orderTransactions
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function bulkChangeStatus(Request $request): JsonResponse
    {
        $ids = $request->input('ids');
        $status = $request->input('status');

        $query = OrderIssue::query()->whereIn('id', $ids);

        if ($status === OrderIssueStatusEnum::COMPLETED) {
            $query->where('status', OrderIssueStatusEnum::PROCESSING);
        } else if ($status === OrderIssueStatusEnum::PROCESSING) {
            $query->where('status', OrderIssueStatusEnum::COMPLETED);
        } else {
            return $this->errorResponse('Status invalid');
        }

        $updated = $query->update(['status' => $status]);

        return $this->successResponse($updated);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function calculateChargeAmount(Request $request): JsonResponse
    {
        $orderId = $request->input('order_id');
        $orderProducts = $request->input('products');

        if (empty($orderProducts)) {
            return $this->errorResponse();
        }

        $order = Order::query()
            ->where('id', $orderId)
            ->with('products')
            ->first();

        $order->rememberOldData();

        $order->promotion_rule_id = null;
        $order->insurance_fee = 0;
        $order->tip_amount = 0;

        $newProducts = $order->products->map(function ($product) use ($orderProducts) {
            $productIdx = array_search($product->id, array_column($orderProducts, 'id'));

            if (is_numeric($productIdx)) {
                $product->quantity = $orderProducts[$productIdx]['issue_quantity'];
            } else {
                $product->quantity = 0;
            }

            return $product;
        });

        $order->setRelation('product', $newProducts);
        $order->calculateDesignCost();
        if ($order->type === OrderTypeEnum::CUSTOM) {
            $order->calculateFulfillProcessingFee();
        }
        $order->calculateOrder();

        return $this->successResponse([
            'order' => $order
        ]);
    }

    /**
     * @param Request $request
     * @return BinaryFileResponse
     */
    public function export(Request $request): BinaryFileResponse
    {
        $keywords = $request->input('q');
        $status = $request->input('status');
        $chargeType = $request->input('charge_type');
        $refundType = $request->input('refund_type');
        $customerRequest = $request->input('customer_request');
        $issueCategory = $request->input('issue_category');
        $supplierId = $request->input('supplier_id');
        $dateRange = $request->input('date_range', DateRangeEnum::LAST_30_DAYS);
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        $query = OrderIssue::query()->with(['staff', 'seller', 'order', 'supplier']);

        if (!empty($keywords)) {
            $query->where('detail', 'like', '%' . $keywords . '%')
                ->orWhere('order_number', 'like', '%' . $keywords . '%')
                ->orWhere('order_id', 'like', '%' . $keywords . '%');
        }

        if (!empty($status)) {
            $query->where('status', $status);
        }

        if (!empty($chargeType)) {
            $query->where('charge_type', $chargeType);
        }

        if (!empty($refundType)) {
            $query->where('refund_type', $refundType);
        }

        if (!empty($customerRequest)) {
            $query->where('customer_request', $customerRequest);
        }

        if (!empty($issueCategory)) {
            $query->where('issue_category', $issueCategory);
        }

        if (!empty($supplierId) && (int)$supplierId !== 0) {
            $query->where('supplier_id', $supplierId);
        }

        $query = $query->orderByDesc('created_at')
            ->filterDateRange($dateRange, $startDate, $endDate);

        $issues = $query->get();

        $fileName = 'Senprints_order_issue_' . date('Y-m-d') . '.xlsx';

        return Excel::download(new ExportOrderIssueToExcel($issues), $fileName, \Maatwebsite\Excel\Excel::XLSX);
    }
}

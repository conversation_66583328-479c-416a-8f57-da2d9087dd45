<?php

namespace App\Http\Controllers\Auth;

use App\Enums\AdsLogsActionEnum;
use App\Enums\EnvironmentEnum;
use App\Enums\SellerTeamRoleEnum;
use App\Enums\StaffStatus;
use App\Enums\UserRegisterTypeEnum;
use App\Enums\UserStatusEnum;
use App\Events\AccountCreated;
use App\Events\Login;
use App\Http\Controllers\Controller;
use App\Http\Requests\User\Disable2faRequest;
use App\Http\Requests\User\Enable2faRequest;
use App\Http\Requests\User\Handle2faRequest;
use App\Jobs\VerifyEmailSellerRegistered;
use App\Models\Role;
use App\Models\SellerTeam;
use App\Models\Staff;
use App\Models\Supplier;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserSessions;
use App\Rules\Auth\ValidRegisterDomain;
use App\Rules\RecaptchaV2;
use App\Services\AdsCampaignService;
use App\Services\SessionService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Laravel\Socialite\Facades\Socialite;
use Modules\SellerAccount\Models\UserBalance;
use Spatie\Permission\Exceptions\RoleDoesNotExist;
use Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Facades\JWTFactory;

class AuthController extends Controller
{
    use ApiResponse;

    const ADMIN_SERVICE = 'admin';
    const SELLER_SERVICE = 'seller';

    /**
     * Get current user info
     * @route /me
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function me(Request $request): JsonResponse
    {
        $user = currentUser();
        $guardName = $request->guard_name;

        if ($request->access_account_id) {
            if ($guardName === 'supplier') {
                $model = new Supplier();
            } else {
                $model = new User();
            }
            $userInfo = $model::query()->firstWhere('id', $user->getUserId());
            if (!$userInfo) {
                return $this->errorResponse('User not found.');
            }
            $store_ids = null;
            $team_store_ids = null;
            if ($request->service_name === self::ADMIN_SERVICE) {
                $accessRole = SellerTeamRoleEnum::MANAGER;
                $guardName = 'user'; // patch guard name to fix RoleDoesNotExist exception
            } else {
                $memberPermission = SellerTeam::getMemberPermission($user->getUserId(), $user->getInfo()->id);
                if (!$memberPermission) {
                    return $this->errorResponse('access_role_not_found');
                }
                $accessRole = $memberPermission->role;
                $store_ids = empty($memberPermission->store_ids) ? null : array_map('intval', explode(',', $memberPermission->store_ids));
                $team_store_ids = get_team_seller_stores($user->getUserId(), $user->getInfo()->id, $user->getInfo());
            }

            $userInfo['store_ids'] = $store_ids;
            $userInfo['roles'] = [$accessRole];
            $userInfo['permissions'] = $this->getPermissionByRole($accessRole, $guardName);
            $userInfo['team_store_ids'] = $team_store_ids;

            if ($userInfo['permissions'] === null) {
                return $this->errorResponse('can_not_get_permission');
            }

            $accessAccount = collect(currentUser()->getInfo())->toArray(); // current role

            // If has been access_account_id exist
            // Get account info and show for client
            $userInfo['access_user'] = $accessAccount;
        } else {
            $userInfo = collect($user->getInfo())->toArray(); // your account
            $roles = $user->roles();
            $userInfo['access_user'] = null;
            $userInfo['store_ids'] = null;
            if (!$roles) {
                return $this->errorResponse('can_not_get_roles');
            }

            $userInfo['roles'] = $roles;

            if (count($userInfo['roles']) > 0) {
                $userInfo['permissions'] = $this->getPermissionByRole($userInfo['roles'][0], $guardName);
            }

            $userInfo['direct_permissions'] = optional($user->getInfo())->getDirectPermissions()->pluck('name') ?: [];

            $userInfo['permissions'] = collect([
                ...($userInfo['permissions'] ?? []),
                ...$userInfo['direct_permissions']
            ])->unique()->values();

            // add signature for Crisp
            // https://docs.crisp.chat/guides/chatbox-sdks/web-sdk/identity-verification/
            $userInfo['crisp_signature'] = self::hashForCrisp($userInfo['email']);

            // https://docs.crisp.chat/guides/chatbox-sdks/web-sdk/session-continuity/
            $userInfo['crisp_token_id'] = self::hashForCrisp('uid:' . $userInfo['id']);
        }
        $sellerBalance = UserBalance::query()->where('seller_id', $userInfo['id'])->get();
        $userInfo['account_balance'] = $sellerBalance->toArray();
        $userInfo['tfa_confirm'] = $request->tfa_confirm;
        $userInfo['email_need_verify_date'] = SystemConfig::getConfig('date_start_email_verify', '2024-08-29');
        $makeHidden = [
            'company_id',
            'shard_id',
            'tfa_secret',
            'db_connection',
            'crawled_email_avatar_at',
            'deleted_at',
            'created_at',
            'updated_at',
            'bonus_times',
            'marketing_bonus_times',
            'sharding_status',
            'is_marketplace'
        ];
        foreach ($makeHidden as $hidden) {
            if (isset($userInfo[$hidden])) {
                unset($userInfo[$hidden]);
            }
        }
        $data = [
            'success' => true,
            'user' => $userInfo
        ];

        return response()->json($data);
    }

    private static function hashForCrisp(string $data): string
    {
        return hash_hmac('sha256', $data, config('crisp.secret'));
    }

    /**
     * Refresh token
     *
     * @return JsonResponse
     */
    public function refreshToken(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'token_type' => 'bearer',
            'expires_in' => config('jwt.ttl') * 60,
            'access_token' => (string)auth()->refresh()
        ]);
    }

    /**
     * Create a user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function doRegister(Request $request): JsonResponse
    {
        $service = $request->service_name;
        if ('admin' === $service) {
            return $this->errorResponse('Access denied', 403);
        }
        if (!in_array($service, ['seller', 'customer'], true)) {
            return $this->errorResponse('Error! Role invalid', 403);
        }

        $rules = [
            'name' => ['required'],
            'email' => [
                'required',
                'email',
                'unique:user,email',
                new ValidRegisterDomain()
            ],
            'password' => ['required', 'min:6', 'confirmed', 'required_with:password_confirmation'],
            'password_confirmation' => ['required', 'min:6'],
            'g_token' => [
                'required',
                new RecaptchaV2('seller_dashboard')
            ],
            'country' => ['required'],
        ];
        $params = $request->all();
        $validator = Validator::make($params, $rules);

        if ($validator->fails()) {
            return $this->errorResponse($validator->messages());
        }
        if ($service === 'customer') {
            $service = 'seller';
        }
        // Use bcrypt hash password
        $params['password'] = Hash::make($params['password']);
        $params['role'] = $service;
        $params['register_country'] = $params['country'];
        // unset request
        unset($params['service_name'], $params['password_confirmation'], $params['country']);

        // check ref_id
        if (!empty($params['ref_id']) && User::query()
            ->where([
                'id' => $params['ref_id'],
                'is_ref' => 1
            ])
            ->exists()
        ) {
            $params['ref_id'] = (int) $params['ref_id'];
        } else {
            unset($params['ref_id']);
        }

        // check sale staff id (SDV-2210)
        if (!empty($params['ss_id']) && Staff::query()
            ->where('id', $params['ss_id'])
            ->exists()
        ) {
            // correct column name
            $params['sale_staff_id'] = (int)$params['ss_id'];
        }

        // always unset ss_id
        unset($params['ss_id']);

        $params['db_connection'] = 'mysql_sub';
        // Fix auth model
        $user = User::query()->create($params);

        if (!$user->wasRecentlyCreated) {
            return $this->errorResponse();
        }

        $user->assignRole($service);
        AccountCreated::dispatch($user, $request);
        return $this->successResponse(null, 'Account was created successfully');
    }

    /**
     * Handle login
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function doLogin(Request $request): JsonResponse
    {
        $serviceName = strtolower($request->service_name);

        // Use providers social (e.g.: facebook token, google token...)
        $token = $request->input('token');
        $country = $request->input('country');
        $driver = $request->input('driver');
        // Use providers database
        $email = $request->input('email');
        $password = $request->input('password');
        $rules = [
            'email' => ['required', 'email'],
            'password' => ['required']
        ];
        $registerTypeAccess = true;
        switch ($serviceName) {
            case 'admin':
                $guard = 'admin';
                $authModel = Staff::class;
                $registerTypeAccess = false;
                unset($country);
                break;
            case 'seller':
            case 'customer':
                $guard = 'user';
                $authModel = User::class;
                break;
            case 'supplier':
                $guard = 'supplier';
                $authModel = Supplier::class;
                $registerTypeAccess = false;
                break;
            default:
                $guard = 'user';
                $authModel = User::class;
        }
        Auth::shouldUse($guard);

        $data = [
            'message' => 'Invalid email or password'
        ];
        $statusCode = 401;
        try {
            if (is_null($token)) {
                $validator = Validator::make($request->all(), $rules);
                if ($validator->fails()) {
                    return response()->json($data, $statusCode);
                }

                $credentials = [
                    'email' => $email,
                    'password' => $password
                ];
                $authToken = Auth::attempt($credentials);
                if ($authToken) {
                    $user = $authModel::query()->firstWhere('email', $email);
                    if ($user) {
                        if (strtolower($user->status) === UserStatusEnum::HARD_BLOCKED) {
                            return response()->json([
                                'message' => 'Your account has been blocked.'
                            ], 403);
                        }

                        if (strtolower($user->status) === UserStatusEnum::DELETED) {
                            return response()->json([
                                'message' => 'Your account has been deleted.'
                            ], 403);
                        }

                        if (strtolower($user->status) === StaffStatus::DEACTIVATED) {
                            return response()->json([
                                'message' => 'Your account has been deactivated.'
                            ], 403);
                        }

                        $payload = JWTFactory::sub($user->id)
                            ->service_name($serviceName)
                            ->guard_name($guard)
                            ->customClaims([
                                'tfa_confirm' => false,
                                'session_token' => app(SessionService::class)->createSession($user)
                            ])
                            ->make();
                        unset($data['message']);
                        $data['success'] = true;
                        $data['token_type'] = 'bearer';
                        $data['expires_in'] = config('jwt.ttl') * 60;
                        $data['access_token'] = jwtauth_encode($payload);
                        $data['tfa_enable'] = $user->tfa_enable ? true : false;
                        $statusCode = 200;
                        if (!$user->country && isset($country)) {
                            $user->country = $country;
                            $user->save();
                        }
                    } else {
                        $data['message'] = 'User role invalid';
                    }
                }
            } else {
                if (is_null($driver)) {
                    $driver = 'google';
                }

                if ($driver === 'google-one-tap') {
                    $client = new \Google_Client();
                    $payload = $client->verifyIdToken($token);
                    unset($token);
                    if (check_access_auth_guard($guard) && !empty($payload) && $payload['email_verified']) {
                        $user = $authModel::query()->firstWhere('email', $payload['email']);
                        if (!empty($user)) {
                            if (strtolower($user->status) === UserStatusEnum::HARD_BLOCKED) {
                                return response()->json([
                                    'success' => false,
                                    'message' => 'Your account has been blocked.'
                                ]);
                            }

                            if (strtolower($user->status) === UserStatusEnum::DELETED) {
                                return response()->json([
                                    'message' => 'Your account has been deleted.'
                                ], 403);
                            }

                            if (strtolower($user->status) === StaffStatus::DEACTIVATED) {
                                return response()->json([
                                    'message' => 'Your account has been deactivated.'
                                ], 403);
                            }

                            if ($registerTypeAccess && (!isset($user->register_type) || $user->register_type !== UserRegisterTypeEnum::GOOGLE))  {
                                $user->register_type = UserRegisterTypeEnum::GOOGLE;
                                $user->db_connection = 'mysql_sub';
                                $user->save();
                            }

                            $payload = JWTFactory::sub($user->id)
                                ->email($user->email)
                                ->service_name($serviceName)
                                ->guard_name($guard)
                                ->customClaims([
                                    'tfa_confirm' => false,
                                    'session_token' => app(SessionService::class)->createSession($user)
                                ])
                                ->make();

                            if ('seller' === $serviceName && !$user->hasRole($serviceName)) {
                                $user->assignRole($serviceName);
                            }

                            $userInfo = $user->toArray();
                            $data = $this->createJwtResponse($payload, $userInfo);
                            $data['tfa_enable'] = $user->tfa_enable ? true : false;
                            $statusCode = 200;
                        } else {
                            return response()->json([
                                'success' => false,
                                'message' => 'User not found'
                            ]);
                        }
                    } else {
                        return response()->json([
                            'success' => false,
                            'message' => 'Google token absent'
                        ]);
                    }
                } else {
                    $googleUser = Socialite::driver($driver)->userFromToken($token);
                    unset($token);
                    $userInfo = null;

                    if (!is_null($googleUser)) {
                        $payload = null;

                        // Check user login with guard
                        if (check_access_auth_guard($guard)) {
                            $user = $authModel::query()->firstWhere('email', $googleUser->email);
                            if ($user) {
                                if (strtolower($user->status) === UserStatusEnum::HARD_BLOCKED) {
                                    return response()->json([
                                        'success' => false,
                                        'message' => 'Your account has been blocked.'
                                    ]);
                                }

//                                if (!$user->register_country && isset($country)) {
//                                    $user->register_country = $country;
//                                    $user->save();
//                                }

                                if ($registerTypeAccess && (!isset($user->register_type) || $user->register_type !== UserRegisterTypeEnum::GOOGLE)) {
                                    $user->register_type = UserRegisterTypeEnum::GOOGLE;
                                    if ($user->email && str_ends_with($user->email, '@gmail.com')) {
                                        $user->email_verified_at = now();
                                    }
                                    $user->save();
                                }

                                $payload = JWTFactory::sub($user->id)
                                    ->email($user->email)
                                    ->service_name($serviceName)
                                    ->guard_name($guard)
                                    ->customClaims([
                                        'tfa_confirm' => false,
                                        'session_token' => app(SessionService::class)->createSession($user)
                                    ])
                                    ->make();

                                if ('seller' === $serviceName && !$user->hasRole($serviceName)) {
                                    $user->assignRole($serviceName);
                                }

                                $userInfo = $user->toArray();
                                AdsCampaignService::track($request, AdsLogsActionEnum::SUBMIT, $user->id);
                            } else if ('seller' === $serviceName) {
                                // Create a user with default password: random string (16 characters)
                                $params = [
                                    'name' => $googleUser->getName(),
                                    'email' => $googleUser->email,
                                    'role' => $serviceName,
                                    'password' => Hash::make(Str::random()),
                                    'avatar' => $googleUser->getAvatar(),
                                    'register_type' => UserRegisterTypeEnum::GOOGLE,
                                    'db_connection' => 'mysql_sub'
                                ];

                                if (isset($country)) {
                                    $params['register_country'] = $country;
                                }

                                if ($googleUser->email && str_ends_with($googleUser->email, '@gmail.com')) {
                                    $params['email_verified_at'] = now();
                                }
                                $blockedDomains = SystemConfig::getConfig('register_blocked_domains', '');
                                if (!empty($blockedDomains)) {
                                    $blockedDomains = Str::of($blockedDomains)->explode(',')->map(fn($item) => trim($item))->map(fn($item) => strtolower($item))->toArray();
                                    $domain = explode('@', $googleUser->email);
                                    if (count($domain) > 1) {
                                        $domain = strtolower($domain[1]);
                                        if (in_array($domain, $blockedDomains, true)) {
                                            return response()->json([
                                                'success' => false,
                                                'message' => 'Your email domain is not allowed to register.'
                                            ]);
                                        }
                                    }
                                }

                                $refId = $request->post('ref_id');

                                // check ref_id
                                if (
                                    $refId
                                    && User::query()
                                        ->where([
                                            'id' => $refId,
                                            'is_ref' => 1
                                        ])
                                        ->exists()
                                ) {
                                    $params['ref_id'] = (int)$refId;
                                }

                                $user = $authModel::query()->create($params);

                                if ($user) {
                                    $user->assignRole($serviceName);
                                    $payload = JWTFactory::sub($user->id)
                                        ->email($user->email)
                                        ->service_name($serviceName)
                                        ->guard_name($guard)
                                        ->customClaims([
                                            'tfa_confirm' => false,
                                            'session_token' => app(SessionService::class)->createSession($user)
                                        ])
                                        ->make();
                                    $userInfo = $user;
                                    dispatch(new VerifyEmailSellerRegistered($user));
                                }

                                AdsCampaignService::track($request, AdsLogsActionEnum::REGISTER, $user->id);
                            }
                        }

                        if (!is_null($payload) && !is_null($userInfo)) {
                            $data = $this->createJwtResponse($payload, $userInfo);
                            $data['tfa_enable'] = $user->tfa_enable ? true : false;
                            $statusCode = 200;
                        } else {
                            $data['message'] = 'User not found';
                        }
                    } else {
                        $data['message'] = 'Google token absent';
                    }
                }
            }
        } catch (\Exception $e) {
            if ($e->getCode() === 2002) {
                $statusCode = 500;
                $data['message'] = 'Server busy. Please try again later';
            } else {
                $statusCode = 400;
                $data['message'] = $e->getMessage();
            }
        }

        if (!empty($user) && $serviceName === 'seller') {
            Login::dispatch($user, $request);
        }

        return response()->json($data, $statusCode);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function doSwitchAccount(Request $request): JsonResponse
    {
        $sellerId = $request->post('seller_id');
        $guardName = 'user';

        abort_if(!$sellerId, 403);

        $tokenPayload = get_payload_current_token();
        $memberId = $tokenPayload['sub'];

        $isAdmin = currentUser()->isAdmin();
        $tfaConfirm = false;
        if ($isAdmin) {
            $accessRole = SellerTeamRoleEnum::MANAGER;
            $tfaConfirm = true;
        } else {
            $memberPermission = SellerTeam::getMemberPermission($sellerId, $memberId);

            if (!$memberPermission) {
                return $this->errorResponse('access_role_not_found');
            }

            $userInfo = User::query()->firstWhere('id', $sellerId);

            if (!$userInfo) {
                return $this->errorResponse('user_not_found');
            }

            if (!currentUser()->getTfaEnable() && $userInfo->tfa_enable) {
                return $this->errorResponse('Your account has not activated two-factor authentication, but the account of the member you are attempting to access has. Please activate two-factor authentication for your account!', 200, ['fta_diff' => true]);
            }

            $accessRole = $memberPermission->role;
            $store_ids = empty($memberPermission->store_ids) ? null : array_map('intval', explode(',', $memberPermission->store_ids));
            $userInfo['store_ids'] = $store_ids;
            $userInfo['roles'] = [$accessRole];
            $userInfo['permissions'] = $this->getPermissionByRole($accessRole, $guardName);

            if (is_null($userInfo['roles'])) {
                return $this->errorResponse();
            }
        }

        /**
         * Fix custom payload access token
         * JWTFactory::sub don't work, use JWTFactory::customClaims
         * @date: 30/12/2021
         */
        SessionService::log(json_encode([
            'member_id' => $memberId,
            'seller_id' => $sellerId,
            'service_name' => $request->service_name,
            'guard_name' => $guardName,
            'access_role' => $accessRole,
            'tfa_confirm' => $tfaConfirm,
        ], JSON_THROW_ON_ERROR));

        $memberModel = $isAdmin ? Staff::class : User::class;
        try {
            $payload = JWTFactory::customClaims([
                'sub' => $memberId,
                'service_name' => $request->service_name,
                'guard_name' => $guardName,
                'access_account_id' => $sellerId,
                'access_role' => $accessRole,
                'tfa_confirm' => $tfaConfirm,
                'session_token' => app(SessionService::class)->createSession($userInfo ?? User::find($sellerId), $memberModel::query()->find($memberId))
            ])->make();
            //            $payload = JWTFactory::sub($memberId)
            //                ->service_name($request->server_name)
            //                ->guard_name($guardName)
            //                ->access_account_id($sellerId)
            //                ->access_role($accessRole)
            //                ->make();
            $data = [
                'token_type' => 'bearer',
                'expires_in' => config('jwt.ttl') * 60,
                'access_token' => jwtauth_encode($payload)
            ];

            if (!$isAdmin) {
                $data['user_info'] = $userInfo;
            }

            return $this->successResponse($data);
        } catch (\Exception $e) {
            return $this->errorResponse('can_not_create_token');
        }
    }

    /**
     * Switch back to seller account
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function doSwitchBack(Request $request): JsonResponse
    {
        $serviceName = strtolower($request->service_name);
        $guard = 'user';

        Auth::shouldUse($guard);

        $tokenPayload = get_payload_current_token();
        $userId = $tokenPayload['sub'];

        try {
            // https://github.com/tymondesigns/jwt-auth/wiki/Creating-Tokens#creating-a-token-based-on-a-user-object
            $user = User::query()->find($userId);

            if (!$user) {
                return $this->errorResponse();
            }

            $authToken = JWTAuth::fromUser($user);

            if (!$authToken) {
                return $this->errorResponse();
            }

            /**
             * Fix custom payload access token
             * JWTFactory::sub don't work, use JWTFactory::customClaims
             * @date: 30/12/2021
             */
            $payload = JWTFactory::customClaims([
                'sub' => $userId,
                'service_name' => $serviceName,
                'guard_name' => $guard,
                'access_account_id' => null,
                'access_role' => null,
            ])->make();

            //            $payload = JWTFactory::sub($userId)
            //                ->service_name($serviceName)
            //                ->guard_name($guard)
            //                ->access_account_id(null)
            //                ->access_role(null)
            //                ->make();

            $data['token_type'] = 'bearer';
            $data['expires_in'] = config('jwt.ttl') * 60;
            $data['access_token'] = jwtauth_encode($payload);
            $data['user_info'] = $user;

            return $this->successResponse($data);
        } catch (\Exception $e) {
            return $this->errorResponse(['error' => $e->getMessage()]);
        }
    }

    private function getPermissionByRole($role, $guardName = 'user'): ?\Illuminate\Support\Collection
    {
        try {
            return Role::findByName($role, $guardName)
                ->permissions
                ->pluck('name');
        } catch (RoleDoesNotExist $e) {
            return null;
        }
    }

    /**
     * User logout
     *
     * @return JsonResponse
     */
    public function doLogout(): JsonResponse
    {
        $user = User::find(currentUser()->getUserId());
        auth()->logout();
        $sessionToken = request()->session_token;
        if ($sessionToken) {
            UserSessions::query()
                ->where('user_id', $user->id)
                ->where('token', $sessionToken)
                ->delete();
        }

        return $this->successResponse(null, 'Successfully logged out');
    }

    /**
     * Generate 2FA secret key
     */
    public function generate2faSecret(Request $request)
    {
        try {
            $user = currentUser();
            $google2fa = (new \PragmaRX\Google2FAQRCode\Google2FA());
            $tfa_secret = $user->getTfaSecret();
            $userUpdate = User::find($user->getUserId());
            if (!isset($tfa_secret) || empty($tfa_secret)) {
                $userUpdate->tfa_enable = false;
                $userUpdate->tfa_secret = $google2fa->generateSecretKey();
                $userUpdate->save();
            }
            $qrCode = UserService::generateGoogle2FaQrCode([
                'name' => $user->getName(),
                'google2fa_secret' => $userUpdate->tfa_secret
            ]);

            $response['qr_code'] = $qrCode;
            $response['tfa_key_generated'] = true;
            $response['tfa_enable'] =  $userUpdate->tfa_enable ? true : false;
            return $this->successResponse($response);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Enable 2FA
     */
    public function enable2fa(Enable2faRequest $request)
    {
        try {
            $user = currentUser();
            $google2fa = (new \PragmaRX\Google2FAQRCode\Google2FA());
            $secret = $request->post('auth_code');
            $userUpdate = User::find($user->getUserId());
            $valid = $google2fa->verifyKey($userUpdate->tfa_secret, $secret);
            $guard = 'user';
            $serviceName = 'seller';
            if ($valid) {
                $userUpdate->tfa_enable = true;
                $userUpdate->save();
                $response['tfa_key_generated'] = true;
                $response['tfa_enable'] =  true;
                $payload = JWTFactory::sub($userUpdate->id)
                    ->service_name($serviceName)
                    ->guard_name($guard)
                    ->customClaims([
                        'tfa_confirm' => true,
                    ])->make();
                $response['token_type'] = 'bearer';
                $response['expires_in'] = config('jwt.ttl') * 60;
                $response['access_token'] = jwtauth_encode($payload);
                return $this->successResponse($response);
            } else {
                return $this->errorResponse('Authentication code is invalid, please try again!');
            }
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * Disable 2FA
     */
    public function disable2fa(Disable2faRequest $request)
    {
        try {
            $user = currentUser();
            $userUpdate = User::find($user->getUserId());
            $userUpdate->tfa_enable = false;
            $userUpdate->save();
            $guard = 'user';
            $serviceName = 'seller';
            $response['qr_code']  = $user->getTfaQrCode();
            $response['tfa_key_generated'] = true;
            $response['tfa_enable'] = false;
            $payload = JWTFactory::sub($userUpdate->id)
                ->service_name($serviceName)
                ->guard_name($guard)
                ->customClaims([
                    'tfa_confirm' => false,
                ])->make();
            $response['token_type'] = 'bearer';
            $response['expires_in'] = config('jwt.ttl') * 60;
            $response['access_token'] = jwtauth_encode($payload);
            return $this->successResponse($response);
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    public function checkTfa(Handle2faRequest $request)
    {
        try {
            $validated = $request->validated();
            $code = $validated['code'];
            $user = currentUser();
            $google2fa = (new \PragmaRX\Google2FAQRCode\Google2FA());
            $valid = $google2fa->verifyKey($user->getTfaSecret(), $code);
            $guard = 'user';
            $serviceName = 'seller';
            if ($valid) {
                $userUpdate = User::find($user->getUserId());
                $userUpdate->save();
                $payload = JWTFactory::sub($userUpdate->id)
                    ->service_name($serviceName)
                    ->guard_name($guard)
                    ->customClaims([
                        'tfa_confirm' => true,
                    ])->make();
                $response['token_type'] = 'bearer';
                $response['expires_in'] = config('jwt.ttl') * 60;
                $response['access_token'] = jwtauth_encode($payload);
                $response['user_info'] = $userUpdate;
                return $this->successResponse($response);
            }
            return $this->errorResponse('Authentication code is wrong, please try again!');
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    private function createJwtResponse($payload, $userInfo)
    {
        $data = [];
        $data['token_type'] = 'bearer';
        $data['expires_in'] = config('jwt.ttl') * 60;
        $data['access_token'] = jwtauth_encode($payload);
        $data['user_info'] = $userInfo;
        $data['tfa_confirm'] = false;

        return $data;
    }

    /**
     * Disable 2FA with email
     */
    public function disable2faForSocial(Request $request)
    {
        $incomingRequestUrl = app()->environment(EnvironmentEnum::LOCAL) ? env('SELLER_APP_URL') ?? rtrim(config('senprints.base_url_seller'), '/') : rtrim(config('senprints.base_url_seller'),'/');
        $currentUrl = app()->environment(EnvironmentEnum::LOCAL) ? env('APP_URL') : config('senprints.base_url_seller').'api';
        try {
            $user = currentUser();
            $user = User::find($user->getUserId());
            if (!$user->tfa_enable) {
                return $this->errorResponse('Two-factor authentication is not enabled');
            }

            $payload = JWTFactory::sub($user->id)
                ->service_name('seller')
                ->guard_name('user')
                ->customClaims([
                    'exp' => time() + (5 * 60)
                ])
                ->make();
            $token = jwtauth_encode($payload);
            $config = [
                'to' => $user->email,
                'template' => 'auth.disable_2fa_for_social_account',
                'data' => [
                    'subject' => 'Disable your two-factor authentication',
                    'name' => $user->name,
                    'base_url' => $incomingRequestUrl,
                    'disable_2fa_url' => $currentUrl . '/seller/disable-2fa-social/' . $token,
                    'token' => $token
                ],
                'sendMailLog' => [
                    'sellerId' => $user->id,
                    'subject' => 'Disable your two-factor authentication ' . $token,
                ],
            ];
            $status = sendEmail($config);
            return $this->successResponse();
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    public function disable2faForSocialComplete (Request $request, $jwt) {
        $incomingRequestUrl = app()->environment(EnvironmentEnum::LOCAL) ? env('SELLER_APP_URL') ?? rtrim(config('senprints.base_url_seller'), '/') : rtrim(config('senprints.base_url_seller'), '/');
        $tfaFailMessage = '';
        try{
            $user = JWTAuth::setToken($jwt)->authenticate();
            if ($user->tfa_enable) {
                $user->tfa_enable = false;
                $user->save();
            } else {
                $tfaFailMessage = '&tfa_message=' . urlencode('TFA is not enabled');
                return redirect($incomingRequestUrl .'/settings/general?current_tab=2fa&social_tfa_disabled=false' . $tfaFailMessage);
            }
        } catch (TokenExpiredException $e) {
            $tfaFailMessage = '&tfa_message=' . urlencode('Email is only available for 5 minutes from the time you receive this email, please confirm again');
            return redirect($incomingRequestUrl .'/settings/general?current_tab=2fa&social_tfa_disabled=false' . $tfaFailMessage);
        } catch (TokenInvalidException $e) {
            $tfaFailMessage = '&tfa_message=' . urlencode('An error occurred, please try again later');
            return redirect($incomingRequestUrl .'/settings/general?current_tab=2fa&social_tfa_disabled=false' . $tfaFailMessage);
        } catch (JWTException $e) {
            $tfaFailMessage = '&tfa_message=' . urlencode('An error occurred, please try again later');
            return redirect($incomingRequestUrl .'/settings/general?current_tab=2fa&social_tfa_disabled=false' . $tfaFailMessage);
        }
        return redirect($incomingRequestUrl .'/settings/general?current_tab=2fa&social_tfa_disabled=true');
    }
}

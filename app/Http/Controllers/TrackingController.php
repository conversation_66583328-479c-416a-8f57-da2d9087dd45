<?php

namespace App\Http\Controllers;

use App\Data\TrackingItemData;
use App\Enums\FulfillmentStatusEnum;
use App\Enums\TrackingLogTypeEnum;
use App\Enums\TrackingServiceEnum;
use App\Enums\TrackingStatusEnum;
use App\Events\KlaviyoTrackingUpdated;
use App\Exports\TrackingExcelExport;
use App\Http\Requests\SeventeenTrackWebhookRequest;
use App\Http\Requests\TrackWebhookRequest;
use App\Http\Resources\TrackingResource;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\TrackingStatus;
use App\Services\TrackingService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class TrackingController extends Controller
{
    use ApiResponse;

    public function index(Request $request): JsonResponse
    {
        $search = $request->get('q');
        $filterStatus = $request->get('status');
        $trackings = TrackingStatus::query()
            ->with('orderProducts:id,tracking_code,tracking_url,received_at')
            ->when($search, function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('order_number', 'like', '%' . $search . '%');
                    $query->orWhere('tracking_code', 'like', '%' . $search . '%');
                });
            })
            ->when($filterStatus, function ($query, $filterStatus) {
                $query->where('status', strtolower($filterStatus));
            })
            ->orderByDesc('created_at')
            ->paginate(25)
            ->through(function ($tracking) {
                if (!$tracking->orderProducts) {
                    $orderProducts = OrderProduct::query()
                        ->select('id', 'tracking_code', 'tracking_url', 'received_at')
                        ->where('order_id', $tracking->order_id)
                        ->where('tracking_code', 'like', $tracking->tracking_code . '%')
                        ->get();
                    $tracking->setRelation('orderProducts', $orderProducts);
                }
                return $tracking;
            });

        $trackingService = TrackingService::instance();
        $carriers = $trackingService->carriers();

        $trackings->getCollection()
            ->transform(fn($tracking) => TrackingResource::make($tracking)
                ->additional(['carriers' => $carriers])
                ->resolve());

        return $this->successResponse($trackings);
    }

    public function import(Request $request): JsonResponse
    {
        $arr = $request->post('tracking');
        $data = [];

        foreach ($arr as $each) {
            $arr = explode(',', $each);
            $storeDomain = array_shift($arr);
            $orderNumber = array_shift($arr);
            $trackingCode = array_shift($arr);
            $sku = array_shift($arr);

            $data[$orderNumber]['store_domain'] = $storeDomain;
            $data[$orderNumber]['products'][TrackingLogTypeEnum::IMPORTED][] = [
                'tracking_code' => $trackingCode,
                'sku' => $sku,
            ];
        }
        $arrTrackingInsert = [];
        $numberChanged = 0;

        try {
            DB::beginTransaction();

            foreach ($data as $orderNumber => &$order) {
                $products = &$order['products'];

                if (empty($products[TrackingLogTypeEnum::IMPORTED])) {
                    continue;
                }

                // check valid
                $objectOrder = Order::query()
                    ->select('id', 'fulfill_status')
                    ->where([
                        'store_domain' => $order['store_domain'],
                        'order_number' => $orderNumber
                    ])
                    ->withCount(['products' => function ($query) {
                        return $query->where('fulfill_status', '<>', FulfillmentStatusEnum::FULFILLED);
                    }])
                    ->first();

                if (is_null($objectOrder)) {
                    $products[TrackingLogTypeEnum::INVALID] = $products[TrackingLogTypeEnum::IMPORTED];
                    unset($products[TrackingLogTypeEnum::IMPORTED]);
                    continue;
                }

                // check fulfill_status to ignore
                if ($objectOrder->fulfill_status === FulfillmentStatusEnum::FULFILLED) {
                    $products[TrackingLogTypeEnum::ORDER_ALREADY_FULFILLED] = $products[TrackingLogTypeEnum::IMPORTED];
                    unset($products[TrackingLogTypeEnum::IMPORTED]);
                    continue;
                }

                $orderId = $objectOrder->id;
                foreach ($products[TrackingLogTypeEnum::IMPORTED] as $key => $product) {
                    $objectOrderProduct = OrderProduct::query()
                        ->select('id', 'fulfill_status')
                        ->firstWhere([
                            'order_id' => $orderId,
                            'sku' => $product['sku']
                        ]);

                    // check invalid
                    if (is_null($objectOrderProduct)) {
                        $products[TrackingLogTypeEnum::INVALID][] = $product;
                        unset($products[TrackingLogTypeEnum::IMPORTED][$key]);
                        continue;
                    }

                    // check exists
                    if ($objectOrderProduct->fulfill_status === FulfillmentStatusEnum::FULFILLED) {
                        $products[TrackingLogTypeEnum::ORDER_ALREADY_FULFILLED][] = $product;
                        unset($products[TrackingLogTypeEnum::IMPORTED][$key]);
                        continue;
                    }

                    OrderProduct::query()
                        ->where('id', $objectOrderProduct->id)
                        ->update([
                            'fulfill_status' => FulfillmentStatusEnum::FULFILLED,
                            'tracking_code' => $product['tracking_code']
                        ]);
                    $arrTrackingInsert[] = [
                        'tracking_code' => $product['tracking_code'],
                        'order_number' => $orderNumber,
                    ];
                    $numberChanged++;
                }

//                check if number of change == number of product hadn't fulfill
                $orderFulfilStatus = ($objectOrder->products_count === $numberChanged)
                    ? FulfillmentStatusEnum::FULFILLED
                    : FulfillmentStatusEnum::PARTIAL_FULFILLED;
                Order::query()
                    ->where('order_number', $orderNumber)
                    ->update([
                        'fulfill_status' => $orderFulfilStatus
                    ]);
            }
            unset($order);

            TrackingStatus::query()->insertOrIgnore($arrTrackingInsert);
            DB::commit();
        } catch (\Exception $e) {
            Log::error($e);
            DB::rollback();
        }

        return $this->successResponse($data);
    }

    public function listingExport(Request $request): JsonResponse
    {
        $storeDomain = $request->get('q');
        $sortBy = $request->get('sortBy');
        $sortDirection = $request->get('sortDirection');
        $orders = Order::listingExport($storeDomain, $sortBy, $sortDirection)
            ->paginate(15);

        return $this->successResponse($orders);
    }

    public function exportExcel(Request $request)
    {
        $storeDomain = $request->get('store_domain');

        // todo @long: limit the export
        $orders = Order::query()
            ->select('store_domain', 'order_number', 'order.id')
            ->addSelect('order_product.sku', 'order_product.product_id', 'order_product.tracking_code', 'order_product.shipping_carrier', 'order_product.tracking_url')
            ->join('order_product', 'order_product.order_id', 'order.id')
            ->whereHas('tracking_statuses', function ($query) {
                return $query->whereNull('exported_at')
                    ->where('tracking_code', DB::raw('`order_product`.`tracking_code`'));
            })
            ->where('store_domain', $storeDomain)
            ->get();

        if ($orders->isEmpty()) {
            return $this->errorResponse();
        }

        $export = new TrackingExcelExport($orders);
        $fileName = 'tracking_export_' . str_replace('.', '-', $storeDomain) . '_' . date('Y-m-d_H-i-s') . '.csv';

        try {
            TrackingStatus::query()
                ->whereIn('order_number', $orders->pluck('order_number'))
                ->whereNull('exported_at')
                ->update([
                    'exported_at' => DB::raw('CURRENT_TIMESTAMP')
                ]);

            return Excel::download($export, $fileName);
        } catch (\Exception $e) {
            Log::error($e);
            return $this->errorResponse();
        }
    }

    /**
     * @param SeventeenTrackWebhookRequest $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function webhook(SeventeenTrackWebhookRequest $request): JsonResponse
    {
        $sign = $request->header('sign');
        if (empty($sign)) {
            logToDiscord('[17Track] The sign field is required', 'tracking_status_logs');
            return $this->errorResponse('The sign field is required');
        }
        $event = $request->input('event');
        if (!SeventeenTrackWebhookRequest::verifySignature($sign, $request->getContent())) {
            logToDiscord('[17Track] Failed to verify signature: ' . $sign . ', body: ' . $request->getContent(), 'tracking_status_logs');
            return $this->errorResponse('The data with sign is invalid');
        }
        if (strtoupper($event) === 'TRACKING_UPDATED') {
            $trackingService = TrackingService::instance(TrackingServiceEnum::SEVENTEEN_TRACK);
            $trackingCode = $request->input('data.number');
            $shippingCarrier = $request->input('data.carrier');
            $status = $trackingService->convertTrackingStatus($request->input('data.track_info.latest_status.status'));
            $trackingStatus = TrackingStatus::query()->firstWhere('tracking_code', $trackingCode);
            if (!$trackingStatus) {
                return $this->errorResponse();
            }
            if ($status !== $trackingStatus->status) {
                $result = TrackingService::handleUpdateTrackingWebhook($trackingStatus, $trackingCode, $shippingCarrier, $status, $request->input('data.track_info.latest_event.time_utc'));
                if (!$result) {
                    return $this->errorResponse();
                }
            }
            KlaviyoTrackingUpdated::dispatchIf($status === TrackingStatusEnum::DELIVERED || $status === TrackingStatusEnum::OUT_FOR_DELIVERY, $trackingCode, $status);
        }
        return $this->successResponse();
    }

    /**
     * @param TrackWebhookRequest $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function webhookShipment(TrackWebhookRequest $request): JsonResponse
    {
        $source = $request->get('source');
        if (empty($source)) {
            return $this->errorResponse('Source is required');
        }
        $trackingService = TrackingService::instance($source);
        if (!$trackingService->verifySignature($request)) {
            logToDiscord("[$source] Failed to verify signature, body: " . $request->getContent(), 'tracking_status_logs');
            return $this->errorResponse('The data with sign is invalid');
        }
        $data = $request->input('data');
        $tracking = TrackingItemData::withMapping([$data])->first();
        $trackingCode = data_get($tracking, 'number');
        if (empty($trackingCode)) {
            return $this->errorResponse();
        }
        $trackingStatus = TrackingStatus::query()->firstWhere('tracking_code', $trackingCode);
        if (!$trackingStatus) {
            return $this->errorResponse();
        }
        $shippingCarrier = data_get($tracking, 'carrier');
        $deliveredTime = data_get($tracking, 'deliveredTime');
        $status = $trackingService->convertTrackingStatus(data_get($tracking, 'trackingStatus'));
        if ($status !== $trackingStatus->status) {
            $result = TrackingService::handleUpdateTrackingWebhook($trackingStatus, $trackingCode, $shippingCarrier, $status, $deliveredTime);
            if (!$result) {
                return $this->errorResponse();
            }
        }
        KlaviyoTrackingUpdated::dispatchIf($status === TrackingStatusEnum::DELIVERED || $status === TrackingStatusEnum::OUT_FOR_DELIVERY, $trackingCode, $status);
        return $this->successResponse();
    }
}

<?php

namespace App\Http\Controllers\Seller;

use App\Enums\UserInfoKeyEnum;
use App\Http\Controllers\Controller;
use App\Models\UserInfo;
use App\Services\IdeogramService;
use App\Services\OpenAI;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ImageGenerationController extends Controller
{
    use ApiResponse;

    protected $ideogramService;

    /**
     * Check if user has provided an Ideogram API key
     *
     * @return bool
     */
    private function hasIdeogramApiKey(): bool
    {
        $userApiKey = UserInfo::where('user_id', currentUser()->getUserId())
            ->where('key', UserInfoKeyEnum::IDEOGRAM_API_KEY)
            ->value('value');

        return !empty($userApiKey);
    }

    /**
     * Generate image from text description
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generate(Request $request)
    {
        // Check if user has provided an Ideogram API key
        if (!$this->hasIdeogramApiKey()) {
            return $this->errorResponse(
                'You need to provide your Ideogram API key in the settings before using this feature',
                403
            );
        }

        $validator = Validator::make($request->all(), [
            'prompt' => 'required|string|max:1000',
            'num_images' => 'integer|min:1|max:8',
            'aspect_ratio' => 'string|in:ASPECT_1_1,ASPECT_16_9,ASPECT_9_16,ASPECT_4_3,ASPECT_3_4,ASPECT_10_16,ASPECT_16_10,ASPECT_3_2,ASPECT_2_3,ASPECT_1_3,ASPECT_3_1',
            'model' => 'string|in:V_1,V_1_TURBO,V_2,V_2_TURBO,V_2A,V_2A_TURBO',
            'magic_prompt_option' => 'string|in:AUTO,NONE',
            'negative_prompt' => 'nullable|string|max:1000',
            'style_type' => 'nullable|string|in:AUTO,GENERAL,REALISTIC,DESIGN,RENDER_3D,ANIME',
            'seed' => 'nullable|integer|min:0|max:2147483647'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Validation error',
                422,
                [],
                ['errors' => $validator->errors()]
            );
        }

        try {
            $this->ideogramService = app(IdeogramService::class);
            $result = $this->ideogramService->generateImage(
                prompt: $request->prompt,
                numImages: $request->num_images ?? 4,
                aspectRatio: $request->aspect_ratio ?? IdeogramService::ASPECT_RATIO_16_9,
                model: $request->model ?? IdeogramService::MODEL_V2A,
                magicPromptOption: $request->magic_prompt_option ?? IdeogramService::MAGIC_PROMPT_AUTO,
                negativePrompt: $request->negative_prompt,
                styleType: $request->style_type,
                seed: $request->seed
            );

            if (!$result) {
                return $this->errorResponse('Failed to generate image', 500);
            }

            return $this->successResponse($result, 'Image generated successfully');
        } catch (\Exception $e) {
            return $this->errorResponse('An error occurred while generating the image: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Remix an existing image with new prompt
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function remix(Request $request)
    {
        // Check if user has provided an Ideogram API key
        if (!$this->hasIdeogramApiKey()) {
            return $this->errorResponse(
                'You need to provide your Ideogram API key in the settings before using this feature',
                403
            );
        }

        $validator = Validator::make($request->all(), [
            'image' => 'required|file|mimes:jpeg,png,webp|max:10240', // Max 10MB
            'prompt' => 'required|string|max:1000',
            'num_images' => 'integer|min:1|max:8',
            'aspect_ratio' => 'string|in:ASPECT_1_1,ASPECT_16_9,ASPECT_9_16,ASPECT_4_3,ASPECT_3_4,ASPECT_10_16,ASPECT_16_10,ASPECT_3_2,ASPECT_2_3,ASPECT_1_3,ASPECT_3_1',
            'model' => 'string|in:V_1,V_1_TURBO,V_2,V_2_TURBO,V_2A,V_2A_TURBO',
            'magic_prompt_option' => 'string|in:AUTO,NONE',
            'negative_prompt' => 'nullable|string|max:1000',
            'style_type' => 'nullable|string|in:AUTO,GENERAL,REALISTIC,DESIGN,RENDER_3D,ANIME',
            'seed' => 'nullable|integer|min:0|max:2147483647',
            'image_weight' => 'integer|min:1|max:100'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Validation error',
                422,
                [],
                ['errors' => $validator->errors()]
            );
        }

        try {
            $this->ideogramService = app(IdeogramService::class);
            $result = $this->ideogramService->remixImage(
                image: $request->file('image'),
                prompt: $request->prompt,
                aspectRatio: $request->aspect_ratio ?? IdeogramService::ASPECT_RATIO_16_9,
                imageWeight: $request->image_weight ?? 50,
                magicPromptOption: $request->magic_prompt_option ?? IdeogramService::MAGIC_PROMPT_AUTO,
                model: $request->model ?? IdeogramService::MODEL_V2A,
                numImages: $request->num_images ?? 1,
                negativePrompt: $request->negative_prompt,
                styleType: $request->style_type,
                seed: $request->seed
            );

            if (!$result) {
                return $this->errorResponse('Failed to remix image', 500);
            }

            return $this->successResponse($result, 'Image remixed successfully');
        } catch (\Exception $e) {
            return $this->errorResponse('An error occurred while remixing the image: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get description for an uploaded image
     *
     * @param Request $request
     * @return JsonResponse|null
     */
    public function describe(Request $request): ?JsonResponse
    {
        // Check if user has provided an Ideogram API key
        if (!$this->hasIdeogramApiKey()) {
            return $this->errorResponse(
                'You need to provide your Ideogram API key in the settings before using this feature',
                403
            );
        }

        $validator = Validator::make($request->all(), [
            'image' => 'required|file|mimes:jpeg,png,webp|max:10240', // Max 10MB
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Validation error',
                422,
                [],
                ['errors' => $validator->errors()]
            );
        }

        try {
            $this->ideogramService = app(IdeogramService::class);
            $result = $this->ideogramService->describeImage(
                image: $request->file('image')
            );

            if (!$result) {
                return $this->errorResponse();
            }

            return $this->successResponse($result);
        } catch (\Exception $e) {
            return $this->errorResponse(app()->isLocal() ? $e->getMessage() : 'An error occurred while describing the image', 500);
        }
    }

    /**
     * Get description for an uploaded image using OpenAI Vision
     *
     * @param Request $request
     * @return JsonResponse|null
     */
    public function describe2(Request $request): ?JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'image' => 'required|file|mimes:jpeg,png,webp|max:10240', // Max 10MB
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Validation error',
                422,
                [],
                ['errors' => $validator->errors()]
            );
        }

        // Check if OpenAI API key is configured
        if (!config('services.openai.api_key')) {
            return $this->errorResponse('OpenAI API key not configured on server', 500);
        }

        try {
            // Get file content and convert to base64
            $imageContent = file_get_contents($request->file('image')->getRealPath());
            $base64Image = base64_encode($imageContent);

            $result = OpenAI::analyzeImageBase64(
                base64Image: $base64Image,
                prompt: 'Describe this image in detail, concisely in under 1000 characters. Focus on the main elements and key details only.'
            );

            if (empty($result)) {
                return $this->errorResponse('Failed to analyze image');
            }

            return $this->successResponse(['description' => $result]);
        } catch (\Exception $e) {
            return $this->errorResponse(app()->isLocal() ? $e->getMessage() : 'An error occurred while analyzing the image', 500);
        }
    }

    /**
     * Upscale an image from URL
     *
     * @param Request $request
     * @return JsonResponse|null
     */
    public function upscale(Request $request): ?JsonResponse
    {
        // Check if user has provided an Ideogram API key
        if (!$this->hasIdeogramApiKey()) {
            return $this->errorResponse(
                'You need to provide your Ideogram API key in the settings before using this feature',
                403
            );
        }

        $validator = Validator::make($request->all(), [
            'image_url' => 'required|url',
            'prompt' => 'nullable|string|max:1000',
            'model' => 'nullable|string|in:V_1,V_1_TURBO,V_2,V_2_TURBO,V_2A,V_2A_TURBO',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Validation error',
                422,
                [],
                ['errors' => $validator->errors()]
            );
        }

        try {
            $this->ideogramService = app(IdeogramService::class);
            $result = $this->ideogramService->upscaleImageFromUrl(
                imageUrl: $request->image_url,
                prompt: $request->prompt,
                model: $request->model ?? IdeogramService::MODEL_V2A
            );

            // Check if result contains error message
            if (isset($result['error']) && $result['error'] === true) {
                return $this->errorResponse(
                    $result['message'] ?? 'Failed to upscale image',
                    $result['status'] ?? 500,
                    [],
                    $result['debug_info'] ?? []
                );
            }

            if (!$result) {
                return $this->errorResponse('Failed to upscale image', 500);
            }

            return $this->successResponse($result, 'Image upscaled successfully');
        } catch (\Exception $e) {
            return $this->errorResponse(
                app()->isLocal() ? $e->getMessage() : 'An error occurred while upscaling the image',
                500,
                [],
                app()->isLocal() ? ['stack_trace' => $e->getTraceAsString()] : []
            );
        }
    }
}

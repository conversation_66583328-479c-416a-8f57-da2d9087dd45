<?php

namespace App\Http\Controllers\Seller;

use App\Enums\CacheTime;
use App\Enums\OrderStatus;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;

class GettingStartedController extends Controller
{
    use ApiResponse;

    public function __invoke(): JsonResponse
    {
        $sellerId = currentUser()->getUserId();
        $cacheKey = md5('getting_started_' . $sellerId);
        $orderCount = cacheAlt()->remember($cacheKey, CacheTime::CACHE_24H, function () use ($sellerId, $cacheKey) {
            $orderCount = Order::query()
                ->where('seller_id', $sellerId)
                ->whereIn('status', [
                    OrderStatus::PROCESSING,
                    OrderStatus::COMPLETED
                ])
                ->count();
            if ($orderCount === 0) {
                cacheAlt()->forget($cacheKey);
                return 0;
            }
            return $orderCount;
        });
        return $this->successResponse(['order_count' => (int)$orderCount]);
    }
}

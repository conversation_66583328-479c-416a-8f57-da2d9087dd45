<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\CheckScamStatusEnum;
use App\Enums\DateRangeEnum;
use App\Enums\EnvironmentEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PayoutStatusEnum;
use App\Enums\PgsAndMysqlVersionEnum;
use App\Enums\ProductStatus;
use App\Enums\SalesAccountStatusEnum;
use App\Enums\SellerHistoryActionEnum;
use App\Enums\SellerNotificationActionEnum;
use App\Enums\SellerNotificationTypeEnum;
use App\Enums\SmartRemarketingEnum;
use App\Enums\SystemRole;
use App\Enums\UserInfoKeyEnum;
use App\Enums\UserRegisterTypeEnum;
use App\Enums\UserStatusEnum;
use App\Events\EmailVerifyAccountRequested;
use App\Events\ResetPasswordRequested;
use App\Exports\Admin\SellerExport;
use App\Http\Controllers\Analytic3\SellerController;
use App\Http\Requests\Admin\User\UpdateStatusRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Seller\User\SaveGeneralSettingsRequest;
use App\Http\Requests\User\UpdatePasswordRequest;
use App\Http\Requests\User\UpdatePersonalInfoRequest;
use App\Models\Campaign;
use App\Models\DeviceInfo;
use App\Models\EventLogs;
use App\Models\IndexEventLogs;
use App\Models\IpInfo;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\PasswordReset;
use App\Models\SellerBilling;
use App\Models\SellerHistory;
use App\Models\SellerNotification;
use App\Models\SellerNotificationLog;
use App\Models\User;
use App\Models\UserInfo;
use App\Services\ConfirmEmailService;
use App\Services\SaleService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use App\Traits\Contest;
use Carbon\Carbon;
use Exception;
use Firebase\JWT\JWT;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use InvalidArgumentException;
use Maatwebsite\Excel\Facades\Excel;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;
use Modules\ShopifyApp\Models\ShopifySession;
use Throwable;

class UserController extends Controller
{
    use ApiResponse, Contest;
    public function index(Request $request, string $userType, $isExported = false, $hasAnalytics = true, $removeEmptyAnalytics = false)
    {
        if ($userType === 'customers') {
            currentUser()->hasPermissionOrAbort('get_customer');
        }
        $options = [];
        $q = $request->query('q');
        $searchType = $request->query('search_type');
        $sort = $request->query('sort', 'created_at');
        $direction = $request->query('direction', 'desc');
        $storeId = $request->query('store_id');
        $country = $request->query('country');
        $hasAnalytic = $request->boolean('has_analytics', $hasAnalytics);
        $dbVersion = $request->get('db_version');
        $getLastSale = $request->get('last_sale') ?? false;
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;

        // for paginate sorting
        $page = $request->query('page', 1);

        // to protect customer data
        if ($userType === 'customers' && (int)$page > 10) {
            return $this->errorResponse('You can only view up to 10 pages', 400);
        }

        $options['page'] = $page;
        $perPage = $request->query('per_page', 15);

        $status = $request->query('status');
        $supportStaffId = $request->query('support_staff_id');
        $saleStaffId = $request->query('sale_staff_id');
        $role = $request->query('role');
        $tierId = $request->query('tier_id');
        $newSale = $request->get('new_sale');
        if (!empty($sort)) {
            $options['sort'] = [
                $sort,
                $direction
            ];
        }

        if ($searchType) {
            $options['search_type'] = $searchType;
        }

        if (!is_null($q)) {
            $options['q'] = $q;
        }

        if (!empty($status)) {
            $options['status'] = $status;
        }

        if ($supportStaffId) {
            $options['support_staff_id'] = $supportStaffId;
        }

        if ($saleStaffId) {
            $options['sale_staff_id'] = $saleStaffId;
        }

        if ($tierId) {
            $options['tier_id'] = $tierId;
        }
        if ($role) {
            $options['role'] = $role;
        }

        if (!empty($country)) {
            $options['country'] = $country;
        }

        if (!empty($storeId)) {
            $options['store_id'] = $storeId;
        }

        if ($request->has('verify_email')) {
            $options['verify_email'] = $request->get('verify_email');
        }

        if ($request->has('min_order')) {
            $options['min_order'] = $request->get('min_order');
        }

        if ($userType === 'sellers') {
            if ($request->has('seller_date_range')) {
                $options['date_range'] = $request->get('seller_date_range');
                $options['start_date'] = $request->get('seller_start_date');
                $options['end_date'] = $request->get('seller_end_date');
            }

            if ($request->has('inactive_date_range')) {
                $options['inactive_date_range'] = $request->input('inactive_date_range');
            }

            if ($request->has('fulfillment_sellers')) {
                $request->get('fulfillment_sellers') === 'true' ? $options['fulfillment_sellers'] = true : $options['fulfillment_sellers'] = false;

                if ($options['fulfillment_sellers']) {
                    $options['date_ranges'] = [
                        "column" => "paid_at",
                        "type" => $request->get('date_range', DateRangeEnum::TODAY),
                        "is_timezone" => false,
                    ];

                    if ($request->get('date_range') === DateRangeEnum::CUSTOM) {
                        $startDate = $request->get('start_date', Carbon::today()->format('Y-m-d'));
                        $endDate = $request->get('end_date', Carbon::today()->format('Y-m-d'));

                        $options['date_ranges']['range'] = [
                            $startDate,
                            $endDate
                        ];
                    }
                }
            }

            if ($request->has('new_sale')) {
                $options['ss_report_date_range'] = $request->get('ss_report_date_range') ?? $request->get('date_range') ?? DateRangeEnum::THIS_MONTH;
                $options['ss_report_start_date'] = $request->get('ss_report_start_date') ?? $request->get('start_date')  ?? null;
                $options['ss_report_end_date'] = $request->get('ss_report_end_date') ?? $request->get('end_date') ?? null;
                $options['new_sale'] = $newSale;
                $totalSaleWithOrder = $this->getTotalSalesWithOrder($options);
                if ($newSale === SalesAccountStatusEnum::SALE_ACCOUNTS) {
                    $totalSellerIds = array_column($totalSaleWithOrder->toArray(), 'seller_id');
                    $options['ids'] = $totalSellerIds;
                    if (!isset($options['sale_staff_id'])) {
                        $options['sale_staff_not_null'] = true;
                    }
                } else if ($newSale === SalesAccountStatusEnum::NEW_SALE_ACCOUNTS) {
                    $dateRangeFilter = SaleService::convertDateRange($options['ss_report_date_range'], $options['ss_report_start_date'], $options['ss_report_end_date'], false);
                    $sellerIds = array_column($totalSaleWithOrder->toArray(), 'seller_id');
                    $totalNewSales = Order::query()
                        ->select(['seller_id', 'paid_at'])
                        ->whereIn('seller_id', $sellerIds)
                        ->whereIn('payment_status', [
                            OrderPaymentStatus::PAID,
                            OrderPaymentStatus::PARTIALLY_REFUNDED,
                        ])
                        ->whereNotIn('status', [
                            OrderStatus::CANCELLED,
                            OrderStatus::REFUNDED
                        ])
                        ->filterQueryDateInPreviousRangeToCurrentEndDate($options['ss_report_date_range'], $options['ss_report_start_date'], $options['ss_report_end_date'], 'paid_at', null, false, 90)
                        ->orderByDesc('paid_at')
                        ->get()
                        ->groupBy('seller_id')
                        ->map(function ($group) use ($dateRangeFilter) {
                            $firstOrder = $group->whereBetween('paid_at', [$dateRangeFilter[0]->toDateTimeString(), $dateRangeFilter[1]->toDateTimeString()])->sortBy('paid_at')->first();

                            if (!isset($firstOrder)) {
                                return collect();
                            }

                            $closesOrderBeforeFirst = $group->where('paid_at', '<', $dateRangeFilter[0]->toDateTimeString())->sortByDesc('paid_at')->first();
                            if (!isset($closesOrderBeforeFirst)) {
                                return collect([$firstOrder]);
                            }

                            if ($closesOrderBeforeFirst->paid_at->diffInDays($firstOrder->paid_at) < 90) {
                                return collect();
                            }

                            return collect([$firstOrder, $closesOrderBeforeFirst]);
                        })
                        ->flatten();
                    $totalNewSaleArr = [];
                    foreach ($totalNewSales as $sale) {
                        $totalNewSaleArr[$sale->seller_id][] = $sale;
                    }
                    $sellerIdsWithNewSale = [];
                    foreach ($sellerIds as $sellerId) {
                        try {
                            if (count($totalNewSaleArr[$sellerId]) === 0) {
                                continue;
                            }

                            if (count($totalNewSaleArr[$sellerId]) === 1 &&  $totalNewSaleArr[$sellerId][0]->paid_at->between($dateRangeFilter[0], $dateRangeFilter[1])) {
                                $sellerIdsWithNewSale[] = $sellerId;
                            }

                            if (count($totalNewSaleArr[$sellerId]) === 2) {
                                if ($totalNewSaleArr[$sellerId][0]->paid_at->between($dateRangeFilter[0], $dateRangeFilter[1]) && $totalNewSaleArr[$sellerId][1]->paid_at->between($dateRangeFilter[0], $dateRangeFilter[1])) {
                                    $sellerIdsWithNewSale[] = $sellerId;
                                    continue;
                                }

                                if ($totalNewSaleArr[$sellerId][0]->paid_at->diffInDays($totalNewSaleArr[$sellerId][1]->paid_at) > 90) {
                                    $sellerIdsWithNewSale[] = $sellerId;
                                    continue;
                                }
                            }
                        } catch (\Exception $e) {
                        }
                    }
                    $options['ids'] = $sellerIdsWithNewSale;
                }
            }

            if ($request->has('support_staff_id')) {
                $options['ss_report_date_range'] = $request->get('ss_report_date_range') ?? $request->get('date_range') ?? DateRangeEnum::THIS_MONTH;
                $options['ss_report_start_date'] = $request->get('ss_report_start_date') ?? $request->get('start_date')  ?? null;
                $options['ss_report_end_date'] = $request->get('ss_report_end_date') ?? $request->get('end_date') ?? null;
                $totalSaleWithOrder = $this->getTotalSalesWithOrder($options, null, null, true);
                $totalSellerIds = array_column($totalSaleWithOrder->toArray(), 'seller_id');
                $options['ids'] = $totalSellerIds;
            }

            if ($request->has('join_contest')) {
                $options['confirm_join_contest'] = (int) $request->get('join_contest');
            }

            $users = User::getSellers($options, (int)$perPage, $hasAnalytic);
            $userIds = $users->pluck('id')->toArray();
            $latestOrders = Order::whereIn('seller_id', $userIds)
            ->select('seller_id', DB::raw('MAX(paid_at) as paid_at'))
            ->whereIn('payment_status', [
                OrderPaymentStatus::PAID,
                OrderPaymentStatus::PARTIALLY_REFUNDED,
            ])
            ->groupBy('seller_id')
            ->get();
            $latestOrderWithUser = optional($latestOrders->groupBy('seller_id'));
            foreach ($users as $user) {
                $user->sale_expired = false;
                $user->sales_compare_allow =  $request->get('date_range') ? $this->validateDateRangeDuration($request->get('date_range'), $request->get('start_date') ?? null, $request->get('end_date') ?? null) : true;
                $lastOrder = isset($latestOrderWithUser[$user->id]) ? $latestOrderWithUser[$user->id]->first() : null;
                $user->last_order_paid_at = $lastOrder->paid_at ?? null;
                if (isset($user->sale_expired_at, $lastOrder) && $lastOrder->paid_at > $user->sale_expired_at) {
                    $user->sale_expired = true;
                }
                unset($user->tfa_secret);
            }
            if ($hasAnalytic && $users->isNotEmpty()) {
                $this->getAnalyticOverviewBySeller($users, $perPage, $removeEmptyAnalytics, $dbVersion, $getLastSale);

                $users->loadMissing('user_balances');
                $users->map(function ($user) {
                    $user->balance_2 = 0;
                    $sellerBalance = $user->user_balances;

                    if ($sellerBalance->isNotEmpty()) {
                        $sellerBalance->map(function ($balance) use ($user) {
                            if ($balance->type === SellerBalanceTypeEnum::DEFAULT) {
                                $user->balance = (float)$balance->balance;
                            }

                            if ($balance->type === SellerBalanceTypeEnum::BALANCE_2) {
                                $user->balance_2 = (float)$balance->balance;
                            }
                        });
                    }
                });

            }

            if ($isExported) {
                return $users;
            }
            return $this->successResponse($users);
        }

        if ($userType === 'customers') {
//            $users = User::getCustomers($options, (int)$perPage);
            $users = UserService::getUserByRoleName([SystemRole::CUSTOMER], $options, (int)$perPage, true, true);
            return $this->successResponse($users);
        }

        return $this->errorResponse();
    }

    private function getAnalyticOverviewBySeller(&$users, int $perPage = 15, bool $removeEmptyAnalytics = false, $dbVersion = PgsAndMysqlVersionEnum::POSTGRES, $getLastSale = false): void
    {
        // check if only one model
        if (!isCollection($users)) {
            $users = collect([$users]);
        }

        $arrayIds = [];
        $senPoints = [];
        $columnsNeedAnalytic = CampaignSortByAllowEnum::getArrayForAnalyticOrder();
        $needSenPoint = true;

        foreach ($users as $user) {
            $arrayIds[] = $user->id;

            // remove column need analytic if it has value
            $dump = $user->toArray();
            foreach ($columnsNeedAnalytic as $index => $column) {
                if (array_key_exists($column, $dump)) {
                    unset($columnsNeedAnalytic[$index]);
                }
            }
            if (array_key_exists(CampaignSortByAllowEnum::ORDERS_SEN_POINTS, $dump)) {
                $needSenPoint = false;
            }
        }

        $filterRequest = new Request();
        $filterRequest['date_type'] = request('date_range');
        $filterRequest['start_date'] = request('start_date');
        $filterRequest['end_date'] = request('end_date');
        // 0: get all
        if ($perPage !== 0) {
            $filterRequest['seller_id'] = $arrayIds;
        }
        $controller = new SellerController();
        $controller->setCommonFilter($filterRequest);
        $analytics = Order::query()
            ->analytic($columnsNeedAnalytic)
            ->addSelect('order.seller_id')
            ->addFilterAnalytic($controller->arrFilter, $controller->dateRanges)
            ->whereIn('payment_status', [
                OrderPaymentStatus::PAID,
                OrderPaymentStatus::PARTIALLY_REFUNDED,
            ])
            ->groupBy('order.seller_id')
            ->get();

        $orderIncludeDraft = Order::query()
            ->selectRaw('count(order.id) as all_orders')
            ->addFilterAnalytic($controller->arrFilter, $controller->dateRanges, null, [], 'order.updated_at')
            ->addSelect(['order.seller_id', 'order.status'])
            ->where('type', '!=', OrderTypeEnum::SERVICE)
            ->whereNotIn('order.status', [OrderStatus::DRAFT, OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::DELETED])
            ->groupBy('order.seller_id')
            ->get();
        $visits = collect();
        if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
            $visits = EventLogs::query()
                ->calculateCount('visit')
                ->addSelect('seller_id')
                ->filterVisit()
                ->addFilterAnalytic($controller->arrFilter, $controller->dateRanges)
                ->groupBy('seller_id')
                ->get();
        } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
            $visits = IndexEventLogs::query()
                ->calculateCount('visit')
                ->addSelect('seller_id')
                ->filterVisit()
                ->addFilterAnalytic($controller->arrFilter, $controller->dateRanges)
                ->groupBy('seller_id')
                ->get();
        }

        if ($needSenPoint) {
            $order = Order::query()
                ->select('id')
                ->addFilterAnalytic([], $controller->dateRanges)
                ->isValidPaidOrder();

            $senPoints = OrderProduct::query()
                ->select('order_product.seller_id')
                ->selectRaw("sum(order_product.sen_points) as current_sen_points")
                ->where('order_product.fulfill_status', '!=', OrderProductFulfillStatus::CANCELLED)
                ->joinSub($order, 'order', function ($join) {
                    $join->on('order.id', '=', 'order_product.order_id');
                })
                ->when($perPage !== 0, function ($query) use ($arrayIds) {
                    $query->whereIn('order_product.seller_id', $arrayIds);
                })
                ->groupBy('order_product.seller_id')
                ->get();
        }

        $analyticsWithDateRangeBeforeFilter = [];
        if ($getLastSale) {
            $analyticsWithDateRangeBeforeFilter = Order::query()
                ->analyticWithFilterDateRangeBefore($columnsNeedAnalytic, $controller->dateRanges)
                ->addSelect('order.seller_id')
                ->whereIn('payment_status', [
                    OrderPaymentStatus::PAID,
                    OrderPaymentStatus::PARTIALLY_REFUNDED,
                ])
                ->addFilterAnalyticBeforeDateRange($controller->arrFilter, $controller->dateRanges)
                ->groupBy('order.seller_id')
                ->get();
        }


        // mapping

        $analyticMapping = [];
        foreach ($analytics as $indexAnalytic => $analytic) {
            $analyticMapping[$analytic->seller_id] = $analytic;
            unset($analytics[$indexAnalytic]);
        }
        foreach ($orderIncludeDraft as $index => $analytic) {
            if (!isset($analyticMapping[$analytic->seller_id])) {
                $analyticMapping[$analytic->seller_id] = $analytic;
            } else {
                $analyticMapping[$analytic->seller_id]->all_orders = $analytic->all_orders;
            }
            unset($orderIncludeDraft[$index]);
        }

        $analyticsWithDateRangeBeforeFilterMapping = [];
        if ($getLastSale) {
            foreach ($analyticsWithDateRangeBeforeFilter as $indexAnalytic => $analytic) {
                $analyticsWithDateRangeBeforeFilterMapping[$analytic->seller_id] = $analytic;
                unset($analyticsWithDateRangeBeforeFilter[$indexAnalytic]);
            }
        }

        $visitMapping = [];
        foreach ($visits as $indexVisit => $visit) {
            $visitMapping[$visit->seller_id] = $visit;
            unset($visits[$indexVisit]);
        }

        $senPointMapping = [];
        foreach ($senPoints as $indexSenPoint => $senPoint) {
            $senPointMapping[$senPoint->seller_id] = $senPoint;
            unset($senPoints[$indexSenPoint]);
        }

        foreach ($users as $indexSeller => $user) {
            if (isset($analyticMapping[$user->id])) {
                foreach ($analyticMapping[$user->id]->getAttributes() as $key => $value) {
                    if ($key === 'status') {
                        continue;
                    }
                    $user->$key = $value;
                }
                unset($analyticMapping[$user->id]->seller_id);
            }

            if ($getLastSale && isset($analyticsWithDateRangeBeforeFilterMapping[$user->id])) {
                foreach ($analyticsWithDateRangeBeforeFilterMapping[$user->id]->getAttributes() as $key => $value) {
                    $user->$key = $value;
                }
                unset($analyticsWithDateRangeBeforeFilterMapping[$user->id]->seller_id);
            }
            // remove empty analytic
            if ($removeEmptyAnalytics) {
                unset($users[$indexSeller]);
                continue;
            }

            if (isset($visitMapping[$user->id])) {
                foreach ($visitMapping[$user->id]->getAttributes() as $key => $value) {
                    $user->$key = (int) $value;
                }
                $visitInfo = data_get($visitMapping, $user->id);
                $user->visit = data_get($visitInfo, 'visit', 0) + data_get($visitInfo, 'total_sum', 0);
                unset($visitMapping[$user->id]->seller_id);
            }

            if (isset($senPointMapping[$user->id])) {
                $user->{CampaignSortByAllowEnum::ORDERS_SEN_POINTS} = $senPointMapping[$user->id]->current_sen_points;
            }
        }
    }

    public function saveUserInfo(Request $request): JsonResponse
    {
        $inputs = $request->all();
        $user_id = currentUser()->getUserId();
        foreach ($inputs as $key => $value) {
            if (!in_array($key, UserInfoKeyEnum::asArray())) {
                return $this->errorResponse("$key is not valid key");
            }
            UserInfo::query()->updateOrCreate(
                [
                    'user_id' => $user_id,
                    'key' => $key
                ],
                [
                    'value' => !empty($value) ? $value : null
                ]
            );
            Cache::forget(CacheKeys::getSettingKey($user_id, $key));
        }

        return $this->successResponse();
    }

    /**
     * Get user info
     *
     * @param Request $request
     * @param string|null $userInfoKey
     * @return JsonResponse
     */
    public function getUserInfo(Request $request, ?string $userInfoKey = null): JsonResponse
    {
        $auth = currentUser();
        $queryUserId = $request->query('user_id');
        $userId = $auth->getUserId();

        if ($auth->isAdmin()) {
            $userId = $queryUserId;
        }

        if (is_null($userId)) {
            return $this->errorResponse('user_id not found');
        }

        $query = UserInfo::query()
            ->select('key', 'value')
            ->where('user_id', $userId);

        if (!is_null($userInfoKey)) {
            $data = $query->where('key', $userInfoKey)->value('value');
            return $this->successResponse($data);
        }

        $data = [];
        $query->get()->each(function ($item) use (&$data) {
            $data[$item['key']] = Str::isJson($item['value']) ? json_decode($item['value']) : $item['value'];
        });

        $user = User::query()->select(['smart_remarketing', 'id', 'name', 'tfa_enable', 'tfa_secret', 'register_type'])->findOrFail($userId);
        $data['smart_remarketing'] = (int) $user?->smart_remarketing;
        $data['tfa_key_generated'] = (bool) $user?->tfa_secret;
        $data['tfa_enable'] = $data['tfa_key_generated'] ? $user->tfa_enable : false;
        $data['qr_code'] = $data['tfa_key_generated'] && !$data['tfa_enable'] ? $auth->getTfaQrCode() : null;
        $data['register_type'] = $user->register_type ?? UserRegisterTypeEnum::SYSTEM;

        return $this->successResponse($data);
    }

    /**
     * Get general settings of current user
     *
     * @return JsonResponse
     */
    public function getGeneralSettings(): JsonResponse
    {
        $result = User::query()
            ->select('timezone', 'currency', 'theme', 'klaviyo_public_key', 'klaviyo_private_key', 'klaviyo_list_id')
            ->firstWhere('id', currentUser()->getUserId());

        return $result ? $this->successResponse($result) : $this->errorResponse();
    }

    public function saveGeneralSettings(SaveGeneralSettingsRequest $request): JsonResponse
    {
        try {
            $id = currentUser()->getUserId();
            $user = User::query()->findOrFail($id);
            $dataUpdate = $request->validated();
            if ($this->hasActiveContest() && $this->isActiveContestNeedConfirmToJoin()) {
                if ($request->has('confirm_join_contest') && !$this->canJoinContest($user)) {
                    $request->offsetUnset('confirm_join_contest');
                }
                if ($request->has('confirm_join_contest')) {
                    $message = $request->boolean('confirm_join_contest') ? "You have joined the contest at " . now()->format('Y-m-d H:i:s') : "You have left the contest at " . now()->format('Y-m-d H:i:s');
                    SellerHistory::query()->insert(array(
                        'seller_id' => $id,
                        'action' => SellerHistoryActionEnum::UPDATE_ACCOUNT,
                        'details' => $message,
                    ));
                }
            }
            $user->fill($dataUpdate);
            $user->save();

            if ($user->wasChanged([
                'timezone',
                'utc_offset',
            ])) {
                $cache['tags'][] = 'seller_' . $id;
                syncClearCache($cache);
            }

            if (isset($dataUpdate['klaviyo_private_key'])) {
                try {
                    $service = new KlaviyoService([
                        'private_key' => $dataUpdate['klaviyo_private_key'],
                    ]);
                    $service->initMetrics(sellerId: $id);
                } catch (\Throwable $e) {
                    return $this->errorResponse('Invalid Klaviyo private key', 400);
                }
            }
            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    /**
     * @throws Exception
     */
    private function canJoinContest(User $user): bool
    {
        $rawTime = $this->getRegistrationContestTime();
        $registrationContestDate = $rawTime ? Carbon::parse($rawTime) : null;
        $createdAt = $user->created_at ? Carbon::parse($user->created_at) : null;
        return !$registrationContestDate || $registrationContestDate->gt(now()) || ($createdAt && $createdAt->gt($registrationContestDate));
    }

    /**
     * Update user password
     *
     * @param UpdatePasswordRequest $request
     * @return JsonResponse
     */
    public function updatePassword(UpdatePasswordRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $newPassword = $validated['new_password'];
        $updated = User::query()
            ->where('id', currentUser()->getUserId())
            ->update([
                'password' => Hash::make($newPassword)
            ]);

        return $updated > 0 ? $this->successResponse() : $this->errorResponse();
    }

    /**
     * Update basic info
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateBasicInfo(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'bail|required|string',
            'nickname' => 'nullable|string',
            'contest_name' => 'nullable|string',
            'avatar' => 'nullable|string',
        ]);

        $updated = User::query()
            ->where('id', currentUser()->getUserId())
            ->update([
                'name' => $request->post('name'),
                'nickname' => $request->post('nickname'),
                'contest_name' => $request->post('contest_name'),
                'avatar' => $request->post('avatar')
            ]);

        return $updated > 0 ? $this->successResponse() : $this->errorResponse();
    }

    public function getUserDetail(string $type, $userId)
    {
        try {
            switch ($type) {
                case 'sellers':
                    currentUser()->hasPermissionOrAbort('get_users');

                    $user = User::getSellerById($userId)
                        ->with('billings')
                        ->firstOrFail();

                    // default date range lifetime for analytic
                    // request()->request->add(['date_range' => DateRangeEnum::LAST_30_DAYS]);
                    // $this->getAnalyticOverviewBySeller($user);

                    return $user;

                case 'customers':
                    currentUser()->hasPermissionOrAbort('get_orders');

                    return User::getById('customer', $userId)
                        ->with('addresses')
                        ->withSum('customer_orders as total_amount', 'total_amount')
                        ->firstOrFail();
            }
        } catch (Exception $e) {
            return null;
        }

        return null;
    }

    /**
     * Get Seller transactions
     * @param $sellerId
     * @return LengthAwarePaginator
     */
    public function getSellerTransactions($sellerId): LengthAwarePaginator
    {
        return SellerBilling::query()
            ->where('seller_id', $sellerId)
            ->where('balance_type', SellerBalanceTypeEnum::DEFAULT)
            ->orderByDesc('id')
            ->paginate(15);
    }


    /**
     * Get Seller History
     * @param $sellerId
     * @return LengthAwarePaginator
     */
    public function getSellerHistories($sellerId): LengthAwarePaginator
    {
        return SellerHistory::query()
            ->with(['campaign', 'order', 'staff'])
            ->where('seller_id', $sellerId)
            ->orderByDesc('id')
            ->paginate(15);
    }

    /**
     * Update personal info of seller
     *
     * @param UpdatePersonalInfoRequest $request
     * @return JsonResponse
     */
    public function updatePersonalInfo(UpdatePersonalInfoRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $dataUpdated = $request->validated();
            $sellerId = currentUser()->getUserId();
            $seller = currentUser()->getInfoAccess();
            $seller->fill($dataUpdated);
            if (data_get($dataUpdated, 'facebook', $seller->facebook) && data_get($dataUpdated, 'phone', $seller->phone) && $seller->tier_id <= 2) {
                $logTierUpdatedByKyc = SellerHistory::query()
                    ->where('seller_id', $sellerId)
                    ->where('action', SellerHistoryActionEnum::UPDATE_TIER_BY_KYC)
                    ->exists();
                if (!$logTierUpdatedByKyc) {
                    $seller->tier_id = 2;
                    SellerHistory::query()->create([
                        'tier_id' => 2,
                        'seller_id' => $seller->id,
                        'action' => SellerHistoryActionEnum::UPDATE_TIER_BY_KYC,
                        'details' => "Your account has been upgraded to Tier 2",
                    ]);
                }
            }
            $updated = $seller->save();
            if($updated) {
                DB::commit();
                return $this->successResponse();
            }
            DB::rollBack();
            return $this->errorResponse();
        }
        catch (Exception $e) {
            DB::rollBack();
            logException($e);
            return $this->errorResponse();
        }
    }

    /**
     * Get personal info
     *
     * @return JsonResponse
     */
    public function getPersonalInfo(): JsonResponse
    {
        $data = User::query()
            ->select([
                'birthday',
                'facebook',
                'address',
                'city',
                'state',
                'postcode',
                'country',
                'phone',
                'sale_volume',
                'smart_remarketing'
            ])
            ->firstWhere('id', currentUser()->getUserId());
        $cdnUrl = (new SystemConfigController())->getAffiliateWidgetCdn();
        $data->affiliate_widget_cdn = $cdnUrl;
        return $data ? $this->successResponse($data) : $this->errorResponse();
    }

    public function updateStatus(UpdateStatusRequest $request, $id): JsonResponse
    {
        try {
            $currentUser = currentUser();
            $status = $request->json('status');
            $user = User::query()->firstWhere('id', $id);

            if (!$user) {
                return $this->errorResponse('User not found');
            }

            if (in_array($status, [UserStatusEnum::VERIFIED, UserStatusEnum::TRUSTED]) && !$user->facebook && !$user->phone) {
                return $this->errorResponse('User must have Facebook or phone number');
            }

            $oldStatus = $user->status;

            if (!$user->update(['status' => $status])) {
                throw new \RuntimeException('Can\'t update this user id: ' . $id);
            }

            $user->roles()->detach();
            $status = strtolower($status);

            if (in_array($status, [UserStatusEnum::HARD_BLOCKED, UserStatusEnum::SOFT_BLOCKED])) {
                $user->assignRole(SystemRole::BLOCKED);
                // if hard locked then set all their campaign to inactive
                $query = Campaign::query()
                    ->onSellerConnection($user)
                    ->where([
                        'seller_id' => $id,
                        'status' => ProductStatus::ACTIVE
                    ]);
                $products = $query->limit(1000)->get('slug');
                $this->clearCacheProducts($products);
                clearHtmlCacheSeller($user->id);

                if ($status === UserStatusEnum::HARD_BLOCKED) {
                    $userLogs = $user->userLogs;
                    $ipAddresses = $userLogs->pluck('ip_address')->toArray();
                    $deviceIds = $userLogs->pluck('device_id')->toArray();

                    if (!empty($ipAddresses)) {
                        IpInfo::whereIn('ip_address', $ipAddresses)->update(['status' => CheckScamStatusEnum::FLAGGED]);
                    }

                    if (!empty($deviceIds)) {
                        DeviceInfo::whereIn('device_id', $deviceIds)->update(['status' => CheckScamStatusEnum::FLAGGED]);
                    }
                }
            } else {
                $user->assignRole(SystemRole::SELLER);
            }

            if (in_array($status, [UserStatusEnum::VERIFIED, UserStatusEnum::TRUSTED])) {
                if (in_array($oldStatus, [UserStatusEnum::HARD_BLOCKED, UserStatusEnum::SOFT_BLOCKED])) {
                    $query = Campaign::query()
                        ->onSellerConnection($user)
                        ->where([
                            'seller_id' => $id,
                            'status' => ProductStatus::ACTIVE
                        ]);
                    $products = $query->limit(1000)->get('slug');
                    $this->clearCacheProducts($products);
                    clearHtmlCacheSeller($user->id);
                }
            }
            $message = "The staff member \"" . ($currentUser->getName() ?? $currentUser->getEmail()) . "\" has changed seller status: ";
            $action = SellerHistoryActionEnum::UPDATE_ACCOUNT;
            if (in_array($status, [UserStatusEnum::SOFT_BLOCKED, UserStatusEnum::HARD_BLOCKED], true)) {
                $action = SellerHistoryActionEnum::BLOCK_ACCOUNT;
            }
            $message .= ' from "' . $oldStatus . '" to "' . $status . '"';
            SellerHistory::query()->insert(array(
                'seller_id' => $id,
                'action' => $action,
                'details' => $message,
                'seller_status' => $status,
                'staff_id' => optional($currentUser->getInfo())->id
            ));

            // hold payout
            if (UserStatusEnum::checkLimitedStatus($status)) {
                SellerBilling::query()
                    ->where([
                        'seller_id' => $id,
                        'status' => PayoutStatusEnum::PENDING
                    ])
                    ->update(['status' => PayoutStatusEnum::ON_HOLD]);
            }

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    private function clearCacheProducts($products): void
    {
        if ($products) {
            $cacheKeys = [];
            foreach ($products as $product) {
                $cacheKeys[] = CacheKeys::PRODUCT_PREFIX . $product->slug;
            }
            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
        }
    }

    public function forgotPassword(Request $request): JsonResponse
    {
        $request->validate(['email' => 'required|email']);
        $email = $request->email;

        try {
            $user = User::query()->firstWhere('email', $email);

            if ($user === null) {
                // we return success response to prevent bruce-force
                // todo: use recaptcha instead
                return $this->successResponse();
            }

            $count = PasswordReset::query()
                ->where('email', $email)
                ->timeLimit()
                ->count();

            if ((app()->environment(EnvironmentEnum::PRODUCTION))
                && $count >= config('auth.passwords.users.limit_per_try')
            ) {
                return $this->errorResponse('You have reached limit reset password. Please try again in ' . PasswordReset::LIMIT_HOUR . ' hours.');
            }

            ResetPasswordRequested::dispatch($user);

            return $this->successResponse();
        } catch (Exception $e) {
            logToDiscord("{$email} have reset password failed.");
            return $this->errorResponse();
        }
    }

    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        try {
            $token = $request->post('token');
            $password = $request->post('password');
            $email = PasswordReset::timeLimit()
                ->where('token', $token)
                ->value('email');

            PasswordReset::query()
                ->where('email', $email)
                ->update(['token' => null]);

            // update password and verify if hadn't
            $user = User::query()->firstWhere('email', $email);

            if (is_null($user)) {
                return $this->errorResponse();
            }

            $user->password = Hash::make($password);

            if (is_null($user->email_verified_at)) {
                $user->email_verified_at = now();
            }

            $user->save();

            return $this->successResponse();
        } catch (Exception $e) {
            logToDiscord('Reset password failed: ' . $e->getMessage());
            return $this->errorResponse();
        }
    }

    public function verifyAccount($hash)
    {
        $redirectLink = isEnvLocalOrDev() ? 'https://seller-v2.dev.senprints.net' : 'https://seller.senprints.com';

        $seller = ConfirmEmailService::sellerLookup($hash);
        if ($seller->custom_payment) {
            $redirectLink = isEnvLocalOrDev() ? 'https://seller-center.dev.senprints.net' : 'https://seller.storehelp.net';
        }

        try {
            ConfirmEmailService::confirm($hash);
            return redirect()->to($redirectLink . '?verify_email=success');
        } catch (Throwable $e) {
            ConfirmEmailService::log($e->getMessage());
            return redirect()->to($redirectLink . '/auth/verify-email?error=' . base64_encode($e->getMessage()));
        }
    }

    public function requestVerifyEmail()
    {
        $auth = currentUser();
        $info = User::query()->find($auth->getUserId());
        if (!$auth->isSeller() || !$info || $info->email_verified_at) {
            return $this->errorResponse('You should not be here');
        }
        if (ConfirmEmailService::hasReachedLimit($info->id)) {
            return $this->errorResponse('You have reached limit request verify email. Please try again in next 24 hours.');
        }
        EmailVerifyAccountRequested::dispatch($info);
        return $this->successResponse();
    }

    public function getNotifyDetail(int $id): JsonResponse
    {
        $sellerId = currentUser()->getUserId();
        $result = SellerNotification::where('id', $id)
            ->where(function ($query) use ($sellerId) {
                $query->where('seller_id', $sellerId);
                $query->orWhereNull('seller_id');
            })
            ->with(['logs', 'admin'])
            ->first();

        if ($result !== null) {
            return $this->successResponse($result);
        }

        return $this->errorResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getNotify(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'per_page' => ['integer', 'min:1', 'max:60']
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        $userId = currentUser()->getUserId();
        $perPage = $request->get('per_page');
        $query = SellerNotification::query()
            ->where('status', 1)
            ->where('expiry_date', '>=', currentTime())
            ->where(function ($query) use ($userId) {
                $query->where('seller_id', $userId);
                $query->orWhereNull('seller_id');
                $query->orWhere('seller_id', 0);
            })
            ->where(function ($q) {
                $q->where(function ($q1) {
                    $q1->where('type', SellerNotificationTypeEnum::DEFAULT)
                        ->whereDoesntHave('logs', function ($q2) {
                            $q2->where('seller_id', currentUser()->getUserId())
                                ->where('action', SellerNotificationActionEnum::READ);
                        });
                })
                    ->orWhere(function ($q1) {
                        $q1->where('type', SellerNotificationTypeEnum::ONE_TIME)
                            ->whereDoesntHave('logs', function ($q2) {
                                $q2->where('seller_id', currentUser()->getUserId())
                                    ->whereIn('action', SellerNotificationActionEnum::seenLevel());
                            });
                    });
            })
            ->with(['logs']);

        if (currentUser()->isAdmin()) {
            $query->withCount(['logs as total_clicks' => function ($query) {
                $query->where('action', SellerNotificationActionEnum::CLICKED);
            }]);
        }

       $query->orderByDesc('created_at');

        if ($perPage) {
            $results = $query->cursorPaginate($perPage);
        } else {
            $results = $query->get();
            $results->each(function ($notification) {
                $notification->append('call_to_action_link');
            });
        }

        $results->makeHidden(['logs']);
        return $this->successResponse($results);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function postReadStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'notification_ids' => ['required', 'max:15', 'min:1', 'array'],
            'action' => 'in:1,2'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $action = $request->post('action', SellerNotificationActionEnum::READ);

        // logged by admin cannot mark as seen
        if ($action === SellerNotificationActionEnum::SEEN && currentUser()->isLoggedAsByAdmin()) {
            return $this->successResponse();
        }

        $sellerId = currentUser()->getUserId();
        $notificationIds = $request->post('notification_ids', []);
        collect($notificationIds)->map(function ($notificationId) use ($sellerId, $action) {
            if (!is_null($notificationId)) {
                $exits = SellerNotificationLog::query()
                    ->where('seller_id', $sellerId)
                    ->where('notification_id', $notificationId)
                    ->when($action === SellerNotificationActionEnum::SEEN, function ($query) {
                        $query->whereIn('action', SellerNotificationActionEnum::seenLevel());
                    })
                    ->when($action === SellerNotificationActionEnum::READ, function ($query) {
                        $query->where('action', SellerNotificationActionEnum::READ);
                    })
                    ->exists();

                if (!$exits) {
                    SellerNotificationLog::query()->updateOrCreate([
                        'seller_id' => $sellerId,
                        'notification_id' => $notificationId,
                    ], [
                        'updated_at' => now()->toDateTimeString(),
                        'action' => $action
                    ]);
                }
            }
        });

        return $this->successResponse();
    }

    public function export(Request $request, $type)
    {
        if ($type === 'sellers') {
            currentUser()->hasPermissionOrAbort('get_users');
        }

        try {
            $hasAnalytics = $request->boolean('has_analytics');
            $data = $this->index($request, $type, true, $hasAnalytics, false);
            $fileName = 'export_' . $type . date('Y-m-d H:i:s') . '.csv';

            if ($type === 'sellers') {
                return Excel::download(new SellerExport($data, $hasAnalytics), $fileName);
            }

            return $this->errorResponse();
        } catch (Throwable $e) {
            logException($e);
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param mixed $sellerId
     * @return mixed
     * @throws InvalidArgumentException
     */
    public static function sellerSmsCredit($sellerId)
    {
        $user = User::query()
            ->select(['id', 'sms_credit'])
            ->firstWhere('id', $sellerId);

        return $user->sms_credit ?? 0;
    }

    public function updateUserTags(Request $request, $userId): JsonResponse
    {
        $tags = $request->post('tags');
        $tags = implode(',', $tags);

        $updated = User::query()
            ->where('id', $userId)
            ->update(['tags' => $tags]);

        if ($updated) {
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function toggleEmailNotification(Request $request): JsonResponse
    {
        $data = $request->validate(['value' => 'required|boolean']);

        try {
            UserInfo::updateOrCreate(
                [
                    'user_id' => currentUser()->getUserId(),
                    'key' => UserInfoKeyEnum::EMAIL_NOTIFICATION_NEW_ORDER
                ],
                ['value' => ($data['value']) ? 1 : 0]
            );

            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e);
            return $this->errorResponse();
        }
    }

    public function rememberInfo(Request $request): JsonResponse
    {
        $key = $request->get('key');
        $value = $request->get('value');

        if (!in_array($key, UserInfoKeyEnum::asArray())) {
            return $this->errorResponse("$key is not valid key");
        }

        if (empty($value)) {
            return $this->errorResponse('Value is required');
        }

        if (is_array($value) || is_object($value)) {
            $value = json_encode($value);
        }

        try {
            UserInfo::updateOrCreate(
                [
                    'user_id' => currentUser()->getUserId(),
                    'key' => $key
                ],
                ['value' => $value]
            );

            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e);
            return $this->errorResponse();
        }
    }

    public function getInfoByKey(Request $request): JsonResponse
    {
        $key = $request->get('key');

        if (!in_array($key, UserInfoKeyEnum::asArray())) {
            return $this->errorResponse("$key is not valid key");
        }

        try {
            $value = UserInfo::query()
                ->where('user_id', currentUser()->getUserId())
                ->where('key', $key)
                ->value('value');

            if (empty($value)) {
                return $this->successResponse($value);
            }

            $value = json_decode($value);
            return $this->successResponse($value);
        } catch (Throwable $e) {
            logException($e);
            return $this->errorResponse();
        }
    }

    public function enabledSmartRemarketing(Request $request): JsonResponse
    {
        $request->validate([
            'status' => [
                'required',
                Rule::in(SmartRemarketingEnum::asArray())
            ]
        ]);
        $status = $request->get('status');

        User::query()
            ->where('id', currentUser()->getUserId())
            ->update(['smart_remarketing' => $status]);

        return $this->successResponse(null, ($status === SmartRemarketingEnum::DISABLED ? 'Disabled' : 'Enabled') . ' smart remarketing success!');
    }

    public function createCannyToken(): JsonResponse
    {
        $user = currentUser()->getInfo();

        if (!$user) {
            return $this->errorResponse();
        }

        $privateKey = config('services.canny.private_key');
        $token = JWT::encode([
            'avatarURL' => $user->avatar, // optional
            'email' => $user->email,
            'id' => $user->id,
            'name' => $user->name,
        ], $privateKey, 'HS256');

        return $this->successResponse(['token' => $token]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function connectPlatform(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'state' => 'required',
            'shop' => 'required',
            'platform' => 'required|in:shopify,amazon',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        $state = $request->input('state');
        $shop = $request->input('shop');
        $platform = $request->input('platform');
        $updated = true;
        if ($platform === 'shopify') {
            $shopify_session = ShopifySession::query()->where('shop', $shop)->where('state', $state)->first();
            if ($shopify_session && empty($shopify_session->seller_id)) {
                $shopify_session->seller_id = currentUser()->getUserId();
                $shopify_session->updated_at = currentTime();
                if (!$shopify_session->save()) {
                    $updated = false;
                }
            }
        }
        return $updated ? $this->successResponse() : $this->errorResponse();
    }

    public function getTotalSalesWithOrder($data, $userWithMonthRange = null, $userWithSubmonth = null, $isSaleSupportQuery = null)
    {
        $dateRange = $data['ss_report_date_range'] ?? DateRangeEnum::THIS_MONTH;
        $startDate = $data['ss_report_start_date'] ?? null;
        $endDate = $data['ss_report_end_date'] ?? null;
        $subQuery = Order::query()
            ->select([
                'seller_id',
                'total_amount',
                'total_quantity',
                'paid_at',
            ])
            ->whereIn('payment_status', [
                OrderPaymentStatus::PAID,
                OrderPaymentStatus::PARTIALLY_REFUNDED,
            ])
            ->whereNotIn('status', [
                OrderStatus::CANCELLED,
                OrderStatus::REFUNDED,
            ]);
        if (isset($userWithMonthRange)) {
            $subQuery = $subQuery->filterQueryDateInRangeWithRound($dateRange, $startDate, $endDate, 'paid_at', null, false, $userWithMonthRange, $userWithSubmonth);
        } else {
            $subQuery = $subQuery->filterDateRange($dateRange, $startDate, $endDate, 'paid_at', null, false, null, true);
        }
        $users = User::query()
            ->select([
                'id as seller_id',
            ])
            ->joinSub($subQuery, 'order', function ($j) use ($isSaleSupportQuery) {
                $j->on('order.seller_id', 'user.id');
                if (!isset($isSaleSupportQuery) || !$isSaleSupportQuery) {
                    $j->where(function ($q) {
                        $q->orWhereNull('user.sale_expired_at');
                        $q->orWhereRaw('order.paid_at < user.sale_expired_at');
                    });
                }
            })
            ->whereNotNull('support_staff_id')
            ->orWhereNotNull('sale_staff_id');
        return $users->groupBy('user.id')->get();
    }

    public function validateDateRangeDuration($dateRange, $startDate = null, $endDate = null)
    {
        $result = true;
        if (in_array($dateRange, [DateRangeEnum::THIS_MONTH, DateRangeEnum::TODAY, DateRangeEnum::THIS_WEEK, DateRangeEnum::CUSTOM])) {
            $currentTime = Carbon::now()->setTimezone('Asia/Ho_Chi_Minh');
            switch ($dateRange) {
                case (DateRangeEnum::TODAY):
                    $startTime = $currentTime->copy()->startOfDay();
                    $diffTimeAllow = 12;
                    break;
                case (DateRangeEnum::THIS_MONTH):
                    $startTime = $currentTime->copy()->startOfMonth();
                    $diffTimeAllow = 15 * 24;
                    break;
                case (DateRangeEnum::THIS_WEEK):
                    $startTime = $currentTime->copy()->startOfWeek();
                    $diffTimeAllow = 3.5 * 24;
                    break;
                case (DateRangeEnum::CUSTOM):
                    $startTime = Carbon::parse($startDate);
                    $diffTimeAllow = Carbon::parse($endDate)->diffInHours($startTime);
                    break;
            }

            if ($currentTime->diffInHours($startTime) > $diffTimeAllow) {
                $result = true;
            } else {
                $result = false;
            }
        }
        return $result;
    }
}

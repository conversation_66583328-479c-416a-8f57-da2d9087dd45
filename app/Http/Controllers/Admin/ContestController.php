<?php

namespace App\Http\Controllers\Admin;

use App\Enums\ContestStatusEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\UserRoleEnum;
use App\Http\Controllers\Controller;
use App\Models\Contest;
use App\Models\OrderProduct;
use App\Models\User;
use App\Traits\ApiResponse;
use App\Traits\Contest as ContestTrait;
use DateTime;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Exports\Admin\SellerParticipatingContest;
use Maatwebsite\Excel\Facades\Excel;

class ContestController extends Controller
{
    use ApiResponse, ContestTrait;

    /**
     * @param Request $request
     * @return mixed
     * @throws \Exception
     */
    public function report(Request $request): mixed
    {
        // Note: maybe need to update for future contests
        $minPoints = 300;
        $isExport = false;

        // ref: SDV-3303
        if ($request->has('no_limit')) {
            $minPoints = 1;
        }
        if ($request->has('export')) {
            $minPoints = 1;
            $isExport = true;
        }
        $sellerSenPoints = OrderProduct::query()
            ->select('seller_id')
            ->selectRaw("sum(sen_points) as sen_points")
            ->where('fulfill_status', '!=', OrderProductFulfillStatus::CANCELLED)
            ->whereNotNull('seller_id')
            ->when($this->hasActiveContest(), function ($query) {
                $query->whereHas('order', function ($q) {
                    $q->where('paid_at', '>=', $this->contestStartDate());
                    $q->where('paid_at', '<=', $this->contestEndDate());
                    $q->isValidPaidOrder();
                });
            })
            ->when($this->isActiveContestNeedConfirmToJoin(), function ($query) {
                $sellerIds = User::query()
                    ->select('id')
                    ->where('confirm_join_contest', 1)
                    ->where('role', '!=', UserRoleEnum::CUSTOMER)
                    ->get()
                    ->pluck('id')
                    ->toArray();
                if (count($sellerIds) > 0) {
                    $query->whereIn('seller_id', $sellerIds);
                }
            })
            ->groupBy('seller_id')
            ->having('sen_points', '>=', $minPoints)
            ->orderByDesc('sen_points')
            ->get();

        $sellerIds = $sellerSenPoints->pluck('seller_id')->toArray();
        if (empty($sellerIds)) {
            return $this->errorResponse();
        }
        $sellers = User::query()
            ->select([
                'id',
                'name',
                'email',
                'nickname',
                'contest_name',
                'avatar',
                'ref_id',
                'status',
                'confirm_join_contest'
            ])
            ->whereKey($sellerIds)
            ->with('referrer:id,name,email,nickname,contest_name')
            ->get();

        if ($sellers->isEmpty()) {
            return $this->errorResponse();
        }

        $sellers = $sellers->transform(function ($user) use ($sellerSenPoints) {
            $user->sen_points = $sellerSenPoints->firstWhere('seller_id', $user->id)?->sen_points ?? 0;
            return $user;
        })->sortByDesc('sen_points')->values();

        if ($isExport) {
            return Excel::download(new SellerParticipatingContest($sellers), 'contest-report.xlsx');
        }

        return $this->successResponse($sellers);
    }

    public function index(Request $request): JsonResponse
    {
        $status = $request->get('status');
        $perPage = $request->get('per_page', 15);

        $query = \App\Models\Contest::query();
        if (!empty($status)) {
            if ($status === 'active') {
                $query->whereIn('status', [ContestStatusEnum::ACTIVE, ContestStatusEnum::MAIN]);
            } elseif ($status === 'inactive') {
                $query->where('status', 0);
            }
        }

        $contest = $query
            ->orderBy('status', 'desc')
            ->orderBy('id', 'desc')
            ->paginate($perPage);

        return $this->successResponse($contest);
    }

    public function show($id): JsonResponse
    {
        $contest = Contest::query()->findOrFail($id);

        return $this->successResponse($contest);
    }

    public function create(Request $request): JsonResponse
    {
        try {
            $raw = $this->validateUpdateRequest($request);
            $contest = Contest::query()->create([
                'name' => $raw['title'],
                'image' => '',
                'rewards' => '{}',
                'status' => ContestStatusEnum::INACTIVE,
                'settings' => '{}',
            ]);
            $processed = $this->processData($raw, $contest->id);
            $contest->update($processed);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
        return $this->successResponse();
    }

    public function update(Request $request, $id): JsonResponse
    {
        $raw = $this->validateUpdateRequest($request);
        $contest = Contest::query()->findOrFail($id);
        try {
            $processed = $this->processData($raw, $id);
            $updated = $contest->update($processed);
            if ($updated && isset($processed['status']) && (int)$processed['status'] === ContestStatusEnum::INACTIVE && !$this->hasActiveContest()) {
                User::query()->where('confirm_join_contest', 1)->update(['confirm_join_contest' => 0]);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
        return $this->successResponse($contest);
    }

    /**
     * @throws \Exception
     */
    private function processData($data, $id): array
    {
        $base = 'contests' . '/' . $id;
        $startAt = new DateTime($data['start_at']);
        $expiredAt = new DateTime($data['expired_at']);
        if ($startAt > $expiredAt) {
            throw new \RuntimeException("Start date cannot be later than expiration date.");
        }
        $data['start_time'] = $startAt->format('Y-m-d H:i:s');
        $data['end_time'] = $expiredAt->format('Y-m-d H:i:s');
        if (!empty($data['registration_date'])) {
            $registration_date = new DateTime($data['registration_date']);
            $data['registration_date'] = $registration_date->format('Y-m-d H:i:s');
            if ($registration_date > $expiredAt) {
                throw new \RuntimeException("Registration date cannot be later than expiration date.");
            }
        }
        $rewards = $data['rewards'];
        $rewards = array_map(function ($reward) use ($base) {
            if (!isset($reward['title']) || !isset($reward['goal']) || !isset($reward['image'])) {
                throw new \RuntimeException("Reward must have title, goal, and image.");
            }
            if ((int)$reward['goal'] < 0) {
                throw new \RuntimeException("Goal must be greater than or equal to 0.");
            }
            $reward['prize'] = $reward['title'];
            unset($reward['title']);
            $reward['image'] = $this->processImage($reward['image'], $base);
            return $reward;
        }, $rewards);
        $data['name'] = $data['title'];
        $data['rewards'] = json_encode($rewards);
        $data['image'] = $this->processImage($data['poster'], $base);
        $background = $this->processImage($data['background'], $base);
        $icon = $this->processImage($data['icon'], $base);
        if (isset($data['end_icon'])) {
            $endIcon = $this->processImage($data['end_icon'], $base);
        } else {
            $endIcon = $icon;
        }
        $data['settings'] = json_encode([
            'background' => $background,
            'icon' => $icon,
            'end_icon' => $endIcon,
        ], JSON_THROW_ON_ERROR);
        return $data;
    }

    private function processImage($path, $base): string
    {
        return saveTempFileAws($path, $base);
    }

    private function validateUpdateRequest(Request $request): array
    {
        return $request->validate(
            [
                'title' => 'required',
                'margin' => 'nullable',
                'rewards' => 'required',
                'start_at' => 'required',
                'expired_at' => 'required',
                'registration_date' => 'nullable',
                'status' => 'required',
                'poster' => 'required',
                'background' => 'required',
                'icon' => 'required',
                'end_icon' => 'nullable',
            ]
        );
    }

    public function destroy($id): JsonResponse
    {
        $contest = Contest::query()
            ->where('id', $id)
            ->firstOrFail();
        if ($contest->status !== ContestStatusEnum::INACTIVE) {
            return $this->errorResponse('You have to deactivate the contest first.');
        }
        $contest->delete();

        return $this->successResponse($contest);
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Enums\FirstOrderTypeEnum;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderStatus;
use App\Enums\ProductStatus;
use App\Enums\SellerBillingType;
use App\Enums\SellerHistoryActionEnum;
use App\Enums\SellerNotificationActionEnum;
use App\Enums\SystemRole;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\AdminAdjustSellerBalanceRequest;
use App\Http\Requests\ListSellerNotificationRequest;
use App\Http\Requests\NotifySellerRequest;
use App\Http\Requests\UpdateNotificationRequest;
use App\Jobs\SendSellerBalanceUpdateNotification;
use App\Models\IndexCampaign;
use App\Models\IndexOrder;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\SellerHistory;
use App\Models\SellerNotification;
use App\Models\SellerNotificationLog;
use App\Models\User;
use App\Services\UserService;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use InvalidArgumentException;
use Modules\SellerTier\Models\SellerTier;
use Throwable;

class SellerController extends Controller
{
    use ApiResponse;

    public function update(Request $request, $sellerId): JsonResponse
    {
        $request->validate([
            'campaign_limit' => 'bail|required|numeric',
            'support_staff_id' => 'nullable|numeric',
            'sale_staff_id' => 'nullable|numeric',
            'ref_id' => 'nullable|numeric',
            'is_ref' => 'required',
            'is_advanced' => 'required',
            'tags' => 'nullable',
            'facebook' => 'nullable|url',
            'phone' => 'nullable|phone:AUTO,US,VN',
            'birthday' => 'nullable|date',
            'note' => 'nullable|string',
            'role' => [
                'nullable',
                'string',
                Rule::in(UserRoleEnum::getValues())
            ],
            'status' => [
                'nullable',
                'string',
                Rule::in(UserStatusEnum::getValues())
            ],
            'tier_id' => [
                'nullable',
                Rule::exists(SellerTier::class, 'id')
            ],
            'hold_amount' => 'nullable|numeric',
            'hold_to' => 'nullable|date',
        ]);

        $campaignLimit = (int)$request->post('campaign_limit');
        $refId = $request->post('ref_id');
        $sale_expired_at = $request->post('sale_expired_at');
        $first_order_type = $request->post('first_order_type');
        $isRef = (int)$request->post('is_ref') === 1;
        $isAdvancedUser = (int)$request->post('is_advanced') === 1;
        $customPayment = (int)$request->post('custom_payment') === 1;
        $tags = $request->post('tags');
        $status = $request->post('status');

        if (!$campaignLimit) {
            $campaignLimit = 50;
        }

        if (!$refId) {
            $refId = null;
        }

        $currentUser = currentUser();
        $user = User::query()->whereKey($sellerId)->first();

        if (is_null($user)) {
            return $this->errorResponse('Seller not found');
        }

        $hasSaleStaff = !empty($request->post('sale_staff_id'));

        if (!$user->facebook && !$user->phone && in_array($status, [UserStatusEnum::VERIFIED, UserStatusEnum::TRUSTED], true)) {
            return $this->errorResponse('Seller must have Facebook or phone number');
        }
        if (!empty($first_order_type) && !in_array($first_order_type, FirstOrderTypeEnum::getValues(), true)) {
            $first_order_type = null;
        }
        $checkAssignPermission = false;
        if ($hasSaleStaff) {
            $checkHaveSale = Order::query()
                ->where('seller_id', $sellerId)
                ->where('paid_at', '>=', now()->subDays(90))
                ->exists();
            if (is_null($user->sale_staff_id)) {
                if (!in_array($status, [UserStatusEnum::VERIFIED, UserStatusEnum::TRUSTED], true)) {
                    return $this->errorResponse('Only verified or trusted user can assign sale staff');
                }
                if ($checkHaveSale && !$currentUser->hasPermissionOrFalse('assign_sale')) {
                    return $this->errorResponse('Seller have sale in 90 days, can not add sale staff');
                }
                $checkAssignPermission = true;
            }
            if (!empty($sale_expired_at) && ($currentUser->hasRole(SystemRole::ADMIN) || $currentUser->hasRole(SystemRole::OPERATIONS_ADMIN))) {
                $actual_sale_expired_at = Carbon::parse($sale_expired_at)->subHours(7);
                if ($actual_sale_expired_at && $actual_sale_expired_at->notEqualTo($user->sale_expired_at) && $actual_sale_expired_at->gt(now())) {
                    $user->sale_expired_at = $actual_sale_expired_at;
                    $user->first_order_at = $actual_sale_expired_at->subDays(180);
                }
                if ($first_order_type) {
                    $user->first_order_type = $first_order_type;
                }
            } else if (is_null($user->sale_expired_at)) {
                $user->sale_expired_at = now()->addDays(90);
                if (empty($user->first_order_type) && $first_order_type) {
                    $user->first_order_type = $first_order_type;
                }
            } else if (!$checkHaveSale) {
                if (!in_array($status, [UserStatusEnum::VERIFIED, UserStatusEnum::TRUSTED], true)) {
                    return $this->errorResponse('Only verified or trusted user can assign sale staff');
                }
                $user->sale_expired_at = now()->addDays(90);
                if (empty($user->first_order_type) && $first_order_type) {
                    $user->first_order_type = $first_order_type;
                }
            }
            if ($user->isDirty('sale_expired_at')) {
                SellerHistory::query()->insert(array(
                    'seller_id' => $user->id,
                    'action' => SellerHistoryActionEnum::UPDATE_ACCOUNT,
                    'details' => '[' . ($currentUser->getName() ?? $currentUser->getEmail()) . '] Changed sale expired at to ' . $user->sale_expired_at->toDateTimeString(),
                    'seller_status' => $user->status,
                    'staff_id' => $currentUser->getUserId()
                ));
            }
            // reset bonus times if first order at changed
            if ($user->bonus_times > 0 && $user->isDirty('first_order_at')) {
                $user->bonus_times = 0;
            }
        }

        if ($refId && is_null($user->ref_id)) {
            $checkAssignPermission = true;
        }

        $update_data = [
            'campaign_limit' => $campaignLimit,
            'is_ref' => (int) $isRef,
            'ref_id' => $refId,
            'support_staff_id' => $request->post('support_staff_id'),
            'sale_staff_id' => $request->post('sale_staff_id'),
            'advanced' => (int) $isAdvancedUser,
            'custom_payment' => (int) $customPayment,
            'tags' => $tags,
            'facebook' => $request->post('facebook'),
            'phone' => $request->post('phone'),
            'role' => $request->post('role'),
            'status' => $status,
            'note' => $request->post('note'),
            'birthday' => $request->post('birthday'),
            'tier_id' => $request->post('tier_id'),
            'hold_amount' => $request->post('hold_amount'),
            'hold_to' => $request->post('hold_to'),
        ];
        $message = "The staff member \"" . ($currentUser->getName() ?? $currentUser->getEmail()) . "\" has modified: ";
        $messages = array();
        $updateTierByKyc = false;
        foreach ($update_data as $key => $value) {
            if ($key === 'tier_id' && $value <= 2 && data_get($update_data, 'facebook') && data_get($update_data, 'phone')) {
                $logTierUpdatedByKyc = SellerHistory::query()
                    ->where('seller_id', $sellerId)
                    ->where('action', SellerHistoryActionEnum::UPDATE_TIER_BY_KYC)
                    ->exists();
                if (!$logTierUpdatedByKyc) {
                    $user->tier_id = 2;
                    $updateTierByKyc = true;
                    continue;
                }
            }
            $user->{$key} = $value;

            if ($user->isDirty($key)) {
                $old_value = $user->getOriginal($key);
                $messages[] = "[$key]" . ' from "' . ($old_value === '' ? 'Not set' : $old_value) . '" to "' . $value . '"';
            }
        }



        if ($checkAssignPermission && !is_null($user->sale_staff_id) && $refId && !$currentUser->hasPermissionOrFalse('assign_sale')) {
            return $this->errorResponse('Seller can not assign to both Ref or Staff');
        }

        $user->save();

        if (!empty($messages)) {
            $action = SellerHistoryActionEnum::UPDATE_ACCOUNT;

            if (in_array($update_data['status'], [UserStatusEnum::SOFT_BLOCKED, UserStatusEnum::HARD_BLOCKED], true)) {
                $action = SellerHistoryActionEnum::BLOCK_ACCOUNT;
            }

            $message .= implode(', ', $messages);
            SellerHistory::query()->insert(array(
                'tier_id' => $update_data['tier_id'],
                'seller_id' => $sellerId,
                'action' => $action,
                'details' => $message,
                'seller_status' => $update_data['status'],
                'staff_id' => optional($currentUser->getInfo())->id
            ));
            if ($updateTierByKyc) {
                SellerHistory::query()->create([
                    'tier_id' => 2,
                    'seller_id' => $user->id,
                    'action' => SellerHistoryActionEnum::UPDATE_TIER_BY_KYC,
                    'details' => "Your account has been upgraded to Tier 2",
                ]);
            }
        }

        // clear seller cache
        clearCacheSeller($sellerId);

        return $this->successResponse();
    }

    /**
     * @param NotifySellerRequest $request
     * @return JsonResponse
     */
    public function notifySeller(NotifySellerRequest $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $data = [
            'seller_id' => $request->post('seller_id'),
            'subject' => $request->post('subject'),
            'message' => $request->post('message', ''),
            'expiry_date' => $request->post('expiry_date'),
            'type' => $request->post('type', 1),
            'admin_id' => $userId,
            'is_warning' => $request->post('is_warning'),
            'image_link' => $request->post('image_link'),
        ];

        if ($request->has('image')) {
            $data['image'] = $this->processNotificationImage($request->post('image'), $userId);
        }

        $result = SellerNotification::query()->create($data);
        return $result->wasRecentlyCreated ? $this->successResponse() : $this->errorResponse();
    }

    private function processNotificationImage($temp, $userId): string
    {
        $fileName = basename($temp);
        $path = 'u/' . $userId . '/seller_notification/' . $fileName;
        return saveTempFileAws($temp, $path);
    }

    public function sellerClickTracking($id) {
        $user = currentUser();
        $notification = SellerNotification::query()->where('id', $id)->first();
        if (!$notification || !$notification->image_link) {
            logToDiscord('Seller click tracking: Invalid link: notification_id: ' . $id, 'thiennt_log');
            return redirect('https://seller.senprints.com');
        }
        if ($user->isSeller()) {
            SellerNotificationLog::query()->updateOrCreate([
                'seller_id' => $user->getUserId(),
                'notification_id' => $id,
                'action' => SellerNotificationActionEnum::CLICKED
            ], [
                'updated_at' => now(),
                'action' => SellerNotificationActionEnum::CLICKED
            ]);
        }
        return redirect($notification->image_link);
    }

    /**
     * @param ListSellerNotificationRequest $request
     * @return JsonResponse
     */
    public function listNotifications(ListSellerNotificationRequest $request): JsonResponse
    {
        $seller_id = $request->get('seller_id');
        $limit = $request->get('limit', 15);
        $page = $request->get('page', 1);
        $subject = $request->get('subject');
        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');
        $status = $request->get('status'); // read or not
        $sort = $request->get('sort');
        $sort_dir = $request->get('sort_dir', 'DESC');

        $query = SellerNotification::query();
        if ($seller_id) {
            $query->where('seller_id', $seller_id);
        }
        if ($subject) {
            $query->where('subject', 'LIKE', '%' . $subject . '%');
        }
        #TODO: Add more filtering and sorting @james
        $query->with(['seller', 'admin']);
        $query->with('logs:notification_id,action');
        $query->orderByDesc('created_at');
        $results = $query->paginate($limit);
        $results->getCollection()->each(function ($notification) {
            $notification->append('total_clicked');
        });
        return $this->successResponse($results);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws InvalidArgumentException
     */
    public function setNotificationStatus(Request $request): JsonResponse
    {
        $id = $request->post('id');
        $status = $request->post('status', 1);
        $validator = Validator::make($request->all(), [
            'id' => ['required'],
            'status' => ['required', 'in:1,0']
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }
        $result = SellerNotification::query()
            ->where('id', $id)
            ->update([
                'status' => $status
            ]);

        if ($result) {
            return $this->successResponse([
                'id' => $id,
                'status' => $status
            ]);
        }

        return $this->errorResponse();
    }

    /**
     * @param UpdateNotificationRequest $request
     * @return JsonResponse
     * @throws InvalidArgumentException
     */
    public function updateNotification(UpdateNotificationRequest $request): JsonResponse
    {
        $id = $request->post('id');
        $subject = $request->post('subject');
        $message = $request->post('message');
        $expiryDate = $request->post('expiry_date');
        $isWarning = $request->post('is_warning');
        $type = $request->post('type', 1);
        $imageLink = $request->post('image_link');
        if ($request->has('image')) {
            $userId = currentUser()->getUserId();
            $image = $this->processNotificationImage($request->post('image'), $userId);
        }
        $result = SellerNotification::query()
            ->where('id', $id)
            ->update([
                'subject' => $subject,
                'message' => $message,
                'expiry_date' => $expiryDate,
                'is_warning' => $isWarning,
                'type' => $type,
                'image_link' => $imageLink,
                'image' => $image ?? null,
            ]);

        if (!$result) {
            return $this->errorResponse();
        }

        return $this->successResponse(['id' => $id]);
    }

    /**
     * @param Request $request
     * @return array
     */
    public function adminSearchSellerOrders(Request $request): array
    {
        $keywords = $request->input('q');
        $sellerId = $request->input('seller_id');
        $limit = $request->input('limit', 20);

        $query = Order::query()
            ->select([
                'id',
                'order_number',
                'seller_id',
                'status'
            ]);

        $query
            ->where('seller_id', '=', $sellerId)
            ->where('status', '!=', OrderStatus::DRAFT);

        if (!empty($keywords)) {
            if (is_numeric($keywords)) {
                $query->where('id', $keywords);
            } elseif (preg_match('/^[a-zA-Z]+\d*-\d+$/', $keywords)) {
                $query->where(function ($q) use ($keywords) {
                    $q->where('order_number', $keywords)->orWhere('order_number_2', $keywords);
                });
            } else {
                $query->where('id', 'like', '%' . $keywords . '%')
                    ->orWhere('order_number', 'like', '%' . $keywords . '%');
            }
        }

        $orders = $query->limit($limit)
            ->orderByDesc('created_at')
            ->get();

        return $orders->toArray();
    }

    /**
     * @param AdminAdjustSellerBalanceRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function adminAdjustSellerBalance(AdminAdjustSellerBalanceRequest $request): JsonResponse
    {
        $sellerId = $request->input('seller_id');
        $amount = $request->input('amount');
        $action = $request->input('action');
        $reason = $request->input('reason');
        $orderId = $request->input('order_id');
        $type = $request->input('type', SellerBillingType::OTHER);

        $seller = User::query()->where('id', $sellerId)->first();

        if (!$seller) {
            return $this->errorResponse();
        }

        if ($action === 'subtract') {
            $amount = 0 - $amount;
        }

        try {
            $order = !empty($orderId) ? Order::query()->find($orderId) : null;
            if ($order && !str_contains($reason, $order->order_number)) {
                $reason = Str::headline($type) . ' order #' . $order->order_number . ' | ' . $reason;
            }
            DB::beginTransaction();
            $seller->updateBalance($amount, $type, $reason, $orderId);
            DB::commit();

            $subject = 'Balance updated: ';

            if ($action === 'add') {
                $subject .= ' +';
            }

            $subject .= UserService::formatCurrency($amount);
            SendSellerBalanceUpdateNotification::dispatchAfterResponse([
                'email' => $seller->email,
                'subject' => $subject,
                'amount' => UserService::formatCurrency($amount),
                'balance' => UserService::formatCurrency($seller->balance),
                'reason' => $reason
            ]);
            if ($order && $type === SellerBillingType::REFUND) {
                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::REFUNDED,
                    'Reason: ' . $reason,
                    OrderHistoryDisplayLevelEnum::CUSTOMER,
                );
            }
            return $this->successResponse('Seller balance updated');
        } catch (\Throwable $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function clearCacheById($id): JsonResponse
    {
        try {
            User::query()
                ->role('seller')
                ->findOrFail($id);

            $cache['tags'][] = 'seller_' . $id;
            syncClearCache($cache);

            return $this->successResponse('Cache cleared');
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function listCleanUp(Request $request): JsonResponse
    {
        try {
            $sort = $request->get('sort', 'campaigns_no_visit_12m_count');
            $direction = $request->get('direction', 'DESC');
            $sellers = IndexCampaign::query()
                ->selectRaw('
                    COUNT(*) as campaigns_count,
                    COUNT(CASE WHEN visited_at < ? THEN 1 END) as campaigns_no_visit_12m_count,
                    (COUNT(CASE WHEN visited_at < ? THEN 1 END) * 100.0 / COUNT(*)) as campaigns_no_visit_12m_percentage,
                    seller_id
                ', [now()->subYear(), now()->subYear()])
                ->with([
                    'seller' => function ($query) {
                        $query->select('id', 'name', 'email', 'created_at', 'support_staff_id', 'sale_staff_id');
                        $query->with([
                            'support_staff:id,name',
                            'sale_staff:id,name',
                            'userLogs' => fn ($query) => $query->orderByDesc('created_at'),
                            'userCleanSetting' => fn ($query) => $query->on('singlestore'),
                        ]);
                    }
                ])
                ->where('status', ProductStatus::ACTIVE)
                ->groupBy('seller_id')
                ->take(100)
                ->orderBy($sort, $direction)
                ->get();
            $sellerSales = IndexOrder::query()
                ->selectRaw('
                    SUM(total_amount) as sales,
                    SUM(CASE WHEN created_at >= ? THEN total_amount ELSE 0 END) as sales_last_3m,
                    seller_id
                ', [now()->subMonths(3)])
                ->whereIn('payment_status', [
                    OrderPaymentStatus::PAID,
                    OrderPaymentStatus::PARTIALLY_REFUNDED,
                ])
                ->whereIn('seller_id', $sellers->pluck('seller_id'))
                ->groupBy('seller_id')
                ->get();
            $sellers->transform(function ($item) use ($sellerSales) {
                $newItem = [
                    ...$item->toArray(),
                    ...($sellerSales->first(fn($saleDataItem) => $saleDataItem->seller_id === $item->seller_id)?->toArray() ??
                        ['seller_id' => $item->seller_id, 'total_amount' => 0, 'sales_last_3m' => 0]),
                    ...($item->seller?->toArray() ?? [])];
                unset($newItem['seller']);
                return $newItem;
            });
            return $this->successResponse($sellers);
        } catch (\Exception $exception) {
            logException($exception, 'SellerController@listCleanUp');
            return $this->errorResponse();
        }
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Actions\Commons\RejectOrdersAction;
use App\Enums\AccessExternalApiType;
use App\Enums\ApiLogTypeEnum;
use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\DesignByEnum;
use App\Enums\DesignStatusEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\DiscordChannel;
use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\FulfillMappingEnum;
use App\Enums\FulfillmentStatusEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderAssigneeEnum;
use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderFraudStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderSupportStatusEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductCategoryEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\PromptTypeEnum;
use App\Enums\QueueName;
use App\Enums\ShipLateFilter29DaysEnum;
use App\Enums\ShippingMethodEnum;
use App\Enums\SupplierEnum;
use App\Enums\TrackingStatusEnum;
use App\Enums\TradeMarkStatusEnum;
use App\Events\OrderChangeDesign;
use App\Events\OrderFulfilled;
use App\Events\OrderProductFulfilled;
use App\Events\OrderUpdated;
use App\Exports\Fulfill\OrdersExport;
use App\Exports\Fulfill\ProductVariantExport;
use App\Exports\Fulfill\Report;
use App\Exports\Fulfill\ValidateBillingExport;
use App\Exports\Fulfill\ValidateOptionsExport;
use App\Exports\Supplier\ProductsExport;
use App\Http\Controllers\Controller;
use App\Http\Controllers\SystemConfigController;
use App\Imports\Fulfill\UpdateOrderImport;
use App\Imports\Supplier\FulfillProductImport;
use App\Imports\Supplier\FulfillProductVariantImport;
use App\Imports\Supplier\ProductDesignMappingImport;
use App\Jobs\CrawlAndUpdateOrderJob;
use App\Jobs\OrderCostStatisticsJob;
use App\Jobs\ProcessUpdateOrderTrackingStatus;
use App\Jobs\RefundShipExtraFeeToSellerJob;
use App\Jobs\SendSmsUpdateOrderStatusToCustomer;
use App\Jobs\Suppliers\HandleFulfillOrderProductJob;
use App\Models\ApiLog;
use App\Models\Campaign;
use App\Models\Design;
use App\Models\File;
use App\Models\FulfillMapping;
use App\Models\FulfillProduct;
use App\Models\IndexOrderProduct;
use App\Models\Order;
use App\Models\OrderAssignSupplierHistory;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductFulfillMapping;
use App\Models\ProductVariant;
use App\Models\Supplier;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Models\Template;
use App\Models\User;
use App\Providers\FulfillAPI\ObjectFulfill;
use App\Repositories\BeefulWebhookRepository;
use App\Repositories\BondreWebhookRepository;
use App\Repositories\CustomCatWebhookRepository;
use App\Repositories\DreamshipWebhookRepository;
use App\Repositories\DupliumWebhookRepository;
use App\Repositories\GearmentWebhookRepository;
use App\Repositories\JonDoWebhookRepository;
use App\Repositories\MerchizeWebhookRepository;
use App\Repositories\MonsterDigitalWebhookRepository;
use App\Repositories\MWWWebhookRepository;
use App\Repositories\PrintforiaWebhookRepository;
use App\Repositories\PrintGeekWebhookRepository;
use App\Repositories\PrintikWebhookRepository;
use App\Repositories\PrintLogisticV2WebhookRepository;
use App\Repositories\PrintLogisticWebhookRepository;
use App\Repositories\PrintwayWebhookRepository;
use App\Repositories\QTCOWebhookRepository;
use App\Repositories\ShirtPlatformWebhookRepository;
use App\Repositories\SwiftPODWebhookRepository;
use App\Repositories\PentifineWebhookRepository;
use App\Repositories\OneprintWebhookRepository;
use App\Services\ClassifyDesignService;
use App\Services\FulfillmentService;
use App\Services\MostFulfilProductForLocationMatcher;
use App\Services\OrderService;
use App\Services\SeventeenTrack;
use App\Services\StoreService;
use App\Services\TrackingService;
use App\Services\TradeMarkService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use App\Traits\ParseRequest;
use App\Traits\ScopeFilterSupplierDashboardTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\OrderService\Models\RegionOrderProducts;
use Ramsey\Uuid\Uuid as UuidValidator;
use RuntimeException;
use Throwable;
use App\Actions\Commons\GetFilesAndMockupsOfOrderProducts;
use function DeepCopy\deep_copy;

class FulfillController extends Controller
{
    use ApiResponse;
    use ParseRequest;
    use ElasticClient;
    use ScopeFilterSupplierDashboardTrait;

    public function index(Request $request): JsonResponse
    {
        $search     = $request->get('q');
        $supplierId = $request->get('supplier_id');
        $productId  = $request->get('product_id');

        $q = FulfillProduct::query()
            ->select(
                [
                    'product.id',
                    'product.name',
                    'product.supplier_id',
                    'product.status',
                    'product.options',
                    'product.sku',
                    'product.thumb_url',
                    'supplier.name as supplier_name',
                    'supplier.location as supplier_location',
                    'product_fulfill_mapping.fulfill_product_id as fulfill_product_id',
                    'product_fulfill_mapping.assign_rate as product_fulfill_mapping__assign_rate',
                    'product_fulfill_mapping.max_items as product_fulfill_mapping__max_items',
                ]
            )
            ->join('supplier', function($join) use($supplierId) {
                $join->on('supplier.id', '=', 'product.supplier_id');

                if ( ! is_null($supplierId)) {
                    $join->where('supplier.id', $supplierId);
                }
            })
            ->leftJoin(
                'product_fulfill_mapping',
                'product_fulfill_mapping.fulfill_product_id',
                'product.id',
            );

        if (empty($search)) {
            $q->where('product_fulfill_mapping.product_id', $productId);
            $q->orderBy('product_fulfill_mapping.id');
            $q->with([
                'fulfill_mappings' => function ($q) use ($productId) {
                    $q->where('product_id', $productId);
                }
            ]);
        } else {
            $q->where('product.status', ProductStatus::ACTIVE);
            $q->where('product.name', 'like', '%' . $search . '%');
            // check if not assign or assign to current product
            $q->where(function ($q) use ($productId) {
                $q->orWhereNull('product_id');
                $q->orWhere('product_id', '!=', $productId);
            });
        }

        $data = $q->limit(50)->get();

        // Đính kèm thêm thông tin avg_items / 1w được tính toán từ singlestore db
        $data = $this->attachAvgItems1w($productId, $data);

        return $this->successResponse($data);
    }

    /**
     * @param int $templateId
     * @param Collection $data
     * @return Collection
     */
    private function attachAvgItems1w(int $templateId, Collection $data): Collection
    {
        $fulfillProductIds = $data->pluck('id')->all();
        $from = now()->subDays($days = 7);

        $rows = $this->aggregateFulfillProductPaidByTemplate(
                $templateId, $fulfillProductIds, $from
            )
            ->keyBy('fulfill_product_id');

        return $data->each(function(FulfillProduct $product) use($rows, $days) {
            $product->statistic_days = $days;
            $product->statistic_num_items = optional(
                $rows->get($product->id)
            )->statistic_num_items ?? 0;
        });
    }

    /**
     * @param int $templateId
     * @param array $fulfillProductIds
     * @param Carbon $from
     * @return Collection
     */
    private function aggregateFulfillProductPaidByTemplate(int $templateId, array $fulfillProductIds, Carbon $from): Collection
    {
        return IndexOrderProduct::join('order', 'order_product.order_id', '=', 'order.id')
            ->where('template_id', $templateId)
            ->whereIn('fulfill_product_id', $fulfillProductIds)
            ->where('order.paid_at', '>=', $from)
            ->groupBy('fulfill_product_id')
            ->select(
                'fulfill_product_id',
                DB::raw('sum(quantity) as statistic_num_items')
            )
            ->get();
    }

    public function colors(Request $request): JsonResponse
    {
        $productId = $request->product_id;
        // prevent color length is greater than 1024 character
        DB::statement('SET SESSION group_concat_max_len = 1000000');
        $colorSupply = Product::query()
            ->selectRaw(
                "
                CONCAT(
                    '[',
                    REPLACE(
                        REPLACE(
                            GROUP_CONCAT(JSON_EXTRACT(options,'$.color')),
                            ']',
                            ''
                        ),
                        '[',
                        ''
                    ),
                    ']'
                ) as colors
                "
            )
            ->where('product_type', ProductType::FULFILL_PRODUCT)
            ->where('template_id', $productId)
            ->first();

        if (is_null($colorSupply)) {
            return $this->errorResponse();
        }

        $arrColors = StoreService::systemColors();
        if ($arrColors->isEmpty()) {
            return $this->errorResponse();
        }

        $colors = json_decode($colorSupply->color, true);
        if (empty($colors)) {
            return $this->successResponse($arrColors);
        }

        foreach ($arrColors as $color) {
            foreach ($colors as $colorSupply) {
                if ($color->name === $colorSupply) {
                    $color->countSupply++;
                }
            }
        }
        //sort by countSupply and flat to remove key when convert to json (prevent auto-reorder from json_decode)
        $data = $arrColors->sortBy(
            [
                ['countSupply', 'desc'],
                ['name', 'asc'],
            ]
        )->flatten();

        return $this->successResponse($data);
    }

    public function listingOrderFulfillProducts(Request $request): JsonResponse
    {
        $keyword           = $request->get('q');
        $orderStatus       = checkAndConvertToArray($request->get('order_status'));
        $orderFulfilStatus = checkAndConvertToArray($request->get('order_fulfill_status'));

        $orderProductFulfillStatus = $request->get('array_fulfill_status');
        if (!is_null($orderProductFulfillStatus) && !is_array($orderProductFulfillStatus)) {
            $orderProductFulfillStatus = explode(',', $orderProductFulfillStatus);
            if (in_array(FulfillmentStatusEnum::UNFULFILLED, $orderProductFulfillStatus)) {
                $orderFulfilStatus[] = FulfillmentStatusEnum::UNFULFILLED;
            }
            if (in_array(FulfillmentStatusEnum::DESIGNING, $orderProductFulfillStatus)) {
                $orderFulfilStatus[] = FulfillmentStatusEnum::DESIGNING;
            }
        }
        $trackingStatus = $request->get('array_tracking_status');
        if (!is_null($trackingStatus) && !is_array($trackingStatus)) {
            $trackingStatus = explode(',', $trackingStatus);
        }
        $sellerId = $request->get('seller_id');
        $supportStatus  = (int)$request->get('support_status', OrderSupportStatusEnum::ALL);
        $supplierId     = $request->get('supplier_id');
        $categoryId     = $request->get('category_id');
        $dateRange      = $request->get('date_range');
        $startDate      = $request->get('start_date');
        $endDate        = $request->get('end_date');
        $assignee       = $request->get('assignee', OrderAssigneeEnum::ALL);
        $personalized   = $request->boolean('personalized');
        $multiSuppliers = $request->boolean('multi_suppliers');
        $shippingLate   = $request->boolean('shipping_late');
        $fulfillLate    = $request->boolean('fulfill_late');
        $trackingNotFound = $request->boolean('tracking_not_found');
        $trackingPreShipment = $request->boolean('tracking_pre_shipment');
        $onDeliveryStats = $request->boolean('on_delivery_stats');
        $beforeDeliveryStats = $request->boolean('before_delivery_stats');
        $isShowOnlyFbaOrder = $request->boolean('show_fba');
        $shippingMethod = $request->get('shipping_method');
        $isCrossShipping = (int) $request->boolean('cross_shipping');
        $isMissingFulfillCost = (int) $request->boolean('missing_fulfill_cost');
        $isFulfill3Days = $request->boolean('fulfill_3_days');
        $isTrackingLate = $request->boolean('tracking_late');
        $isOnlyOneProduct = $request->boolean('is_only_one_product');
        $isAtRisk = $request->boolean('at_risk');
        $isAdminDesign = $request->boolean('admin_design');
        $sortDirection = $request->get('direction');
        $sortBy        = $request->get('order_by');
        $perPage       = $request->get('per_page', 15);
        $shipLateFilter29Days = $request->get('shiplate_filter_29_days_by', ShipLateFilter29DaysEnum::ALL);
        $addressVerifiedStatus = $request->get('address_verified_status', Order::ORDER_ADDRESS_VERIFIED_STATUS_ALL);
        $expressShippingLate   = $request->boolean('express_shipping_late', 0);
        $templateId = $request->get('template_id');
        $isNoRendered = $request->get('no_rendered');
        $currentDate = now();
        if (empty($sortBy)) {
            $sortBy        = Order::FILTER_COLUMN_DATE;
            $sortDirection = 'asc';
        }
        $templateIds = [];
        if (!empty($categoryId)) {
            $templateIds = ProductCategory::query()
                ->where('category_id', $categoryId)
                ->pluck('product_id');
        }
        if (!empty($templateId)) {
            $templateIds[] = (int) $templateId;
        }

        $dateRangeFilterColumn = 'order.fulfilled_at';

        if ($shippingLate) {
            $shippingMethod = ShippingMethodEnum::STANDARD;
        }

        if ($expressShippingLate) {
            $shippingMethod = ShippingMethodEnum::EXPRESS;
            $dateRangeFilterColumn = 'order.created_at';
        }
        $orderProductQueryLateTimeCondition = OrderProduct::QUERY_LATE_TIME_CONDITION;
        $orderProductQueryDesignBtnExistedCondition = OrderProduct::QUERY_PRODUCT_DESIGNING_CONDITION;

        $orderQuery = Order::query()
            ->select(
                [
                    'order.id',
                    'order.order_number',
                    'order.country',
                    'order.state',
                    'order.paid_at',
                    'order.fulfilled_at',
                    'order.admin_note',
                    'order.order_note',
                    'order.address_verified',
                    'order.type',
                    'order.status',
                    'order.fulfill_status',
                    'order.support_status',
                    'order.assignee',
                    'order.shipping_method',
                    'order.sen_fulfill_status',
                    'order.tm_status',
                    'order.estimate_delivery_date',
                    'order.seller_id',
                    'order.store_domain',
                ]
            )
            ->with([
                'order_products' => function ($q) use (
                    $orderProductFulfillStatus,
                    $supplierId,
                    $personalized,
                    $shippingLate,
                    $isMissingFulfillCost,
                    $templateIds,
                    $shipLateFilter29Days,
                    $orderProductQueryLateTimeCondition,
                    $expressShippingLate,
                    $trackingStatus,
                    $beforeDeliveryStats,
                    $orderProductQueryDesignBtnExistedCondition,
                    $isNoRendered,
                    $isAdminDesign,
                ) {
                    $isMissingFulfillCost && $q->missingFulfillCost();
                    $q->filterFulfillProduct($orderProductFulfillStatus, $supplierId, $personalized)
                        ->select(
                            'order_product.id',
                            'order_product.order_id',
                            'order_product.product_id',
                            'order_product.template_id',
                            'order_product.product_name',
                            'order_product.quantity',
                            'order_product.options',
                            'order_product.custom_options',
                            'order_product.color',
                            'order_product.fulfill_status',
                            'order_product.fulfill_exception_log',
                            'order_product.supplier_id',
                            'order_product.supplier_name',
                            'order_product.fulfill_sku',
                            'order_product.fulfill_order_id',
                            'order_product.fulfill_product_id',
                            'order_product.updated_at',
                            'order_product.personalized',
                            'order_product.full_printed',
                            'order_product.fulfilled_at',
                            'order_product.delivered_at',
                            'order_product.tracking_code',
                            'order_product.tracking_url',
                            'order_product.tracking_status',
                            'order_product.shipping_carrier',
                            'order_product.received_at',
                            'order_product.total_reprint',
                            'order_product.seller_id',
                            DB::raw("$orderProductQueryLateTimeCondition AS late_time"),
                            DB::raw("$orderProductQueryDesignBtnExistedCondition AS order_product_designing"),
                        )
                        ->orderBy('order_product.updated_at', 'desc')
                        ->with(['supplier:supplier.id,dashboard_order_url,dashboard_url', 'seller']);
                    if (!$expressShippingLate && $shippingLate) {
                        $q->shippingLate();
                        if ($shipLateFilter29Days !== ShipLateFilter29DaysEnum::ALL) {
                            if ($shipLateFilter29Days === ShipLateFilter29DaysEnum::WITHIN_29_DAYS) {
                                $q->whereRaw("$orderProductQueryLateTimeCondition <= 29");

                            } else if ($shipLateFilter29Days === ShipLateFilter29DaysEnum::OVER_29_DAYS) {
                                $q->whereRaw("$orderProductQueryLateTimeCondition > 29");
                            }
                        }
                    }
                    if (!empty($templateIds)) {
                        $q->whereIn('template_id', $templateIds);
                    }
                    if ($trackingStatus) {
                        $q->whereIn('tracking_status', $trackingStatus);
                    }
                    if ($isNoRendered) {
                        $q->whereDoesntHave('product_files', function ($query) {
                            $query->whereNotNull('file_url_2');
                        });
                    }
                    $q->when($isAdminDesign,
                        fn ($q) => $q->where('campaign_type', '=', ProductSystemTypeEnum::AI_MOCKUP),
                        fn ($q) => $q->where('campaign_type', '!=', ProductSystemTypeEnum::AI_MOCKUP)
                    );
                },
                'country_info',
                'seller:id,name,email'
            ])
            ->whereHas(
                'order_products',
                function ($q) use (
                    $orderProductFulfillStatus,
                    $supplierId,
                    $personalized,
                    $shippingLate,
                    $fulfillLate,
                    $trackingNotFound,
                    $trackingPreShipment,
                    $onDeliveryStats,
                    $isTrackingLate,
                    $templateIds,
                    $shipLateFilter29Days,
                    $orderProductQueryLateTimeCondition,
                    $expressShippingLate,
                    $trackingStatus,
                    $isAtRisk,
                    $beforeDeliveryStats,
                    $isNoRendered,
                    $isAdminDesign,
                ) {
                    $q->filterFulfillProduct($orderProductFulfillStatus, $supplierId, $personalized);
                    $q->when($isAtRisk, fn ($q) => $q->where('at_risk', $isAtRisk));
                    if (!$expressShippingLate) {
                        if ($shippingLate) {
                            $q->shippingLate();
                            if ($shipLateFilter29Days !== ShipLateFilter29DaysEnum::ALL) {
                                if ($shipLateFilter29Days === ShipLateFilter29DaysEnum::WITHIN_29_DAYS) {
                                    $q->whereRaw("$orderProductQueryLateTimeCondition <= 29");
                                } else if ($shipLateFilter29Days === ShipLateFilter29DaysEnum::OVER_29_DAYS) {
                                    $q->whereRaw("$orderProductQueryLateTimeCondition > 29");
                                }
                            }
                        }
                        if ($fulfillLate) {
                            $q->fulfillLate();
                        }
                        if ($isTrackingLate) {
                            $q->trackingLate();
                        }
                        if ($trackingNotFound) {
                            $q->trackingNotFound();
                        }
                        if ($trackingPreShipment) {
                            $q->trackingPreShipment();
                        }
                        if ($onDeliveryStats) {
                            $q->onDeliveryStats();
                        }
                        if ($beforeDeliveryStats) {
                            $q->where('fulfilled_at', '<', now()->subDays(9));
                            $q->whereIn('tracking_status', [TrackingStatusEnum::NEW, TrackingStatusEnum::PENDING, TrackingStatusEnum::INFO_RECEIVED]);
                        }
                    }
                    if (!empty($templateIds)) {
                        $q->whereIn('template_id', $templateIds);
                    }
                    if ($trackingStatus) {
                        $q->whereIn('tracking_status', $trackingStatus);
                    }
                    if ($isNoRendered) {
                        $q->whereDoesntHave('product_files', function ($query) {
                            $query->whereNotNull('file_url_2');
                        });
                    }
                    $q->when($isAdminDesign,
                        fn ($q) => $q->where('campaign_type', '=', ProductSystemTypeEnum::AI_MOCKUP),
                        fn ($q) => $q->where('campaign_type', '!=', ProductSystemTypeEnum::AI_MOCKUP)
                    );
                }
            )
            ->when($isFulfill3Days && !$expressShippingLate, function ($q) {
                $q->whereHas('templates', function ($q) {
                    $q->where('system_type', ProductSystemTypeEnum::FULFILL_3_DAYS);
                });
            })
            ->when($expressShippingLate, function ($q) use ($currentDate) {
                $q->where('estimate_delivery_date', '<', $currentDate->clone()->addDay()->toDateTimeString());
            })
            ->when(
                $multiSuppliers,
                fn($q) => $q->whereRaw('(select count(DISTINCT op.supplier_id) from order_product op where order.id=op.order_id and op.deleted_at is null)>1')
            )
            ->when($isShowOnlyFbaOrder, fn($q) => $q->where('order.type', '=', OrderTypeEnum::FBA))
            ->when($isCrossShipping, fn($q) => $q->where('order.cross_shipping', 1))
            ->when($isOnlyOneProduct, fn($q) => $q->where('order.total_quantity', 1))
            ->when($shippingLate, function ($q) use ($addressVerifiedStatus) {
                if ($addressVerifiedStatus !== Order::ORDER_ADDRESS_VERIFIED_STATUS_ALL) {
                    $q->where('order.address_verified', $addressVerifiedStatus);
                }
            })
            ->when($sellerId, fn($q) => $q->where('order.seller_id', $sellerId))
            ->filterFulfillProduct(
                $dateRange,
                $startDate,
                $endDate,
                $orderStatus,
                $orderFulfilStatus,
                $shippingMethod,
                $supportStatus,
                $assignee,
                $dateRangeFilterColumn,
                true,
                $isTrackingLate,
                $shippingLate,
                $expressShippingLate
            )
        ;

        $orderQuery->orderBy($sortBy, $sortDirection);

        [$orderIds, $orderNumbers] = OrderService::keywordsParser($keyword);
        if (!empty($orderNumbers) || !empty($orderIds)) {
            $orderQuery->where(function ($q) use ($orderIds, $orderNumbers) {
                if (!empty($orderIds)) {
                    $q->whereIn('order.id', $orderIds);
                }
                if (!empty($orderNumbers)) {
                    $q->orWhereIn('order.order_number', $orderNumbers);
                }
            });
        }

        if ($isNoRendered) {
            FulfillmentService::getNoRenderedOrder($orderQuery);
        }

        $orders = $orderQuery->paginate($perPage);
        if ($shippingLate && !$expressShippingLate) {
            OrderService::setUpLateDaysTagForOrder($orders);
        }
        $orders->getCollection()->transform(
            function ($order) {
                $order->order_products->transform(
                    function ($orderProduct) {
                        $orderProduct->options = correctOptionValue($orderProduct->options);

                        return $orderProduct;
                    }
                );
                return $order;
            }
        );

        FulfillmentService::getNeedDesignTags($orders);
        return $this->successResponse($orders);
    }

    public function listingOrderProductSenDesign(Request $request): JsonResponse
    {
        $keyword = $request->get('q');
        $perPage = $request->get('per_page', 20);
        $page = $request->get('page', 1);
        $dateRange = $request->get('date_range');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $dateRangeFilterColumn = 'order.paid_at';
        $sellerProducts = collect();
        $sellerOrderProducts = collect();
        $sellerPrintDesigns = collect();
        $sellerProductFiles = collect();
        $sellerProductTemplates = collect();
        $orderProducts = OrderProduct::query()
            ->selectRaw('id,order_id,product_id,seller_id,template_id,campaign_id,fulfill_product_id,product_name,quantity,options,custom_options,color,fulfill_status,updated_at,personalized,full_printed,fulfilled_at')
            ->with([
                'order:id,order_number,country,state,paid_at,fulfilled_at,admin_note,order_note,address_verified,type,status,payment_status,fulfill_status,support_status,assignee,shipping_method,sen_fulfill_status,tm_status,seller_id',
                'campaign:id,name,seller_id,system_type',
                'campaign.aiPrompt:id,seller_id,campaign_id,product_id,print_space',
                'fulfillOrderMockups:id,order_product_id,file_url,seller_id,print_space',
                'fulfillOrderDesigns:id,order_product_id,file_url,seller_id,print_space',
            ])
            ->when($keyword, function ($query) use ($keyword) {
                $query->whereHas('order', function ($query) use ($keyword) {
                    $query->where('id', $keyword);
                    $query->orWhere('order_number', $keyword);
                });
            })
            ->when($dateRange, function ($query) use ($dateRange, $startDate, $endDate, $dateRangeFilterColumn) {
                $query->whereHas('order', fn ($q) => $q->filterDateRange($dateRange, $startDate, $endDate, $dateRangeFilterColumn));
            })
            ->whereHas('order', fn ($q) => $q->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                ->whereNotIn('status', [
                    OrderStatus::CANCELLED,
                    OrderStatus::REFUNDED,
                ])
            )
            ->where('design_by', DesignByEnum::SENPRINTS)
            ->where('fulfill_status', FulfillmentStatusEnum::DESIGNING)
            ->paginate($perPage, page: $page);
        $orderProductsCollection = $orderProducts->getCollection();
        $sellers = User::query()->whereIn('id', $orderProductsCollection->pluck('seller_id')->unique()->toArray())->get();
        $orderProductsCollection->groupBy('seller_id')->each(function ($orderProductGroup, $sellerId) use (&$sellerProducts, &$sellerPrintDesigns, &$sellerProductFiles, &$sellerProductTemplates, $sellers) {
            $sellerProductTemplates = $sellerProductTemplates->merge(
                Product::query()
                    ->select(['id', 'system_type', 'print_spaces'])
                    ->whereIn('id', $orderProductGroup->pluck('template_id'))
                    ->get()
            );
            $seller = $sellers->firstWhere('id', $sellerId);
            $productIds = $orderProductGroup->pluck('product_id')->unique()->filter(fn($item) => !empty($item));
            if ($productIds->isEmpty()) {
                return;
            }
            $sellerProducts = $sellerProducts->merge(
                Product::query()
                    ->select(['id', 'system_type', 'name', 'sku', 'seller_id'])
                    ->onSellerConnection($seller)
                    ->whereIn('id', $productIds)
                    ->get()
            );
            $sellerPrintDesigns = $sellerPrintDesigns->merge(
                File::query()
                    ->select([
                        'id',
                        'file_url',
                        'type',
                        'type_detail',
                        'print_space',
                        'campaign_id',
                        'product_id',
                        'render_type',
                        'seller_id'
                    ])
                    ->onSellerConnection($seller)
                    ->where('type', FileTypeEnum::DESIGN)
                    ->where('option', FileRenderType::PRINT)
                    ->whereIn('product_id', $productIds)
                    ->get()
            );
            $sellerProductFiles = $sellerProductFiles->merge(
                File::query()
                    ->onSellerConnection($seller)
                    ->select([
                        'id',
                        'file_url',
                        'type',
                        'type_detail',
                        'print_space',
                        'campaign_id',
                        'product_id',
                        'render_type',
                        'seller_id'
                    ])
                    ->where('type', FileTypeEnum::IMAGE)
                    ->whereIn('product_id', $productIds)
                    ->get()
            );
        });

        $orderProducts = $orderProducts->through(function ($orderProduct) use (&$sellerOrderProducts, &$sellerProducts, $sellerPrintDesigns, $sellerProductFiles, $sellerProductTemplates, $orderProductsCollection, $sellers) {
            $orderProduct->setRelation('seller', $sellers->first(fn($item) => (int)$item->id === (int)$orderProduct->seller_id));
            $orderProduct->setRelation('template', $sellerProductTemplates->first(fn($item) => (int)$item->id === (int)$orderProduct->template_id));
            if (!$orderProduct->order->isFulfillmentOrder()) {
                $orderProduct->setRelation('product_files', $sellerProductFiles->filter(fn($item) => (int)$item->seller_id === (int)$orderProduct->seller_id && (int)$item->product_id === (int)$orderProduct->product_id));
            } else {
                $orderProduct->setRelation('product_files', $orderProduct->fulfillOrderMockups);
                $product_print_spaces = !empty($orderProduct->template->print_spaces) ? json_decode($orderProduct->template->print_spaces, true) : [];
                $print_spaces = [];
                array_map(static function ($item) use (&$print_spaces) {
                    if(!empty($item['name'])) {
                        $print_spaces[$item['name']] = Str::lower($item['name']);
                    }
                    return $item;
                }, $product_print_spaces);
                $orderProduct->printSpaces = array_values($print_spaces);
            }
            unset($orderProduct->fulfillOrderMockups);
            if (!$orderProduct->order->isFulfillmentOrder() && $orderProduct->campaign?->seller_id !== $orderProduct->seller_id) {
                if (!$sellerOrderProducts->has($orderProduct->seller_id)) {
                    $seller = $sellers->first(fn($item) => (int)$item->id === (int)$orderProduct->seller_id);
                    $campaignIds = $orderProductsCollection->where('seller_id', $seller->id)->pluck('campaign_id')->filter()->unique();
                    $campaigns = Campaign::query()
                        ->selectRaw('id,name,seller_id,system_type')
                        ->with(['aiPrompt' => function ($query) use ($seller) {
                            $query->selectRaw('id,seller_id,campaign_id,product_id,print_space');
                            $query->where('seller_id', $seller->id);
                            $query->where('type', PromptTypeEnum::DESIGN);
                        }])
                        ->onSellerConnection($seller)
                        ->whereIn('id', $campaignIds)->get()->keyBy('id');
                    $sellerOrderProducts->put($orderProduct->seller_id, $campaigns);
                }
                $campaign = $sellerOrderProducts->get($orderProduct->seller_id)->get($orderProduct->campaign_id);
                $orderProduct->setRelation('campaign', $campaign);
                $orderProduct->printSpaces = $campaign?->aiPrompt?->map(fn($item) => $item->print_space) ?? [];
            }
            if (!$orderProduct->order->isFulfillmentOrder()) {
                $orderProduct->setRelation('product', $sellerProducts->first(fn($item) => $item->seller_id === $orderProduct->seller_id && $item->id === $orderProduct->product_id));
                $orderProduct->setRelation('printDesigns', $sellerPrintDesigns->filter(fn($item) => $item->seller_id === $orderProduct->seller_id && $item->product_id === $orderProduct->product_id));
            }
            if ($orderProduct->isFullPrintedType()) {
                $orderProduct->printDesigns = $orderProduct->printDesigns->filter(function ($designs) use ($orderProduct) {
                    $options = json_decode($orderProduct->options, true) ?? [];
                    return (!empty($options['size']) && str_contains($options['size'], $designs->print_space)) || $designs->print_space === PrintSpaceEnum::DEFAULT;
                })->values();
            }
            if (!empty($orderProduct->product)) {
                $designs = $orderProduct->printDesigns->values();
                if ($designs->isEmpty()) {
                    $designs = $orderProduct->getPrintDesigns();
                }
                $orderProduct->designs = $designs;
            } else if ($orderProduct->order->isFulfillmentOrder()) {
                $orderProduct->designs = $orderProduct->fulfillOrderDesigns;
                unset($orderProduct->fulfillOrderDesigns);
            }
            $orderProduct->setRelation('printDesigns', null);
            $orderProduct->setRelation('seller', null);
            return $orderProduct;
        });
        return $this->successResponse($orderProducts);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadDesign(Request $request) {
        $currentUser = currentUser();
        $fileId = $request->input('file_id', 0);
        $printSpace = $request->input('print_space');
        $filePath = $request->input('file_path');
        $orderProductId = $request->input('order_product_id');
        $orderProduct = OrderProduct::query()
            ->with('order')
            ->whereKey($orderProductId)
            ->firstOrFail();
        $seller = User::query()->find($orderProduct->seller_id);
        if ($orderProduct->campaign_id && (!$orderProduct->campaign || $orderProduct->campaign->seller_id !== $seller->id)) {
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->select('id', 'slug', 'name', 'product_type', 'template_id', 'system_type')
                ->find($orderProduct->campaign_id);
            $orderProduct->setRelation('campaign', $campaign);
        }
        if ($orderProduct->order->isFulfillmentOrder()) {
            $array_where = [
                'order_product_id' => $orderProduct->id,
                'option' => FileRenderType::PRINT,
                'print_space' => $printSpace,
                'type' => FileTypeEnum::DESIGN,
                'status' => FileStatusEnum::ACTIVE
            ];
            $design = File::query()
                ->where($array_where)
                ->when(!empty($fileId), function ($q) use ($fileId) {
                    $q->whereKey($fileId);
                })
                ->first();

            try {
                $newPath = 'o/' . $orderProduct->order_id;
                $newThumbUrl = saveTempFileAws($filePath, $newPath);
                if (!empty($design)) {
                    $fileId = $design->id;
                    File::query()->whereKey($fileId)->update(array(
                        'file_url' => $newThumbUrl,
                        'file_url_2' => null,
                    ));
                } else {
                    $fileId = File::query()->insertGetId(array_merge($array_where, [
                        'file_url' => $newThumbUrl,
                        'file_url_2' => null,
                        'seller_id' => $orderProduct->seller_id,
                    ]));
                }
                if ($orderProduct) {
                    OrderChangeDesign::dispatch($orderProduct->order_id, ($currentUser->getName() ?? $currentUser->getEmail()) . ' has uploaded a new design. Url: ' . imgUrl($newThumbUrl));
                }
                return $this->successResponse([
                    'url' => $newThumbUrl,
                    'file_id' => $fileId
                ]);
            } catch (\Exception $exception) {
                return $this->errorResponse('Upload design failed' . $exception->getMessage());
            }
        } else {
            $isCampaignMockupCustom = in_array($orderProduct->campaign_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true);
            $hasCustomOptions = $orderProduct->personalized === PersonalizedType::CUSTOM_OPTION && ($orderProduct->isPrintTypeEmbroidery() || $isCampaignMockupCustom);
            if ($hasCustomOptions) {
                $newPath = 'o/' . $orderProduct->order_id;
                $newThumbUrl = moveFileAws($filePath, $newPath);
                $fields = [
                    'product_id' => $orderProduct->product_id,
                    'order_id' => $orderProduct->order_id,
                    'order_product_id' => $orderProduct->id,
                    'seller_id' => $orderProduct->seller_id,
                    'print_space' => $printSpace,
                ];
                Design::query()
                    ->where($fields)
                    ->update(['status' => DesignStatusEnum::INACTIVE]);

                $design = Design::query()->create([
                    'product_id' => $orderProduct->product_id,
                    'order_id' => $orderProduct->order_id,
                    'order_product_id' => $orderProduct->id,
                    'seller_id' => $orderProduct->seller_id,
                    'print_space' => $printSpace,
                    'file_url' => $newThumbUrl,
                    'type' => DesignTypeEnum::PRINT,
                    'status' => DesignStatusEnum::ACTIVE
                ]);
                OrderChangeDesign::dispatch($orderProduct->order_id, ($currentUser->getName() ?? $currentUser->getEmail()) . ' has uploaded a new design. Url: ' . imgUrl($newThumbUrl));
                return $this->successResponse([
                    'url' => $newThumbUrl,
                    'file_id' => $design->id
                ]);
            }
            $campaignId = $orderProduct->campaign_id;
            $array_where = [
                'product_id' => $orderProduct->product_id,
                'option' => 'print',
                'print_space' => $printSpace,
                'type' => FileTypeEnum::DESIGN,
                'status' => FileStatusEnum::ACTIVE
            ];
            $design = File::query()
                ->onSellerConnection($seller)
                ->where($array_where)
                ->when(!empty($fileId), function ($q) use ($fileId) {
                    $q->whereKey($fileId);
                })
                ->first();

            try {
                $newPath = 'p/' . $campaignId;
                $newThumbUrl = saveTempFileAws($filePath, $newPath);
                if (!empty($design)) {
                    $fileId = $design->id;
                    File::query()
                        ->onSellerConnection($seller)
                        ->whereKey($fileId)->update(array(
                            'file_url' => $newThumbUrl,
                            'file_url_2' => null,
                        ));
                } else {
                    $fileId = File::query()
                        ->onSellerConnection($seller)
                        ->insertGetId(array_merge($array_where, [
                            'file_url' => $newThumbUrl,
                            'campaign_id' => $campaignId,
                            'seller_id' => $seller->id,
                        ]));
                }
                if ($orderProduct) {
                    OrderChangeDesign::dispatch($orderProduct->order_id, ($currentUser->getName() ?? $currentUser->getEmail()) . ' has uploaded a new design. Url: ' . imgUrl($newThumbUrl));
                }
                return $this->successResponse([
                    'url' => $newThumbUrl,
                    'file_id' => $fileId
                ]);
            } catch (\Exception $exception) {
                return $this->errorResponse('Upload design failed' . $exception->getMessage());
            }
        }
    }

    public function deleteDesign(Request $request)
    {
        $sellerId = $request->get('seller_id');
        $fileId = $request->get('file_id');
        $orderProductId = $request->get('order_product_id');
        $seller = UserService::getSellerSharding($sellerId);
        if (empty($fileId) || empty($orderProductId)) {
            return $this->errorResponse('Data invalid');
        }
        $orderProduct = OrderProduct::query()
            ->with('order')
            ->whereKey($orderProductId)
            ->first();

        if (empty($orderProduct)) {
            return $this->errorResponse('Order product not found');
        }
        if ($orderProduct->order->isFulfillmentOrder()) {
            $deleted = File::query()
                ->where([
                    'id' => $fileId,
                    'order_product_id' => $orderProductId,
                    'option' => FileRenderType::PRINT,
                    'seller_id' => $sellerId,
                    'type' => FileTypeEnum::DESIGN,
                    'status' => FileStatusEnum::ACTIVE
                ])
                ->update(['status' => FileStatusEnum::INACTIVE]);
        } else if (UuidValidator::isValid($fileId)) {
            $deleted = Design::query()->where([
                'id' => $fileId,
                'seller_id' => $sellerId,
                'type' => DesignTypeEnum::PRINT,
                'order_product_id' => $orderProductId,
            ])->update(['status' => DesignStatusEnum::INACTIVE]);
        } else {
            $deleted = File::query()
                ->onSellerConnection($seller)
                ->where([
                    'id' => $fileId,
                    'type' => FileTypeEnum::DESIGN,
                    'option' => 'print',
                    'status' => FileStatusEnum::ACTIVE
                ])
                ->update([
                    'status' => FileStatusEnum::INACTIVE
                ]);
        }
        if (!$deleted) {
            $this->errorResponse('Can not delete your selected design.');
        }
        return $this->successResponse();
    }

    public function postOrders(Request $request): JsonResponse
    {
        $rules = [
            'order_ids' => [
                Rule::requiredIf(function() use ($request) {
                    return empty($request->get('order_product_ids'));
                })
            ],
            'order_product_ids' => [
                Rule::requiredIf(function() use ($request) {
                    return empty($request->get('order_ids'));
                })
            ]
        ];
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        $user = currentUser();
        $orders = $this->getOrders($request, false);
        $object      = new ObjectFulfill();
        $objectOrder = (object)[]; //default to remove check CI/CD
        $logs        = [];
        $exception   = false;
        $userId      = $user->getUserId();
        foreach ($orders as $order) {
            try {
                $arr             = [];
                $orderProductIds = [];
                $detailCustomer  = '';
                $detailAdmin     = '';
                $orderId         = $order->id;
                $orderProducts   = $order->order_products;
                $reOrder         = false;
                $isHoldOrderFulfillByTM = false;
                $isHoldOrderFulfillByNotSupportExpressShipping = false;
                $orderProductInvalidIds = [];
                $continueCheckShippingMethod = isset($order->shipping_method) && $order->shipping_method === ShippingMethodEnum::EXPRESS;
                if ($order->skipFulfill()) {
                    continue;
                }

                /**
                 * @var OrderProduct $orderProduct
                 */
                foreach ($orderProducts as $orderProduct) {
                    if ($orderProduct->skipFulfill()) {
                        continue;
                    }
                    $supplierId = (int) $orderProduct->supplier_id;
                    // Supplier gắn cho product không hỗ trợ fulfill qua api
                    if (!is_supplier_support_api($supplierId)) {
                        $orderProduct->whereKey($orderProduct->id)->update([
                            'fulfill_status' => OrderProductFulfillStatus::UNFULFILLED
                        ]);
                        continue;
                    }
                    $orderProduct->loadMissing('supplier');
                    $supplier = $orderProduct->supplier;
                    if (!$supplier) {
                        continue;
                    }
                    if ($continueCheckShippingMethod && !empty($supplier->no_express_line_ship)) {
                        $isHoldOrderFulfillByNotSupportExpressShipping = true;
                        $orderProductInvalidIds[] = $orderProduct->id;
                        continue;
                    }
                    if ($orderProduct->skipFulfillByCampaignTradeMark() && !$user->isAdmin()) {
                        $isHoldOrderFulfillByTM = true;
                        $orderProductInvalidIds[] = $orderProduct->id;
                        continue;
                    }

                    //get list to update status
                    $arr['order_products_ids'][$supplierId][] = $orderProduct->id;
                    $orderProductIds[]                        = $orderProduct->id;

                    $detailCustomer .= $orderProduct->product_name . '|';
                    $detailAdmin    .= $orderProduct->id . '-' . $orderProduct->product_name . ':' . $orderProduct->supplier_name . '|';
                    // create order by each supplier
                    if (empty($arr['provider'][$supplierId])) {
                        $object->setupTestingConfig($order, $supplierId);
                        $objectProvider = $object->getProviderBySupplierId($supplierId);
                        if (is_null($objectProvider)) {
                            continue;
                        }
                        // check to not cancel old order
                        $countSameSupplier = OrderProduct::query()
                            ->where('order_id', $orderProduct->order_id)
                            ->where('supplier_id', $supplierId)
                            ->whereIn('fulfill_status', [
                                OrderProductFulfillStatus::PENDING,
                                OrderProductFulfillStatus::PROCESSING,
                                OrderProductFulfillStatus::FULFILLED,
                            ])
                            ->count();

                        if ($countSameSupplier > 0) {
                            $checkCancelled = false;
                            $reOrder        = true;
                        } else {
                            $checkCancelled = true;
                        }

                        // cancel order by each supplier
                        if ($orderProduct->fulfill_status === OrderProductFulfillStatus::REJECTED && $checkCancelled) {
                            if (!$objectProvider->cancel_order) {
                                // ignore cancel these order
                                $logs[] = 'This supplier: ' . $orderProduct->supplier_name . ' does not have api cancel order. Please cancel manually before create new one.';
                            } else {
                                $arr['order_products_cancel'][$supplierId] = $orderProduct->fulfill_order_id;
                            }
                            $reOrder = true;
                        }
                        $objectOrder = $objectProvider->createOrder($order, $reOrder);
                        $objectProvider->setOrderId($orderId);
                        $arr['provider'][$supplierId] = $objectProvider;
                    }

                    $orderProduct = $arr['provider'][$supplierId]->correctOrderProduct($orderProduct);
                    $objectOrder->setItems($orderProduct);
                    $arr['orders'][$supplierId] = $objectOrder;
                }
                if (!empty($orderProductInvalidIds) && ($isHoldOrderFulfillByNotSupportExpressShipping || $isHoldOrderFulfillByTM)) {
                    $onHoldScriptMessage = ' supplier not support ship express.';
                    if ($isHoldOrderFulfillByTM) {
                        $onHoldScriptMessage = ' violated supplier TM.';
                    }
                    $note = '';
                    $glue = ' - ';
                    foreach ($orderProductInvalidIds as $orderProductInvalidId) {
                        $note .= $orderProductInvalidId . $glue . "On hold because of $onHoldScriptMessage $glue";
                    }
                    $order->update([
                        'fulfill_status' => OrderFulfillStatus::ON_HOLD,
                        'admin_note' => rtrim($note, $glue),
                        'support_status' => OrderSupportStatusEnum::NEED_REASSIGN_SUPPLIER_MANUALLY,
                    ]);
                    OrderHistory::insertLog($order->refresh(), OrderHistoryActionEnum::HOLD_ORDER, $note);
                    $message = 'Can not process the order because of ';
                    if ($isHoldOrderFulfillByTM) {
                        $message .= ' violated TM.';
                    }
                    if ($isHoldOrderFulfillByNotSupportExpressShipping) {
                        $message .= ' supplier not support ship express.';
                    }
                    $message .= ' Order Product Id: ' . implode(', ', $orderProductInvalidIds);
                    TradeMarkService::logToDiscord($order->id, $message, '15548997');
                    continue;
                }
                // check order does not have any product unfulfilled
                if (!empty($arr)) {
                    $order->status         = OrderFulfillStatus::PROCESSING;
                    $order->fulfill_status = OrderFulfillStatus::PROCESSING;
                    $order->fulfilled_at   = now();
                    $order->save();

                    //update first, job handle update later
                    OrderProduct::query()
                    ->whereIn('id', $orderProductIds)
                    ->update(
                        [
                            'fulfill_status' => OrderProductFulfillStatus::PENDING,
                            'fulfilled_at'   => DB::raw('CURRENT_TIMESTAMP')
                        ]
                    );
                    HandleFulfillOrderProductJob::dispatch($arr, $order->id, $userId)->onQueue(QueueName::FULFILL)->delay(now()->addSeconds(random_int(0, 10)));
                    // order updated send email
                    OrderUpdated::dispatch($order->id); // create order
                    RefundShipExtraFeeToSellerJob::dispatch($order->id);
                    OrderHistory::insertLog(
                        $order,
                        OrderHistoryActionEnum::FULFILLMENT_CREATED,
                        [$detailCustomer, $detailAdmin],
                        OrderHistoryDisplayLevelEnum::CUSTOMER,
                    );
                }
            } catch (Throwable $e) {
                $exception = true;
                logException($e, 'Create order failed: ' . $orderId, DiscordChannel::FULFILL_ORDER, true);
                /** @var RejectOrdersAction $action */
                $action = app(RejectOrdersAction::class);
                $action->createOrderHistory($order, $e->getMessage());
                $orderProducts = OrderProduct::query()->whereIn('id', $orderProductIds)->get();
                foreach ($orderProducts as $orderProduct) {
                    $supplierId = (int) $orderProduct->supplier_id;
                    $orderProduct->update([
                        'fulfill_status'        => OrderProductFulfillStatus::REJECTED,
                        'fulfill_exception_log' => $e->getMessage(),
                    ]);
                    if (!$supplierId || !is_supplier_support_api($supplierId)) {
                        continue;
                    }
                    try {
                        $objectProvider = $object->getProviderBySupplierId($supplierId);
                        if (!$objectProvider) {
                            continue;
                        }
                        if (!data_get($objectProvider, 'crawl_order')) {
                            continue;
                        }
                        CrawlAndUpdateOrderJob::dispatch($objectProvider, $orderProduct->fulfill_order_id ?? $orderProduct->order_id, $orderProduct->order_id, false, false, true);
                    } catch (Throwable $e) {
                        logException($e, 'Retried crawl order was rejected failed: ' . $orderProduct->order_id, DiscordChannel::FULFILL_ORDER, true);
                    }
                }
            }
        }

        if ($exception) {
            return $this->errorResponse();
        }

        return $this->successResponse($logs);
    }

    /**
     * @param Request $request
     * @param bool $isListing
     * @param array $select
     * @return Builder[]|\Illuminate\Database\Eloquent\Collection|Order[]
     */
    private function getOrders(Request $request, $isListing = true, $select = [])
    {
        $ordersIds                   = checkAndConvertToArray($request->get('order_ids'));
        $ordersProductIds            = checkAndConvertToArray($request->get('order_product_ids'));
        $orderStatus                 = checkAndConvertToArray($request->get('order_status'));
        $orderFulfillStatus          = checkAndConvertToArray($request->get('order_fulfill_status'));
        $orderProductFulfillStatuses = checkAndConvertToArray($request->get('order_product_fulfill_statuses'));
        $trackingStatus = checkAndConvertToArray($request->get('array_tracking_status'));
        $supplierId                  = $request->get('supplier_id');
        $dateColumn                  = $request->get('date_column', 'order.fulfilled_at');
        $dateRange                   = $request->get('date_range');
        $startDate                   = $request->get('start_date');
        $endDate                     = $request->get('end_date');
        $shippingMethod              = $request->get('shipping_method');
        $isShippingLate              = $request->boolean('is_shipping_late');
        $isExpressShippingLate       = $request->boolean('is_express_shipping_late');
        $isNeedDesign                = $request->boolean('is_need_design');

        if ($isShippingLate) {
            $dateColumn = 'order.fulfilled_at';
        }
        $orderProductQueryLateTimeCondition = OrderProduct::QUERY_LATE_TIME_CONDITION;
        $orderProductQueryDesignBtnExistedCondition = OrderProduct::QUERY_PRODUCT_DESIGNING_CONDITION;

        $querySelectedFields = OrderProduct::FIELD_FULFILL;
        if ($isShippingLate) {
            $querySelectedFields = array_merge($querySelectedFields, [
                DB::raw("$orderProductQueryLateTimeCondition AS late_time"),
            ]);
        } else if ($isNeedDesign) {
            $querySelectedFields = array_merge($querySelectedFields, [
                DB::raw("$orderProductQueryDesignBtnExistedCondition AS order_product_designing"),
            ]);
        }


        $orders = Order::query()
            ->select(Order::FIELD_FULFILL)
            ->when(!empty($select), fn($q) => $q->addSelect($select))
            ->when($ordersIds, fn($q) => $q->whereIn('id', $ordersIds))
            ->when(!$isListing, fn($q) => $q->where('address_verified', '!=', OrderAddressVerifiedEnum::INVALID)) // make sure post order need to check address verified
            ->with([
                'order_products' => fn($q) => $q->select($querySelectedFields)
                    ->when(!empty($ordersProductIds), function ($query) use ($ordersProductIds) {
                        return $query->whereIn('id', $ordersProductIds);
                    })
                    ->when(!empty($trackingStatus), function ($query) use ($trackingStatus) {
                        return $query->whereIn('tracking_status', $trackingStatus);
                    })
                    ->filterFulfillProduct($orderProductFulfillStatuses, $supplierId)
                    ->orderBy('supplier_id')
                    // to get status exception first
                    ->orderByRaw("FIELD(fulfill_status,'" . OrderProductFulfillStatus::REJECTED . "') desc"),
                'country_info:code,name',
                'seller:id,name,email',
            ])
            ->filterFulfillProduct(
                $dateRange,
                $startDate,
                $endDate,
                $orderStatus,
                $orderFulfillStatus,
                $shippingMethod,
                OrderSupportStatusEnum::ALL,
                OrderAssigneeEnum::ALL,
                $dateColumn,
                $isListing,
                false,
                $isShippingLate,
                $isExpressShippingLate
            )
            ->get();

        $campaignSystemType = [];
        // set relation Files to Order Products
        foreach ($orders as $key => $order) {
            $this->checkAndUpdateOrderProduct($order);
            // try catch if error will hold this order
            try {
                /** @var OrderProduct $order_product */
                foreach ($order->order_products as &$order_product) {
                    $options = json_decode($order_product->options);

                    $params = [
                        'order_id'         => $order->id,
                        'order_product_id' => $order_product->id,
                        'product_id'       => $order_product->product_id,
                        'color'            => correctOptionValue(optional($options)->color),
                    ];
                    if ($order_product->isFullPrintedType()) {
                        $params['size'] = correctOptionValue(optional($options)->size);
                    }
                    $request = new Request($params);
                    if (empty($campaignSystemType[$order_product->campaign_id])) {
                        $campaignSystemType[$order_product->campaign_id] = Product::whereId($order_product->campaign_id)->value('system_type');
                    }
                    [$order_product->files, $order_product->mockups] = $this->getFiles($request, $order_product, true);
                    // check if order product is not listing and is mugs product, then only get file by size
                    if (!$isListing && $order_product->files->isNotEmpty()) {
                        $mugsProductId = Template::getAndCacheProductIdByCategory(ProductCategoryEnum::MUGS);
                        if (!empty($mugsProductId) && !empty($order_product->options) && in_array((int) $order_product->template_id, $mugsProductId, true)) {
                            $productOptions = json_decode($order_product->options, true, 512, JSON_THROW_ON_ERROR);
                            if (isset($productOptions['size'])) {
                                $order_product->files = $order_product->files->filter(fn($file) => $file->print_space === str_replace('_', '', $productOptions['size']) || $file->print_space === 'default')->values();
                            }
                        }
                    }
                    if ($order_product->files->isEmpty() && !$order_product->isNoNeedDesignProductType()) {
                        if ($order_product->personalized === PersonalizedType::CUSTOM_OPTION || $campaignSystemType[$order_product->campaign_id] === ProductSystemTypeEnum::CUSTOM || $campaignSystemType[$order_product->campaign_id] === ProductSystemTypeEnum::MOCKUP) {
                            throw new RuntimeException('Need upload file design', 400);
                        }
                        throw new RuntimeException('Empty files design, Order product id: ' . $order_product->id . ', Order id: ' . $order_product->order_id);
                    }
                    if ($order_product->isFullPrintedType()) {
                        $counted = 0;
                        $order_product->files->each(static function ($each) use (&$counted) {
                            if ($each->type === FileTypeEnum::DESIGN && $each->print_space !== 'back') {
                                // $each->print_space !== 'back' => support the product full printed but have 2 designs
                                $counted++;
                            }
                        });
                        if ($counted > 1) {
                            throw new \RuntimeException('Too many designs. Order Product Id: ' . $order_product->id);
                        }
                    }
                }
            } catch (Throwable $e) {
                if (!$order->isServiceOrder() && !$order->isCustomServiceOrder()) {
                    if ($order->fulfill_status === OrderFulfillStatus::UNFULFILLED || !$isListing) {
                        if ($e->getCode() === 400) {
                            // update order and order product: fulfill_status => designing
                            $order->fulfill_status = OrderFulfillStatus::DESIGNING;
                            $order->order_products->map(function ($each) {
                                unset($each->files, $each->mockups);
                                $each->fulfill_status = OrderFulfillStatus::DESIGNING;
                            });
                        } else {
                            // update order and order product: fulfill_status => invalid
                            $order->fulfill_status = OrderFulfillStatus::INVALID;
                            $order->order_products->map(function ($each) use ($e) {
                                unset($each->files, $each->mockups);
                                $each->fulfill_status        = OrderFulfillStatus::INVALID;
                                $each->fulfill_exception_log = $e->getMessage();
                            });
                        }
                        $order->push();
                        unset($orders[$key]);
                    }
                }
            }
        }

        if ($isNeedDesign) {
            FulfillmentService::getNeedDesignTags($orders, false);
        }
        return $orders;
    }

    public function upload(Request $request): JsonResponse
    {
        try {
            Excel::import(new FulfillProductImport($request->supplier_id), $request->input_file_product);

            return $this->successResponse();
        } catch (Throwable $e) {
            logToDiscord('Upload product failed' . $e->getMessage(), 'fulfill_product', true);
            return $this->errorResponse();
        }
    }

    public function uploadSku(Request $request): JsonResponse
    {
        try {
            $supplier = suppliers()->firstWhere('supplier_id', $request->input('supplier_id'));
            if ( ! $importer = data_get($supplier, 'sku_importer')) {
                throw new RuntimeException(__('Supplier not supported'));
            }

            Excel::import(app($importer), $request->file('input_file'));

            return $this->successResponse();
        } catch (Throwable $e) {
            logToDiscord('Upload sku failed' . $e->getMessage(), 'fulfill_product', true);

            return $this->errorResponse($e->getMessage());
        }
    }

    private function checkAndUpdateOrderProduct(Order $order): void
    {
        $seller = currentUser()->getInfoAccess();
        $fulfillMappings = FulfillMapping::getAndCache(FulfillMappingEnum::SEPARATE_TRACKING);
        if ($fulfillMappings->isEmpty()) {
            return;
        }

        $arrNewAttribute = [
            'total_amount',
            'shipping_cost',
            'discount_amount',
            'seller_profit',
            'profit',
            'artist_profit',
            'sen_points',
        ];
        $arr             = [];

        foreach ($order->order_products as $orderProduct) {
            $check      = false;
            $templateId = $orderProduct->template_id;
            $supplierId = $orderProduct->supplier_id;
            $quantity   = $orderProduct->quantity;

            if ($quantity === 1) {
                $arr[] = $orderProduct;
                continue;
            }

            foreach ($fulfillMappings as $fulfillMapping) {
                /** @noinspection TypeUnsafeComparisonInspection */
                if (
                    $fulfillMapping->object_id == $templateId
                    &&
                    (
                        is_null($fulfillMapping->supplier_id)
                        ||
                        $fulfillMapping->supplier_id == $supplierId
                    )
                ) {
                    $check = true;
                    break;
                }
            }

            if (!$check) {
                $arr[] = $orderProduct;
                continue;
            }

            $designs = Design::query()
                ->where('order_id', $orderProduct->order_id)
                ->where('order_product_id', $orderProduct->id)
                ->get();

            $files = File::query()
                ->when(!OrderTypeEnum::isFulfillOrder($order->type), function ($query) use ($seller) {
                    $query->onSellerConnection($seller);
                })
                ->where('order_id', $orderProduct->order_id)
                ->where('order_product_id', $orderProduct->id)
                ->get();
            for ($i = 0; $i < $quantity; $i++) {
                $newOrderProduct           = $orderProduct->replicate();
                $newOrderProduct->quantity = 1;
                foreach ($arrNewAttribute as $attribute) {
                    $newOrderProduct->$attribute = $orderProduct->$attribute / $quantity;
                }
                $newOrderProduct->save();

                foreach ($designs as $design) {
                    $newDesign                   = $design->replicate();
                    $newDesign->order_product_id = $newOrderProduct->id;
                    $newDesign->save();
                }

                foreach ($files as $file) {
                    $newFile                   = $file->replicate();
                    $newFile->order_product_id = $newOrderProduct->id;
                    $newFile->save();
                }
                $arr[] = $newOrderProduct;
            }

            foreach ($designs as $design) {
                $design->delete();
            }

            foreach ($files as $file) {
                $file->delete();
            }

            $orderProduct->delete();
        }

        $order->setRelation('order_products', collect($arr));
    }

    public function uploadVariants(Request $request): JsonResponse
    {
        try {
            Excel::import(new FulfillProductVariantImport($request->supplier_id), $request->input_file_product_variant);

            return $this->successResponse();
        } catch (Throwable $e) {
            logToDiscord('Upload product variant failed' . $e->getMessage(), 'fulfill_product', true);
            return $this->errorResponse($e->getMessage());
        }
    }

    public function crawlProductsAndProductVariants(Request $request): JsonResponse
    {
        $supplierId = $request->supplier_id;
        try {
            $object         = new ObjectFulfill();
            $objectProvider = $object->getProviderBySupplierId($supplierId);

            if (!$objectProvider) {
                return $this->errorResponse('Supplier not found');
            }

            $objectProvider->crawlProductsAndProductVariants();
            return $this->successResponse();
        } catch (Throwable $e) {
            logToDiscord(
                'Crawl product variant failed. Supplier Id: '
                . $supplierId
                . json_encode($e->getMessage()),
                'fulfill_product',
                true
            );
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateOrders(Request $request): JsonResponse
    {
        $isManually                  = $request->boolean('is_manually', true);
        $ordersIds                   = $request->input('order_ids');
        $orderProductFulfillStatuses = $request->input('order_product_fulfill_statuses')
            ?? [
                OrderProductFulfillStatus::PENDING,
                OrderProductFulfillStatus::PROCESSING,
                OrderProductFulfillStatus::REJECTED,
                OrderProductFulfillStatus::EXCEPTION,
            ];
        if ($isManually) {
//            $orderProductFulfillStatuses[] = OrderProductFulfillStatus::FULFILLED;
            $orderProductFulfillStatuses[] = OrderProductFulfillStatus::ON_DELIVERY;
        }
        $orderProducts = OrderProduct::query()
            ->select(
                [
                    'order_id',
                    'supplier_id',
                    'supplier_name',
                    'fulfill_order_id',
                    'fulfill_status',
                ]
            )
            ->whereIn('order_id', $ordersIds)
            ->whereRaw("fulfill_order_id != ''")
            ->where('supplier_id', '>', 0)
            ->whereIn('fulfill_status', $orderProductFulfillStatuses)
            ->groupBy('supplier_id', 'fulfill_order_id')
            ->get();
        $object         = new ObjectFulfill();
        $logs           = [];

        $fulfillOrderIdsOnSuppliers = [];
        FulfillmentService::groupOrderProductsBySuppliers($orderProducts, $fulfillOrderIdsOnSuppliers);
        if (!empty($fulfillOrderIdsOnSuppliers)) {
            foreach ($fulfillOrderIdsOnSuppliers as $key => $opsOfSupplier) {
                /** @var ObjectFulfill $objectProvider */
                $objectProvider = $object->getProviderBySupplierId($key);
                if (!$objectProvider) {
                    continue;
                }

                if (empty($objectProvider->crawl_order ?? null)) {
                    $logs[] = 'This supplier id: ' . $key . ' don\'t have api for update order.';
                    continue;
                }

                $opsGroup = array_chunk($opsOfSupplier, 20);
                foreach ($opsGroup as $op) {
                    CrawlAndUpdateOrderJob::{$isManually ? 'dispatchSync' : 'dispatch'}(
                        $objectProvider,
                        $op,
                        0,
                        false,
                        $isManually
                    );
                }
            }
        }

        /** @var OrderProduct $op */
        foreach ($orderProducts as $op) {
            try {
                /** @var ObjectFulfill $objectProvider */
                $objectProvider = $object->getProviderBySupplierId($op->supplier_id);
                if (!$objectProvider) {
                    continue;
                }

                // ignore when supplier don't have api for update order
                if (empty($objectProvider->crawl_order ?? null)) {
                    $logs[] = 'This supplier: ' . $op->supplier_name . ' don\'t have api for update order.';
                    continue;
                }
                CrawlAndUpdateOrderJob::{$isManually ? 'dispatchSync' : 'dispatch'}($objectProvider, $op->fulfill_order_id, $op->order_id, $op->justCreated(), $isManually);
            } catch (Throwable $e) {
                $logs[] = $e->getMessage();
            }
        }

        if (!empty($logs)) {
            return $this->errorResponse(implode(', ', $logs));
        }

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateOrdersTrackingStatus(Request $request): JsonResponse
    {
        $ordersIds = $request->input('order_ids');
        $ordersProductIds = $request->input('order_product_ids');
        $ordersIds = checkAndConvertToArray($ordersIds);
        $ordersProductIds = checkAndConvertToArray($ordersProductIds);
        if (empty($ordersIds) && empty($ordersProductIds)) {
            return $this->errorResponse('Order ids or order product ids is required');
        }
        $orderProductFulfillStatuses = $request->input('order_product_fulfill_statuses', [
            OrderProductFulfillStatus::PENDING,
            OrderProductFulfillStatus::PROCESSING,
            OrderProductFulfillStatus::REJECTED,
            OrderProductFulfillStatus::EXCEPTION,
        ]);
        $orderProductFulfillStatuses[] = OrderProductFulfillStatus::FULFILLED;
        $orderProductFulfillStatuses[] = OrderProductFulfillStatus::ON_DELIVERY;
        $orderProducts = OrderProduct::query()
            ->select([
                'id',
                'order_id',
                'tracking_code',
                'shipping_carrier',
                'tracking_service',
                'tracking_url',
                'fulfilled_at',
                'received_at',
                'delivered_at',
                'tracking_status as status',
                'supplier_name',
                'supplier_id',
                'fulfill_status',
            ])
            ->with('order:id,order_number,country')
            ->when(!empty($ordersIds), function ($query) use ($ordersIds) {
                return $query->whereIn('order_id', $ordersIds);
            })
            ->when(!empty($ordersProductIds), function ($query) use ($ordersProductIds) {
                return $query->whereIn('id', $ordersProductIds);
            })
            ->where('tracking_code', '!=', '')
            ->whereNotNull('tracking_code')
            ->whereIn('fulfill_status', $orderProductFulfillStatuses)
            ->groupBy('tracking_code')
            ->get();
        if ($orderProducts->isEmpty()) {
            return $this->errorResponse('No order found');
        }
        $result = [];
        if ($orderProducts->count() > 30) {
            foreach ($orderProducts->chunk(30) as $group) {
                ProcessUpdateOrderTrackingStatus::dispatch($group->toArray())->onQueue(QueueName::ORDER_EVENTS)->delay(now()->addSeconds(5));
            }
        } else {
            $result = (new ProcessUpdateOrderTrackingStatus($orderProducts->toArray()))->handle();
        }
        return $this->successResponse($result);
    }

    /**
     * @param array $response
     * @param bool $isManually
     * @param string $region
     * @return void
     * @throws Throwable
     */
    public static function updateByResponse(array $response, $isManually = false, $region = 'sg'): void
    {
        try {
            $orderProductFulfilled = false;
            $supplierId            = $response['supplier_id'];
            $fulfillOrderId        = $response['order_id'];
            $orderId               = $response['senprint_order_id'];
            $orderProductId        = Arr::get($response, 'senprint_order_product_id');
            $newFulfillOrderId     = $response['new_fulfill_order_id'];
            $fulfillStatus         = $response['fulfill_status'];
            $isException           = $response['fulfill_exception'];
            $fulfillExceptionLog   = $response['fulfill_exception_log'];
            if (!in_array($fulfillStatus, OrderProductFulfillStatus::asArray(), true)) {
                throw new RuntimeException('Invalid fulfill status');
            }
            if (empty($orderId) && empty($fulfillOrderId)) {
                throw new RuntimeException('Empty order id and fulfill order id');
            }
        } catch (\Throwable $e) {
            if (!$isManually) {
                return;
            }
            throw $e;
        }
        $detail   = '';
        $tracking = [
            'tracking_code'    => null,
            'shipping_carrier' => null,
            'tracking_url'     => null,
        ];
        $trackingService = TrackingService::instance();
        // update fulfillments cancel
        if (!empty($response['fulfillments_cancelled'])) {
            foreach ($response['fulfillments_cancelled'] as $fulfillment) {
                OrderProduct::onRegion($region)
                    ->where('supplier_id', $supplierId)
                    ->whereIn('fulfill_status', [
                        OrderProductFulfillStatus::PENDING,
                        OrderProductFulfillStatus::REJECTED,
                        OrderProductFulfillStatus::PROCESSING,
                        OrderProductFulfillStatus::EXCEPTION,
                    ])
                    ->where(function ($query) use ($orderId, $fulfillOrderId) {
                        return $query->where('order_id', $orderId)->orWhere('fulfill_order_id', $fulfillOrderId);
                    })
                    ->when(!empty($orderProductId), fn($q) => $q->where('id', $orderProductId))
                    ->where(function ($q) use ($fulfillment) {
                        return $q->where('id', Arr::get($fulfillment, 'id'))->orWhere('fulfill_sku', $fulfillment['sku']);
                    })
                    ->update([
                        'fulfill_status'        => OrderProductFulfillStatus::REJECTED,
                        'fulfill_exception_log' => 'Order is cancelled by supplier. Log: ' . $fulfillment['log'],
                    ]);

                $orderProducts = OrderProduct::onRegion($region)
                    ->select(['id', 'order_id', 'fulfill_status'])
                    ->where('supplier_id', $supplierId)
                    ->where(function ($query) use ($orderId, $fulfillOrderId) {
                        return $query->where('order_id', $orderId)->orWhere('fulfill_order_id', $fulfillOrderId);
                    })
                    ->when(!empty($orderProductId), fn($q) => $q->where('id', $orderProductId))
                    ->where(function ($q) use ($fulfillment) {
                        return $q->where('id', Arr::get($fulfillment, 'id'))->orWhere('fulfill_sku', $fulfillment['sku']);
                    })->get();
                if ($orderProducts->isNotEmpty()) {
                    $countOrderProduct = $orderProducts->count();
                    $countOrderProductRejected = $orderProducts->where('fulfill_status', OrderProductFulfillStatus::REJECTED)->count();
                    if ($countOrderProduct === $countOrderProductRejected) {
                        $processingOrderIds = $orderProducts->pluck('order_id')->unique()->values()->toArray();
                        Order::onRegion($region)->whereIn('id', $processingOrderIds)
                            ->where('status', OrderStatus::COMPLETED)
                            ->whereIn('fulfill_status', [OrderFulfillStatus::ON_DELIVERY, OrderFulfillStatus::FULFILLED])
                            ->update([
                                'status' => OrderStatus::PROCESSING,
                                'fulfill_status' => OrderFulfillStatus::PROCESSING,
                            ]);
                    }
                }
            }
        }
        try {
            DB::beginTransaction();
            // update fulfillments processing
            $orderIds = [];
            if (!empty($response['fulfillments'])) {
                foreach ($response['fulfillments'] as $fulfillment) {
                    $arrSku = [];
                    $arrId  = [];
                    if (empty($fulfillment['no_items'])) {
                        if (empty($fulfillment['items'])) {
                            throw new RuntimeException('Fulfillment items is empty');
                        }
                        foreach ($fulfillment['items'] as $item) {
                            if (!empty(Arr::get($item, 'sku'))) {
                                $arrSku[] = Arr::get($item, 'sku');
                            }
                            if (!empty(Arr::get($item, 'id'))) {
                                $arrId[] = Arr::get($item, 'id');
                            }
                        }
                    }

                    if (!empty($fulfillment['tracking'])) {
                        $tracking = $fulfillment['tracking'];
                    }

                    if (!isset($tracking['tracking_url'])) {
                        $tracking['tracking_url'] = null;
                    }

                    if (!empty($tracking['tracking_code']) && is_array($tracking['tracking_code'])) {
                        $tracking['tracking_code'] = data_get($tracking, 'tracking_code.0');
                    }

                    if (empty($tracking['shipping_carrier']) && !empty($tracking['tracking_code'])) {
                        $carrier = $trackingService->detectCarrier($tracking['tracking_code'], null, null, $tracking['tracking_url']);
                        if ($carrier) {
                            $tracking['shipping_carrier'] = $trackingService->carriers()->where('courierCode', $carrier)->value('courierName');
                        }
                    }

                    $data = [
                        'tracking_code'         => trim($tracking['tracking_code']),
                        'shipping_carrier'      => $tracking['shipping_carrier'],
                        'tracking_url'          => $tracking['tracking_url'],
                        'fulfill_status'        => $fulfillment['fulfill_status'] ?? $fulfillStatus,
                        'fulfill_exception_log' => $fulfillExceptionLog,
                    ];
                    $arrFulfillStatus = OrderProductFulfillStatus::getForUpdate($isManually);
                    DB::enableQueryLog();
                    $queryOrderProducts = OrderProduct::onRegion($region)
                        ->select([
                            'id',
                            'order_id',
                            'fulfill_order_id',
                            'fulfill_status',
                            'product_name',
                            'tracking_code',
                            'tracking_url',
                            'tracking_status',
                            'total_reprint',
                            'fulfilled_at',
                            'received_at',
                            'delivered_at',
                            'supplier_id',
                            'supplier_name',
                            'fulfill_sku',
                            'fulfill_exception_log'
                        ])
                        ->with(['order:id,type,status,fulfill_status,shipping_label'])
                        ->where('supplier_id', $supplierId)
                        ->when(!empty($orderProductId), fn($q) => $q->where('id', $orderProductId))
                        ->when($orderId, fn ($q) => $q->where('order_id', $orderId))
                        ->when($fulfillOrderId, fn ($q) => $q->where('fulfill_order_id', (string) $fulfillOrderId))
                        ->when(empty($fulfillment['no_items']), function ($q) use ($arrId, $arrSku) {
                            return $q->where(function ($q) use ($arrId, $arrSku) {
                                return $q->when(!empty($arrId), fn($q) => $q->whereIn('id', $arrId))
                                    ->when(!empty($arrSku), fn($q) => $q->orWhereIn('fulfill_sku', $arrSku));
                            });
                        });
                    $orderProducts = $queryOrderProducts->get();
                    $oldFulfillOrderId = null;
                    $cloneOrderProducts = clone $orderProducts;
                    $filterOrderProducts = $cloneOrderProducts->whereIn('fulfill_status', $arrFulfillStatus);
                    $queryLog = DB::getQueryLog();
                    graylogInfo('Debug Update Order Fulfill In UpdateByResponse', [
                        'category' => 'debug_update_order_fulfill',
                        'query_log' => json_encode($queryLog),
                        'is_manually' => (int)$isManually
                    ]);
                    if ($filterOrderProducts->isEmpty()) {
                        // ignore if supplier is print logistic v1
                        if ($supplierId != SupplierEnum::PRINTLOGISTIC) {
                            graylogInfo('Empty order products', [
                                'category' => DiscordChannel::FULFILL_ORDER,
                                'method_name' => __FUNCTION__,
                                'fulfill_status_text' => $orderProducts->pluck('fulfill_status')->toJson(),
                                'params' => json_encode($response),
                            ]);
                        }
                        continue;
                    }
                    foreach ($filterOrderProducts as $orderProduct) {
                        /** @var OrderProduct $orderProduct */
                        $oldFulfillOrderId ??= $orderProduct->fulfill_order_id;
                        $oldFulfillStatus = $orderProduct->fulfill_status;
                        if ($oldFulfillOrderId !== $orderProduct->fulfill_order_id) {
                            throw new RuntimeException('This order has multiple fulfill order id');
                        }

                        $detail .= $orderProduct->product_name . '|';
                        $orderProduct->fill($data);
                        if (!empty($newFulfillOrderId)) {
                            $orderProduct->fulfill_order_id = $newFulfillOrderId;
                        }
                        if ($orderProduct->fulfill_status === OrderProductFulfillStatus::PENDING && $isException) {
                            $orderProduct->fulfill_status = OrderProductFulfillStatus::REJECTED;
                        }

                        // prevent null or ""
                        if (empty($orderId) && !empty($orderProduct->order_id)) {
                            $orderId = $orderProduct->order_id;
                        }
                        // check if have change something
                        if ($orderProduct->fulfill_status === OrderProductFulfillStatus::FULFILLED && $orderProduct->isDirty()) {
                            if (!empty($orderProduct->tracking_code)) {
                                $orderProductFulfilled = true;
                                // update fulfill status if have shipping label and tracking status is info received, new, not found is processing
                                if ($orderProduct->order->type === OrderTypeEnum::FBA
                                    && $orderProduct->order->shipping_label
                                    && $orderProduct->supplier_id === SupplierEnum::GEARMENT_TWO_DAYS
                                    && in_array($orderProduct->tracking_status, [TrackingStatusEnum::INFO_RECEIVED, TrackingStatusEnum::NEW, TrackingStatusEnum::NOTFOUND], true)
                                ) {
                                    $orderProduct->fulfill_status = OrderProductFulfillStatus::PROCESSING;
                                } else {
                                    $orderProduct->delivered_at = now();
                                    $orderProduct->processing_day = diffProcessingDay($orderProduct->fulfilled_at);
                                    $orderProduct->fulfill_status = OrderProductFulfillStatus::ON_DELIVERY;
                                    if (!empty($orderProduct->order_id)) {
                                        $orderIds[$orderProduct->order_id] = $orderProduct->order_id;
                                    }
                                }
                            } else {
                                $orderProduct->fulfill_status = OrderProductFulfillStatus::EXCEPTION;
                                $orderProduct->fulfill_exception_log = 'Fulfillment has no tracking code';
                            }
                        }

                        if ($orderProduct->fulfill_status === OrderProductFulfillStatus::PROCESSING && in_array($oldFulfillStatus, [OrderProductFulfillStatus::UNFULFILLED, OrderProductFulfillStatus::REJECTED], true)) {
                            $orderProduct->total_reprint = is_null($orderProduct->total_reprint) ? 0 : ($orderProduct->total_reprint + 1);
                        }
                        $orderProduct->save();
                    }
                    unset($filterOrderProducts);
                    $orderProducts = $queryOrderProducts->get();
                    if ($orderProducts->isNotEmpty()) {
                        $countOrderProduct = $orderProducts->count();
                        $countOrderProductRejected = $orderProducts->where('fulfill_status', OrderProductFulfillStatus::REJECTED)->count();
                        if ($countOrderProduct === $countOrderProductRejected) {
                            $processingOrderIds = $orderProducts->pluck('order_id')->unique()->values()->toArray();
                            Order::onRegion($region)->whereIn('id', $processingOrderIds)
                                ->where('status', OrderStatus::COMPLETED)
                                ->whereIn('fulfill_status', [OrderFulfillStatus::ON_DELIVERY, OrderFulfillStatus::FULFILLED])
                                ->update([
                                    'status' => OrderStatus::PROCESSING,
                                    'fulfill_status' => OrderFulfillStatus::PROCESSING,
                                ]);
                        }
                        unset($orderProducts);
                    }
                    $trackingCodes = $queryOrderProducts->select([
                        'id',
                        'order_id',
                        'tracking_code',
                        'shipping_carrier',
                        'tracking_url',
                        'fulfilled_at',
                        'received_at',
                        'delivered_at',
                        'tracking_status as status',
                        'supplier_name',
                        'supplier_id',
                        'fulfill_status',
                    ])
                    ->with('order:id,order_number,country')
                    ->whereNotNull('tracking_code')
                    ->where('tracking_code', '!=', '')
                    ->groupBy('tracking_code')
                    ->get();
                    if ($trackingCodes->isNotEmpty()) {
                        $trackingCodes->chunk(40)->each(function ($chunkTrackingCodes) use ($trackingService) {
                            dispatch(function () use ($chunkTrackingCodes, $trackingService) {
                                $trackingService->registerOrderTracking($chunkTrackingCodes);
                            })->onQueue(QueueName::ORDER);
                        });
                    }
                    unset($queryOrderProducts);
                }
            }
            if (!empty($orderIds)) {
                foreach ($orderIds as $orderId) {
                    SendSmsUpdateOrderStatusToCustomer::dispatch($orderId);
                }
            }
            // check if still empty orderId
            if (empty($orderId)) {
                throw new RuntimeException('Not found order');
            }
            $order = Order::onRegion($region)->whereKey($orderId)->firstOrFail();
            DB::commit();
        } catch (Throwable $e) {
            graylogError('Error in process update and response with supplier', [
                'category' => 'error_in_update_with_response_supplier',
                'supplier_id' => $supplierId,
                'message' => $e->getMessage(),
                'response' => json_encode($response)
            ]);
            logException($e);
            DB::rollBack();
            if (!$isManually) {
                return;
            }
            throw $e;
        }
        /** @var Order $order */
        if ($isException) {
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::FULFILL_UPDATED,
                $fulfillExceptionLog,
            );
        }

        // order updated send email
        if ($orderProductFulfilled === true) {
            // update gateway tracking
            OrderProductFulfilled::dispatch($orderId, $tracking);

            // send email customer
            OrderFulfilled::dispatch($orderId);
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::FULFILLED,
                $detail,
                OrderHistoryDisplayLevelEnum::CUSTOMER,
            );
            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::EMAIL_TO_CUSTOMER,
                'Fulfillment notification',
            );
            if (empty($order->delivered_at)) {
                $order->delivered_at = now();
            }
        }
        $countUnfulfilled = OrderProduct::onRegion($region)
            ->where('order_id', $orderId)
            ->whereNotIn('fulfill_status', [
                OrderProductFulfillStatus::FULFILLED,
                OrderProductFulfillStatus::CANCELLED,
                OrderProductFulfillStatus::ON_DELIVERY,
            ])
            ->count();
        $countProductPreShipment = OrderProduct::onRegion($region)
            ->where('order_id', $orderId)
            ->whereHas('order', function ($q) {
                $q->where('type', OrderTypeEnum::FBA);
                $q->whereNotNull('shipping_label');
            })
            ->whereIn('tracking_status', [
                TrackingStatusEnum::INFO_RECEIVED,
                TrackingStatusEnum::NEW,
                TrackingStatusEnum::NOTFOUND
            ])
            ->where('supplier_id', SupplierEnum::GEARMENT_TWO_DAYS)
            ->whereNotNull('tracking_code')
            ->count();

        if ($countUnfulfilled === 0) {
            if (in_array($order->status, [OrderStatus::PENDING, OrderStatus::PROCESSING], true)) {
                $order->status = OrderStatus::COMPLETED;
            }
            $order->fulfill_status = OrderFulfillStatus::ON_DELIVERY;
            if ($countProductPreShipment > 0) {
                $order->fulfill_status = OrderFulfillStatus::PROCESSING;
            }
        }
        if ($order->isDirty()) {
            $order->save();
        }
    }

    public function webhookSupplierCallback(Request $request): JsonResponse
    {
        $token    = request('token');
        $info     = request('api_key');
        $response = [];
        try {
            if ($info !== null && $info->type === AccessExternalApiType::SUPPLIER) {
                // Supplier Process
                $supplierId = $info->reference_id;
                if ($supplierId !== SupplierEnum::JONDO) {
                    $data = $request->toArray();
                } else {
                    // this supplier request xml
                    $data = simplexml_load_string($request->getContent());
                }

                switch ($supplierId) {
                    case SupplierEnum::PRINTLOGISTIC:
                        $response = (new PrintLogisticWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::PRINTLOGISTIC_V2:
                    case SupplierEnum::PRINTLOGISTIC_UK:
                    case SupplierEnum::PRINTLOGISTIC_US:
                        $response = (new PrintLogisticV2WebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::MONSTERDIGITAL:
                        $jsonData = $request->json('data');
                        if (!is_null($jsonData)) {
                            $data     = json_decode($jsonData, true, 512, JSON_THROW_ON_ERROR);
                            $object   = new MonsterDigitalWebhookRepository($data, $supplierId);
                            $response = $object->get();

                            logHttp(
                                [
                                    'from'             => $token,
                                    'request_body'     => json_encode($data),
                                    'response_body'    => json_encode($response),
                                    'reference_id'     => $supplierId,
                                    'reference_type'   => AccessExternalApiType::SUPPLIER,
                                    'order_id'         => Arr::get($response, 'senprint_order_id'),
                                    'fulfill_order_id' => Arr::get($response, 'order_id'),
                                    'type'             => ApiLogTypeEnum::WEBHOOK,
                                ]
                            );

                            return $this->checkResponseMonsterDigital($response, $object);
                        }
                        break;
                    case SupplierEnum::DREAMSHIP:
                    case SupplierEnum::DREAMSHIP_TWO_DAYS:
                        $response = (new DreamshipWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::SHIRTPLATFORM:
                        $response = (new ShirtPlatformWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::CUSTOMCAT:
                        $response = (new CustomCatWebhookRepository($data, $supplierId))->get();
                        logHttp(
                            [
                                'from'             => $token,
                                'request_body'     => json_encode($data),
                                'response_body'    => json_encode($response),
                                'reference_id'     => $supplierId,
                                'reference_type'   => AccessExternalApiType::SUPPLIER,
                                'order_id'         => Arr::get($response, 'senprint_order_id'),
                                'fulfill_order_id' => Arr::get($response, 'order_id'),
                                'type'             => ApiLogTypeEnum::WEBHOOK,
                            ]
                        );

                        return $this->checkResponseCustomCat($response);
                    case SupplierEnum::JONDO:
                        // cause data is xml => array
                        $data     = json_decode(json_encode($data), true);
                        $response = (new JonDoWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::MWW:
                        $response = (new MWWWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::PRINTFORIA:
                        $response = (new PrintforiaWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::PRINTWAY:
                        $response = (new PrintwayWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::GEARMENT:
                    case SupplierEnum::GEARMENT_LABEL:
                    case SupplierEnum::GEARMENT_TWO_DAYS:
                    case SupplierEnum::GEARMENT_TWO_DAYS_LABEL:
                        $response = (new GearmentWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::PRINTGEEK:
                        $response = (new PrintGeekWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::SWIFTPOD:
                    case SupplierEnum::SWIFTPOD_TIKTOK:
                    case SupplierEnum::SWIFTPOD_TIKTOK_LABEL:
                        $response = (new SwiftPODWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::DUPLIUM:
                        $response = (new DupliumWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::QTCO:
                        $response = (new QTCOWebhookRepository($data, $supplierId))->get();
                        break;
                    case SupplierEnum::BEE_FUL:
                        $response = (new BeefulWebhookRepository($data[0] ?? [], $supplierId))->get();
                        break;
                    case SupplierEnum::PRINTIK:
                        $response = (new PrintikWebhookRepository($data ?? [], $supplierId))->get();
                        break;
                    case SupplierEnum::BONDRE:
                        $response = (new BondreWebhookRepository($data ?? [], $supplierId))->get();
                        break;
                    case SupplierEnum::MERCHIZE:
                        $response = (new MerchizeWebhookRepository($data ?? [], $supplierId))->get();
                        break;
                    case SupplierEnum::PENTIFINE:
                        $response = (new PentifineWebhookRepository($data ?? [], $supplierId))->get();
                        break;
                    case SupplierEnum::ONEPRINT:
                        $response = (new OneprintWebhookRepository($data ?? [], $supplierId))->get();
                        break;
                    default:
                        break;
                }

                // Đối với một số supplier trong 1 lần call webhook có thể sẽ trả về nhiều order (Ex: BeeFul, ..etc)
                // Lúc đó sẽ cần suy nghĩ giải pháp tái cấu trức flow này. (Hiện tại đang force xử lý cho 1 order)
                $jsonResponse = $this->checkResponse($response);
                logHttp(
                    [
                        'from'             => $token,
                        'request_body'     => json_encode($data),
                        'response_body'    => $jsonResponse->content(),
                        'reference_id'     => $supplierId,
                        'reference_type'   => AccessExternalApiType::SUPPLIER,
                        'order_id'         => Arr::get($response, 'senprint_order_id'),
                        'fulfill_order_id' => Arr::get($response, 'order_id'),
                        'type'             => ApiLogTypeEnum::WEBHOOK,
                    ]
                );

                return $jsonResponse;
            }
            $data = [
                'success' => false,
                'message' => 'Unauthorized'
            ];
            $jsonResponse = response()->json(
                $data,
                403
            );
            logHttp(
                [
                    'from'          => $token,
                    'request_body'  => $request->getContent(),
                    'response_body' => $jsonResponse->content(),
                ]
            );

            return $jsonResponse;
        } catch (Throwable $e) {
            logToDiscord(
                'Method: ' . __FUNCTION__
                . "\r\nError: " . $e->getMessage()
                . "\r\nResponse: " . json_encode($response)
                , DiscordChannel::FULFILL_ORDER
                , true
            );
            $data         = [
                'success' => false,
                'message' => $e->getMessage()
            ];
            $jsonResponse = response()->json(
                $data,
                500
            );
            logHttp(
                [
                    'from'           => $token,
                    'request_body'   => $request->getContent(),
                    'response_body'  => $jsonResponse->content(),
                    'reference_id'   => $supplierId ?? null,
                    'reference_type' => AccessExternalApiType::SUPPLIER,
                    'type'           => ApiLogTypeEnum::WEBHOOK,
                ]
            );

            return $jsonResponse;
        }
    }

    private function checkResponseMonsterDigital(
        $response,
        $object
    ): JsonResponse {
        // Update DB
        if (!empty($response)) {
            try {
                self::updateByResponse($response);
                // {
                // "res": "success",
                // "time": "Fri, 09 Aug 2019 11:48:13 -0800",
                // "xid": "12345678",
                // "receipt_id": "56789"
                //}
                return response()->json(
                    [
                        'res'        => 'success',
                        'time'       => 'Updated successfully',
                        'xid'        => $object->getSupplierOrderId(),
                        'receipt_id' => $object->receiptId,
                    ]
                );
            } catch (Throwable $e) {
                logToDiscord(
                    'Method: ' . __FUNCTION__
                    . "\r\nError: " . $e->getMessage()
                    . "\r\nResponse: " . json_encode($response)
                    , DiscordChannel::FULFILL_ORDER
                    , true
                );
                return response()->json(
                    [
                        'error'   => 'exception',
                        'code'    => $e->getCode(),
                        'message' => $e->getMessage(),
                    ],
                    400
                );
            }
        }
        // Default return false
        return response()->json(
            [
                'error'   => 'parsing_error',
                'code'    => 400,
                'message' => 'Parsing Error',
            ],
            400
        );
    }

    private function checkResponseCustomCat($response): JsonResponse
    {
        if (!empty($response)) {
            try {
                self::updateByResponse($response, true);
            } catch (Throwable $e) {
                logToDiscord(
                    'Method: ' . __FUNCTION__
                    . "\r\nError: " . $e->getMessage()
                    . "\r\nResponse: " . json_encode($response)
                    , DiscordChannel::FULFILL_ORDER
                    , true
                );
            }
        }

        return response()->json(
            [
                'success' => true,
                'message' => 'Updated successfully',
                'data'    => $response,
            ]
        );
    }

    private function checkResponse($response): JsonResponse
    {
        if (!empty($response)) {
            try {
                self::updateByResponse($response, true);
                return response()->json(
                    [
                        'success' => true,
                        'message' => 'Updated successfully',
                        'data'    => $response,
                    ]
                );
            } catch (Throwable $e) {
                logToDiscord(
                    'Method: ' . __FUNCTION__
                    . "\r\nError: " . $e->getMessage()
                    . "\r\nResponse: " . json_encode($response)
                    , DiscordChannel::FULFILL_ORDER
                    , true
                );
            }
        }
        // Default return false
        return response()->json(
            [
                'success' => false,
                'message' => 'Update fulfillment data failed',
                'data'    => $response,
            ],
            400
        );
    }

    public function cancelOrders(Request $request): JsonResponse
    {
        $orderIds = $request->get('order_ids');

        $orders = Order::query()
            ->select('id')
            ->selectForHistory()
            ->with('order_products:id,order_id')
            ->whereDoesntHave('order_products', function ($q) {
                return $q->whereIn('fulfill_status', [
                    OrderProductFulfillStatus::FULFILLED,
                    OrderProductFulfillStatus::PROCESSING,
                    OrderProductFulfillStatus::EXCEPTION,
                    OrderProductFulfillStatus::PENDING,
                ]);
            })
            ->whereIn('id', $orderIds)
            ->whereIn('fulfill_status', [
                OrderFulfillStatus::UNFULFILLED,
                OrderFulfillStatus::PROCESSING,
                OrderFulfillStatus::ON_HOLD,
                OrderFulfillStatus::INVALID,
                OrderFulfillStatus::REVIEWING,
                OrderFulfillStatus::DESIGNING,
            ])
            ->get();

        $countProducts = 0;
        $countOrders   = $orders->count();

        try {
            foreach ($orders as $order) {
                $order->status         = OrderStatus::CANCELLED;
                $order->fulfill_status = OrderFulfillStatus::CANCELLED;
                $order->order_products->map(fn($each) => $each->fulfill_status = OrderProductFulfillStatus::CANCELLED);
                $order->push();
                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::CANCELLED,
                );
            }
            return $this->successResponse(
                [
                    'count_order'    => $countOrders,
                    'count_products' => $countProducts,
                ]
            );
        } catch (Throwable $exception) {
            logException($exception, __FUNCTION__, DiscordChannel::FULFILL_ORDER, true);
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function suspendOrders(Request $request): JsonResponse
    {
        $orderIds = $request->get('order_ids');

        $orders = Order::query()
            ->select('id')
            ->selectForHistory()
            ->whereIn('id', $orderIds)
            ->get();

        $countOrders = $orders->count();

        try {
            foreach ($orders as $order) {
                $order->status = OrderStatus::SUSPENDED;
                $order->save();
                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::SUSPENDED,
                );
            }
            return $this->successResponse(
                [
                    'count_order' => $countOrders,
                ]
            );
        } catch (Throwable $exception) {
            logException($exception, __FUNCTION__, DiscordChannel::FULFILL_ORDER, true);
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    public function rejectOrders(Request $request): JsonResponse
    {
        $user = currentUser();
        try {
            /** @var RejectOrdersAction $action */
            $action = app(RejectOrdersAction::class);

            return $this->successResponse(
                $action->handle(
                    checkAndConvertToArray($request->get('order_ids')),
                    checkAndConvertToArray($request->get('order_product_ids'))
                )
            );
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @return array [files, mockups]
     * @throws Throwable
     */
    public function getFiles(Request $request, $orderProduct = null, $isServerSide = false): array
    {
        $user = currentUser();
        $orderId        = $request->get('order_id');
        $orderProductId = $request->get('order_product_id');
        $productId      = $request->get('product_id');
        $color          = $request->get('color');
        $size           = $request->get('size');
        $fullPath       = $request->boolean('full_path');
        $isSupplier     = $user->isSupplier();
        $isSeller       = $user->isSeller();
        if ($isServerSide || $fullPath) {
            $fullPath = true;
        } else {
            $fullPath = false;
        }
        try {
            return FulfillmentService::getOrderProductFiles($orderProductId, $orderId, $productId, $size, $color, $fullPath, $isSupplier, $orderProduct, $isSeller);
        } catch (Throwable $e) {
            throw new RuntimeException($e->getMessage());
        }
    }

    public function assignSupplier(Request $request): JsonResponse
    {
        $ordersIds          = $request->post('order_ids', []);
        $ordersProductIds   = $request->post('order_product_ids', []);
        $selectedSupplierId = $request->post('selected_assign_supplier_id');
        $forceAssign        = $selectedSupplierId ? $request->post('force_assign', false) : false;

        $orders = Order::query()
            ->selectForHistory(
                [
                    'id',
                    'shipping_method',
                    'address',
                    'state',
                    'country',
                    'order_number',
                    'paid_at',
                    'seller_id'
                ]
            )
            ->whereIn('id', $ordersIds)
            ->with(
                [
                    'order_products' => function ($query) use ($ordersProductIds) {
                        $query->when(!empty($ordersProductIds), static fn($q) => $q->whereIn('id', $ordersProductIds))
                            ->filterFulfill(
                                [
                                    OrderProductFulfillStatus::UNFULFILLED,
                                    OrderProductFulfillStatus::REJECTED,
                                    OrderProductFulfillStatus::ON_HOLD,
                                    OrderProductFulfillStatus::INVALID,
                                    OrderProductFulfillStatus::REVIEWING,
                                    OrderProductFulfillStatus::DESIGNING,
                                    OrderProductFulfillStatus::NO_SHIP, // allow assign supplier for no ship order product
                                ],
                                null, // any suppliers
                                true // include pending fulfill order
                            )
                            ->with([
                                'template:id,options',
                                'seller:id,tags,name,custom_payment'
                            ]);
                    }
                ]
            )
            ->filterFulfill(
                [
                    OrderStatus::DRAFT,
                    OrderStatus::PROCESSING,
                    OrderStatus::ON_HOLD,
                    OrderStatus::PENDING,
                ],
                [
                    OrderFulfillStatus::PROCESSING,
                    OrderFulfillStatus::UNFULFILLED,
                    OrderFulfillStatus::ON_HOLD,
                    OrderFulfillStatus::INVALID,
                    OrderFulfillStatus::REVIEWING,
                    OrderFulfillStatus::DESIGNING,
                    OrderFulfillStatus::NO_SHIP, // allow assign supplier for no ship order
                ],
                OrderSupportStatusEnum::ALL,
                true // include pending fulfill order
            )
            ->get();

        $errorOrderNumbers = [];
        $disabledCountry = SystemConfig::getConfig('disable_country', '');
        // Danh sách các order_product_id đã được assign
        // sẽ được dùng để lọc ra các order_product_id chưa được assign
        $ordersProductIdsAssigned = [];
        $ordersChangedProcessing = [];
        foreach ($orders as $order) {
            $isChangedNoShip = false;
            try {
                if (!$orderLocation = SystemLocation::findByCountryCodeThenSetForAssign($order)) {
                    continue;
                }
                if (!empty($disabledCountry) && str_contains($disabledCountry, $order->country)) {
                    continue;
                }
                if ($order->isCustomServiceOrder() || $order->isServiceOrder()) {
                    continue;
                }
                // nếu đơn hàng được đánh dấu là fulfill manually thì sau khi assign đc sup xong
                // đánh dấu đơn là processing để không đẩy tự động, và đơn có thể import đc tracking sau đó
                if (!empty($order->admin_note) && (int) $order->support_status === OrderSupportStatusEnum::FULFILL_MANUALLY) {
                    $order->status = OrderStatus::PROCESSING;
                    $order->fulfill_status = OrderFulfillStatus::PROCESSING;
                    $ordersChangedProcessing[$order->id] = $order->id;
                }
                $detail = '';
                foreach ($order->order_products as $orderProduct) {
                    $no_supplier_id = is_null($orderProduct->supplier_id) || (int) $orderProduct->supplier_id === 0;
                    if ($selectedSupplierId && (int)$orderProduct->supplier_id !== (int)$selectedSupplierId) {
                        $supplierSupportTypeConfig = suppliers()->whereNotNull('support_type')->select('supplier_id', 'support_type')->where('supplier_id', $selectedSupplierId)->first();
                        $supplierSupportType = $supplierSupportTypeConfig ? data_get($supplierSupportTypeConfig, 'support_type', []) : [];
                        if (!empty($supplierSupportTypeConfig) && !empty($supplierSupportType)) {
                            $files = collect();
                            if (Str::isJson($orderProduct->options)) {
                                $options = json_decode($orderProduct->options, false, 512, JSON_THROW_ON_ERROR);
                                $color = correctOptionValue(optional($options)->color);
                                $size = null;
                                if ($orderProduct->isFullPrintedType()) {
                                    $size = correctOptionValue(optional($options)->size);
                                }
                                [$files,] = FulfillmentService::getOrderProductFiles($orderProduct->id, $order->id, $orderProduct->product_id, $size, $color, false, false, $orderProduct);
                            }
                            graylogInfo('Start detect design type - Order ID: ' . $order->id, [
                                'category' => 'detect_design_type',
                                'order_id' => $order->id,
                                'supplier_id' => $orderProduct->supplier_id,
                                'file_ids' => $files->pluck('id')->toArray(),
                            ]);
                            if ($files->isNotEmpty()) {
                                foreach ($files as $file) {
                                    if (!$file->file_url) {
                                        continue;
                                    }
                                    $designType = ClassifyDesignService::instance()->handle(imgUrl($file->file_url, 'full_hd'), $order->id);
                                    if ($designType !== '' && !in_array($designType, $supplierSupportType, true)) {
                                        logToDiscord('Assign supplier manual for order product id ' . $orderProduct->id . ' failed. Design type: ' . $designType . ' not support by supplier id ' . $selectedSupplierId . ', Order id: ' . $order->id, DiscordChannel::FULFILL_ORDER);
                                        continue 2;
                                    }
                                }
                            }
                        }
                    }
                    $supplierName = self::assignSupplierByLocation(
                        $orderProduct,
                        $orderLocation,
                        $selectedSupplierId,
                        true,
                        $forceAssign
                    );

                    if (empty($supplierName)) {
                        continue;
                    }
                    if ($orderProduct->supplier_id && !empty($orderProduct->id) && $orderProduct->wasReAssignSupplier()) {
                        OrderAssignSupplierHistory::createFromOrderProduct($orderProduct);
                    }
                    // Nếu đơn hàng được đánh dấu là fulfill manually và sản phẩm chưa có supplier_id
                    // nhưng sau đó assign đc cho supplier khác thì đánh dấu sản phẩm là processing để import được tracking cho sản phẩm đó
                    if (isset($ordersChangedProcessing[$orderProduct->order_id]) && $no_supplier_id && (int) $orderProduct->supplier_id > 0 && $orderProduct->wasReAssignSupplier()) {
                        $orderProduct->fulfill_status = OrderProductFulfillStatus::PROCESSING;
                    }
                    // Nếu đơn đang bị đánh dấu là no ship nhưng đã được K thanh toán thì sau khi assign sup xong sẽ đánh dấu là unfulfilled
                    if ($order->status === OrderStatus::PROCESSING && $order->fulfill_status === OrderFulfillStatus::NO_SHIP && $orderProduct->fulfill_status === OrderProductFulfillStatus::NO_SHIP && $orderProduct->wasReAssignSupplier()) {
                        $orderProduct->fulfill_status = OrderProductFulfillStatus::UNFULFILLED;
                        $isChangedNoShip = true;
                    }
                    // Nếu bất cứ sản phẩm nào trong đơn hàng đã được đánh dấu là cross shipping thì cũng đánh dấu
                    // đơn hàng là cross shipping. Việc này để đội vận hành có thể lọc ra để xử lí thủ công
                    if ($orderProduct->cross_shipping) {
                        $order->markCrossShipping(true);
                    }

                    $detail .= $orderProduct->id . '-' . $orderProduct->product_name . ':' . $supplierName . '|';
                    $orderProduct->save();

                    if ($orderProduct->supplier_id) {
                        $ordersProductIdsAssigned[] = $orderProduct->id;
                    }
                }
                if (!empty($detail)) {
                    OrderHistory::insertLog(
                        $order,
                        OrderHistoryActionEnum::ASSIGNED_SUPPLIER,
                        $detail,
                    );
                }
            } catch (Throwable $e) {
                $errorOrderNumbers[] = $order->order_number;
                logToDiscord(
                    'Assign supplier for order_number: ' . $order->order_number . ' failed' . $e->getMessage(),
                    DiscordChannel::FULFILL_ORDER,
                    true
                );
            }
            if ($isChangedNoShip) {
                $order->fulfill_status = OrderFulfillStatus::UNFULFILLED;
            }
            // Tính toán lại giá vận chuyển và lợi nhuận của sup cho đơn hàng
            if ($order->paid_at) {
                OrderCostStatisticsJob::dispatchAfterResponse($order->id);
            }

            // re-calculate order if order is pending
            if ($order->status === OrderStatus::PENDING || $order->status === OrderStatus::ON_HOLD) {
                $order = $order->refresh();
                $order->calculateOrder();
                $order->push();
            } else {
                $order->save();
            }
        }

        $errorProductIDs = array_diff($ordersProductIds, $ordersProductIdsAssigned);
        $errors = array_filter([
            $errorOrderNumbers ? 'Assign Supplier for order_number: ' . implode(', ', $errorOrderNumbers) . ' failed' : null,
            $errorProductIDs ? 'Assign Supplier for order_product_id: ' . implode(', ', $errorProductIDs) . ' failed' : null,
        ]);
        if (!empty($ordersChangedProcessing)) {
            $orders = Order::query()->whereIn('id', array_values($ordersChangedProcessing))->get();
            foreach ($orders as $order) {
                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::FULFILL_UPDATED,
                    'Assign supplier for order manually, skip auto fulfill',
                );
            }
        }
        if ($errors) {
            return $this->errorResponse(implode("\n", $errors));
        }

        return  $this->successResponse();
    }

    /**
     * @param OrderProduct|RegionOrderProducts $op
     * @param SystemLocation $location
     * @param int|null $selectedSupplierId
     * @param bool $isManual
     * @param bool $forceAssign
     *
     * @return string
     */
    public static function assignSupplierByLocation(
        OrderProduct|RegionOrderProducts $op,
        SystemLocation $location,
        ?int $selectedSupplierId = null,
        bool $isManual = false,
        ?bool $forceAssign = false
    ): string {
        try {
            $variantKeyTM = getVariantKey($op['options']) . '-tm';
            if ($op->seller && $op->seller->custom_payment) {
                $existVariantKeyTM = ProductVariant::findAndCacheByTemplate($op->template_id)->contains('variant_key', $variantKeyTM);
                if ($existVariantKeyTM) {
                    return '';
                }
            }
            $fulfillProduct = self::findFulfillProduct($op, $location, $selectedSupplierId, $isManual, $forceAssign);
            $supplierId = (int)optional($fulfillProduct)->supplier_id;

            // set supplier
            $op->fulfill_sku           = data_get(optional($fulfillProduct)->variant, 'sku');
            $op->fulfill_product_id    = optional($fulfillProduct)->fulfill_product_id;
            $op->fulfill_cost          = (float)data_get(optional($fulfillProduct)->variant, 'base_cost', 0);
            $op->weight                = (float)data_get(optional($fulfillProduct)->variant, 'weight', 0);
            $op->supplier_id           = $supplierId;
            $op->supplier_name         = data_get(optional($fulfillProduct)->supplier, 'name');
            $op->assigned_supplier_at  = !empty($supplierId) ? now() : null;

            // Giá gốc sản phẩm của sup
            $op->fulfill_base_cost = data_get(optional($fulfillProduct)->variant, 'base_cost', 0);
        } catch (Throwable $e) {
            logToDiscord(
                implode("\n", ["Assign Supplier for order_product_id: {$op->id} failed", $e->getMessage(), $e->getTraceAsString()]),
                DiscordChannel::FULFILL_ORDER,
                true,
            );
        }

        return $op->supplier_name ?? '';
    }

    /**
     * @param OrderProduct|RegionOrderProducts $op
     * @param SystemLocation $location
     * @param null $selectedSupplierId
     * @param bool $isManual
     * @param bool $forceAssign
     * @param null $excludeSupplierId
     *
     * @return object|null
     * @throws Throwable
     */
    public static function findFulfillProduct(
        OrderProduct|RegionOrderProducts $op,
        SystemLocation $location,
        $selectedSupplierId = null,
        bool $isManual = false,
        bool $forceAssign = false,
        $excludeSupplierId = null
    ): ?object {
        // Khi buộc assign supplier thì chỉ lấy các sản phẩm của supplier đó
        $products = array_filter(
            self::getFullFillProducts($op, $location, $isManual),
            static fn ($p) => !$forceAssign || ($selectedSupplierId && (int) $p->supplier_id === (int) $selectedSupplierId)
        );
        if ($excludeSupplierId) {
            $products = array_filter($products, static fn ($p) => (int) $p->supplier_id !== (int) $excludeSupplierId);
        }
        if (!$products) {
            $op->setFulfillLogNotFoundVariant();
            return null;
        }

        $matched = self::findFulfillProductMatchCountryCode(
            $products,
            $location,
            $selectedSupplierId,
            $op->template_id
        );

        if (!$matched) {
            $op->setFulfillLogUnMatched($location);
        } else {
            $op->fulfill_exception_log = null;
            $op->determineCrossShipping($matched->supplier->location ?? '', $location->code);
        }

        return $matched;
    }

    /**
     * @param OrderProduct|RegionOrderProducts $orderProduct
     * @param SystemLocation $orderLocation
     * @param bool $isManual
     *
     * @return array
     * @throws Throwable
     */
    public static function getFullFillProducts(OrderProduct|RegionOrderProducts $orderProduct, SystemLocation $orderLocation, bool $isManual): array
    {
        $fulfillProducts = ProductFulfillMapping::findAndCacheByTemplateId($orderProduct->template_id, !$isManual);
        $fulfillProducts = self::removeProductOfSupplierInExcludeLocation($fulfillProducts, $orderLocation, $orderProduct->template_id);
        return self::matchFulfillProductsThroughVariantKey($fulfillProducts, $orderProduct, $isManual);
    }

    /**
     * @param array $fulfillProducts
     * @param OrderProduct|RegionOrderProducts $orderProduct
     * @param bool $isManual
     *
     * @return array
     * @throws Exception
     */
    private static function matchFulfillProductsThroughVariantKey(array $fulfillProducts, OrderProduct|RegionOrderProducts $orderProduct, bool $isManual): array
    {
        $variantKeys = [];
        $result = [];
        foreach ($fulfillProducts as $p) {
            if (!array_key_exists($p->supplier_id, $variantKeys)) {
                // Khi không tồn tại provider config thì sẽ lấy provider mặc định
                $provider = ObjectFulfill::provider($p->supplier_id) ?: ObjectFulfill::newInstance();
                // Với mỗi sup sẽ có các logic khác nhau cho dữ liệu OrderProduct
                // Vì thế để đảm bảo dữ liệu nhận được khi xử lý OrderProduct của từng sup
                // đảm bảo nguyên vẹn thì cần clone lại OrderProduct
                $variantKeys[$p->supplier_id] = self::makeVariantKey($provider, $p, clone $orderProduct, $isManual);
            }
            if ((($p->supplier && !$p->supplier->additional_print_space) || !str_contains($orderProduct->seller?->tags, 'sleeve printspace')) &&
                FulfillmentService::productIncludeAdditionalPrintSpace($orderProduct->product_id, PrintSpaceEnum::additionalPrintSpaces())) {
                continue;
            }

            $variant = self::firstMatchedVariant(
                $variantKeys[$p->supplier_id],
                $p->product_variants
            );

            if (is_null($variant)) {
                continue;
            }

            $p->variant ??= [];
            $p->variant['sku'] = $variant->sku;
            $p->variant['base_cost'] = $variant->base_cost;
            $p->variant['weight'] = $variant->weight;
            $p->variant['variant_key'] = $variant->variant_key;

            $result[] = $p;
        }
        return $result;
    }

    /**
     * Với mỗi sup sẽ có các xử lí khác nhau để tạo ra một variant key.
     *
     * @param ObjectFulfill $provider
     * @param $product
     * @param OrderProduct $op
     * @param bool $isManual
     * @return string
     * @throws \JsonException
     */
    private static function makeVariantKey(ObjectFulfill $provider, $product, OrderProduct|RegionOrderProducts $op, bool $isManual): string
    {
        $provider->correctOrderProduct($op);
        $orderOptions = with($op->options, static function($opt) use($op, $provider) {
            if (!empty($opt['color']) && $op->isFullPrintedType()) {
                $options = json_decode($op->template->options, true, 512, JSON_THROW_ON_ERROR);
                $opt['color'] = Arr::get($options, 'color.0');
            }
            $provider->unsetOptions($opt);
            return $opt;
        });
        if ($op->sku === 'AMPT') {
            $optionColor = Arr::get($orderOptions, 'color');
            if (!empty($optionColor) && $optionColor === 'pink bc') {
                $orderOptions['color'] = 'pink';
            }
        }

        return $provider->mappingVariantKey(
            $provider->getVariantKey($orderOptions, $op),
            $op,
            $product,
            $isManual,
        );
    }

    /**
     * @param array $fulfillProducts
     * @param SystemLocation $orderLocation
     * @param string|null $templateId
     * @return array
     */
    private static function removeProductOfSupplierInExcludeLocation(
        array $fulfillProducts,
        SystemLocation $orderLocation,
        ?string $templateId
    ): array
    {
        $excludes = FulfillMapping::filterByExcludeLocation(
            FulfillMappingEnum::PRODUCT_EXCLUDE_LOCATION,
            $orderLocation,
            $templateId,
        );
        if ($excludes->isEmpty()) {
            return $fulfillProducts;
        }
        return array_filter(
            $fulfillProducts,
            static fn($fulfillProduct) => !$excludes->contains('supplier_id', $fulfillProduct->supplier_id)
        );
    }

    /**
     * @param string $variantKey
     * @param array $variants
     * @return object|mixed|null
     */
    private static function firstMatchedVariant(string $variantKey, array $variants): ?object
    {
        return Arr::first(
            $variants,
            static fn($each) => $each->variant_key === $variantKey
        );
    }

    /**
     * @throws Exception
     */
    public static function findFulfillProductMatchCountryCode(
        array $fulfillProducts,
        object $orderLocation,
        ?int $selectedSupplierId,
        ?int $templateId
    ): ?object {
        // Loại bỏ các product không khả dụng
        $fulfillProducts = static::filterProductOutOfSupplier(
            $fulfillProducts, $templateId
        );

        return app()->make(MostFulfilProductForLocationMatcher::class)->match(
            $fulfillProducts,
            $orderLocation,
            $selectedSupplierId,
            $templateId
        );
    }

    /**
     * @param    array    $fulfillProducts
     * @param    int      $templateId
     *
     * @return array
     */
    private static function filterProductOutOfSupplier(array $fulfillProducts, int $templateId): array
    {
        $supplierIds = static::suppliers()->pluck('id');

        $filtered = array_filter(
            $fulfillProducts,
            static fn($p) => $supplierIds->contains($p->supplier_id)
        );

        if (count($filtered) !== count($fulfillProducts)) {
            static::clearCache($templateId);
        }

        return $filtered;
    }

    /**
     * @return Collection
     */
    public static function suppliers(): Collection
    {
        return Supplier::getAndCache();
    }

    /**
     * @param    int    $templateId
     *
     * @return void
     */
    public static function clearCache(int $templateId): void
    {
        syncClearCache(['tags' => [CacheKeys::getTemplateFulfillProduct($templateId)]], CacheKeys::CACHE_TYPE_ALTERNATIVE);
    }

    public function exportValidateOptions()
    {
        try {
            $fileName = 'validate_options_' . date('Y-m-d H:i:s') . '.csv';

            return Excel::download(new ValidateOptionsExport(), $fileName);
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    public function exportOrders(Request $request)
    {
        try {
            // file name format in front end
            $fileName = 'exportOrdersFulfill.csv';
            $isShippingLate = $request->get('is_shipping_late') ?? false;
            $isExpressShippingLate = $request->get('is_express_shipping_late') ?? false;
            $isNeedDesign = $request->get('is_need_design') ?? false;

            $orders = $this->getOrders($request, true, ['fulfilled_at', 'admin_note', 'support_status', 'assignee']);
            if ($request->boolean('change_status')) {
                $supplierId = $request->get('supplier_id');
                if ($orders->isEmpty()) {
                    throw new \RuntimeException('Orders is invalid');
                }

                $ordersUpdate = Order::query()
                    ->selectForHistory(['fulfilled_at', 'admin_note'])
                    ->whereIn('id', $orders->pluck('id'))
                    ->where('address_verified', '!=', OrderAddressVerifiedEnum::INVALID)
                    ->where(function ($query) {
                        $query->where(fn ($query) => $query->where('type', '<>', OrderTypeEnum::CUSTOM));
                        $query->orWhere(fn ($query) => $query->where('type', OrderTypeEnum::CUSTOM)
                            ->where('sen_fulfill_status', OrderSenFulfillStatus::YES)
                            ->where('processing_fee_paid', '>', 0)
                        );
                    })
                    ->get();
                if ($ordersUpdate->isEmpty()) {
                    throw new \RuntimeException('Orders is invalid');
                }
                $orders = $orders->filter(fn ($order) => in_array($order->id, $ordersUpdate->pluck('id')->toArray()));

                foreach ($ordersUpdate as $order) {
                    if (in_array($order->fulfill_status, [OrderProductFulfillStatus::UNFULFILLED, OrderProductFulfillStatus::REJECTED], true)) {
                        $order->fulfill_status = OrderProductFulfillStatus::PROCESSING;
                        $order->fulfilled_at = now();
                    }
                    $order->save();

                    OrderHistory::insertLog(
                        $order,
                        OrderHistoryActionEnum::EXPORT,
                    );
                }
                $order_products = OrderProduct::query()
                    ->whereIn('order_id', $ordersUpdate->pluck('id'))
                    ->when($supplierId > 0, function ($q) use ($supplierId) {
                        return $q->where('supplier_id', $supplierId);
                    })
                    ->get();

                foreach ($order_products as $order_product) {
                    if (in_array($order_product->fulfill_status, [OrderProductFulfillStatus::UNFULFILLED, OrderProductFulfillStatus::REJECTED], true)) {
                        $order_product->fulfilled_at = now();
                        $order_product->fulfill_status = OrderProductFulfillStatus::PROCESSING;
                        $order_product->total_reprint = is_null($order_product->total_reprint) ? 0 : ($order_product->total_reprint + 1);
                    }
                    $order_product->save();
                }
            }
            return Excel::download(new OrdersExport($orders, $isShippingLate, $isExpressShippingLate, $isNeedDesign), $fileName);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function supplierExportOrderProducts(Request $request)
    {
        $orderProductIds = $request->get('order_product_ids');
        $markAsProcessing = $request->boolean('mark_as_processing');
        $isExportUnfulfilledAndRejected = $request->boolean('is_export_unfulfilled_and_rejected');
        $user = currentUser();
        $supplierId = $user->getUserId();
        $isSupplier     = $user->isSupplier();
        $fullPath       = $request->boolean('full_path');
        $isSeller       = $user->isSeller();
        $fileName = 'export_products_' . Carbon::now()  . '.xlsx';
        try {
            $query = OrderProduct::query()->with(['seller', 'order', 'campaign'])->where('supplier_id', $supplierId);
            if ($isExportUnfulfilledAndRejected) {
                $query->whereIn('fulfill_status', [OrderProductFulfillStatus::UNFULFILLED]);
            } else {
                $query->whereIn('fulfill_status', Order::SUPPLIER_ORDER_STATUS);
            }
            if (empty($orderProductIds) || $isExportUnfulfilledAndRejected) {
                $query->whereHas('order', function ($q) {
                    $q->whereIn('status', OrderStatus::listingStatusForSupplier());
                    $q->whereDate('updated_at', '>=', Carbon::now()->subDays(30)->toDateString());
                    $q->whereIn('fulfill_status', Order::SUPPLIER_ORDER_STATUS);
                    $q->where('sen_fulfill_status', OrderSenFulfillStatus::YES);
                    $this->filterUnfulfilledSupplierOrder($q);
                    $q->where(function ($query) {
                        $query->whereDoesntHave('request_cancel')->orWhereHas('request_cancel', function ($query) {
                            $query->whereNotIn('status', [OrderCancelRequestStatus::PROCESSING, OrderCancelRequestStatus::CONFIRMED, OrderCancelRequestStatus::COMPLETED]);
                        });
                    });
                });
            } else {
                $query->whereIn('id', $orderProductIds);
            }
            $this->filterCancelledSupplierOrderProduct($query);
            $query->orderByRaw("
                CASE
                    WHEN fulfill_status = '" . OrderProductFulfillStatus::UNFULFILLED . "' THEN 1
                    WHEN fulfill_status = '" . OrderProductFulfillStatus::PROCESSING . "' THEN 2
                    WHEN fulfill_status = '" . OrderProductFulfillStatus::ON_DELIVERY . "' THEN 3
                    WHEN fulfill_status = '" . OrderProductFulfillStatus::FULFILLED . "' THEN 4
                    ELSE 5
                END, updated_at DESC
            ");
            $query->whereNotNull('supplier_id');
            $query->whereIn('tm_status', [TradeMarkStatusEnum::UNVERIFIED, TradeMarkStatusEnum::VERIFIED, TradeMarkStatusEnum::FLAGGED]);
            $orderProducts = $query->orderBy('order_id')->get();
            $getFiles = (new GetFilesAndMockupsOfOrderProducts($orderProducts, $fullPath, $isSupplier, $isSeller))->handle();
            $fulfillProductIds = $orderProducts->pluck('fulfill_product_id')->unique()->toArray();
            $variants = ProductVariant::query()
                ->whereIn('product_id', $fulfillProductIds)
                ->get();
            foreach ($orderProducts as $order_product) {
                $options = json_decode($order_product->options);
                $order_product = OrderService::getAndRemoveUnusedCustomOptions($order_product, $order_product->order->type);
                $custom_options = Str::isJson($order_product->custom_options) ? json_decode($order_product->custom_options, true, 512, JSON_THROW_ON_ERROR) : [];
                foreach ($custom_options as $kG => $group) {
                    $newGroup = [];
                    if (!is_array($group)) {
                        $custom_options[$kG] = str_replace(' ', '_', $kG) . ': ' . $group;
                        continue;
                    }
                    foreach ($group as $op) {
                        $newGroup[] = [
                            'type' => $op['type'],
                            'label' => $op['label'],
                            'value' => $op['value'],
                        ];
                    }
                    $custom_options[$kG] = $newGroup;
                }
                $order_product->custom_options = json_encode($custom_options, JSON_THROW_ON_ERROR);
                $filesData = data_get($getFiles, $order_product->id, []);
                $order_product->files = data_get($filesData, 'files');
                $order_product->mockups = data_get($filesData, 'mockups');
                if ($order_product->files->isEmpty() && !$order_product->isNoNeedDesignProductType()) {
                    throw new RuntimeException('Empty files design, Order product id: ' . $order_product->id . ', Order id: ' . $order_product->order_id);
                }
                if ($order_product->isFullPrintedType()) {
                    $counted = 0;
                    $order_product->files->each(static function ($each) use (&$counted) {
                        if ($each->type === FileTypeEnum::DESIGN && $each->print_space !== 'back') {
                            // $each->print_space !== 'back' => support the product full printed but have 2 designs
                            $counted++;
                        }
                    });
                    if ($counted > 1) {
                        throw new \RuntimeException('Too many designs. Order Product Id: ' . $order_product->id . ', Order id: ' . $order_product->order_id);
                    }
                }

                if (!empty($order_product->fulfill_product_id) && !empty($order_product->options)) {
                    $variant = $variants->where('product_id', $order_product->fulfill_product_id)->where('variant_key', getVariantKey($options))->first();
                    if (!empty($variant)) {
                        $order_product->sku = $variant->sku;
                    }
                }
            }
            if ($markAsProcessing) {
                $orderProductIds = $orderProducts->pluck('id')->toArray();
                $orderIds = $orderProducts->pluck('order_id')->unique()->toArray();
                $orders = Order::query()->whereIn('id', $orderIds)->get();
                $now = now();
                $filterOrderIds = [];
                $orderIdsToUpdate = [];
                foreach ($orderIds as $orderId) {
                    /** @var Order $order */
                    $order = $orders->firstWhere('id', $orderId);
                    if ($order) {
                        if ($order->fulfill_status === OrderFulfillStatus::UNFULFILLED) {
                            $filterOrderIds[] = $orderId;
                            $orderIdsToUpdate[] = $orderId;
                        } else if ($order->fulfill_status === OrderFulfillStatus::PROCESSING) {
                            $filterOrderIds[] = $orderId;
                        }
                    }
                }
                $orderIdsToUpdate = array_unique($orderIdsToUpdate);
                Order::query()->whereIn('id', $orderIdsToUpdate)->update([
                    'fulfill_status' => OrderFulfillStatus::PROCESSING,
                    'fulfilled_at' => $now,
                ]);
                $ordersInserted = $orders
                    ->whereIn('id', $orderIdsToUpdate);
                OrderHistory::insertLogMultipleOrders(
                    $ordersInserted,
                    OrderHistoryActionEnum::EXPORT,
                    'Supplier export order products'
                );
                if (!empty($filterOrderIds) && !empty($orderProductIds)) {
                    OrderProduct::query()
                        ->whereIn('id', $orderProductIds)
                        ->whereIn('order_id', $filterOrderIds)
                        ->where('fulfill_status', OrderProductFulfillStatus::UNFULFILLED)
                        ->where('supplier_id', $supplierId)
                        ->update([
                            'fulfill_status' => OrderProductFulfillStatus::PROCESSING,
                            'supplier_exported_at' => $now,
                            'fulfilled_at' => $now,
                            'total_reprint' => DB::raw('CASE WHEN total_reprint IS NULL THEN 0 ELSE total_reprint + 1 END'),
                        ]);
                }
            }
            return Excel::download(new ProductsExport($orderProducts, $markAsProcessing), $fileName);
        } catch (Throwable $e) {
            graylogError('error when export supplier order products', [
                'category' => 'export_supplier_order_products',
                'supplier_id' => $supplierId,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            logException($e, __FUNCTION__ . "\nException:" . $e->getMessage() . "\nSupplier Id:" . $supplierId);
            return '';
        }
    }

    private function getDataReport($request, $isPaginate)
    {
        $keyword          = $request->get('q');
        $arrFulfillStatus = checkAndConvertToArray($request->get('array_fulfill_status'));
        $supplierId       = $request->get('supplier_id');
        $dateRange        = $request->get('date_range');
        $startDate        = $request->get('start_date');
        $endDate          = $request->get('end_date');
        $shippingMethod   = $request->get('shipping_method');
        $orderSub = Order::query()
            ->select([
                'id',
                'shipping_method',
                'total_shipping_cost',
                'country',
            ])
            ->when($keyword, function ($q) use ($keyword) {
                return $q->where('id', (int)$keyword);
            })
            ->when($shippingMethod, function ($q) use ($shippingMethod) {
                return $q->where('shipping_method', $shippingMethod);
            });

        $data = OrderProduct::query()
            ->addSelect(
                [
                    'order.id',
                    'order.shipping_method',
                    'order.country',
                    'order.total_shipping_cost',
                    'order_product.product_name',
                    'order_product.options',
                    'order_product.fulfill_sku',
                    'order_product.price',
                    'order_product.quantity',
                    'order_product.total_amount',
                    'order_product.fulfill_status',
                    'order_product.fulfill_order_id',
                    'order_product.supplier_name',
                    'order_product.fulfilled_at',
                    'order_product.processing_day',
                    'order_product.delivered_at',
                    'order_product.received_at',
                ]
            )
            ->joinSub($orderSub, 'order', 'order.id', '=', 'order_product.order_id')
            ->filterFulfillProduct($arrFulfillStatus, $supplierId)
            ->filterDateRange($dateRange, $startDate, $endDate, null, null, true);

        if ($isPaginate) {
            $perPage = $request->get('per_page');

            return $data->paginate($perPage);
        }

        return $data->get();
    }

    public function report(Request $request): JsonResponse
    {
        try {
            $data = $this->getDataReport($request, true);

            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function exportReport(Request $request)
    {
        try {
            $fileName = 'exportReport.csv';

            $data = $this->getDataReport($request, false);

            return Excel::download(new Report($data), $fileName);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function log(Request $request): JsonResponse
    {
        try {
            $orderId = $request->get('order_id');
            if (empty($orderId)) {
                return $this->errorResponse('Order Id is required');
            }

            $data = ApiLog::query()
                ->select(
                    [
                        'id',
                        'reference_id',
                        'request_body',
                        'response_body',
                        'type',
                        'created_at',
                    ]
                )
                ->where('order_id', $orderId)
                ->where('reference_type', AccessExternalApiType::SUPPLIER)
                ->latest()
                ->get();

            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function uploadUpdateOrders(Request $request): JsonResponse
    {
        try {
            Excel::import(new UpdateOrderImport($request->supplier_id), $request->file('input_file_update_orders'));

            return $this->successResponse();
        } catch (Throwable $e) {
            logToDiscord(
                __FUNCTION__
                . "\nException:" . $e->getMessage()
                . "\nUser Id:" . currentUser()->getUserId()
                , DiscordChannel::FULFILL_ORDER
                , true
            );
            return $this->errorResponse($e->getMessage());
        }
    }

    public function importBillingAndExportValidate(Request $request)
    {
        try {
            $markBilled = $request->post('mark_billed') === 'true';
            $supplierId = $request->post('supplier_id');

            $importer = suppliers()->validateBillingImporterClass($supplierId);

            if (! $importer || ! class_exists($importer)) {
                throw new RuntimeException('Importer not found');
            }

            $importer = app()->makeWith($importer, [
                'supplierId' => $supplierId
            ]);

            /** @var \App\Imports\Supplier\BaseValidateBillingImporter $importer */
            Excel::import($importer, $request->file('input_file_billing'));

//            if ($markBilled) {
//                $importer->markBilled();
//            }

            return Excel::download(new ValidateBillingExport($importer->errors()), 'validate_billing.csv');
        } catch (Throwable $e) {
            logToDiscord(
                "\nException:" . $e->getMessage() . "\n" . $e->getTraceAsString().
                'error'
            );
            return $this->errorResponse();
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateValid(Request $request): JsonResponse
    {
        $currentUser = currentUser();
        try {
            $orderId = $request->post('order_id');
            $order = Order::query()->find($orderId);
            if (!$order) {
                return $this->errorResponse('Order not found');
            }
            if ($order->status === OrderStatus::DRAFT) {
                return $this->errorResponse('Order is draft, cannot set valid');
            }
            $orderProducts = OrderProduct::query()
                ->where('order_id', $orderId)
                ->get();

            $approvedProductIds = deep_copy($orderProducts)
                ->where('fulfill_status', OrderProductFulfillStatus::REVIEWING)
                ->pluck('product_id');
            $designingOrderProducts = deep_copy($orderProducts)
                ->where('fulfill_status', OrderProductFulfillStatus::DESIGNING);

            Order::query()
                ->where('id', $orderId)
                ->whereIn('fulfill_status', [
                    OrderProductFulfillStatus::INVALID,
                    OrderProductFulfillStatus::REVIEWING,
                    OrderProductFulfillStatus::DESIGNING,
                ])
                ->update([
                    'fulfill_status' => $designingOrderProducts->isEmpty() ? OrderFulfillStatus::UNFULFILLED : OrderProductFulfillStatus::DESIGNING,
                    'approved_at' => now()
                ]);

            OrderProduct::query()
                ->where('order_id', $orderId)
                ->whereIn('fulfill_status', [
                    OrderProductFulfillStatus::INVALID,
                    OrderProductFulfillStatus::REVIEWING,
                ])
                ->update(['fulfill_status' => OrderProductFulfillStatus::UNFULFILLED]);

            if ($approvedProductIds->isNotEmpty()) {
                OrderProduct::query()
                    ->whereIn('product_id', $approvedProductIds)
                    ->where('fulfill_status', OrderProductFulfillStatus::REVIEWING)
                    ->where('personalized', 0)
                    ->update(['fulfill_status' => OrderProductFulfillStatus::UNFULFILLED]);

                Order::query()
                    ->where('fulfill_status', OrderFulfillStatus::REVIEWING)
                    ->whereRaw("(select count(*) from order_product where order_product.order_id = `order`.id and fulfill_status='reviewing')=0")
                    ->update([
                        'fulfill_status' => OrderFulfillStatus::UNFULFILLED,
                        'approved_at' => now()
                    ]);
            }
            $order = $order->refresh();
            if ($order) {
                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::FULFILL_UPDATED,
                    $currentUser->getName() . ' updated order is valid'
                );
                OrderService::processCustomOrder($order);
            }
            return $this->successResponse();
        } catch (Throwable $e) {
            logException($e);
            return $this->errorResponse();
        }
    }

    public function getLogs(Request $request): JsonResponse
    {
        try {
            $orderId        = (string)$request->get('order_id', '');
            $fulfillOrderId = (string)$request->get('fulfill_order_id', '');
            $supplierId     = (string)$request->get('supplier_id');

            $query = ApiLog::query()
                ->when(!empty($supplierId), function ($q) use ($supplierId) {
                    $q->where('reference_id', $supplierId);
                })
                ->latest();

            $sent_logs = $query->clone()
                ->select(
                    [
                        'name',
                        'url',
                        'fulfill_order_id',
                        'request_body',
                        'response_body',
                        'created_at',
                    ]
                )
                ->where('order_id', $orderId)
                ->where('type', ApiLogTypeEnum::API)
                ->whereIn('name', [
                    ObjectFulfill::METHOD_CREATE_ORDER,
                    ObjectFulfill::METHOD_CANCEL_ORDER,
                ])
                ->get();

            $latest_crawl_log = $query->clone()
                ->select(
                    [
                        'response_body',
                        'created_at',
                    ]
                )
                ->where('fulfill_order_id', $fulfillOrderId)
                ->where('type', ApiLogTypeEnum::API)
                ->whereIn('name', [
                    ObjectFulfill::METHOD_CRAWL_ORDER,
                    ObjectFulfill::METHOD_CRAWL_ORDER_DETAILS,
                    ObjectFulfill::METHOD_CRAWL_ORDER_TRACKING,
                ])
                ->first();

            $webhook_logs = $query->clone()
                ->select(
                    [
                        'request_body',
                        'created_at',
                    ]
                )
                ->where('fulfill_order_id', $fulfillOrderId)
                ->where('type', ApiLogTypeEnum::WEBHOOK)
                ->get();

            return $this->successResponse(compact('sent_logs', 'latest_crawl_log', 'webhook_logs'));
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /** @noinspection PhpUnusedLocalVariableInspection
     */
    public function getAnalytics(): JsonResponse
    {
        try {
            $assigned = Order::query()
                ->whereHas('order_products', function ($q) {
                    $q->whereNotNull('supplier_id');
                    $q->filterFulfillProduct([
                        OrderProductFulfillStatus::REJECTED,
                    ]);
                })
                ->filterFulfill()
                ->count();

            $unassigned = Order::query()
                ->whereHas('order_products', function ($q) {
                    $q->whereNull('supplier_id');
                })
                ->filterFulfill()
                ->count();

            $on_hold = Order::query()->where(function ($query) {
                $query->where('sen_fulfill_status', OrderSenFulfillStatus::YES);
                $query->where('order.status', OrderStatus::PROCESSING);
                $query->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('order.fulfill_status', OrderFulfillStatus::ON_HOLD)->orWhereHas('order_products', function ($q) {
                            return $q->where('fulfill_status', OrderProductFulfillStatus::ON_HOLD);
                        });
                    });
                    $query->orWhere(function ($query) {
                        $query->where('order.address_verified', OrderAddressVerifiedEnum::INVALID);
                        $query->whereIn('order.type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM]);
                        $query->whereHas('order_products', function ($q) {
                            return $q->whereNotNull(['product_id', 'campaign_id']);
                        });
                    });
                });
            })->count();

            $need_review = Order::query()
                ->whereHas('order_products', function ($q) {
                    $q->whereNotNull('supplier_id')
                        ->whereIn('fulfill_status', [
                            OrderProductFulfillStatus::INVALID,
                            OrderProductFulfillStatus::REVIEWING,
                        ]);
                })
                ->filterFulfill()
                ->count();

            $in_production = (object)[];

            $in_production->late = cache()->remember(
                'order_processing_late',
                CacheTime::CACHE_1H,
                function () {
                    return Order::query()
                        ->filterFulfill([
                            OrderStatus::PROCESSING,
                        ], [
                            OrderFulfillStatus::PROCESSING,
                        ])
                        ->where('fulfilled_at', '<', now()->subDays(5))
                        ->whereNull('admin_note')
                        ->whereHas('order_products', function ($q) {
                            $q
                                ->filterFulfill([
                                    OrderProductFulfillStatus::PROCESSING,
                                    OrderProductFulfillStatus::EXCEPTION,
                                    OrderProductFulfillStatus::PENDING,
                                ])
                                ->whereNotNull('supplier_id');
                        })
                        ->count();
                }
            );

            $in_production->exception = cache()->remember(
                'order_processing_exception',
                CacheTime::CACHE_5m,
                function () {
                    return Order::query()
                    ->filterFulfill([
                        OrderStatus::PROCESSING,
                    ], [
                        OrderFulfillStatus::PROCESSING,
                    ])
                    ->whereHas('order_products', function ($q) {
                        $q->filterFulfill([
                            OrderProductFulfillStatus::EXCEPTION,
                        ]);
                    })
                    ->count();
                }
            );

            $ship_late = cache()->remember(
                'order_ship_late',
                CacheTime::CACHE_1H,
                function () {
                    return Order::query()
                        ->filterFulfill([
                            OrderStatus::PROCESSING,
                            OrderStatus::COMPLETED,
                        ])
                        ->whereHas('order_products', function ($q) {
                            $q->shippingLate();
                        })
                        ->count();
                }
            );

            $need_support = Order::query()
                ->where('support_status', '>', OrderSupportStatusEnum::NORMAL)
                ->filterSupport(OrderSupportStatusEnum::NEED_SUPPORT, OrderAssigneeEnum::ALL)
                ->count();

            $fraud_flagged = Order::query()
                ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                ->where('fraud_status', OrderFraudStatus::FLAGGED)
                ->count();

            $review_trademark = Order::query()
                ->where('tm_status', TradeMarkStatusEnum::FLAGGED)
                ->count();

            $violated_orders = Order::query()
                ->where('tm_status', TradeMarkStatusEnum::VIOLATED)
                ->where('status', OrderStatus::PROCESSING)
                ->count();

            $express_ship_late = Order::query()
                ->whereIn('status', [OrderStatus::PROCESSING, OrderStatus::COMPLETED])
                ->where('estimate_delivery_date', '<', now()->clone()->addDay()->toDateTimeString())
                ->where('shipping_method', ShippingMethodEnum::EXPRESS)
                ->whereHas('order_products', function ($q) {
                    $q->whereNotNull('supplier_id');
                })
                ->count();

            return $this->successResponse(get_defined_vars());
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    public function exportProductVariant(Request $request)
    {
        try {
            $supplierId = $request->get('supplier_id');
            $fileName   = 'product_variant_' . date('Y-m-d H:i:s') . '.xlsx';

            return Excel::download(new ProductVariantExport($supplierId), $fileName);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function importDesignMapping(Request $request): JsonResponse
    {
        try {
            Excel::import(new ProductDesignMappingImport($request->get('supplier_id')), $request->file('input_file'));

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateStatus(Request $request): JsonResponse
    {
        try {
            $supplierId = $request->get('supplier_id');
            $productId  = $request->get('product_id');
            $status     = $request->get('status');

            $product         = FulfillProduct::query()
                ->where('supplier_id', $supplierId)
                ->findOrFail($productId);
            $product->status = $status;
            $product->save();

            $templateIds = ProductFulfillMapping::query()
                ->where('fulfill_product_id', $productId)
                ->where('supplier_id', $supplierId)
                ->pluck('product_id');

            $cacheKeys = [];
            foreach ($templateIds as $templateId) {
                $cacheKeys['tags'][] = CacheKeys::getTemplateFulfillProduct($templateId);
            }

            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function importTracking(int $orderProductId, Request $request): JsonResponse
    {
        $trackingCode = $request->input('tracking_code');
        $trackingUrl  = $request->input('tracking_url');

        if (empty($trackingCode) && empty($trackingUrl)) {
            return $this->errorResponse('Missing tracking code or tracking url');
        }

        $product = OrderProduct::query()
            ->whereKey($orderProductId)
            ->whereNotIn('fulfill_status', [OrderProductFulfillStatus::UNFULFILLED, OrderProductFulfillStatus::REJECTED, OrderProductFulfillStatus::FULFILLED])
            ->where('supplier_id', currentUser()->getUserId())
            ->with('order')
            ->first();

        if (empty($product)) {
            return $this->errorResponse('Order product not found');
        }

        try {
            $product->tracking_code = $trackingCode;
            $product->tracking_url  = $trackingUrl;
            $product->fulfill_status = $product->fulfill_status === OrderProductFulfillStatus::ON_DELIVERY ? OrderProductFulfillStatus::ON_DELIVERY : OrderProductFulfillStatus::PROCESSING;
            $product->save();
            $countUnfulfilled = OrderProduct::query()
                ->where('order_id', $product->order_id)
                ->whereNotIn('fulfill_status', [
                    OrderProductFulfillStatus::FULFILLED,
                    OrderProductFulfillStatus::CANCELLED,
                    OrderProductFulfillStatus::ON_DELIVERY,
                ])
                ->count();
            $updated = 0;
            if ($countUnfulfilled === 0) {
                $updated = Order::query()
                    ->whereKey($product->order_id)
                    ->update([
                        'fulfill_status' => OrderFulfillStatus::ON_DELIVERY,
                        'status' => OrderStatus::COMPLETED,
                    ]);
            }
            if ($updated > 0) {
                OrderHistory::insertLog(
                    $product->order,
                    OrderHistoryActionEnum::UPDATED,
                    currentUser()->getInfo()->name . ' update product ' . $product->id . ': Tracking code ' . $trackingCode,
                );
            }
            OrderService::updateOrderTrackingStatus($product->order_id, $trackingCode);
        } catch (\Throwable $e) {
            $this->errorResponse('Server error: Please try again!');
        }

        return $this->successResponse();
    }

    /**
     * @param OrderProduct $op
     * @param SystemLocation $location
     * @param ?array $excludeSupplierIds
     *
     * @return string
     */
    public static function reAssignSupplierByLocationWithExcludeSupplier(OrderProduct $op, SystemLocation $location, ?array $excludeSupplierIds): string {
        try {
            $fulfillProduct = self::findFulfillProductExcludeSuppliers($op, $location, null, false, false, $excludeSupplierIds);
            $supplierId = (int) optional($fulfillProduct)->supplier_id;
            $supplierName = data_get(optional($fulfillProduct)->supplier, 'name');
            // set supplier
            $op->fulfill_sku           = data_get(optional($fulfillProduct)->variant, 'sku');
            $op->fulfill_product_id    = optional($fulfillProduct)->fulfill_product_id;
            $op->fulfill_cost          = data_get(optional($fulfillProduct)->variant, 'base_cost', 0);
            $op->weight                = data_get(optional($fulfillProduct)->variant, 'weight', 0);
            $op->supplier_id           = $supplierId;
            $op->supplier_name         = $supplierName;
            $op->assigned_supplier_at  = !empty($supplierId) ? now() : null;
            $op->fulfill_base_cost = data_get(optional($fulfillProduct)->variant, 'base_cost', 0);
        } catch (Throwable $e) {
            logToDiscord(
                implode("\n", ["Assign Supplier for order_product_id: {$op->id} failed", $e->getMessage(), $e->getTraceAsString()]),
                DiscordChannel::FULFILL_ORDER,
                true,
            );
        }
        return $op->supplier_name ?? '';
    }

    /**
     * @param OrderProduct|RegionOrderProducts $op
     * @param SystemLocation $location
     * @param null $selectedSupplierId
     * @param bool $isManual
     * @param bool $forceAssign
     * @param    ?array $excludeSupplierIds
     *
     * @return object|null
     * @throws Throwable
     */
    public static function findFulfillProductExcludeSuppliers(
        OrderProduct|RegionOrderProducts $op,
        SystemLocation $location,
        $selectedSupplierId = null,
        bool $isManual = false,
        bool $forceAssign = false,
        ?array $excludeSupplierIds = []
    ): ?object {
        // Khi buộc assign supplier thì chỉ lấy các sản phẩm của supplier đó
        $products = array_filter(
            self::getFullFillProducts($op, $location, $isManual),
            static fn ($p) => !$forceAssign || ($selectedSupplierId && (int) $p->supplier_id === (int) $selectedSupplierId)
        );
        if (!empty($excludeSupplierIds)) {
            $products = array_filter($products, static fn ($p) => !in_array((int) $p->supplier_id, $excludeSupplierIds, true));
        }
        if (!$products) {
            $op->setFulfillLogNotFoundVariant();
            return null;
        }

        $matched = self::findFulfillProductMatchCountryCode(
            $products,
            $location,
            $selectedSupplierId,
            $op->template_id
        );

        if (!$matched) {
            $op->setFulfillLogUnMatched($location);
        } else {
            $op->fulfill_exception_log = null;
            $op->determineCrossShipping($matched->supplier->location ?? '', $location->code);
        }

        return $matched;
    }

    public function supplierExportOrderProductsV2(Request $request)
    {
        $orderProductIds = $request->get('order_product_ids');
        $markAsProcessing = $request->boolean('mark_as_processing');
        $isExportUnfulfilledAndRejected = $request->boolean('is_export_unfulfilled_and_rejected');
        $supplierId = $request->get('supplier_id', 0);
        $isSupplier     = true;
        $isSeller       = false;
        $fullPath       = $request->boolean('full_path');
        try {
            $query = OrderProduct::query()->with(['seller', 'order', 'campaign'])->where('supplier_id', $supplierId);
            if ($isExportUnfulfilledAndRejected) {
                $query->whereIn('fulfill_status', [OrderProductFulfillStatus::UNFULFILLED]);
            } else {
                $query->whereIn('fulfill_status', Order::SUPPLIER_ORDER_STATUS);
            }
            if (empty($orderProductIds) || $isExportUnfulfilledAndRejected) {
                $query->whereHas('order', function ($q) {
                    $q->whereIn('status', OrderStatus::listingStatusForSupplier());
                    $q->whereDate('updated_at', '>=', Carbon::now()->subDays(30)->toDateString());
                    $q->whereIn('fulfill_status', Order::SUPPLIER_ORDER_STATUS);
                    $q->where('sen_fulfill_status', OrderSenFulfillStatus::YES);
                    $this->filterUnfulfilledSupplierOrder($q);
                    $q->where(function ($query) {
                        $query->whereDoesntHave('request_cancel')->orWhereHas('request_cancel', function ($query) {
                            $query->whereNotIn('status', [OrderCancelRequestStatus::PROCESSING, OrderCancelRequestStatus::CONFIRMED, OrderCancelRequestStatus::COMPLETED]);
                        });
                    });
                });
            } else {
                $query->whereIn('id', $orderProductIds);
            }
            $this->filterCancelledSupplierOrderProduct($query);
            $query->orderByRaw("
                CASE
                    WHEN fulfill_status = '" . OrderProductFulfillStatus::UNFULFILLED . "' THEN 1
                    WHEN fulfill_status = '" . OrderProductFulfillStatus::PROCESSING . "' THEN 2
                    WHEN fulfill_status = '" . OrderProductFulfillStatus::ON_DELIVERY . "' THEN 3
                    WHEN fulfill_status = '" . OrderProductFulfillStatus::FULFILLED . "' THEN 4
                    ELSE 5
                END, updated_at DESC
            ");
            $query->whereNotNull('supplier_id');
            $query->whereIn('tm_status', [TradeMarkStatusEnum::UNVERIFIED, TradeMarkStatusEnum::VERIFIED, TradeMarkStatusEnum::FLAGGED]);
            $orderProducts = $query->get();
            $getFiles = (new GetFilesAndMockupsOfOrderProducts($orderProducts, $fullPath, $isSupplier, $isSeller))->handle();
            $fulfillProductIds = $orderProducts->pluck('fulfill_product_id')->unique()->toArray();
            $variants = ProductVariant::query()
                ->whereIn('product_id', $fulfillProductIds)
                ->get();

            foreach ($orderProducts as $order_product) {
                $options = json_decode($order_product->options);
                $color = correctOptionValue(optional($options)->color);
                $size = correctOptionValue(optional($options)->size);
                $order_product = OrderService::getAndRemoveUnusedCustomOptions($order_product, $order_product->order->type);
                $custom_options = Str::isJson($order_product->custom_options) ? json_decode($order_product->custom_options, true, 512, JSON_THROW_ON_ERROR) : [];
                foreach ($custom_options as $kG => $group) {
                    $newGroup = [];
                    if (!is_array($group)) {
                        $custom_options[$kG] = str_replace(' ', '_', $kG) . ': ' . $group;
                        continue;
                    }
                    foreach ($group as $op) {
                        $newGroup[] = [
                            'type' => $op['type'],
                            'label' => $op['label'],
                            'value' => $op['value'],
                        ];
                    }
                    $custom_options[$kG] = $newGroup;
                }
                $order_product->custom_options = json_encode($custom_options, JSON_THROW_ON_ERROR);
                $filesData = data_get($getFiles, $order_product->id, []);
                $order_product->files = data_get($filesData, 'files');
                $order_product->mockups = data_get($filesData, 'mockups');
                if ($order_product->files->isEmpty() && !$order_product->isNoNeedDesignProductType()) {
                    throw new RuntimeException('Empty files design, Order product id: ' . $order_product->id . ', Order id: ' . $order_product->order_id);
                }
                if ($order_product->isFullPrintedType()) {
                    $counted = 0;
                    $order_product->files->each(static function ($each) use (&$counted) {
                        if ($each->type === FileTypeEnum::DESIGN && $each->print_space !== 'back') {
                            // $each->print_space !== 'back' => support the product full printed but have 2 designs
                            $counted++;
                        }
                    });
                    if ($counted > 1) {
                        throw new \RuntimeException('Too many designs. Order Product Id: ' . $order_product->id . ', Order id: ' . $order_product->order_id);
                    }
                }

                if (!empty($order_product->fulfill_product_id) && !empty($order_product->options)) {
                    $variant = $variants->where('product_id', $order_product->fulfill_product_id)->where('variant_key', getVariantKey($options))->first();
                    if (!empty($variant)) {
                        $order_product->sku = $variant->sku;
                    }
                }
            }
            if ($markAsProcessing) {
                $orderProductIds = $orderProducts->pluck('id')->toArray();
                $orderIds = $orderProducts->pluck('order_id')->unique()->toArray();
                $orders = Order::query()->whereIn('id', $orderIds)->get();
                $now = now();
                $filterOrderIds = [];
                foreach ($orderIds as $orderId) {
                    /** @var Order $order */
                    $order = $orders->firstWhere('id', $orderId);
                    if ($order) {
                        if ($order->fulfill_status === OrderFulfillStatus::UNFULFILLED) {
                            $filterOrderIds[] = $orderId;
                            $order->fulfill_status = OrderFulfillStatus::PROCESSING;
                            $order->fulfilled_at = $now;
                            $order->save();
                            OrderHistory::insertLog(
                                $order,
                                OrderHistoryActionEnum::EXPORT,
                                'Supplier export order products'
                            );
                        } else if ($order->fulfill_status === OrderFulfillStatus::PROCESSING) {
                            $filterOrderIds[] = $orderId;
                        }
                    }
                }
                if (!empty($filterOrderIds) && !empty($orderProductIds)) {
                    OrderProduct::query()
                        ->whereIn('id', $orderProductIds)
                        ->whereIn('order_id', $filterOrderIds)
                        ->where('fulfill_status', OrderProductFulfillStatus::UNFULFILLED)
                        ->where('supplier_id', $supplierId)
                        ->update([
                            'fulfill_status' => OrderProductFulfillStatus::PROCESSING,
                            'supplier_exported_at' => $now,
                            'fulfilled_at' => $now,
                            'total_reprint' => DB::raw('CASE WHEN total_reprint IS NULL THEN 0 ELSE total_reprint + 1 END'),
                        ]);
                }
            }
            return $this->successResponse($orderProducts);
        } catch (Throwable $e) {
            logException($e, __FUNCTION__ . "\nException:" . $e->getMessage() . "\nSupplier Id:" . $supplierId);
            return $this->errorResponse($e->getMessage());
        }
    }

}

<?php

namespace App\Http\Controllers\Admin;

use App\Enums\EnvironmentEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Elastic;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductPromotion;
use App\Models\ProductVariant;
use App\Models\ShippingRule;
use App\Services\GoogleSheetApiService;
use App\Traits\ApiResponse;
use Google\Exception;
use Google\Service\Sheets\AddSheetRequest;
use Google\Service\Sheets\AutoResizeDimensionsRequest;
use Google\Service\Sheets\BatchUpdateSpreadsheetResponse as BatchUpdateSpreadsheetResponseAlias;
use Google\Service\Sheets\DimensionRange;
use Google\Service\Sheets\MergeCellsRequest;
use Google\Service\Sheets\RepeatCellRequest;
use Google\Service\Sheets\UpdateSheetPropertiesRequest;
use Google_Client;
use Google_Service_Drive;
use Google_Service_Drive_Permission;
use Google_Service_Sheets;
use Google_Service_Sheets_BatchUpdateSpreadsheetRequest;
use Google_Service_Sheets_BatchUpdateValuesRequest;
use Google_Service_Sheets_Request;
use Google_Service_Sheets_Spreadsheet;
use Google_Service_Sheets_ValueRange;
use Illuminate\Http\JsonResponse;

class GoogleSheetApiController extends Controller
{
    use ApiResponse;
    private Google_Client $googleClient;
    private Google_Service_Sheets $googleSheetServices;
    private string $productCatalogSpreadSheetId;
    private string $priceSpreadSheetId;

    private string $oosProductSpreadSheetId;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        $this->googleClient = $this->getClient();
        $this->googleSheetServices = new Google_Service_Sheets($this->googleClient);
        $this->googleDriveService = new Google_Service_Drive($this->googleClient);

        $prodProductCatalogSpreadsheetId = '1v9jfqXnAsAr-ymRGv6yj0lvWccxXKw_OxcOEUrXPv4U';
        $devProductCatalogSpreadsheetId = '17qp3LESzkffcqbkA-O4YwjCv408i5wmhnoQy5qSxVfQ';
        $prodPriceCatalogSpreadsheetId = '1s63SoEjtWr0rA0gSShPZLYJWY0KuBFDyflzO_jPKeYY';
        $devPriceCatalogSpreadsheetId = '1KtF-fWqTGcdzQKVKbk9S3YAjj6wl0h31nYyBKwQOa80';
        $prodOosProductSpreadsheetId = '1-R8VwweFYlB6vJQzMiFw9WxEYXpfJzyDd9M9veGZKs4';
        $devOosProductSpreadsheetId = '1hCUg5QS3xovCzIAUH004uBlG5Xt4kMQ69dSQGxmxMaw';

        if(app()->isProduction()) {
            $this->productCatalogSpreadSheetId = $prodProductCatalogSpreadsheetId;
            $this->priceSpreadSheetId = $prodPriceCatalogSpreadsheetId;
            $this->oosProductSpreadSheetId = $prodOosProductSpreadsheetId;
        } else {
            $this->productCatalogSpreadSheetId = $devProductCatalogSpreadsheetId;
            $this->priceSpreadSheetId = $devPriceCatalogSpreadsheetId;
            $this->oosProductSpreadSheetId = $devOosProductSpreadsheetId;
        }
    }

    public function getListCatalogs(): JsonResponse
    {
        return response()->json([
            'product_catalog' => $this->productCatalogSpreadSheetId,
            'base_cost_catalog' => $this->priceSpreadSheetId,
            'oos_product' => $this->oosProductSpreadSheetId,
        ]);
    }

    /**
     * @throws Exception
     */
    private function getClient(): Google_Client
    {
        $client = new Google_Client();

        $prodServiceAccount = [
          "type"=> "service_account",
          "project_id"=> "senprints-admin-dashboard",
          "private_key_id"=> "cd94919c76d0c3804eeeb41980bbe69aa49760ef",
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          "client_email"=> "<EMAIL>",
          "client_id"=> "117988211355893867432",
          "auth_uri"=> "https://accounts.google.com/o/oauth2/auth",
          "token_uri"=> "https://oauth2.googleapis.com/token",
          "auth_provider_x509_cert_url"=> "https://www.googleapis.com/oauth2/v1/certs",
          "client_x509_cert_url"=> "https://www.googleapis.com/robot/v1/metadata/x509/senprint-production%40senprints-admin-dashboard.iam.gserviceaccount.com"
        ];

        $devServiceAccount = [
          "type" => "service_account",
          "project_id" => "senprints-admin-dashboard",
          "private_key_id" => "3d2d0b240c294a92cd438a5cb69c2207885ffedd",
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          "client_email" => "<EMAIL>",
          "client_id" => "103112513019279776110",
          "auth_uri" => "https =>//accounts.google.com/o/oauth2/auth",
          "token_uri" => "https =>//oauth2.googleapis.com/token",
          "auth_provider_x509_cert_url" => "https://www.googleapis.com/oauth2/v1/certs",
          "client_x509_cert_url" => "https://www.googleapis.com/robot/v1/metadata/x509/senprint-catalog-exporter%40senprints-admin-dashboard.iam.gserviceaccount.com"
        ];

        $jsonKey = [];
        if(app()->environment(EnvironmentEnum::PRODUCTION)) {
            $jsonKey =  $prodServiceAccount;
        } else {
            $jsonKey = $devServiceAccount;
        }

        $client->setAuthConfig($jsonKey);
        $client->addScope([
            Google_Service_Sheets::SPREADSHEETS,
            Google_Service_Drive::DRIVE
        ]);
        return $client;
    }

    private function updateProductCatalog() {
        $categories = Category::query()
            ->select('id', 'name', 'full_name', 'slug')
            ->where('show_dashboard', 1) // only load category to show on dashboard
            ->get();

        if ($categories->count() === 0) {
            return $this->errorResponse();
        }

        $requestUpdateSheets = [];

        $this->clearAllSheets($this->productCatalogSpreadSheetId);
        $headings = [
            'Product SKU',
            'Product name',
            'Options',
            'Print space',
            'Actual print size',
            'Design template',
        ];

        $topTemplateIds = topTemplateIds();
        foreach ($categories as $cIdx => &$category) {
            $productsData = [$headings];
            $templatesCount = count($topTemplateIds);
            $product_ids = ProductCategory::query()->where('category_id', $category->id)->get()->pluck('product_id')->toArray();
            $category->products = collect();
            if (!empty($product_ids)) {
                $category->products = Product::query()->select([
                        'id',
                        'sku',
                        'name',
                        'options',
                        'print_spaces',
                        'attributes',
                        'pricing_mode',
                        'full_printed',
                ])
                ->whereIn('id', $product_ids)
                ->where('product_type', ProductType::TEMPLATE)
                ->where('status', ProductStatus::ACTIVE)
                ->orderBy('priority')
                ->orderBy('name')
                ->get();
            }

            $products = $category->products->sortBy(function ($template) use ($topTemplateIds, $templatesCount) {
                $key = array_search($template->id, $topTemplateIds);

                if ($key === false) {
                    return $templatesCount++;
                }

                return $key;
            })->toArray();

            foreach ($products as $product) {
                $row = [];
                $row['product_sku'] = $product['sku'] ?? '';
                $row['product_name'] = $product['name'] ?? '';
                $fullPrinted = (bool) $product['full_printed'];
                $optionStr = '';
                if (empty($product['options'])) {
                    continue;
                }
                $options = json_decode($product['options'], true);
                $product['custom_options'] = null;
                if ($product['full_printed'] === ProductPrintType::HANDMADE && !empty($options["custom_options"])) {
                    $product['custom_options'] = $options["custom_options"];
                    unset($options["custom_options"]);
                    $product['options'] = $options;
                }

                if (
                    isset($options['color']) &&
                    ($product['full_printed'] == ProductPrintType::PRINT_2D_FULL || $product['full_printed'] == ProductPrintType::AOP)
                ) {
                    if (in_array('white', $options['color'])) {
                        $options['color'] = ['white'];
                    } else {
                        $options['color'] = [];
                    }
                }

                $optionKeys = array_keys($options);
                foreach ($optionKeys as $keyIndex => $key) {
                    if($key === 'pack' || ($key === 'color' && $fullPrinted)) {
                        continue;
                    }
                    if($key === 'color') {
                        $optionStr .= $key . ': ' . $this->chunkColorsToString($options[$key]);
                    } else {
                        $optionStr .= $key . ': ' . implode(',', $options[$key]);
                    }

                    if($keyIndex + 1 < count($optionKeys)) {
                        $optionStr .= PHP_EOL;
                    }
                }
                $row['options'] = $optionStr;
                $printSpaceStr = '';
                $actualSizeStr = '';
                if (!empty($product['print_spaces'])) {
                    $printSpaces = json_decode($product['print_spaces']);
                    foreach ($printSpaces as $p) {
                        $printSpaceStr .= $p->name . ': ' . $p->width . ' x ' . $p->height . 'px';
                        if(!empty($p->dpi)) {
                            $printSpaceStr .= ', dpi: ' . $p->dpi;
                        }
                        $printSpaceStr .= PHP_EOL;
                        if (isset($p->actualWidth) && isset($p->actualHeight)) {
                            $actualSizeStr .= $p->name . ': ' . $this->convertInchToPixel((int)$p->actualWidth) . ' x ' . $this->convertInchToPixel((int)$p->actualHeight) . 'px';
                        }
                    }
                }
                $row['print_spaces'] = $printSpaceStr;
                $row['actual_size'] = $actualSizeStr;
                $row['design_template'] = '';
                $productAttributes = json_decode($product['attributes']);
                if(!empty($productAttributes) && isset($productAttributes->design_template_url)) {
                    $row['design_template'] = s3Url($productAttributes->design_template_url);
                }
                $productsData[] = array_values($row);
            }

            $category['products'] = $productsData;

            if($cIdx === 0) {
                $requestUpdateSheets[] = new Google_Service_Sheets_Request([
                    'updateSheetProperties' => new UpdateSheetPropertiesRequest([
                        'properties' => [
                            'sheetId' => 0,
                            'title' => $category['name']
                        ],
                        'fields' => 'title'
                    ])
                ]);
            } else {
                $requestUpdateSheets[] = new Google_Service_Sheets_Request([
                    'addSheet' => new AddSheetRequest([
                        'properties' => [
                            'title' => $category['name']
                        ],
                    ])
                ]);
            }
        }

        $body = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requestUpdateSheets
        ]);

        $this->googleSheetServices->spreadsheets->batchUpdate($this->productCatalogSpreadSheetId, $body);

        unset($category);

        foreach ($categories as $category) {
            $body = new Google_Service_Sheets_ValueRange([
                'range' => "'" . $category['name'] . "'",
                'values' => $category['products']
            ]);
            $params = [
                'valueInputOption' => 'USER_ENTERED'
            ];
            $this->googleSheetServices->spreadsheets_values->update($this->productCatalogSpreadSheetId, "'" . $category['name'] . "'", $body, $params);
        }

        return $this->formatProductCatalogs();
    }
    public function updateOutOfStockProduct() {
        $this->clearAllSheets($this->oosProductSpreadSheetId);

        $requestUpdateSheets = [
            new Google_Service_Sheets_Request([
                'updateSheetProperties' => new UpdateSheetPropertiesRequest([
                    'properties' => [
                        'sheetId' => 0,
                        'title' => 'Table of contents'
                    ],
                    'fields' => 'title'
                ])
            ])
        ];

        $page = 1;
        $limit = 1000;
        $productVariants = [];
        do {
            [$products, $total] = (new Elastic())->getListStockStatus(['in_stock' => 0], $limit, $page);
            foreach ($products as $product) {
                $tempProduct = [...$product];
                unset ($tempProduct['id'], $tempProduct['product_id']);
                if (!isset($productVariants[$product['product_id']])) {
                    $requestUpdateSheets[] = new Google_Service_Sheets_Request([
                        'addSheet' => new AddSheetRequest([
                            'properties' => [
                                'title' => str_replace('"', '""', $product['name']) . ' - ' . $product['sku']
                            ],
                        ])
                    ]);
                    $heading = array_map(fn($item) => strtoupper($item), array_keys($tempProduct));
                    $productVariants[$product['product_id']][] = ['No.', ...$heading];
                }
                $productVariants[$product['product_id']][] = [...$tempProduct];
            }
            $page++;
        }
        while ($page <= ceil($total/$limit));
        $body = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requestUpdateSheets
        ]);
        $this->googleSheetServices->spreadsheets->batchUpdate($this->oosProductSpreadSheetId, $body);
        $sheets = $this->googleSheetServices->spreadsheets->get($this->oosProductSpreadSheetId)?->getSheets();
        $dataUpdateValues = [
            ['No.', 'Product Name'],
        ];
        foreach ($sheets as $key => $sheet) {
            if ($key === 0) continue;
            $properties = $sheet->getProperties();
            $url = "https://docs.google.com/spreadsheets/d/{$this->oosProductSpreadSheetId}/edit#gid={$properties->getSheetId()}";
            $dataUpdateValues[] = [$key, "=HYPERLINK(\"$url\"; \"{$properties->getTitle()}\")"];
        }

        $data = [
            new Google_Service_Sheets_ValueRange([
                'range' => "Table of contents",
                'values' => $dataUpdateValues
            ])
        ];
        foreach ($productVariants as $productId => $variants) {
            foreach ($variants as $key => &$variant) {
                if ($key != 0){
                    $variant['ww'] = $variant['ww'] ? '' : 'Available';
                    $variant['eu'] = $variant['eu'] ? '' : 'Available';
                    $variant['us'] = $variant['us'] ? '' : 'Available';
                    $variant['ca'] = $variant['ca'] ? '' : 'Available';
                    $variant = [$key++, ...$variant];
                }
            }
            $data[] = new Google_Service_Sheets_ValueRange([
                'range' =>  str_replace('"', '""', $variants[1]['name']) . " - " . $variants[1]['sku'],
                'values' => array_map(fn($item) => array_values($item), $variants),
            ]);
        }
        $body = new Google_Service_Sheets_BatchUpdateValuesRequest([
            'valueInputOption' => 'USER_ENTERED',
            'data' => $data
        ]);
        $this->googleSheetServices->spreadsheets_values->batchUpdate($this->oosProductSpreadSheetId, $body);

        return $this->formatOosProduct();
    }

    private function convertInchToPixel(int $inch) {
        $DPI = 300;
        return $inch * $DPI;
    }

    public function updatePriceCatalog() {
        // clear spreadsheet
        $this->clearAllSheets($this->priceSpreadSheetId);

        // rename default sheet to base cost and create new sheet for shipping cost
        $requestUpdateSheets = [
            new Google_Service_Sheets_Request([
                'updateSheetProperties' => new UpdateSheetPropertiesRequest([
                    'properties' => [
                        'sheetId' => 0,
                        'title' => 'Base costs'
                    ],
                    'fields' => 'title'
                ])
            ]),
            new Google_Service_Sheets_Request([
                'addSheet' => new AddSheetRequest([
                    'properties' => [
                        'title' => 'Shipping costs',
                        'sheetId' => 2,
                    ],
                ])
            ]),
            new Google_Service_Sheets_Request([
                'addSheet' => new AddSheetRequest([
                    'properties' => [
                        'title' => 'On Promotion',
                        'sheetId' => 1,
                        'sheetType' => 'GRID',
                        'gridProperties' => [
                            'rowCount' => 50000,  # Set number of rows
                            'columnCount' => 100  # Set number of columns (A-Z)
                        ]
                    ],
                ])
            ]),
        ];
        // sheetId will need to be updated format below

        $body = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requestUpdateSheets
        ]);

        $this->googleSheetServices->spreadsheets->batchUpdate($this->priceSpreadSheetId, $body);

        $sheets = $this->googleSheetServices->spreadsheets->get($this->priceSpreadSheetId)->getSheets();
        $sheets = array_map(function ($sheet) {
            return $sheet->getProperties();
        }, $sheets);

        if(count($sheets) !== 3) {
            return;
        }

        // EXPORT BASE COST
        $productVariants = ProductVariant::query()
            ->join('product', 'product_variant.product_id', '=', 'product.id')
            ->join('system_location', 'system_location.code', '=', 'product_variant.location_code')
            ->select([
                'product.id as product_id',
                'product.sku as product_sku',
                'product.name as product_name',
                'product_variant.variant_key as variant_key',
                'system_location.name as location',
                'product_variant.base_cost as base_cost',
                'product_variant.out_of_stock as out_of_stock',
            ])
            ->where([
                'product.product_type' => ProductType::TEMPLATE,
                'product.status' => ProductStatus::ACTIVE,
            ])
            ->get();

        $productPromotion = ProductPromotion::query()
            ->where('end_time', '>=', now())
            ->whereNull('deleted_at')
            ->pluck('product_id')->toArray();

        $productPromotionVariants = $productVariants->whereIn('product_id', $productPromotion);

        $listLocations = $productVariants->pluck('location')->unique()->toArray();
        $baseCostData = GoogleSheetApiService::prepareBaseCostDataSheet($productVariants, $listLocations);
        [$body, $params] = GoogleSheetApiService::prepareBodyAndParams($baseCostData, "Base costs");
        $this->googleSheetServices->spreadsheets_values->update($this->priceSpreadSheetId, "Base costs", $body, $params);
        // END EXPORT BASE COST

        // EXPORT PROMOTION
        $listLocations = $productPromotionVariants->pluck('location')->unique()->toArray();
        $baseCostPromotionData = GoogleSheetApiService::prepareOnPromotionDataSheet($productPromotionVariants, $listLocations);
        [$body, $params] = GoogleSheetApiService::prepareBodyAndParams($baseCostPromotionData, "On Promotion");
        $this->googleSheetServices->spreadsheets_values->update($this->priceSpreadSheetId, "On Promotion", $body, $params);


        $titles = array_shift($baseCostPromotionData);
        $numberTitles = count($titles);
        $totalRows = count($baseCostPromotionData);

        // backgroundColor & text
        $pointIndex = $numberTitles - 3;
        $endRowIndex = $totalRows + 1;
        $requests = GoogleSheetApiService::setBackgroundColorAndText(1, $pointIndex, 1, $endRowIndex);
        $requestBody = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requests
        ]);
        $this->googleSheetServices->spreadsheets->batchUpdate($this->priceSpreadSheetId, $requestBody);

        // merge cells
        $endTimeIndex = $numberTitles - 1;
        $startTimeIndex = $numberTitles - 2;
        $listSku = array_unique(array_map(function($item) {
            return $item[0];
        }, $baseCostPromotionData));

        $variantGroupBySku = [];
        foreach($listSku as $item) {
            foreach($baseCostPromotionData as $variant) {
                if($variant[0] === $item) {
                    $variantGroupBySku[$item][] = $variant;
                }
            }
        }
        $variantGroupBySku = array_values($variantGroupBySku);
        $startRowIndex = 1;
        $requests = [];
        foreach($variantGroupBySku as $index => $item) {
            if ($index !== 0) {
                $startRowIndex += count($variantGroupBySku[$index - 1]);
            }

            $arrRequestEndTime = GoogleSheetApiService::mergeTimeOnPromotion($item, $endTimeIndex, $startRowIndex);
            $arrRequestStartTime = GoogleSheetApiService::mergeTimeOnPromotion($item, $startTimeIndex, $startRowIndex);
            $requests = [...$requests, ...$arrRequestEndTime, ...$arrRequestStartTime];
        }
        if (count($requests)) {
            $requestBody = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
                'requests' => $requests
            ]);
            $this->googleSheetServices->spreadsheets->batchUpdate($this->priceSpreadSheetId, $requestBody);
        }
        // END EXPORT PROMOTION

        // EXPORT SHIPPING COST
        $shippingCosts = ShippingRule::query()
            ->join('product', 'shipping_rule.product_id', '=', 'product.id')
            ->join('system_location', 'system_location.code', '=', 'shipping_rule.location_code')
            ->select([
                'product.sku as product_sku',
                'product.name as product_name',
                'system_location.name as location',
                'shipping_rule.shipping_cost as shipping_cost',
                'shipping_rule.extra_cost as extra_cost',
                'shipping_rule.shipping_method as shipping_method',
            ])
            ->where([
                'product.product_type' => ProductType::TEMPLATE,
                'product.status' => ProductStatus::ACTIVE,
            ])
            ->get();

        $listLocations = $shippingCosts->pluck('location')->unique()->toArray();
        $shippingCostData = GoogleSheetApiService::prepareShippingCostDataSheet($shippingCosts, $listLocations);
        [$body, $params] = GoogleSheetApiService::prepareBodyAndParams($shippingCostData, "Shipping costs");
        $this->googleSheetServices->spreadsheets_values->update($this->priceSpreadSheetId, "Shipping costs", $body, $params);
        // END EXPORT SHIPPING COST

        return $this->formatBaseCostCatalogs(count($listLocations));
    }

    public function updateListCatalogs(): JsonResponse
    {
        try {
            $this->updateProductCatalog();
            $this->updatePriceCatalog();
            $this->updateOutOfStockProduct();

            return $this->successResponse();
        } catch (\Exception $exception) {
            logException($exception);
            return $this->errorResponse($exception->getMessage(), 400);
        }
    }

    /**
     * @param string $spreadSheetId
     * @return BatchUpdateSpreadsheetResponseAlias
     */
    public function clearAllSheets(string $spreadSheetId): BatchUpdateSpreadsheetResponseAlias
    {
        $sheets = $this->googleSheetServices->spreadsheets->get($spreadSheetId)->getSheets();
        $requests = [];
        foreach ($sheets as $sheet) {
            $sheetId = $sheet->getProperties()->sheetId;
            if($sheetId === 0) {
                $requests[] = new \Google_Service_Sheets_UpdateCellsRequest([
                    'updateCells' => [
                        'range' => [
                            'sheetId' => 0
                        ],
                        'fields' => "*" //clears everything
                    ]
                ]);
            } else {
                $requests[] = new \Google_Service_Sheets_DeleteSheetRequest([
                    'deleteSheet' => [
                        'sheetId' => $sheetId,
                    ]
                ]);
            }
        }


        $requestBody = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest();
        $requestBody->setRequests($requests);
        return $this->googleSheetServices->spreadsheets->batchUpdate($spreadSheetId, $requestBody);
    }


    private function formatProductCatalogs(): BatchUpdateSpreadsheetResponseAlias
    {
        $sheets = $this->googleSheetServices->spreadsheets->get($this->productCatalogSpreadSheetId)->getSheets();

        $requests = [];

        foreach ($sheets as $sheet) {
            $sheetProperties = $sheet->getProperties();
            $sheetId = $sheetProperties->sheetId;

            // set text bold for headers
            $requests[] = new Google_Service_Sheets_Request([
                'repeatCell' => new RepeatCellRequest([
                    'range' => [
                        'sheetId' => $sheetId,
                        'endRowIndex' => 1
                    ],
                    'cell' => [
                        'userEnteredFormat' => [
                            'textFormat' => [
                                'bold' => true
                            ]
                        ]
                    ],
                    'fields' => 'userEnteredFormat.textFormat.bold'
                ])
            ]);

            $requests[] = new Google_Service_Sheets_Request([
                'updateSheetProperties' => [
                    'properties' => [
                        'sheetId' => $sheetId,
                        'gridProperties' => [
                            'frozenRowCount' => 1,
                        ]
                    ],
                    'fields'=> 'gridProperties.frozenRowCount'
                ]
            ]);

            // auto resize fields
            $requests[] = new Google_Service_Sheets_Request([
                'autoResizeDimensions' => new AutoResizeDimensionsRequest([
                    'dimensions' => new DimensionRange([
                        'sheetId' => $sheetId,
                        'dimension' => 'columns',
                        'startIndex' => 1,
                    ])
                ])
            ]);
        }

        $requests = array_values($requests);

        $requestBody = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requests
        ]);

        return $this->googleSheetServices->spreadsheets->batchUpdate($this->productCatalogSpreadSheetId, $requestBody);
    }
    public function formatOosProduct(): BatchUpdateSpreadsheetResponseAlias
    {
        $sheets = $this->googleSheetServices->spreadsheets->get($this->oosProductSpreadSheetId)->getSheets();

        $requests = [];

        foreach ($sheets as $sheet) {
            $sheetProperties = $sheet->getProperties();
            $sheetId = $sheetProperties->sheetId;

            // set text bold for headers
            $requests[] = new Google_Service_Sheets_Request([
                'repeatCell' => new RepeatCellRequest([
                    'range' => [
                        'sheetId' => $sheetId,
                        'endRowIndex' => 1,
                    ],
                    'cell' => [
                        'userEnteredFormat' => [
                            'textFormat' => [
                                'foregroundColor' => [
                                  'red' => 1,
                                  'green' => 1,
                                  'blue' => 1,
                                ],
                                'fontSize' => 17,
                                'bold' => true
                            ],
                            'backgroundColor' => [
                                'red' => 192,
                                'green' => 204,
                                'blue' => 116,
                            ],
                            'horizontalAlignment' => 'CENTER'
                        ]
                    ],
                    'fields' => 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)'
                ])
            ]);
            $requests[] = new Google_Service_Sheets_Request([
                'repeatCell' => new RepeatCellRequest([
                    'range' => [
                        'sheetId' => $sheetId,
                        'startColumnIndex' => 0,
                        'endColumnIndex' => 2,
                        'startRowIndex' => 1,
                    ],
                    'cell' => [
                        'userEnteredFormat' => [
                            'horizontalAlignment' => 'center'
                        ]
                    ],
                    'fields' => 'userEnteredFormat.horizontalAlignment'
                ])
            ]);
            $requests[] = new Google_Service_Sheets_Request([
                'repeatCell' => new RepeatCellRequest([
                    'range' => [
                        'sheetId' => $sheetId,
                        'startRowIndex' => 1,
                        'startColumnIndex' => 3,
                    ],
                    'cell' => [
                        'userEnteredFormat' => [
                            'horizontalAlignment' => 'center'
                        ]
                    ],
                    'fields' => 'userEnteredFormat.horizontalAlignment'
                ])
            ]);

            $requests[] = new Google_Service_Sheets_Request([
                'updateSheetProperties' => [
                    'properties' => [
                        'sheetId' => $sheetId,
                        'gridProperties' => [
                            'frozenRowCount' => 1,
                        ],
                    ],
                    'fields'=> 'gridProperties.frozenRowCount'
                ]
            ]);
//
//            // auto resize fields
//            $requests[] = new Google_Service_Sheets_Request([
//                'autoResizeDimensions' => new AutoResizeDimensionsRequest([
//                    'dimensions' => new DimensionRange([
//                        'sheetId' => $sheetId,
//                        'dimension' => 'columns',
//                        'startIndex' => 1,
//                    ])
//                ])
//            ]);
        }

        $requests = array_values($requests);

        $requestBody = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requests
        ]);

        return $this->googleSheetServices->spreadsheets->batchUpdate($this->oosProductSpreadSheetId, $requestBody);
    }


    private function formatBaseCostCatalogs($countLocations = 0): BatchUpdateSpreadsheetResponseAlias
    {
        $sheets = $this->googleSheetServices->spreadsheets->get($this->priceSpreadSheetId)->getSheets();
        $requests = [];
        foreach ($sheets as $sheet) {
            $sheetProperties = $sheet->getProperties();
            $sheetId = $sheetProperties->sheetId;

            // set text bold for headers
            $requests[] = new Google_Service_Sheets_Request([
                'repeatCell' => new RepeatCellRequest([
                    'range' => [
                        'sheetId' => $sheetId,
                        'endRowIndex' => 1
                    ],
                    'cell' => [
                        'userEnteredFormat' => [
                            'textFormat' => [
                                'bold' => true
                            ]
                        ]
                    ],
                    'fields' => 'userEnteredFormat.textFormat.bold'
                ])
            ]);

            if($sheetId === 2) {
                $requests[] = new Google_Service_Sheets_Request([
                    'mergeCells' => new MergeCellsRequest([
                        'range' => [
                            'sheetId' => $sheetId,
                            'startRowIndex' => 0,
                            'endRowIndex' => 3,
                            'startColumnIndex' => 0,
                            'endColumnIndex' => 1,
                        ],
                        'mergeType' => 'MERGE_ALL'
                    ])
                ]);
                $requests[] = new Google_Service_Sheets_Request([
                    'mergeCells' => new MergeCellsRequest([
                        'range' => [
                            'sheetId' => $sheetId,
                            'startRowIndex' => 0,
                            'endRowIndex' => 3,
                            'startColumnIndex' => 1,
                            'endColumnIndex' => 2,
                        ],
                        'mergeType' => 'MERGE_ALL'
                    ])
                ]);
                $i = 0;
                do {
                    //format location header
                    $startColIdx = 4 * ($i + 1) - 2;
                    $endColIdx = 4 * ($i + 2) - 2;
                    $requests[] = new Google_Service_Sheets_Request([
                        'mergeCells' => new MergeCellsRequest([
                            'range' => [
                                'sheetId' => $sheetId,
                                'startRowIndex' => 0,
                                'endRowIndex' => 1,
                                'startColumnIndex' => $startColIdx,
                                'endColumnIndex' => $endColIdx,
                            ],
                            'mergeType' => 'MERGE_ALL'
                        ])
                    ]);

                    //format shipping method header
                    $startStandardColIdx = 2 * ($i + 1);
                    $endColStandardIdx = 2 * ($i + 2);
                    $startExpressColIdx = 2 * ($i + 1) + 2;
                    $endColExpressIdx = 2 * ($i + 2) + 2;
                    $requests[] = new Google_Service_Sheets_Request([
                        'mergeCells' => new MergeCellsRequest([
                            'range' => [
                                'sheetId' => $sheetId,
                                'startRowIndex' => 1,
                                'endRowIndex' => 2,
                                'startColumnIndex' => $startStandardColIdx,
                                'endColumnIndex' => $endColStandardIdx,
                            ],
                            'mergeType' => 'MERGE_ALL'
                        ])
                    ]);
                    $requests[] = new Google_Service_Sheets_Request([
                        'mergeCells' => new MergeCellsRequest([
                            'range' => [
                                'sheetId' => $sheetId,
                                'startRowIndex' => 1,
                                'endRowIndex' => 2,
                                'startColumnIndex' => $startExpressColIdx,
                                'endColumnIndex' => $endColExpressIdx,
                            ],
                            'mergeType' => 'MERGE_ALL'
                        ])
                    ]);
                    $i++;
                } while ($i < $countLocations);
                $requests[] = new Google_Service_Sheets_Request([
                    'repeatCell' => new RepeatCellRequest([
                        'range' => [
                            'sheetId' => $sheetId,
                            'endRowIndex' => 2,
                            'startColumnIndex' => 2
                        ],
                        'cell' => [
                            'userEnteredFormat' => [
                                'horizontalAlignment' => 'center'
                            ]
                        ],
                        'fields' => 'userEnteredFormat.horizontalAlignment'
                    ])
                ]);
                $requests[] = new Google_Service_Sheets_Request([
                    'updateSheetProperties' => [
                        'properties' => [
                            'sheetId' => $sheetId,
                            'gridProperties' => [
                                'frozenColumnCount' => 1,
                            ]
                        ],
                        'fields'=> 'gridProperties.frozenColumnCount'
                    ]
                ]);
                $requests[] = new Google_Service_Sheets_Request([
                    'updateSheetProperties' => [
                        'properties' => [
                            'sheetId' => $sheetId,
                            'gridProperties' => [
                                'frozenRowCount' => 3,
                            ]
                        ],
                        'fields'=> 'gridProperties.frozenRowCount'
                    ]
                ]);
            } else {
                $requests[] = new Google_Service_Sheets_Request([
                    'updateSheetProperties' => [
                        'properties' => [
                            'sheetId' => $sheetId,
                            'gridProperties' => [
                                'frozenRowCount' => 1,
                            ]
                        ],
                        'fields'=> 'gridProperties.frozenRowCount'
                    ]
                ]);
            }

            // auto resize fields
            $requests[] = new Google_Service_Sheets_Request([
                'autoResizeDimensions' => new AutoResizeDimensionsRequest([
                    'dimensions' => new DimensionRange([
                        'sheetId' => $sheetId,
                        'dimension' => 'columns',
                        'startIndex' => 1,
                    ])
                ])
            ]);
        }

        $requests = array_values($requests);

        $requestBody = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requests
        ]);

        return $this->googleSheetServices->spreadsheets->batchUpdate($this->priceSpreadSheetId, $requestBody);
    }

    /**
     * Insert a new permission.
     * Default to anyone as reader.
     *
     * @param String $fileId ID of the file to insert permission for.
     * "default" type.
     * @param String $type The value "user", "group", "domain" or "default".
     * @param String $role The value "owner", "writer" or "reader".
     * @return Google_Service_Drive_Permission|JsonResponse The inserted permission. NULL is
     *     returned if an API error occurred.
     */
    private function insertPermission(string $fileId = '', String $type = 'anyone', string $role = 'reader'): ?Google_Service_Drive_Permission
    {
        if(empty($fileId)) {
            return $this->errorResponse('Empty file id');
        }
        $newPermission = new Google_Service_Drive_Permission();
        $newPermission->setType($type);
        $newPermission->setRole($role);
        return $this->googleDriveService->permissions->create($fileId, $newPermission);
    }

    /**
     *Create new SpreadSheet
     */
    private function createNewSpreadSheet()
    {
        $spreadsheet = new Google_Service_Sheets_Spreadsheet([
            'properties' => [
                'title' => 'Senprints base cost catalog'
            ]
        ]);
        $spreadsheet = $this->googleSheetServices->spreadsheets->create($spreadsheet, [
            'fields' => 'spreadsheetId'
        ]);
        return $spreadsheet->spreadsheetId;
    }

    /**
     * Create new Sheet in Spreadsheet
     *
     * @param string $spreadSheetId
     * @param string $newSheetTitle
     * @return BatchUpdateSpreadsheetResponseAlias
     */
    private function createNewSheet(string $spreadSheetId = '', string $newSheetTitle = ''): BatchUpdateSpreadsheetResponseAlias
    {
        $requests = [
            new Google_Service_Sheets_Request([
                'addSheet' => new AddSheetRequest([
                    'properties' => [
                        'title' => $newSheetTitle
                    ]
                ])
            ])
        ];

        $body = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requests
        ]);
        return $this->googleSheetServices->spreadsheets->batchUpdate($spreadSheetId, $body);
    }

    private function renameSheet(string $spreadSheetId = '', string $sheetId = '', string $newSheetTitle = ''): BatchUpdateSpreadsheetResponseAlias
    {
        $requests = [
            new Google_Service_Sheets_Request([
                'updateSheetProperties' => new UpdateSheetPropertiesRequest([
                    'properties' => [
                        'sheetId' => $sheetId,
                        'title' => $newSheetTitle
                    ],
                    'fields' => 'title'
                ])
            ])
        ];

        $body = new Google_Service_Sheets_BatchUpdateSpreadsheetRequest([
            'requests' => $requests
        ]);
        return $this->googleSheetServices->spreadsheets->batchUpdate($spreadSheetId, $body);
    }

    private function chunkColorsToString($colors): string
    {
        $colors = array_chunk($colors, 8);
        $colorString = '';
        foreach ($colors as $colorArray) {
            $colorString .= implode(', ', $colorArray) . PHP_EOL;
        }
        return $colorString;
    }

    public function test() {
        $this->updateListCatalogs();
        return $this->successResponse();
    }
}

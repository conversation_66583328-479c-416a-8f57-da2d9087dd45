<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CampaignPublicStatusEnum;
use App\Enums\DomainStatusEnum;
use App\Enums\PageTypeEnum;
use App\Enums\ProductReviewDisplayEnum;
use App\Enums\ProductStatus;
use App\Enums\SellerBillingType;
use App\Enums\StorageDisksEnum;
use App\Enums\StoreConnectionPlatformEnum;
use App\Enums\StoreDomainStatusEnum;
use App\Enums\StoreStatusEnum;
use App\Enums\SystemConfigCrispKeyEnum;
use App\Http\Requests\Admin\AdminVerifyStoreDomainRequest;
use App\Http\Requests\Admin\MassUpdateStoreStatusRequest;
use App\Http\Requests\ProductReview\ProductReviewCouponSettingRequest;
use App\Http\Requests\ProductReview\UpdateProductReviewDisplayStatusRequest;
use App\Http\Requests\Seller\Store\AddDomainRequest;
use App\Http\Requests\Seller\Store\StoreRequest;
use App\Http\Requests\Store\FetchNavigationRequest;
use App\Http\Requests\Store\UpdateAdvancedStoreInfo;
use App\Http\Requests\Store\UpdateContactRequest;
use App\Http\Requests\Store\UpdateInfoRequest;
use App\Http\Requests\Store\UpdateWooCommerceWebhookRequest;
use App\Http\Requests\Storefront\Navigation\BulkUpdateRequest;
use App\Http\Requests\UpdateStoreBannersRequest;
use App\Http\Requests\UpdateStoreTrackingCodeRequest;
use App\Jobs\Seller\ProcessNewDomainJob;
use App\Library\DomainManagement\DomainClient;
use App\Library\Kubernetes\SPIngress;
use App\Models\Campaign;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Elastic;
use App\Models\File;
use App\Models\Page;
use App\Models\Product;
use App\Models\PromotionRule;
use App\Models\SellerCollection;
use App\Models\SellerDomain;
use App\Models\Store;
use App\Models\StoreCollection;
use App\Models\StoreConnection;
use App\Models\StoreDomain;
use App\Models\StoreHeadTag;
use App\Models\StoreNavigation;
use App\Models\StoreProduct;
use App\Models\SystemConfig;
use App\Models\TrademarkList;
use App\Models\User;
use App\Rules\Store\UniqueSubdomainRule;
use App\Rules\ValidImage;
use App\Services\CloudFlareCustomHostname;
use App\Services\NameDotCom;
use App\Services\StoreService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use App\Traits\GetStoreDomain;
use Cloudflare\API\Adapter\ResponseException;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator as Paginator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Modules\ShopifyApp\Models\ShopifySession;
use Shopify\Rest\Admin2022_10\Webhook;
use Shopify\Utils;
use Throwable;
use Tymon\JWTAuth\Facades\JWTFactory;

class StoreController extends Controller
{
    use ApiResponse, GetStoreDomain;

    /**
     * @param $storeId
     * @param $sellerId
     * @return bool
     */
    public static function isStoreBelongToSeller($storeId, $sellerId): bool
    {
        return Store::query()
            ->where([
                'id' => $storeId,
                'seller_id' => $sellerId
            ])
            ->exists();
    }

    /**
     * Display a listing of the resource.
     * @param Request $request
     * @return Paginator
     */
    public function index(Request $request): Paginator
    {
        $user = currentUser();
        $query = Store::query()
            ->with(['shopify_session', 'tiktok_shop:id,store_id', 'connection'])
            ->select([
                'store.id',
                'store.domain',
                'store.logo_url',
                'store.sub_domain',
                'store.domain_expired_at',
                'store.name as store_name',
                'store.seller_id',
                'user.name as seller_name',
                'user.email as seller_email',
                'store.status as store_status',
                'store.total_orders',
                'store.created_at',
                'store.enable_add_to_cart',
                'store.enable_custom_phone',
                'store.enable_contract_form',
                'store.disable_promotion',
                'store.disable_related_product',
                'store.disable_related_collection',
                'store.disable_pre_discount',
                'store.always_show_order_summary',
                'store.enable_payment_ssl_norton',
                'store.enable_deliver_to',
                'store.enable_product_name_export_feed',
                'store.enable_insurance_fee',
                'store.show_product_stats',
                'store.enable_crisp_support',
                'store.list_all_my_campaigns',
            ])
            ->join('user', 'user.id', '=', 'store.seller_id');

        $search = $request->get('q');
        $status = $request->get('status');
        $sellerId = $request->get('seller_id');

        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('store.name', 'like', '%' . $search . '%');
                $query->orWhere('store.domain', 'like', '%' . $search . '%');
                $query->orWhere('store.sub_domain', 'like', '%' . $search . '%');
                $query->orWhereHas('storeDomain', function ($q) use ($search) {
                    $q->where('domain', 'like', '%' . $search . '%');
                });
            });
        }

        if (!empty($sellerId)) {
            $query->where('seller_id', $sellerId);
        }

        if (!empty($status)) {
            $query->where('store.status', $status);
        }

        if ($user->isSeller()) {
            $query->where('seller_id', $user->getUserId());
            $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
            if (!empty($store_ids)) {
                $query->whereIn('store.id', $store_ids);
            }
        }
        $query->orderByDesc('created_at');
        $total = $query->get()->count();
        $currentPage = $request->get('page');
        $currentPage = max($currentPage, 1);
        $limit = (int)$request->query('per_page', 15);
        $stores = $query->limit($limit)->offset(($currentPage - 1) * $limit)->get();
        $stores->map(function ($store) {
            $store->store_id = (int)$store->id;
            $store->is_shopify_connected = (int)(isset($store->shopify_session) && !empty($store->shopify_session->access_token));
            if (empty($store->domain) && $store->is_shopify_connected) {
                $store->domain = $store->shopify_session->shop;
            }
            $payload = JWTFactory::customClaims([
                'seller_id' => $store->seller_id,
                'store_id' => $store->store_id,
            ])->make();
            $store->store_token = jwtauth_encode($payload);
            try {
                $arrFilterElastic = [];
                $arrFilterElastic['seller_id'] = $store->seller_id;
                if (!$store->list_all_my_campaigns) {
                    $arrFilterElastic['store_id'] = $store->id;
                }
                $store->campaigns = (new Elastic())->countCampaigns($arrFilterElastic);
            } catch (Throwable $e) {
                $store->campaigns = 0;
            }
            unset($store->shopify_session, $store->id);
            return $store;
        });
        return new Paginator(
            $stores,
            $total,
            $limit,
            $currentPage,
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreRequest $request
     * @return JsonResponse
     */
    public function store(StoreRequest $request): JsonResponse
    {
        try {
            // note: use create() instead of insertGetId()
            // because create() will trigger observer (Observers\StoreObserver)
            $user = currentUser();

            if ($user->isSeller() && !$user->canMemberManageStore()) {
                return $this->errorResponse('You don\'t have permission to add a new storefront.');
            }

            $sellerId = $user->getUserId();
            $count = Store::query()->where('seller_id', $sellerId)->count();
            $result = Store::query()->create([
                'seller_id' => $sellerId,
                'name' => $request->post('name'),
                'description' => $request->post('description'),
                'sub_domain' => $request->post('sub_domain'),
                'theme' => $request->post('theme'),
                'email' => $request->post('email'),
                'phone' => $request->post('phone'),
                'logo_url' => $request->post('logo_url'),
                'address' => $request->post('address'),
                'list_all_my_campaigns' => ($count === 0) // list all camp for first store
            ]);

            if ($result->wasRecentlyCreated) {
                UserService::updateStepProductTour($sellerId, 'create-campaign');
                return $this->successResponse(['store_id' => $result->id]);
            }

            return $this->errorResponse('Create store failed.');
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function show($id): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('get_users');
        if ($user->isSeller()) {
            $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
            if (!empty($store_ids) && !in_array((int)$id, $store_ids, true)) {
                return $this->errorResponse('You don\'t have permission to get detail of this storefront.');
            }
        }
        $userId = $user->getUserId();
        $query = Store::query()
            ->with(['seller', 'banners', 'collection_banners'])
            ->where('id', $id);

        if ($user->isSeller()) {
            $query->addSelect(
                [
                    'custom_payment' => User::select('custom_payment')
                        ->whereColumn('id', 'store.seller_id')
                        ->limit(1)
                ]
            )->where('seller_id', $userId);
        }

        $store = $query->first();

        if ($store && $store->custom_payment) {
            $store->is_proxy = $store->custom_payment;
        }

        return $store ? $this->successResponse($store) : $this->errorResponse();
    }

    protected function update(Request $request, $storeId): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $data = $request->validated();

        if (!empty($data['featured_collection_ids']) && is_array($data['featured_collection_ids'])) {
            $data['featured_collection_ids'] = implode(',', $data['featured_collection_ids']);
        }

        $query = Store::query()
            ->where('id', $storeId)
            ->when(!$user->isAdmin(), function ($query) use ($userId) {
                $query->where('seller_id', $userId);
            });

        if ($user->isSeller()) {
            $query->addSelect([
                'custom_payment' => User::query()->select('custom_payment')->whereColumn('id', 'store.seller_id')->limit(1)
            ])->where('seller_id', $userId);
        }
        $store = $query->first();

        if (is_null($store)) {
            return $this->errorResponse();
        }
        if (empty($store->custom_payment)) {
            $data['enable_custom_phone'] = 0;
            $data['enable_contract_form'] = 1;
        }
        if ($store->update($data)) {
            clearStoreCache($storeId);
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function updateInfo(UpdateInfoRequest $request, $storeId): JsonResponse
    {
        $user = currentUser();
        if ($user->isSeller()) {
            $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
            if (!empty($store_ids) && !in_array((int)$storeId, $store_ids, true)) {
                return $this->errorResponse('Error: You can not update the data of store is not belong to you.');
            }
        }
        $singleFeaturedCollectionId = $request->get('feature_collection_id');
        $featuredCollectionIds = $request->post('featured_collection_ids');

        $sellerCollectionQuery = SellerCollection::query();
        $storeCollectionQuery = StoreCollection::query();

        if ($featuredCollectionIds) {
            if (count($featuredCollectionIds) > 5) {
                return $this->errorResponse('Error: You can only select up to 5 collections.');
            }

            foreach ($featuredCollectionIds as $collectionId) {
                self::addFeaturedCollection($sellerCollectionQuery, $storeCollectionQuery, $storeId, $collectionId);
            }
        }

        if ($singleFeaturedCollectionId) {
            self::addFeaturedCollection($sellerCollectionQuery, $storeCollectionQuery, $storeId, $singleFeaturedCollectionId);
        }

        try {
            clearStoreCache($storeId);
        } catch (Exception $e) {
            logException($e);
        }

        return $this->update($request, $storeId);
    }

    public function updateAdvanced(UpdateAdvancedStoreInfo $request, $storeId)
    {
        $user = currentUser();
        if ($user->isSeller()) {
            $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
            if (!empty($store_ids) && !in_array((int)$storeId, $store_ids, true)) {
                return $this->errorResponse('Error: You can not update the data of store is not belong to you.');
            }
        }

        return $this->update($request, $storeId);
    }

    private static function addFeaturedCollection($sellerCollectionQuery, $storeCollectionQuery, $storeId, $collectionId): void
    {
        try {
            $sellerCollectionQuery->firstOrCreate([
                'seller_id' => currentUser()->getUserId(),
                'collection_id' => $collectionId
            ], [
                'popularity' => 1
            ]);
        } catch (QueryException $e) {
            // noop
        }

        try {
            $storeCollectionQuery->firstOrCreate([
                'store_id' => $storeId,
                'collection_id' => $collectionId
            ], [
                'position' => 1,
                'popularity' => 0
            ]);
        } catch (QueryException $e) {
            // noop
        }
    }

    public function updateContact(UpdateContactRequest $request, $id): JsonResponse
    {
        return $this->update($request, $id);
    }

    public function destroy($storeId): JsonResponse
    {
        $storeQuery = Store::query()
            ->where([
                'seller_id' => currentUser()->getUserId(),
                'id' => $storeId
            ]);

        $storeInfo = $storeQuery->first();
        if (is_null($storeInfo)) {
            return $this->errorResponse();
        }
        $deleted = $storeQuery->delete();
        StoreDomain::destroyAllByStoreId($storeId);
        clearStoreCache($storeId);

        return $deleted ? $this->successResponse() : $this->errorResponse();
    }

    public function updateNavigation(Request $request, $storeId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'text' => ['bail', 'required', 'string'],
            'url' => ['required', 'string'],
            'place' => ['required', 'string'],
            'parent_id' => [
                'nullable',
                Rule::exists('store_navigation', 'id')
                    ->where(function ($query) use ($storeId) {
                        $query->where('store_id', $storeId);
                    })
            ],
        ], [
            'parent_id:exists' => 'Parent ID is not parent navigation.'
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $place = $request->post('place');
        $parentId = $request->post('parent_id');

        $maxPosition = StoreNavigation::query()
            ->where([
                'store_id' => $storeId,
                'place' => $place
            ])
            ->max('position');

        if ($parentId) {
            // Check if parent id is parent navigation and parent is same type with new navigation
            $result = StoreNavigation::query()
                ->where([
                    'place' => $place,
                    'id' => $parentId
                ])
                //                ->whereNull('parent_id')
                ->first();

            if (!$result || $result->place !== $place) {
                $parentId = null;
            }
        }

        $result = StoreNavigation::query()
            ->create([
                'store_id' => $storeId,
                'link_text' => $request->post('text'),
                'link_url' => $request->post('url'),
                'place' => $place ?? 'header', // default
                'parent_id' => $parentId,
                'position' => ($maxPosition + 1)
            ]);

        if ($result->wasRecentlyCreated) {
            clearStoreCache($storeId);
            return $this->successResponse(['navigation_id' => $result->id]);
        }

        return $this->errorResponse();
    }

    /**
     * @param BulkUpdateRequest $request
     * @param $storeId
     * @return JsonResponse
     */
    public function bulkUpdateNavigation(BulkUpdateRequest $request, $storeId): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $navigations = $request->json('navigations');
        $place = $request->json('place');

        if (!$navigations) {
            return $this->errorResponse();
        }

        try {
            foreach ($navigations as $navigationInfo) {
                $id = $navigationInfo['nav_id'];
                $parent_id = $navigationInfo['parent_id'];
                $link_text = $navigationInfo['link_text'];
                $link_url = $navigationInfo['link_url'];
                $position = $navigationInfo['position'];
                $exists = StoreNavigation::query()
                    ->join('store', 'store.id', '=', 'store_navigation.store_id')
                    ->where([
                        'store_navigation.id' => $id,
                        'store_navigation.place' => $place,
                        'store.seller_id' => $userId
                    ])
                    ->exists();

                if ($exists) {
                    StoreNavigation::query()
                        ->where([
                            'id' => $id,
                            'place' => $place
                        ])
                        ->update([
                            'parent_id' => $parent_id,
                            'link_text' => $link_text,
                            'link_url' => $link_url,
                            'position' => $position,
                        ]);
                }
            }

            clearStoreCache($storeId);

            return $this->successResponse();
        } catch (Exception $e) {
            logException($e);
            return $this->errorResponse();
        }
    }

    /**
     * @throws Exception
     */
    public function editNavigation(Request $request, $storeId, $navigationId): JsonResponse
    {
        $request->validate([
            'text' => 'bail|required|string',
            'url' => 'required|string'
        ]);

        $updated = StoreNavigation::query()
            ->where([
                'store_id' => $storeId,
                'id' => $navigationId
            ])
            ->update([
                'link_text' => $request->post('text'),
                'link_url' => $request->post('url')
            ]);
        clearStoreCache($storeId);
        return $updated > 0 ? $this->successResponse() : $this->errorResponse();
    }

    public function fetchNavigationParents($id): JsonResponse
    {
        $navigations = StoreNavigation::query()
            ->select(['id', 'link_text', 'link_url', 'place', 'parent_id'])
            ->whereNull('parent_id')
            ->where('store_id', $id)
            ->get();

        return $this->successResponse($navigations);
    }

    public function fetchNavigationTree(FetchNavigationRequest $request, $storeId): JsonResponse
    {
        $place = $request->get('place');
        $query = StoreNavigation::query();

        if ($place) {
            $query->where('place', $place);
        }

        // closure to recursive query (multi-level navigation)
        $recursiveChildrens = function ($query) use (&$recursiveChildrens, $storeId) {
            $query->where('store_id', $storeId)
                  ->orderBy('position', 'ASC');

            $query->with(['childrens' => $recursiveChildrens]);
        };

        $navigations = $query->select(['id', 'link_text', 'link_url', 'place', 'parent_id'])
            ->with(['childrens' => $recursiveChildrens])
            ->whereNull('parent_id')
            ->where('store_id', $storeId)
            ->orderBy('position', 'ASC')
            ->get();

        return $this->successResponse($navigations);
    }

    public function fetchNavigation(Request $request, $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'place' => ['nullable', 'in:header,footer,side_column']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $place = $request->get('place');
        $query = StoreNavigation::query();

        if ($place) {
            $query->where('place', $place);
        }

        $navigations = $query->select(['id', 'link_text', 'link_url', 'place', 'parent_id'])
            ->where('store_id', $id)
            ->get();

        return $this->successResponse($navigations);
    }

    public function deleteNavigation($storeId, $navigationId): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Navigate should don't have any sub navigation
            if (StoreNavigation::query()
                ->where([
                    'parent_id' => $navigationId,
                    'store_id' => $storeId
                ])
                ->exists()
            ) {
                throw new \RuntimeException('You should remove all sub navigation.');
            }

            $deleted = StoreNavigation::query()
                ->where([
                    'store_id' => $storeId,
                    'id' => $navigationId
                ])
                ->delete();

            if ($deleted) {
                clearStoreCache($storeId);
                DB::commit();
                return $this->successResponse();
            }

            DB::rollBack();
            return $this->errorResponse('Cannot delete navigation.');
        } catch (Exception $e) {
            DB::rollBack();
            return $this->errorResponse($e->getMessage());
        }
    }

    public function updateStatus(Request $request, $storeId): JsonResponse
    {
        $user = currentUser();
        $user->hasPermissionOrAbort('update_user');
        if ($user->isSeller()) {
            $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
            if (!empty($store_ids) && !in_array((int)$storeId, $store_ids, true)) {
                return $this->errorResponse('Error: You can not update the status of store is not belong to you.');
            }
        }
        $userId = $user->getUserId();

        $validator = Validator::make($request->all(), [
            'status' => ['required', Rule::in(StoreStatusEnum::getValues())]
        ]);

        if ($validator->fails()) {
            return $this->errorResponse('Update store status failed. Validation error.');
        }

        $status = $request->json('status');

        try {
            DB::beginTransaction();
            $query = Store::query();

            if (!$user->isAdmin()) {
                $query->where('seller_id', $userId);
            }

            $query->where('id', $storeId);
            $data = ['status' => $status];
            $result = $query->update($data);

            if ($result > 0) {
                DB::commit();
                clearStoreCache($storeId);
                return $this->successResponse($data);
            }

            DB::rollBack();
        } catch (Exception $ex) {
            DB::rollBack();
        }

        return $this->errorResponse('Update store status failed.');
    }

    /**
     * @param MassUpdateStoreStatusRequest $request
     * @return JsonResponse
     * @throws Throwable
     */
    public function adminMassUpdateStatus(MassUpdateStoreStatusRequest $request): JsonResponse
    {
        $status = $request->input('status');
        $storeIds = $request->input('store_ids');

        try {
            DB::beginTransaction();
            $query = Store::query();

            $query->whereIn('id', $storeIds);
            $data = ['status' => $status];
            $updateResult = $query->update($data);

            if ($updateResult > 0) {
                DB::commit();
                foreach ($storeIds as $storeId) {
                    clearStoreCache($storeId);
                }

                return $this->successResponse($data);
            }
            DB::rollBack();
        } catch (Exception $ex) {
            DB::rollBack();
        }

        return $this->errorResponse('Update store status failed.');
    }

    public function addCampaign(Request $request, $storeId): JsonResponse
    {
        $campaignId = $request->post('campaign_id');
        $includeFromMarketPlace = $request->boolean('include_from_market_place');

        $seller = currentUser()->getInfoAccess();

        $queryCheck = Campaign::query()
            ->onSellerConnection($seller)
            ->whereKey($campaignId);
        if ($includeFromMarketPlace) {
            $queryCheck->where('public_status', CampaignPublicStatusEnum::APPROVED);
        } else {
            $queryCheck->where('seller_id', currentUser()->getUserId());
        }

        if (!$queryCheck->exists()) {
            return $this->errorResponse();
        }

        try {
            StoreProduct::query()
                ->firstOrCreate([
                    'store_id' => $storeId,
                    'product_id' => $campaignId
                ]);

            CampaignController::updateProductSyncStatus([$campaignId]);
            clearStoreCache($storeId);

            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse();
        }
    }

    public function removeCampaign($storeId, $campaignId): JsonResponse
    {
        $deleted = StoreProduct::query()
            ->where([
                'store_id' => $storeId,
                'product_id' => $campaignId
            ])
            ->delete();

        CampaignController::updateProductSyncStatus([$campaignId]);
        clearStoreCache($storeId);

        return $deleted ? $this->successResponse() : $this->errorResponse();
    }

    public function getAddedCampaigns($storeId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $campaignIds = StoreProduct::query()
            ->where('store_id', $storeId)
            ->orderBy('created_at', 'desc')
            ->limit(500)
            ->pluck('product_id')->toArray();
        $campaigns = Campaign::query()
            ->onSellerConnection($seller)
            ->whereIn('id', $campaignIds)
            ->where('status', ProductStatus::ACTIVE)
            ->orderBy('created_at', 'DESC')
            ->limit(30)
            ->get();

        return $this->successResponse($campaigns);
    }

    public function getAddedCollections($storeId): JsonResponse
    {
        $collections = StoreCollection::query()
            ->select('id', 'name', 'slug', 'banner_url')
            ->leftJoin('collection', 'collection.id', '=', 'store_collection.collection_id')
            ->where('store_id', $storeId)
            ->get();

        return $this->successResponse($collections);
    }

    public function addCollection(Request $request, $storeId): JsonResponse
    {
        try {
            StoreCollection::query()->firstOrCreate([
                'store_id' => $storeId,
                'collection_id' => $request->post('collection_id')
            ]);

            clearStoreCache($storeId);
            return $this->successResponse();
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function removeCollection($storeId, $collectionId): JsonResponse
    {
        $deleted = StoreCollection::query()
            ->where([
                'store_id' => $storeId,
                'collection_id' => $collectionId
            ])
            ->delete();

        clearStoreCache($storeId);

        return $deleted ? $this->successResponse() : $this->errorResponse();
    }

    private function getTradeMarkList()
    {
        $ttl = 86400 * 30; // 30 days
        return Cache::remember('trademarks', $ttl, function () {
            return TrademarkList::query()->get()->toArray();
        });
    }

    public function checkTrademarkDomain(Request $request, bool $removeSenprintsTLD = true): JsonResponse
    {
        if ($request->has('skip_validate_tld')) {
            $removeSenprintsTLD = false;
        }
        $domain = parseValidateDomain($request->input('domain'), $removeSenprintsTLD);
        if (is_null($domain)) {
            return $this->errorResponse('Invalid domain.');
        }
        $user = currentUser();
        $strictValidateTM = true;

        if ($user->isLoggedAsByAdmin()) {
            $strictValidateTM = false;
        }

        $domainValid = isDomainCanNotDetectTradeMark($domain, $strictValidateTM, $user->hasCustomPayment());

        if (!$domainValid) {
            return $this->errorResponse('Domain name trademark infringement');
        }

        return $this->successResponse('Success');
    }

    public function addDomain(AddDomainRequest $request, $storeId): JsonResponse
    {
        $domain = parseValidateDomain($request->post('domain'));

        if (is_null($domain)) {
            return $this->errorResponse('Invalid domain.');
        }

        try {
            DB::beginTransaction();

            $store = Store::query()
                ->where([
                    'id' => $storeId,
                    'seller_id' => currentUser()->getUserId()
                ])
                ->first();

            if (!$store) {
                return $this->errorResponse('Store not found.');
            }

            $store->update([
                'domain' => $domain,
                'domain_status' => DomainStatusEnum::PENDING
            ]);

            $storeDomain = StoreDomain::query()->create([
                'seller_id' => currentUser()->getUserId(),
                'store_id' => $storeId,
                'domain' => $domain,
                'status' => StoreDomainStatusEnum::PENDING
            ]);

            if ($storeDomain->wasRecentlyCreated) {
                $user = currentUser();
                $isCustomStore = $user->getInfoAccess()->custom_payment && $store->is_proxy;

                ProcessNewDomainJob::dispatchAfterResponse(
                    $domain,
                    $isCustomStore,
                    $user->getUserId()
                );
            }

            DB::commit();
            return $this->successResponse('Add domain success.');

        } catch (Exception $e) {
            DB::rollBack();
            logException($e);
            return $this->errorResponse('Add domain failed.');
        }
    }

    public function verifyDomain($storeId): JsonResponse
    {
        $storeDomain = Store::getDomainById($storeId);

        // Check store have a domain
        if (is_null($storeDomain)) {
            return $this->errorResponse();
        }

        // Call method create ingress with kubernetes apis
        $domainManager = DomainClient::instance();

        if ($domainManager && $domainManager->register($storeDomain)) {
            Store::updateDomainStatus($storeId, 'complete');
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function getStoreLinks(Request $request, $storeId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => ['in:product,page,collection,category'],
            'q' => ['nullable']
        ]);

        if ($validator->fails()) {
            return $this->errorResponse();
        }

        $q = $request->get('q');
        $type = $request->get('type');
        $user = currentUser();
        $userId = $user->getUserId();

        switch ($type) {
            case 'product':
                $query = Product::query()
                    ->onSellerConnection($user)
                    ->select(['product.id', 'product.name', 'product.slug'])
                    ->join('store_product', 'store_product.store_id', '=', 'product.id')
                    ->where([
                        'seller_id' => $userId,
                        'store_product.store_id' => $storeId
                    ])
                    ->when($q, function ($query, $keyword) {
                        $query->where(function ($query) use ($keyword) {
                            $query->where('product.name', 'LIKE', '%' . $keyword . '%');
                            $query->orWhere('product.slug', 'LIKE', '%' . $keyword . '%');
                        });
                    });

                $query->limit(15);
                $products = $query->get();

                if ($products->count() > 0) {
                    return $this->successResponse($products);
                }

                return $this->errorResponse();
            case 'page':
                $query = Page::query();
                $query->select(['id', 'title as name', 'slug']);
                $query->where(
                    function ($query) use ($storeId, $q) {
                        $query->where('page.title', 'LIKE', '%' . $q . '%');
                        $query->where([
                            'type' => PageTypeEnum::CUSTOM,
                            'store_id' => $storeId
                        ]);
                    }
                )
                    ->orWhere(function ($query) use ($storeId, $q) {
                        $query->where('type', PageTypeEnum::TEMPLATE)
                            ->whereNotIn(
                                'id',
                                Page::query()->select(['template_id'])
                                    ->where('page.title', 'LIKE', '%' . $q . '%')
                                    ->where('store_id', $storeId)
                                    ->whereNotNull('template_id')
                                    ->distinct()
                                    ->get()
                            );
                    });

                $query->orderBy('title', 'ASC');
                $pages = $query->get();

                //                $query = Page::query()
                //                    ->select(['id', 'title as name', 'slug'])
                //                    ->where('store_id', $storeId);
                //
                //                if ($q) {
                //                    $query->where('page.title', 'LIKE', '%' . $q . '%');
                //                    $query->orWhere('page.slug', 'LIKE', '%' . $q . '%');
                //                }
                //
                //                $query->where('seller_id', $userId);
                //                $query->limit(15);
                //                $pages = $query->get();

                if ($pages->count() > 0) {
                    return $this->successResponse($pages);
                }

                return $this->errorResponse();
            case 'collection':
                $query = Collection::query()
                    ->select(['collection.id', 'collection.name', 'collection.slug'])
                    // ->join('store_collection', 'collection.id', '=', 'store_collection.collection_id')
                    ->join('seller_collection', 'seller_collection.collection_id', '=', 'collection.id');
                // ->where('store_collection.store_id', $storeId);

                if ($q) {
                    $query->where('collection.name', 'LIKE', '%' . $q . '%');
                    // $query->orWhere('collection.slug', 'LIKE', '%' . $q . '%');
                }

                $query->where('seller_collection.seller_id', $userId);
                $query->limit(15);
                $pages = $query->get();

                if ($pages->count() > 0) {
                    return $this->successResponse($pages);
                }

                return $this->errorResponse();
            case 'category':
                $query = Category::query()
                    ->select(['id', 'name', 'slug'])
                    ->orderBy('parent_id', 'ASC');

                if ($q) {
                    $query->where('name', 'LIKE', '%' . $q . '%');
                    // $query->orWhere('slug', 'LIKE', '%' . $q . '%');
                }

                $query->limit(15);
                $categories = $query->get();

                if ($categories->count() > 0) {
                    return $this->successResponse($categories);
                }

                return $this->errorResponse();
            default:
                return $this->errorResponse();
        }
    }

    public function deleteStoreBanner($storeId, $bannerId): JsonResponse
    {
        $file = File::query()
            ->select('file_url')
            ->firstWhere([
                'id' => $bannerId,
                'store_id' => $storeId
            ]);

        if (is_null($file)) {
            return $this->errorResponse();
        }

        try {
            // remove from DB
            $deleted = File::query()
                ->where([
                    'id' => $bannerId,
                    'store_id' => $storeId
                ])
                ->delete();

            if (!$deleted) {
                return $this->errorResponse();
            }

            // remove from S3
            $deleted_default = Storage::disk(StorageDisksEnum::DEFAULT)->delete($file->file_url);
            $deleted_s3 = false;
            if (StorageDisksEnum::DEFAULT !== StorageDisksEnum::S3) {
                $deleted_s3 = Storage::disk(StorageDisksEnum::S3)->delete($file->file_url);
            }

            clearStoreCache($storeId);
            return ($deleted_s3 || $deleted_default) ? $this->successResponse() : $this->errorResponse();
        } catch (Exception $e) {
            return $this->errorResponse();
        }
    }

    /**
     * @param UpdateStoreBannersRequest $request
     * @param $storeId
     * @return JsonResponse
     * @throws Throwable
     */
    public function saveBanners(UpdateStoreBannersRequest $request, $storeId): JsonResponse
    {
        $data = $request->post('banners');

        if (!$data) {
            return $this->errorResponse();
        }

        foreach ($data as $key => $value) {
            $value['store_id'] = $storeId;
            $data[$key] = $value;
        }

        try {
            DB::beginTransaction();
            batch()->updateWithTwoIndex(new File([], true), $data, 'id', 'store_id');
            DB::commit();
            return $this->successResponse();
        } catch (Exception $exception) {
            DB::rollback();
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * Get all stores of seller
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function indexBySeller(Request $request): JsonResponse
    {
        $user = currentUser();
        $sellerId = $user->getUserId();
        $authId = $user->getAuthorizedAccountId();
        $store_ids = get_team_seller_stores($sellerId, $authId);
        $limit = $request->get('limit', 500);
        $query = Store::query()->select('id', 'name', 'domain', 'sub_domain', 'domain_status')->where('seller_id', $sellerId);
        if (!empty($store_ids)) {
            $query->whereIn('id', $store_ids);
        }
        $stores = $query->limit($limit)->get();
        return $stores->count() > 0
            ? $this->successResponse($stores)
            : $this->errorResponse();
    }

    public function clearCacheStore($storeId): JsonResponse
    {
        try {
            clearStoreCache($storeId);

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    public function clearCacheAllStore(): JsonResponse
    {
        try {
            clearStoreCache();

            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    /**
     * @param $storeId
     * @return JsonResponse
     */
    public function getStoreTrackingCode($storeId): JsonResponse
    {
        $store = Store::query()
            ->select(['tracking_code', 'seller_id'])
            ->firstWhere('id', $storeId);

        if ($store === null) {
            return $this->errorResponse('Get Tracking Code failed');
        }

        $store->setAppends([]);
        $arrayTrackingCode = json_decode($store->tracking_code, true);
        if (empty($arrayTrackingCode['klaviyo_public_key'])) {
            $arrayTrackingCode['klaviyo_public_key'] = $store->seller?->klaviyo_public_key;
        }
        return $this->successResponse(json_encode($arrayTrackingCode), 'Successfully');
    }

    /**
     * @param UpdateStoreTrackingCodeRequest $request
     * @param int $storeId
     * @return JsonResponse
     */
    public function updateStoreTrackingCode(UpdateStoreTrackingCodeRequest $request, int $storeId): JsonResponse
    {
        $googleTag = $request->json('google_tag');
        $googleMerchantCenterVerification = $request->json('google_merchant_verification');
        $googleAnalytics = $request->json('google_analytics');
        $googleAdwordsId = $request->json('google_adwords_id');
        $googleAdsGTag = $request->json('google_ads_gtag');
        $googleAdsGTagThankYou = $request->json('google_ads_gtag_thank_you');
        $facebookPixel = $request->json('facebook_pixel');
        $facebookMetaTag = $request->json('facebook_meta_tag');
        $facebookConversionToken = $request->json('facebook_conversion_token');
        $pinterestMetaTag = $request->json('pinterest_meta_tag');
        $pinterestTagId = $request->json('pinterest_tag_id');
        $tiktokPixel = $request->json('tiktok_pixel');
        $snapchatPixel = $request->json('snapchat_pixel');
        $quoraPixel = $request->json('quora_pixel');
        $bingPixel = $request->json('bing_pixel');
        $klaviyoPublicKey = $request->json('klaviyo_public_key');
        $redditPixel = $request->json('reddit_pixel');

        $data = [
            'google_tag' => $googleTag,
            'google_merchant_verification' => $googleMerchantCenterVerification,
            'google_analytics' => $googleAnalytics,
            'google_adwords_id' => $googleAdwordsId,
            'google_ads_gtag' => $googleAdsGTag,
            'google_ads_gtag_thank_you' => $googleAdsGTagThankYou,
            'facebook_pixel' => $facebookPixel,
            'facebook_meta_tag' => $facebookMetaTag,
            'facebook_conversion_token' => $facebookConversionToken,
            'pinterest_meta_tag' => $pinterestMetaTag,
            'pinterest_tag_id' => $pinterestTagId,
            'tiktok_pixel' => $tiktokPixel,
            'snapchat_pixel' => $snapchatPixel,
            'quora_pixel' => $quoraPixel,
            'bing_pixel' => $bingPixel,
            'klaviyo_public_key' => $klaviyoPublicKey,
            'reddit_pixel' => $redditPixel,
        ];

        $jsonData = json_encode($data);

        try {
            DB::beginTransaction();

            Store::query()
                ->where([
                    'id' => $storeId,
                    'seller_id' => currentUser()->getUserId()
                ])
                ->update(['tracking_code' => $jsonData]);

            DB::commit();
            clearStoreCache($storeId);
            return $this->successResponse('Tracking Code updated successfully');
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->errorResponse('Tracking Code update failed');
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function adminListPendingStoreDomains(Request $request): JsonResponse
    {
        $user = currentUser();
        $query = Store::query()
            ->select([
                'store.id as store_id',
                'store.domain',
                'store.domain_status',
                'store.sub_domain',
                'store.name as store_name',
                'user.name as seller_name',
                'user.email as seller_email',
                'store.status as store_status',
                'store.created_at'
            ])
            ->join('user', 'user.id', '=', 'store.seller_id');

        $search = $request->get('q');
        $sellerId = $request->get('email');

        // todo: refactor to when()
        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('store.name', 'like', '%' . $search . '%');
                $query->orWhere('store.domain', 'like', '%' . $search . '%');
            });
        }

        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        if ($user->isSeller()) {
            $query->where('seller_id', $user->getUserId());
        }

        $query->where('store.domain_status', DomainStatusEnum::PENDING);
        $query->orderByDesc('store.created_at');
        $stores = $query->paginate(15);

        return $this->successResponse($stores);
    }

    /**
     * @param AdminVerifyStoreDomainRequest $request
     * @return JsonResponse
     */
    public function adminVerifyStoreDomain(AdminVerifyStoreDomainRequest $request): JsonResponse
    {
        $storeId = $request->input('store_id');
        DB::beginTransaction();
        try {
            $store = Store::query()->find($storeId);
            if (!$store) {
                return $this->errorResponse('Cannot find storeId');
            }

            if (is_null($store->domain)) {
                return $this->errorResponse('Cannot find domain name');
            }

            // Call method create ingress with kubernetes apis
            $ingress = SPIngress::instance();
            if ($ingress && $ingress->create($store->domain)) {
                $store->domain_status = DomainStatusEnum::COMPLETE;
                $store->save();
                DB::commit();

                return $this->successResponse();
            }
            return $this->errorResponse('Cannot verify domain');
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function updateProductReviewDisplayStatus(UpdateProductReviewDisplayStatusRequest $request, $storeId): JsonResponse
    {
        DB::beginTransaction();
        try {
            $status = $request->input('status', ProductReviewDisplayEnum::DISABLE);
            $store = Store::where('seller_id', currentUser()->getUserId())->find($storeId);

            if (!$store) {
                return $this->errorResponse('Store not found');
            }

            $store->fill(['product_review_display' => $status])->update();

            DB::commit();
            return $this->successResponse();
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function productReviewCouponSetting(ProductReviewCouponSettingRequest $request, $storeId): JsonResponse
    {
        DB::beginTransaction();
        try {
            $coupon = $request->input('product_review_coupon');
            $message = $request->input('product_review_thank_you_message');
            $store = Store::where('seller_id', currentUser()->getUserId())->find($storeId);

            if (!$store) {
                return $this->errorResponse('Store not found');
            }

            if (!empty($coupon)) {
                $code = PromotionRule::where('seller_id', currentUser()->getUserId())
                    ->where('discount_code', $coupon)
                    ->first();

                if (!$code) {
                    return $this->errorResponse('Discount code not found');
                }
            }

            $store->fill([
                'product_review_coupon' => $coupon,
                'product_review_thank_you_message' => $message,
            ])->update();

            DB::commit();
            return $this->successResponse();
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * List store domains in admin dashboard
     * @param Request $request
     * @return JsonResponse
     */
    public function adminListPendingStoreDomainsPatch(Request $request): JsonResponse
    {
        $user = currentUser();
        $query = StoreDomain::query()
            ->select([
                'store_domains.id as store_domain_id',
                'store.id as store_id',
                'store_domains.domain as domain',
                'store_domains.status as domain_status',
                'store.sub_domain as sub_domain',
                'store.name as store_name',
                'user.id as seller_id',
                'user.name as seller_name',
                'user.email as seller_email',
                'store.status as store_status',
                'store.created_at as created_at'
            ])
            ->leftJoin('user', 'store_domains.seller_id', '=', 'user.id')
            ->leftJoin('store', 'store_domains.store_id', '=', 'store.id');

        $search = $request->get('q');
        $sellerId = $request->get('email');

        // todo: refactor to when()
        if ($search) {
            $query->where(function ($query) use ($search) {
                $query->where('store.name', 'like', '%' . $search . '%');
                $query->orWhere('store_domains.domain', 'like', '%' . $search . '%');
            });
        }

        if ($sellerId) {
            $query->where('store.seller_id', $sellerId);
        }

        if ($user->isSeller()) {
            $query->where('store_domains.seller_id', $user->getUserId());
        }

        $query->where('store_domains.status', StoreDomainStatusEnum::PENDING);
        $query->orderByDesc('store_domains.created_at');
        $stores = $query->paginate(15);

        return $this->successResponse($stores);
    }

    public function adminVerifyStoreDomainPatch(AdminVerifyStoreDomainRequest $request): JsonResponse
    {
        $storeId = $request->input('store_id');
        $storeDomainId = $request->input('store_domain_id');
        if (is_null($storeId) || is_null($storeDomainId)) {
            return $this->errorResponse('Cannot find storeId or storeDomainId');
        }

        DB::beginTransaction();
        try {
            $storeDomain = StoreDomain::query()
                ->where([
                    'store_id' => $storeId,
                    'id' => $storeDomainId,
                ])
                ->with('store')
                ->first();

            if (!$storeDomain) {
                return $this->errorResponse('Cannot find storeId or storeDomainId');
            }

            if (is_null($storeDomain->domain)) {
                return $this->errorResponse('Cannot find domain name');
            }

            $domainManager = DomainClient::instance();
            if ($domainManager->register($storeDomain->domain)) {
                $isDefault = 0;
                if (is_null($storeDomain->store->domain)) {
                    $isDefault = 1;
                }
                $storeDomain->update([
                    'status' => StoreDomainStatusEnum::ACTIVATED,
                    'is_default' => $isDefault
                ]);
                if ($isDefault === 1) {
                    $storeDomain->store->update([
                        'domain' => $storeDomain->domain,
                        'domain_status' => DomainStatusEnum::COMPLETE
                    ]);
                }
                DB::commit();
                return $this->successResponse();
            }
            return $this->errorResponse('Cannot verify domain');
        } catch (Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * Get store info
     */
    public function adminGetStoreInfo(Request $request): JsonResponse
    {
        $storeId = $request->input('store_id');

        $storeInfo = Store::query()
            ->select([
                'id',
                'is_proxy',
                'name',
                'tags'
            ])
            ->where('id', $storeId)
            ->firstOrFail();

        return $this->successResponse($storeInfo);
    }

    /**
     * Update store info
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function adminUpdateStoreInfo(Request $request): JsonResponse
    {
        $storeId = $request->input('store_id');
        $isProxy = $request->boolean('is_proxy');
        $customTags = $request->input('custom_tags');

        try {
            Store::query()->where('id', $storeId)
                ->update([
                    'is_proxy' => $isProxy,
                    'tags' => $customTags
                ]);

            clearStoreCache($storeId);
            Cache::forget(CacheKeys::getStoreDomainKey(
                self::getDomainByStoreId($storeId)
            ));

            return $this->successResponse();
        } catch (Exception $exception) {
            return $this->errorResponse();
        }
    }

    public function enabledSmartRemarketing(Request $request, $storeId): JsonResponse
    {
        $enabled = $request->boolean('status');

        Store::query()
            ->where('seller_id', currentUser()->getUserId())
            ->where('id', $storeId)
            ->update(['smart_remarketing' => $enabled]);

        return $this->successResponse(null, ($enabled ? 'Enabled' : 'Disabled') . ' smart remarketing success!');
    }

    public function saveNotificationSettings(Request $request, $storeId): JsonResponse
    {
        $settings = $request->input('settings', '');

        Store::query()
            ->where('seller_id', currentUser()->getUserId())
            ->where('id', $storeId)
            ->update([
                'notification_settings' => $settings
            ]);

        return $this->successResponse(null, 'Save notification settings success');
    }

    /**
     * Check if subdomain is already taken
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkDuplicateSubdomain(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'sub_domain' => ['required', new UniqueSubdomainRule()]
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->errors());
        }

        return $this->successResponse();
    }

    /**
     * @param Request $request
     * @param $storeId
     * @return JsonResponse
     */
    public function connectPlatform(Request $request, $storeId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'shop' => 'required',
                'platform' => 'required|in:shopify,amazon',
            ]);
            if ($validator->fails()) {
                return $this->errorResponse($validator->getMessageBag());
            }
            $seller_id = currentUser()->getUserId();
            $shop = $request->input('shop');
            $platform = $request->input('platform');
            $store = Store::query()->where('seller_id', $seller_id)->whereKey($storeId)->first();
            if (!$store) {
                return $this->errorResponse('Store not found');
            }
            if ($platform === 'shopify') {
                $shopify_session = ShopifySession::query()->where('shop', $shop)->where('seller_id', $seller_id)->first();
                if (!$shopify_session) {
                    return $this->errorResponse('Shopify store not found in our system.');
                }
                if (empty($shopify_session->store_id) || (int)$storeId !== (int)$shopify_session->store_id) {
                    $shopify_session->store_id = $storeId;
                    $shopify_session->updated_at = currentTime();
                    $session = Utils::loadOfflineSession($shopify_session->shop);
                    if (!empty($shopify_session->webhooks)) {
                        $webhooks = json_decode($shopify_session->webhooks, true);
                        if (!empty($webhooks) && is_array($webhooks)) {
                            try {
                                foreach ($webhooks as $webhook_id) {
                                    Webhook::delete(
                                        $session,
                                        $webhook_id
                                    );
                                }
                            } catch (\Throwable $e) {
                                logException($e, 'Webhook::delete', 'shopify');
                            }
                        }
                    }
                    $hash = substr(md5($shop . $storeId), 0, 15);
                    $webhooks = array();
                    $webhookUrl = env('API_DOMAIN', 'https://api.senprint.io') . '/public/shopify/webhooks/' . $hash . '/createOrder';
                    try {
                        $webhook = new Webhook($session);
                        $webhook->address = $webhookUrl;
                        $webhook->topic = "orders/paid";
                        $webhook->format = "json";
                        $webhook->save(true);
                        $shopify_webhooks = Webhook::all(
                            $session,
                            [],
                            [
                                'address' => $webhookUrl
                            ]
                        );
                        if (!empty($shopify_webhooks)) {
                            $webhooks["orders_paid"] = $shopify_webhooks[0]->id;
                        }
                    } catch (\Throwable $e) {
                        logException($e, $webhookUrl, 'shopify');
                    }
                    $webhookUrl = env('API_DOMAIN', 'https://api.senprint.io') . '/public/shopify/webhooks/' . $hash . '/updateOrder';
                    try {
                        $webhook = new Webhook($session);
                        $webhook->address = $webhookUrl;
                        $webhook->topic = "orders/updated";
                        $webhook->format = "json";
                        $webhook->save(true);
                        $shopify_webhooks = Webhook::all(
                            $session,
                            [],
                            [
                                'address' => $webhookUrl
                            ]
                        );
                        if (!empty($shopify_webhooks)) {
                            $webhooks["orders_updated"] = $shopify_webhooks[0]->id;
                        }
                    } catch (\Throwable $e) {
                        logException($e, $webhookUrl, 'shopify');
                    }
                    if (!empty($webhooks)) {
                        $shopify_session->webhooks = json_encode($webhooks);
                    }
                    $shopify_session->save();
                }
            }
            return $this->successResponse();
        } catch (Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @param $storeId
     * @return JsonResponse
     */
    public function cloneStore(Request $request, $storeId)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'domain' => 'required',
            'buy_domain' => 'required|boolean',
            'logo_url' => ['nullable', new ValidImage()],
            'favicon' => ['nullable', new ValidImage()],
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $domain = $request->input('domain');
        $domain = parseValidateDomain($domain);

        if (is_null($domain)) {
            return $this->errorResponse('Invalid domain.');
        }

        $user = currentUser();
        $strictValidateTM = true;

        if ($user->isLoggedAsByAdmin()) {
            $strictValidateTM = false;
        }

        $domainValid = isDomainCanNotDetectTradeMark(
            $domain,
            $strictValidateTM,
            $user->hasCustomPayment()
        );

        if (!$domainValid) {
            return $this->errorResponse('Domain name trademark infringement');
        }

        $userId = $user->getUserId();
        $userInfo = $user->getInfoAccess();
        $store = Store::query()
            ->where('seller_id', $userId)
            ->where('domain', $domain)
            ->exists();

        if ($store) {
            return $this->errorResponse('Domain already added to another store.');
        }

        $store = Store::query()
            ->where('seller_id', $userId)
            ->where('id', $storeId)
            ->first();

        if (!$store) {
            return $this->errorResponse('Store not found');
        }

        $exists_store_domain = StoreDomain::query()->where('domain', $domain)->exists();

        if ($exists_store_domain) {
            return $this->errorResponse('Domain already added to another store.');
        }

        $buy_domain = $request->boolean('buy_domain');
        $clone_navigation = $request->boolean('clone_navigation', true);
        $clone_collection = $request->boolean('clone_collection', true);
        $clone_head_tag = $request->boolean('clone_head_tag', true);
        $clone_products = $request->boolean('clone_products');
        $name = $request->input('name');
        $logo_url = $request->input('logo_url');
        $favicon = $request->input('favicon');

        if ($buy_domain) {
            $nameDotCom = new NameDotCom();
            if ($userInfo->balance === 0) {
                return $this->errorResponse('You don\'t have enough balance to buy this domain.');
            }
            try {
                $domainInfo = $nameDotCom->checkAvailability($domain);
            } catch (Exception $e) {
                logException($e);
                return $this->errorResponse('Cannot check domain name availability');
            }
            if ($domainInfo === false || !data_get($domainInfo, 'results.0.purchasable', false)) {
                return $this->errorResponse('Domain name is not available.');
            }
            $domainPrice = data_get($domainInfo, 'results.0.purchasePrice', 0);
            if ($userInfo->balance < $domainPrice) {
                return $this->errorResponse('You don\'t have enough balance to buy this domain. The domain price is ' . $domainPrice);
            }
            if ($domainPrice === 0) {
                return $this->errorResponse('Domain name price is not available.');
            }
        }
        $email = $store->email;
        if ($email) {
            $email_array = explode('@', $email);
            $email = str_replace('.', '-', $domain) . '@' . $email_array[1];
        }
        $store_data = $store->replicate()->fill([
            'name' => $name,
            'status' => StoreStatusEnum::INACTIVE,
            'domain' => null,
            'domain_status' => null,
            'domain_expired_at' => null,
            'logo_url' => null,
            'favicon' => null,
            'description' => str_replace($store->name, $name, $store->description),
            'sub_domain' => str_replace('.', '-', $domain),
            'email' => $email,
            'mail_support' => $store->mail_support ? str_replace('.', '-', $domain) : null,
            'created_at' => now(),
            'updated_at' => now(),
        ])->toArray();
        unset($store_data['base_url']);
        $newStore = new Store($store_data);
        if ($clone_navigation) {
            $newStore->saveQuietly();
        } else {
            $newStore->save();
        }

        $update = [];
        if (!empty($logo_url)) {
            $logo_path = saveTempFileAws($logo_url, 's/' . $newStore->id);
            if (!empty($logo_path)) {
                $update['logo_url'] = $logo_path;
            }
        }
        if (!empty($favicon)) {
            $favicon = saveTempFileAws($favicon, 's/' . $newStore->id);
            if (!empty($favicon)) {
                $update['favicon'] = $favicon;
            }
        }
        if (!empty($update)) {
            $newStore->fill($update)->update();
        }

        if ($clone_navigation) {
            $navigations = StoreNavigation::query()->where('store_id', $store->id)->get();
            if ($navigations->isNotEmpty()) {
                $navigations->each(function (StoreNavigation $navigation) use ($newStore) {
                    $data_insert = $navigation->replicate()->fill([
                        'store_id' => $newStore->id
                    ])->toArray();
                    unset($data_insert['child_menu']);
                    StoreNavigation::query()->insert($data_insert);
                });
            }
        }
        if ($clone_head_tag) {
            $storeHeadTags = StoreHeadTag::query()->where('store_id', $store->id)->get();
            if ($storeHeadTags->isNotEmpty()) {
                $storeHeadTags->each(function (StoreHeadTag $storeHeadTag) use ($newStore) {
                    StoreHeadTag::query()->create($storeHeadTag->replicate()->fill([
                        'store_id' => $newStore->id
                    ])->toArray());
                });
            }
        }
        if ($clone_collection) {
            $countCollection = StoreCollection::query()->where('store_id', $store->id)->count();
            if ($countCollection > 1000) {
                dispatch(function () use ($store, $newStore) {
                    $storeCollections = StoreCollection::query()->where('store_id', $store->id)->get();
                    $storeCollections->chunk(500)->each(function ($storeCollections) use ($newStore) {
                        $dataInsert = [];
                        $storeCollections->map(function (StoreCollection $storeCollection) use ($newStore, &$dataInsert) {
                            $dataInsert[] = $storeCollection->replicate()->fill([
                                'store_id' => $newStore->id
                            ])->toArray();
                        });
                        StoreCollection::query()->insert($dataInsert);
                    });
                });
            } else if ($countCollection > 0) {
                $dataInsert = [];
                $storeCollections = StoreCollection::query()->where('store_id', $store->id)->get();
                $storeCollections->map(function (StoreCollection $storeCollection) use ($newStore, &$dataInsert) {
                    $dataInsert[] = $storeCollection->replicate()->fill([
                        'store_id' => $newStore->id
                    ])->toArray();
                });
                StoreCollection::query()->insert($dataInsert);
            }
        }
        if ($clone_products) {
            $countProduct = StoreProduct::query()->where('store_id', $store->id)->count();
            if ($countProduct > 1000) {
                dispatch(function () use ($store, $newStore) {
                    $storeProducts = StoreProduct::query()->where('store_id', $store->id)->get();
                    $storeProducts->chunk(500)->each(function ($storeProducts) use ($newStore) {
                        $dataInsert = [];
                        $storeProducts->map(function (StoreProduct $storeProduct) use ($newStore, &$dataInsert) {
                            $dataInsert[] = $storeProduct->replicate()->fill([
                                'store_id' => $newStore->id
                            ])->toArray();
                        });
                        StoreProduct::query()->insert($dataInsert);
                    });
                });
            } else if ($countProduct > 0) {
                $dataInsert = [];
                $storeProducts = StoreProduct::query()->where('store_id', $store->id)->get();
                $storeProducts->map(function (StoreProduct $storeProduct) use ($newStore, &$dataInsert) {
                    $dataInsert[] = $storeProduct->replicate()->fill([
                        'store_id' => $newStore->id
                    ])->toArray();
                });
                StoreProduct::query()->insert($dataInsert);
            }
        }
        if (!$buy_domain) {
            $storeDomain = StoreDomain::query()->create([
                'seller_id' => $userId,
                'store_id' => $newStore->id,
                'domain' => $domain,
                'status' => StoreDomainStatusEnum::PENDING
            ]);
            if ($storeDomain->wasRecentlyCreated) {
                try {
                    $domainManager = DomainClient::instance();
                    if ($domainManager && $domainManager->register($domain)) {
                        StoreService::setDefaultStoreDomain($storeDomain->id, $newStore->id);
                        $result = (new CloudFlareCustomHostname())->addCustomHostname($domain);
                        $customHostnameId = data_get($result, 'id');
                        if ($customHostnameId) {
                            $storeDomain->update([
                                'cloudflare_custom_hostname_id' => $customHostnameId
                            ]);
                        }
                        return $this->successResponse();
                    }
                    return $this->errorResponse('Cannot register domain');
                } catch (ResponseException $e) {
                    logException($e);
                    return $this->errorResponse('Cannot add DNS domain to Cloudflare');
                } catch (Exception $e) {
                    logException($e);
                    return $this->errorResponse('Cannot verify domain');
                }
            }
            return $this->successResponse();
        }
        try {
            $result = $nameDotCom->createDomain($domain, 1, false, false);
            if ($result === false || !data_get($result, 'totalPaid')) {
                return $this->errorResponse("Cannot buy domain");
            }
            $userInfo->updateBalance(-$domainPrice, SellerBillingType::FEE, 'Buy domain name: ' . $domain);
            $renewalPrice = data_get($result, 'domain.renewalPrice', 0);
            SellerDomain::query()->create([
                'seller_id' => $userId,
                'store_id' => $newStore->id,
                'domain' => $domain,
                'auto_renew' => 0,
                'whois_privacy' => 0,
                'renewal_price' => $renewalPrice,
                'next_charge_at' => now()->addYear(),
            ]);

            $storeDomain = StoreDomain::query()->create([
                'seller_id' => $userId,
                'store_id' => $newStore->id,
                'domain' => $domain,
                'status' => StoreDomainStatusEnum::PENDING
            ]);
            if ($storeDomain->wasRecentlyCreated) {
                try {
                    $isCustomStore = $userInfo->custom_payment && $store->is_proxy;
                    ProcessNewDomainJob::dispatchSync($domain, $isCustomStore, $userId, forceDefault: true);
                    return $this->successResponse();
                } catch (ResponseException $e) {
                    logException($e);
                    return $this->errorResponse('Cannot add DNS domain to Cloudflare');
                } catch (Exception $e) {
                    logException($e);
                    return $this->errorResponse('Cannot verify domain');
                }
            }
            return $this->errorResponse('Cannot create store domain');
        } catch (Exception $e) {
            logException($e);
            return $this->errorResponse('Can not buy domain');
        }
    }

    public function updateWooCommerceWebhook(UpdateWooCommerceWebhookRequest $request, $storeId): JsonResponse
    {
        try {
            $webhookUrl = $request->input('webhook_url');

            DB::beginTransaction();

            // Update webhook
            Store::query()
                ->where('seller_id', currentUser()->getUserId())
                ->where('id', $storeId)
                ->update([
                    'woocommerce_webhook_url' => $webhookUrl
                ]);

            StoreConnection::query()->updateOrCreate(
                [
                    'store_id' => $storeId,
                    'platform' => StoreConnectionPlatformEnum::WOOCOMMERCE
                ],
                [
                    'connection_data' => [
                        'webhook_url' => $webhookUrl
                    ],
                    'status' => 'active'
                ]
            );

            DB::commit();
            clearStoreCache($storeId);
            return $this->successResponse();
        } catch (Exception $exception) {
            DB::rollBack();
            logException($exception);
            return $this->errorResponse();
        }
    }

    public function getAllStoreCrispStateFromSystemConfig()
    {
        $config = SystemConfig::getCustomConfig(SystemConfigCrispKeyEnum::KEY, false);

        if ($config) {
            return $this->successResponse(intval($config->value));
        }

        return $this->errorResponse(SystemConfigCrispKeyEnum::KEY . ' is not set in system config');
    }

    public function updateAllStoreCrispStateToSystemConfig(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'enable' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }

        $config = SystemConfig::getCustomConfig(SystemConfigCrispKeyEnum::KEY);

        if (!$config) {
            return $this->errorResponse();
        }

        SystemConfig::setConfig(SystemConfigCrispKeyEnum::KEY, ['value' => $request->enable]);

        syncClearCache(CacheKeys::CRISP_ENABLED);

        return $this->successResponse();
    }
}

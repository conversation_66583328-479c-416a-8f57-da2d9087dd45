<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\CurrencyEnum;
use App\Enums\OrderSummaryPositionEnums;
use App\Enums\PersonalizedType;
use App\Enums\PricingModeEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\PromotionRuleViewTypeEnums;
use App\Enums\PromotionTypeEnum;
use App\Http\Requests\Promotion\UpdateByStoreIdRequest;
use App\Http\Requests\Seller\Promotion\IndexRequest as SellerIndexRequest;
use App\Http\Requests\Seller\Promotion\StoreRequest;
use App\Http\Requests\Storefront\Promotion\IndexBundleDiscountRequest;
use App\Http\Requests\Storefront\Promotion\IndexRequest as StorefrontIndexRequest;
use App\Models\Campaign;
use App\Models\Collection;
use App\Models\Elastic;
use App\Models\Product;
use App\Models\ProductCollection;
use App\Models\ProductPromotion;
use App\Models\ProductVariant;
use App\Models\PromotionRule;
use App\Models\Store;
use App\Models\Template;
use App\Services\StoreService;
use App\Services\UserService;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use App\Traits\Product as ProductTrait;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Throwable;

class PromotionController extends Controller
{
    use ApiResponse, ElasticClient, ProductTrait;

    /**
     * Display a listing of the resource.
     *
     * @param SellerIndexRequest $request
     * @return LengthAwarePaginator
     */
    public function index(SellerIndexRequest $request): LengthAwarePaginator
    {
        $publicStatus = $request->get('public_status', '');
        $storeId = $request->get('store_id');
        $perPage = $request->get('per_page', 15);
        $sortBy = $request->get('sort');
        $sortDirection = $request->get('direction', 'desc');
        $user = currentUser();
        $query = PromotionRule::query()
            ->with('store:id,name')
            ->where([
                'seller_id' => currentUser()->getUserId(),
                'campaign_id' => null // exclude promotions for campaign
            ])
            ->when($sortBy, function ($query) use ($sortBy, $sortDirection) {
                return $query->orderBy($sortBy, $sortDirection);
            });

        if ($publicStatus !== '') {
            $query->where('public_status', $request->get('public_status'));
        }

        if (!empty($storeId)) {
            $query->where('store_id', $storeId);
        }

        if ($user->isSeller()) {
            $storeIds = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());

            if (!empty($storeIds)) {
                $query->whereIn('store_id', $storeIds);
            }
        }

        return $query->orderByDesc('created_at')
            ->paginate($perPage);
    }

    public function getPromotionProducts(Request $request): JsonResponse
    {
        try {
            $keyword = $request->get('q');
            $categoryId = $request->get('category_id');
            $onPromotionCollection = ProductPromotion::query()
                ->select(['product_id', 'start_time', 'end_time', 'point'])
                ->when($categoryId, function ($query, $categoryId) {
                    $query->whereHas('product.categories', function ($query) use ($categoryId) {
                        $query->where('category_id', $categoryId);
                    });
                })
                ->when($keyword, function ($query) use ($keyword) {
                    $query->whereHas('product', function ($query) use ($keyword) {
                       $query->where('name', 'like', '%' . $keyword . '%');
                       $query->orWhere('sku', 'like', '%' . $keyword . '%');
                    });
                })
                ->where('end_time', '>=', now())
                ->get();

            if (empty($onPromotionCollection)) {
                return $this->successResponse([]);
            }

            $onPromotionProductIds = $onPromotionCollection->pluck('product_id')->toArray();
            $onPromotionProducts = $onPromotionCollection->toArray();
            $limit = count($onPromotionProductIds);
            $templateInfos = (new Elastic())->getProductsByCustomConditions(Elastic::ARR_FIELD_LISTING_PRODUCT, ['id' => $onPromotionProductIds], $limit);

            $promotionMap = [];
            foreach ($onPromotionProducts as $promo) {
                $promotionMap[$promo['product_id']] = [
                    'promotion_start_time' => $promo['start_time'],
                    'promotion_end_time' => $promo['end_time'],
                    'promotion_point' => $promo['point']
                ];
            }

            foreach ($templateInfos as &$templateInfo) {
                if (isset($promotionMap[$templateInfo['id']])) {
                    $templateInfo = array_merge($templateInfo, $promotionMap[$templateInfo['id']]);
                }
            }

            if (empty($templateInfos)) {
                return $this->successResponse([]);
            }

            return $this->successResponse($templateInfos);
        } catch (\Exception $e) {
            logException($e, 'PromotionController@getPromotionProducts');
            return $this->errorResponse([]);
        }
    }

    public function couponIndex(Request $request): JsonResponse
    {
        $q = $request->get('q');
        $query = PromotionRule::query()
            ->select(['id', 'discount_code', 'seller_id', 'campaign_id', 'created_at'])
            ->where([
                'seller_id' => currentUser()->getUserId(),
                'campaign_id' => null // exclude promotions for campaign
            ]);

        if ($q) {
            $query->where('discount_code', 'LIKE', '%' . $q . '%');
        }

        $results = $query->orderByDesc('created_at')
            ->limit(15)
            ->get();

        return $results->count() > 0
            ? $this->successResponse($results)
            : $this->errorResponse();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param StoreRequest $request
     * @return JsonResponse
     * @throws \JsonException
     */
    public function store(StoreRequest $request): JsonResponse
    {
        $isEditing = $request->has(['is_edit', 'target_promotion_id']);
        $promotionId = $request->post('target_promotion_id');
        $user = currentUser();
        $userId = currentUser()->getUserId();
        $endTime = $request->post('end_time');
        if ($isEditing) {
            $promotion = PromotionRule::query()->select('used_count')->firstWhere('id', $promotionId);
            if (is_null($promotion)) {
                return $this->errorResponse('Promotion not found.');
            }

            if ($promotion->used_count > 0) {
                if ($endTime && Carbon::parse($endTime)->gt(now())) {
                    $promotion->end_time = $endTime;
                    if ($promotion->isDirty('end_time') && $promotion->save()) {
                        return $this->successResponse();
                    }
                }
                return $this->errorResponse('Cannot edit promotion because it is already used.');
            }
        }

        $type = strtoupper($request->post('type'));
        $data = [
            'seller_id' => $userId,
            'type' => $type,
            'public_status' => $request->post('public_status') ? 1 : 0,
        ];

        $discountCode = $request->post('discount_code');
        $startTime = $request->post('start_time');
        $endTime = $request->post('end_time');

        $data['discount_code'] = strtoupper($discountCode);

        if ($startTime || $isEditing) {
            $data['start_time'] = $startTime;
        }

        if ($endTime || $isEditing) {
            $data['end_time'] = $endTime;
        }

        // create JSON object
        $rules = [];
        $rules['type'] = $type;
        $requirement = $request->post('minimum_requirement');

        if (self::checkMinimumRequirement($requirement, $type)) {
            $value = (int)$request->post('minimum_requirement_value');

            if ($requirement === 'amount') {
                $rules['minimum_amount'] = $value;
            } elseif ($requirement === 'quantity') {
                $rules['minimum_quantity'] = $value;
            }
        }

        $appliesTo = $request->post('applies_to');

        // set all to null (on updating)
        $data['collection_id'] = null;
        $data['store_id'] = null;
        $data['campaign_id'] = null;

        if (self::checkAppliesTo($appliesTo, $type)) {
            $appliesToValue = (int)$request->post('applies_to_value');

            if (($appliesTo === 'campaign') && !Campaign::query()
                    ->onSellerConnection($user)
                    ->where([
                        'seller_id' => $userId,
                        'id' => $appliesToValue
                    ])
                    ->when($type === PromotionTypeEnum::BUNDLE_DISCOUNT, function ($q) {
                        $q->where('system_type', '!=', ProductSystemTypeEnum::COMBO);
                    })
                    ->exists()) {
                return $this->errorResponse('Campaign not found.');
            }

            $key = [
                'campaign' => 'campaign_id',
                'collection' => 'collection_id',
                'store' => 'store_id'
            ][$appliesTo] ?? '';

            $data[$key] = $appliesToValue;
        }

        switch ($type) {
            case PromotionTypeEnum::PERCENT_DISCOUNT:
                $rules['discount_percentage'] = (int)$request->post('discount_percentage');
                break;

            case PromotionTypeEnum::FIXED_DISCOUNT:
                $rules['discount_amount'] = (int)$request->post('discount_amount');
                break;

            case PromotionTypeEnum::FREE_SHIPPING:
                $rules['countries'] = $request->post('countries');
                $rules['fs_percent'] = $request->post('fs_percent', '');
                $rules['fs_max_amount'] = $request->post('fs_max_amount', '');
                break;

            case PromotionTypeEnum::BUY_X_GET_Y:
                if ($requirement !== 'none') {
                    $value = (int)$request->post('minimum_requirement_value');

                    if ($requirement === 'amount') {
                        $rules['buy']['minimum_amount'] = $value;
                    } elseif ($requirement === 'quantity') {
                        $rules['buy']['minimum_quantity'] = $value;
                    }
                }

                $rules['get'] = [
                    'quantity' => (int)$request->post('bxgy_get_quantity'),
                    'discount_percentage' => (int)$request->post('bxgy_get_discount_percentage')
                ];

                $key = $request->post('bxgy_get_applies_to');

                if ($key && $key !== 'default') {
                    // store_id
                    $rules['get'][$key . '_id'] = (int)$request->post('bxgy_get_applies_to_value');
                }
                break;

            case PromotionTypeEnum::BUNDLE_DISCOUNT:
                if (!empty($request->post('bd_get_discount_percentage'))) {
                    $rules['get']['discount_percentage'] = (int)$request->post('bd_get_discount_percentage');
                }

                $key = $request->post('bd_get_applies_to');
                if ($key === 'default') {
                    $key = 'same_collection';
                }
                $bdGetAppliesToValue = (int)$request->post('bd_get_applies_to_value');
                if (!in_array($key, ['same_collection', 'same_campaign'])) {
                    $rules['get'][$key . '_id'] = $bdGetAppliesToValue;
                } else {
                    $rules['get'][$key] = true;
                }

                $key = $request->post('bd_get_templates_to_offer');

                if ($key && $key !== 'default') {
                    // ex: 123,456 => [123,456]
                    $string = $request->post('bd_get_templates_to_offer_value');
                    $arr = explode(',', $string);

                    if (count($arr) > 0) {
                        foreach ($arr as $value) {
                            $rules['get'][$key . '_ids'][] = (int)$value;
                        }
                    }
                }
                $rules['get']['order_by'] = $request->post('bd_order_by');
                $bundleQuantity = $request->post('bd_quantity');
                $rules['get']['quantity'] = $bundleQuantity;
                $numberProductCartBd = $request->post('number_cart_bundle_product_limit');
                if (!empty($numberProductCartBd)) {
                    if ($numberProductCartBd < $bundleQuantity) {
                        return $this->errorResponse('Limit number products have to be greater than number of product to offer');
                    }
                    $rules['get']['number_cart_bundle_product_limit'] = $numberProductCartBd;
                }
                $data['promotion_position'] = $request->post('bd_promotion_position');
                break;

            case PromotionTypeEnum::TIERS_DISCOUNT:
                $tiers = $request->post('tiers');
                $result = [];

                foreach ($tiers as $tier) {
                    $result[] = [
                        'qty' => (int)$tier['quantity'],
                        'discount' => (int)$tier['discount']
                    ];
                }

                $rules['tiers'] = $result;
                unset($tiers);
                break;
        }

        $data['rules'] = json_encode($rules, JSON_THROW_ON_ERROR);


        try {
            // if it is editing, update instead of create new promotion
            if ($isEditing) {
                PromotionRule::query()
                    ->where([
                        'id' => $promotionId,
                        'seller_id' => $userId
                    ])
                    ->update($data);

                return $this->successResponse();
            }
            // create a new one
            $id = PromotionRule::query()->insertGetId($data);

            return $this->successResponse(
                [
                    'id' => $id,
                    'type' => $type,
                ]
            );
        } catch (Throwable $e) {
            graylogError('Error in store new promotion', [
                'category' => 'store_promotions_error',
                'detail' => $e
            ]);
            return $this->errorResponse();
        }
    }

    public static function checkMinimumRequirement($requirement, $type): bool
    {
        return $requirement !== 'none'
            && in_array($type, [
                PromotionTypeEnum::PERCENT_DISCOUNT,
                PromotionTypeEnum::FIXED_DISCOUNT,
                PromotionTypeEnum::FREE_SHIPPING,
            ]);
    }

    public static function checkAppliesTo($appliesTo, $type): bool
    {
        $whitelist = [
            PromotionTypeEnum::PERCENT_DISCOUNT,
            PromotionTypeEnum::FIXED_DISCOUNT,
            PromotionTypeEnum::TIERS_DISCOUNT,
            PromotionTypeEnum::BUY_X_GET_Y,
            PromotionTypeEnum::BUNDLE_DISCOUNT,
            PromotionTypeEnum::FREE_SHIPPING,
        ];

        return $appliesTo && $appliesTo !== 'default'
            && in_array($type, $whitelist);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        $updated = PromotionRule::query()
            ->where(
                [
                    'seller_id' => currentUser()->getUserId(),
                    'id' => $id
                ]
            )
            ->update(
                [
                    'status' => $request->post('status') ? 1 : 0,
                    'public_status' => $request->post('public_status') ? 1 : 0,
                ]
            );

        return $updated > 0 ? $this->successResponse() : $this->errorResponse();
    }

    /**
     * Get detail promotion
     * @param int $id
     * @return JsonResponse
     */
    public function detail(int $id): JsonResponse
    {
        $user = currentUser();
        $sellerId = $user->getUserId();
        $promotion = PromotionRule::query()
            ->select('promotion_rule.*', 'collection.name as collection_name', 'store.name as store_name')
            ->leftJoin(
                'collection',
                'collection.id',
                '=',
                'promotion_rule.collection_id'
            )
            ->leftJoin(
                'store',
                'store.id',
                '=',
                'promotion_rule.store_id'
            )
            ->firstWhere([
                'promotion_rule.id' => $id,
                'promotion_rule.seller_id' => $sellerId
            ]);

        if ($promotion) {
            $rules = json_decode($promotion->rules);
            if (!empty($rules->get->store_id)) {
                $promotion->get_store_name = Store::query()
                    ->where('id', $rules->get->store_id)
                    ->value('name');
            }
            if (!empty($rules->get->collection_id)) {
                $promotion->get_collection_name = Collection::query()
                    ->where('id', $rules->get->collection_id)
                    ->value('name');
            }
            if (!empty($rules->get->template_ids)) {
                $promotion->get_templates = Product::query()
                    ->select('id', 'name')
                    ->onSellerConnection($user)
                    ->whereIn('id', $rules->get->template_ids)
                    ->get();
            }
            if (!empty($rules->get->campaign_id)) {
                $promotion->get_campaign_name = Product::query()
                    ->onSellerConnection($user)
                    ->where('id', $rules->get->campaign_id)
                    ->value('name');
            }
            return $this->successResponse($promotion);
        }

        return $this->errorResponse('You do not have permission to access this promotion rule.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return JsonResponse
     * @throws \Exception
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();
            $sellerId = currentUser()->getUserId();
            $promotion = PromotionRule::query()
                ->where('seller_id', $sellerId)
                ->find($id);

            if (!$promotion) {
                return $this->errorResponse('Promotion not found.');
            }

            if ((int)$promotion->used_count > 0) {
                return $this->errorResponse('Promotion already used. Cannot delete.');
            }

            Store::query()
                ->where([
                    'seller_id' => $sellerId,
                    'product_review_coupon' => $promotion->discount_code
                ])
                ->update(['product_review_coupon' => null]);

            $promotion->delete();

            DB::commit();
            return $this->successResponse();
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function ajaxSearch(Request $request, string $type, $dataOnly = false, $limitResults = 15)
    {
        $type = strtolower($type);
        $data = null;
        $query = $request->get('q');
        $user = currentUser();

        switch ($type) {
            case 'campaign':
                $systemType = $request->get('system_type');
                $data = $this->elasticPromotionSearchBySeller(
                    ProductType::CAMPAIGN,
                    $limitResults,
                    withOutCampCombo: $request->boolean('without_combo'),
                ) ?? [];
                $data = collect($data);
                break;

            case 'collection':
                $q = Collection::query()
                    ->select(['id', 'name', 'slug'])
                    ->selectRaw("CASE WHEN name = ? THEN 1 ELSE 0 END AS exact_match", [$query])
                    ->limit($limitResults)
                    ->orderByDesc('exact_match')
                    ->latest('id');
                if ($request->has('ids')) {
                    $q->whereIn('id', $request->get('ids'));
                }

                if (!empty($query)) {
                    $q->where('name', 'like', '%' . $query . '%');
                }
                $filterSameUser = $q->clone();
                $filterSameUser->join('seller_collection', function ($j) use ($user) {
                    $j->on('seller_collection.collection_id', '=', 'collection.id');
                    $j->where('seller_collection.seller_id', $user->getUserId());
                });
                $data = $filterSameUser->get();
                if (empty($query)) {
                    break;
                }

                // get collection system
                $numberNeedMore = $limitResults - $data->count();
                if ($numberNeedMore > 0) {
                    $filterSystem = $q->clone();
                    $filterSystem->where(function ($q) {
                        $q->orWhere('collection.popularity', '>', 2);
                        $q->orWhere('system', 1);
                    });
                    $filterSystem->limit($numberNeedMore);
                    $result = $filterSystem->get();
                    $data = $data->merge($result);
                }
                break;

            case 'store':
                $data = Store::query()
                    ->select('id', 'name')
                    ->where('seller_id', $user->getUserId())
                    ->where('name', 'like', '%' . $query . '%')
                    ->limit($limitResults)
                    ->get();
                break;

            case 'template':
                $data = $this->elasticPromotionSearchBySeller(
                    ProductType::TEMPLATE,
                    $limitResults,
                    get_env('ELATICSEARCH_INDEX', 'products')
                ) ?? [];
                $data = collect($data);
                break;
        }

        if ($dataOnly) {
            return $data;
        }

        return $data->isNotEmpty() ? $this->successResponse($data) : $this->errorResponse();
    }

    public function getByStoreId($storeId): JsonResponse
    {
        $promotions = PromotionRule::query()
            ->select('discount_code')
            ->checkThankYouCode($storeId)
            ->get();
        $existsPromotion = Store::query()
            ->select('discount_code', 'promotion_title')
            ->firstWhere([
                'seller_id' => currentUser()->getUserId(),
                'id' => $storeId
            ]);

        $response['promotions'] = $promotions;
        $response['exists'] = $existsPromotion;

        return $promotions->count() > 0
            ? $this->successResponse($response)
            : $this->errorResponse();
    }

    /**
     * @throws \Exception
     */
    public function updateByStoreId(UpdateByStoreIdRequest $request, $storeId): JsonResponse
    {
        $user = currentUser()->getInfoAccess();
        if (!$user) {
            return $this->errorResponse('User not found');
        }
        $discountCode = $request->post('discount_code');
        $promotionTitle = $request->post('promotion_title');
        $checkoutPhone = $request->post('checkout_phone');
        $autoApplyCoupon = $request->post('auto_apply_coupon');
        $orderPrefix = $request->post('order_prefix');
        $tipping = $request->post('tipping');
        $paypal_gateway_id = $request->post('paypal_gateway_id', 0);
        $stripe_gateway_id = $request->post('stripe_gateway_id', 0);
        $always_show_order_summary = $request->post('always_show_order_summary', 0);
        $enableDynamicPriceOnCheckout = $request->post('enable_dynamic_base_cost', 0);
        $orderSummaryPosition = $request->post('order_summary_position', OrderSummaryPositionEnums::TOP);

        $showTipping = false;
        $defaultTipping = 0;
        if(!is_null($tipping)) {
            $showTipping = true;
            $defaultTipping = $tipping;
        }
        if (empty($user->custom_payment)) {
            $stripe_gateway_id = 0;
            $paypal_gateway_id = 0;
        }
        $updated = Store::query()
            ->whereKey($storeId)
            ->update([
                'discount_code' => $discountCode,
                'promotion_title' => $promotionTitle,
                'checkout_phone' => (bool)$checkoutPhone,
                'auto_apply_coupon' => $autoApplyCoupon,
                'order_prefix' => $orderPrefix,
                'show_tipping' => $showTipping,
                'default_tipping' => $defaultTipping,
                'paypal_gateway_id' => $paypal_gateway_id,
                'stripe_gateway_id' => $stripe_gateway_id,
                'always_show_order_summary' => $always_show_order_summary,
                'enable_dynamic_base_cost' => $enableDynamicPriceOnCheckout,
                'order_summary_position' => $orderSummaryPosition,
            ]);

        if ($updated) {
            clearStoreCache($storeId);
            return $this->successResponse();
        }

        return $this->errorResponse('Update failed');
    }

    public function indexStorefront(StorefrontIndexRequest $request): JsonResponse
    {
        try {
            $campaignIds = [];
            $requestCampaignIds = $request->get('campaign_ids', []);
            if (!empty($requestCampaignIds) && is_array($requestCampaignIds)) {
                $campaignIds = array_merge(...array_map(static fn($id) => str_contains($id, ',') ? explode(',', $id) : [$id], $requestCampaignIds));
            }
            $store = StoreService::getCurrentStoreInfo();

            if (is_null($store)) {
                return $this->errorResponse('Store not found');
            }

            $cache = cacheAlt();

            if ($store->id) {
                $tag = CacheKeys::getStoreId($store->id);
                $cache->tags([$tag]);
            }
            if (count($campaignIds) === 1) {
                $cacheKey = CacheKeys::STOREFRONT_PROMOTION_PREFIX . $campaignIds[0];
                $data = $cache->get($cacheKey);
            }

            if (empty($data)) {
                if (!empty($campaignIds)) {
                    $collectionIds = ProductCollection::query()
                        ->whereIn('product_id', $campaignIds)
                        ->select('collection_id')
                        ->get()
                        ->pluck('collection_id');
                } else {
                    $collectionIds = [];
                }

                $data = PromotionRule::getIndexStorefront($campaignIds, $collectionIds);
                if (isset($cacheKey)) {
                    $cache->put($cacheKey, $data, CacheTime::CACHE_24H);
                }
            }

            $headers = generateHeadersCacheHTMLTagByCampaignIds($campaignIds);

            return $this->successResponse($data)->withHeaders($headers);
        } catch (Throwable $e) {
            return $this->errorResponse();
        }
    }

    public function indexBundleDiscount(IndexBundleDiscountRequest $request): JsonResponse
    {
        $promotionPosition = [PromotionRuleViewTypeEnums::CAMPAIGN_DETAIL_AND_CART];
        $campaignIds = $request->get('campaign_ids');
        $productIds = $request->get('product_ids', []);
        $bundleIds = $request->get('bundle_ids', []);
        $position = $request->get('position', PromotionRuleViewTypeEnums::CAMPAIGN_DETAIL);
        $promotionPosition[] = (int) $position;
        $store = StoreService::getCurrentStoreInfo();
        if (empty($store) || empty($campaignIds)) {
            return $this->errorResponse('Data is invalid');
        }
        try {
            $seller = UserService::getSellerSharding($store->seller_id);
            $campaignIds = Campaign::query()
                ->onSellerConnection($seller)
                ->whereIn('id', $campaignIds)
                ->where('system_type', '!=', ProductSystemTypeEnum::COMBO)
                ->pluck('id')
                ->toArray();
            if (empty($campaignIds)) {
                return $this->errorResponse('Data is invalid');
            }
            $sellerId = $store->seller_id;
            $storeId = $store->id;
            $arrResponse = [];
            $cache = cacheAlt();

            if ($storeId) {
                $tag = CacheKeys::getStoreId($storeId);
                $cache->tags([$tag]);
            }

            if (count($campaignIds) === 1) {
                $productCacheKeyGen = '';
                if (!empty($productIds)) {
                    $productCacheKeyGen = implode('_', (array) $productIds);
                }
                $cacheKey = CacheKeys::BUNDLE_DISCOUNT_PREFIX . $store->id . '_' . $campaignIds[0] . '_' . $productCacheKeyGen . '_' . $position;
                $arrResponse = $cache->get($cacheKey);
            }

            if (!empty($bundleIds)) {
                $bundleIdsArr = explode(',', $bundleIds[0]);
                $bundleId = end($bundleIdsArr);
            } else {
                $bundleId = null;
            }

            if (empty($arrResponse)) {
                $promotion = null;
                if (isset($bundleId)) {
                    $promotion = PromotionRule::query()
                        ->filterBundleDiscount($sellerId, false, true)
                        ->where('id', $bundleId)
                        ->where('type',PromotionTypeEnum::BUNDLE_DISCOUNT)
                        ->first();
                }

                if (is_null($promotion)) {
                    // get by campaign
                    $promotion = PromotionRule::query()
                        ->filterBundleDiscount($sellerId, true, true)
                        ->whereIn('campaign_id', $campaignIds)
                        ->whereNull('collection_id')
                        ->whereNull('store_id')
                        ->whereIn('promotion_position', $promotionPosition)
                        ->latest('id')
                        ->first();
                }
                $collectionIds = ProductCollection::query()
                    ->whereIn('product_id', $campaignIds)
                    ->where('seller_id', $sellerId)
                    ->get('collection_id');
                if (is_null($promotion)) {
                    // get by collection
                    $promotion = PromotionRule::query()
                        ->filterBundleDiscount($sellerId, false, true)
                        ->whereNull('campaign_id')
                        ->whereIn('collection_id',
                            $collectionIds->pluck('collection_id')->toArray())
                        ->where(function ($query) use ($storeId) {
                            $query->whereNull('store_id')
                                ->orWhere('store_id', $storeId);
                        })
                        ->whereIn('promotion_position', $promotionPosition)
                        ->latest('id')
                        ->first();
                }

                if (is_null($promotion)) {
                    $promotion = PromotionRule::query()
                        ->filterBundleDiscount($sellerId, false, true)
                        ->whereNull('campaign_id')
                        ->whereNull('collection_id')
                        ->where('store_id', $storeId)
                        ->whereIn('promotion_position', $promotionPosition)
                        ->latest('id')
                        ->first();
                }


                if (is_null($promotion)) {
                    // get by seller
                    $promotion = PromotionRule::query()
                        ->filterBundleDiscount($sellerId)
                        ->whereNull('campaign_id')
                        ->whereNull('collection_id')
                        ->whereNull('store_id')
                        ->whereIn('promotion_position', $promotionPosition)
                        ->latest('id')
                        ->first();
                }
                if (is_null($promotion)) {
                    return $this->errorResponse();
                }

                $rules = json_decode($promotion->rules);
                // array filter elastic
                $arrFilter = [];

                // exclude params
                $arrFilter['exclude_campaign_ids'] = $campaignIds;
                $excludeIds = [];
                if (!empty($productIds)) {
                    $excludeIds = array_merge($excludeIds, explode(',',$productIds[0]));
                }
                $arrFilter['exclude_ids'] = array_map('intval', $excludeIds);
                $isSameCollection = false;
                $isSameCampaign = false;
                if (!empty($rules?->get?->same_campaign)) {
                    $arrFilter['campaign_ids'] = $campaignIds;
                    // unset exclude
                    $isSameCampaign = true;
                    unset($arrFilter['exclude_campaign_ids']);
                } elseif (!empty($rules->get->campaign_id)) {
                    $arrFilter['campaign_id'] = $rules->get->campaign_id;
                } elseif (!empty($rules->get->collection_id)) {
                    $arrFilter['collection_id'] = $rules->get->collection_id;
                } elseif (!empty($rules->get->same_collection)) {
                    $isSameCollection = true;
                    if (empty($collectionIds)) {
                        $collectionIds = ProductCollection::query()
                            ->whereIn('product_id', $campaignIds)
                            ->where('seller_id', $sellerId)
                            ->get('collection_id');
                    }
                    $arrFilter['collection_ids'] = $collectionIds->pluck('collection_id')->toArray();
                }

                $templateIds = [];
                if (!empty($rules->get->template_ids)) {
                    $templateIds = $rules->get->template_ids;
                }
                $templateIds = array_unique($templateIds);
                if (!empty($templateIds)) {
                    $arrFilter['template_ids'] = array_values($templateIds);
                }
                $excludedTemplateIds = [];
                Template::query()
                    ->where('status', '!=',ProductStatus::ACTIVE)
                    ->pluck('id')
                    ->each(function ($id) use (&$excludedTemplateIds) {
                        $excludedTemplateIds[] = $id;
                    });
                if (!empty($excludedTemplateIds)) {
                    $arrFilter['exclude_template_ids'] = $excludedTemplateIds;
                }

                $sort = $rules->get->order_by;
                $limit = $rules->get->quantity;
                $products = $this->getBundleDiscount($campaignIds, $arrFilter, $sort, $limit, false, $isSameCollection);
                self::mappingCorrectPricing($products);
                $arrResponse['products'] = $products;
                $arrResponse['custom_personalized_options'] = $this->getCustomPersonalizedOptions($products);

                $arrResponse['variants'] = $this->getVariants($arrResponse['products']);
                $numberCartBundleProduct = $rules->get?->number_cart_bundle_product_limit ?? null;
                if (!$numberCartBundleProduct || !isset($numberCartBundleProduct)) {
                    $numberCartBundleProduct = null;
                }
                $arrResponse['promotion'] = [
                    'id' => $promotion->id,
                    'discount_code' => $promotion->discount_code,
                    'discount_percentage' => $rules->get->discount_percentage ?? 0,
                    'number_cart_bundle_product_limit' => isset($numberCartBundleProduct) ? (int) $numberCartBundleProduct : null,
                    'is_same_campaign' => $isSameCampaign
                ];

                if (isset($cacheKey)) {
                    $cache->put($cacheKey, $arrResponse, CacheTime::CACHE_4H);
                }
            }

            $headers = generateHeadersCacheHTMLTagByCampaignIds($campaignIds);
            return $this->successResponse($arrResponse)->withHeaders($headers);
        } catch (Throwable $e) {
            logException($e, 'PromotionController@indexBundleDiscount -> Domain: ' . $request->header('X-Domain') . ' -> CampaignIds: ' . json_encode($campaignIds));
            return $this->errorResponse();
        }
    }

    /**
     * @param array $campaignIds
     * @param $arrFilter
     * @param $sort
     * @param $limit
     * @param bool $wasRetry
     * @param bool $isSameCollection
     * @return array
     * @throws Throwable
     */
    protected function getBundleDiscount(array $campaignIds, $arrFilter, $sort, $limit, bool $wasRetry = false, bool $isSameCollection = false)
    {
        try {
            //get bundle discount by last element
            $products = $this->elasticGetProductByBundleDiscount([$campaignIds[count($campaignIds) - 1]], $arrFilter, $sort, $limit * 5, false, $isSameCollection);
            $variants = $this->getVariantsAndValidateOutOfStockForBundle($products);
            $productIdsChecked = [];
            $productIdsInStock = [];
            foreach ($products as $index => $product) {
                $productDefaultOption = Arr::get($product, 'default_option');
                $productInStock = false;
                if (!isset($productDefaultOption)) {
                    $productInStock = true;
                }
                $inStockVariants = array_filter($variants, static function ($q) use ($product){
                    return (int) $q['out_of_stock'] === 0 &&
                        str_contains($q['variant_key'], $product['default_option']) &&
                        (int) $q['product_id'] === (int) $product['id'];
                });


                if (!empty($inStockVariants)) {
                    $productInStock = true;
                }

                if ($productInStock) {
                    $productIdsInStock[] = $product['id'];
                } else {
                    unset($products[$index]);
                }
                $productIdsChecked[] = $product['id'];
            }

            $productIdsChecked = array_unique($productIdsChecked);
            if (count($productIdsInStock) < $limit) {
                $productsExcludeIds = Arr::get($arrFilter, 'exclude_ids');
                $productsExcludeIds = !isset($productsExcludeIds) ? $productIdsChecked : array_merge($productsExcludeIds, $productIdsChecked);
                $productsExcludeIds = array_unique($productsExcludeIds);
                $arrFilter['exclude_ids'] = $productsExcludeIds;
                $queryLimit = $limit - count($productIdsInStock);
                $products = array_merge($products, $this->elasticGetProductByBundleDiscount([$campaignIds[count($campaignIds) - 1]], $arrFilter, $sort, $queryLimit, false, $isSameCollection));
            } else {
                $products = array_slice($products, 0, $limit);
            }
            if (!empty($products)) {
                foreach ($products as &$product) {
                    $product['custom_options'] ??= null;
                    $product['common_options'] ??= null;
                    $product['template_custom_options'] ??= null;
                    if (!empty($product['template_id']) && $product['full_printed'] === ProductPrintType::HANDMADE) {
                        $product_template = Product::query()->whereKey($product['template_id'])->first();
                        if ($product_template) {
                            $templateOptions = json_decode($product_template->options, false, 512, JSON_THROW_ON_ERROR);
                            if (!empty($templateOptions->custom_options)) {
                                $product['custom_options'] = $templateOptions->custom_options;
                                $product['template_custom_options'] = $templateOptions->custom_options;
                                unset($templateOptions->custom_options);
                            }
                            if (!empty($templateOptions->common_options)) {
                                $product['common_options'] = $templateOptions->common_options;
                                unset($templateOptions->common_options);
                            }
                            $product['options'] = json_encode($templateOptions, JSON_THROW_ON_ERROR);
                        }
                    }
                }
            }

            return $products;
        } catch (Throwable $e) {
            throw $e;
        }
    }

    private function getVariants($products, $isValidatingOutOfStock = false): array
    {
        if (empty($products)) {
            return [];
        }
        $user = currentUser();
        $variantProductIds = [];
        $marketLocation = null;
        foreach ($products as &$product) {
            $variantProductIds[] = $product['template_id'];
            $product['pricing_mode'] ??= PricingModeEnum::ADJUST_PRICE;
            if ($product['pricing_mode'] === PricingModeEnum::CUSTOM_PRICE) {
                $variantProductIds[] = $product['id'];
            }
            $marketLocation ??= Arr::get($product, 'market_location');
        }

        unset($product);
        $marketLocation ??= '*';
        $marketLocations = [$marketLocation];
        if ($isValidatingOutOfStock) {
            $marketLocation = getLocationByCode($marketLocation);
            $marketLocations = $marketLocation->getRegionCodes();
        }

        $queryVariant = ProductVariant::query()
            ->onSellerConnection($user)
            ->select([
                'product_id',
                'variant_key',
                'out_of_stock',
                'adjust_price',
                'price',
                'old_price'
            ])
            ->whereIn('product_id', $variantProductIds)
            ->whereIn('location_code', $marketLocations);
        $q1 = $queryVariant->clone()
            ->where('out_of_stock', 1);

        $q2 = $queryVariant->clone()
            ->where('adjust_price', '>', 0);

        $q3 = $queryVariant->clone()
            ->where('campaign_id', '>', 0);

        $variants = $q1
            ->unionAll($q2)
            ->unionAll($q3)
            ->when($isValidatingOutOfStock, function ($q) use ($queryVariant) {
                $q->unionAll($queryVariant->clone()->where('adjust_price', 0));
            })
            ->get();
        $productVariants = [];
        foreach ($products as $product) {
            $currencyRate = 1;

            if ($product['currency_code'] !== CurrencyEnum::USD) {
                $currency = SystemConfigController::findOrDefaultCurrency($product['currency_code']);

                if ($currency) {
                    $currencyRate = $currency->rate;
                }
            }
            if ($isValidatingOutOfStock) {
                sortArrayOptions($product['options']);
            }
            $variantKeys = generateVariantKeysByProductOptions($product['options']);
            foreach ($variants as $key => $variant) {
                if ($variant->product_id === $product['template_id']) {
                    if (in_array($variant->variant_key, $variantKeys)) {
                        $variant->adjust_price = ceil($variant->adjust_price * $currencyRate);

                        if ($product['pricing_mode'] === PricingModeEnum::FIXED_PRICE) {
                            $variant->price = $product['price'];
                            $variant->old_price = $product['old_price'];
                        } elseif ($product['pricing_mode'] === PricingModeEnum::ADJUST_PRICE) {
                            $variant->price = round($product['price'] + $variant->adjust_price, 2);
                            $variant->old_price = $product['old_price'] > 0 ? calculateCompareAtPrice($variant->price) : 0;
                        } elseif ($product['pricing_mode'] === PricingModeEnum::CUSTOM_PRICE) {
                            $variant->price *= $currencyRate;
                            foreach ($variants as $key2 => $variant2) {
                                if ($variant2->product_id === $product['id'] && $variant2->variant_key === $variant->variant_key) {
                                    $variant->price = $variant2->price;
                                    $variants->forget($key2);
                                    break;
                                }
                            }
                            $variant->old_price = $product['old_price'] > 0 ? calculateCompareAtPrice($variant->price) : 0;
                        }
                        $variant->product_id = $product['id'];
                        $productVariants[] = $variant->toArray();
                    }
                    $variants->forget($key);
                }
            }

            if ($product['pricing_mode'] === PricingModeEnum::CUSTOM_PRICE) {
                foreach ($variants as $key => $variant) {
                    if ($variant->product_id === $product['id']) {
                        $productVariants[] = $variant->toArray();
                        $variants->forget($key);
                    }
                }
            }
        }

        return $productVariants;
    }

    /**
     * @param $products
     * @return array
     */
    private function getCustomPersonalizedOptions($products): array
    {
        $user = currentUser();
        $campaignIds = [];
        foreach ($products as $product) {
            if (Arr::get($product, 'personalized') === PersonalizedType::CUSTOM_OPTION) {
                $campaignIds[] = $product['campaign_id'];
            }
        }

        return Campaign::query()
            ->onSellerConnection($user)
            ->select([
                'id',
                'options',
            ])
            ->whereIn('id', $campaignIds)
            ->get()
            ->map(function ($camp) {
                $options = Str::isJson($camp->options) ? json_decode($camp->options, false, 512, JSON_THROW_ON_ERROR) : [];
                if (!empty($options)) {
                    if (!empty($options->common_options)) {
                        $camp->common_options = json_encode($options->common_options, JSON_THROW_ON_ERROR);
                        unset($options->common_options);
                    }
                    if (!empty($options->options)) {
                        if (isset($options->options->text) || isset($options->options->dropdown) || isset($options->options->image)) {
                            $newOptions = [];

                            foreach ($options->options as $kOption => $option) {
                                if ($option->active) {
                                    $option->type = $kOption;
                                    $newOptions[] = $option;
                                }
                            }
                            $options->options = $newOptions;
                            $camp->options = json_encode($options, JSON_THROW_ON_ERROR);
                        }
                    }
                }
                return $camp;
            })
            ->toArray();
    }

    private function getVariantsAndValidateOutOfStockForBundle($products): array
    {
        if (empty($products)) {
            return [];
        }
        $user = currentUser();
        $variantProductIds = [];
        $marketLocation = null;
        foreach ($products as &$product) {
            $variantProductIds[] = $product['template_id'];
            $product['pricing_mode'] ??= PricingModeEnum::ADJUST_PRICE;
            if ($product['pricing_mode'] === PricingModeEnum::CUSTOM_PRICE) {
                $variantProductIds[] = $product['id'];
            }
            $marketLocation ??= Arr::get($product, 'market_location');
        }

        unset($product);
        $marketLocation ??= '*';
        $marketLocations = [$marketLocation];
        $marketLocation = getLocationByCode($marketLocation);
        $marketLocations = $marketLocation->getRegionCodes();

        $queryVariant = ProductVariant::query()
            ->onSellerConnection($user)
            ->select([
                'product_id',
                'variant_key',
                'out_of_stock',
                'adjust_price',
                'price',
                'old_price'
            ])
            ->whereIn('product_id', $variantProductIds)
            ->whereIn('location_code', $marketLocations);
        $q1 = $queryVariant->clone()
            ->where('out_of_stock', 1);

        $q2 = $queryVariant->clone()
            ->where('adjust_price', '>', 0);

        $q3 = $queryVariant->clone()
            ->where('campaign_id', '>', 0);

        $variants = $q1
            ->unionAll($q2)
            ->unionAll($q3)
            ->unionAll($queryVariant->clone()->where('adjust_price', 0))
            ->get();

        $productVariants = [];
        foreach ($products as $product) {
            $currencyRate = 1;

            if ($product['currency_code'] !== CurrencyEnum::USD) {
                $currency = SystemConfigController::findOrDefaultCurrency($product['currency_code']);

                if ($currency) {
                    $currencyRate = $currency->rate;
                }
            }
            sortArrayOptions($product['options']);

            $variantKeys = generateVariantKeysByProductOptions($product['options']);
            foreach ($variants as $key => $variant) {
                if ($variant->product_id === $product['template_id']) {
                    if (in_array($variant->variant_key, $variantKeys)) {
                        $variant->adjust_price = ceil($variant->adjeust_price * $currencyRate);

                        if ($product['pricing_mode'] === PricingModeEnum::FIXED_PRICE) {
                            $variant->price = $product['price'];
                            $variant->old_price = $product['old_price'];
                        } elseif ($product['pricing_mode'] === PricingModeEnum::ADJUST_PRICE) {
                            $variant->price = round($product['price'] + $variant->adjust_price, 2);
                            $variant->old_price = $product['old_price'] > 0 ? calculateCompareAtPrice($variant->price) : 0;
                        } elseif ($product['pricing_mode'] === PricingModeEnum::CUSTOM_PRICE) {
                            $variant->price *= $currencyRate;
                            foreach ($variants as $key2 => $variant2) {
                                if ($variant2->product_id === $product['id'] && $variant2->variant_key === $variant->variant_key) {
                                    $variant->price = $variant2->price;
                                    break;
                                }
                            }
                            $variant->old_price = $product['old_price'] > 0 ? calculateCompareAtPrice($variant->price) : 0;
                        }
                        $productVariantInserts = $variant->toArray();
                        $productVariantInserts['product_id'] = $product['id'];
                        $productVariants[] = $productVariantInserts;
                    }
                }
            }

            if ($product['pricing_mode'] === PricingModeEnum::CUSTOM_PRICE) {
                foreach ($variants as $key => $variant) {
                    if ($variant->product_id === $product['id']) {
                        $productVariants[] = $variant->toArray();
                    }
                }
            }
        }

        return $productVariants;
    }
}

<?php

namespace App\Http\Controllers;

use App\Enums\AddressEnum;
use App\Enums\CacheKeys;
use App\Enums\DateRangeEnum;
use App\Enums\DesignByEnum;
use App\Enums\FbaFulfillBy;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\ShippingMethodEnum;
use App\Exports\ExportBaseCostCatalog;
use App\Exports\ExportProductCatalog;
use App\Exports\ExportShippingCostCatalog;
use App\Http\Requests\SaveNewFBAOrderRequest;
use App\Http\Requests\SaveNewFulfillOrderRequest;
use App\Http\Requests\UpdateFBAOrderRequest;
use App\Http\Requests\UpdateFulfillOrderAddressRequest;
use App\Jobs\CheckOrderAtRisk;
use App\Jobs\ValidateSellerFulfillOrder;
use App\Jobs\ExtractOrderLabelContent;
use App\Models\File;
use App\Models\FulfillMapping;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShippingRule;
use App\Models\Template;
use App\Models\User;
use App\Rules\ValidFilePath;
use App\Services\FulfillmentService;
use App\Services\OrderService;
use App\Services\UserLog;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\TiktokShop\Events\ValidateOrderEvent;
use Modules\TiktokShop\Services\SyncOrderService;
use Modules\TiktokShop\Services\TiktokShopService;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class FulfillOrderController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */

    protected $orderService;
    public function __construct(OrderService $orderService)
    {
        $this->orderService = $orderService;
    }
    public function index(Request $request): JsonResponse
    {
        $auth = currentUser();
        $user = $auth->getInfo();
        $query = Order::query();
        $keywords = $request->get('q');
        $status = $request->get('status');
        $type = $request->get('type', OrderTypeEnum::FULFILLMENT);
        $fulfillStatus = $request->get('fulfill_status');
        $dateRange = $request->get('time');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $sortBy = $request->get('sort');
        $sortDirection = $request->get('direction');
        $sellerId = $request->query('seller_id');
        $sortableFields = ['total_amount', 'total_quantity', 'updated_at', 'paid_at', 'exported_at', 'id', 'created_at'];
        $query->select(
            [
                'order.seller_id',
                'order.customer_id',
                'order.customer_email',
                'order.customer_name',
                'order.customer_phone',
                'order.fulfill_status',
                'order.id',
                'order.order_number',
                'order.order_number_2',
                'order.paid_at',
                'order.status',
                'order.total_amount',
                'order.total_quantity',
                'order.total_seller_profit',
                'order.created_at',
                'order.updated_at',
                'order.country',
                'order.transaction_id',
                'order.personalized',
                'order.address_verified',
                'order.fulfill_log',
                'order.order_note',
                'order.type',
                'order.shipping_label',
                'order.cross_shipping',
            ]
        );
        if ($auth->isAdmin()) {
            $query->with(['seller', 'customer']);
            $query->addSelect(['order.admin_note', 'order.address_verified']);

            if ($sellerId) {
                $query->where('order.seller_id', $sellerId);
            }
        } else {
            $query->where('order.seller_id', $auth->getUserId())
                ->with(['customer'])
                ->with(['order_products' => static function ($q) use ($auth) {
                    $q->select(
                        [
                            'id',
                            'order_id',
                            'campaign_title',
                            'thumb_url',
                            'product_name',
                            'sku',
                            'options',
                            'custom_options',
                            'color',
                            'product_id',
                            'full_printed',
                            'tracking_code',
                            'tracking_url',
                            'shipping_carrier',
                            'tracking_service',
                            'cross_shipping',
                        ]
                    )
                        ->where('seller_id', $auth->getUserId())
                        ->orderBy('id');
                }]);
        }

        if (!empty($keywords)) {
            if ($auth->isAdmin()) {
                $query->join('user', 'user.id', '=', 'order.seller_id');
            }
            $query->where(
                function ($q) use ($keywords, $auth) {
                    if ($auth->isAdmin()) {
                        $q->orWhere('user.name', 'like', '%' . $keywords . '%')
                            ->orWhere('user.email', 'like', '%' . $keywords . '%');
                    } else {
                        $q->whereHas(
                            'order_products',
                            function ($q) use ($keywords) {
                                return $q->where('campaign_title', 'like', '%' . $keywords . '%');
                            }
                        );
                    }

                    return $q->orWhere('order.order_number', 'like', '%' . $keywords . '%')
                        ->orWhere('order.order_number_2', 'like', '%' . $keywords . '%')
                        ->orWhere('order.customer_name', 'like', '%' . $keywords . '%')
                        ->orWhere('order.customer_email', 'like', '%' . $keywords . '%');
                }
            );
        }

        $excludeStatus = [
            OrderStatus::DELETED
        ];

        $query->whereNotIn('order.status', $excludeStatus);
        $query->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA]);

        $query = $query->filterDateRange($dateRange, $startDate, $endDate, 'order.created_at');
        $countOrder = $this->countOrderByStatus(clone $query, [
            OrderStatus::DRAFT,
            OrderStatus::PENDING,
            OrderStatus::PROCESSING,
            OrderStatus::COMPLETED,
            OrderStatus::ON_HOLD,
            OrderStatus::CANCELLED,
        ], 'status');
        $countAbleFulfillStatuses = [OrderFulfillStatus::FULFILLED, OrderFulfillStatus::ON_DELIVERY];
        $countOderByFulfillStatus = $this->countOrderByStatus(clone $query, $countAbleFulfillStatuses, 'fulfill_status');
        $countOrder =  $this->orderService->mappingOrderCountedByStatus($countOrder, $countOderByFulfillStatus, $countAbleFulfillStatuses);

        if (!empty($status)) {
            $query->where('order.status', $status);
        }
        if (!empty($fulfillStatus)) {
            $query->where('order.fulfill_status', $fulfillStatus);
        }
        if (!is_null($sortBy) && in_array($sortBy, $sortableFields)) {
            $query->orderBy($sortBy, $sortDirection);
        } else if ($type === OrderTypeEnum::FULFILLMENT) {
            $query->orderByDesc('order.created_at');
        } else {
            $query->orderByDesc(Order::FILTER_COLUMN_DATE);
        }


        $statsQuery = clone $query;
        $statsQuery->select(DB::raw('COUNT(order.id) as total_orders, SUM(order.total_quantity) as total_items, SUM(order.total_amount) as total_amount'));
        $orderStats = $statsQuery->first();

        $perPage = $request->query('per_page', 15);
        $listOrders = $query->paginate((int)$perPage);

        // Get mockup / design for fulfill orders
        $orderIds = array_column($listOrders->items(), 'id');
        $images = File::query()
            ->select([
                'id',
                'file_url',
                'file_url_2',
                'type',
                'order_id',
                'order_product_id'
            ])
            ->whereIn('order_id', $orderIds)
            ->get();
        $response = json_decode(json_encode($listOrders));
        $response->images = $images;
        $response->countOrders = $countOrder;
        $response->orderStats = $orderStats;

        // Update seller info
        if ($dateRange === DateRangeEnum::LAST_30_DAYS && $user instanceof User) {
            if (count($orderIds) > 0 && !$user->isFulfillment()) {
                $user->addTag('fulfill');
            }

            if (count($orderIds) === 0 && $user->isFulfillment()) {
                $user->removeTag('fulfill');
            }
        }

        return new JsonResponse($response);
    }

    public function userFulfillInfo(): JsonResponse
    {
        $currentUser = currentUser();
        $userBalance = User::find($currentUser->getUserId())->balance;
        $pendingOrders = Order::query()
            ->where([
                'seller_id' => $currentUser->getUserId(),
                'status' => OrderStatus::PENDING
            ])
            ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
            ->get();

        $countTotalPending = $pendingOrders->count();
        $totalPendingAmount = 0;

        if ($countTotalPending > 0) {
            $pendingOrders->map(function ($order) use (&$totalPendingAmount) {
                $totalPendingAmount += $order->total_amount;
                return $order;
            });
        }

        $needTopup = 0;

        if ($totalPendingAmount > $userBalance) {
            $needTopup = ceil(($totalPendingAmount - $userBalance) * 100) / 100;
        }

        return response()->json([
            'total_pendings' => $countTotalPending,
            'total_pendings_amount' => $totalPendingAmount,
            'current_balance' => round($userBalance, 2),
            'need_topup' => $needTopup
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws \Throwable
     */
    public function processFulfillOrder(Request $request): JsonResponse
    {
        $currentUserId = currentUser()->getUserId();

        $request->validate(['ids' => 'bail|required']);

        $orderIds = $request->post('ids');
        $orders = Order::query()
            ->where('seller_id', $currentUserId)
            ->whereIn('id', $orderIds)
            ->where('status', OrderStatus::PENDING)
            ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
            ->get();

        if ($orders->isEmpty()) {
            return $this->errorResponse('No order found');
        }

        foreach ($orders as $order) {
            $userBalance = User::query()->find($currentUserId)->balance;

            if (round($order->total_amount, 2) > $userBalance) {
                return $this->errorResponse('Your balance is not enough to perform this action', 403);
            }

            OrderService::processFulfillOrder($order);
        }

        return $this->successResponse();
    }

    public function updateAddressStatus(Request $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $request->validate(['ids' => 'bail|required']);

        $orderIds = $request->post('ids');
        $orders = Order::query()
            ->where('seller_id', $userId)
            ->whereIn('id', $orderIds)
            ->whereIn('address_verified', [
                OrderAddressVerifiedEnum::UNVERIFIED,
                OrderAddressVerifiedEnum::INVALID,
            ])
            ->get();

        if ($orders->isEmpty()) {
            return $this->errorResponse('No order found');
        }

        $updated = 0;
        $orders->map(function ($order) use (&$updated) {
            $order->address_verified = OrderAddressVerifiedEnum::VERIFIED;
            $order->save();
            $updated++;
            // logToDiscord('Queue validating order #' . $order->order_number, 'error');
            ValidateSellerFulfillOrder::dispatch($order);
        });

        return $updated ? $this->successResponse($updated) : $this->errorResponse();
    }

    public function updateStatus(Request $request, string $action): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $request->validate(['ids' => 'bail|required']);

        if (!in_array($action, ['hold', 'resume', 'cancel'])) {
            return $this->errorResponse();
        }

        $orderIds = $request->post('ids');
        $query = Order::query()
            ->where('seller_id', $userId)
            ->whereIn('id', $orderIds)
            ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA]);

        if ($action === 'resume') {
            $query->where('status', OrderStatus::ON_HOLD);
        } else {
            $query->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT,
            ]);
        }

        $map = [
            'hold' => OrderStatus::ON_HOLD,
            'resume' => OrderStatus::DRAFT,
            'cancel' => OrderStatus::CANCELLED
        ];

        $status = $map[$action];
        $orders = $query->get();
        $updated = $query->update(['status' => $status]);

        if (!$updated) {
            return $this->errorResponse();
        }

        if ($action === 'resume') {
            $orders->each(fn($order) => ValidateSellerFulfillOrder::dispatch($order));
        }

        return $this->successResponse();
    }

    /**
     * @return BinaryFileResponse
     */
    public function exportListProducts(): BinaryFileResponse
    {
        $headings = [
            'sku',
            'name',
            'options',
            'print_spaces',
        ];
        $products = Product::query()
            ->select([
                'sku',
                'name',
                'options',
                'print_spaces',
                'full_printed'
            ])
            ->where([
                'product_type' => ProductType::TEMPLATE,
                'status' => ProductStatus::ACTIVE,
            ])
            ->get()
            ->toArray();

        $sheet = new ExportProductCatalog($products, $headings);
        $fileName = 'Senprints_products_catalog.csv';
        return Excel::download($sheet, $fileName);
    }

    public function exportListBaseCosts(): BinaryFileResponse
    {
        $sheet = new ExportBaseCostCatalog();
        $fileName = 'Senprints_base_cost_catalog.csv';
        return Excel::download($sheet, $fileName);
    }

    public function exportListShippingCosts(): BinaryFileResponse
    {
        $results = ShippingRule::query()
            ->join('product', 'shipping_rule.product_id', '=', 'product.id')
            ->join('system_location', 'system_location.code', '=', 'shipping_rule.location_code')
            ->select([
                'product.sku as product_sku',
                'product.name as product_name',
                'shipping_rule.shipping_method as shipping_method',
                'system_location.name as location',
                'shipping_rule.shipping_cost as shipping_cost',
                'shipping_rule.extra_cost as extra_cost',
            ])
            ->where([
                'product.product_type' => ProductType::TEMPLATE,
                'product.status' => ProductStatus::ACTIVE,
            ])
            ->get()
            ->toArray();

        $headings = [
            'product_sku',
            'product_name',
            'shipping_method',
            'location',
            'shipping_cost',
            'extra_cost'
        ];

        $sheet = new ExportShippingCostCatalog($results, $headings);
        $fileName = 'Senprints_shipping_cost_catalog.csv';
        return Excel::download($sheet, $fileName);
    }

    private function countOrderByStatus($query, $statusTypeValues, $colFilter)
    {
        // $status = OrderStatus::getValues();
        $status = $statusTypeValues;
        $querySelectRaw = 'COUNT(*) all_orders,';
        foreach ($status as $index => $st) {
            $querySelectRaw .= 'SUM('.$colFilter.' = "' . $st . '") as "' . $st . '"';

            if ($index !== count($status) - 1) {
                $querySelectRaw .= ', ';
            }
        }
        $countOrder = $query->selectRaw($querySelectRaw)->first();
        $countOrderCollection = collect($countOrder->toArray());
        return $countOrderCollection->only(array_merge(['all_orders'], $status))->toArray();
    }

    public function getFulfillOrder(int $orderId): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $query = Order::query()->where([
            'seller_id' => $seller->id,
            'id' => $orderId
        ]);

        if (!$query->exists()) {
            return $this->errorResponse('Order not exists');
        }

        $order = $query->with([
            'products' => function ($query) {
                $query->select([
                    'id',
                    'sku',
                    'options',
                    'custom_options',
                    'campaign_title',
                    'order_id',
                    'seller_id',
                    'template_id',
                    'product_id',
                    'product_name',
                    'product_url',
                    'price',
                    'quantity',
                    'thumb_url',
                    'total_amount',
                    'seller_profit',
                    'fulfill_status',
                    'full_printed',
                    'external_product_id',
                    'barcode',
                    'fulfill_fba_by',
                    'design_by',
                ])
                    ->with(['template:id,thumb_url,options,print_spaces,name',
                        'designs',
                        'mockups',
                        'fulfillOrderDesigns',
                        'fulfillOrderMockups',
                    ])
                    ->orderBy('id', 'desc');
            }
        ])
            ->first();
        return $this->successResponse($order);
    }

    public function updateAddress(UpdateFulfillOrderAddressRequest $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $orderId = $request->input('order_id');
        $orderNumber = $request->input('order_number');
        $storeDomain = $request->input('store_domain');
        $storeName = $request->input('store_name');
        $orderNote = $request->input('order_note');

        $customerName = $request->input('customer_name');
        $customerEmail = $request->input('customer_email');
        $customerPhone = $request->input('customer_phone');
        $address = $request->input('address');
        $address2 = $request->input('address_2');
        $city = $request->input('city');
        $state = $request->input('state');
        $postcode = $request->input('postcode');
        $country = $request->input('country');
        $shippingMethod = $request->input('shipping_method', ShippingMethodEnum::STANDARD);
        $iossNumber = $request->input('ioss_number');
        if (!in_array($shippingMethod, ShippingMethodEnum::getValues())) {
            $shippingMethod = ShippingMethodEnum::STANDARD;
        }

        $order = Order::query()
            ->where([
                'seller_id' => $userId,
                'id' => $orderId,
                'type' => OrderTypeEnum::FULFILLMENT
            ])
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT,
                OrderStatus::ON_HOLD,
            ])
            ->first();

        if (empty($order)) {
            return $this->errorResponse('Order not exists');
        }

        $updateData = [
            'order_number_2' => $orderNumber,
            'store_domain' => $storeDomain,
            'store_name' => $storeName,
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'customer_phone' => $customerPhone,
            'address' => $address,
            'address_2' => $address2,
            'city' => $city,
            'state' => $state,
            'postcode' => $postcode,
            'status' => OrderStatus::DRAFT,
            'order_note' => $orderNote,
            'shipping_method' => $shippingMethod,
            'ioss_number' => $iossNumber,
        ];

        $getCountryByName = getLocationByName($country);
        $getCountryByCode = getLocationByCode($country);

        if (!empty($getCountryByName)) {
            $updateData['country'] = $getCountryByName->code;
        } elseif ($getCountryByCode) {
            $updateData['country'] = $getCountryByCode->code;
        }

        $order->update($updateData);

        ValidateSellerFulfillOrder::dispatch($order);

        return $this->successResponse();
    }
    /**
     * @param UpdateFBAOrderRequest $request
     * @return JsonResponse
     */
    public function updateFBAOrder(UpdateFBAOrderRequest $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $orderId = $request->input('order_id');
        $orderNumber = $request->input('order_number');
        $storeDomain = $request->input('store_domain');
        $storeName = $request->input('store_name');
        $orderNote = $request->input('order_note');
        $customerName = $request->input('customer_name');
        $customerEmail = $request->input('customer_email');
        $customerPhone = $request->input('customer_phone');
        $address = $request->input('address');
        $address2 = $request->input('address_2');
        $city = $request->input('city');
        $state = $request->input('state');
        $postcode = $request->input('postcode');
        $country = $request->input('country');
        $shipping_label = $request->input('shipping_label');

        $order = Order::query()
            ->where([
                'seller_id' => $userId,
                'id' => $orderId,
                'type' => OrderTypeEnum::FBA
            ])
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT,
                OrderStatus::ON_HOLD,
            ])
            ->first();

        if (empty($order)) {
            return $this->errorResponse('Order not exists');
        }

        $updateData = [
            'order_number_2' => $orderNumber,
            'store_domain' => $storeDomain,
            'store_name' => $storeName,
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'customer_phone' => $customerPhone,
            'address' => $address,
            'address_2' => $address2,
            'city' => $city,
            'state' => $state,
            'postcode' => $postcode,
            'status' => OrderStatus::DRAFT,
            'order_note' => $orderNote,
            'shipping_method' => ShippingMethodEnum::STANDARD,
            'shipping_label' => $this->fileDownloadedUrl($shipping_label),
        ];

        $getCountryByCode = getLocationByCode($country);
        $getCountryByName = getLocationByName($country);

        if (!empty($getCountryByName)) {
            $updateData['country'] = $getCountryByName->code;
        } elseif ($getCountryByCode) {
            $updateData['country'] = $getCountryByCode->code;
        }

        $order->update($updateData);

        ValidateSellerFulfillOrder::dispatch($order);

        return $this->successResponse();
    }

    /**
     * @param SaveNewFulfillOrderRequest $request
     * @return JsonResponse
     */
    public function saveNewOrder(SaveNewFulfillOrderRequest $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $orderNumber = $request->input('order_number');
        $storeDomain = $request->input('store_domain');
        $storeName = $request->input('store_name');
        $customerName = $request->input('customer_name');
        $customerEmail = $request->input('customer_email');
        $customerPhone = $request->input('customer_phone');
        $address = $request->input('address');
        $address2 = $request->input('address_2');
        $city = $request->input('city');
        $state = $request->input('state');
        $postcode = $request->input('postcode');
        $country = $request->input('country');
        $orderNote = $request->input('order_note');
        $shippingMethod = $request->input('shipping_method', ShippingMethodEnum::STANDARD);
        $iossNumber = $request->input('ioss_number');
        if (!in_array($shippingMethod, ShippingMethodEnum::getValues())) {
            $shippingMethod = ShippingMethodEnum::STANDARD;
        }
        $refId = User::whereId($userId)->value('ref_id');

        $newData = [
            'seller_id' => $userId,
            'order_number_2' => $orderNumber,
            'store_domain' => $storeDomain,
            'store_name' => $storeName,
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'customer_phone' => $customerPhone,
            'address' => $address,
            'address_2' => $address2,
            'city' => $city,
            'state' => $state,
            'postcode' => $postcode,
            'country' => $country,
            'order_note' => $orderNote,
            'type' => OrderTypeEnum::FULFILLMENT,
            'status' => OrderStatus::DRAFT,
            'shipping_method' => $shippingMethod,
            'ref_id' => $refId,
            'ioss_number' => $iossNumber,
        ];

        $order = new Order();
        $order->fill($newData);
        $order->save();

        $newOrder = $order->refresh();
        // update order number
        Order::query()->whereId($newOrder->id)->update(['order_number' => 'FF-' . $newOrder->id]);
        if ($user->isSeller()) {
            $request->merge(['order_id' => $newOrder->id]);
            UserLog::logSellerActivities();
        }
        return $this->successResponse(['new_order_id' => $newOrder->id]);
    }

    public function saveOrderProducts(Request $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $validator = Validator::make($request->all(), [
            'order_id' => 'required',
            'products' => 'nullable|array',
            'products.*.designs' => 'nullable|array',
            'products.*.designs.*.file_url' => [
                'nullable',
                'string',
                new ValidFilePath(),
            ],
            'products.*.designs.*.file_url_2' => [
                'nullable',
                'string',
                new ValidFilePath(),
            ],
            'products.*.designs.*.print_space' => 'required|string',
            'products.*.mockups' => 'nullable|array',
            'products.*.mockups.*.print_space' => 'required|string',
            'products.*.quantity' => ['required', 'numeric', 'min:1'],
            'products.*.mockups.*.file_url' => [
                'nullable',
                'string',
                new ValidFilePath(),
            ],
            'products.*.mockups.*.file_url_2' => [
                'nullable',
                'string',
                new ValidFilePath(),
            ],
            'products.*.design_by_sen'    => [
                'nullable',
                'boolean',
            ],
        ]);

        if ($validator->fails()) {
            $messages = implode("\n", $validator->messages()->unique());
            return $this->errorResponse($messages);
        }
        $orderId = $request->input('order_id');
        $products = $request->input('products');
        $deletedProducts = $request->input('deleted_products');
        $order = Order::query()
            ->where([
                'seller_id' => $userId,
                'id' => $orderId
            ])
            ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
            ->whereIn('status', [
                OrderStatus::DRAFT,
                OrderStatus::PENDING,
                OrderStatus::ON_HOLD,
            ])
            ->first();

        if ($order === null) {
            return $this->errorResponse('Order not found');
        }
        $fulfillByAmazonProducts = collect($products)->filter(function ($product) use ($order) {
            return !empty($product['fulfill_fba_by']) && $product['fulfill_fba_by'] === FbaFulfillBy::BY_AMAZON && $order->type === OrderTypeEnum::FBA;
        });
        if ($fulfillByAmazonProducts->count() > 1) {
            return $this->errorResponse('Please only add one product with fulfill by Amazon');
        }
        OrderProduct::query()
            ->where([
                'order_id' => $orderId,
                'seller_id' => $userId,
            ])
            ->whereIn('id', $deletedProducts)
            ->delete();

        try {
            $order->status = OrderStatus::DRAFT;
            $order->fulfill_status = OrderFulfillStatus::UNFULFILLED;
            if (!empty($products)) {
                $logs = '';
                $handledExternalProductIds = [];
                $templateMapWithExternalProductIds = [];
                foreach ($products as $product) {
                    $productTemplate = Product::query()->firstWhere([
                        'id' => $product['template_id'],
                        'product_type' => ProductType::TEMPLATE,
                        'status' => ProductStatus::ACTIVE
                    ]);

                    if (!$productTemplate) {
                        $order->fulfill_status = OrderFulfillStatus::INVALID;
                        $logs .= 'Product not valid;';
                        continue;
                    }

                    if (!empty($order->shipping_label) && $productTemplate->system_type !== ProductSystemTypeEnum::FULFILL_FBA) {
                        $order->fulfill_status = OrderFulfillStatus::INVALID;
                        $logs .= 'Product type is invalid;';
                        continue;
                    }

                    $location = getLocationByCode($order->country);

                    $isExcludeShipping = FulfillMapping::checkExcludeShipping(
                        $location,
                        $productTemplate->id,
                    );


                    if($isExcludeShipping && $location) {
                        $order->fulfill_status = OrderFulfillStatus::INVALID;
                        $logs .= "Product {$productTemplate->name} not shipping to {$location->name};";
                        continue;
                    }
                    $isSenPrintsDesign = data_get($product, 'design_by_sen', false);
                    $validDesigns = array_filter($product['designs'], function ($design) {
                        return !empty($design['file_url']);
                    });
                    $validMockups = array_filter($product['mockups'], function ($mockup) {
                        return !empty($mockup['file_url']);
                    });
                    if (!$isSenPrintsDesign && count($validDesigns) === 0 && !$productTemplate->isHandMadeType()) {
                        $order->fulfill_status = OrderFulfillStatus::INVALID;
                        $logs .= 'Product ' . $productTemplate->name . ' has no design;';
                    }
                    if (empty($product['barcode']) && !empty($product['fulfill_fba_by']) && $product['fulfill_fba_by'] === FbaFulfillBy::BY_AMAZON && $order->type === OrderTypeEnum::FBA) {
                        $order->fulfill_status = OrderFulfillStatus::INVALID;
                        $logs .= 'Product ' . $productTemplate->name . ' has no barcode;';
                    }
                    if ($isSenPrintsDesign && count($validMockups) === 0) {
                        $order->fulfill_status = OrderFulfillStatus::INVALID;
                        $logs .= 'Product ' . $productTemplate->name . ' require the mockups if need SenPrints clone the design;';
                    }
                    $insertedDesigns = collect();
                    $queryCondition = [
                        'order_id' => $orderId,
                        'type' => FileTypeEnum::DESIGN,
                    ];
                    if (!empty($product['id'])) {
                        $orderProduct = OrderProduct::query()->firstWhere([
                            'order_id' => $orderId,
                            'id' => $product['id'],
                            'seller_id' => $userId,
                        ]);

                        if ($orderProduct === null) {
                            $order->fulfill_status = OrderFulfillStatus::INVALID;
                            $logs .= 'Product not found;';
                            continue;
                        }

                        $updateData = [
                            'campaign_title' => $product['campaign_title'] ?? '',
                            'options' => $product['options'],
                            'quantity' => $product['quantity'],
                            'full_printed' => $productTemplate->full_printed,
                        ];

                        $batchFileData = [];

                        foreach ($product['mockups'] as $mIdx => $mockup) {
                            if (!$mockup['file_url']) {
                                continue;
                            }

                            if (str_contains($mockup['file_url'], 'tmp')) {
                                $mockup['file_url'] = $this->saveTempFile($mockup['file_url'], $orderId);
                            }

                            if (empty($mockup['id'])) {
                                File::query()
                                    ->firstOrCreate([
                                        'order_id' => $orderId,
                                        'type' => FileTypeEnum::IMAGE,
                                        'print_space' => $mockup['print_space'],
                                        'order_product_id' => $product['id'],
                                    ], [
                                        'order_id' => $orderId,
                                        'type' => FileTypeEnum::IMAGE,
                                        'print_space' => $mockup['print_space'],
                                        'file_url' => $mockup['file_url'],
                                        'file_url_2' => $this->fileDownloadedUrl($mockup['file_url']),
                                        'order_product_id' => $product['id'],
                                        'seller_id' => $userId,
                                        'option' => null
                                    ]);
                            } else {
                                $batchFileData[] = [
                                    'id' => $mockup['id'],
                                    'file_url' => $mockup['file_url'],
                                    'file_url_2' => $this->fileDownloadedUrl($mockup['file_url']),
                                ];
                            }

                            if ($mIdx === 0 && empty($orderProduct->thumb_url)) {
                                $updateData['thumb_url'] = $this->fileDownloadedUrl($mockup['file_url']);
                            }
                        }
                        foreach ($product['designs'] as $dIdx => $design) {
                            if (!$design['file_url']) {
                                continue;
                            }
                            if (empty($design['id'])) {
                                $files = File::query()
                                    ->updateOrCreate([
                                        'order_id' => $orderId,
                                        'type' => FileTypeEnum::DESIGN,
                                        'print_space' => $design['print_space'],
                                        'order_product_id' => $product['id'],
                                    ], [
                                        'order_id' => $orderId,
                                        'type' => FileTypeEnum::DESIGN,
                                        'print_space' => $design['print_space'],
                                        'file_url' => $design['file_url'],
                                        'file_url_2' => $this->fileDownloadedUrl($design['file_url']),
                                        'order_product_id' => $product['id'],
                                        'seller_id' => $userId,
                                        'option' => FileRenderType::PRINT,
                                        'image_width' => data_get($design, 'image_width'),
                                        'image_height' => data_get($design, 'image_height'),
                                        'file_name' => data_get($design, 'file_name'),
                                    ]);
                                $insertedDesigns = $insertedDesigns->merge($files);
                            } else {
                                $batchFileData[] = [
                                    'id' => $design['id'],
                                    'file_url' => $design['file_url'],
                                    'file_url_2' => $this->fileDownloadedUrl($design['file_url']),
                                    'image_width' => data_get($design, 'image_width'),
                                    'image_height' => data_get($design, 'image_height'),
                                    'file_name' => data_get($design, 'file_name'),
                                ];
                                $insertedDesigns = $insertedDesigns->merge(collect($batchFileData));
                            }
                            if ($dIdx === 0) {
                                $updateData['thumb_url'] = $this->fileDownloadedUrl($design['file_url']);
                            }
                        }
                        if (!empty($product['barcode'])) {
                            File::query()
                                ->updateOrCreate([
                                    'order_id' => $orderId,
                                    'type' => FileTypeEnum::BARCODE,
                                    'order_product_id' => $product['id'],
                                ], [
                                    'order_id' => $orderId,
                                    'type' => FileTypeEnum::BARCODE,
                                    'file_url' => $product['barcode'],
                                    'file_url_2' => $this->fileDownloadedUrl($product['barcode']),
                                    'order_product_id' => $product['id'],
                                    'seller_id' => $userId,
                                ]);
                            $updateData['barcode'] = $this->fileDownloadedUrl($product['barcode']);
                        }
                        if (!empty($product['fulfill_fba_by']) && in_array($product['fulfill_fba_by'], FbaFulfillBy::getValues(), true)) {
                            $updateData['fulfill_fba_by'] = $product['fulfill_fba_by'];
                        }
                        if (!empty($product['custom_options'])) {
                            $updateData['custom_options'] = json_encode($product['custom_options']);
                        }
                        $designBy = count($validMockups) > 0 && $isSenPrintsDesign ? DesignByEnum::SENPRINTS : DesignByEnum::SELLER;
                        $updateData['design_by'] = $designBy;
                        $orderProduct->update($updateData);
                        $fileModel = new File([], true);

                        batch()->update($fileModel, $batchFileData, 'id');
                        if ($orderProduct->external_product_id && !in_array($orderProduct->external_product_id, array_values($handledExternalProductIds), true)) {
                            $handledExternalProductIds[$orderProduct->id] = $orderProduct->external_product_id;
                            $templateMapWithExternalProductIds[$orderProduct->external_product_id] = $productTemplate;
                        }
                        $queryCondition['order_product_id'] = $product['id'];
                    } else {
                        $barcode = null;
                        $fbaFulfillBy = null;
                        if (!empty($product['fulfill_fba_by']) && in_array($product['fulfill_fba_by'], FbaFulfillBy::getValues(), true)) {
                            $fbaFulfillBy = $product['fulfill_fba_by'];
                        }
                        if (!empty($product['barcode'])) {
                            $barcode = $this->fileDownloadedUrl($product['barcode']);
                        }
                        $designBy = $isSenPrintsDesign ? DesignByEnum::SENPRINTS : DesignByEnum::SELLER;
                        $orderProduct = OrderProduct::query()->create([
                            'order_id' => $orderId,
                            'sku' => $product['sku'], // sku of the template product
                            'template_id' => $product['template_id'], // id of the template product
                            'seller_id' => $userId,
                            'ref_id' => $order->ref_id,
                            'campaign_title' => $product['campaign_title'] ?? '',
                            'options' => $product['options'],
                            'product_name' => $productTemplate->name,
                            'quantity' => $product['quantity'],
                            'full_printed' => $productTemplate->full_printed,
                            'barcode' => $barcode,
                            'fulfill_fba_by' => $fbaFulfillBy,
                            'custom_options' => !empty($product['custom_options']) ? json_encode($product['custom_options']) : null,
                            'campaign_type' => ProductSystemTypeEnum::FULFILL,
                            'design_by' => $designBy,
                        ]);
                        foreach ($product['mockups'] as $mIdx => $mockup) {
                            if (!empty($mockup['file_url'])) {
                                if (str_contains($mockup['file_url'], 'tmp')) {
                                    $mockup['file_url'] = $this->saveTempFile($mockup['file_url'], $orderId);
                                }

                                File::query()
                                    ->firstOrCreate([
                                        'order_id' => $orderId,
                                        'order_product_id' => $orderProduct->id,
                                        'type' => FileTypeEnum::IMAGE,
                                        'print_space' => $mockup['print_space'],
                                    ], [
                                        'order_id' => $orderId,
                                        'type' => FileTypeEnum::IMAGE,
                                        'print_space' => $mockup['print_space'],
                                        'file_url' => $mockup['file_url'],
                                        'order_product_id' => $orderProduct->id,
                                        'seller_id' => $userId,
                                        'option' => null
                                    ]);
                            }
                            if ($mIdx === 0 && empty($orderProduct->thumb_url)) {
                                $orderProduct->thumb_url = $mockup['file_url'];
                                $orderProduct->save();
                            }
                        }
                        foreach ($product['designs'] as $dIdx => $design) {
                            if (!empty($design['file_url'])) {
                                $files = File::query()
                                    ->firstOrCreate([
                                        'order_id' => $orderId,
                                        'type' => FileTypeEnum::DESIGN,
                                        'print_space' => $design['print_space'],
                                        'order_product_id' => $orderProduct->id,

                                ],[
                                    'order_id' => $orderId,
                                    'type' => FileTypeEnum::DESIGN,
                                    'print_space' => $design['print_space'],
                                    'file_url' => $design['file_url'],
                                    'order_product_id' => $orderProduct->id,
                                    'seller_id' => $userId,
                                    'option' => FileRenderType::PRINT
                                ]);
                                $insertedDesigns = $insertedDesigns->merge($files);
                            }
                            if ($dIdx === 0 && empty($orderProduct->thumb_url)) {
                                $orderProduct->thumb_url = $design['file_url'];
                                $orderProduct->save();
                            }
                        }
                        $queryCondition['order_product_id'] = $orderProduct->id;
                    }
                    FulfillmentService::removeUnusedDesigns($insertedDesigns, $queryCondition);
                }

                if ($order->fulfill_status !== OrderFulfillStatus::INVALID) {
                    $order->fulfill_status = OrderFulfillStatus::UNFULFILLED;
                    $order->fulfill_log = null;
                    $order->save();
                    ValidateSellerFulfillOrder::dispatch($order);
                } else {
                    $order->fulfill_log = $logs;
                    $order->save();
                }

                $updatedOrderIds = [];
                foreach ($handledExternalProductIds as $originalId => $externalProductId) {
                    $orderProductNeedUpdateDesign = OrderProduct::query()
                        ->where('external_product_id', $externalProductId)
                        ->whereHas('order', function ($q) {
                            $q->where('fulfill_status', OrderFulfillStatus::INVALID)
                            ->where('status', OrderStatus::DRAFT);
                        })
                        ->where('seller_id', $userId)
                        ->whereDoesntHave('designs')
                        ->get();
                    foreach ($orderProductNeedUpdateDesign as $item) {
                        $success = SyncOrderService::copyDesigns($originalId, $item);
                        if ($success && !in_array($item->order_id, $updatedOrderIds, true)) {
                            $updatedOrderIds[] = $item->order_id;
                        }
                        $this->processCustomOption($item, $templateMapWithExternalProductIds[$externalProductId], ',');
                    }
                }
                if (!empty($updatedOrderIds)) {
                    $orders = Order::query()
                        ->whereIn('id', $updatedOrderIds)
                        ->get();
                    foreach ($orders as $order) {
                        ValidateSellerFulfillOrder::dispatch($order);
                    }
                }
            } else {
                $order->fulfill_status = OrderFulfillStatus::INVALID;
                $order->fulfill_log = 'No products found';
                $order->save();
            }
            CheckOrderAtRisk::dispatch($orderId);
            if (!empty($order->shipping_label)) {
                ExtractOrderLabelContent::dispatch($orderId);
            }
            return $this->successResponse();
        } catch (\Exception $exception) {
            return $this->errorResponse($exception->getMessage());
        }
    }

    public function saveTempFile($fileUrl, $orderId): ?string
    {
        $newPath = 'o/' . $orderId;
        return saveTempFileAws($fileUrl, $newPath);
    }
    /**
     * @param SaveNewFBAOrderRequest $request
     * @return JsonResponse
     */
    public function saveNewFBAOrder(SaveNewFBAOrderRequest $request): JsonResponse
    {
        $user = currentUser();
        $userId = $user->getUserId();
        $orderNumber = $request->input('order_number');
        $storeDomain = $request->input('store_domain');
        $storeName = $request->input('store_name');
        $customerName = $request->input('customer_name');
        $customerEmail = $request->input('customer_email');
        $customerPhone = $request->input('customer_phone');
        $address = $request->input('address', AddressEnum::ADDRESS);
        $address2 = $request->input('address_2', AddressEnum::ADDRESS_2);
        $city = $request->input('city', AddressEnum::CITY);
        $state = $request->input('state', AddressEnum::STATE);
        $postcode = $request->input('postcode', AddressEnum::POSTCODE);
        $country = $request->input('country', AddressEnum::COUNTRY);
        $orderNote = $request->input('order_note');
        $shipping_label = $request->input('shipping_label');

        $refId = User::whereId($userId)->value('ref_id');

        try {
            DB::beginTransaction();

            $newData = [
                'seller_id' => $userId,
                'order_number_2' => $orderNumber,
                'store_domain' => $storeDomain,
                'store_name' => $storeName,
                'customer_name' => $customerName,
                'customer_email' => $customerEmail,
                'customer_phone' => $customerPhone,
                'address' => $address,
                'address_2' => $address2,
                'city' => $city,
                'state' => $state,
                'postcode' => $postcode,
                'country' => $country,
                'order_note' => $orderNote,
                'type' => OrderTypeEnum::FBA,
                'status' => OrderStatus::DRAFT,
                'shipping_method' => ShippingMethodEnum::STANDARD,
                'shipping_label' => $this->fileDownloadedUrl($shipping_label),
                'ref_id' => $refId,
            ];

            $newOrder = Order::create($newData);

            // update order number
            $newOrder->update(['order_number' => 'FBA-' . $newOrder->id]);
            DB::commit();
            if ($user->isSeller()) {
                $request->merge(['order_id' => $newOrder->id]);
                UserLog::logSellerActivities();
            }
            return $this->successResponse(['new_order_id' => $newOrder->id]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse();
        }
    }

    private function fileDownloadedUrl($fileUrl)
    {
        if (Str::startsWith($fileUrl, ['o/', 'u/'])) {
            return $fileUrl;
        }
        return null;
    }

    /**
     * @return JsonResponse
     */
    public function getTemplateProducts(): JsonResponse
    {
        $tag = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;

        $data = cacheGet(
            CacheKeys::LIST_PRODUCT_TEMPLATES_FOR_FULFILL,
            CacheKeys::CACHE_30D,
            static function () {
                return Template::query()
                    ->select([
                        'id',
                        'thumb_url',
                        'name',
                        'options',
                        'sku',
                        'print_spaces',
                        'description',
                        'base_cost',
                        'full_printed',
                        'attributes',
                        'base_costs',
                        'thumb_url',
                        'print_spaces',
                        'extra_print_cost',
                        'price',
                    ])
                    ->where('status', ProductStatus::ACTIVE)
                    ->with([
                        'mockups' => function($query) {
                            $query->select([
                                'product_id',
                                'file_url',
                                'print_space',
                            ]);
                        },
                        'shipping_rules' => function($query) {
                            $query->select([
                                'product_id',
                                'location_code',
                                'shipping_method',
                                'shipping_cost',
                                'extra_cost',
                            ]);
                        },
                    ])
                    ->get()
                    ->toArray();
            }, [$tag], CacheKeys::CACHE_TYPE_ALTERNATIVE
        );

        return $this->successResponse($data);
    }

    public function getVariantsByTemplate($templateId): JsonResponse
    {
        $data = ProductVariant::findAndCacheByTemplate($templateId);

        return $this->successResponse($data);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function saveOrderTemplateProduct(Request $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $template_id = $request->input('template_id');
        $order_product_id = $request->input('order_product_id');
        $order_product = OrderProduct::query()
            ->firstWhere([
                'id' => $order_product_id,
                'seller_id' => $userId,
            ]);
        if($order_product === null || !$order_product->external_product_id) {
            return $this->errorResponse('Order product not found.');
        }
        $productTemplate = Product::query()->firstWhere([
            'id' => $template_id,
            'product_type' => ProductType::TEMPLATE,
            'status' => ProductStatus::ACTIVE
        ]);
        if($productTemplate === null) {
            return $this->errorResponse('Product template not found.');
        }

        $separator = '/';
        if ($order_product->isTikTokOrderProduct() || $order_product->isWoocommerceOrderProduct()) {
            $separator = ',';
        }
        $this->processCustomOption($order_product, $productTemplate, $separator);
        $order_product->refresh();

        //Update product template for none assign products
        $externalOrderProducts = OrderProduct::query()
            ->whereHas('order', function ($query) {
                $query
                    ->where('type', OrderTypeEnum::FULFILLMENT)
                    ->where('status', OrderStatus::DRAFT);
            })
            ->where('id', '!=', $order_product_id)
            ->where('seller_id', $userId)
            ->where('external_product_id', $order_product->external_product_id)
            ->whereNull('template_id')
            ->get();
        foreach ($externalOrderProducts as $item) {
            $this->processCustomOption($item, $productTemplate, $separator);
        }
        return $this->successResponse();
    }

    private function processCustomOption(OrderProduct $orderProduct, Product $productTemplate, $separator): void
    {
        if (empty($productTemplate->options)) {
            TiktokShopService::logToDiscord('Product template options is empty');
            return;
        }
        $variantOptions = explode($separator, $orderProduct->custom_options);
        $variantOptions = array_map('trim', $variantOptions);
        $variantOptions = array_map('strtolower', $variantOptions);

        $templateOptions = json_decode($productTemplate->options, true);
        $options = collect($templateOptions)->mapWithKeys(function ($optionValues, $option) use ($variantOptions) {
            $selectedOption = collect($variantOptions)->first(function ($variantOption) use ($optionValues) {
                return in_array($variantOption, $optionValues, true);
            });

            return [$option => $selectedOption];
        });
        $orderProduct->color = $options->get('color');
        $orderProduct->size = $options->get('size');
        $orderProduct->options = $options->isNotEmpty() ? $options->toJson() : null;
        $orderProduct->sku = $productTemplate->sku;
        $orderProduct->template_id = $productTemplate->id;
        $orderProduct->save();
    }
}

<?php

namespace App\Http\Controllers;

use App\Http\Requests\SaveBlogCategoryRequest;
use App\Models\BlogCategory;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class BlogCategoryController extends Controller
{
    use ApiResponse;

    /**
     * Display a listing of blog categories
     */
    public function index(Request $request, $storeId): JsonResponse
    {
        $search = $request->get('search');

        $query = BlogCategory::query()
            ->withCount('blogs')
            ->filterSellerOwner()
            ->where('store_id', $storeId);

        if ($search) {
            $query->where('name', 'like', '%' . $search . '%');
        }

        $categories = $query
            ->orderBy('name')
            ->paginate($request->get('per_page', 25));

        return $this->successResponse($categories, 'Categories retrieved successfully');
    }

    public function listForSelect($storeId): JsonResponse
    {
        $categories = BlogCategory::query()
            ->where('store_id', $storeId)
            ->select('id', 'name')
            ->get();

        return $this->successResponse($categories, 'Categories retrieved successfully');
    }

    /**
     * Store a newly created category or update existing one
     */
    public function save(SaveBlogCategoryRequest $request, $storeId): JsonResponse
    {
        $currentUser = currentUser();
        $data = $request->validated();
        $data['seller_id'] = $currentUser->getUserId();
        $data['store_id'] = $storeId;

        // Check if updating existing category
        $category = null;
        if (!empty($data['id'])) {
            $category = BlogCategory::query()
                ->where('id', $data['id'])
                ->where('store_id', $storeId)
                ->filterSellerOwner($currentUser)
                ->firstOrFail();
        }

        if (empty($data['slug'])) {
            $data['slug'] = Str::slug($data['name']);

            // check if slug is unique
            $slug = BlogCategory::query()
                ->where('slug', $data['slug'])
                ->filterSellerOwner($currentUser)
                ->where('store_id', $storeId)
                ->when(!empty($data['id']), function ($query) use ($data) {
                    $query->where('id', '!=', $data['id']);
                })
                ->first();

            if ($slug) {
                $data['slug'] = $data['slug'] . '-' . Str::random(5);
            }
        }

        // Create or update category
        if ($category) {
            $category->update($data);
            $message = 'Category updated successfully';
        } else {
            $category = BlogCategory::create($data);
            $message = 'Category created successfully';
        }

        return $this->successResponse($category, $message);
    }

    /**
     * Remove the specified category
     */
    public function destroy($storeId, $id): JsonResponse
    {
        $category = BlogCategory::query()
            ->whereKey($id)
            ->filterSellerOwner()
            ->firstOrFail();
        $category->blogs()->detach();
        $category->delete();
        return $this->successResponse(true, 'Category deleted successfully');
    }
}

<?php

namespace App\Http\Controllers\Analytic;

use App\Enums\CacheKeys;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\DateRangeEnum;
use App\Enums\EventLogsTypeEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PgsAndMysqlVersionEnum;
use App\Enums\ProductType;
use App\Enums\UserInfoKeyEnum;
use App\Enums\UserRoleEnum;
use App\Http\Requests\Analytic\Seller\CampaignRequest;
use App\Http\Requests\Analytic\Seller\DashboardRequest;
use App\Http\Requests\Analytic\Seller\GetAdsRequest;
use App\Http\Requests\Analytic\Seller\StoreRequest;
use App\Models\Campaign;
use App\Models\EventLogs;
use App\Models\IndexEventLogs;
use App\Models\IndexOrder;
use App\Models\IndexOrderProduct;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\StatsOrder;
use App\Models\Store;
use App\Models\SystemLocation;
use App\Models\TempEventLog;
use App\Models\User;
use App\Models\UserInfo;
use App\Traits\ApiResponse;
use App\Traits\ElasticClient;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class SellerController
{
    use ApiResponse;
    use ElasticClient;

    protected object $controller;
    protected $model;
    protected $modelOrderProduct;
    public array $arrFilter = [];
    public array $arrExclude = [
        UserInfoKeyEnum::EXCLUDE_COUNTRIES => []
    ];
    // filter by this column
    public array $dateRanges = [
        'column' => StatsOrder::FILTER_COLUMN_DATE,
    ];

    public function __construct()
    {
        $this->controller = $this;
        $this->model      = new StatsOrder(); //StatsOrder, IndexOrder, IndexOrderProduct
        $this->modelOrderProduct = new IndexOrderProduct();
    }

    public function campaign(CampaignRequest $request, $campaignId): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;
        $campaignName = Campaign::query()
            ->where([
                'id'        => $campaignId,
                // 'seller_id' => currentUser()->getUserId()
            ])
            ->value('name');
        if (!$campaignName) {
            return $this->errorResponse('Campaign not found');
        }

        $this->controller->setCommonFilter($request);

        if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
            $products = $this->controller->getCommonQuery()
                ->addSelect('stats_order.product_id')
                ->with('product:id,name')
                ->groupBy('stats_order.product_id')
                ->get();
        } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
            $products = $this->controller->getCommonQueryOrderProduct()
                ->addSelect('order_product.product_id')
                ->with('product:id,name')
                ->groupBy('order_product.product_id')
                ->get();
        }

        return $this->successResponse(
            [
                'campaign_name' => $campaignName,
                'analytic'      => [
                    'overview'  => $this->controller->getOverview('campaign', true, false, $dbVersion),
                    'products'  => $products,
                    'countries' => $this->controller->getOverViewAndViews('country', [], '', 20, $dbVersion)
                ]
            ]
        );
    }

    public function dashboard(DashboardRequest $request): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;
        $this->controller->setCommonFilter($request);
        $data = $this->controller->getOverview('dashboard', true, false, $dbVersion);
        return $this->successResponse($data);
    }

    public function getCountries(DashboardRequest $request): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;
        $this->controller->setCommonFilter($request);
        $data = $this->controller->getOverViewAndViews('country', [], '', 20, $dbVersion);

        return $this->successResponse($data);
    }

    public function getCommonQuery($getActiveSeller = true)
    {
        $query = $this->controller->getAnalyticQuery()
            ->getAnalyticOverview();

        if ($getActiveSeller && currentUser()->isAdmin()) {
            $query->countActiveSellers();
        }

        return $query;
    }

    public function getCommonQueryOrderProduct($getActiveSeller = true)
    {
        $query = $this->controller->getAnalyticQueryOrderProduct()
            ->getAnalyticOverview();

        if ($getActiveSeller && currentUser()->isAdmin()) {
            $query->countActiveSellers();
        }

        return $query;
    }

    public function getAnalyticQuery($sellerId = null, $arrUnsetFilter = [])
    {
        $response = $this->model::query()
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges, $sellerId, $arrUnsetFilter)
            ->addExcludeAnalytic($this->arrExclude);
        return $response;
    }

    public function getAnalyticQueryOrderProduct($sellerId = null, $arrUnsetFilter = [])
    {
        $response = $this->modelOrderProduct::query()
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges, $sellerId, $arrUnsetFilter)
            ->addExcludeAnalytic($this->arrExclude);
        return $response;
    }

    /** @noinspection PhpMissingBreakStatementInspection */
    public function getOverview($typeAnalytic = 'dashboard', $allReportsWithoutTime = true, $isV2 = false, $dbVersion = PgsAndMysqlVersionEnum::POSTGRES): object
    {
        $dateRangeType = $this->dateRanges['type'];
        switch ($dateRangeType) {
            case DateRangeEnum::YESTERDAY:
                $dateType = 'day';
                $timeout  = CacheKeys::CACHE_24H;
                goto cacheAndGet;
            case DateRangeEnum::LAST_WEEK:
                $dateType = 'week';
                $timeout  = CacheKeys::CACHE_1W;
                goto cacheAndGet;
            case DateRangeEnum::LAST_MONTH:
                $dateType = 'month';
                $timeout  = CacheKeys::CACHE_1W;
                goto cacheAndGet;
            case DateRangeEnum::LAST_3_DAYS:
            case DateRangeEnum::LAST_7_DAYS:
            case DateRangeEnum::LAST_14_DAYS:
            case DateRangeEnum::LAST_30_DAYS:
            case DateRangeEnum::THIS_WEEK:
            case DateRangeEnum::THIS_MONTH:
            case DateRangeEnum::THIS_YEAR:
                $dateType = 'hour';
                $timeout = CacheKeys::CACHE_1H;
                goto cacheAndGet;
                cacheAndGet:
                $storeId = Arr::get($this->arrFilter, 'store_id');
                $tags = ['seller_id_' . currentUser()->getUserId()];
                if ($storeId) {
                    $tags[] = CacheKeys::getStoreId($storeId);
                }

                return cache()
                    ->tags($tags)
                    ->remember(
                        CacheKeys::getStats($typeAnalytic, $dateType, $dateRangeType, $storeId, $isV2),
                        $timeout,
                        function () use ($typeAnalytic, $allReportsWithoutTime, $dbVersion) {
                            return $this->controller->getAnalyticOverview($typeAnalytic, $allReportsWithoutTime, $dbVersion);
                        }
                    );
            default:
                return $this->controller->getAnalyticOverview($typeAnalytic, $allReportsWithoutTime, $dbVersion);
        }
    }

    protected function getAnalyticOverview($typeAnalytic, $allReportsWithoutTime, $dbVersion = PgsAndMysqlVersionEnum::POSTGRES): object
    {
        $overview = $this->controller->getCommonQueryOrderProduct()->first();

        if ($overview === null) {
            return (object)[];
        }

        if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES && $this->dateRanges['type'] === DateRangeEnum::TODAY) {
            $eventLogQuery = TempEventLog::query()
                ->selectRaw('COUNT(DISTINCT session_id) as count');
        } else {
            $range = data_get($this->dateRanges, 'range', [now(), now()]);
            $eventLogQuery = IndexEventLogs::query()
                ->addSelect([
                    DB::raw('SUM(CASE WHEN total > 0 THEN total ELSE 0 END) AS total_sum'),
                ])
                ->when((DateRangeEnum::isMoreThan30Days($this->dateRanges['type']) || (Carbon::create($range[0])->isBefore(now()->startOfMonth()))), function ($query) {
                    $query->selectRaw('APPROX_COUNT_DISTINCT(session_id) AS count');
                }, fn ($query) => $query->selectRaw('COUNT(DISTINCT session_id) as count'));
        }
        $eventLogs = $eventLogQuery
            ->addSelect('type')
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
            ->addExcludeAnalytic($this->arrExclude)
            ->groupBy('type')
            ->get();

        foreach (EventLogsTypeEnum::asArray() as $type) {
            $overview->$type = optional($eventLogs->firstWhere('type', $type))->count + optional($eventLogs->firstWhere('type', $type))->total_sum ?? 0;
        }

        if ($typeAnalytic === 'dashboard') {
            $user = currentUser();

            $queryOrder = Order::query()
                ->selectRaw('sum(tip_amount) as tip_amount')
                ->selectRaw('sum(insurance_fee) as insurance_fee')
                ->addFilterAnalytic($this->arrFilter, $this->dateRanges);

            if ($user->isAdmin()) {
                $queryOrder
                    ->selectRaw(
                        "SUM(IF(type in ('" . OrderTypeEnum::FULFILLMENT . "', '" . OrderTypeEnum::FBA . "'), 1, 0)) AS fulfillment_order_count"
                    )
                    ->selectRaw(
                        "SUM(IF(type in ('" . OrderTypeEnum::FULFILLMENT . "', '" . OrderTypeEnum::FBA . "'), total_quantity, 0)) AS fulfillment_items"
                    )
                    ->selectRaw(
                        "SUM(IF(type in ('" . OrderTypeEnum::FULFILLMENT . "', '" . OrderTypeEnum::FBA . "'), total_amount, 0)) AS fulfillment_order_sales"
                    )
                    ->selectRaw(
                        "SUM(IF(type = '" . OrderTypeEnum::CUSTOM . "', 1, 0)) AS custom_order_count"
                    )
                    ->selectRaw(
                        "SUM(IF(type = '" . OrderTypeEnum::CUSTOM . "', total_quantity, 0)) AS custom_order_items"
                    )
                    ->selectRaw(
                        "SUM(IF(type = '" . OrderTypeEnum::CUSTOM . "', total_amount, 0)) AS custom_order_sales"
                    )
                    ->selectRaw(
                        "SUM(IF(type in ('" . OrderTypeEnum::CUSTOM . "', '" . OrderTypeEnum::REGULAR . "') , total_amount, 0)) AS total_sales"
                    )
                    ->selectRaw("SUM(total_fulfill_base_cost) AS total_fulfill_base_cost")
                    ->selectRaw("SUM(total_fulfill_shipping_cost) AS total_fulfill_shipping_cost")
                    ->selectRaw("SUM(total_fulfill_profit) AS total_fulfill_profit");

                $supplierDebt = OrderProduct::query()
                        ->selectRaw('sum(fulfill_base_cost + fulfill_shipping_cost) as debt')
                        ->whereNull('billed_at')
                        ->where('fulfilled_at', '>', now()->subDays(30))
                        ->value('debt');
            }
            $order = $queryOrder->first();

            if ($order) {
                foreach ($order->getAttributes() as $key => $val) {
                    $overview->$key = $val;
                }
                $overview->insurance_fee = $user->isAdmin() ? $order->insurance_fee * 0.8 : $order->insurance_fee * 0.2;
                $overview->supplier_debt = $supplierDebt ?? 0;
            }
            $this->controller->addAttributeToOverview($overview, $allReportsWithoutTime, $user);
        }

        return model_map($overview, 'floatval');
    }

    protected function addAttributeToOverview(&$overview, $allReportsWithoutTime, $user): void
    {
        $overview->pending_payment = Order::query()
            ->where('status', OrderStatus::PENDING_PAYMENT)
            ->where('payment_status', OrderPaymentStatus::PENDING)
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
            ->count();

        $filters             = $this->arrFilter + ['product_type' => ProductType::CAMPAIGN];
        $seller = currentUser()->getInfoAccess();
        $elasticSearchIndex = $seller?->getElasticSearchIndex() ?? get_env('ELATICSEARCH_INDEX', 'products');
        $overview->campaigns = $this->elasticCount($filters, $this->dateRanges, index: $elasticSearchIndex);

        if ($allReportsWithoutTime) {
            $overview->pending_fee = Order::query()
                ->selectRaw('sum(processing_fee + total_fulfill_fee) as pending_fee')
                ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                ->where('sen_fulfill_status', OrderSenFulfillStatus::PENDING)
                ->where('fulfill_status', '!=', OrderFulfillStatus::CANCELLED)
                ->when($user->isSeller(), function ($query) use ($user) {
                    return $query->where('seller_id', $user->getUserId());
                })
                ->value('pending_fee');

            $userInfo = User::query()
                ->selectRaw('sum(balance) as balance')
                ->selectRaw('sum(sen_points) as sen_points')
                ->when($user->isSeller(), function ($q) use ($user) {
                    return $q->where('id', $user->getUserId());
                })
                ->first();

            if ($userInfo) {
                $overview->seller_balance    = $userInfo->balance;
                $overview->seller_sen_points = $userInfo->sen_points;
            }
        }
    }

    public function getStores(DashboardRequest $request): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;
        $this->setCommonFilter($request);
        $data = $this->controller->getOverViewAndViews('store_id', [], '', 20, $dbVersion);

        return $this->successResponse($data);
    }

    public function getAds(GetAdsRequest $request): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;
        $this->setCommonFilter($request);
        $data = $this->controller->getOverViewAndViews($request->get('ad_option'), [], '', 20, $dbVersion);

        return $this->successResponse($data);
    }

    public function getDevices(DashboardRequest $request): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;
        $this->setCommonFilter($request);
        $data = $this->controller->getOverViewAndViews('device_detail', [], '', 20, $dbVersion);

        return $this->successResponse($data);
    }

    public function getTemplateProducts(DashboardRequest $request): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;
        $this->setCommonFilter($request);
        $data = $this->controller->getOverViewAndViews('template_id', [], '', 20, $dbVersion);

        return $this->successResponse($data);
    }

    public function getOverViewAndViews(
        string $field,
        ?array $filterArrId = [],
        ?string $orderBy = '',
        int $limit = 20,
        $dbVersion = PgsAndMysqlVersionEnum::POSTGRES
    ): array {
        if (request('limit')) {
            $limit = min(request('limit'), 500);
        }
        // if field is ad: ad_campaign, ad_source,... will be replace as name
        $nameAlias = $field . (strpos($field, 'ad') !== false ? ' as name' : '');
        $nameField = strpos($field, 'ad') === false ? $field : 'name';
        if ($field !== 'template_id') {
            switch ($orderBy) {
                    // get all
                case '':
                    $overviews    = $this->getStatOrders($nameAlias, $field, $filterArrId, null, $orderBy, $limit, $dbVersion);
                    $orderByArrId = array_column($overviews, $nameField);
                    $eventLogs    = $this->getEventLogs($nameAlias, $field, $filterArrId, $orderByArrId, '', 100, $dbVersion);
                    if (empty($filterArrId)) {
                        $arrKey = array_unique(array_merge($orderByArrId, array_column($eventLogs, $nameField)));
                        $arrKey = array_slice($arrKey, 0, $limit);
                    }
                    break;
                    // don't need event logs
                default:
                    $overviews = $this->getStatOrders($nameAlias, $field, $filterArrId, null, $orderBy, $limit, $dbVersion);
                    if (empty($filterArrId)) {
                        $arrKey = array_column($overviews, $nameField);
                    }
                    $eventLogs = [];
                    break;
                case CampaignSortByAllowEnum::CONVERSION_RATE:
                    $overviews = $this->getStatOrders($nameAlias, $field, $filterArrId, null, $orderBy, $limit, $dbVersion);
                    $eventLogs = $this->getEventLogs($nameAlias, $field, $filterArrId, null, $orderBy, 100, $dbVersion);
                    if (empty($filterArrId)) {
                        $arrKey = array_unique(
                            array_merge(
                                array_column($eventLogs, $nameField),
                                array_column($overviews, $nameField)
                            )
                        );
                    }
                    break;
                    // else need sort by event logs
                case EventLogsTypeEnum::VISIT:
                case EventLogsTypeEnum::ADD_TO_CART:
                case EventLogsTypeEnum::INIT_CHECKOUT:
                    $orderByArrId = [];
                    if (empty($filterArrId)) {
                        if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
                            $eventLogsForSort = EventLogs::query()
                                ->select($nameField)
                                ->addSelect(DB::raw("count(distinct(session_id)) as count"))
                                ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                                ->addExcludeAnalytic($this->arrExclude)
                                ->groupBy($field)
                                ->where('type', $orderBy)
                                ->orderByDesc('count')
                                ->limit($limit)
                                ->get()
                                ->toArray();
                        } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
                            $eventLogsForSort = IndexEventLogs::query()
                                ->select($nameField)
                                ->addSelect(DB::raw("count(distinct(session_id)) as count"))
                                ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                                ->addExcludeAnalytic($this->arrExclude)
                                ->groupBy($field)
                                ->where('type', $orderBy)
                                ->orderByDesc('count')
                                ->limit($limit)
                                ->get()
                                ->toArray();
                        }
                        $orderByArrId = array_column($eventLogsForSort, $nameField);
                    }

                    $eventLogs = $this->getEventLogs($nameAlias, $field, $filterArrId, $orderByArrId, '', 100, $dbVersion);
                    $overviews = $this->getStatOrders($nameAlias, $field, $filterArrId, $orderByArrId, $orderBy, 100, $dbVersion);
                    if (empty($filterArrId)) {
                        $arrKey = array_unique(
                            array_merge(
                                array_column($eventLogs, $nameField),
                                array_column($overviews, $nameField)
                            )
                        );
                    }
                    break;
            }

            $arr    = [];
            $arrKey ??= $filterArrId ?? [];
            if (!empty($orderByArrId)) {
                usort($arrKey, static function ($a, $b) use ($orderByArrId) {
                    $a = array_search($a, $orderByArrId);
                    $b = array_search($b, $orderByArrId);
                    if ($a === false && $b === false) {
                        return 0;
                    }

                    if ($a === false) {
                        return 1;
                    }

                    if ($b === false) {
                        return -1;
                    }

                    return $a - $b;
                });
            }
            $arrAnalyticKey = CampaignSortByAllowEnum::getArrayForAnalytic();
            if ($field === 'country') {
                $locations = SystemLocation::systemLocations();
            }
            if ($field === 'store_id') {
                $stores = Store::query()
                    ->select([
                        'id',
                        'domain',
                        'sub_domain',
                    ])
                    ->whereKey($arrKey)
                    ->get();

                if (currentUser()->isAdmin()) {
                    $sellerIds = array_unique(
                        array_merge(
                            array_column($overviews, 'seller_id'),
                            array_column($eventLogs, 'seller_id')
                        )
                    );
                    $sellers   = User::query()
                        ->select([
                            'id',
                            'email',
                        ])
                        ->whereKey($sellerIds)
                        ->get();
                }
            }
            foreach ($arrKey as $key) {
                $arr[$key][$nameField] = $key;

                // set default
                foreach ($arrAnalyticKey as $analyticKey) {
                    $arr[$key][$analyticKey] = 0;
                }

                foreach ($overviews as $indexOverview => $overview) {
                    if ($key === Arr::pull($overview, $nameField)) {
                        foreach ($overview as $index => $value) {
                            $arr[$key][$index] = $value;
                        }
                        unset($overviews[$indexOverview]);
                        break;
                    }
                }
                foreach ($eventLogs as $indexEventLog => $eventLog) {
                    if ($key === $eventLog[$nameField]) {
                        $arr[$key][$eventLog['type']] = $eventLog['count'];
                        unset($eventLog['type'], $eventLog['count']);
                        foreach ($eventLog as $index => $value) {
                            $arr[$key][$index] = $value;
                        }
                        unset($eventLogs[$indexEventLog]);
                    }
                }
                // mapping
                if (!empty($locations)) {
                    foreach ($locations as $keyLocation => $location) {
                        if ($key === $location->code) {
                            $arr[$key]['name'] = $location->name;
                            unset($locations[$keyLocation]);
                            break;
                        }
                    }
                }
                if (!empty($stores)) {
                    foreach ($stores as $index => $each) {
                        if ($arr[$key]['store_id'] === $each->id) {
                            $arr[$key]['store'] = $each;
                            unset($stores[$index]);
                            break;
                        }
                    }
                }
                if (!empty($sellers)) {
                    $arr[$key]['seller'] = $sellers->first(function ($each) use (&$arr, $key) {
                        return $arr[$key]['seller_id'] === $each->id;
                    });
                }

                $arr[$key]['conversion_rate'] =
                    (!empty($arr[$key]['orders']) && !empty($arr[$key]['visit']))
                    ? $arr[$key]['orders'] / $arr[$key]['visit']
                    : 0;
            }
        } else {
            $arr = $this->getStatOrders($nameAlias, $field, $filterArrId, null, $orderBy, 100, $dbVersion);
        }

        if ($orderBy === CampaignSortByAllowEnum::CONVERSION_RATE) {
            $arrSorted = array_column($arr, CampaignSortByAllowEnum::CONVERSION_RATE);
            array_multisort($arrSorted, SORT_DESC, SORT_NUMERIC, $arr);
        }

        return array_values_recursive($arr);
    }

    private function getStatOrders(
        string $nameAlias,
        string $field,
        ?array $filterArrId = [],
        ?array $orderByArrId = [],
        ?string $orderBy = '',
        ?int $limit = 100,
        $dbVersion = PgsAndMysqlVersionEnum::POSTGRES
    ): array {
        if (!empty($filterArrId)) {
            $limit = count($filterArrId);
        }
        $eventLogsType = EventLogsTypeEnum::asArray();
        $response = [];
        if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
            $statsOrder = StatsOrder::query()
                ->select($nameAlias)
                // just analytic column need to Order by
                ->when(
                    $orderBy && !in_array($orderBy, $eventLogsType),
                    function ($query) use ($orderBy) {
                        // ex: calculateOrders, calculateSales, ...
                        if ($orderBy !== CampaignSortByAllowEnum::CONVERSION_RATE) {
                            $functionName = 'calculate' . $orderBy;
                        } else {
                            // conversion_rate need order and view
                            $functionName = 'calculateOrders';
                        }


                        return $query->$functionName();
                    }
                )
                // otherwise take all
                ->when(
                    !$orderBy,
                    function ($query) {
                        return $query->getAnalyticOverview();

                    }
                )
                ->when(
                    $field === 'template_id',
                    function ($query) {
                        return $query->with('template_product:id,name');
                    }
                )
                ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                ->addExcludeAnalytic($this->arrExclude)
                ->groupBy($field)
                ->when(
                    $field === 'store_id',
                    function ($query) {
                        $query->addSelect('seller_id');
                        $query->groupBy('seller_id');
                        return $query;
                    }
                )
                ->when(
                    !empty($filterArrId),
                    function ($query) use ($field, $filterArrId) {
                        $query->whereIn($field, $filterArrId);
                    }
                )
                ->when(
                    !empty($orderByArrId),
                    function ($query) use ($field, $orderByArrId) {
                        // field have id in name like campaign_id, template_id will sort by integer
                        if (strpos($field, '_id') === false) {
                            $typeArr = 'text';
                        } else {
                            $typeArr = 'int';
                        }
                        $query->orderByArray($orderByArrId, $field, true, $typeArr);
                    }
                )
                ->when(
                    !in_array($orderBy, $eventLogsType),
                    function ($query) use ($orderBy) {
                        if ($orderBy === CampaignSortByAllowEnum::CONVERSION_RATE || !$orderBy) {
                            $query->orderByDesc('orders');
                        } else {
                            $query->orderByDesc($orderBy);
                        }
                    }
                )
                ->limit($limit)
                // ->sql()
                ->get()
                ->toArray();
            $response = $statsOrder;
        } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
            $statsOrder = IndexOrderProduct::query()
                ->join('order', function ($q) {
                    $q->select([
                        'order.campaign_id',
                        'order.product_id',
                    ])->on('order.id', '=', 'order_product.order_id');
                })
                ->select($nameAlias)
                ->when(
                    $orderBy && !in_array($orderBy, $eventLogsType),
                    function ($query) use ($orderBy) {
                        if ($orderBy !== CampaignSortByAllowEnum::CONVERSION_RATE) {
                            $functionName = 'calculate' . $orderBy;
                        } else {
                            $functionName = 'calculateOrders';
                        }

                        return $query->$functionName();
                    }
                )
                ->when(
                    !$orderBy,
                    function ($query) {
                        return $query->getAnalyticOverview();
                    }
                )
                ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                ->addExcludeAnalytic($this->arrExclude)
                ->groupBy($field)
                ->when(
                    $field === 'store_id',
                    function ($query) {
                        $query->addSelect('order_product.seller_id');
                        $query->groupBy('order_product.seller_id');

                        return $query;
                    }
                )
                ->when(
                    !empty($filterArrId),
                    function ($query) use ($field, $filterArrId) {
                        $query->whereIn($field, $filterArrId);
                    }
                )
                ->when(
                    !in_array($orderBy, $eventLogsType),
                    function ($query) use ($orderBy) {
                        if ($orderBy === CampaignSortByAllowEnum::CONVERSION_RATE || !$orderBy) {
                            $query->orderByDesc('orders');
                        } else {
                            $query->orderByDesc($orderBy);
                        }
                    }
                )
                ->limit($limit)
                // ->sql()
                ->get()
                ->toArray();
            $nameAlias = (explode(' ', $nameAlias));
            $fieldQuery = end($nameAlias);
            $response = [];
            $nullKey = array_search(null, array_column($statsOrder, $fieldQuery));
            if ($nullKey) {
                $statsOrder[$nullKey][$fieldQuery] = "N/A";
            }
            if (!empty($orderByArrId)) {
                $orderByArr = $orderByArrId;
                $unPrioritizedEvent = [];
                $prioritizedEvent = [];

                foreach ($statsOrder as $stats) {
                    if (!in_array($stats[$fieldQuery], $orderByArr, true)) {
                        $unPrioritizedEvent[] = $stats;
                    } else {
                        $prioritizedEvent[] = $stats;
                    }
                }

                foreach ($orderByArr as $orderData) {
                    $statFilter = (array_filter($prioritizedEvent, function ($item) use ($orderData, $fieldQuery) {
                        return $item[$fieldQuery] == $orderData;
                    }));
                    $statFilter = end($statFilter);
                    if (!in_array($statFilter, $response, true)) {
                        $response[] = $statFilter;
                    }
                }
                $response = array_merge($response, $unPrioritizedEvent);
            } else {
                $response = $statsOrder;
            }
        }

        return $response;
    }

    private function getEventLogs(
        string $nameAlias,
        string $field,
        ?array $filterArrId = [],
        ?array $orderByArrId = [],
        ?string $orderBy = '',
        ?int $limit = 100,
        $dbVersion = PgsAndMysqlVersionEnum::POSTGRES
    ): array {
        $eventLogsType = EventLogsTypeEnum::asArray();
        if (!empty($filterArrId)) {
            $limit = count($filterArrId) * count($eventLogsType);
        }

        $response = [];

        if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
            $eventLogs = EventLogs::query()
                ->select($nameAlias)
                ->addSelect('type')
                ->calculateCount()
                ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                ->addExcludeAnalytic($this->arrExclude)
                ->groupBy($field, 'type')
                ->when(
                    $field === 'store_id',
                    function ($query) {
                        $query->addSelect('seller_id');
                        $query->groupBy('seller_id');

                        return $query;
                    }
                )
                ->when(
                    $orderBy === CampaignSortByAllowEnum::CONVERSION_RATE,
                    function ($query) {
                        return $query->filterVisit();
                    }
                )
                ->when(
                    !empty($filterArrId),
                    function ($query) use ($field, $filterArrId) {
                        $query->whereIn($field, $filterArrId);
                    }
                )
                ->when(
                    !empty($orderByArrId),
                    function ($query) use ($field, $orderByArrId) {
                        if (strpos($field, '_id') === false) {
                            $typeArr = 'text';
                        } else {
                            $typeArr = 'int';
                        }

                        $query->orderByArray($orderByArrId, $field, true, $typeArr);
                    }
                )
                ->orderByDesc('count')
                ->limit($limit)
                // ->sql()
                ->get()
                ->toArray();
            $response = $eventLogs;
        } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
            $eventLogs = IndexEventLogs::query()
                ->select($nameAlias)
                ->addSelect('type')
                ->calculateCount(dateRanges: $this->dateRanges)
                ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                ->addExcludeAnalytic($this->arrExclude)
                ->groupBy($field, 'type')
                ->when(
                    $field === 'store_id',
                    function ($query) {
                        $query->addSelect('seller_id');
                        $query->groupBy('seller_id');

                        return $query;
                    }
                )
                ->when(
                    $orderBy === CampaignSortByAllowEnum::CONVERSION_RATE,
                    function ($query) {
                        return $query->filterVisit();
                    }
                )
                ->when(
                    !empty($filterArrId),
                    function ($query) use ($field, $filterArrId) {
                        $query->whereIn($field, $filterArrId);
                    }
                )
                ->orderByDesc('count')
                ->limit($limit)
                // ->sql()
                ->get()
                ->toArray();
            $nameAlias = (explode(' ', $nameAlias));
            $fieldQuery = end($nameAlias);
            if (!empty($orderByArrId)) {
                $orderByArr = [];
                foreach ($orderByArrId as $key => $idForGetOrderBy) {
                    $orderByArr[] = $idForGetOrderBy;
                }
                $unPrioritizedEvent = [];
                $prioritizedEvent = [];
                foreach ($eventLogs as $eventLog) {
                    if (!in_array($eventLog[$fieldQuery], $orderByArr, true)) {
                        $unPrioritizedEvent[] = $eventLog;
                    } else {
                        $prioritizedEvent[] = $eventLog;
                    }
                }

                foreach ($orderByArr as $orderData) {
                    $eventFilter = array_filter($prioritizedEvent, function ($item) use ($orderData, $fieldQuery) {
                        return $item[$fieldQuery] == $orderData;
                    });
                    $typeArray = array_column($eventFilter, 'type');
                    foreach ($typeArray as $type) {
                        $eventTypeFilter = array_filter($eventFilter, function ($item) use ($type) {
                            return $type == $item['type'];
                        });
                        if (!empty($eventTypeFilter)) {
                            $counter = array_sum(array_column($eventTypeFilter, 'count'));
                            $eventTypeFilter[0] = [
                                $fieldQuery => $orderData,
                                'type' => $type,
                                'count' => $counter,
                            ];

                            if (!in_array($eventTypeFilter[0], $response, true)) {
                                $response[] = $eventTypeFilter[0];
                            }
                        }
                    }
                }
                $response = array_merge($response, $unPrioritizedEvent);
            } else {
                $response = $eventLogs;
            }
        }

        return $response;
    }

    public function setCommonFilter(Request $request, $setDateRangeByRequest = true): void
    {
        $user = currentUser();

        if ($user->isSeller()) {
            $userId = $user->getUserId();
        } elseif ($request->has('seller_id')) {
            $userId = $request->get('seller_id');
        } elseif ($request->has('seller_ids')) {
            $userIds = $request->get('seller_ids');
        }

        if (isset($userId)) {
            $this->arrFilter['seller_id'] = $userId;
            $this->controller->setExcludeCountries($userId);
        }

        if (isset($userIds)) {
            $this->arrFilter['seller_id'] = $userIds;
        }

        if ($request->filled('store_id') && $request->get('store_id') !== 'All') {
            $this->arrFilter['store_id'] = $request->store_id;
        }

        if (empty($this->arrFilter['store_id']) && $user->isSeller()) {
            $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
            if (!empty($store_ids)) {
                $this->arrFilter['store_id'] = $store_ids[0];
            }
        }

        if ($request->filled('campaign_id')) {
            $this->arrFilter['campaign_id'] = $request->campaign_id;
        }

        if ($request->filled('campaign_id')) {
            $this->arrFilter['campaign_id'] = $request->campaign_id;
        }

        if ($setDateRangeByRequest) {
            $this->controller->setDateRangeByRequest($request);
        }
    }

    protected function setDateRangeByRequest($request): void
    {
        $this->dateRanges['type']        = $request->date_type ?? $request->date_range ?? DateRangeEnum::TODAY;
        $this->dateRanges['is_timezone'] = $request->boolean('is_timezone', false);
        if ($request->date_type === 'custom') {
            $this->dateRanges['range'] = [
                $request->start_date,
                $request->end_date,
            ];
        }
    }

    protected function setExcludeCountries($userId): void
    {
        $excludeCountries = UserInfo::query()
            ->where('user_id', $userId)
            ->where('key', UserInfoKeyEnum::EXCLUDE_COUNTRIES)
            ->pluck('value')
            ->toArray();

        if (!empty($excludeCountries[0])) {
            $this->arrExclude[UserInfoKeyEnum::EXCLUDE_COUNTRIES] = explode(',', $excludeCountries[0]);
        }
    }

    public function getChart(StoreRequest $request): array
    {
        $dateType = 'day';
        $user     = currentUser();
        $timeout  = ($user->isAdmin()) ? CacheKeys::CACHE_1H : CacheKeys::CACHE_24H;
        $storeId  = $request->get('store_id');
        $sellerId = $user->isAdmin() ? 0 : currentUser()->getUserId();
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;

        $tags = [
            'seller_id_' . $sellerId,
        ];

        if ($storeId) {
            $tags[] = CacheKeys::getStoreId($storeId);
        }

        return cache()
            ->tags($tags)
            ->remember(
                CacheKeys::getStats('chart', $dateType, DateRangeEnum::LAST_14_DAYS, $storeId),
                $timeout,
                function () use ($request, $user, $dbVersion) {
                    $request->date_type  = DateRangeEnum::CUSTOM;
                    $request->start_date = now()->subDays(15);
                    $request->end_date   = now()->subDays();
                    $request->datetime   = true;

                    $this->controller->setCommonFilter($request);
                    $days         = 15;
                    $now          = getTodayTimeZone(null, false)->subDays($days);
                    $columnByTime = ($user->isAdmin()) ? 'DATE("timestamp"+ INTERVAL ' . "'7 hours')" : 'datestamp';
                    $columnByTime2 = ($user->isAdmin()) ? 'DATE_FORMAT(DATE_ADD(`timestamp`, INTERVAL ' . "7 HOUR), '%Y-%m-%d')" : `datestamp`;
                    $columnByTime3 = ($user->isAdmin()) ? 'DATE_FORMAT(DATE_ADD(`paid_at`, INTERVAL ' . "7 HOUR), '%Y-%m-%d')" : `datestamp`;
                    // $now = \Carbon\Carbon::createFromFormat('Y-m-d', '2021-12-21');

                    $dates = [];
                    for ($i = 1; $i < $days; $i++) {
                        $dates[] = $now->copy()->addDays($i)->format('Y-m-d');
                    }

                if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
                    $dataEvents = EventLogs::query()
                        ->selectRaw("$columnByTime as date")
                        ->addSelect('type')
                        ->calculateCount()
                        ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                        ->addExcludeAnalytic($this->arrExclude)
                        ->groupByRaw("$columnByTime , type")
                        ->get();

                    $dataStats = StatsOrder::query()
                        ->selectRaw("$columnByTime3 as date")
                        ->calculateOrders()
                        ->calculateItems()
                        ->when($user->isAdmin(), function ($q) {
                            $q->countActiveSellers();
                        })
                        ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                        ->addExcludeAnalytic($this->arrExclude)
                        ->groupByRaw($columnByTime3)
                        ->get();
                } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
                    $dataEvents = IndexEventLogs::query()
                        ->selectRaw("$columnByTime2 as date")
                        ->addSelect('type')
                        ->calculateCount(dateRanges: $this->dateRanges)
                        ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                        ->addExcludeAnalytic($this->arrExclude)
                        ->groupByRaw("$columnByTime2 , `type`")
                        ->orderBy('date')
                        ->get();


                    $dataStats = IndexOrder::query()
                        ->selectRaw("$columnByTime3 as date")
                        ->calculateOrders()
                        ->calculateItems()
                        ->when($user->isAdmin(), function ($q) {
                            $q->countActiveSellers();
                        })
                        ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                        ->addExcludeAnalytic($this->arrExclude)
                        ->groupByRaw($columnByTime3)
                        ->orderBy('date')
                        ->get();
                }

                    $arr    = [];
                    $events = EventLogsTypeEnum::getValues();
                    foreach ($dates as $index => $date) {
                        $orders        = 0;
                        $items         = 0;
                        $activeSellers = 0;
                        $stringDate    = $user->isAdmin() ? $date : $date . ' 00:00:00';
                        $sameDate      = false;
                        foreach ($dataEvents as $key => $each) {
                            $value = $each->count;
                            if ($each->type === EventLogsTypeEnum::VISIT) {
                                $value = round($value / 10);
                            }
                            if (empty($sameDate)) {
                                if ($stringDate === $each->date) {
                                    $sameDate                 = $stringDate;
                                    $arr[$each->type][$index] = $value;
                                    unset($dataEvents[$key]);
                                }
                            } elseif ($sameDate === $each->date) {
                                $arr[$each->type][$index] = $value;
                                unset($dataEvents[$key]);
                            } else {
                                break;
                            }
                        }
                        foreach ($events as $event) {
                            if (!isset($arr[$event][$index])) {
                                $arr[$event][$index] = 0;
                            }
                        }
                        foreach ($dataStats as $key => $value) {
                            if ($stringDate === $value->date) {
                                $orders        = $value->orders;
                                $items         = $value->items;
                                $activeSellers = $value->active_sellers ?? 0;
                                unset($dataStats[$key]);
                                break;
                            }
                        }

                        $arr['orders'][$index]         = $orders;
                        $arr['items'][$index]          = $items;
                        if ($user->isAdmin()) {
                            $arr['active_sellers'][$index] = $activeSellers;
                        }
                        $arr['dates'][$index]          = Str::after($date, '-');
                    }

                    return $arr;
                }
            );
    }

    public function getCompares(DashboardRequest $request): JsonResponse
    {
        $dateType           = $request->get('date_type');
        $dateRanges['type'] = $dateType;
        if ($dateType === DateRangeEnum::CUSTOM) {
            $dateRanges['range'] = [
                $request->get('start_date'),
                $request->get('end_date'),
            ];
        }

        return $this->controller->getResponseCompares($dateRanges, $request);
    }

    private function getResponseCompares($dateRanges, $request): JsonResponse
    {
        $this->controller->getComparesDateRange($dateRanges);
        $this->dateRanges = $dateRanges;
        $this->controller->setCommonFilter($request, false);

        $data = $this->controller->getOverview('dashboard', false);

        return $this->successResponse($data);
    }

    /** @noinspection PhpMissingBreakStatementInspection */
    private function getComparesDateRange(&$dateRange): void
    {
        $hourOffset = getHourOffsetBySeller();
        $today      = now()->addRealHours($hourOffset)->startOfDay()->subRealHours($hourOffset);
        switch ($dateRange['type']) {
            case DateRangeEnum::LIFE_TIME:
                return;
            case DateRangeEnum::CUSTOM:
                if ($dateRange['range'] === [null, null]) {
                    return;
                }

                $t1 = Carbon::parse($dateRange['range'][0]);
                $t2 = Carbon::parse($dateRange['range'][1]);

                $dateDiff = $t2->diffInDays($t1);
                $t0       = $t1->copy()->subDays($dateDiff)->startOfDay();
                $t1       = $t1->copy()->endOfDay();
                break;
            default:
            case DateRangeEnum::TODAY:
                $dateRange['type'] = DateRangeEnum::YESTERDAY;
                return;
            case DateRangeEnum::YESTERDAY:
                $t1         = $today->subDays();
                $dateDiff   = 1;
                $isTimezone = true;
                goto diff;
            case DateRangeEnum::LAST_3_DAYS:
                $t1         = now()->subDays(3);
                $dateDiff   = 3;
                $isTimezone = true;
                goto diff;
            case DateRangeEnum::LAST_7_DAYS:
                $t1         = now()->subDays(7);
                $dateDiff   = 7;
                $isTimezone = true;
                goto diff;
            case DateRangeEnum::LAST_30_DAYS:
                $t1         = now()->subDays(30);
                $dateDiff   = 30;
                $isTimezone = true;
                goto diff;
            case DateRangeEnum::THIS_WEEK:
                $t1       = $today->startOfWeek();
                $dateDiff = 7;
                goto diff;
            case DateRangeEnum::LAST_WEEK:
                $t1       = $today->subWeeks()->startOfWeek();
                $dateDiff = 7;
                goto diff;
                diff:
                // dd($dateDiff);
                $t0 = $t1->copy()->subDays($dateDiff);
                break;
            case DateRangeEnum::THIS_MONTH:
                $t1 = now()->subMonthsWithNoOverflow()->endOfMonth();
                $t0 = $t1->copy()->startOfMonth();
                break;
            case DateRangeEnum::LAST_MONTH:
                $t1 = $today->subMonthsWithNoOverflow()->startOfMonth();
                $t0 = $t1->copy()->subMonthsWithNoOverflow(2)->endOfMonth();
                break;
            case DateRangeEnum::THIS_YEAR:
                $t1 = $today->subYearsWithNoOverflow()->startOfYear();
                $t0 = $t1->copy()->subYearsWithNoOverflow()->endOfYear();
                break;
            case DateRangeEnum::LAST_YEAR:
                $t1 = $today->subYearsWithNoOverflow(2)->startOfYear();
                $t0 = $t1->copy()->subYearsWithNoOverflow(2)->endOfYear();
                break;
        }
        $dateRange['range'][0] = $t0;
        $dateRange['range'][1] = $t1;
        $dateRange['type']     = DateRangeEnum::CUSTOM;

        if (!empty($isTimezone)) {
            $dateRange['is_timezone'] = $isTimezone;
        }
    }

    public function compareToPlatform(StoreRequest $request): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;
        try {
            $data = cache()->remember(
                CacheKeys::ANALYTIC_PERCENT_PLATFORM,
                CacheKeys::CACHE_1H,
                function () use ($request, $dbVersion) {
                    $this->dateRanges['type'] = DateRangeEnum::LAST_7_DAYS;
                    $this->setCommonFilter($request, false);
                    // get all by platform
                    unset($this->arrFilter['seller_id']);

                    $order = $this->getAnalyticQuery()
                        ->calculateOrders()
                        ->first();


                    if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
                        $events = TempEventLog::query()
                            ->select('type')
                            ->calculateCount()
                            ->groupBy('type')
                            ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                            ->addExcludeAnalytic($this->arrExclude)
                            ->get();
                    } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
                        $events = IndexEventLogs::query()
                            ->select('type')
                            ->calculateCount(dateRanges: $this->dateRanges)
                            ->groupBy('type')
                            ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                            ->addExcludeAnalytic($this->arrExclude)
                            ->get();
                    }


                    $data                  = [];
                    $data['orders']        = optional($order)->orders ?? 0;
                    $data['visit']         = 0;
                    $data['add_to_cart']   = 0;
                    $data['init_checkout'] = 0;
                    $data['cr']            = 0;

                    foreach ($events as $event) {
                        $data[$event->type] = $event->count + $event->total_sum;
                    }

                    $motivateRate = 1.05;

                    if ($data['visit']) {
                        $data['cr'] = round($data['orders'] * $motivateRate / $data['visit'] * 100, 2);
                    }
                    if ($data['init_checkout']) {
                        $data['orders'] = round($data['orders'] * $motivateRate / $data['init_checkout'] * 100, 2);
                    }
                    if ($data['add_to_cart']) {
                        $data['init_checkout'] = round(
                            $data['init_checkout'] * $motivateRate / $data['add_to_cart'] * 100,
                            2
                        );
                    }
                    if ($data['visit']) {
                        $data['add_to_cart'] = round($data['add_to_cart'] * $motivateRate / $data['visit'] * 100, 2);
                    }

                    unset($data['visit']);

                    return $data;
                }
            );

            return $this->successResponse($data);
        } catch (Throwable $e) {
            logToDiscord(
                __FUNCTION__
                    . PHP_EOL
                    . $e->getMessage(),
                'analytic'
            );
            return $this->errorResponse();
        }
    }

    public function getInactiveUsers(Request $request): JsonResponse
    {
        $dateType = $request->get('date_type', DateRangeEnum::LAST_3_DAYS);
        $startTime = Carbon::now()->subDays(3);
        $endTime = Carbon::now()->subDays(7);

        switch ($dateType) {
            case DateRangeEnum::LAST_3_DAYS:
                $startTime = Carbon::now()->subDays(3);
                $endTime = Carbon::now()->subDays(7);
                break;
            case DateRangeEnum::LAST_7_DAYS:
                $startTime = Carbon::now()->subWeek();
                $endTime = Carbon::now()->subMonth();
                break;
            case DateRangeEnum::LAST_30_DAYS:
                $startTime = Carbon::now()->subMonth();
                $endTime = Carbon::now()->subDays(60);
                break;
        }

        try {
            $orders = Order::query()
                ->GetLastPaidOrder($startTime, $endTime)
                ->limit(1000);

            $data = User::query()
                ->select('user.id', 'user.name', 'user.email', 'order.paid_at')
                ->role(UserRoleEnum::SELLER)
                ->joinSub($orders, 'order', function ($join) {
                    $join->on('user.id', '=', 'order.seller_id');
                })
                ->orderBy('order.paid_at', 'desc')
                ->get();

            return $this->successResponse($data);
        } catch (\Throwable $e) {
            return $this->errorResponse();
        }
    }
}

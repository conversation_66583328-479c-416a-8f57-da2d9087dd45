<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use App\Traits\ApiResponse;

class BlogTagController extends Controller
{
    use ApiResponse;

    /**
     * Attach tags to a blog
     */
    public function attachTags(Request $request, $blogId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'tag_ids' => 'required|array',
            'tag_ids.*' => 'integer|exists:tags,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $blog = Blog::findOrFail($blogId);
            // Attach tags without duplicating existing ones
            $blog->tags()->syncWithoutDetaching($request->tag_ids);
            // Load updated relationships
            $blog->load('tags');

            return response()->json([
                'success' => true,
                'message' => 'Tags attached to blog successfully',
                'data' => [
                    'blog_id' => $blog->id,
                    'blog_title' => $blog->title,
                    'tags' => $blog->tags->pluck('name')->toArray()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to attach tags to blog',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Detach tags from a blog
     */
    public function detachTags(Request $request, $blogId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'tag_ids' => 'required|array',
            'tag_ids.*' => 'integer|exists:tags,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $blog = Blog::findOrFail($blogId);
            $blog->tags()->detach($request->tag_ids);
            // Load updated relationships
            $blog->load('tags');

            return response()->json([
                'success' => true,
                'message' => 'Tags detached from blog successfully',
                'data' => [
                    'blog_id' => $blog->id,
                    'blog_title' => $blog->title,
                    'remaining_tags' => $blog->tags->pluck('name')->toArray()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to detach tags from blog',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync tags for a blog (replace all existing tags)
     */
    public function syncTags(Request $request, $blogId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'tag_ids' => 'required|array',
            'tag_ids.*' => 'integer|exists:tags,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $blog = Blog::findOrFail($blogId);
            $blog->tags()->sync($request->tag_ids);
            // Load updated relationships
            $blog->load('tags');

            return response()->json([
                'success' => true,
                'message' => 'Blog tags synchronized successfully',
                'data' => [
                    'blog_id' => $blog->id,
                    'blog_title' => $blog->title,
                    'tags' => $blog->tags->pluck('name')->toArray()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync blog tags',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

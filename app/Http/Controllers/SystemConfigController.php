<?php

namespace App\Http\Controllers;

use App\Enums\CacheKeys;
use App\Enums\CurrencyEnum;
use App\Enums\StaffStatus;
use App\Enums\StorageDisksEnum;
use App\Models\Staff;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Models\SystemTimeZone;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use App\Traits\Encrypter;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Throwable;

class SystemConfigController extends Controller
{
    use ApiResponse, Encrypter;

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function __invoke(): JsonResponse
    {
        $configs = SystemConfig::frontendConfigs();
        $currencies = StoreService::systemCurrencies();
        $colors = StoreService::systemColors();
        $countries = SystemLocation::systemCountries(); // todo: remove country
        $locations = SystemLocation::systemLocations();
        $timezones = SystemTimeZone::systemTimezones();
        $paypal_withdraw_settings = SystemConfig::getCustomConfig('paypal_withdraw_settings');
        $paypal_withdraw_settings = optional($paypal_withdraw_settings)->json_data;
        $withdrawPercentFee = 2;
        $withdrawFee = 60;
        if (!empty($paypal_withdraw_settings)) {
            $paypal_withdraw_settings = json_decode($paypal_withdraw_settings, true);
            $withdrawPercentFee = $paypal_withdraw_settings['percent_charge'] ?? 2;
            $withdrawFee = $paypal_withdraw_settings['max_fee'] ?? 60;
        }
        $withdrawPaypalSettings = [
            'percent_charge' => $withdrawPercentFee,
            'max_fee' => $withdrawFee,
        ];

        $data = compact(
            'configs',
            'currencies',
            'colors',
            'countries',
            'locations',
            'timezones',
            'withdrawPaypalSettings',
        );
        if (currentUser()->isAdmin()) {
            $data['supporters'] = self::supporters();
            $data['all_staff'] = self::allStaff();
        }

        return $this->successResponse($data);
    }

    public static function findOrDefaultCurrency($currencyCode)
    {
        $currencies = StoreService::systemCurrencies();
        $currency = $currencies?->firstWhere('code', strtoupper($currencyCode));
        if (!$currency) {
            return $currencies?->firstWhere('code', CurrencyEnum::USD);
        }
        return $currency;
    }

    public static function supporters(): Collection
    {
        return cache()->remember(
            CacheKeys::SUPPORTERS,
            CacheKeys::CACHE_30D,
            function () {
                return Staff::query()
                    ->select(
                        [
                            'id',
                            'name',
                            'email',
                        ]
                    )
                    ->where('status', StaffStatus::ACTIVATED)
                    ->role([
                        'supporter',
                        'Seller Support',
                        'CS Manager',
                        'Operations Admin',
                        'Accounting',
                    ])
                    ->with('roles:id,name')
                    ->get();
            });
    }

    public static function allStaff(): Collection
    {
        return cache()->remember(
            CacheKeys::ALL_STAFF,
            CacheKeys::CACHE_30D,
            function () {
                return Staff::query()
                    ->select(['id', 'name', 'email'])
                    ->where('status', StaffStatus::ACTIVATED)
                    ->with('roles:id,name')
                    ->get();
            });
    }

    public function index(Request $request): JsonResponse
    {
        $query = SystemConfig::query();
        if ($request->filled('q')) {
            $query->where('key', 'like', '%' . $request->get('q') . '%');
        }
        $data = $query->get();

        $encrypter = self::encrypter();
        $data->each(static function ($each) use ($encrypter) {
            if ($each->is_encrypted) {
                if (!empty($each->value)) {
                    $each->value = $encrypter->decrypt($each->value);
                }
                if (!empty($each->json_data)) {
                    $each->json_data = $encrypter->decrypt($each->json_data);
                }
            }
        });

        return $this->successResponse($data);
    }

    public function show($id): JsonResponse
    {
        try {
            $obj = SystemConfig::findOrFail($id);
            if ($obj->is_encrypted) {
                $encrypter = self::encrypter();
                if (!empty($obj->value)) {
                    $obj->value = $encrypter->decrypt($obj->value);
                }
                if (!empty($obj->json_data)) {
                    $obj->json_data = $encrypter->decrypt($obj->json_data);
                }
            }

            return $this->successResponse($obj);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    private static function getInputData(Request $request): array
    {
        $data = $request->all();

        if ($request->boolean('is_encrypted')) {
            $encrypter = self::encrypter();
            if ($request->filled('value')) {
                $data['value'] = $encrypter->encrypt($request->get('value'));
            }
            if ($request->filled('json_data')) {
                $data['json_data'] = $encrypter->encrypt($request->get('json_data'));
            }
        }

        return $data;
    }

    public function store(Request $request): JsonResponse
    {
        try {
            SystemConfig::create(self::getInputData($request));
            $this->clearCacheConfig($request->input('type'));
            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function update(Request $request, $id): JsonResponse
    {
        try {
            $systemConfig = SystemConfig::findOrFail($id);
            $systemConfig->fill(self::getInputData($request));
            $systemConfig->save();
            $this->clearCacheConfig($request->input('type'));
            return $this->successResponse();
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function killDatabaseProcessId(Request $request) {
        try {
            $processId = (int) $request->get('id');
            if ($processId > 0) {
                DB::statement("KILL {$processId}");
                return $this->successResponse();
            }
            return $this->errorResponse('Invalid process id');
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function updateTableData(Request $request) {
        try {
            $validator = Validator::make($request->all(), [
                'table' => ['required'],
                'connection' => ['nullable', 'in:mysql,singlestore'],
                'action' => ['nullable', 'in:delete,update'],
                'soft_delete' => ['nullable', 'in:0,1'],
                'update' => ['nullable', 'array'],
                'id' => [
                    Rule::requiredIf(function() use ($request) {
                        return empty($request->post('ids')) && empty($request->post('condition'));
                    })
                ],
                'ids' => [
                    Rule::requiredIf(function() use ($request) {
                        return empty($request->post('id')) && empty($request->post('condition'));
                    })
                ],
                'condition' => [
                    Rule::requiredIf(function() use ($request) {
                        return empty($request->post('id')) && empty($request->post('ids'));
                    })
                ],
            ]);
            if ($validator->fails()) {
                return $this->errorResponse($validator->getMessageBag());
            }
            $connection = $request->post('connection', 'mysql');
            $id = $request->post('id');
            $ids = $request->post('ids');
            $table = $request->post('table');
            $update = $request->post('update');
            $condition = $request->post('condition');
            $soft_delete = $request->post('soft_delete', 1);
            $action = $request->post('action', 'update');
            if ($action === 'update' && empty($update)) {
                return $this->errorResponse('Update data is required');
            }
            if (!empty($ids)) {
                $ids = explode(',', $ids);
            }
            $query = DB::connection($connection);
            $tables = array_map(function ($item) {
                return collect($item)->values()->first();
            }, $query->select('SHOW TABLES'));
            if (empty($tables)) {
                return $this->errorResponse('No table found');
            }
            if (!in_array($table, $tables, true)) {
                return $this->errorResponse('Table does not exist');
            }
            if (empty($soft_delete) && in_array($table, ['order', 'order_product', 'file', 'product', 'design'])) {
                return $this->errorResponse('Table can not hard delete record.');
            }
            $columns = Schema::connection($connection)->getColumnListing($table);
            $query = $query->table($table);
            $query = $query->when(!empty($id), function ($query) use ($id) {
                return $query->where('id', $id);
            });
            $query = $query->when(!empty($ids), function ($query) use ($ids) {
                return $query->whereIn('id', $ids);
            });
            $where_columns = [];
            if (!empty($condition)) {
                $where_columns = array_intersect($columns, array_keys($condition));
            }
            if (empty($where_columns) && empty($ids) && empty($id)) {
                return $this->errorResponse('Invalid condition.');
            }
            $query = $query->when(!empty($where_columns), function ($query) use ($condition, $where_columns) {
                foreach ($where_columns as $column) {
                    $query = $query->where($column, $condition[$column]);
                }
            });
            if (!$query->exists()) {
                return $this->errorResponse('Record not found');
            }
            if ($action === 'update') {
                $update_columns = array_intersect($columns, array_keys($update));
                $data = array();
                foreach ($update_columns as $column) {
                    $data[$column] = $update[$column];
                }
                if (empty($data)) {
                    return $this->errorResponse('No column found for update');
                }
                $updated = $query->update($data);
                if ($updated) {
                    return $this->successResponse();
                }
                return $this->errorResponse('Update failed');
            }
            if (!empty($soft_delete)) {
                $data = [];
                if (Str::contains('deleted_at', $columns)) {
                    $data['deleted_at'] = now();
                }
                if (Str::contains('is_deleted', $columns)) {
                    $data['is_deleted'] = 1;
                }
                if (empty($data)) {
                    return $this->errorResponse('No column found for update');
                }
                $updated = $query->update($data);
                if ($updated) {
                    return $this->successResponse();
                }
                return $this->errorResponse('Soft delete failed');
            }
            $deleted = $query->delete();
            if ($deleted) {
                return $this->successResponse();
            }
            return $this->errorResponse('Hard delete failed');
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function forceRunCommand(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'class' => ['required']
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        try {
            $class = $request->post('class');
            if (!class_exists($class)) {
                return $this->errorResponse('Class not found');
            }
            $result = app()->make($class)->handle();
            return $this->successResponse('Successfully.', [
                'result' => $result
            ]);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function getAffiliateWidgetCdn () {
        try {
            $cdn = SystemConfig::getCustomConfig('affiliate_widget_cdn');
            $response = $cdn ? $cdn->value : 'https://cdn.jsdelivr.net/gh/ngothanhthien/spa-widget@main-1.0.1/spa-widget.min.js';
            return $response;
        } catch (Throwable $e) {
            return null;
        }
    }

    public function copyS3File(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'disk' => ['required', 'in:' . implode(',', StorageDisksEnum::getValues())],
            'source' => ['required'],
            'destination' => ['required'],
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        try {
            $disk = $request->post('disk', StorageDisksEnum::S3);
            $source = $request->post('source');
            if (!Storage::disk($disk)->exists($source)) {
                return $this->errorResponse('File not found');
            }
            $destination = $request->post('destination');
            $newFilePath = copyS3TempToMain($source, $destination, $disk);
            if (!empty($newFilePath)) {
                return $this->successResponse();
            }
            return $this->errorResponse('Copy failed');
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    public function searchFiles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'path' => ['required'],
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        try {
            $imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
            $path = $request->get('path');
            if (Str::endsWith(Str::lower($path), $imageExtensions)) {
                $onDisk = file_exists_on_storage($path);
                if (empty($onDisk)) {
                    return $this->errorResponse('File not found');
                }
                return $this->successResponse([[
                    'disk' => $onDisk,
                    'file_url' => s3Url($path),
                    'file_size' => convertBytes(Storage::disk($onDisk)->size($path), 'MB') . ' MB',
                ]]);
            }
            if (Str::of($path)->explode('/')->filter()->count() === 1) {
                return $this->errorResponse('Invalid path');
            }
            $resultFiles = [];
            foreach (StorageDisksEnum::activeStorages() as $disk) {
                $files = Storage::disk($disk)->getDriver()->listContents($path, true)->filter(function ($attributes) {
                    return $attributes->isFile();
                })->toArray();
                if (empty($files)) {
                    continue;
                }
                collect($files)->sortByDesc('fileSize')->each(function ($attributes) use ($disk, &$resultFiles) {
                    $resultFiles[] = [
                        'disk' => $disk,
                        'file_url' => s3Url($attributes->path()),
                        'file_size' => convertBytes($attributes->fileSize(), 'MB') . ' MB',
                    ];
                });
            }
            if (empty($resultFiles)) {
                return $this->errorResponse('Not found file');
            }
            return $this->successResponse($resultFiles);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param $type
     * @return void
     */
    private function clearCacheConfig($type)
    {
        if ((int) $type === 2) {
            $key = CacheKeys::SYSTEM_CONFIGS_BACKEND;
        } else {
            $key = CacheKeys::SYSTEM_CONFIGS_FRONTEND;
        }
        syncClearCache($key, CacheKeys::CACHE_TYPE_ALTERNATIVE);
        cacheAlt()->forget($key);
    }

    public function getCheckoutFormConfig(?string $country): JsonResponse
    {
        return $this->successResponse(SystemConfig::getCheckoutFormConfig(strtoupper($country)));
    }
}

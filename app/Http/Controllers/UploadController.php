<?php

namespace App\Http\Controllers;

use App\Enums\DesignStatusEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\MOCKUP_LAYERS;
use App\Enums\OrderTypeEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductType;
use App\Enums\StorageDisksEnum;
use App\Events\OrderChangeDesign;
use App\Http\Requests\MoveTempImageRequest;
use App\Http\Requests\ReplaceDesignRequest;
use App\Library\UrlUploadedFile;
use App\Models\Campaign;
use App\Models\Design;
use App\Models\File;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\Store;
use App\Models\User;
use App\Rules\CheckExistsIdRule;
use App\Services\FileUploadService;
use App\Services\MediaService;
use App\Traits\ApiResponse;
use Cloudinary\Api\Exception\ApiError;
use Cloudinary\Cloudinary;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class UploadController extends Controller
{
    use ApiResponse;

    /**
     * Upload file to S3
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function __invoke(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'bail|required|mimes:jpeg,jpg,png,ico,zip,psd',
            'type' => 'required|string',
            'id' => 'integer'
        ]);

        $originalFile = $request->file('file');
        $type = $request->post('type');
        $showFileInfo = $request->boolean('show_file_info');

        $fileName = hash_file_name($originalFile);

        $userId = currentUser()->getUserId();
        $id = (int)$request->post('id');

        if ($id === 0) {
            $id = date('Y/m/d') . '/' . $userId;
            $type = 'tmp';
        }

        abort_if(!$type, 403);

        if ($type === 'page') {
            $id = date('Y/m/d') . '/' . $userId;
        }

        $folder = self::getFolder($type, $id);

        // Convert image to webp
        if ($type !== 'product_design') {
            convertImageToWebpAndUploadToS3($originalFile, $folder);
        }

        $path = $originalFile->storePubliclyAs(
            $folder,
            $fileName,
            StorageDisksEnum::DEFAULT
        );

        if (!$path) {
            return $this->errorResponse();
        }

        if (!$showFileInfo) {
            return $this->successResponse($path);
        }

        $data = [
            'path' => $path,
            'name' => $originalFile->getClientOriginalName(),
            'size' => $originalFile->getSize()
        ];

        return $this->successResponse($data);
    }

//     public static function uploadToCloudinary(UploadedFile $file, string $folder, string $fileName): ?string
//     {
//         $config = config('senprints.cloudinary_config');
//         $cloudinary = new Cloudinary($config);
//         $public_id = $folder . '/' . $fileName;
//         try {
//             $cloudinary->uploadApi()->upload(
//                 $file->getRealPath(),
//                 [
//                     'resource_type' => 'image',
//                     'public_id' => $public_id,
//                     'chunk_size' => 6000000, // 6MB
//                     'overwrite' => true
// //                    'type' => 'private'
//                 ]
//             );

//             return $public_id;
//         } catch (ApiError $e) {
//             logException($e);
//             return null;
//         }
//     }

    /**
     * @param string $file
     * @param string $folder
     * @param string $fileName
     * @return string|null
     */
    public static function uploadToCloudinaryFromS3(string $file, string $folder, string $fileName)
    {
        $config = config('senprints.cloudinary_config');
        $cloudinary = new Cloudinary($config);
        $public_id = $folder . '/' . $fileName;
        try {
            $cloudinary->uploadApi()->upload(
                $file,
                [
                    'resource_type' => 'image',
                    'public_id' => $public_id,
                    'chunk_size' => 6000000, // 6MB
//                    'type' => 'private'
                ]
            );

            return $public_id;
        } catch (ApiError $e) {
            self::logToDiscord('UploadController->uploadToCloudinaryFromS3 -> File Name: ' . $fileName . '. Message: ' . $e->getMessage() . ', File: ' . $e->getFile() . ', Line: ' . $e->getLine(), true);
            return null;
        }
    }

    public function uploadTemplateProductImg(Request $request): JsonResponse
    {
        $request->validate([
            'files' => 'bail|required|array',
            'files.*' => 'required|image',
            'type' => 'required|string',
            'mockup_id' => 'nullable|exists:file,id',
            'product_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product'),
            ],
        ]);

        $files = $request->file('files');
        $mockupId = $request->post('mockup_id');
        $productId = $request->post('product_id');
        $type = strtolower($request->post('type'));
        // $cdnMode = $request->post('cdn');
        $records = [];

        try {
            DB::beginTransaction();
            foreach ($files as $file) {
                $hash = md5_file($file);
                $hash16 = substr($hash, 0, 6);
                $fileName = $hash16 . '.' . $file->extension();
                $folder = 'p/' . $productId;

                if (in_array($type, ['glb', 'crop', 'shadow', 'color'])) {
                    $folder .= '/3d/' . $mockupId;
                }

                convertImageToWebpAndUploadToS3($file, $folder);
                $path = $file->storePubliclyAs(
                    $folder,
                    $fileName,
                    StorageDisksEnum::DEFAULT
                );
                $record = new File();
                $record->file_url = $path;
                // $record->file_url_2 = $fileCdnUrl;
                $record->file_name = $file->getClientOriginalName();
                $record->file_size = $file->getSize();
                $record->product_id = $productId;
                $record->type = $type;
                $record->save();

                //if type = 'thumbnail' then change product thumb_url
                if ($type === 'thumbnail') {
                    Product::query()
                        ->where('id', $productId)
                        ->update([
                            'thumb_url' => $path
                        ]);
                }

                $records[] = $record->toArray();
            }

            DB::commit();

            return response()->json([
                'images' => $records,
                'success' => true
            ]);
        } catch (Exception $exception) {
            DB::rollBack();
            return response()->json([
                'message' => $exception->getMessage(),
                'success' => true
            ]);
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadPSD(Request $request): JsonResponse
    {
        $request->validate([
            'file_name' => 'bail|required',
            'product_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product'),
            ],
        ]);
        $file = $request->post('file_name');
        // check file path is contains /tmp/
        if (Str::startsWith('tmp/', $file)) {
            return $this->errorResponse([
                'message' => 'File path is invalid'
            ]);
        }
        $productId = $request->post('product_id');
        // move file path to new folder
        $folder = 'p/' . $productId . '/psd';
        // get file name from file path
        $fileName = substr($file, strrpos($file, '/') + 1);
        // get file name without extension
        $fileName = substr($fileName, 0, strrpos($fileName, '.'));
        // move file path to new folder
        $newFilePath = $folder . '/' . $fileName . '.psd';
        // move file to new folder
        $newFilePath = saveTempFileAws($file, $newFilePath);
        // return new file path
        return $this->successResponse(['file_path' => $newFilePath]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws Exception
     */
    public function uploadStoreBanners(Request $request): JsonResponse
    {
        $request->validate([
            'files' => 'bail|required',
            'files.*' => 'required|image',
            'id' => 'integer',
            'is_sub_banner' => 'integer'
        ]);

        $files = $request->file('files');
        $storeId = $request->post('id');
        if (!Store::belongsToCurrentSeller($storeId)) {
            return $this->errorResponse('Store not found');
        }
        clearStoreCache($storeId);
        $data = [];
        array_map(function ($originalFile) use ($request, &$data) {
            $fileName = hash_file_name($originalFile);

            $id = (int)$request->post('id');
            $folder = self::getFolder('store', $id);

            // Convert image to webp
            convertImageToWebpAndUploadToS3($originalFile, $folder);

            $path = $originalFile->storePubliclyAs(
                $folder,
                $fileName,
                StorageDisksEnum::DEFAULT
            );

            if (!$path) {
                return $this->errorResponse();
            }
            $typeDetail = (int)$request->post('is_sub_banner') === 0 ? 'store_banner' : 'collection_banner';

            $record = new File([
                'file_url' => $path,
                'file_name' => $originalFile->getClientOriginalName(),
                'file_size' => $originalFile->getSize(),
                'type' => FileTypeEnum::BANNER,
                'store_id' => $id,
                'type_detail' => $typeDetail
            ]);
            $record->save();
            $data[] = $record;

            return $originalFile;
        }, $files);

        return $this->successResponse($data);
    }


    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadExpress2File(Request $request): JsonResponse
    {
        $file = $request->file('file');

        if (empty($file)) {
            return $this->errorResponse();
        }

        $mockupId = $request->post('mockup_id');
        $productId = $request->post('product_id');
        $layerType = $request->post('layer_type');

        $fileExt = strtolower($file->extension());

        $fileExt = '.' . $fileExt;

        // get hash of file to generate file name
        // todo: use $originalFile->hashName() instead of md5_file()
        $hash = md5($mockupId . now());

        // todo: add user name to file name to prevent override
        // path: uploads/artworks/2020/08/19/1fde2b417f50e8f.png
        $name = substr($hash, 0, 6);
        switch ($layerType) {
            case MOCKUP_LAYERS::COLOR:
                $name .= '_co';
                break;
            case MOCKUP_LAYERS::CROP:
                $name .= '_cr';
                break;
            case MOCKUP_LAYERS::SHADOW:
                $name .= '_sh';
                break;
            case MOCKUP_LAYERS::SHADOW_HEATHER:
                $name .= '_sdh';
                $shadowFileName = $request->post('shadow_file_name');
                if (!empty($shadowFileName)) {
                    $offset = strrpos($shadowFileName, '/') + 1;
                    $length = strrpos($shadowFileName, '_sh') - $offset;
                    $name = substr($shadowFileName, $offset, $length) . '_sdh';
                }
                break;
        }
        $fileName = $name . $fileExt;

        $folder = 'p/' . $productId;

        // $cdnMode = $request->post('cdn');
        // $fileUrl2 = null;
        // $fileMineType = $file->getMimeType();
        // if ($cdnMode && preg_match('#^image/#', $fileMineType)) {
        //     $fileUrl2 = self::uploadToCloudinary($file, $folder, $name);
        // }

        // Convert image to webp
        convertImageToWebpAndUploadToS3($file, $folder);

        $path = $file->storePubliclyAs(
            $folder,
            $fileName,
            StorageDisksEnum::DEFAULT
        );

        if ($path) {
            return $this->successResponse([
                'file_path' => $path,
                // 'file_path_2' => $fileUrl2
            ]);
        }

        return $this->errorResponse();
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function upload3DFile(Request $request): JsonResponse
    {
        $file = $request->file('file');

        if (empty($file)) {
            return $this->errorResponse();
        }

        $mockupId = $request->post('mockup_id');
        $productId = $request->post('product_id');
        $layerType = $request->post('layer_type');

        $fileExt = strtolower($file->extension());
        // remove GLB file extension
        $fileExt = ($fileExt === 'glb') ? '' : '.' . $fileExt;

        // get hash of file to generate file name
        // todo: use $originalFile->hashName() instead of md5_file()
        $hash = md5($mockupId . now());

        // todo: add user name to file name to prevent override
        // path: uploads/artworks/2020/08/19/1fde2b417f50e8f.png
        $name = substr($hash, 0, 6);
        switch ($layerType) {
            case MOCKUP_LAYERS::GLB:
                $name .= '_gb';
                break;
            case MOCKUP_LAYERS::COLOR:
                $name .= '_co';
                break;
            case MOCKUP_LAYERS::CROP:
                $name .= '_cr';
                break;
            case MOCKUP_LAYERS::SHADOW:
                $name .= '_sh';
                break;
            case MOCKUP_LAYERS::SHADOW_HEATHER:
                $name .= '_sdh';
                $shadowFileName = $request->post('shadow_file_name');
                if (!empty($shadowFileName)) {
                    $offset = strrpos($shadowFileName, '/') + 1;
                    $length = strrpos($shadowFileName, '_sh') - $offset;
                    $name = substr($shadowFileName, $offset, $length) . '_sdh';
                }
                break;
        }
        $fileName = $name . $fileExt;

        $folder = 'p/' . $productId;

        // $cdnMode = $request->post('cdn');
        // $fileUrl2 = null;
        // $fileMineType = $file->getMimeType();
        // if ($cdnMode && preg_match('#^image/#', $fileMineType)) {
        //     $fileUrl2 = self::uploadToCloudinary($file, $folder, $name);
        // }

        // Convert image to webp
        convertImageToWebpAndUploadToS3($file, $folder);

        $path = $file->storePubliclyAs(
            $folder,
            $fileName,
            StorageDisksEnum::DEFAULT
        );

        if ($path) {
            return $this->successResponse([
                'file_path' => $path,
                // 'file_path_2' => $fileUrl2
            ]);
        }

        return $this->errorResponse();
    }

    /**
     * Upload file to special folder by type
     *
     * @param string $type
     * @param $id
     * @return string
     */
    private static function getFolder(string $type, $id): string
    {
        switch ($type) {
            case 'user':
                $folder = 'u/' . currentUser()->getUserId();
                break;

            case 'product_design':
            case 'product':
                $folder = 'p/' . $id;
                break;

            case 'store':
                $folder = 's/' . $id;
                break;

            case 'tmp':
                $folder = 'tmp/' . $id;
                break;

            case 'page':
                $folder = 'page/' . $id;
                break;

            default:
                $folder = 'images/' . $id;
                break;
        }

        return $folder;
    }

    public function uploadCustomFont(Request $request): JsonResponse
    {
        $userId = currentUser()->getUserId();
        $fileSize = $request->input('file_size');
        $fontName = $request->input('font_name');
        $fileTempPath = $request->input('file_url');

        $fileNames = explode('/', $fileTempPath);
        $fileName = end($fileNames);
        $newFilePath = 'u/' . $userId . '/' . $fileName;
        try {
            $validateSize = validateFileUploadedSize($fileTempPath);
            if (!$validateSize['accept']) {
                return $this->errorResponse($validateSize['message']);
            }
            $newFilePath = saveTempFileAws($fileTempPath, $newFilePath);
            if (empty($newFilePath)) {
                return $this->errorResponse('Upload fonts failed.');
            }
            $cleanFileName = cleanString($fontName);

            $fontData = [
                'file_url' => $newFilePath,
                'file_size' => $fileSize,
                'file_name' => $cleanFileName,
                'seller_id' => $userId,
                'type' => FileTypeEnum::FONT
            ];

            File::query()->create($fontData);

            return $this->successResponse($fontData);
        } catch (Exception $exception) {
            return $this->errorResponse();
        }
    }

    /**
     * @param $directLink
     * @param $uploadPath
     * @return array|null
     */
    public static function uploadS3FromDirectLink($directLink, $uploadPath): ?array
    {
        if (isEnvTesting()) {
            return [
                'path'   => $directLink,
                'width'  => 0,
                'height' => 0
            ];
        }

        try {
            $uploadPath = rtrim($uploadPath, '/');
            // if user using Sen's mockup, regex to get info
            if (str_contains($directLink, 'img.cloudimgs.net')) {
                $pattern = '/p\/\d+.*$/';
                preg_match($pattern, $directLink, $matches);
                $pattern_2 = '/(\d+x\d+)/';
                preg_match($pattern_2, $directLink, $matches_2);

                $extractedString = $matches[0];
                $widthHeight = $matches_2[0];
                $widthHeight = explode('x', $widthHeight);
                return [
                    'path' => $extractedString,
                    'width' => $widthHeight[0],
                    'height' => $widthHeight[1],
                ];
            }

            $tmpFile = UrlUploadedFile::createFileFromUrl($directLink);

            if (!$tmpFile) {
                return null;
            }

            [$imgWidth, $imgHeight] = getimagesize($tmpFile);
            $name = $tmpFile->getClientOriginalName();
            $path = $tmpFile->storePubliclyAs(
                $uploadPath,
                $name,
                StorageDisksEnum::DEFAULT
            );
            silent(static function () use ($tmpFile) {
                @unlink($tmpFile);
            });
            return [
                'path' => $path,
                'width' => $imgWidth,
                'height' => $imgHeight,
            ];
        } catch (\Throwable $e) {
            return [];
        }
    }

    /**
     * @param $directLink
     * @param $uploadFullFilePath
     * @param string $fileType
     * @param bool $exception
     * @return array|string
     */
    public static function uploadFromDirectLink($directLink, $uploadFullFilePath, $fileType = 'image', $exception = false): array|string
    {
        try {
            $tmpFile = UrlUploadedFile::createFileFromUrl($directLink, $fileType, $exception);
            if (!$tmpFile) {
                return 'Url is invalid';
            }
            [$imgWidth, $imgHeight] = getimagesize($tmpFile);
            $path = $tmpFile->storePubliclyAs(
                pathinfo($uploadFullFilePath, PATHINFO_DIRNAME),
                pathinfo($uploadFullFilePath, PATHINFO_BASENAME),
                StorageDisksEnum::DEFAULT
            );
            silent(static function () use ($tmpFile) {
                @unlink($tmpFile);
            });
            return [
                'path' => $path,
                'width' => $imgWidth,
                'height' => $imgHeight,
            ];
        } catch (\Throwable $e) {
            graylogError($e->getMessage(), [
                'category' => 'uploadFromDirectLink',
                'directLink' => $directLink,
                'uploadFullFilePath' => $uploadFullFilePath,
                'trace' => $e->getTraceAsString()
            ]);
            return $exception ? $e->getMessage() : '';
        }
    }

    /**
     * @param MoveTempImageRequest $request
     * @return JsonResponse
     */
    public function moveTempFile(MoveTempImageRequest $request): JsonResponse
    {
        $productId = $request->input('product_id');
        $tempPath = $request->input('temp_path');
        $user = currentUser();
        $currentUserId = $user->getUserId();
        $fileExists = Storage::disk(StorageDisksEnum::DEFAULT)->exists($tempPath);
        if (!$fileExists) {
            return $this->errorResponse('Files not exists', 403);
        }
        $pathRoot = 'p/' . $productId;
        if (str_contains($tempPath, $pathRoot)) {
            return $this->successResponse(['new_path' => $tempPath]);
        }
        $productExists = Product::query()
            ->onSellerConnection($user)
            ->where([
                'id' => $productId,
                'seller_id' => $currentUserId
            ])
            ->exists();
        if (!$productExists) {
            return $this->errorResponse('Product not exists', 403);
        }
        $arrayPath = explode('/', $tempPath);
        $fileName = end($arrayPath);
        $newPath = $pathRoot . '/' . $fileName;
        saveTempFileAws($tempPath, $newPath);
        return $this->successResponse(['new_path' => $newPath]);
    }

    /**
     * @param ReplaceDesignRequest $request
     * @return JsonResponse
     */
    public function adminReplaceOrderDesign(ReplaceDesignRequest $request): JsonResponse
    {
        $file = $request->file('file');
        $oldFileId = $request->input('old_file_id');
        $oldTable = $request->input('old_table');
        $orderId = $request->input('order_id');
        $orderProductId = $request->input('order_product_id');
        $productId = $request->input('product_id');
        $fileBase64 = $request->input('file_base64');
        $order = Order::query()->with('seller')->firstWhere('id', $orderId);
        if (!$order) {
            return $this->errorResponse('Order not exists.');
        }
        $seller = $order->seller;
        switch ($oldTable) {
            case 'design':
                $updateRow = 'file_url';
                $query = Design::query()->where('id', $oldFileId);
                break;
            default:
                $updateRow = 'file_url_2';
                $query = File::query()
                    ->when(!in_array($order->type, [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA], true), fn ($query) => $query->onSellerConnection($seller))
                    ->where('id', $oldFileId);
                break;
        }

        $oldFile = $query->first();

        if (!$oldFile) {
            return $this->errorResponse('Old file not exists. Try again.');
        }

        $order = Order::query()->firstWhere('id', $orderId);
        $orderProduct = OrderProduct::query()->firstWhere('id', $orderProductId);

        if (!$orderProduct) {
            return $this->errorResponse('Order product not exists. Try again.');
        }

        $campaignId = $orderProduct->campaign_id;
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->firstWhere('id', $campaignId);

        if ($oldTable === 'file' && !empty($campaignId)) {
            $newFilePath = 'p/' . $campaignId;
        } else {
            $newFilePath = 'o/' . $orderId;
        }
        if (empty($fileBase64)) {
            $newFileName = hash_file_name($file);
            $newUploadedPath = $file->storePubliclyAs(
                $newFilePath,
                $newFileName,
                StorageDisksEnum::DEFAULT
            );
        } else {
            $newUploadedPath = FileUploadService::uploadFileBase64($fileBase64, $newFilePath);
        }

        if ($campaign !== null && $campaign->product_type === ProductType::CAMPAIGN_EXPRESS && $order !== null) {
            $newDesignData = [
                'id' => generateUUID(),
                'file_url' => $newUploadedPath,
                'type' => DesignTypeEnum::PRINT,
                'print_space' => $oldFile->print_space,
                'seller_id' => $order->seller_id,
                'campaign_id' => $oldFile->campaign_id,
                'product_id' => $productId,
                'order_id' => $orderId,
                'order_product_id' => $orderProductId,
            ];

            Design::query()
                ->where([
                    'order_id' => $orderId,
                    'order_product_id' => $orderProductId,
                ])
                ->delete();

            $newFile = Design::query()->insert($newDesignData);
            // register new cloudMockup id
            $newDesignCMId = ExpressCampaignController::registerIdCloudMockup($newUploadedPath);
            $newId = 'id=' . $newDesignCMId;
            $orderProduct->thumb_url = preg_replace('/id=\w+/', $newId, $orderProduct->thumb_url);
            $orderProduct->save();

            OrderChangeDesign::dispatch($order->id, imgUrl($newUploadedPath));

            return $this->successResponse([
                'new_file' => $newFile,
                'design' => $newDesignCMId
            ]);
        }

        $fillData = [
            $updateRow => $newUploadedPath,
            'status' => DesignStatusEnum::ACTIVE
        ];

        if ($oldTable === 'file') {
            $fillData['status'] = FileStatusEnum::ACTIVE;
        }

        if ($oldTable === 'file') {
            $oldFile->status = FileStatusEnum::INACTIVE;
        } else {
            $oldFile->status = DesignStatusEnum::INACTIVE;
        }

        $oldFile->save();
        if ($oldTable === 'file') {
            $newFile = File::query()->when(!in_array($order->type, [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA], true), fn ($query) => $query->onSellerConnection($seller))->create($oldFile->replicate()->fill($fillData)->toArray());
        } else {
            $newFile = $oldFile->replicate()->fill($fillData)->save();
        }

        OrderChangeDesign::dispatch($order->id, imgUrl($newUploadedPath));

        return $this->successResponse([
            'new_file' => $newFile
        ]);
    }

    // clone from adminReplaceOrderDesign
    /**
     * @param ReplaceDesignRequest $request
     * @param int $campaignId
     * @return JsonResponse
     */
    public function adminReplaceCampaignDesign(Request $request, int $campaignId): JsonResponse
    {
        $request->validate([
            'file' => ['required', 'file', 'image'],
            'old_file_id' => ['required'],
            'seller_id' => ['required'],
        ]);
        $seller = User::find($request->get('seller_id'));
        $file = $request->file('file');
        $oldFileId = $request->input('old_file_id');
        $oldFile = File::query()
            ->onSellerConnection($seller)
            ->where('id', $oldFileId)
            ->where('campaign_id', $campaignId)
            ->first();
        if (empty($oldFile)) {
            return $this->errorResponse('Old file not exists. Try again.');
        }

        $newFileName = hash_file_name($file);
        $newFilePath = 'p/' . $campaignId;
        $newUploadedPath = $file->storePubliclyAs(
            $newFilePath,
            $newFileName,
            StorageDisksEnum::DEFAULT
        );

        $fillData['file_url_2'] = $newUploadedPath;
        $fillData['status'] = FileStatusEnum::ACTIVE;
        $oldFile->status = FileStatusEnum::INACTIVE;
        $oldFile->save();
        File::query()
            ->onSellerConnection($seller)
            ->create($oldFile->replicate()->fill($fillData)->toArray());

        return $this->successResponse([]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadNewImageOrderFulfill(Request $request): JsonResponse
    {
        $request->validate(['file' => 'required|image']);

        $currentUserId = currentUser()->getUserId();
        $seller = User::query()->find($currentUserId);
        $orderId = $request->input('order_id');
        $oldFileId = $request->input('file_id');
        $productTemplateId = $request->input('template_product_id');
        $printSpace = $request->input('print_space');
        $type = $request->input('type');
        $newFile = $request->file('file');

        if (!Order::query()->where([
            'id' => $orderId,
            'seller_id' => $currentUserId,
        ])->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])->exists()) {
            return $this->errorResponse('Order not found');
        }

        $queryFile = File::query()
            ->where([
            'id' => $oldFileId
        ]);

        if (!empty($oldFileId) && !$queryFile->exists()) {
            return $this->errorResponse('File not found');
        }

        $templateProduct = Product::query()->firstWhere([
            'id' => $productTemplateId,
            'product_type' => ProductType::TEMPLATE
        ]);

        if ($templateProduct !== null && $type === 'designs') {
            $templatePrintSpaces = json_decode($templateProduct->print_spaces);
            $index = array_search($printSpace, array_column($templatePrintSpaces, 'name'));
            $filePrintSpace = $templatePrintSpaces[$index];
            $imageInfo = getimagesize($newFile);

            if (!empty($filePrintSpace) && !empty($imageInfo)) {
                [$imgWidth, $imgHeight] = $imageInfo;
                $printSpaceWidth = (float) $filePrintSpace->width;
                $printSpaceHeight = (float) $filePrintSpace->height;

                $dpi = !empty($filePrintSpace->DPI) ? $filePrintSpace->DPI : 300;
                $minDpi = !empty($filePrintSpace->minDPI) ? $filePrintSpace->minDPI : $dpi / 2;

                // check image quality / DPI
                if ($imgWidth / $printSpaceWidth < $minDpi / $dpi || $imgHeight / $printSpaceHeight < $minDpi / $dpi) {
                    $this->isOrderValid = false;
                    $minWidth = $printSpaceWidth * $minDpi / $dpi;
                    $minHeight = $printSpaceHeight * $minDpi / $dpi;

                    return $this->errorResponse('Design is low quality. Expect ' . $printSpaceWidth . 'x' . $printSpaceHeight . ' px , minimum ' . $minWidth . 'x' . $minHeight . ' px');
                }

                // check design size
                if ($templateProduct->full_printed && $templateProduct->full_printed !== ProductPrintType::HANDMADE && abs($imgWidth / $printSpaceWidth - $imgHeight / $printSpaceHeight)) {
                    return $this->errorResponse('Image size is invalid. Expect ' . $filePrintSpace->width . ' x ' . $filePrintSpace->height . ' px',);
                }
            }
        }

        $newFileName = hash_file_name($newFile);
        $newFilePath = 'o/' . $orderId;
        $newUploadedPath = $newFile->storePubliclyAs(
            $newFilePath,
            $newFileName,
            StorageDisksEnum::DEFAULT
        );


        $updateData = [
            'file_url' => $newUploadedPath,
            'file_url_2' => $newUploadedPath,
        ];

        if (!empty($oldFileId)) {
            $queryFile->update($updateData);
        }

        return $this->successResponse(
            ['new_file_path' => $newUploadedPath],
            'Upload success'
        );
    }
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadFileFBAOrder(Request $request): JsonResponse
    {
        $request->validate([
            'file' => 'required|mimes:pdf|max:5000',
        ]);

        $currentUserId = currentUser()->getUserId();
        $orderId = $request->post('order_id');
        $oldFileId = $request->post('file_id');
        $newFile = $request->file('file');
        if (!empty($orderId)) {
            if (!Order::query()->where([
                'id' => $orderId,
                'seller_id' => $currentUserId,
            ])->whereIn('type', [OrderTypeEnum::FBA])->exists()) {
                return $this->errorResponse('Order not found');
            }
        }

        $queryFile = File::query()->where([
            'id' => $oldFileId
        ]);

        if (!empty($oldFileId) && !$queryFile->exists()) {
            return $this->errorResponse('File not found');
        }

        $newFileName = hash_file_name($newFile);
        $prefix = 'fba_file';
        if (!empty($orderId)) {
            $prefix = $orderId;
        }
        $newFilePath = 'o/' . $prefix;
        $newUploadedPath = $newFile->storePubliclyAs(
            $newFilePath,
            $newFileName,
            StorageDisksEnum::DEFAULT
        );


        $updateData = [
            'file_url' => $newUploadedPath,
            'file_url_2' => $newUploadedPath,
        ];

        if (!empty($oldFileId)) {
            $queryFile->update($updateData);
        }

        return $this->successResponse(
            ['new_file_path' => $newUploadedPath],
            'Upload success'
        );
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getPreSignedUrl(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'fileName' => 'required|string',
            'filePath' => 'nullable|string',
        ]);
        if ($validator->fails()) {
            return $this->errorResponse($validator->getMessageBag());
        }
        $expiresMinutes = 20;
        try {
            $fileName = $request->input('fileName');
            $filePath = $request->input('filePath');
            $fileName = basename($fileName);
            if (empty($filePath)) {
                $filePath = 'tmp/' . $fileName;
            } else if (!str_contains($filePath, $fileName)) {
                return $this->errorResponse('Invalid file path');
            }
            $storage = Storage::disk(StorageDisksEnum::DEFAULT);
            if (method_exists($storage, 'temporaryUploadUrl')) {
                $response = $storage->temporaryUploadUrl(
                    $filePath,
                    now()->addMinutes($expiresMinutes)
                );
                return $this->successResponse([
                    'filePath' => $filePath,
                    'preSignedUrl' => $response['url'],
                    'fileUrl' => s3Url($filePath),
                    'version' => 'v2',
                ]);
            }
            $client = $storage->getDriver()->getAdapter()->getClient();
            $command = $client->getCommand('PutObject', [
                'Bucket' => config('filesystems.disks.' . StorageDisksEnum::DEFAULT . '.bucket'),
                'Key' => $filePath,
            ]);
            $response = $client->createPresignedRequest($command, "+$expiresMinutes minutes");
            return $this->successResponse([
                'filePath' => $filePath,
                'preSignedUrl' => (string) $response->getUri(),
                'fileUrl' => s3Url($filePath),
                'version' => 'v1',
            ]);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function getFileMedia(Request $request, MediaService $mediaService): JsonResponse
    {
        $params = $request->validate([
            'type' => 'required|string|in:design,mockup',
            'keyword' => 'nullable|string',
            'perPage' => 'nullable|int',
            'page' => 'nullable|int',
        ]);

        $files = $mediaService->getFile($params);

        return $this->successResponse($files);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function uploadFileMedia(Request $request, MediaService $mediaService): JsonResponse
    {
        try {
            $request->validate([
                'file' => 'required|image',
                'type' => 'required|string|in:design,mockup',
            ]);

            $newUploadedPath = $mediaService->uploadFile($request->all());

            return $this->successResponse(
                ['new_file_path' => $newUploadedPath],
                'Upload success'
            );
        }
        catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param $message
     * @param bool $mention
     * @return void
     */
    private static function logToDiscord($message, bool $mention = false): void
    {
        logToDiscord($message, 'upload_file_failed');
    }
}

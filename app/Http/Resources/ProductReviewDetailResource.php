<?php

namespace App\Http\Resources;

use App\Enums\ProductReviewFileTypeEnum;
use App\Enums\ProductReviewTypeEnum;
use App\Models\ProductReview;
use App\Models\User;
use App\Traits\GetStoreDomain;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;


/**
 * Class ProductReviewDetailResource
 *
 * @mixin ProductReview
 */
class ProductReviewDetailResource extends JsonResource
{
    use GetStoreDomain;

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        $user = currentUser();
        $offset = 7;
        if ($user->isSeller()) {
            $offset = User::query()->find($user->getUserId())?->utc_offset ?? 7;
        }
        $timezone = sprintf('Etc/GMT%s%d', $offset > 0 ? '-' : '+', abs($offset));
        $created_at = $this->created_at->setTimezone($timezone)->format('M d, Y H:i');
        $updated_at = $this->finished_at?->setTimezone($timezone)->format('M d, Y H:i');
        $productSize = null;
        $productColor = null;
        $productName = null;
        $productUrl = null;
        $orderNumber = null;
        $orderId = null;
        $storeDomain = null;
        $customerId = null;
        $customerName = null;
        $customerEmail = null;
        $customerCountry = null;
        if ($this->type === ProductReviewTypeEnum::NORMAL) {
            $productOptions = [];
            if (isset($this->orderProduct->options)) {
                $productOptions = json_decode($this->orderProduct->options, true);
            }
            $productSize = !empty($productOptions['size']) ? $productOptions['size'] : null;
            $productColor = !empty($productOptions['color']) ? $productOptions['color'] : null;
            $productName = isset($this->orderProduct) ? $this->orderProduct->product_name : '';
            $productUrl = isset($this->orderProduct) ? $this->orderProduct->product_url : '';
            $orderNumber = optional($this->order)->order_number;
            $orderId = $this->order_id;
            $storeDomain = optional($this->order)->store_domain;
            $customerId = $this->customer_id;
            $customerName = optional($this->order)->customer_name;
            $customerEmail = optional($this->order)->customer_email;
            $customerCountry = optional($this->order)->country;
        } else {
            $reviewOptions = (array)json_decode($this->review_options);
            $productSize = !empty($reviewOptions['size']) ? $reviewOptions['size'] : null;
            $productColor = !empty($reviewOptions['color']) ? $reviewOptions['color'] : null;
            $productName = optional($this->template)->name;
            $storeDomain = !empty($this->store) ? self::getDomainByStoreId($this->store->id) : null;
            $customerName = $this->customer_name;
            $customerEmail = $this->customer_email;
            $customerCountry = $this->country;
        }

        $files = $this->productReviewFiles->map(function ($file) {
            return [
                'type' => $file->type,
                'thumb' => $file->thumb_url ?? $file->url,
                'src' => $file->url,
            ];
        });

        $keywords = $this->productReviewKeywords->map(function ($each) {
            return [
                'id' => $each->id,
                'keyword' => $each->keyword,
            ];
        });

        if ($files->isEmpty() && !empty($this->orderProduct->thumb_url)) {
            $files->prepend([
                'type' => ProductReviewFileTypeEnum::IMAGE,
                'thumb' => $this->orderProduct->thumb_url,
                'src' => $this->orderProduct->thumb_url,
            ]);
        }

        return [
            'id' => $this->id,
            'customer_id' => $customerId,
            'customer_name' => $customerName,
            'customer_email' => $customerEmail,
            'customer_country' => $customerCountry,
            'average_rating' => $this->average_rating,
            'campaign_id' => $this->campaign_id,
            'campaign_name' => optional($this->campaign)->name,
            'store_id' => $this->store_id,
            'store_name' => optional($this->store)->name,
            'store_domain' => $storeDomain,
            'seller_id' => $this->seller_id,
            'seller_name' => optional($this->seller)->name,
            'seller_email' => optional($this->seller)->email,
            'order_id' => $orderId,
            'order_number' => $orderNumber,
            'product_id' => $this->product_id,
            'product_name' => $productName,
            'product_url' => $productUrl,
            'product_size' => $productSize,
            'product_color' => $productColor,
            'comment' => $this->comment,
            'files' => $files,
            'score' => $this->score,
            'allow_sharing' => $this->allow_sharing,
            'status' => $this->status,
            'created_at' => $created_at,
            'updated_at' => $updated_at,
            'keywords' => $keywords->isEmpty() ? [] : $keywords,
            'assignee' => $this->staff,
            'support' => $this->support,
        ];
    }
}

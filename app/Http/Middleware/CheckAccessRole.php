<?php

namespace App\Http\Middleware;

use App\Enums\SystemRole;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;

class CheckAccessRole
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @param $permission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $permission)
    {
        $auth = Auth::user();
        abort_if(!$auth, 403);
        try {
            $payload = get_payload_current_token();

            // Check role
            if ($auth->hasRole(SystemRole::ADMIN)) {
                return $next($request);
            }

            // Fix case current staff (but not admin) use login as seller
            // Check current auth use switch account in admin dashboard
            if (isset($payload['access_role']) && $payload['service_name'] === 'admin') {
                // Check current auth is guard admin
                if (Auth::guard('admin')->check()) {
                    return $next($request);
                }
            }

            $permissions = is_array($permission)
                ? $permission
                : explode('|', $permission);
            // Check permission
            if (!isset($payload['access_role']) || is_null($payload['access_role'])) {
                foreach ($permissions as $per) {
                    if ($auth->hasPermissionTo($per)) {
                        return $next($request);
                    }
                }

            }

            // check if access_role is null
            abort_if(is_null($payload['access_role']), 403);

            $roleHasPermission = false;
            // Check permission of account switch
            $role = Role::findByName($payload['access_role'], $payload['guard_name']);
            foreach ($permissions as $per) {
                if ($role->hasPermissionTo($per)) {
                    $roleHasPermission = true;
                }
            }
            abort_if(!$roleHasPermission, 403);

            return $next($request);
        } catch (\Tymon\JWTAuth\Exceptions\TokenExpiredException $e) {
            return \response()->json([
                'success' => false,
                'error' => 'token_expired'
            ], 401);
        } catch (\Tymon\JWTAuth\Exceptions\TokenInvalidException $e) {
            return \response()->json([
                'success' => false,
                'error' => 'token_invalid'
            ], 401);
        } catch (\Tymon\JWTAuth\Exceptions\JWTException $e) {
            return \response()->json([
                'success' => false,
                'error' => 'token_absent'
            ], 406);
        } catch (\Exception $e) {
            return \response()->json([
                'success' => false,
                'error' => "Permission's authorization check failed"
            ], 500);
        }
    }
}

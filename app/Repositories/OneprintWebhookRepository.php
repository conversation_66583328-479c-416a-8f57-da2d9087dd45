<?php

namespace App\Repositories;

use App\Enums\EnvironmentEnum;
use App\Enums\FulfillmentStatusEnum;
use App\Enums\SupplierEnum;
use App\Models\ProductVariant;
use App\Providers\FulfillAPI\Oneprint\Model\ResponseModel;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class OneprintWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING => [
            ResponseModel::UNPAID,
            ResponseModel::INCOMPLETE
        ],
        FulfillmentStatusEnum::PROCESSING => [
            ResponseModel::PROCESSING,
        ],
        FulfillmentStatusEnum::FULFILLED => [
            ResponseModel::SHIPPED,
        ],
        FulfillmentStatusEnum::REJECTED => [
            ResponseModel::CANCELED,
        ]
    ];

    public function __construct($data, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $data;
    }

    /**
     * @return $this
     * @throws \Throwable
     */
    public function crawlHandle(): self
    {
        if (count($this->data) === 0) {
            return $this;
        }
        $orderData = $this->data;
        $data = [];
        $this->setSenPrintsOrderId(data_get($orderData, 'source_id'));
        $this->setSupplierOrderId(data_get($orderData, 'id'));
        $orderStatus = data_get($orderData, 'status');
        $this->setFulfillmentStatus($this->fulfillStatusMapping($this->arrStatusMapping, $orderStatus));
        $items = $this->getLineItems($orderData);
        $tracking = data_get($orderData, 'shipments');
        if (!empty($tracking)) {
            $trackData = reset($tracking);
            $data = [
                'tracking' => [
                    'tracking_code' => data_get($trackData, 'tracking_number') ?? '',
                    'shipping_carrier' => data_get($trackData, 'vendor_name') ?? '',
                    'tracking_url' => data_get($trackData, 'tracking_url') ?? '',
                ]
            ];
        }

        if (empty($items)) {
            $data['no_items'] = true;
        } else {
            $data['items'] = $items;
        }
        $this->setFulfillments([$data]);
        return $this;
    }

    /**
     * @return $this
     */
    public function webhookHandle(): self
    {
        $webhookType = Arr::get($this->data, 'event_name');
        $orderData = data_get($this->data, 'data', []);
        $this->setSupplierOrderId(
            Arr::get($orderData, 'order_id')
        );
        $data = [];
        $data['no_items'] = true;
        if ($webhookType === 'update_status') {
            $orderStatus = Arr::get($orderData, 'status');
            $this->setFulfillmentStatus($this->fulfillStatusMapping($this->arrStatusMapping, $orderStatus));
        } else if ($webhookType === 'update_tracking') {
            $tracking = data_get($orderData, 'tracking_data');
            if (!empty($tracking)) {
                $data = [
                    'tracking' => [
                        'tracking_code' => data_get($tracking, 'tracking_number') ?? '',
                        'shipping_carrier' => data_get($tracking, 'vendor_name') ?? '',
                        'tracking_url' => data_get($tracking, 'tracking_url') ?? '',
                    ]
                ];
            }
            $this->setFulfillments([$data]);
        }
        return $this;
    }

    /**
     * @param $orderData
     * @return array|mixed
     */
    private function getLineItems($orderData)
    {
        $items = data_get($orderData, 'items');
        return $items;
    }
}

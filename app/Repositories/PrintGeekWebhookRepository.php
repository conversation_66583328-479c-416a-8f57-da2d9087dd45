<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Services\TrackingService;
use Illuminate\Support\Arr;

class PrintGeekWebhookRepository extends GeneralWebhookRepository
{
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING => [
            'created'
        ],
        FulfillmentStatusEnum::PROCESSING => [
            'received',
            'printed',
            'picked'
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'shipped',
        ],
    ];

    public function webhookHandle(): self
    {
        $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, Arr::get($this->data, 'status'));
        $this->setFulfillmentStatus($fulfillStatus);

        $orderId = Arr::get($this->data, 'order_id', Arr::get($this->data, 'orderId'));
        $this->setSupplierOrderId($orderId);
        $this->setSenPrintsOrderId($orderId);

        $trackingService = TrackingService::instance();
        $carrier = Arr::get($this->data, 'carrier');
        $tracking_code = Arr::get($this->data, 'tracking', Arr::get($this->data, 'trackingCode'));
        $tracking_url = Arr::get($this->data, 'trackingLink');
        $shipping_carrier = null;
        if (!empty($tracking_code)) {
            $shipping_carrier = $this->getShippingCarrier($carrier);
            $carrier = $trackingService->detectCarrier($tracking_code, null, null, $tracking_url);
            if ($carrier) {
                $shipping_carrier = $trackingService->carriers()->where('courierCode', $carrier)->value('courierName');
            }
        }
        $fulfillments = [
            [
                'no_items' => true,
                'tracking' => [
                    'tracking_code'    => $tracking_code,
                    'shipping_carrier' => $shipping_carrier,
                    'tracking_url'     => $tracking_url,
                ]
            ]
        ];
        $this->setFulfillments($fulfillments);

        return $this;
    }

    public function crawlHandle(): self
    {
        $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, Arr::get($this->data, 'status'));
        $events = data_get($this->data, 'events');
        $orderId = data_get($this->data, 'fulfill_order_id');
        if (empty($events)) {
            return $this;
        }
        $this->setFulfillmentStatus($fulfillStatus);
        $latestData = $this->handleLatestEvents($events);
        $status = data_get($latestData, 'status');
        $trackingCode = '';
        $shippingCarrier = '';
        $fulfillments =  [
            'no_items' => true,
        ];
        if ($status === 'shipped') {
            $trackingCode = data_get($latestData, 'event.tracking_number');
            $shippingCarrier = data_get($latestData, 'event.carrier');
        }
        if (!empty($trackingCode)) {
            $fulfillments['tracking'] = [
                'tracking_code'    => $trackingCode,
                'shipping_carrier' => $shippingCarrier,
            ];
        }
        $this->setFulfillments([$fulfillments]);
        $this->setSupplierOrderId($orderId);
        $this->setSenPrintsOrderId($orderId);
        // don't have
        return $this;
    }

    public function handleLatestEvents (array $events) {
        $event = reset($events);
        $status = data_get($event, 'action');
        $orderIds = data_get($event, 'affected_items');
        $orderId = reset($orderIds);
        return [
            'status' => $status,
            'order_id' => $orderId,
            'event' => $event,
        ];
    }
}

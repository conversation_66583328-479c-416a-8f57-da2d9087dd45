<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use App\Services\TrackingService;
use Illuminate\Support\Arr;

class FlashShipWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    protected array $data;

    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PROCESSING => [
            'IN_PRODUCING',
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'COMPLETED'
        ],
        FulfillmentStatusEnum::REJECTED   => [
            'CANCELED', 'REQUEST_CANCEL', 'REFUNDED', 'REQUEST_REFUND'
        ],
        FulfillmentStatusEnum::PENDING    => [
            'CONFIRMED',
        ],
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        /*
        {
            "id": 1485880,
            "order_code": "WYZTDUU3L",
            "partner_id": 247,
            "partner_order_id": "22109010",
            "shipment_method": 1,
            "first_name": "<PERSON>",
            "last_name": "W Fischmann",
            "email": "<EMAIL>",
            "phone": "+13154149347",
            "province": "NY",
            "country": "US",
            "region": "NY",
            "address_line_1": "107  Ivanhoe Ave",
            "address_line_2": "",
            "city": "Camillus",
            "zip": "13031",
            "note": null,
            "status": "COMPLETED",
            "created": "2024-06-17T21:53:26+07:00",
            "products": [
                {
                    "lineId": 1,
                    "quantity": 1,
                    "variant_sku": "2XL/CHARCOAL",
                    "variant_id": 11876,
                    "front_print_url": "https://img.cloudimgs.net/rx/4500x5400,ofmt_png,c_2/s2/img/p/166989486/043b47bef1a5b3304153c3940c248bbfc4240850.png?nocache=14",
                    "back_print_url": null,
                    "left_print_url": null,
                    "right_print_url": null,
                    "neck_print_url": null,
                    "mockup_back": null,
                    "mockup_front": "https://img.cloudimgs.net/rx/1000/s4/l_p:3105952:7d1c3bf281e373b1/fl_cutter,fl_layer_apply/u_p:3105952:f1c6cd79d9b72d3a/co_rgb:555B5D,e_colorize:100/fl_layer_apply/l_p:3105952:662974_sh/fl_layer_apply/u_p:3105952:184d580923f94160/fl_layer_apply/c_thumb,w_1280/f_jpg/v1/p/166989471/e59ada86b82c92a16d959ea98c10f663/t/2c74e5f7de6f6f45.jpg",
                    "mockup_left": null,
                    "mockup_right": null,
                    "mockup_neck": null,
                    "note": "",
                    "product_type_enum": "SHIRT"
                }
            ],
            "quantity": 0,
            "tracking_number": "9400109105459569482834",
            "reject_user": "",
            "reject_note": null,
            "reject_type": null,
            "reject_request_date": null,
            "reject_confirm_date": null,
            "total_fee": 12.1,
            "carrier": "USPS"
        }
        */
        $this->data = $response;
    }

    public function webhookHandle(): self
    {
        // don't have
        return $this;
    }

    public function crawlHandle(): self
    {
        $this->setFulfillmentStatus(
            $this->fulfillStatusMapping($this->arrStatusMapping, $this->data['status'])
        );

        $this->setSupplierOrderId(
            Arr::get($this->data, 'order_code')
        );

        $this->setSenPrintsOrderId(
            Arr::get($this->data, 'partner_order_id')
        );

        $items = array_map(
            static fn($v) => ['sku' => $v['variant_id']],
            data_get($this->data, 'products', [])
        );
        $carrier = Arr::get($this->data, 'carrier');
        $tracking_number = Arr::get($this->data, 'tracking_number');
        $trackingService = TrackingService::instance();
        $shipping_carrier = null;
        if (!empty($tracking_number)) {
            $shipping_carrier = $this->getShippingCarrier($carrier);
            $carrier = $trackingService->detectCarrier($tracking_number);
            if ($carrier) {
                $shipping_carrier = $trackingService->carriers()->where('courierCode', $carrier)->value('courierName');
            }
        }
        $data = [
            'tracking' => [
                'tracking_code'    => $tracking_number,
                'shipping_carrier' => $shipping_carrier,
                'tracking_url'     => '',
            ]
        ];

        if (empty($items)) {
            $data['no_items'] = true;
        } else {
            $data['items'] = $items;
        }

        $this->setFulfillments([$data]);

        return $this;
    }
}

<?php

namespace App\Traits;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Throwable;

trait QueryTrait
{
    /**
     * @param $connection
     * @param $limit
     * @return array
     * @throws \Throwable
     */
    public function getLockProcessByConnection($connection, $limit)
    {
        return DB::connection($connection)->select('SELECT b.id as id,
            b.info as `query`,
            b.command,
            b.USER as `user`,
            b.host,
            b.db,
            TIMESTAMPDIFF(SECOND,a.trx_started, now()) as "time_running",
            a.trx_id,
            a.trx_state,
            a.trx_started,
            a.trx_rows_modified
            FROM information_schema.innodb_trx a, information_schema.processlist b
            WHERE a.trx_mysql_thread_id = b.id AND TIMESTAMPDIFF(SECOND,a.trx_started, now()) >= ' . $limit . ' ORDER BY trx_started;'
        );
    }

    /**
     * @param string $connectionName
     * @param $limit
     * @return array
     * @throws Throwable
     */
    public function getLongQueries(string $connectionName, $limit = 300): array
    {
        try {
            $queries = DB::connection($connectionName)
                ->table('information_schema.PROCESSLIST')
                ->select([
                    'id AS ID',
                    'USER AS USER',
                    'HOST AS HOST',
                    'db AS DB',
                    'command AS Command',
                    'TIME AS Sec',
                    'state AS State',
                    'info AS QUERY',
                ])
                ->whereNotIn('command', ['Sleep', 'Slave_IO', 'Slave_SQL', 'Binlog Dump'])
                ->where('TIME', '>', $limit)
                ->orderBy('state', 'DESC')
                ->orderBy(DB::raw('TIME + 0'), 'ASC')
                ->get();
            $result = [];
            foreach ($queries as $query) {
                if (empty($query->QUERY)) {
                    continue;
                }
                $result[] = [
                    'id' => $query->ID,
                    'user' => $query->USER,
                    'host' => $query->HOST,
                    'db' => $query->DB,
                    'time' => $query->Sec,
                    'mins' => round($query->Sec / 60, 2),
                    'query' => $query->QUERY,
                    'connection' => $connectionName,
                ];
            }
            return $result;
        } catch (Throwable $e) {
            return [];
        }
    }

    /**
     * @param string $connectionName
     * @return void
     */
    public function checkLongQueries(string $connectionName): void
    {
        $queries = DB::connection($connectionName)
            ->table('information_schema.PROCESSLIST')
            ->select([
                'id AS ID',
                'USER AS USER',
                'HOST AS HOST',
                'db AS DB',
                'command AS Command',
                'TIME AS Sec',
                'state AS State',
                'info AS QUERY',
            ])
            ->whereNotIn('command', ['Sleep', 'Slave_IO', 'Slave_SQL', 'Binlog Dump'])
            ->where('TIME', '>', $connectionName === 'singlestore' ? 60 : self::QUERY_DURATION_THRESHOLD)
            ->orderBy('state', 'DESC')
            ->orderBy(DB::raw('TIME + 0'), 'ASC')
            ->get();
        foreach ($queries as $query) {
            if (empty($query->QUERY)) {
                continue;
            }
            $cacheKey = "long_query_{$connectionName}_{$query->ID}";
            if (Cache::has($cacheKey)) {
                $message = "Connection: ``{$connectionName}``\nID: ``$query->ID``\nDuration: {$query->Sec} seconds\n";
                logToDiscord($message, 'long-queries');
            } else {
                $message = "Connection: ``{$connectionName}``\nID: ``$query->ID``\nUser: ``{$query->USER}``\nHost: ``{$query->HOST}``\nDuration: {$query->Sec} seconds```sql\n{$query->QUERY}\n```";
                logToDiscord($message, 'long-queries');
                graylogError("Long running query", [
                    'category' => 'long-queries',
                    'connection' => $connectionName,
                    'query_id' => $query->ID,
                    'duration' => $query->Sec,
                    'user' => $query->USER,
                    'host' => $query->HOST,
                    'query' => $query->QUERY,
                ]);
                Cache::put($cacheKey, $message, now()->addMinutes(5));
            }
        }
    }
}

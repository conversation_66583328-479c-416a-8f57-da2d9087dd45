<?php

namespace App\Services;

use App\Enums\AppDeploymentEnum;
use App\Enums\CurrencyEnum;
use App\Models\Order;
use Modules\OrderService\Models\RegionOrders;

class StripeEmbeddedService
{
    protected RegionOrders|Order $order;

    public const ZERO_DECIMAL_CURRENCIES = ['BIF', 'CLP', 'DJF', 'GNF', 'JPY', 'KMF', 'KRW', 'MGA', 'PYG', 'RWF', 'UGX', 'VUV', 'XAF', 'XOF', 'XPF'];

    public const SPECIAL_CURRENCIES = ['ISK', 'HUF', 'TWD', 'UGX'];

    public function __construct(Order|RegionOrders $order)
    {
        $this->order = $order;
    }

    /**
     * @return bool
     */
    public function isEnabled(): bool
    {
        return AppDeploymentEnum::isDevelopment();
    }

    /**
     * @return bool
     */
    public function isSupportEmbeddedService(): bool
    {
        return $this->isEnabled() && !in_array(strtoupper($this->order->country), ['US', 'VN']);
    }

    /**
     * @return string
     */
    public function getCurrencyCode(): string
    {
        return $this->order->currency_code ?? CurrencyEnum::USD;
    }

    /**
     * @see https://docs.stripe.com/currencies
     * @return float|int
     */
    public function getChargeAmount(): float|int
    {
        $amount = $this->order->total_amount;
        if ($this->isEnabled()) {
            $currency = strtoupper($this->getCurrencyCode());
            if (in_array($currency, self::ZERO_DECIMAL_CURRENCIES, true)) {
                return $amount;
            }
            // Need rounded because not accept fraction
            if (in_array($currency, self::SPECIAL_CURRENCIES, true)) {
                return ceil($amount) * 100;
            }
        }
        return $amount * 100;
    }

    /**
     * @param $amount
     * @return float
     */
    public function reverseChargeAmount($amount): float
    {
        if ($this->isEnabled()) {
            $currency = strtoupper($this->getCurrencyCode());
            if (in_array($currency, self::ZERO_DECIMAL_CURRENCIES, true)) {
                return (float)$amount;
            }
        }
        return (float)($amount / 100);
    }
}

<?php

namespace App\Services;

use App\Contracts\TrackingServiceContract;
use App\Enums\TrackingServiceEnum;
use App\Enums\TrackingStatusEnum;
use App\Models\TrackingStatus;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class TrackOneTwoThree implements TrackingServiceContract
{
    // Main status
    public const INIT = 'INIT';
    public const NO_RECORD = 'NO_RECORD';
    public const INFO_RECEIVED = 'INFO_RECEIVED';
    public const IN_TRANSIT = 'IN_TRANSIT';
    public const WAITING_DELIVERY = 'WAITING_DELIVERY';
    public const DELIVERY_FAILED = 'DELIVERY_FAILED';
    public const ABNORMAL = 'ABNORMAL';
    public const DELIVERED = 'DELIVERED';
    public const EXPIRED = 'EXPIRED';

    // Sub status
    public const SUB_INFO_RECEIVED_01 = 'INFO_RECEIVED_01';
    public const SUB_IN_TRANSIT_01 = 'IN_TRANSIT_01';
    public const SUB_IN_TRANSIT_02 = 'IN_TRANSIT_02';
    public const SUB_IN_TRANSIT_03 = 'IN_TRANSIT_03';
    public const SUB_IN_TRANSIT_04 = 'IN_TRANSIT_04';
    public const SUB_IN_TRANSIT_05 = 'IN_TRANSIT_05';
    public const SUB_IN_TRANSIT_06 = 'IN_TRANSIT_06';
    public const SUB_IN_TRANSIT_07 = 'IN_TRANSIT_07';
    public const SUB_IN_TRANSIT_08 = 'IN_TRANSIT_08';
    public const SUB_WAITING_DELIVERY_01 = 'WAITING_DELIVERY_01';
    public const SUB_WAITING_DELIVERY_02 = 'WAITING_DELIVERY_02';
    public const SUB_WAITING_DELIVERY_03 = 'WAITING_DELIVERY_03';
    public const SUB_DELIVERED_01 = 'DELIVERED_01';
    public const SUB_DELIVERED_02 = 'DELIVERED_02';
    public const SUB_DELIVERED_03 = 'DELIVERED_03';
    public const SUB_DELIVERED_04 = 'DELIVERED_04';
    public const SUB_DELIVERY_FAILED_01 = 'DELIVERY_FAILED_01';
    public const SUB_DELIVERY_FAILED_02 = 'DELIVERY_FAILED_02';
    public const SUB_DELIVERY_FAILED_03 = 'DELIVERY_FAILED_03';
    public const SUB_DELIVERY_FAILED_04 = 'DELIVERY_FAILED_04';
    public const SUB_ABNORMAL_01 = 'ABNORMAL_01';
    public const SUB_ABNORMAL_02 = 'ABNORMAL_02';
    public const SUB_ABNORMAL_03 = 'ABNORMAL_03';
    public const SUB_ABNORMAL_04 = 'ABNORMAL_04';
    public const SUB_ABNORMAL_05 = 'ABNORMAL_05';
    public const SUB_ABNORMAL_06 = 'ABNORMAL_06';
    public const SUB_ABNORMAL_07 = 'ABNORMAL_07';
    public const SUB_ABNORMAL_08 = 'ABNORMAL_08';

    public const SUCCESS = '00000';
    public const USER_ERROR = 'A0001';
    public const USER_LOGIN_ERROR = 'A0200';
    public const USER_NOT_EXIST = 'A0201';
    public const USER_ACCOUNT_LOCKED = 'A0202';
    public const USER_ACCOUNT_INVALID = 'A0203';
    public const TOKEN_INVALID_OR_EXPIRED = 'A0230';
    public const TOKEN_ACCESS_FORBIDDEN = 'A0231';
    public const AUTHORIZED_ERROR = 'A0300';
    public const ACCESS_UNAUTHORIZED = 'A0301';
    public const PARAM_ERROR = 'A0400';
    public const PARAM_IS_NULL = 'A0410';
    public const SYSTEM_EXECUTION_ERROR = 'B0001';
    public const SYSTEM_EXECUTION_TIMEOUT = 'B0100';
    public const SYSTEM_ORDER_PROCESSING_TIMEOUT = 'B0100'; // duplicate code
    public const FLOW_LIMITING = 'B0210';
    public const SYSTEM_RESOURCE_ERROR = 'B0300';
    public const SYSTEM_RESOURCE_EXHAUSTION = 'B0310';
    public const INTERFACE_NOT_EXIST = 'C0113';
    public const SERVICE_DETAIL_MSG_ERROR = 'D0000';
    public const TOO_MANY_REQUESTS = 'A0706';

    protected $apiEndpoint;
    protected $secretKey;

    public function __construct()
    {
        $this->apiEndpoint = config('services.tracking.track123.api_endpoint');
        $this->secretKey = config('services.tracking.track123.secret_key');
    }

    /**
     * @param $trackingNumbers
     * @return array|mixed|null
     * @throws \Throwable
     */
    public static function register($trackingNumbers)
    {
        return (new self)->postRequest('import', $trackingNumbers);
    }

    /**
     * @param $trackingNumbers
     * @return array|mixed|null
     * @throws \Throwable
     */
    public static function deleteTrack($trackingNumbers)
    {
        return (new self)->postRequest('delete', $trackingNumbers);
    }

    /**
     * @param $trackingNumbers
     * @return array
     * @throws \Throwable
     */
    public static function getTrackInfo($trackingNumbers)
    {
        $result = [
            'accepted' => [],
            'rejected' => []
        ];
        $response = (new self)->postRequest('query', $trackingNumbers);
        if (empty($response)) {
            return $result;
        }
        if (!empty($response['accepted'])) {
            $result['accepted'] = data_get($response, 'accepted.content', []);
        }
        if (!empty($response['rejected'])) {
            $result['rejected'] = $response['rejected'];
        }
        $result['service'] = TrackingServiceEnum::TRACK_123;
        return $result;
    }

    /**
     * @param $url
     * @param $trackingNumbers
     * @return array
     */
    public function handle($url, $trackingNumbers): array
    {
        $url = !str_starts_with($url, '/') ? ('/' . $url) : $url;
        $trackingNumbers = is_array($trackingNumbers) ? $trackingNumbers : [$trackingNumbers];
        if (!empty($trackingNumbers)) {
            if ($url === '/query') {
                return [$url, ['trackNos' => $trackingNumbers]];
            }
            $trackingNumbers = array_map(function ($trackingNumber) use ($url) {
                if ($url === '/import') {
                    $trackingNumber = (array) $trackingNumber;
                    $carrier = self::detectCarrier($trackingNumber['tracking_code'], $trackingNumber['shipping_carrier'], $trackingNumber['supplier_name'] ? strtolower(trim($trackingNumber['supplier_name'])) : null, $trackingNumber['tracking_url'] ?? null);
                    $remark = '';
                    if (!empty($trackingNumber['shipping_carrier'])) {
                        $remark .= 'Carrier: ' . $trackingNumber['shipping_carrier'] . PHP_EOL;
                    }
                    if (!empty($trackingNumber['tracking_url'])) {
                        $remark .= 'URL: ' . $trackingNumber['tracking_url'] . PHP_EOL;
                    }
                    $data = [
                        'trackNo' => $trackingNumber['tracking_code'],
                        'remark' => $remark
                    ];
                    $data['courierCode'] = null;
                    if ($carrier) {
                        $data['courierCode'] = $carrier;
                    }
                    if (data_get($trackingNumber, 'order.order_number')) {
                        $data['orderNo'] = data_get($trackingNumber, 'order.order_number');
                    } else if (data_get($trackingNumber, 'order_id')) {
                        $data['orderNo'] = data_get($trackingNumber, 'order_id');
                    }
                    if (data_get($trackingNumber, 'fulfilled_at')) {
                        $data['shipTime'] = data_get($trackingNumber, 'fulfilled_at');
                    }
                    return $data;
                }
                return ['trackNo' => $trackingNumber];
            }, $trackingNumbers);
        }
        return [$url, $trackingNumbers];
    }

    /**
     * @param $url
     * @param array $data
     * @param bool $handleExcept
     * @return array|mixed|null
     * @throws \Throwable
     */
    public function postRequest($url, array $data = [], bool $handleExcept = false)
    {
        if (!app()->isProduction()) {
            return null;
        }
        if (!$handleExcept) {
            [$url, $data] = $this->handle($url, $data);
        }
        $response = Http::asJson()
            ->withoutVerifying()
            ->withUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36')
            ->withHeaders([
                'Track123-Api-Secret' => $this->secretKey,
            ])
            ->post($this->apiEndpoint . $url, $data);
        if ($response->failed()) {
            if ($response->status() === 429 || $response->json('code') === self::TOO_MANY_REQUESTS) {
                return null;
            }
            graylogError('[Track123] Post request failed to ' . $url, [
                'category' => 'tracking_status_logs',
                'url' => $url,
                'response_status' => $response->status(),
                'response_data' => $response->json(),
                'response_header' => json_encode($response->headers(), JSON_THROW_ON_ERROR),
            ]);
            logToDiscord('[Track123] Post request failed to ' . $url . ' - Status: ' . $response->status(), 'tracking_status_logs', true);
            return null;
        }
        $response = $response->json();
        return !empty($response['data']) ? $response['data'] : $response;
    }

    /**
     * @return array
     */
    public static function statusesMapWithTrackingStatusEnum(): array
    {
        return [
            self::NO_RECORD => TrackingStatusEnum::NOTFOUND,
            self::INFO_RECEIVED => TrackingStatusEnum::INFO_RECEIVED,
            self::IN_TRANSIT => TrackingStatusEnum::TRANSIT,
            self::DELIVERY_FAILED => TrackingStatusEnum::TRANSIT,
            self::WAITING_DELIVERY => TrackingStatusEnum::OUT_FOR_DELIVERY,
            self::EXPIRED => TrackingStatusEnum::EXPIRED,
            self::DELIVERED => TrackingStatusEnum::DELIVERED,
            self::ABNORMAL => TrackingStatusEnum::EXCEPTION,
            self::INIT => TrackingStatusEnum::NEW
        ];
    }

    /**
     * @param $status
     * @return string|null
     */
    public static function convertTrackingStatus($status): ?string
    {
        $statuses = self::statusesMapWithTrackingStatusEnum();
        return $statuses[$status] ?? null;
    }

    /**
     * @param $code
     * @param string $message
     * @return bool
     */
    public static function isSkipCheck($code, $message = ''): bool
    {
        return false;
    }

    /**
     * @param $code
     * @param string $message
     * @return bool
     */
    public static function isNeedRegister($code, $message = ''): bool
    {
        return $code === self::PARAM_ERROR && !empty($message) && str_ends_with($message, 'not registered');
    }

    /**
     * @return Collection
     */
    public static function carriers(): Collection
    {
        try {
            $carriersFilePath = resource_path('/json/carriers/track123.json');
            if (!File::exists($carriersFilePath)) {
                return collect();
            }
            $carriers = File::get($carriersFilePath);
            $carriers = json_decode($carriers, true, 512, JSON_THROW_ON_ERROR);
            return collect($carriers);
        } catch (\Throwable $e) {
            return collect();
        }
    }

    /**
     * @return Collection
     */
    public static function detectPatterns(): Collection
    {
        return collect([
            [
                'carrier_name' => ['spring'],
                'carrier_key' => "springgds",
                'domain' => ['mailingtechnology.com'],
                'tracking_code_prefix' => ['3SDOKC'],
            ],
            [
                'carrier_name' => ['upsmailinnovations'],
                'carrier_key' => "ups-mail-innovations",
                'domain' => ['upsmailinnovations.com']
            ],
            [
                'carrier_name' => ['ups'],
                'carrier_key' => "ups",
                'domain' => ['ups.com', 'ups-mi.net', 'wwwapps.ups.com']
            ],
            [
                'carrier_name' => ['usps', 'genericstandard'],
                'carrier_key' => "usps",
                'domain' => ['usps.com', 'tools.usps.com'],
                'tracking_code_prefix' => ['92', '94'],
                'tracking_code_length' => [26]
            ],
            [
                'carrier_name' => ['royalmail'],
                'carrier_key' => "royal-mail",
                'domain' => ['royalmail.com'],
            ],
            [
                'carrier_name' => ['bartolini', 'brtbartolini'],
                'carrier_key' => "bartolini",
                'domain' => ['brt.it'],
            ],
            [
                'carrier_name' => ['gls'],
                'carrier_key' => 'gls',
                'suppliers' => ['jondo', 'textildruck'],
                'domain' => ['gso.com'],
            ],
            [
                'carrier_name' => ['asendiausa'],
                'carrier_key' => "asendiausa",
                'suppliers' => ['monsterdigital', 'dreamship', 'gooten', 'jondo', 'printlogistic', 'printlogisticv2'],
                'domain' => ['asendiausa.com', 'a1.asendiausa.com']
            ],
            [
                'carrier_name' => ['asendia'],
                'carrier_key' => 'asendia',
                'suppliers' => ['monsterdigital', 'dreamship', 'gooten', 'jondo', 'printlogistic', 'printlogisticv2'],
                'domain' => ['asendiausa.com', 'a1.asendiausa.com']
            ],
            [
                'carrier_name' => ['fastway'],
                'carrier_key' => 'fastway',
                'suppliers' => ['jondo'],
                'domain' => ['fastway.com.au'],
            ],
            [
                'carrier_name' => ['dhlpaket', 'dhlwarenpost'],
                'carrier_key' => "dhl-germany",
                'domain' => ['dhl.com'],
                'tracking_code_prefix' => ['LE'],
            ],
            [
                'carrier_name' => ['dhlexpress'],
                'carrier_key' => 'dhl',
                'domain' => ['mydhl.express.dhl'],
            ],
            [
                'carrier_name' => ['dhl', 'dhlecommerce', 'dhlecs', 'dhlgm', 'genericstandard', 'dhlecs'],
                'carrier_key' => "dhlglobalmail",
                'suppliers' => ['gooten', 'dreamship', 'gearment', 'moteefe', 'textildruck', 'printforia', 'printlogistic', 'optondemand', 'monsterdigital'],
                'domain' => ['webtrack.dhlglobalmail.com', 'dhl.com'],
                'tracking_code_prefix' => ['GM', '00'],
                'tracking_code_length' => [20, 22]
            ],
            [
                'carrier_name' => ['fedex'],
                'carrier_key' => 'fedex',
                'domain' => ['fedex.com']
            ],
            [
                'carrier_name' => ['yunexpress'],
                'carrier_key' => 'yunexpress',
                'domain' => ['yunexpress.com'],
                'tracking_code_prefix' => ['YT'],
            ],
            [
                'carrier_name' => ['chitchats'],
                'carrier_key' => 'chitchats',
                'domain' => ['chitchats.com'],
            ],
            [
                'carrier_name' => ['gtagsm'],
                'domain' => ['gtagsm.com'],
                'carrier_key' => 'gtagsm',
                'tracking_code_prefix' => ['GR'],
                'tracking_code_length' => [19]
            ],
            [
                'carrier_name' => ['canadapost'],
                'carrier_key' => "canada-post",
                'suppliers' => ['printgeek'],
                'tracking_code_prefix' => ['201506'],
                'tracking_code_length' => [16]
            ],
            [
                'carrier_name' => ['dpd'],
                'suppliers' => ['printlogisticv2'],
                'carrier_key' => "dpd",
                'domain' => ['dpd.com.pl']
            ]
        ]);
    }

    /**
     * @param $trackingNumber
     * @param $carrier
     * @param $supplier
     * @param $trackingUrl
     * @return \Closure|false|null
     */
    public static function detectCarrier($trackingNumber, $carrier = null, $supplier = null, $trackingUrl = null)
    {
        if (empty($trackingNumber)) {
            return null;
        }
        $carrier = str_replace(' ', '', strtolower(trim($carrier)));
        if (!empty($carrier)) {
            $carriers = self::carriers();
            if ($carriers->isNotEmpty()) {
                $carrierFilter = $carriers->first(fn($item) => str_replace('-', '', strtolower($item['courierCode'])) === $carrier);
                if ($carrierFilter) {
                    return data_get($carrierFilter, 'courierCode');
                }
            }
        }
        $supplier = str_replace(' ', '', strtolower(trim($supplier)));
        $trackingUrl = trim($trackingUrl);
        $patterns = self::detectPatterns();
        $carrierFilter = $patterns->first(function ($pattern) use ($trackingNumber, $carrier, $supplier, $trackingUrl) {
            $carrierMatch = !empty($pattern['carrier_name']) && !empty($carrier) && (in_array($carrier, $pattern['carrier_name'], true) || Str::startsWith($carrier, $pattern['carrier_name']));
            $supplierMatch = !empty($pattern['suppliers']) && in_array($supplier, $pattern['suppliers'], true);
            $trackingCodePrefixMatch = !empty($pattern['tracking_code_prefix']) && collect($pattern['tracking_code_prefix'])->first(fn($prefix) => str_starts_with($trackingNumber, $prefix));
            $trackingCodeLength = !empty($pattern['tracking_code_length']) && in_array(strlen($trackingNumber), $pattern['tracking_code_length'], true);
            $domainMatch = false;
            if (!empty($pattern['domain']) && !empty($trackingUrl)) {
                $parseUrl = parse_url($trackingUrl);
                if ($parseUrl && !empty($parseUrl['host'])) {
                    $domain = str_replace('www.', '', $parseUrl['host']);
                    $domainMatch = in_array($domain, $pattern['domain'], true);
                }
            }
            if ($carrier === 'usps' && $trackingCodePrefixMatch && str_starts_with($trackingNumber, 91) && $trackingCodeLength && strlen($trackingNumber) === 21) {
                return true;
            }
            if (!empty($pattern['suppliers'])) {
                return $supplierMatch && ($carrierMatch || $domainMatch || $trackingCodePrefixMatch);
            }
            return $carrierMatch || $domainMatch || $trackingCodePrefixMatch;
        });
        return data_get($carrierFilter, 'carrier_key');
    }

    /**
     * @param $trackingCodes
     * @return array|false
     */
    public static function registerOrderTracking($trackingCodes)
    {
        try {
            $insertCodes = [];
            $updateCodes = [];
            $orderProducts = $trackingCodes->toArray();
            $register = self::register($orderProducts);
            if (empty($register['rejected']) && empty($register['accepted'])) {
                graylogInfo('[Track123] Register tracking number failed', [
                    'category' => 'tracking_status_logs',
                    'tracking_codes' => $trackingCodes,
                    'response' => $register,
                ]);
                return false;
            }
            if (!empty($register['rejected'])) {
                foreach ($register['rejected'] as $rejectedCode) {
                    if ($rejectedCode['error']['code'] === self::PARAM_ERROR) {
                        continue;
                    }
                    graylogInfo('[Track123] Register tracking number failed - Number: ' . $rejectedCode['trackNo'] . ' - Code: ' . $rejectedCode['error']['code'] . ' - Message: ' . $rejectedCode['error']['msg'], [
                        'category' => 'tracking_status_logs',
                        'tracking_code' => $rejectedCode['trackNo'],
                    ]);
                    $trackingStatus = TrackingStatus::query()->where([
                        'tracking_code' => $rejectedCode['trackNo'],
                    ])->first();
                    $updateCodes[] = [
                        [
                            'tracking_code' => $rejectedCode['trackNo']
                        ],
                        [
                            'tracking_status' => $trackingStatus->status ?? TrackingStatusEnum::NOTFOUND,
                            'tracking_service' => TrackingServiceEnum::TRACK_123
                        ]
                    ];
                }
            }

            if (!empty($register['accepted'])) {
                foreach ($register['accepted'] as $acceptedCode) {
                    $code = TrackingService::getObjectTrackingCode($trackingCodes, $acceptedCode['number']);
                    if (empty($code)) {
                        continue;
                    }
                    $insertCodes[$code->tracking_code] = [
                        [
                            'tracking_code' => $code->tracking_code,
                            'order_number' => $code->order->order_number ?? $code->order_id,
                        ],
                        [
                            'order_id' => $code->order_id ?? $code->order->order_number,
                            'status' => TrackingStatusEnum::NEW,
                            'shipping_carrier' => $acceptedCode['courierCode'],
                            'tracking_service' => TrackingServiceEnum::TRACK_123
                        ]
                    ];
                    $updateCodes[] = [
                        [
                            'tracking_code' => $code->tracking_code
                        ],
                        [
                            'tracking_status' => TrackingStatusEnum::NEW,
                            'tracking_service' => TrackingServiceEnum::TRACK_123
                        ]
                    ];
                }
            }
            return [$insertCodes, $updateCodes];
        } catch (\Throwable $e) {
            return [[], []];
        }
    }

    /**
     * @param $request
     * @return bool
     */
    public function verifySignature($request): bool
    {
        try {
            $verifyData = $request->input('verify');
            if (empty($verifyData) || !is_array($verifyData)) {
                return false;
            }
            $signature = data_get($verifyData, 'signature');
            $timestamp = data_get($verifyData, 'timestamp');
            if (empty($signature) || empty($timestamp)) {
                return false;
            }
            return $signature === hash_hmac('sha256', $timestamp, config("services.tracking.track123.secret_key"));
        } catch (\Exception $e) {
            logToDiscord('[Track123] Failed to verify signature, message: ' . $e->getMessage(), 'tracking_status_logs');
            return false;
        }
    }
}

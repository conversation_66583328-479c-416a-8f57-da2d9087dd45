<?php

namespace App\Services;

use App\Console\Commands\ScanExpiringDomains;
use App\Enums\InactiveEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Models\IndexProduct;
use App\Models\IndexUserCleanSettings;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserLog;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class InactiveService
{
    public static function generateToken(): string
    {
        $currentTime = round_timestamp(now()->clone());
        $string = Str::random(40);
        return $string . $currentTime;
    }

    public static function getLastScanToken($type): ?string
    {
        return match ($type) {
            InactiveEnum::CAMPAIGN => Cache::get('last_inactive_campaign_token'),
            InactiveEnum::LAST_LOGIN => Cache::get('last_inactive_last_login_token'),
            default => null,
        };
    }

    public static function shouldDo($jsonData, string $key): bool
    {
        $action = $jsonData[$key] ?? false;
        if (!is_bool($action)) {
            $action = strtolower(trim($action));
            if ($action == 'true') {
                $action = true;
            } elseif ($action == 'false') {
                $action = false;
            } else {
                $action = false;
            }
        }

        return $action;
    }

    public static function getActiveSystemConfig($systemConfigKey)
    {
        return SystemConfig::query()
            ->select('json_data')
            ->where('key', $systemConfigKey)
            ->where('status', 1)
            ->first();
    }

    public static function getAppointMail($jsonData): ?string
    {
        if (!isset($jsonData['appoint_mail']) || trim($jsonData['appoint_mail']) === "") {
            return null;
        }

        return trim($jsonData['appoint_mail']);
    }

    public static function getSystemConfigLastUserId($jsonData, $cacheKey): int
    {
        if (!isset($jsonData[$cacheKey]) || trim($jsonData[$cacheKey]) === "") {
            return 0;
        }

        return (int)$jsonData[$cacheKey];
    }

    public static function getAppointSellerIds($jsonData): array
    {
        $sellerIdsString = trim($jsonData['seller_ids'] ?? '');

        return empty($sellerIdsString)
            ? []
            : array_map('intval', explode(',', $sellerIdsString));
    }

    public static function getLimitAppointSellerIds($sellerIds, $lastUserId, $limit): array
    {
        return User::query()
            ->select('id', 'email')
            ->where('role', '!=', UserRoleEnum::CUSTOMER)
            ->whereIn('id', $sellerIds)
            ->where('id', '>', $lastUserId)
            ->orderBy('id')
            ->limit($limit)
            ->get()
            ->pluck('email', 'id')
            ->toArray();

    }

    public static function getLimitedInactiveCampaignSellerIds(int $lastUserId, int $limit): array
    {
        // 21/03/24: ref a Thắng: Nếu seller đã ra sales rồi, không kể thời gian nào, bỏ qua luôn
        return User::query()
            ->select('id', 'email')
            ->where('role', '!=', UserRoleEnum::CUSTOMER)
            ->where('is_deleted', 0)
            // thêm relation orders() trong user
            ->whereDoesntHave('orders', function ($q) {
                $q->whereNotNull('paid_at');
            })
            ->where('id', '>', $lastUserId)
            ->whereNotIn('status', UserStatusEnum::getLimitedStatuses(false))
            ->orderBy('id')
            ->limit($limit)
            ->get()
            ->pluck('email', 'id')
            ->toArray();
    }

    public static function getUserIdBigger(int $lastUserId, int $limit): array
    {
        return User::query()->select('id', 'email')
            ->where('role', '!=', UserRoleEnum::CUSTOMER)
            ->where('is_deleted', 0)
            ->where('id', '>', $lastUserId)
            ->whereNotIn('status', UserStatusEnum::getLimitedStatuses(false))
            ->orderBy('id')
            ->limit($limit)
            ->get()
            ->pluck('email', 'id')
            ->toArray();

    }

    public static function getLimitedSellerIds(int $lastUserId, int $limit): array
    {
        return User::query()
            ->select('id', 'email')
            ->where('is_deleted', 0)
            ->where('role', '!=', UserRoleEnum::CUSTOMER)
            ->whereNotIn('status', UserStatusEnum::getLimitedStatuses(false))
            ->where('id', '>', $lastUserId)
            ->orderBy('id')
            ->limit($limit)
            ->get()
            ->pluck('email', 'id')
            ->toArray();
    }

    public static function generateProcessId(): string
    {
        $timestamp = now()->clone()->timestamp;
        return Str::random(5) . $timestamp;
    }

    public static function getUserCleanSetting(int $userId, string $type)
    {
        return IndexUserCleanSettings::query()
            ->where('seller_id', $userId)
            ->where('type', $type)
            ->firstOrFail();
    }

    public static function newUserIdsHaveTypeInactive($userIds, $type): array
    {
        $userCleanSettingsIds = IndexUserCleanSettings::query()
            ->select('seller_id')
            ->whereIn('seller_id', $userIds)
            ->where('type', $type)
            ->get()
            ->toArray();

        $usersAlreadyHaveCleanSettings = array_column($userCleanSettingsIds, 'seller_id');
        return array_values(array_diff($userIds, $usersAlreadyHaveCleanSettings));
    }


    public static function getLimitCleanSettings(string $type, int $lastCleanSettingId = 0, $excludeUserIds = [], int $limit = 100): Collection
    {
        return IndexUserCleanSettings::query()
            ->select('id', 'seller_id', 'clean_at', 'token', 'sent_at')
            ->where('id', '>', $lastCleanSettingId)
            ->where('type', '=', $type)
            ->whereNotIn('seller_id', $excludeUserIds)
            ->limit($limit)
            ->orderBy('id')
            ->get();
    }

    public static function insertNewUsersCleanSettings($newUserIdsChecked, $type, $token): bool
    {
        $days = isEnvLocalOrDev() ? 8 : 30;
        $cleanAt = now()->clone()->addDays($days);

        foreach ($newUserIdsChecked as $id => &$mail) {
            $mail = [
                'seller_id' => $id,
                'clean_at' => $cleanAt,
                'token' => $token,
                'type' => $type
            ];
        }

        return IndexUserCleanSettings::query()->insert($newUserIdsChecked);
    }

    public static function configInactiveCampaignMail($userId, $userEmail, string $token, $days = 30): array
    {
        $baseUrl = isEnvLocalOrDev() ? 'https://seller-v2.dev.senprints.net' : 'https://seller.senprints.com/';
        $dataSendMailLog = [
            'sellerId' => $userId,
        ];
        $checkCampaignUrl = $baseUrl . '/campaigns?token_inactive_campaign=' . $token;
        return [
            'to' => $userEmail,
            'template' => 'seller.inactive_campaign',
            'data' => [
                'base_url' => $baseUrl,
                'subject' => 'Important: Extend Your Campaign Within ' . $days . ' Days to Avoid Deletion',
                'check_campaign_url' => $checkCampaignUrl,
                'token' => $token
            ],
            'sendMailLog' => $dataSendMailLog,
            'hash' => gen_unique_hash()
        ];
    }

    public static function appointConfigMail(string $userEmail, string $type, string $token): array
    {
        $baseUrl = isEnvLocalOrDev() ? 'https://seller-v2.dev.senprints.net' : 'https://seller.senprints.com';
        $userId = isEnvLocalOrDev() ? 111264 : 201291446;
        $dataSendMailLog = [
            'sellerId' => $userId,
        ];
        $randomToken = $token;
        $checkCampaignUrl = $baseUrl . '/campaigns?token_inactive_campaign=' . $randomToken;
        return [
            'to' => $userEmail,
            'template' => 'seller.inactive_campaign',
            'data' => [
                'base_url' => $baseUrl,
                'subject' => 'Important: APPOINT TESTING MAIL ' . $type,
                'check_campaign_url' => $checkCampaignUrl,
                'token' => $randomToken
            ],
            'sendMailLog' => $dataSendMailLog,
            'hash' => gen_unique_hash()
        ];
    }

    public static function send1stWarningMail(array $usersIds, $type, $token): void
    {
        switch ($type) {
            case 'inactive_campaign':
                foreach ($usersIds as $id => $mail) {
                    $config = self::configInactiveCampaignMail($id, $mail, $token);
                    sendEmail($config);
                }
                break;
            case 'inactive_last_login':
                foreach ($usersIds as $id => $mail) {
                    $config = self::configInactiveLastLoginMail($id, $mail, $token);
                    sendEmail($config);
                }
                break;
        }
    }

    public static function configInactiveLastLoginMail($userId, $userEmail, string $token, $days = 30): array
    {
        $baseUrl = isEnvLocalOrDev() ? 'https://seller-v2.dev.senprints.net' : 'https://seller.senprints.com';
        $dataSendMailLog = [
            'sellerId' => $userId,
        ];
        $loginUrl = $baseUrl . '/auth/login?token_inactive_last_login=' . $token;
        return [
            'to' => $userEmail,
            'template' => 'seller.inactive_last_login',
            'data' => [
                'base_url' => $baseUrl,
                'subject' => 'Important: Log In Within 30 Days to Prevent Account Deletion',
                'login_url' => $loginUrl,
                'token' => $token
            ],
            'sendMailLog' => $dataSendMailLog,
            'hash' => gen_unique_hash()
        ];
    }

    public static function getUser($userId)
    {
        return User::query()->where('id', $userId)->first();
    }

    public static function getCheckingDay($cleanAt, $day): Carbon
    {
        return Carbon::parse($cleanAt)->clone()->subDays($day);
    }

    public static function checkSentAtTime($sendAt): bool
    {
        if (empty($sendAt)) {
            return true;
        }
        $sendAt = Carbon::parse($sendAt);

        return !$sendAt->isToday();
    }

    public static function checkCleanAtTime($cleanAt): int
    {
        $now = Carbon::now()->clone();
        $cleanAt = Carbon::parse($cleanAt)->clone();
        return $now->diffInDays($cleanAt, false);
    }

    public static function getMonthAgoDatetime($checkingDay, $month): string
    {
        return $checkingDay->clone()->subMonths($month)->toDateTimeString();
    }

    public static function deleteCampaign($campaignIds): void
    {
        $now = now()->clone();
        Product::query()
            ->whereIn('id', $campaignIds)
            ->update(['is_deleted' => 1, 'deleted_at' => $now, 'sync_status' => 0, 'slug' => null]);

        IndexProduct::query()
            ->whereIn('id', $campaignIds)
            ->update(['is_deleted' => 1, 'deleted_at' => $now, 'slug' => null]);
    }

    public static function getExcludeUserIds($type, $limit): array
    {
        $excludeUserIds = [];
        $lastScanToken = self::getLastScanToken($type);
        if (!empty($lastScanToken)) {
            $excludeUserIds = self::getUserIdWithTokens($lastScanToken, $type, $limit);
        }
        return $excludeUserIds;
    }

    public static function getUserIdWithTokens($token, $type, $limit): array
    {
        return IndexUserCleanSettings::query()
            ->select('seller_id')
            ->Where('token', '=', $token)
            ->where('type', $type)
            ->limit($limit)
            ->get()
            ->pluck('seller_id')
            ->toArray();
    }

    public static function deleteInactiveUserLastLogin($userId, $sixMonthAgoDateTime, $isDelete = false): bool
    {
        $lastLogin = UserLog::query()
            ->where('user_id', $userId)
            ->max('created_at');

        if ((!$lastLogin || $lastLogin < $sixMonthAgoDateTime) && $isDelete) {
            $result = User::query()->where('id', $userId)->update(['status' => UserStatusEnum::DELETED]);
            return $result > 0;
        }

        return false;
    }

    public static function deleteInactiveCampaignsByCondition1($userId, $sixMonthAgoDateTime, $count, $shouldDelete = false): array
    {
        $exampleCampaignIds = [];
        //condition 1: get all campaign which have not been visited in 6 months -> inactive campaign
        Product::query()
            ->select('id')
            ->inactiveCampaignByCondition1($userId, $sixMonthAgoDateTime)
            ->chunk($count, function ($campaigns) use (&$exampleCampaignIds, &$shouldDelete) {
                $campaignIds = $campaigns->pluck('id')->toArray();
                $exampleCampaignIds[] = $campaignIds;
                if ($shouldDelete) {
                    self::deleteCampaign($campaignIds);
                }
            });
        $exampleCampaignIds = array_merge(...$exampleCampaignIds);
        $total = count($exampleCampaignIds);
        $exampleCampaignIds = array_slice($exampleCampaignIds, 0, 20);

        return [
            'total' => $total,
            'exampleCampaignIds' => $exampleCampaignIds
        ];
    }

    public static function deleteInactiveCampaignsByCondition2($userId, $threeMonthAgoDateTime, $sixMonthAgoDateTime, $count, $shouldDelete = false): array
    {
        //condition 2: get all campaign have visited in 6 months but have no paid_at in 3 months -> inactive campaign
        $visitedCampaignIds = Product::query()
            ->select('id')
            ->where('seller_id', '=', $userId)
            ->where('is_deleted', 0)
            ->where('product_type', '=', 'campaign')
            ->where('created_at', '<=', $sixMonthAgoDateTime)
            ->where('visited_at', '>', $sixMonthAgoDateTime)
            ->pluck('id')
            ->toArray();

        $orderIds = [];
        foreach (array_chunk($visitedCampaignIds, $count) as $chunk) {
            $orderIds[] = OrderProduct::query()
                ->select('order_id')
                ->whereIn('campaign_id', $chunk)
                ->pluck('order_id')
                ->toArray();
        }

        $orderIds = array_unique(array_merge(...$orderIds));
        $orderIds = array_values(array_filter($orderIds));

        // if there's just one order with paid_at >= 3 month, then that campaign is not inactive campaign
        $excludeOrderIds = [];
        foreach (array_chunk($orderIds, $count) as $chunk) {
            $excludeOrderIds[] = Order::query()
                ->select('id')
                ->whereIn('id', $chunk)
                ->whereIn('type', [
                    OrderTypeEnum::CUSTOM,
                    OrderTypeEnum::REGULAR,
                ])
                ->Where('paid_at', '>=', $threeMonthAgoDateTime)
                ->pluck('id')
                ->toArray();
        }

        $excludeOrderIds = array_unique(array_merge(...$excludeOrderIds));
        unset($orderIds);

        $excludeCampaignIds = [];
        foreach (array_chunk($excludeOrderIds, $count) as $chunk) {
            $excludeCampaignIds[] = OrderProduct::query()
                ->select('campaign_id')
                ->whereIn('order_id', $chunk)
                ->pluck('campaign_id')
                ->toArray();
        }

        $excludeCampaignIds = array_unique(array_merge(...$excludeCampaignIds));
        $inactiveCampaignIds = array_values(array_diff($visitedCampaignIds, $excludeCampaignIds));

        $total = count($inactiveCampaignIds);

        if (empty($inactiveCampaignIds)) {
            return [
                'exampleCampaignIds' => [],
                'total' => 0,
            ];
        }

        if ($shouldDelete) {
            foreach (array_chunk($inactiveCampaignIds, $count) as $chunk) {
                self::deleteCampaign($chunk);
            }
        }

        $exampleCampaignIds = array_slice($inactiveCampaignIds, 0, 20);

        return [
            "total" => $total,
            "exampleCampaignIds" => $exampleCampaignIds
        ];
    }

    public static function logToEstimateTimeGraylog(string $processId, string $fileName, string $type, $esTime1, $esTime2): void
    {
        $context = [
            'category' => $type,
            'query_condition1' => (string)$esTime1,
            'query_condition2' => (string)$esTime2,
        ];
        $messages = 'Estimated time: ' . $esTime1 . ' ms for condition 1 and ' . $esTime2 . ' ms for condition 2';
        self::logToGraylog($processId, $fileName, $messages, $context);
    }

    public static function logToGraylog(string $processId, string $fileName, string $messages, array $context = null, bool $isReport = false): void
    {
        $context ??= [
            'category' => 'inactive_campaign',
        ];

        $isReport ? $report = '[Report]-' : $report = '';
        $processId = '[' . $processId . ']-';
        $fileName = '[' . $fileName . ']-';
        $messages = $processId . $fileName . $report . $messages;
        graylogInfo($messages, $context);
    }

    public static function logExceptionToGraylog(string $processId, string $fileName, string $type, \Exception $exception): void
    {
        $context = [
            'category' => $type,
            'type' => 'error exception',
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
        ];

        $processId = '[' . $processId . ']-';
        $fileName = '[' . $fileName . ']-';
        $report = '[Exception]-';
        $pre = $processId . $fileName . $report;

        graylogInfo($pre . $exception->getMessage(), $context);
    }

    public static function getEstimatedTime($startEsTime, $endEsTime): int
    {
        return $endEsTime - $startEsTime;
    }

    public static function manualDeleteInactiveDomain(array $domains): void
    {
        (new ScanExpiringDomains())->manualDeleteInactiveDomain($domains);
    }

    public static function checkingInactive($type, $sellerId): JsonResponse
    {
        $type = 'inactive_' . $type;
        $isUserExist = User::query()->where('id', $sellerId)->exists();

        if (!$isUserExist) {
            return response()->json([
                'status' => 'error',
                'message' => 'User not found'
            ], 404);
        }

        $userScanned = IndexUserCleanSettings::query()
            ->where('seller_id', $sellerId)
            ->first();

        if (!is_null($userScanned)) {
            $days = isEnvLocalOrDev() ? 8 : 30;
            $timeScan = Carbon::parse($userScanned->clean_at)->clone()->subDays($days);
        } else {
            $timeScan = Carbon::now()->clone();
        }

        $sixMonthAgoDateTime = self::getMonthAgoDatetime($timeScan, 6);
        $threeMonthAgoDateTime = self::getMonthAgoDatetime($timeScan, 3);
        $limit = 100;
        $count = 1000;

        switch ($type) {
            case 'inactive_campaign':
                //Điều kiện 1: Camp được tạo bằng 6 tháng trước hoặc xa hơn. Có visited_at xa hơn 6 tháng trước.
                $result1 = Product::query()
                    ->select('id')
                    ->inactiveCampaignByCondition1($sellerId, $sixMonthAgoDateTime)
                    ->pluck('id');

                // Điều kiện 2:
                // Camp được tạo từ 6 tháng trước đến nay,
                // Có visted_at từ 6 tháng trước đến nay
                // Có order - paid_at xa hơn 3 tháng trước
                // testing:
                $visitedCampaignIds = Product::query()
                    ->select('id')
                    ->where('seller_id', $sellerId)
                    ->where('is_deleted', 0)
                    ->where('product_type', '=', 'campaign')
                    ->where('created_at', '<=', $sixMonthAgoDateTime)
                    ->where('visited_at', '>', $sixMonthAgoDateTime)
                    ->pluck('id')
                    ->toArray();

                $orderIds = [];
                foreach (array_chunk($visitedCampaignIds, $count) as $chunk) {
                    $orderIds[] = OrderProduct::query()
                        ->select('order_id')
                        ->whereIn('campaign_id', $chunk)
                        ->pluck('order_id')
                        ->toArray();
                }

                $orderIds = array_unique(array_merge(...$orderIds));
                $orderIds = array_values(array_filter($orderIds));

                // if there's just one order with paid_at >= 3 month, then that campaign is not inactive campaign
                $excludeOrderIds = [];
                foreach (array_chunk($orderIds, $count) as $chunk) {
                    $excludeOrderIds[] = Order::query()
                        ->select('id')
                        ->whereIn('id', $chunk)
                        ->whereIn('type', [
                            OrderTypeEnum::CUSTOM,
                            OrderTypeEnum::REGULAR,
                        ])
                        ->Where('paid_at', '>=', $threeMonthAgoDateTime)
                        ->pluck('id')
                        ->toArray();
                }

                $excludeOrderIds = array_unique(array_merge(...$excludeOrderIds));
                unset($orderIds);

                $excludeCampaignIds = [];
                foreach (array_chunk($excludeOrderIds, $count) as $chunk) {
                    $excludeCampaignIds[] = OrderProduct::query()
                        ->select('campaign_id')
                        ->whereIn('order_id', $chunk)
                        ->pluck('campaign_id')
                        ->toArray();
                }

                $excludeCampaignIds = array_unique(array_merge(...$excludeCampaignIds));
                $result2 = array_values(array_diff($visitedCampaignIds, $excludeCampaignIds));
                $result2 = array_slice($result2, 0, 100);

                $data = [
                    'timeScan' => $timeScan->toDateTimeString(),
                    'sixMonthAgoDateTime' => $sixMonthAgoDateTime,
                    'threeMonthAgoDateTime' => $threeMonthAgoDateTime,
                    'limit' => $limit,
                    'result1' => $result1,
                    'result2' => $result2
                ];

                return response()->json([
                    'success' => true,
                    'data' => $data
                ]);
            case 'inactive_last_login':
                $isUserHaveLog = UserLog::query()
                    ->where('user_id', $sellerId)
                    ->exists();

                if (!$isUserHaveLog) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'User not have log so it is inactive'
                    ], 404);
                }

                $lastLogin = UserLog::query()
                    ->where('user_id', $sellerId)
                    ->select('user_id', DB::raw('MAX(created_at) as last_login'))
                    ->first();

                return response()->json([
                    'success' => true,
                    'data' => [
                        'timeScan' => $timeScan->toDateTimeString(),
                        'sixMonthAgoDateTime' => $sixMonthAgoDateTime,
                        'threeMonthAgoDateTime' => $threeMonthAgoDateTime,
                        'lastLogin' => $lastLogin
                    ]
                ]);
        }
        return response()->json([
            'status' => 'error',
            'message' => 'Type not found'
        ], 404);
    }
}

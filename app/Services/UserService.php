<?php

namespace App\Services;
use App\Enums\AdsLogsActionEnum;
use App\Enums\CacheKeys;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\CurrencyEnum;
use App\Enums\DateRangeEnum;
use App\Enums\OrderFraudStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\SellerBillingType;
use App\Enums\SystemRole;
use App\Enums\TradeMarkStatusEnum;
use App\Enums\UserInfoKeyEnum;
use App\Enums\UserStatusEnum;
use App\Enums\UserTopupTransactionStatus;
use App\Jobs\Seller\TopupIPNCallbackJob;
use App\Models\Currency;
use App\Models\Customer;
use App\Models\Elastic;
use App\Models\IndexOrder;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Pricing;
use App\Models\PromotionRule;
use App\Models\SellerBilling;
use App\Models\Template;
use App\Models\TopupUnmatchTransaction;
use App\Models\User;
use App\Models\UserInfo;
use App\Models\UserTopupTransaction;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use PragmaRX\Google2FAQRCode\Exceptions\MissingQrCodeServiceException;
use PragmaRX\Google2FAQRCode\Google2FA;

class UserService
{
    private const HOLD_HOURS = 72;
    private const HOLD_HOURS_VERIFIED = 24;
    private const HOLD_HOURS_EXPRESS = 8;
    /**
     * @throws MissingQrCodeServiceException
     */
    public static function generateGoogle2FaQrCode($data)
    {
        return (new Google2FA())->getQRCodeInline(
            'Senprints',
            $data['name'],
            $data['google2fa_secret'],
        );
    }

    private static function checkTopupUnmatchTransactionExist($topupId, $transactionId) {
        return UserTopupTransaction::query()->whereHas('topupUnmatchTransaction', function ($q) use ($transactionId) {
            $q->where('transaction_id', $transactionId);
        })
            ->where('id', $topupId)
            ->where('status', UserTopupTransactionStatus::PENDING)
            ->first();
    }

    public static function createTopupUnmatchTransaction($topupId = '', $transactionId = '', $message = '') {
        try {
            $topupExist = self::checkTopupUnmatchTransactionExist($topupId, $transactionId);
            if (!isset($topupExist)) {
                TopupUnmatchTransaction::query()->create([
                    'transaction_id' => $transactionId,
                    'topup_id' => $topupId,
                    'message' => json_encode($message),
                ]);
                return true;
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    public static function claimTopupUnmatch($topupId = '', $transactionId = '',  $topupCode = '') {
        $response = [];
        $topupExist = self::checkTopupUnmatchTransactionExist($topupId, $transactionId);
        if (!isset($topupExist)) {
            $response['complete'] = false;
            $response['message'] = 'Transaction not found';
        } else {
            if (empty($topupCode) || $topupExist->code != $topupCode) {
                $response['complete'] = false;
                $response['message'] = 'Invalid topup code';
                return $response;
            }
            if ($topupExist->status != UserTopupTransactionStatus::PENDING) {
                $response['complete'] = false;
                $response['message'] = 'Transaction already completed or cancelled';
                return $response;
            }

            $callbackMessage = json_decode($topupExist->topupUnmatchTransaction->message, true);
            $callbackMessage['code'] = $topupExist->code;
            TopupIPNCallbackJob::dispatch('email', $callbackMessage);
            $response['complete'] = true;
            $response['message'] = 'Topup claimed successfully';
        }
        return $response;
    }

    public static function addUserIdForUrlInMailContent ($content) {
        preg_match_all('/href="([^"]*)"/i', $content, $matches);
        if (!isset($matches[1])) {
            return $content;
        }
        $urls = $matches[1];
        $urlsForReplace = [];
        $currentUserId = currentUser()->getUserId();
        if (!isset($currentUserId)) {
            return $content;
        }
        foreach ($urls as $url) {
            if (str_contains($url, 'confirm-address')) {
                $urlsForReplace [] = [
                    'old' => $url,
                    'new' => $url . '&' . "userId=$currentUserId"
                ];
                continue;
            }
            $parsedUrl = parse_url($url);
            if (str_contains($url, 'redirectUrl') && isset($parsedUrl['query'])) {
                $query = $parsedUrl['query'];
                $redirectUrl = str_replace('redirectUrl=', '', $query);

                if (str_contains($redirectUrl, urldecode('?'))) {
                    $newRedirectUrl = $redirectUrl . htmlentities('&') . "userId=$currentUserId";
                } else {
                    $newRedirectUrl = $redirectUrl . htmlentities('&') . "userId=$currentUserId";
                }
                $urlsForReplace [] = [
                    'old' => $url,
                    'new' => str_replace($redirectUrl, $newRedirectUrl, $url)
                ];

            }
        }
        graylogError('Check urls for replace : ', [
            'category' => 'replace_content_for_email',
            'data' => $urlsForReplace
        ]);
        foreach($urlsForReplace as $url) {
            if (!isset($url['old']) || !isset($url['new'])) {
                continue;
            }
            $content = str_replace($url['old'], $url['new'], $content);
        }
        return $content;
    }
    /**
     * @param $sellerId
     * @return mixed
     */
    public static function getSellerTrackingCode($sellerId)
    {
        $result = UserInfo::query()
            ->select('value')
            ->firstWhere([
                'user_id' => $sellerId,
                'key' => UserInfoKeyEnum::TRACKING_CODE
            ]);
        return $result->value ?? null;
    }

    /**
     * @param $sellerId
     * @param bool $status
     * @param bool $withCache
     * @return mixed
     */
    public static function getPricingBySellerId($sellerId, bool $status = true, bool $withCache = true)
    {
        if (!$withCache) {
            return Pricing::getPricingBySellerId($sellerId, $status);
        }

        $cacheKey = CacheKeys::SELLER_PRICING_PREFIX . $sellerId . '_';
        $cacheKey .= $status ? 'active' : 'all';
        return  cacheAlt()->remember($cacheKey, CacheKeys::CACHE_24H, function () use ($sellerId, $status) {
            $pricings = Pricing::getPricingBySellerId($sellerId, $status);
            if($pricings->isNotEmpty()) {
                foreach ($pricings as $idx => $pricing) {
                    $template = Template::findAndCacheByKey($pricing->product_id);
                    if (empty($template)) {
                        $pricings->forget($idx);
                    }
                }
            }
            return $pricings;
        });
    }

    /**
     * @param array $sellerIds
     * @return Collection
     */
    public static function getPricingListBySellerIds(array $sellerIds): Collection
    {
        $pricing = collect();

        if (!empty($sellerIds)) {
            foreach ($sellerIds as $sellerId) {
                $pricing = $pricing->merge(self::getPricingBySellerId($sellerId));
            }
        }

        return $pricing;
    }

    /**
     * @param $user
     * @return int
     */
    public static function calculateHoldHours($user): int
    {
        $holdHours = self::HOLD_HOURS;

        if ($user->status === UserStatusEnum::VERIFIED || $user->status === UserStatusEnum::TRUSTED) {
            $holdHours = self::HOLD_HOURS_VERIFIED;

            if ($user->isExpressPayout()) {
                $holdHours = self::HOLD_HOURS_EXPRESS;
            }
        }

        return $holdHours;
    }

    /**
     * @param $user
     * @return float
     */
    public static function getHoldProfit($user): float
    {
        $holdHours = self::calculateHoldHours($user);
        $flaggedOrderQuery = Order::query()
            ->select(['id', 'total_seller_profit'])
            ->where('seller_id', $user->id)
            ->where(function ($q) {
                $q->where('fraud_status', '!=', OrderFraudStatus::TRUSTED);
                $q->orWhereIn('tm_status', [TradeMarkStatusEnum::FLAGGED, TradeMarkStatusEnum::VIOLATED]);
            })
            ->where('type', OrderTypeEnum::REGULAR)
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->where('paid_at', '>=', now()->subDays(90));
        $flaggedOrderIds = $flaggedOrderQuery->clone()->get()->pluck('id')->toArray();
        $flaggedOrderProfit = $flaggedOrderQuery->clone()->sum('total_seller_profit');
        $pendingLateDesignQuery = Order::query()
            ->select(['id', 'total_seller_profit'])
            ->where('seller_id', $user->id)
            ->whereNotIn('id', $flaggedOrderIds)
            ->where('type', OrderTypeEnum::REGULAR)
            ->where('status', OrderStatus::PROCESSING)
            ->where('fulfill_status', OrderFulfillStatus::DESIGNING)
            ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
            ->where('paid_at', '>=', now()->subDays(5));

        $pendingLateDesignOrderIds = $pendingLateDesignQuery->clone()->get()->pluck('id')->toArray();
        $pendingLateDesignProfit = $pendingLateDesignQuery->clone()->sum('total_seller_profit');
        $ignoreOrderIds = array_unique(array_merge($flaggedOrderIds, $pendingLateDesignOrderIds));
        $pendingProfit = SellerBilling::query()
            ->where([
                'seller_id' => $user->id,
                'type' => SellerBillingType::COMMISSION
            ])
            ->where('created_at', '>=', now()->subHours($holdHours))
            ->when(count($ignoreOrderIds) > 0, function ($q) use ($ignoreOrderIds) {
                $q->whereNotIn('order_id', $ignoreOrderIds);
            })
            ->sum('amount');
        return $flaggedOrderProfit + $pendingProfit + $pendingLateDesignProfit;
    }

    /**
     * @param $money
     * @return string
     */
    public static function formatCurrency($money): string
    {
        $output = '$' . formatCurrency($money, 'USD', 'en-US');
        return str_replace('$-', '-$', $output);
    }

    /**
     * @param $value
     * @param string $currencyCode
     * @param float $currencyRate
     * @param Currency|null $currency
     * @return string
     */
    public static function formatPrice($value, string $currencyCode = 'USD', float $currencyRate = 1, $currency = null): string
    {
        $currency ??= StoreService::systemCurrencies()?->firstWhere('code', strtoupper($currencyCode));
        if (!$currency) {
            $currency = StoreService::systemCurrencies()?->firstWhere('code', CurrencyEnum::USD);
        }
        if (!$currency) {
            return formatCurrency(convertCurrency($value, 1), CurrencyEnum::USD, 'en-US', true);
        }
        $currency->rate = $currencyRate ?? $currency->rate;
        return formatCurrency(convertCurrency($value, $currency->rate), $currency->code, $currency->locale, true);
    }

    public static function updateStepProductTour($sellerId, $nextStepKey): void
    {
        $mapStep = [
            'create-campaign' => 2,
            'manage-order' => 3,
            'complete-product-tour' => 5
        ];
        $stepInfo = UserInfo::query()
            ->firstOrNew([
                'key' => UserInfoKeyEnum::STEP_PRODUCT_TOUR,
                'user_id' => $sellerId,
            ], [
                'value' => 0
            ]);
        $nextStep = data_get($mapStep, $nextStepKey, 0);
        $currentStep = data_get($mapStep, $stepInfo->value, 1);
        if ($nextStep > $currentStep) {
            $stepInfo->value = $nextStepKey;
            $stepInfo->save();
        }
    }

    public static function getUserByRoleName (
        array $roles,
        array $options = [],
        int   $pageSize = 0,
        bool  $hasAnalytics = true,
        bool  $isCustomer = false
    ) {
        try {
            $model = User::query()->role($roles);
            $tableName = 'user';
            if ($isCustomer) {
                $model = Customer::query();
                $tableName = 'customer';
            }

            $emailVerifiedFilter = !empty($options['verify_email']);

            if (!empty($options['select']) && is_array($options['select'])) {
                foreach ($options['select'] as $select) {
                    $model->addSelect("$tableName." . $select);
                }
            } else {
                $model->select("$tableName.*");
            }

            $model->with([
                'recent_store' => function ($query) {
                    return $query->select(['id', 'name']);
                }
            ]);

            if (!empty($options['q'])) {
                $searchType = Arr::get($options, 'search_type', 'default');
                if ($searchType === 'ads_campaign' && !$isCustomer) {
                    $model->join('ads_campaign_logs', 'ads_campaign_logs.seller_id', '=', "$tableName.id")
                        ->whereIn('ads_campaign_logs.action' , AdsLogsActionEnum::engageActions())
                        ->join('ads_campaign', 'ads_campaign.id', '=', 'ads_campaign_logs.ads_campaign_id')
                        ->where(function ($q) use ($options) {
                            $q->where('ads_campaign.utm_campaign', 'like', '%' . $options['q'] . '%')
                                ->orWhere('ads_campaign.utm_source', 'like', '%' . $options['q'] . '%')
                                ->orWhere('ads_campaign.utm_medium', 'like', '%' . $options['q'] . '%');
                        });
                } else if ($searchType === 'country') {
                    $keywords = strtoupper($options['q']);
                    $model->where("$tableName.register_country", 'like', '%' . $keywords . '%');
                } else {
                    if (is_numeric($options['q'])) {
                        $model->where("$tableName.id", $options['q']);
                        $model->limit(1);
                        $pageSize = 1;
                    } else {
                        $model->where(function ($q) use ($options, $tableName) {
                            $q->orWhere("$tableName.email", 'like', $options['q'] . '%');
                            $q->orWhere("$tableName.name", 'like', '%' . $options['q'] . '%');
                        });
                    }
                }
            }

            if (isset($options['ids'])) {
                $model->whereIn("$tableName.id", $options['ids']);
            }

            if (isset($options['fulfillment_sellers'])) {
                $dateRanges = Arr::get($options, 'date_ranges');
                $sellerIds = IndexOrder::query()
                    ->select('id', 'seller_id')
                    ->addFilterAnalytic([], $dateRanges)
                    ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                    ->whereIn('status', [OrderStatus::PROCESSING, OrderStatus::COMPLETED])
                    ->whereNull('deleted_at')
                    ->pluck('seller_id')
                    ->unique();

                $fulfillSellerIds = User::query()
                    ->select('id')
                    ->whereIn('id', $sellerIds)
                    ->when($emailVerifiedFilter, function ($q) {
                        $q->whereNotNull('email_verified_at');
                    })
                    ->filterFulfillSeller()
                    ->pluck('id');

                if ($fulfillSellerIds->isNotEmpty()) {
                    $model->whereIn("$tableName.id", $fulfillSellerIds);
                } else {
                    return collect();
                }
            }

            if (!empty($options['status'])) {
                $model->where("$tableName.status", $options['status']);
            }

            if (isset($options['sale_staff_not_null'])) {
                $model->where("$tableName.sale_staff_id", '!=', null);
            }

            if (!empty($options['support_staff_id'])) {
                $model->where("$tableName.support_staff_id", $options['support_staff_id']);
            }

            if (!empty($options['sale_staff_id'])) {
                $model->where("$tableName.sale_staff_id", $options['sale_staff_id']);
            }

            if (!empty($options['role'])) {
                $model->where("$tableName.role", $options['role']);
            }

            if (!empty($options['tier_id'])) {
                $model->where("$tableName.tier_id", $options['tier_id']);
            }

            if (!empty($options['country'])) {
                $model->where("$tableName.country", $options['country']);
            }

            if (isset($options['confirm_join_contest'])) {
                $model->where("$tableName.confirm_join_contest", $options['confirm_join_contest']);
            }

            if (isset($options['min_order'])) {
                $model->where("$tableName.total_orders", '>', $options['min_order']);
            }

            if ($emailVerifiedFilter) {
                $model->whereNotNull("$tableName.email_verified_at");
            }

            if (!empty($options['store_id'])) {
                $model->join('seller_customer', 'seller_customer.customer_id', '=', "$tableName.id", 'right')
                    ->where('seller_customer.store_id', $options['store_id']);
            }

            if (!empty($options['date_range']) && !isset($options['new_sale']) && !isset($options['support_staff_id'])) {
                $model->filterDateRange(
                    $options['date_range'],
                    $options['start_date'],
                    $options['end_date'],
                );
            }

            $sortBy = Arr::get($options, 'sort.0');
            if (!empty($options['order_by_users_id'])) {
                // order by analytic
                $model->orderByRaw("FIELD($tableName.id,'" . implode("','", $options['order_by_users_id']) . "') desc");
            } elseif (!empty($sortBy) && $sortBy !== 'campaigns') {

                if ($sortBy === 'paid_at') {
                    //exception: inactive sellers case
                    //return sortBy total_items here for get sellers
                    //then we'll sort by paid_at at the end (when have inactive sellers)
                    $sortBy = CampaignSortByAllowEnum::TOTAL_ITEMS;
                }

                // order by else
                $prefix = "$tableName.";

                // left join for order by
                switch ($sortBy) {
                    case CampaignSortByAllowEnum::TOTAL_ITEMS:
                    case CampaignSortByAllowEnum::TOTAL_ORDERS:
                    case CampaignSortByAllowEnum::TOTAL_PROFITS:
                    case CampaignSortByAllowEnum::TOTAL_SALES:
                        $prefix = '';
                        $dateRanges['date_type'] = request('date_range', DateRangeEnum::CUSTOM);
                        $dateRanges['range'] = [
                            request('start_date'),
                            request('end_date'),
                        ];

                        $order = Order::query()
                            ->analytic($sortBy)
                            ->addSelect('order.seller_id')
                            ->addFilterAnalytic([], $dateRanges)
                            ->when(isset($options['fulfillment_sellers']), function ($q) {
                                $q->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA]);
                            })
                            ->whereIn('payment_status', [
                                OrderPaymentStatus::PAID,
                                OrderPaymentStatus::PARTIALLY_REFUNDED,
                            ])
                            ->groupBy('order.seller_id');
                        $model
                            ->addSelect('order.*')
                            ->leftJoinSub($order, 'order', 'order.seller_id', '=', "$tableName.id")
                            ->groupBy("$tableName.id");
                        break;
                    case CampaignSortByAllowEnum::ORDERS_SEN_POINTS:
                        $prefix = '';
                        $dateRanges['date_type'] = request('date_range', DateRangeEnum::CUSTOM);
                        $dateRanges['range'] = [
                            request('start_date', '2023-03-12 17:00:00'),
                            request('end_date', '2023-06-18 06:59:59'),
                        ];

                        $order = Order::query()
                            ->select('id')
                            ->addFilterAnalytic([], $dateRanges)
                            ->isValidPaidOrder();

                        $orderProduct = OrderProduct::query()
                            ->select([
                                'sen_points',
                                'seller_id',
                            ])
                            ->where('order_product.fulfill_status', '!=', OrderProductFulfillStatus::CANCELLED)
                            ->joinSub($order, 'order', function ($join) {
                                $join->on('order.id', '=', 'order_product.order_id');
                            });

                        $model
                            ->selectRaw("sum(order_product.sen_points) as $sortBy")
                            ->leftJoinSub($orderProduct, 'order_product', 'order_product.seller_id', '=', "$tableName.id")
                            ->groupBy("$tableName.id");
                        break;
                    case 'stores':
                        $prefix = '';
                        $model
                            ->selectRaw('count(s.id) as stores')
                            ->leftJoin('store as s', 's.seller_id', "$tableName.id")
                            ->groupBy("$tableName.id");
                        break;
                    case 'promotions':
                        $prefix = '';
                        $sub = PromotionRule::query()
                            ->select('seller_id')
                            ->where('status', 1);

                        $model
                            ->selectRaw('count(p.seller_id) as promotions')
                            ->leftJoinSub($sub, 'p', 'p.seller_id', "$tableName.id")
                            ->groupBy("$tableName.id");
                        break;
                }
                $model->orderByRaw($prefix . $sortBy . ' ' . $options['sort'][1]);
            }

            if (in_array(SystemRole::SELLER, $roles)) {
                // order by campaigns
                if ($sortBy === 'campaigns') {
                    $options['is_sort_by_campaigns'] = true;

                    $dataCampaigns = (new Elastic())->getCountCampaignBySeller($options, $pageSize);

                    $sellerIds = array_column($dataCampaigns, 'seller_id');

                    if (!empty($sellerIds)) {
                        if ($options['sort'][1] === 'desc') {
                            $sellerIds = array_reverse($sellerIds);
                        }
                        $model->orderByRaw("FIELD($tableName.id," . implode(',', $sellerIds) . ') ' . $options['sort'][1]);
                    }
                }

                $model->with(['support_staff:id,name', 'sale_staff:id,name', 'tier']);
                $model->with(['register_ads' => function ($query) {
                    $query->select('seller_id', 'ads_campaign_id', 'action')->with('ads_campaign:id,utm_campaign,extra');
                }]);

                if ($hasAnalytics) {
                    // if not sort by, select sub query
                    if ($sortBy !== 'stores') {
                        $model->withCount('stores as stores');
                    }
                    if ($sortBy !== 'promotions') {
                        $model->withCount([
                            'promotion_rules as promotions' => function ($query) {
                                return $query->where('status', 1);
                            }
                        ]);
                    }
                }
                if (!empty($options['inactive_date_range'])) {
                    $sort = Arr::get($options, 'sort.0');
                    $sortPaidAt = [];

                    if ($sort === 'paid_at') {
                        $sortPaidAt = $options['sort'];
                    }
                    $result = self::getInactiveUsers($options['inactive_date_range'], $model, $pageSize, $sortPaidAt);
                } else {
                    if ($pageSize === 0) {
                        $result = $model->get();
                    } else {
                        $result = $model->paginate($pageSize);
                    }
                }

                if ($hasAnalytics) {
                    if (!isset($dataCampaigns)) {
                        // where in array ids
                        $options['seller_ids'] = $result->modelKeys();

                        $dataCampaigns = (new Elastic())->getCountCampaignBySeller($options, $pageSize);
                    }

                    foreach ($result as &$seller) {
                        // set campaigns
                        $dataCampaign = Arr::first($dataCampaigns, static function ($arr) use ($seller) {
                            return $arr['seller_id'] === $seller->id;
                        });
                        $seller->campaigns = Arr::get($dataCampaign, 'campaigns', 0);
                    }
                }
                return $result;
            }
            if ($pageSize > 0) {
                return $model->paginate($pageSize);
            }

            return $model->get();
        } catch (\Exception $e) {
            logToDiscord('Error in load user : ' . $e->getMessage());
            return null;
        }
    }

    /**
     * @param $sellerId
     * @param $select
     * @return User|null
     */
    public static function getSellerSharding($sellerId, $select = []): ?User
    {
        $callback = function () use ($sellerId, $select) {
            $defaultSelect = ['id', 'email', 'status', 'custom_payment', 'sharding_status', 'db_connection'];
            if (!empty($select)) {
                $defaultSelect = array_merge($defaultSelect, $select);
            }
            return User::query()->find($sellerId, $defaultSelect);
        };
        $cacheKey = md5(CacheKeys::SELLER_CONNECTION . '_' . $sellerId . '_' . (!empty($select) ? json_encode($select) : ''));
        $user = cacheAlt()->remember($cacheKey, CacheKeys::CACHE_5m, $callback);
        if (!empty($user)) {
            return $user;
        }
        return $callback();
    }
}

<?php

namespace App\Services;

use App\Contracts\TrackingServiceContract;
use App\Enums\TrackingServiceEnum;
use App\Enums\TrackingStatusEnum;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class Trakow implements TrackingServiceContract
{
    // Main status
    public const NO_RECORD = 'no_record';
    public const INFO_RECEIVED = 'info_received';
    public const IN_TRANSIT = 'in_transit';
    public const WAITING_DELIVERY = 'waiting_delivery';
    public const DELIVERY_FAILED = 'delivery_failed';
    public const ABNORMAL = 'abnormal';
    public const DELIVERED = 'delivered';
    public const EXPIRED = 'expired';

    protected $apiEndpoint;
    protected $secretKey;

    public function __construct()
    {
        $this->apiEndpoint = config('services.tracking.trakow.api_endpoint');
        $this->secretKey = config('services.tracking.trakow.secret_key');
    }
    /**
     * @param $trackingNumbers
     * @return array|mixed|null
     * @throws \Throwable
     */
    public static function register($trackingNumbers)
    {
        return (new self)->postRequest('tracking/push-tracking', $trackingNumbers);
    }

    /**
     * Trakow does not support delete track
     * @param $trackingNumbers
     * @return false
     * @throws \Throwable
     */
    public static function deleteTrack($trackingNumbers)
    {
        return false;
    }

    /**
     * @param $trackingNumbers
     * @return array
     * @throws \Throwable
     */
    public static function getTrackInfo($trackingNumbers)
    {
        $result = [
            'accepted' => [],
            'rejected' => []
        ];
        $response = (new self)->postRequest('tracking/get-tracking-info', $trackingNumbers);
        if (empty($response)) {
            return $result;
        }
        if (!empty($response['accepted'])) {
            $result['accepted'] = $response['accepted'];
        }
        if (!empty($response['rejected'])) {
            $rejected = [];
            if (is_array($response['rejected'])) {
                $rejected = array_map(static function ($item) {
                    return [
                        'trackingId' => $item,
                        'error' => [
                            'code' => self::NO_RECORD,
                            'message' => 'TrackingId not registered'
                        ]
                    ];
                }, $response['rejected']);
            }
            $result['rejected'] = $rejected;
        }
        $result['service'] = TrackingServiceEnum::TRACK_123;
        return $result;
    }

    /**
     * Temporary used the track123 carriers
     *
     * @return Collection
     */
    public static function carriers(): Collection
    {
        try {
            $carriersFilePath = resource_path('/json/carriers/track123.json');
            if (!File::exists($carriersFilePath)) {
                return collect();
            }
            $carriers = File::get($carriersFilePath);
            $carriers = json_decode($carriers, true, 512, JSON_THROW_ON_ERROR);
            return collect($carriers);
        } catch (\Throwable $e) {
            return collect();
        }
    }

    /**
     * @param $trackingCodes
     * @return array
     */
    public static function registerOrderTracking($trackingCodes): array
    {
        try {
            $insertCodes = [];
            $updateCodes = [];
            $orderProducts = $trackingCodes->toArray();
            foreach ($orderProducts as $orderProduct) {
                $register = self::register([$orderProduct]);
                if (empty($register['state'])) {
                    graylogInfo('[Trakow] Register tracking number failed', [
                        'category' => 'tracking_status_logs',
                        'tracking_codes' => $trackingCodes,
                        'response' => $register,
                    ]);
                    continue;
                }
                $insertCodes[$orderProduct['tracking_code']] = [
                    [
                        'tracking_code' => $orderProduct['tracking_code'],
                        'order_number' => data_get($orderProduct, 'order.order_number', data_get($orderProduct, 'order_id')),
                    ],
                    [
                        'order_id' => data_get($orderProduct, 'order_id', data_get($orderProduct, 'order.order_number')),
                        'status' => TrackingStatusEnum::NEW,
                        'shipping_carrier' => self::detectCarrier($orderProduct['tracking_code'], $orderProduct['shipping_carrier'], $orderProduct['supplier_name'] ? strtolower(trim($orderProduct['supplier_name'])) : null, $orderProduct['tracking_url'] ?? null),
                        'tracking_service' => TrackingServiceEnum::TRAKOW
                    ]
                ];
                $updateCodes[] = [
                    [
                        'tracking_code' => $orderProduct['tracking_code']
                    ],
                    [
                        'tracking_status' => TrackingStatusEnum::NEW,
                        'tracking_service' => TrackingServiceEnum::TRAKOW
                    ]
                ];
            }
            return [$insertCodes, $updateCodes];
        } catch (\Throwable $e) {
            return [[], []];
        }
    }

    /**
     * @return array
     */
    public static function statusesMapWithTrackingStatusEnum(): array
    {
        return [
            self::NO_RECORD => TrackingStatusEnum::NOTFOUND,
            self::INFO_RECEIVED => TrackingStatusEnum::INFO_RECEIVED,
            self::IN_TRANSIT => TrackingStatusEnum::TRANSIT,
            self::DELIVERY_FAILED => TrackingStatusEnum::TRANSIT,
            self::WAITING_DELIVERY => TrackingStatusEnum::OUT_FOR_DELIVERY,
            self::EXPIRED => TrackingStatusEnum::EXPIRED,
            self::DELIVERED => TrackingStatusEnum::DELIVERED,
            self::ABNORMAL => TrackingStatusEnum::EXCEPTION,
        ];
    }

    /**
     * @param $status
     * @return string|null
     */
    public static function convertTrackingStatus($status): ?string
    {
        $statuses = self::statusesMapWithTrackingStatusEnum();
        return $statuses[$status] ?? null;
    }

    /**
     * @return Collection
     */
    public static function detectPatterns(): Collection
    {
        return collect([
            [
                'carrier_name' => ['spring'],
                'carrier_key' => "springgds",
                'tracking_code_prefix' => ['3SDOKC'],
            ],
            [
                'carrier_name' => ['upsmailinnovations'],
                'carrier_key' => "ups-mail-innovations",
            ],
            [
                'carrier_name' => ['ups'],
                'carrier_key' => "ups",
            ],
            [
                'carrier_name' => ['usps', 'genericstandard'],
                'carrier_key' => "usps",
                'tracking_code_prefix' => ['92', '94'],
                'tracking_code_length' => [26]
            ],
            [
                'carrier_name' => ['royalmail'],
                'carrier_key' => "royal-mail",
            ],
            [
                'carrier_name' => ['bartolini', 'brtbartolini'],
                'carrier_key' => "bartolini",
            ],
            [
                'carrier_name' => ['gls'],
                'carrier_key' => 'gls',
                'suppliers' => ['jondo', 'textildruck'],
            ],
            [
                'carrier_name' => ['asendiausa'],
                'carrier_key' => "asendiausa",
                'suppliers' => ['monsterdigital', 'dreamship', 'gooten', 'jondo', 'printlogistic', 'printlogisticv2'],
            ],
            [
                'carrier_name' => ['asendia'],
                'carrier_key' => 'asendia',
                'suppliers' => ['monsterdigital', 'dreamship', 'gooten', 'jondo', 'printlogistic', 'printlogisticv2'],
            ],
            [
                'carrier_name' => ['fastway'],
                'carrier_key' => 'fastway',
            ],
            [
                'carrier_name' => ['dhlpaket', 'dhlwarenpost'],
                'carrier_key' => "dhl-germany",
            ],
            [
                'carrier_name' => ['dhlexpress'],
                'carrier_key' => 'dhl',
            ],
            [
                'carrier_name' => ['dhl', 'dhlecommerce', 'dhlecs', 'dhlgm', 'genericstandard', 'dhlecs'],
                'carrier_key' => "dhlglobalmail",
                'suppliers' => ['gooten', 'dreamship', 'gearment', 'moteefe', 'textildruck', 'printforia', 'printlogistic', 'optondemand', 'monsterdigital'],
                'tracking_code_prefix' => ['GM', '00'],
                'tracking_code_length' => [20, 22]
            ],
            [
                'carrier_name' => ['fedex'],
                'carrier_key' => 'fedex',
            ],
            [
                'carrier_name' => ['yunexpress'],
                'carrier_key' => 'yunexpress',
                'tracking_code_prefix' => ['YT'],
            ],
            [
                'carrier_name' => ['chitchats'],
                'carrier_key' => 'chitchats',
            ],
            [
                'carrier_name' => ['gtagsm'],
                'carrier_key' => 'gtagsm',
                'tracking_code_prefix' => ['GR'],
                'tracking_code_length' => [19]
            ],
            [
                'carrier_name' => ['canadapost'],
                'carrier_key' => "canada-post",
                'suppliers' => ['printgeek'],
                'tracking_code_prefix' => ['201506'],
                'tracking_code_length' => [16]
            ],
            [
                'carrier_name' => ['dpd'],
                'suppliers' => ['printlogisticv2'],
                'carrier_key' => "dpd",
            ]
        ]);
    }

    public static function isSkipCheck($code, $message = '')
    {
        return false;
    }

    /**
     * @param $code
     * @param string $message
     * @return bool
     */
    public static function isNeedRegister($code, $message = ''): bool
    {
        return $code && !empty($message) && str_ends_with($message, 'not registered');
    }

    /**
     * @param $trackingNumber
     * @param $carrier
     * @param $supplier
     * @param $trackingUrl
     * @return \string|false|null
     */
    public static function detectCarrier($trackingNumber, $carrier = null, $supplier = null, $trackingUrl = null)
    {
        if (empty($trackingNumber)) {
            return null;
        }
        $carrier = str_replace(' ', '', strtolower(trim($carrier)));
        $supplier = str_replace(' ', '', strtolower(trim($supplier)));
        $trackingUrl = trim($trackingUrl);
        $patterns = self::detectPatterns();
        $carrierFilter = $patterns->first(function ($pattern) use ($trackingNumber, $carrier, $supplier, $trackingUrl) {
            $carrierMatch = !empty($pattern['carrier_name']) && !empty($carrier) && (in_array($carrier, $pattern['carrier_name'], true) || Str::startsWith($carrier, $pattern['carrier_name']));
            $supplierMatch = !empty($pattern['suppliers']) && in_array($supplier, $pattern['suppliers'], true);
            $trackingCodePrefixMatch = !empty($pattern['tracking_code_prefix']) && collect($pattern['tracking_code_prefix'])->first(fn($prefix) => str_starts_with($trackingNumber, $prefix));
            $trackingCodeLength = !empty($pattern['tracking_code_length']) && in_array(strlen($trackingNumber), $pattern['tracking_code_length'], true);
            $domainMatch = false;
            if (!empty($pattern['domain']) && !empty($trackingUrl)) {
                $parseUrl = parse_url($trackingUrl);
                if ($parseUrl && !empty($parseUrl['host'])) {
                    $domain = str_replace('www.', '', $parseUrl['host']);
                    $domainMatch = in_array($domain, $pattern['domain'], true);
                }
            }
            if ($carrier === 'usps' && $trackingCodePrefixMatch && str_starts_with($trackingNumber, 91) && $trackingCodeLength && strlen($trackingNumber) === 21) {
                return true;
            }
            if (!empty($pattern['suppliers'])) {
                return $supplierMatch && ($carrierMatch || $domainMatch || $trackingCodePrefixMatch);
            }
            return $carrierMatch || $domainMatch || $trackingCodePrefixMatch;
        });
        return data_get($carrierFilter, 'carrier_key');
    }

    /**
     * @param $url
     * @param array $data
     * @param bool $handleExcept
     * @return array|mixed|null
     * @throws \Throwable
     */
    public function postRequest($url, array $data = [], bool $handleExcept = false)
    {
        if (!app()->isProduction()) {
            return null;
        }
        if (!$handleExcept) {
            [$url, $data] = $this->handle($url, $data);
        }
        $response = Http::asJson()
            ->withoutVerifying()
            ->withUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36')
            ->withHeaders([
                'Authorization-Token' => $this->secretKey,
            ])
            ->post($this->apiEndpoint . $url, $data);
        if ($response->failed()) {
            if ($response->status() === 429) {
                return null;
            }
            graylogError('[Trakow] Post request failed to ' . $url, [
                'category' => 'tracking_status_logs',
                'url' => $url,
                'response_status' => $response->status(),
                'response_data' => $response->json(),
                'response_header' => json_encode($response->headers(), JSON_THROW_ON_ERROR),
            ]);
            logToDiscord('[Trakow] Post request failed to ' . $url . ' - Status: ' . $response->status(), 'tracking_status_logs', true);
            return null;
        }
        $response = $response->json();
        return !empty($response) ? $response : [];
    }

    /**
     * @param $request
     * @return true
     */
    public function verifySignature($request)
    {
        return true;
    }

    /**
     * @param $url
     * @param $trackingNumbers
     * @return array
     */
    public function handle($url, $trackingNumbers)
    {
        $url = !str_starts_with($url, '/') ? ('/' . $url) : $url;
        $trackingNumbers = is_array($trackingNumbers) ? $trackingNumbers : [$trackingNumbers];
        if (!empty($trackingNumbers)) {
            if ($url === '/tracking/get-tracking-info') {
                return [$url, ['trackingIds' => $trackingNumbers]];
            }
            $trackingNumbers = array_map(function ($trackingNumber) use ($url) {
                if ($url === '/tracking/push-tracking') {
                    $trackingNumber = (array) $trackingNumber;
                    $carrier = self::detectCarrier($trackingNumber['tracking_code'], $trackingNumber['shipping_carrier'], $trackingNumber['supplier_name'] ? strtolower(trim($trackingNumber['supplier_name'])) : null, $trackingNumber['tracking_url'] ?? null);
                    $data = [
                        'trackingId' => $trackingNumber['tracking_code']
                    ];
                    $data['courierName'] = null;
                    if ($carrier) {
                        $data['courierName'] = $carrier;
                    }
                    $additionInfo = [];
                    $additionInfo['clientId'] = '';
                    if (!empty($trackingNumber['shipping_carrier'])) {
                        $additionInfo['clientId'] = 'Carrier: ' . $trackingNumber['shipping_carrier'] . PHP_EOL;
                    }
                    if (data_get($trackingNumber, 'order.order_number')) {
                        $additionInfo['orderId'] = data_get($trackingNumber, 'order.order_number');
                    } else if (data_get($trackingNumber, 'order_id')) {
                        $additionInfo['orderId'] = data_get($trackingNumber, 'order_id');
                    }
                    $data['additionInfo'] = $additionInfo;
                    return $data;
                }
                return ['trackingId' => $trackingNumber];
            }, $trackingNumbers);
        }
        return [$url, $trackingNumbers];
    }
}

<?php

namespace App\Services;

use App\Contracts\TrackingServiceContract;
use App\Enums\CacheKeys;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderProductLogEnum;
use App\Enums\SupplierEnum;
use App\Enums\TrackingServiceEnum;
use App\Enums\TrackingStatusEnum;
use App\Models\OrderProduct;
use App\Models\OrderProductLog;
use App\Models\SystemLocation;
use App\Models\TrackingStatus;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class SeventeenTrack implements TrackingServiceContract
{
    // Main status
    public const NOT_FOUND = 'NotFound';
    public const INFO_RECEIVED = 'InfoReceived';
    public const IN_TRANSIT = 'InTransit';
    public const EXPIRED = 'Expired';
    public const AVAILABLE_FOR_PICKUP = 'AvailableForPickup';
    public const OUT_FOR_DELIVERY = 'OutForDelivery';
    public const DELIVERY_FAILURE = 'DeliveryFailure';
    public const DELIVERED = 'Delivered';
    public const EXCEPTION = 'Exception';

    // Sub status
    public const SUB_NOT_FOUND_OTHER = 'NotFound_Other';
    public const SUB_NOT_FOUND_INVALID_CODE = 'NotFound_InvalidCode';
    public const SUB_INFO_RECEIVED = 'InfoReceived';
    public const SUB_IN_TRANSIT_PICKED_UP = 'InTransit_PickedUp';
    public const SUB_IN_TRANSIT_OTHER = 'InTransit_Other';
    public const SUB_IN_TRANSIT_DEPARTURE = 'InTransit_Departure';
    public const SUB_IN_TRANSIT_ARRIVAL = 'InTransit_Arrival';
    public const SUB_IN_TRANSIT_CUSTOMS_PROCESSING = 'InTransit_CustomsProcessing';
    public const SUB_IN_TRANSIT_CUSTOMS_RELEASED = 'InTransit_CustomsReleased';
    public const SUB_IN_TRANSIT_CUSTOMS_REQUIRING_INFORMATION = 'InTransit_CustomsRequiringInformation';
    public const SUB_EXPIRED_OTHER = 'Expired_Other';
    public const SUB_AVAILABLE_FOR_PICKUP_OTHER = 'AvailableForPickup_Other';
    public const SUB_OUT_FOR_DELIVERY_OTHER = 'OutForDelivery_Other';
    public const SUB_DELIVERY_FAILURE_OTHER = 'DeliveryFailure_Other';
    public const SUB_DELIVERY_FAILURE_NO_BODY = 'DeliveryFailure_NoBody';
    public const SUB_DELIVERY_FAILURE_SECURITY = 'DeliveryFailure_Security';
    public const SUB_DELIVERY_FAILURE_REJECTED = 'DeliveryFailure_Rejected';
    public const SUB_DELIVERY_FAILURE_INVALID_ADDRESS = 'DeliveryFailure_InvalidAddress';
    public const SUB_DELIVERED_OTHER = 'Delivered_Other';
    public const SUB_EXCEPTION_OTHER = 'Exception_Other';
    public const SUB_EXCEPTION_RETURNING = 'Exception_Returning';
    public const SUB_EXCEPTION_RETURNED = 'Exception_Returned';
    public const SUB_EXCEPTION_NO_BODY = 'Exception_NoBody';
    public const SUB_EXCEPTION_SECURITY = 'Exception_Security';
    public const SUB_EXCEPTION_DAMAGE = 'Exception_Damage';
    public const SUB_EXCEPTION_REJECTED = 'Exception_Rejected';
    public const SUB_EXCEPTION_DELAYED = 'Exception_Delayed';
    public const SUB_EXCEPTION_LOST = 'Exception_Lost';
    public const SUB_EXCEPTION_DESTROYED = 'Exception_Destroyed';
    public const SUB_EXCEPTION_CANCEL = 'Exception_Cancel';

    public const REGISTERED = -18019901;
    public const UNDETECTED = -18019903;
    public const NEED_REGISTER = -18019902;
    public const NO_TRACKING_INFO = -18019909;

    protected $apiEndpoint;
    public $secretKey;


    public function __construct()
    {
        $this->apiEndpoint = config('services.tracking.17track.api_endpoint');
        $this->secretKey = config('services.tracking.17track.secret_key');
    }

    /**
     * @param $trackingNumbers
     * @return array|mixed|null
     * @throws \Throwable
     */
    public static function register($trackingNumbers)
    {
        return (new self)->postRequest('register', $trackingNumbers);
    }

    /**
     * @param $trackingNumbers
     * @return array|mixed|null
     * @throws \Throwable
     */
    public static function deleteTrack($trackingNumbers)
    {
        return (new self)->postRequest('deleteTrack', $trackingNumbers);
    }

    /**
     * @param $trackingNumbers
     * @return array
     * @throws \Throwable
     */
    public static function getTrackInfo($trackingNumbers)
    {
        $result = [
            'accepted' => [],
            'rejected' => []
        ];
        $response = (new self)->postRequest('gettrackinfo', $trackingNumbers);
        if (empty($response)) {
            return $result;
        }
        if (!empty($response['accepted'])) {
            $result['accepted'] = $response['accepted'];
        }
        if (!empty($response['rejected'])) {
            $result['rejected'] = $response['rejected'];
        }
        $result['service'] = TrackingServiceEnum::SEVENTEEN_TRACK;
        return $result;
    }

    public static function getQuota()
    {
        return (new self)->postRequest('getquota');
    }

    public function handle($url, $trackingNumbers): array
    {
        $url = !str_starts_with($url, '/') ? ('/' . $url) : $url;
        $trackingNumbers = is_array($trackingNumbers) ? $trackingNumbers : [$trackingNumbers];

        if (!empty($trackingNumbers)) {
            $trackingNumbers = array_map(function ($trackingNumber) use ($url) {
                if ($url === '/register') {
                    $trackingNumber = (array) $trackingNumber;
                    $carrier = self::detectCarrier($trackingNumber['tracking_code'], $trackingNumber['shipping_carrier'], $trackingNumber['supplier_name'] ? strtolower(trim($trackingNumber['supplier_name'])) : null, $trackingNumber['tracking_url'] ?? null);
                    $remark = 'Platform: SenPrints' . PHP_EOL;
                    if (!empty($trackingNumber['shipping_carrier'])) {
                        $remark .= 'Carrier: ' . $trackingNumber['shipping_carrier'] . PHP_EOL;
                    }
                    if (!empty($trackingNumber['tracking_url'])) {
                        $remark .= 'URL: ' . $trackingNumber['tracking_url'] . PHP_EOL;
                    }
                    $data = [
                        'number' => $trackingNumber['tracking_code'],
                        'remark' => $remark,
                    ];
                    $data['auto_detection'] = true;
                    if ($carrier) {
                        $data['carrier'] = $carrier;
                        $data['auto_detection'] = false;
                    }
                    if (data_get($trackingNumber, 'order.order_number')) {
                        $data['order_no'] = data_get($trackingNumber, 'order.order_number');
                        $data['tag'] = data_get($trackingNumber, 'id', data_get($trackingNumber, 'order_id', ''));
                    } else if (data_get($trackingNumber, 'order_id')) {
                        $data['order_no'] = data_get($trackingNumber, 'order_id');
                    }

                    if (data_get($trackingNumber, 'fulfilled_at')) {
                        $data['order_time'] = date('Y/m/d', strtotime(data_get($trackingNumber, 'fulfilled_at')));
                    }

                    return $data;
                }
                return ['number' => $trackingNumber];
            }, $trackingNumbers);
        }

        return [$url, $trackingNumbers];
    }

    /**
     * @param $url
     * @param $data
     * @param $handleExcept
     * @return array|mixed|null
     * @throws \Throwable
     */
    public function postRequest($url, $data = [], $handleExcept = false)
    {
        // Skip in local or development environment
        if (!app()->isProduction()) {
            return null;
        }
        if (!$handleExcept) {
            [$url, $data] = $this->handle($url, $data);
        }
        $response = Http::asJson()
            ->withoutVerifying()
            ->withUserAgent('Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36')
            ->withHeaders([
                '17token' => $this->secretKey,
            ])
            ->post($this->apiEndpoint . $url, $data);
        if ($response->failed()) {
            if ($response->status() === 429) {
                return null;
            }
            graylogError('[17Track] Post request failed to ' . $url, [
                'category' => 'tracking_status_logs',
                'url' => $url,
                'response_status' => $response->status(),
                'response_data' => $response->json(),
                'response_header' => json_encode($response->headers(), JSON_THROW_ON_ERROR),
            ]);
            logToDiscord('[17Track] Post request failed to ' . $url . ' - Status: ' . $response->status(), 'tracking_status_logs', true);
            return null;
        }
        $response = $response->json();
        return !empty($response['data']) ? $response['data'] : $response;
    }

    public static function statusesMapWithTrackingStatusEnum(): array
    {
        return [
            self::NOT_FOUND => TrackingStatusEnum::NOTFOUND,
            self::INFO_RECEIVED => TrackingStatusEnum::INFO_RECEIVED,
            self::IN_TRANSIT => TrackingStatusEnum::TRANSIT,
            self::OUT_FOR_DELIVERY => TrackingStatusEnum::OUT_FOR_DELIVERY,
            self::EXPIRED => TrackingStatusEnum::EXPIRED,
            self::AVAILABLE_FOR_PICKUP => TrackingStatusEnum::PICKUP,
            self::DELIVERY_FAILURE => TrackingStatusEnum::TRANSIT,
            self::DELIVERED => TrackingStatusEnum::DELIVERED,
            self::EXCEPTION => TrackingStatusEnum::EXCEPTION,
            self::UNDETECTED => TrackingStatusEnum::UNDETECTED,
            self::REGISTERED => TrackingStatusEnum::NEW
        ];
    }

    /**
     * @param $status
     * @return string|null
     */
    public static function convertTrackingStatus($status): ?string
    {
        $statuses = self::statusesMapWithTrackingStatusEnum();
        return $statuses[$status] ?? null;
    }

    /**
     * @param $code
     * @param string $message
     * @return bool
     */
    public static function isSkipCheck($code, $message = ''): bool
    {
        return $code == self::NO_TRACKING_INFO;
    }

    /**
     * @param $code
     * @param string $message
     * @return bool
     */
    public static function isNeedRegister($code, $message = ''): bool
    {
        return $code == self::NEED_REGISTER;
    }

    /**
     * @return Collection
     */
    public static function carriers(): Collection
    {
        try {
            $carriersFilePath = resource_path('/json/carriers/17track.json');
            if (!File::exists($carriersFilePath)) {
                return collect();
            }
            $carriers = File::get($carriersFilePath);
            $carriers = json_decode($carriers, true, 512, JSON_THROW_ON_ERROR);
            return collect($carriers);
        } catch (\Throwable $e) {
            return collect();
        }
    }

    public static function detectPatterns(): Collection
    {
        return collect([
            [
                'carrier_name' => ['spring'],
                'carrier_key' => 100213,
                'domain' => ['mailingtechnology.com'],
                'tracking_code_prefix' => ['3SDOKC'],
            ],
            [
                'carrier_name' => ['ups'],
                'carrier_key' => 100002,
                'domain' => ['ups.com', 'ups-mi.net', 'wwwapps.ups.com']
            ],
            [
                'carrier_name' => ['usps', 'genericstandard'],
                'carrier_key' => 21051,
                'domain' => ['usps.com', 'tools.usps.com'],
                'tracking_code_prefix' => ['92', '94'],
                'tracking_code_length' => [26]
            ],
            [
                'carrier_name' => ['royalmail'],
                'carrier_key' => 11031,
                'domain' => ['royalmail.com'],
            ],
            [
                'carrier_name' => ['bartolini'],
                'carrier_key' => 100026,
                'domain' => ['brt.it'],
            ],
            [
                'carrier_name' => ['bartolini'],
                'carrier_key' => 100026,
                'domain' => ['brt.it'],
            ],
            [
                'carrier_name' => ['gls'],
                'carrier_key' => 100305,
                'local' => 'US',
                'suppliers' => ['jondo', 'textildruck'],
                'domain' => ['gso.com'],
            ],
            [
                'carrier_name' => ['asendiausa'],
                'carrier_key' => 100016,
                'local' => 'US',
                'suppliers' => ['monsterdigital', 'dreamship', 'gooten', 'jondo', 'printlogistic', 'printlogisticv2'],
                'domain' => ['asendiausa.com', 'a1.asendiausa.com']
            ],
            [
                'carrier_name' => ['asendia'],
                'carrier_key' => 100029,
                'suppliers' => ['monsterdigital', 'dreamship', 'gooten', 'jondo', 'printlogistic', 'printlogisticv2'],
                'domain' => ['asendiausa.com', 'a1.asendiausa.com']
            ],
            [
                'carrier_name' => ['fastway'],
                'carrier_key' => 100044,
                'local' => 'AU',
                'suppliers' => ['jondo'],
                'domain' => ['fastway.com.au'],
            ],
            [
                'carrier_name' => ['dhlpaket', 'dhlwarenpost'],
                'carrier_key' => 7041,
                'domain' => ['dhl.com'],
                'tracking_code_prefix' => ['LE'],
            ],
            [
                'carrier_name' => ['dhlexpress'],
                'carrier_key' => 100001,
                'domain' => ['mydhl.express.dhl'],
            ],
            [
                'carrier_name' => ['dhl', 'dhlecommerce', 'dhlecs', 'dhlgm', 'genericstandard', 'dhlecs'],
                'carrier_key' => 7047,
                'local' => 'US',
                'suppliers' => ['gooten', 'dreamship', 'gearment', 'moteefe', 'textildruck', 'printforia', 'printlogistic', 'optondemand', 'monsterdigital'],
                'domain' => ['webtrack.dhlglobalmail.com', 'dhl.com'],
                'tracking_code_prefix' => ['GM', '00'],
                'tracking_code_length' => [20, 22]
            ],
            [
                'carrier_name' => ['genericstandard'],
                'carrier_key' => 7047,
                'local' => 'US',
                'suppliers' => ['moteefe'],
                'tracking_code_prefix' => ['GM'],
            ],
            [
                'carrier_name' => ['fedex'],
                'carrier_key' => 100003,
                'domain' => ['fedex.com']
            ],
            [
                'carrier_name' => ['mi'],
                'carrier_key' => 100002,
                'suppliers' => ['monsterdigital']
            ],
            [
                'carrier_name' => ['chitchats'],
                'domain' => ['chitchats.com'],
                'carrier_key' => 100244,
            ],
            [
                'carrier_name' => ['gtagsm'],
                'domain' => ['gtagsm.com'],
                'carrier_key' => 101119,
                'tracking_code_prefix' => ['GR'],
                'tracking_code_length' => [19]
            ],
            [
                'carrier_name' => ['canadapost'],
                'carrier_key' => 3041,
                'suppliers' => ['printgeek'],
                'tracking_code_prefix' => ['201506'],
                'tracking_code_length' => [16]
            ],
            [
                'carrier_name' => ['dpd'],
                'suppliers' => ['printlogisticv2'],
                'carrier_key' => 100111,
                'domain' => ['dpd.com.pl']
            ]
        ]);
    }

    public static function detectCarrier($trackingNumber, $carrier = null, $supplier = null, $trackingUrl = null)
    {
        if (empty($trackingNumber)) {
            return false;
        }

        $carrier = str_replace(' ', '', strtolower(trim($carrier)));
        $supplier = str_replace(' ', '', strtolower(trim($supplier)));
        $trackingUrl = trim($trackingUrl);
        $patterns = self::detectPatterns();

        $carrierFilter = $patterns->first(function ($pattern) use ($trackingNumber, $carrier, $supplier, $trackingUrl) {
            $carrierMatch = !empty($pattern['carrier_name']) && !empty($carrier) && (in_array($carrier, $pattern['carrier_name'], true) || Str::startsWith($carrier, $pattern['carrier_name']));
            $supplierMatch = !empty($pattern['suppliers']) && in_array($supplier, $pattern['suppliers'], true);
            $trackingCodePrefixMatch = !empty($pattern['tracking_code_prefix']) && collect($pattern['tracking_code_prefix'])->first(fn($prefix) => str_starts_with($trackingNumber, $prefix));
            $trackingCodeLength = !empty($pattern['tracking_code_length']) && in_array(strlen($trackingNumber), $pattern['tracking_code_length'], true);
            $domainMatch = false;
            if (!empty($pattern['domain']) && !empty($trackingUrl)) {
                $parseUrl = parse_url($trackingUrl);
                if ($parseUrl && !empty($parseUrl['host'])) {
                    $domain = str_replace('www.', '', $parseUrl['host']);
                    $domainMatch = in_array($domain, $pattern['domain'], true);
                }
            }

            // Special condition for usps
            if ($carrier === 'usps' && $trackingCodePrefixMatch && str_starts_with($trackingNumber, 91) && $trackingCodeLength && strlen($trackingNumber) === 21) {
                return true;
            }

            // Special condition for genericstandard of moteefe
            if ($carrier === 'genericstandard' && $carrierMatch && $supplier === 'moteefe' && $supplierMatch && !$trackingCodePrefixMatch) {
                return false;
            }

            if (!empty($pattern['suppliers'])) {
                return $supplierMatch && ($carrierMatch || $domainMatch || $trackingCodePrefixMatch);
            }

            return $carrierMatch || $domainMatch || $trackingCodePrefixMatch;
        });

        return !empty($carrierFilter) ? $carrierFilter['carrier_key'] : false;
    }

    /**
     * @param $orderProducts
     * @param $isChangeCarrier
     * @return void
     * @throws \Throwable
     */
    private static function overrideRegister(&$orderProducts, $isChangeCarrier = false)
    {
        try {
            $shippingCarrierPatterns = config('17track-shipping-carrier');
            $carriersAvailable = [
                'dhl',
                'asendia',
                'canada_post',
                'PRING GDS',
                'POSTNL',
                'AsendiaUsa'
            ];
            $carriersForOverride = [];
            foreach ($carriersAvailable as $carrier) {
                foreach ($orderProducts as $orderProduct) {
                    $_orderProduct = $orderProduct;
                    if (is_object($orderProduct)) {
                        $_orderProduct = (array) $orderProduct;
                    }
                    if (isset($_orderProduct['supplier_id'], $_orderProduct['tracking_code']) && empty($_orderProduct['shipping_carrier'])) {
                        if ((int) $_orderProduct['supplier_id'] === SupplierEnum::PRINTGEEK) {
                            $_orderProduct['shipping_carrier'] = 'canada_post';
                        }
                    }
                    if (isset($_orderProduct['shipping_carrier']) && (strtolower($_orderProduct['shipping_carrier']) === strtolower($carrier) || strtoupper($_orderProduct['shipping_carrier']) === strtoupper($carrier))) {
                        $carriersForOverride[$carrier][] = $_orderProduct;
                    }
                }
            }
            foreach ($carriersAvailable as $carrier) {
                if (!isset($carriersForOverride[$carrier], $shippingCarrierPatterns[$carrier])) {
                    continue;
                }
                $carrierInfo = $shippingCarrierPatterns[$carrier];
                foreach ($carriersForOverride[$carrier] as $product) {
                    if (!isset($product['order'], $product['tracking_code'])) {
                        continue;
                    }
                    $mapCarriers = array_map(function ($item) use ($product) {
                        $orderRegion = '';
                        $regionCode = '';
                        $orderCountry = '';
                        if (isset($product['order']['country'])) {
                            $systemLocation = SystemLocation::query()->where('code', $product['order']['country'])->first();
                            if ($systemLocation) {
                                $regionCode = $systemLocation->region_code;
                                $orderRegion = $systemLocation->region;
                            }
                            $orderCountry = $product['order']['country'];
                        }

                        $item['matching_score'] = $item['priority_score'];
                        $orderCountryLower = strtolower($orderCountry);
                        if ($item['_country_iso'] == $orderCountry && str_contains($item['_name'], $orderCountryLower) && $item['region_code'] === $regionCode) {
                            $item['matching_score'] += 10;
                            return $item;
                        }
                        if ($item['_country_iso'] == $orderCountry && str_contains($item['_name'], $orderCountryLower)) {
                            $item['matching_score'] += 9;
                            return $item;
                        }

                        if ($item['_country_iso'] == $orderCountry && (str_contains($item['_name'], strtolower($orderRegion)) || str_contains($item['_name'], strtolower($regionCode)))) {
                            $item['matching_score'] += 9;
                            return $item;
                        }

                        if (str_contains($item['_name'], $orderCountryLower) || str_contains($item['_name'], $orderCountry)) {
                            $item['matching_score'] += 8;
                            return $item;
                        }

                        if ($item['_country_iso'] == $orderCountryLower || $item['_country_iso'] == $orderCountry) {
                            $item['matching_score'] += 7;
                            return $item;
                        }

                        if ($item['region_code'] == $regionCode) {
                            $item['matching_score'] += 6;
                            return $item;
                        }

                        if (isset($product['supplier']['location']) && str_contains($product['supplier']['location'], $item['region_code'])) {
                            $item['matching_score'] += 5;
                            return $item;
                        }

                        if ($item['region_code'] === '*') {
                            $item['matching_score'] += 4;
                            return $item;
                        }
                        $item['matching_score'] += 0;
                        return $item;
                    }, $carrierInfo);
                    $mapCarriers = array_filter($mapCarriers, function ($item) {
                        return isset($item);
                    });

                    usort($mapCarriers, function ($a, $b) {
                        return $b['matching_score'] - $a['matching_score'];
                    });
                    foreach ($mapCarriers as $carrierMapped) {
                        $registerComplete = self::changeShippingCarrierAndUpdateTracking($product['id'], $product['tracking_code'], $carrierMapped['key'], count($mapCarriers), $isChangeCarrier);
                        if ($registerComplete) {
                            break;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            graylogError('Override register with specific carriers errors.', [
                'category' => 'override_register_with_specific_carriers_error',
                'error' => $e,
            ]);
        }
    }

    /**
     * @param $orderProductId
     * @param $trackingCode
     * @param $shippingCarrierCode
     * @param $totalCarriers
     * @param $isChangeCarrier
     * @return bool
     * @throws \Throwable
     */
    private static function changeShippingCarrierAndUpdateTracking($orderProductId, $trackingCode, $shippingCarrierCode, $totalCarriers, $isChangeCarrier = false)
    {
        try {
            $seventeenTrackInstance = new self();
            $getTrackInfo = self::getTrackingInfoOfSingleTrackingCode($trackingCode);
            $response = self::handleResponseInfoOverrideRegister($getTrackInfo);
            $query = OrderProductLog::query()->where('order_product_id', $orderProductId)->where('type', OrderProductLogEnum::REGISTER_TRACKING_CODE);
            $recordExisted = $query->clone()->first();
            if (!$response) {
                if (isset($recordExisted)) {
                    $carrierScanned = self::reformatListCarrierScanned($recordExisted->content);
                    $nextScanAt = now()->addMinutes(5)->toDateTimeString();
                    if (in_array($shippingCarrierCode, $carrierScanned, true)) {
                        return false;
                    }
                    if (count($carrierScanned) == $totalCarriers && in_array($shippingCarrierCode, $carrierScanned)) {
                        $carrierScanned[] = 'DONE';
                        $nextScanAt = null;
                    }
                    if ($shippingCarrierCode !== '' && !in_array($shippingCarrierCode, $carrierScanned)) {
                        $carrierScanned[] = $shippingCarrierCode;
                    }
                    $query->update([
                        'content' => implode(',', $carrierScanned),
                        'custom_timestamp' => $nextScanAt
                    ]);
                } else {
                    OrderProductLog::create([
                        'order_product_id' => $orderProductId,
                        'type' => OrderProductLogEnum::REGISTER_TRACKING_CODE,
                        'content' => $shippingCarrierCode,
                        'custom_timestamp' => now()->addMinutes(5)->toDateTimeString()
                    ]);
                }
                if ($isChangeCarrier) {
                    $seventeenTrackInstance->postRequest('/changecarrier', [[
                        'number' => $trackingCode,
                        'carrier_new' => $shippingCarrierCode,
                    ]], true);
                } else {
                    $seventeenTrackInstance->postRequest('/register', [[
                        'number' => $trackingCode,
                        'carrier' => $shippingCarrierCode,
                    ]], true);
                }
            } else if (isset($recordExisted)) {
                $carrierScanned = self::reformatListCarrierScanned($recordExisted->content);
                $carrierScanned[] = 'DONE';
                $query->update([
                    'content' => implode(',', $carrierScanned),
                    'custom_timestamp' => null
                ]);
            }
            return true;
        } catch (\Exception $e) {
            graylogError('Override register with specific carriers errors in changeShippingCarrierAndUpdateTracking.', [
                'category' => 'override_register_with_specific_carriers_error',
                'error' => $e,
            ]);
            return true;
        }
    }

    /**
     * @param $trackingCode
     * @return array|mixed|null
     * @throws \Throwable
     */
    private static function getTrackingInfoOfSingleTrackingCode($trackingCode)
    {
        return (new self)->postRequest('/gettrackinfo', [[
            'number' => $trackingCode,
        ]], true);
    }

    /**
     * @param $getTrackInfo
     * @return bool
     */
    private static function handleResponseInfoOverrideRegister($getTrackInfo): bool
    {
        if (!empty($getTrackInfo['accepted'])) {
            $acceptedCode = $getTrackInfo['accepted'][0];
            $status = self::convertTrackingStatus($acceptedCode['track_info']['latest_status']['status']);
            if ($status !== TrackingStatusEnum::NOTFOUND && $status !== TrackingStatusEnum::UNDETECTED) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param $breakRecursion
     * @return void
     * @throws \Throwable
     */
    public static function scanRegisterTrackingCodeWithDiffCarrier($breakRecursion = false)
    {
        if (self::reachQuotaLimit()) {
            return;
        }
        $startDate = now()->subMinutes(30);
        $endDate = now();
        $orderProductsLogs = OrderProductLog::query()
            ->where('type', OrderProductLogEnum::REGISTER_TRACKING_CODE)
            ->where('custom_timestamp', '<', now()->toDateTimeString())
            ->whereRaw("content NOT REGEXP CONCAT('(^|,)', ?, '(,|$)')", ['DONE'])
            ->whereHas('order_product', function ($query) use ($startDate, $endDate) {
                $query->where('fulfill_status', OrderFulfillStatus::ON_DELIVERY)
                ->whereBetween('fulfilled_at', [$startDate->toDateTimeString(), $endDate->toDateTimeString()])
                ->where(function ($q) {
                    $q->where('tracking_status', '!=', TrackingStatusEnum::DELIVERED)->orWhere('tracking_status', '=', null);
                });
            })
            ->distinct('order_product_id')
            ->orderBy('created_at')
            ->limit(10)
            ->get();

        if ($orderProductsLogs->isEmpty()) {
            if ($breakRecursion) {
                return;
            }
            self::reactiveScannedOrderProducts();
        }
        $orderProductsIds = $orderProductsLogs->pluck('order_product_id')->toArray();
        $orderProductsLogs = $orderProductsLogs->keyBy('order_product_id')->toArray();
        $orderProducts = OrderProduct::query()
            ->select([
                'id',
                'order_id',
                'tracking_code',
                'shipping_carrier',
                'tracking_service',
                'fulfilled_at',
                'received_at',
                'delivered_at',
                'tracking_status as status',
                'supplier_name',
                'supplier_id'
            ])
            ->whereIn('id', $orderProductsIds)
            ->where('fulfill_status', OrderFulfillStatus::ON_DELIVERY)
            ->where(function ($q) {
                $q->where('tracking_status', '!=', TrackingStatusEnum::DELIVERED)->orWhere('tracking_status', '=', null);
            })
            ->with([
                'order:id,order_number,country',
                'supplier:id,location'
            ])
            ->get()
            ->toArray();
        $orderProducts = array_map(function ($item) use ($orderProductsLogs) {
            $item['log'] = $orderProductsLogs[$item['id']] ?? null;
            return $item;
        }, $orderProducts);
        self::overrideRegister($orderProducts, true);
    }

    /**
     * @param $carriersString
     * @return mixed
     */
    private static function reformatListCarrierScanned($carriersString)
    {
        $carrierScanned = explode(',', $carriersString);
        return collect($carrierScanned)->filter()->unique()->values()->toArray();
    }

    /**
     * @return bool
     */
    private static function reachQuotaLimit()
    {
        $getQuota = self::getQuota();
        cacheGet(
            CacheKeys::get17TrackQuota(),
            CacheKeys::CACHE_1H,
            static function () use ($getQuota) {
                $getQuota['timestamp'] = now()->toDateTimeString();
                return $getQuota;
            }
        );

        if (!isset($getQuota['max_track_perday']) || !isset($getQuota['today_used'])) {
            return true;
        }
        return $getQuota['max_track_perday'] <= $getQuota['today_used'];
    }

    /**
     * @return void
     * @throws \Throwable
     */
    private static function reactiveScannedOrderProducts()
    {
        OrderProductLog::query()
            ->where('type', OrderProductLogEnum::REGISTER_TRACKING_CODE)
            ->whereRaw("content REGEXP CONCAT('(^|,)', ?, '(,|$)')", ['DONE'])
            ->whereHas('order_product', function ($query) {
                $query->where('fulfill_status', OrderFulfillStatus::ON_DELIVERY)
                    ->where(function ($q) {
                        $q->where('tracking_status', '!=', TrackingStatusEnum::DELIVERED)
                            ->orWhere('tracking_status', '=', null);
                    });
            })
            ->orderBy('created_at')
            ->limit(10)
            ->update([
                'content' => null,
                'custom_timestamp' => now()->toDateTimeString(),
            ]);
        self::scanRegisterTrackingCodeWithDiffCarrier(true);
    }

    /**
     * @param $trackingCodes
     * @return array|false
     */
    public static function registerOrderTracking($trackingCodes)
    {
        try {
            $insertCodes = [];
            $updateCodes = [];
            $orderProducts = $trackingCodes->toArray();
            self::overrideRegister($orderProducts);
            $register = self::register($orderProducts);
            if (!empty($register['errors'])) {
                foreach ($register['errors'] as $error) {
                    graylogInfo('[17Track] Register tracking number failed - Code: ' . $error['code'] . ' - Message: ' . $error['message'], [
                        'category' => 'tracking_status_logs',
                    ]);
                }
                return false;
            }
            if (!empty($register['rejected'])) {
                foreach ($register['rejected'] as $rejectedCode) {
                    if (in_array($rejectedCode['error']['code'], ['-18019908', '-18019907'])) {
                        if (Cache::get('17track_register_error')) {
                            continue;
                        }
                        Cache::add('17track_register_error', $rejectedCode['error']['message'], CacheKeys::CACHE_1H);
                    }

                    graylogInfo('[17Track] Register tracking number failed - Number: ' . $rejectedCode['number'] . ' - Code: ' . $rejectedCode['error']['code'] . ' - Message: ' . $rejectedCode['error']['message'], [
                        'category' => 'tracking_status_logs',
                        'tracking_code' => $rejectedCode['number'],
                    ]);

                    if ($rejectedCode['error']['code'] != self::UNDETECTED && $rejectedCode['error']['code'] != self::REGISTERED) {
                        $trackingStatus = TrackingStatus::query()->where([
                            'tracking_code' => $rejectedCode['number'],
                        ])->first();
                        $updateCodes[] = [
                            [
                                'tracking_code' => $rejectedCode['number']
                            ],
                            [
                                'tracking_status' => $trackingStatus->status ?? TrackingStatusEnum::NOTFOUND,
                                'tracking_service' => TrackingServiceEnum::SEVENTEEN_TRACK
                            ]
                        ];
                        continue;
                    }
                    $code = TrackingService::getObjectTrackingCode($trackingCodes, $rejectedCode['number']);
                    if (empty($code)) {
                        continue;
                    }
                    $status = self::convertTrackingStatus($rejectedCode['error']['code']);
                    $updateCodes[] = [
                        [
                            'tracking_code' => $code->tracking_code
                        ],
                        [
                            'tracking_status' => $status,
                            'tracking_service' => TrackingServiceEnum::SEVENTEEN_TRACK
                        ]
                    ];
                    $insertCodes[$code->tracking_code] = [
                        [
                            'tracking_code' => $code->tracking_code,
                            'order_number' => $code->order->order_number ?? $code->order_id,
                        ],
                        [
                            'order_id' => $code->order_id ?? $code->order->order_number,
                            'status' => $status,
                            'tracking_service' => TrackingServiceEnum::SEVENTEEN_TRACK
                        ]
                    ];
                }
            }

            if (!empty($register['accepted'])) {
                foreach ($register['accepted'] as $acceptedCode) {
                    $code = TrackingService::getObjectTrackingCode($trackingCodes, $acceptedCode['number']);
                    if (empty($code)) {
                        continue;
                    }
                    $insertCodes[$code->tracking_code] = [
                        [
                            'tracking_code' => $code->tracking_code,
                            'order_number' => $code->order->order_number ?? $code->order_id,
                        ],
                        [
                            'order_id' => $code->order_id ?? $code->order->order_number,
                            'status' => TrackingStatusEnum::NEW,
                            'shipping_carrier' => $acceptedCode['carrier'],
                            'tracking_service' => TrackingServiceEnum::SEVENTEEN_TRACK
                        ]
                    ];
                    $updateCodes[] = [
                        [
                            'tracking_code' => $code->tracking_code
                        ],
                        [
                            'tracking_status' => TrackingStatusEnum::NEW,
                            'tracking_service' => TrackingServiceEnum::SEVENTEEN_TRACK
                        ]
                    ];
                }
            }
            return [$insertCodes, $updateCodes];
        } catch (\Throwable $e) {
            logException($e, 'Error in register tracking code on the 17track.');
            return [[], []];
        }
    }

    /**
     * @param $request
     * @return bool
     */
    public function verifySignature($request): bool
    {
        try {
            $sign = $request->header('sign');
            if (empty($sign)) {
                return false;
            }
            $data = $request->getContent();
            $originalSign = $data . '/' . config("services.tracking.17track.secret_key");
            return $sign === hash('sha256', $originalSign);
        } catch (\Exception $e) {
            logToDiscord('[17Track] Failed to verify signature, message: ' . $e->getMessage(), 'tracking_status_logs');
            return false;
        }
    }
}

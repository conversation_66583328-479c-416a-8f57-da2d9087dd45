<?php

namespace App\Services;

use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

class PsdRenderWorker
{
    protected AMQPStreamConnection $connection;
    protected AMQPChannel $channel;
    private string $psdFile = '';
    private string $defaultLayer;
    private array $message;
    private string $resultPath = '';
    private string $artworkUrl = '';
    private string $webhookUrl;

    public function __construct()
    {
        $templateJson = resource_path('/json/psd-queue-message.json');
        $this->message = json_decode(file_get_contents($templateJson), true);
        $this->defaultLayer = 'print'; //TODO: add default layer name
    }

    /**
     * @param string $layerName
     * @return void
     */
    public function setDefaultLayer(string $layerName)
    {
        $this->defaultLayer = trim($layerName);
    }

    /**
     * @param string $resultPath
     * @return void
     */
    public function setResultPath(string $resultPath)
    {
        $this->resultPath = $resultPath;
    }

    /**
     * @param string $psdUrl
     * @return void
     */
    public function setPsdUrl(string $psdUrl)
    {
        $this->psdFile = $psdUrl;
    }

    /**
     * @param string $artworkUrl
     * @return void
     */
    public function setArtworkUrl(string $artworkUrl)
    {
        $this->artworkUrl = $artworkUrl;
    }

    /**
     * @param string $webhookUrl
     * @return void
     */
    public function setWebhookUrl(string $webhookUrl)
    {
        $this->webhookUrl = $webhookUrl;
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function send() {
        try {
            $this->connection = new AMQPStreamConnection(
                config('psd-render-worker.host'),
                config('psd-render-worker.port'),
                config('psd-render-worker.user'),
                config('psd-render-worker.password'),
                config('psd-render-worker.vhost')
            );
            $this->channel = $this->connection->channel();
            $amqpMessage = new AMQPMessage(
                json_encode($this->message, JSON_UNESCAPED_SLASHES),
                [
                    'content_type' => 'application/json',
                    'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT
                ]
            );
            $this->channel->queue_declare(
                config('psd-render-worker.queue'),
                false,
                true,
                false,
                false
            );
            $this->channel->exchange_declare(
                config('psd-render-worker.exchange'),
                'direct',
                false,
                true,
                false
            );
            $this->channel->queue_bind(
                config('psd-render-worker.queue'),
                config('psd-render-worker.exchange')
            );
            $this->channel->basic_publish(
                $amqpMessage,
                config('psd-render-worker.exchange')
            );
            $this->channel->close();
            $this->connection->close();
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function run()
    {
        // set default layer name at first artwork info
        $this->message['artwork'][0]['layer_name'] = $this->defaultLayer;
        $this->message['webhook_url'] = $this->webhookUrl;
        $this->message['origin_webhook_url'] = $this->message['webhook_url'];
        $this->message['result_host'] = config('psd-render-worker.result_host');
        $this->message['result_disk'] = config('psd-render-worker.result_disk');
        $templateHash = md5($this->psdFile);
        $mockup = [
            "psd" => $this->psdFile,
            "smart_object_info" => [
                [
                    "artwork_name" => "front",
                    "color_selector" => "",
                    "enable_color_selector" => "",
                    "selector" => $this->defaultLayer,
                    "url" => $this->artworkUrl
                ]
            ]
        ];
        $this->message['mockups'][0] = $mockup;
        if (!empty($this->resultPath)) {
            $this->message['result_path'] = $this->resultPath;
        } else {
            $fileName = md5($this->psdFile . $this->artworkUrl). time() . '.png';
            $this->message['result_path'] = 'p/' . $templateHash . '/' . $fileName;
        }
        $this->send();
    }
}

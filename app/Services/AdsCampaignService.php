<?php

namespace App\Services;

use App\Enums\AdsCampaignApplyForEnum;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Enums\SellerHistoryActionEnum;
use App\Enums\SellerNotificationTypeEnum;
use App\Models\AdsCampaign;
use App\Models\AdsCampaignLog;
use App\Models\SellerHistory;
use App\Models\SellerNotification;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\SellerTier\Models\SellerTier;
use Throwable;

class AdsCampaignService
{
    private const KEY = 'utm_campaign';

    /**
     * @param Request $request
     * @param int $action
     * @param int|null $userId
     * @return void
     */
    public static function track(Request $request, int $action, ?int $userId): void
    {
        if ($userId) {
            $adsCode = $request->get(self::KEY);
            if (empty($adsCode)) {
                return;
            }
            try {
                $ads = self::getAdsFromCode($adsCode);
                if ($ads->isEngaged($userId)) {
                    return;
                }
                $user = User::query()->find($userId);
                $ads_options = Str::isJson($ads->options) ? json_decode($ads->options, true, 512, JSON_THROW_ON_ERROR) : [];
                if ($user) {
                    if (!empty($ads_options) && isset($ads_options['apply_for'])) {
                        $apply_for = (int) $ads_options['apply_for'];
                        $processBonus = false;
                        if ($apply_for === AdsCampaignApplyForEnum::ALL_ACCOUNT) {
                            $processBonus = true;
                        }
                        if ($apply_for === AdsCampaignApplyForEnum::ONLY_NEW_ACCOUNT && $user->created_at->gte($ads->created_at) && $user->created_at->lte($ads->expired_at)) {
                            $processBonus = true;
                        }
                        if ($apply_for === AdsCampaignApplyForEnum::ONLY_OLD_ACCOUNT && $user->created_at->lte($ads->created_at)) {
                            $processBonus = true;
                        }
                        if ($processBonus) {
                            self::processBonus($ads, $user);
                        }
                    } else {
                        self::processBonus($ads, $user);
                    }
                    AdsCampaignLog::query()->create([
                        'ads_campaign_id' => $ads->id,
                        'seller_id' => $userId,
                        'action' => $action,
                    ]);
                }
            } catch (Throwable $e) {
                if ($e->getMessage() === 'Ads campaign is expired or not found') {
                    return;
                }
                logException($e, 'AdsCampaignService::track(' . $adsCode . ')');
            }
        }
    }

    /**
     * @param Request $request
     * @return void
     */
    public static function trackClick(Request $request): void
    {
        // Hien tai: Su dung client de handle case user click multiple times
        $adsCode = $request->get(self::KEY);
        if (!$adsCode) {
            return;
        }
        try {
            $ads = self::getAdsFromCode($adsCode);
            $ads->total_click++;
            $ads->save();
        } catch (Throwable $e) {
            if ($e->getMessage() === 'Ads campaign is expired or not found') {
                return;
            }
            logException($e, 'AdsCampaignService::track(' . $adsCode . ')');
        }
    }

    /**
     * @param AdsCampaign $ads
     * @param User $user
     * @return void
     */
    private static function processBonus(AdsCampaign $ads, User $user): void
    {
        try {
            $message = null;
            if ($ads->bonus_money > 0) {
                self::processBonusMoney($ads, $user);
                $message = "You have received <b>{$ads->bonus_money}$</b> from promotion.";
            }

            $currentTier = $user->tier_id ?? 0;
            if ($ads->bonus_tier > $currentTier) {
                self::processBonusTier($ads, $user);
                $message .= "<br>Your account has been upgraded to <b>Tier {$ads->bonus_tier}</b>";
            } else if ($ads->plus_tier > 0) {
                $newTier = self::processPlusTier($ads, $user);
                $message .= "<br>Your account has been upgraded to <b>Tier {$newTier}</b>";
            }
            if ($message) {
                SellerNotification::query()->create([
                    'seller_id' => $user->id,
                    'subject' => 'Ads Campaign',
                    'message' => $message,
                    'expiry_date' => now()->addDays(7),
                    'type' => SellerNotificationTypeEnum::ONE_TIME,
                    'admin_id' => 1,
                ]);
            }
        } catch (Throwable $e) {
            logException($e);
        }
    }

    /**
     * @param AdsCampaign $ads
     * @param User $user
     */
    private static function processBonusMoney(AdsCampaign $ads, User $user): void
    {
        $user->updateBalance2($ads->bonus_money, SellerBillingType::OTHER, 'Bonus from ads campaign', 0, 90, SellerBillingStatus::COMPLETED, false);
    }

    /**
     * @param AdsCampaign $ads
     * @param User $user
     */
    private static function processBonusTier(AdsCampaign $ads, User $user): void
    {
        try {
            $tier = SellerTier::query()->whereKey($ads->bonus_tier)->first();
            $currentTier = SellerTier::query()->whereKey($user->tier_id)->first();
            $tierName = $tier->name ?? 'None';
            $fromTierName = $currentTier->name ?? 'None';
            $user->fill(['tier_id' => $ads->bonus_tier]);
            $user->save();
            SellerHistory::query()->insert(array(
                'tier_id' => $ads->bonus_tier,
                'seller_id' => $user->id,
                'action' => SellerHistoryActionEnum::UPDATE_TIER,
                'details' => "Your account has been upgraded to " . $tierName,
            ));
            $embedDesc = [
                [
                    'description' => "Congratulation! Seller {$user->email} has been upgraded from {$fromTierName} to {$tierName}.",
                    'color' => 5763719
                ]
            ];
            logToDiscord('', 'seller_tier', false, true, 7, $embedDesc);
        } catch (Throwable $e) {
            logException($e);
        }
    }

    /**
     * @param AdsCampaign $ads
     * @param User $user
     * @return int
     */
    private static function processPlusTier(AdsCampaign $ads, User $user): int
    {
        try {
            $maxTier = SellerTier::query()
                ->max('id');
            $currentTier = $user->tier_id ?? 0;
            $newTier = $currentTier + $ads->plus_tier;
            if ($newTier > $maxTier) {
                $newTier = $maxTier;
            }
            $user->tier_id = $newTier;
            $user->save();
            SellerHistory::query()->insert(array(
                'tier_id' => $newTier,
                'seller_id' => $user->id,
                'action' => SellerHistoryActionEnum::UPDATE_TIER,
                'details' => "Your account has been upgraded to Tier " . $newTier,
            ));
            $embedDesc = [
                [
                    'description' => "Congratulation! Seller {$user->email} has been upgraded from {$currentTier} to {$newTier}.",
                    'color' => 5763719
                ]
            ];
            logToDiscord('', 'seller_tier', false, true, 7, $embedDesc);
            return $newTier;
        } catch (Throwable $e) {
            logException($e);
            return 0;
        }
    }

    /**
     * @param string $adsCode
     * @return AdsCampaign
     * @throws Throwable
     */
    private static function getAdsFromCode(string $adsCode): AdsCampaign
    {
        $ads = AdsCampaign::query()
            ->where('utm_campaign', $adsCode)
            ->isRunning()
            ->first();
        throw_if(!$ads, new \RuntimeException('Ads campaign is expired or not found'));
        return $ads;
    }
}

<?php

namespace App\Services;
use App\Enums\StorageDisksEnum;
use Exception;
use Illuminate\Http\UploadedFile;
use Symfony\Component\HttpFoundation\File\UploadedFile as SymfonyUploadedFile;
use Illuminate\Support\Facades\Storage;

class FileUploadService {
    public static function uploadFileBase64($base64Image, $folder): ?string
    {
        if (preg_match('/^data:image\/(\w+);base64,/', $base64Image, $matches)) {
            $imageExtension = $matches[1];
            $base64Image = substr($base64Image, strpos($base64Image, ',') + 1);
        } else {
            return null;
        }

        $image = base64_decode($base64Image);
        $fileName = substr(md5(time()), 0, 16) . '.' . $imageExtension;
        $tmpFilename = tempnam(sys_get_temp_dir(), 'SP') . '.' . $imageExtension;
        file_put_contents($tmpFilename, $image);

        try {
            $symfonyFile = new SymfonyUploadedFile(
                $tmpFilename,
                $fileName,
                'image/' . $imageExtension,
                null,
                true
            );

            $newFile = UploadedFile::createFromBase($symfonyFile);
            $response = $newFile->storePubliclyAs($folder, $fileName, StorageDisksEnum::DEFAULT);
            @unlink($tmpFilename);
            return $response;
        } catch (Exception $e) {
            @unlink($tmpFilename);
            return null;
        }
    }

    public static function duplicateFile($path, $newName): ?string
    {
        $hasExtension = pathinfo($newName, PATHINFO_EXTENSION);
        if (empty($hasExtension)) {
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            $newName = $newName . '.' . $extension;
        }

        try {
            Storage::disk(StorageDisksEnum::DEFAULT)->copy($path, $newName);
            return $newName;
        } catch (Exception $e) {
            return null;
        }
    }
}

<?php

namespace App\Services;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\ProductReviewAllowSharingEnum;
use App\Enums\ProductReviewEnum;
use App\Models\ProductReview;
use App\Models\ProductReviewKeyword;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class ProductReviewService
{
    private const KEYWORD_PER_PAGE = 25;

    /**
     * @param $request
     * @return Collection|Builder|Model
     */
    public static function loadAllKeyWord($request)
    {
        $perPage = $request->get('per_page', self::KEYWORD_PER_PAGE);
        $keyword = $request->get('q', null);
        $sortBy = $request->get('sort') ?? 'created_at';
        $sortDirecion = $request->get('direction') ?? 'desc';
        $query = ProductReviewKeyword::query()
            ->select([
                'id',
                'keyword'
            ]);
        if (isset($keyword)) {
            $query = $query->where('keyword', 'like', '%' . $keyword . '%');
        }
        $query = $query->orderBy($sortBy, $sortDirecion);
        return $query->paginate($perPage);
    }

    /**
     * @param $request
     * @return Boolean
     */
    public static function addKeyWord($request)
    {
        $keyword = $request->post('keyword');
        ProductReviewKeyword::create([
            'keyword' => $keyword
        ]);
        return true;
    }

    /**
     * @param $request
     * @return Boolean
     */
    public static function updateKeyWord($request)
    {
        $keyword = $request->post('keyword');
        $id = $request->post('id');
        ProductReviewKeyword::find($id)
            ->update([
                'keyword' => $keyword
            ]);
        return true;
    }

    /**
     * @param $keywordId
     * @return Boolean
     */
    public static function deleteKeyWord($keywordId)
    {
        ProductReviewKeyword::find($keywordId)->delete();
        return true;
    }

    /**
     * @param $request
     * @return Collection|Builder|Model
     */
    public static function loadKeyWord($keywordId)
    {
        return ProductReviewKeyword::query()->select(['id', 'keyword'])->find($keywordId);
    }

    public static function getProductReviewStats($campaignId, $templateId, $sellerId)
    {
        return cacheAlt()->remember(CacheKeys::STOREFRONT_PRODUCT_REVIEW_STATS_PREFIX . '_' . $campaignId . '_' . $templateId . '_' . $sellerId, CacheTime::CACHE_24H, function () use ($campaignId, $templateId, $sellerId) {
            try {
                $stats = ProductReview::query()
                    ->on('mysql_main_us')
                    ->select([
                        DB::raw('CAST(TRUNCATE(COALESCE(AVG(average_rating), 0), 1) AS UNSIGNED) as average_rating'),
                        DB::raw('COUNT(*) as review_count'),
                        DB::raw("SUM(CASE WHEN average_rating = 5 THEN 1 ELSE 0 END) AS five_star_count"),
                        DB::raw("SUM(CASE WHEN average_rating >= 4 AND average_rating < 5 THEN 1 ELSE 0 END) AS four_star_count"),
                        DB::raw("SUM(CASE WHEN average_rating >= 3 AND average_rating < 4 THEN 1 ELSE 0 END) AS three_star_count"),
                        DB::raw("SUM(CASE WHEN average_rating >= 2 AND average_rating < 3 THEN 1 ELSE 0 END) AS two_star_count"),
                        DB::raw("SUM(CASE WHEN average_rating >= 1 AND average_rating < 2 THEN 1 ELSE 0 END) AS one_star_count"),
                    ])
                    ->fromRaw('product_reviews USE INDEX (idx_review_coverage_v1)')
                    ->where(function ($query) use ($templateId, $campaignId) {
                        $query->where('template_id', (int)$templateId);

                        if (!empty($campaignId)) {
                            $query->orWhere('campaign_id', (int)$campaignId);
                        }
                    })
                    ->where(function ($query) use ($sellerId) {
                        $query->where('seller_id', (int)$sellerId)
                            ->orWhere('allow_sharing', '!=', ProductReviewAllowSharingEnum::DENY);
                    })
                    ->where('status', ProductReviewEnum::ACTIVE)
                    ->get()
                    ->first()
                    ->toArray();

                $bestRating = null;
                $worstRating = null;
                $rateKeys = ['one', 'two', 'three', 'four', 'five'];
                foreach ($rateKeys as $key => $rate) {
                    if ($stats[$rate . '_star_count'] && [$rate . '_star_count'] > 0) {
                        $worstRating = $worstRating ?? $key + 1;
                        $bestRating = $key + 1;
                    }
                }
                $stats['best_rating'] = $bestRating;
                $stats['worst_rating'] = $worstRating;
                return $stats;
            } catch (\Exception $e) {
                return [
                    'average_rating' => 0,
                    'review_count' => 0,
                    'five_star_count' => null,
                    'four_star_count' => null,
                    'three_star_count' => null,
                    'two_star_count' => null,
                    'one_star_count' => null,
                    'best_rating' => null,
                    'worst_rating' => null,
                ];
            }
        });
    }

    public static function scoreCalc($averageRating, $comment, $assets, $timestamp)
    {
        $ratingScore = $averageRating / 5 * 40;
        $commentScore = min(str_word_count($comment), 100) / 100 * 30;
        $assetScore = min(count($assets), 3) / 3 * 20;
        $timeScore = $timestamp / 86400;

        return $ratingScore + $commentScore + $assetScore + $timeScore;
    }
}

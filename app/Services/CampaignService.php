<?php

namespace App\Services;

use App\Enums\CampaignPublicStatusEnum;
use App\Enums\CampaignRenderModeEnum;
use App\Enums\CampaignStatusEnum;
use App\Enums\CampaignTrademarkStatusEnum;
use App\Enums\CurrencyEnum;
use App\Enums\DmcaNotificationExecutionEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\PricingModeEnum;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\SystemConfigController;
use App\Models\Campaign;
use App\Models\CampaignDMCANotification;
use App\Models\Collection;
use App\Models\Elastic;
use App\Models\File;
use App\Models\IndexProduct;
use App\Models\Product;
use App\Models\ProductCollection;
use App\Models\ProductVariant;
use App\Models\ReportedCampaign;
use App\Models\SellerCollection;
use App\Models\Slug;
use App\Models\StoreProduct;
use App\Models\SystemConfig;
use App\Models\Template;
use App\Models\Upsell;
use App\Models\User;
use Carbon\Carbon;
use Cloudinary\Cloudinary;
use Cloudinary\Transformation\Argument\Color;
use Cloudinary\Transformation\Effect;
use Cloudinary\Transformation\Format;
use Cloudinary\Transformation\ImageTransformation;
use Cloudinary\Transformation\Overlay;
use Cloudinary\Transformation\Reshape;
use Cloudinary\Transformation\Resize;
use Cloudinary\Transformation\Source;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Imagick;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\Campaign\Jobs\SyncSlugJob;
use Modules\Campaign\Models\ImportCampaignsData;
use Normalizer;

class CampaignService
{
    /**
     * @param $name
     * @return Collection|Builder|Model
     */
    public static function addCollection($name)
    {
        // remove all special character
        $name = preg_replace('/[^a-zA-Zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ0-9 ]/iu', '', $name);
        $name = trim($name);

        // upper case first letter
        $name = ucwords(strtolower($name));
        $slug = Str::slug($name);
        return Collection::query()->firstOrCreate(['slug' => $slug], ['name' => $name]);
    }

    /**
     * @param $productId
     * @param $sellerId
     * @param $collectionId
     * @return bool
     */
    public static function addCampaignToCollection($productId, $sellerId, $collectionId): bool
    {
        $seller = User::query()->find($sellerId);
        $collection = Collection::query()->find($collectionId);
        $product = Product::query()
            ->onSellerConnection($seller)
            ->find($productId);
        if (!$collection || !$product) {
            return false;
        }

        // use upsert instead of firstOrCreate to not make duplicate exception for multiple connection
        // and faster for not make one more query select
        SellerCollection::query()->upsert([
            'collection_id' => $collectionId,
            'seller_id' => $sellerId
        ], ['collection_id', 'seller_id']);

        ProductCollection::query()->upsert([
            'collection_id' => $collectionId,
            'product_id' => $productId,
            'seller_id' => $sellerId
        ], ['collection_id', 'product_id', 'seller_id']);

        return true;
    }

    /**
     * @param $data
     * @param bool $returnObject
     * @return int|Campaign
     */
    public static function createDraftCampaign($data, bool $returnObject = false)
    {
        try {
            $user = currentUser();
            $userId = $user->getUserId();
            $seller = User::query()->find($userId);

            if ($userId) {
                $authId = $user->getAuthorizedAccountId();
                if (empty($data['market_location']) && empty($data['pricing_mode']) && empty($data['currency_code'])) {
                    $userInfo = $user->getInfo();
                    $data['market_location'] = $userInfo->market_location ?? 'US';
                    $data['pricing_mode'] = $userInfo->pricing_mode ?? 'fixed';
                    $data['currency_code'] = $userInfo->currency ?? 'USD';
                }
            }

            $userId = $userId ?? null;
            $authId = $authId ?? null;
            $draft_campaign = array(
                'seller_id' => $data['seller_id'] ?? $userId,
                'auth_id' => $authId ?? $userId,
                'product_type' => ProductType::CAMPAIGN,
                'status' => ProductStatus::DRAFT,
                'slug' => self::generateCampaignSlug($data['name'], $data['prefix'] ?? '', $data['suffix'] ?? '')
            );
            unset($data['suffix'], $data['prefix']);
            $draft_campaign = array_merge($draft_campaign, $data);
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->create($draft_campaign);

            if ($returnObject) {
                return $campaign;
            }

            return $campaign->id;
        } catch (\Exception $e) {
            logException($e);
            return null;
        }
    }

    public static function generateCampaignSlug(string $name, string $prefix, string $suffix): string
    {
        $slug = self::nameToSlug($name, $prefix, $suffix);
        $tmpSlug = $slug;
        $count = 1;
        while (Slug::isInvalid($slug)) {
            $slug = $tmpSlug . '-' . $count;
            $count++;
        }
        return $slug;
    }

    /**
     * @param string $name
     * @param string $prefix
     * @param string $suffix
     * @param $sellerId
     * @return string
     * @throws \Throwable
     */
    public static function generateCampaignSlugAndClearDraftCampaign(string $name, string $prefix, string $suffix, $sellerId): string
    {
        $slug = self::nameToSlug($name, $prefix, $suffix);
        $maxSuffix = Slug::findMaxSlugSuffix($slug);
        $tmpSlug = $slug;
        if ($maxSuffix > 0) {
            $tmpSlug .= '-' . $maxSuffix;
        }

        $exist = self::deleteDraftCampaign($tmpSlug, $sellerId);
        return $exist ? $slug . '-' . ($maxSuffix + 1) : $tmpSlug;
    }

    /**
     * @param $slug
     * @param $sellerId
     * @return bool
     * @throws \Throwable
     */
    public static function deleteDraftCampaign($slug, $sellerId): bool
    {
        $slug = Slug::query()
            ->where('slug', $slug)
            ->first();
        if (!$slug) {
            return false;
        }
        $seller = User::query()->find($sellerId);
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->select('id')
            ->whereKey($slug->campaign_id)
            ->where('seller_id', $sellerId)
            ->where('status', ProductStatus::DRAFT)
            ->where('created_at', '>', now()->subMinutes(10))
            ->first();
        if ($campaign) {
            self::clearCampaignById($slug->campaign_id, $seller);
            return false;
        }
        return true;
    }

    private static function nameToSlug(string $name, string $prefix, string $suffix): string
    {
        $name = preg_replace('/[\x{0300}-\x{036f}]/u', "", Normalizer::normalize($name, Normalizer::NFD));
        $slug = preg_replace('/[^a-z0-9]+/i', ' ', $name);
        $slug = trim($slug);
        $slug = preg_replace('/\s+/', '-', $slug);
        $slug = strtolower($slug);
        if (!empty($prefix)) {
            $slug = $prefix . '-' . $slug;
        }
        if (!empty($suffix)) {
            $slug .= '-' . trim($suffix, '-');
        }
        return $slug;
    }

    /**
     * @param $fromCampId
     * @param $fromProdId
     * @param $toCampId
     * @param $toProdId
     * @param $seller
     * @return bool
     */
    public static function cloneMockups($fromCampId, $fromProdId, $toCampId, $toProdId, $seller): bool
    {
        $files = File::query()
            ->onSellerConnection($seller)
            ->where([
                'product_id' => $fromProdId,
                'campaign_id' => $fromCampId,
                'type' => FileTypeEnum::IMAGE,
                'type_detail' => FileRenderType::CUSTOM,
            ])
            ->get();

        if ($files->count() > 0) {
            foreach ($files as $file) {
                File::query()
                    ->onSellerConnection($seller)
                    ->create($file->replicate()->fill([
                        'product_id' => $toProdId,
                        'campaign_id' => $toCampId,
                    ])->toArray());
            }
        }
        return true;
    }


    /**
     * @param $mockup
     * @param $design
     * @param $colorName
     * @param string $renderMode
     * @return string
     */
    public static function getCdnMockupImage($mockup, $design, $colorName, string $renderMode = CampaignRenderModeEnum::NATURE)
    {
        try {
            $designJson = json_decode($mockup->design_json, true); // to array
            $layoutColor = $designJson['color'] ?? '';
            $layoutColor = str_replace('.png', '', $layoutColor);
            $layoutCrop = $designJson['crop'] ?? '';
            $layoutCrop = str_replace('.png', '', $layoutCrop);
            $layoutShadow = $designJson['shadow'] ?? '';
            $layoutShadow = str_replace('.png', '', $layoutShadow);
            $background = str_replace('.png', '', $mockup->file_url);
            $pathPrefix = ($design && is_null($design->file_url)) ? '/s3' : '/s4';
            $layoutDesign = !is_null($design) ? $design->file_url ?? $design->file_url_2 : '';
            $layoutDesign = str_replace('.png', '', $layoutDesign);
            $hexColor = !is_null($colorName) ? color2hex($colorName) : '';
            return self::getCdnMockupImageUrl($background, $layoutColor, $layoutCrop, $layoutShadow, $layoutDesign, $hexColor, $renderMode, $pathPrefix);
        } catch (\Exception $ex) {
            return '';
        }
    }

    /**
     * @param $background
     * @param $color
     * @param $crop
     * @param $shadow
     * @param $design
     * @param $hexColor
     * @param $renderMode
     * @param string $pathPrefix
     * @return string
     */
    public static function getCdnMockupImageUrl($background, $color, $crop, $shadow, $design, $hexColor, $renderMode, string $pathPrefix = '/s4'): string
    {
        $config = config('senprints.cloudinary_config');
        $cloudinary = new Cloudinary($config);

        if (!empty($design)) {
            $image = $cloudinary->image($design);
            if (!empty($crop)) {
                $image->reshape(Reshape::cutByImage(Source::image($crop)));
            }
        } else {
            $image = $cloudinary->image($background);
        }

        if (!empty($design) && !empty($shadow) && $renderMode === CampaignRenderModeEnum::DYNAMIC) {
            $image->underlay(Overlay::source(Source::image($shadow)));
        }

        if (!empty($color)) {
            $transformationColor = Source::image($color)
                ->transformation((new ImageTransformation())
                    ->effect(Effect::colorize()
                        ->level(100)
                        ->color(Color::rgb($hexColor))));
            if (!empty($design)) {
                $image->underlay(Overlay::source($transformationColor));
            } else {
                $image->overlay(Overlay::source($transformationColor));
            }
        }

        if (!empty($shadow) && $renderMode !== CampaignRenderModeEnum::DYNAMIC || empty($design)) {
            $image->overlay(Overlay::source(Source::image($shadow)));
        }

        if (!empty($background) && !empty($design)) {
            $image->underlay(Overlay::source(Source::image($background)));
        }

        $image->resize(Resize::thumbnail()->width(1280))->format(Format::jpg());

        $url = preg_replace('/https:\/\/res\.cloudinary\.com\/\w+\/image\/upload/', $pathPrefix, $image->toUrl());
        $hashName = substr(md5($url), 0, 16);
        // remove version param and add file name
        return preg_replace('/\?_a=.+$/', '/t/' . $hashName . '.jpg', $url);
    }

    public static function checkSynchronizingCampsEsAndSinglestore()
    {
        try {
            $filter['created_at_from_date_to_now'] = Carbon::now()->subDay();
            $totalEs = (new Elastic())->countCampaigns($filter);
            $totalSingleStore = IndexProduct::select('id')->where('created_at', '>=', $filter['created_at_from_date_to_now'])->where('sync_status', 1)->pluck('id')->count();
            $diffPercent = self::diffPercent($totalSingleStore, $totalEs);
            $response['diff_percent'] = $diffPercent . '%';
            $response['number_camps_singlestore'] = $totalSingleStore;
            $response['number_camps_es'] = $totalEs;
            if (isset($diffPercent) && $diffPercent > 10) {
                $response['sync_err'] = true;
                return $response;
            }
            $response['sync_err'] = false;
            return $response;
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function diffPercent($totalSingleStore, $totalEs)
    {
        if ($totalSingleStore == 0) {
            return null;
        }

        if ($totalEs <= $totalSingleStore) {
            return ceil((($totalSingleStore - $totalEs) / $totalSingleStore) * 100);
        }
        return null;
    }

    /**
     * @param $dmcaUrlInfo
     * @return array
     */
    public static function extractDmcaCampaignIds($dmcaUrlInfo): array
    {
        $response = [];
        try {
            $response = json_decode($dmcaUrlInfo)->campaignIds;
        } catch (\Exception $e) {
        }
        return $response;
    }

    private static function DMCAViolationBlockNotification($sellerInfo, $violationTime): bool
    {
        $mailConfig = [
            'to' => $sellerInfo->email,
            'template' => 'seller.seller-blocked-cause-trademark-violation',
            'data' => [
                'subject' => '[ACCOUNT BLOCKED] Your account has been blocked due to DMCA policy violations.',
                'sellerName' => $sellerInfo->name,
                'time' => $violationTime,
                'base_url' => config('senprints.base_url_seller'),

            ],
        ];
        return sendEmail($mailConfig);
    }

    private static function DMCAViolationTimeWarningHandler($sellerInfo, $violationTime): bool
    {
        $mailConfig = [
            'to' => $sellerInfo->email,
            'template' => 'seller.dmca-violation-time-warning',
            'data' => [
                'subject' => '[WARNING] You have violated DMCA policies.',
                'sellerName' => $sellerInfo->name,
                'time' => $violationTime,
                'base_url' => config('senprints.base_url_seller'),

            ],
        ];
        return sendEmail($mailConfig);
    }

    public static function takedownNotifiedTmCampaign(): void
    {
        try {
            $campaignNotifications = CampaignDMCANotification::query()->with(['user'])->where('created_at', '<', Carbon::now()->subHours(24))->where('dmca_execution_status', DmcaNotificationExecutionEnum::PENDING)->get();
            foreach ($campaignNotifications as $campaignNotification) {
                $campIds = self::extractDmcaCampaignIds($campaignNotification->dmca_url_info);
                if (empty($campIds)) {
                    continue;
                }
                $campsForBlock = Product::query()->select('id')->whereIn('id', $campIds)->where('tm_status', CampaignTrademarkStatusEnum::TM)->pluck('id')->toArray();
                Product::query()->whereIn('id', $campsForBlock)->orWhereIn('campaign_id', $campsForBlock)->update([
                    'status' => CampaignStatusEnum::BLOCKED,
                ]);
                $campaignNotification->update([
                    'dmca_execution_status' => DmcaNotificationExecutionEnum::DONE,
                ]);
                $userData = $campaignNotification->user;
                $userNumberViolated = $userData->dmca_violation_time;
                $userNumberViolatedIncrease = $userNumberViolated + 1;
                $userData->dmca_violation_time = $userNumberViolatedIncrease;
                if ($userNumberViolated >= 1) {
                    $userData->status = UserStatusEnum::SOFT_BLOCKED;
                    self::DMCAViolationBlockNotification($userData, $userNumberViolatedIncrease);
                } else {
                    $userData->status = UserStatusEnum::FLAGGED;
                    $userData->flag_log = 'Seller had violated DMCA policy : ' . $userNumberViolatedIncrease . ' times - campaigns were violated : ' . implode(',', $campsForBlock) . ' - time flagged : ' . Carbon::now();
                    self::DMCAViolationTimeWarningHandler($userData, $userNumberViolatedIncrease);
                }
                $userData->save();
            }
        } catch (\Exception $e) {
            logException($e);
        }
    }

    public static function checkPersonalizedProductsMatched($products, $campaignPersonalized = null): bool
    {
        $products = array_filter($products, function ($product) {
            return $product['full_printed'] !== ProductPrintType::HANDMADE;
        });

        try {
            if (empty($products) || !isset($products[0]['personalized'])) {
                return true;
            }

            $pivot = $products[0]['personalized'];
            foreach ($products as $product) {
                if ($pivot !== $product['personalized']) {
                    return false;
                }
            }

            if (isset($campaignPersonalized)) {
                if ($pivot !== $campaignPersonalized) {
                    return false;
                }
            }

            return true;
        } catch (Exception $e) {
            return true;
        }

    }

    public static function listUserLimitCreateCampaign(): array
    {
        return User::query()
            ->select('id')
            ->where('hold_launch_campaign_at', '>', now()->subHour())
            ->where('role', '<>', UserRoleEnum::CUSTOMER)
            ->pluck('id')
            ->toArray();
    }

    /**
     * @throws \Throwable
     */
    public static function clearCampaignById(int $id, User $seller, $sync = true): void
    {
        $query = Product::query()
            ->onSellerConnection($seller)
            ->where(function ($query) use ($id) {
                $query->where('id', $id)
                    ->orWhere('campaign_id', $id);
            })
            ->where([
                'seller_id' => $seller->id,
                'status' => ProductStatus::DRAFT
            ]);
        $data = ['slug' => null];
        if (!$sync) {
            $data['sync_status'] = 0;
        }
        $query->update($data);
        $query->delete();
        SyncSlugJob::dispatchSync(ids: [$id], seller: $seller, isUpsert: false);
        if ($sync) {
            $productModel = new Product();
            $productIds = $query->pluck('id')->toArray();
            $elasticSearchIndex = get_env('ELATICSEARCH_INDEX', 'products');
            if ($seller) {
                $elasticSearchIndex = $seller->getElasticSearchIndex();
            }
            $productModel->elasticDeleteProductsByProductIds($productIds, index: $elasticSearchIndex);
        }
    }

    /**
     * @param String $slug
     * @return string
     */
    public static function processBulkUploadSlug(string $slug): string
    {
        if (Slug::isValid($slug)) {
            return $slug;
        }

        $number = 1;
        $newSlug = $slug . '-' . $number;

        while (Slug::isInvalid($newSlug)) {
            $number++;
            $newSlug = $slug . '-' . $number;
        }

        return $newSlug;
    }

    /**
     * @param int $campaignId
     * @param string $artwork
     * @param string $name
     * @param string $slug
     * @param SenPrintsAuth $user
     * @return array|false
     * @throws \Throwable
     */
    public static function duplicateCampaignWithFile(int $campaignId, string $artwork, string $name, string $slug, SenPrintsAuth $user)
    {
        $userId = $user->getUserId();
        $userInfo = $user->getInfo();

        if (empty($userId) || $userInfo === null) {
            logToDiscord('Cannot get user info ' . $userId . 'info ' . $userInfo, 'bulk_campaign');
            return false;
        }

        $seller = User::query()->find($userId);
        // Get all data from campaign ID
        $query = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $campaignId,
                'seller_id' => $userId
            ]);
        $targetCampaign = $query->first();
        throw_if(!$targetCampaign, new Exception('Cannot find campaign with id: ' . $campaignId . ' and seller id: ' . $userId));

        $arrayScore = getScoreProductBySales();
        $newCampaign = Campaign::query()
            ->onSellerConnection($seller)
            ->create($targetCampaign->replicate([
                'slug',
                'default_product_id',
                'tm_status',
                'public_status',
                'sync_status',
                'elastic_document_id',
                'created_at',
                'updated_at',
                'sales_score',
                'score',
                'time_score',
                'template_campaign_id'
            ])->fill([
                'name' => $name,
                'slug' => $slug,
                'product_type' => ProductType::CAMPAIGN,
                'status' => ProductStatus::DRAFT,
                'score' => $arrayScore['score'],
                'time_score' => $arrayScore['timestamp'],
                'template_campaign_id' => $campaignId
            ])->toArray());

        $publicStatus = CampaignPublicStatusEnum::NO;

        if (in_array($targetCampaign->public_status, [
            CampaignPublicStatusEnum::YES,
            CampaignPublicStatusEnum::APPROVED
        ], true)) {
            $publicStatus = CampaignPublicStatusEnum::YES;
        }
        $newCampaign->public_status = $publicStatus;

        $defaultProductId = $targetCampaign->default_product_id;
        $defaultProduct = null;
        $newArtWorkPath = null;

        try {
            if (!$newCampaign->save()) {
                logToDiscord('Cannot save new campaign 1' . json_encode($newCampaign), 'bulk_campaign');
                return false;
            }
            (new SyncSlugJob(ids: [$newCampaign->id], seller: $seller))->handle();

            $newCampaignId = $newCampaign->id;

            // Clone collection
            self::cloneCollections($campaignId, $newCampaignId);
            // Clone store
            self::cloneStores($campaignId, $newCampaignId);
            // Clone upsell
            self::cloneUpsell($campaignId, $newCampaignId, $seller->id);

            // prepare variable for s3 file storage
            $fileName = pathinfo($artwork, PATHINFO_BASENAME);
            $newArtWorkPath = 'p/' . $newCampaignId . '/' . $fileName;

            // clone all products
            $products = Product::query()
                ->onSellerConnection($seller)
                ->where([
                    'product.seller_id' => $targetCampaign->seller_id,
                    'product.campaign_id' => $campaignId,
                    'product_type' => ProductType::PRODUCT
                ])
                ->get();

            $newProducts = [];
            if ($products->count() > 0) {
                if (!str_starts_with($artwork, 'p/')) {
                    // move file to right directory
                    if (!copy_file_on_storage($artwork, $newArtWorkPath)) {
                        logToDiscord('Cannot copy artwork from ' . $artwork . ' to ' . $newArtWorkPath, 'bulk_campaign');
                        return false;
                    }
                } else {
                    $newArtWorkPath = $artwork;
                }

                foreach ($products as $product) {
                    $currentProductId = $product->id;
                    // add new product and get new product id
                    $product->makeHiddenAll();
                    $newProduct = Product::query()
                    ->onSellerConnection($seller)
                    ->create($product->replicate([
                        'extra_print_cost',
                        'tm_status',
                        'public_status',
                        'sync_status',
                        'elastic_document_id',
                        'created_at',
                        'updated_at',
                        'sales_score',
                        'score',
                        'time_score',
                        'template_campaign_id'
                    ])->fill([
                        'campaign_id' => $newCampaignId,
                        'status' => ProductStatus::DRAFT,
                        'score' => ($currentProductId === $defaultProductId) ? $arrayScore['score'] + 1 : $arrayScore['score'],
                        'time_score' => $arrayScore['timestamp'],
                        'template_campaign_id' => $campaignId
                    ])->toArray());

                    $newProduct->public_status = $publicStatus;

                    if (!$newProduct->save()) {
                        self::removeProductByCampaignId($newCampaignId);
                        logToDiscord('Cannot save new product: ' . json_encode($newProduct), 'bulk_campaign');
                        return false;
                    }

                    $newProducts[] = $newProduct;
                    $newProductId = $newProduct->id;

                    if ($newProductId && $newProductId > 0) {
                        if ($newProduct->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                            self::cloneVariants($campaignId, $currentProductId, $newCampaignId, $newProductId);
                        }
                        if ($currentProductId === $defaultProductId) {
                            $defaultProduct = $newProduct;
                        }
                    }
                }
            }

            // default product is not null then update
            if (!empty($defaultProduct)) {
                $newCampaign->default_product_id = $defaultProduct->id;
                $newCampaign->thumb_url = $defaultProduct->thumb_url;
                $newCampaign->template_id = $defaultProduct->template_id;
            }

            if (!$newCampaign->save()) {
                self::removeFileByCampaignId($newCampaignId);
                self::removeUpsellByCampaignId($newCampaignId);
                self::removeProductByCampaignId($newCampaignId);
                logToDiscord('Cannot save new campaign' . json_encode($newCampaign), 'bulk_campaign');
                return false;
            }

            $newCampaign->new_products = $newProducts;
            $newCampaign->artwork_url = $newArtWorkPath;

            $columns = ['id', 'name', 'slug', 'thumb_url', 'artwork_url', 'new_products'];
            exportColumns($newCampaign, $columns);
            $arrayCampaign = $newCampaign->toArray();
            $newArrayCampaign = [];

            foreach ($columns as $column) {
                $newArrayCampaign[$column] = $arrayCampaign[$column];
            }

            return $newArrayCampaign;
        } catch (\Throwable $e) {
            logToDiscord('Exception:' . $e->getMessage(), 'bulk_campaign');
        }
        if ($newArtWorkPath) {
            delete_file_on_storage($newArtWorkPath);
        }
        return false;
    }

    /**
     * @param int $campaignId
     * @return bool
     */
    public static function removeFileByCampaignId(int $campaignId): bool
    {
        try {
            return File::query()
                ->where('campaign_id', $campaignId)
                ->delete();
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * @param int $campaignId
     * @return bool
     */
    public static function removeUpsellByCampaignId(int $campaignId, int $sellerId): bool
    {
        try {
            return Upsell::query()
                ->where('product_id', $campaignId)
                ->where('seller_id', $sellerId)
                ->delete();
        } catch (Exception $e) {
            return false;
        }
    }

    public static function cloneVariants($fromCampId, $fromProdId, $toCampId, $toProdId): bool
    {
        $variants = ProductVariant::query()
            ->where([
                'product_id' => $fromProdId,
                'campaign_id' => $fromCampId
            ])
            ->get();

        if ($variants->count() > 0) {
            $data = [];
            foreach ($variants as $variant) {
                $variantData = $variant->toArray();
                $variantData['product_id'] = $toProdId;
                $variantData['campaign_id'] = $toCampId;
                $data[] = $variantData;
            }
            ProductVariant::query()->insert($data);
        }
        return true;
    }

    /**
     * @param int $campaignId
     * @return bool
     */
    public static function removeProductByCampaignId(int $campaignId): bool
    {
        try {
            if (empty($campaignId)) {
                return false;
            }
            return Product::query()
                ->where('campaign_id', $campaignId)
                ->delete();
        } catch (Exception $e) {
            return false;
        }
    }

    private static function cloneRecords(string $modelClass, string $column, int $value, int $newValue, array $arrFilter = []): bool
    {
        /* @var Model $modelClass */
        $items = $modelClass::query()
            ->where($column, $value)
            ->when(count($arrFilter), function ($query) use ($arrFilter) {
                $query->where($arrFilter);
            })
            ->get();

        $totalItems = $items->count();
        $totalSuccess = 0;

        if ($items->isNotEmpty()) {
            foreach ($items as $item) {
                try {
                    $newItem = $item->replicate()->fill([
                        $column => $newValue
                    ]);

                    if (!$newItem->save()) {
                        return false;
                    }

                    $totalSuccess++;
                } catch (\Throwable $e) {
                    return false;
                }
            }

            return ($totalSuccess === $totalItems);
        }

        return true;
    }

    public static function cloneCollections(int $productId, int $newProductId): bool
    {
        return self::cloneRecords(ProductCollection::class, 'product_id', $productId, $newProductId);
    }

    public static function cloneUpsell(int $productId, int $newProductId, $sellerId): bool
    {
        Upsell::query()
            ->where('product_id', $newProductId)
            ->where('seller_id', $sellerId)
            ->delete();

        return self::cloneRecords(Upsell::class, 'product_id', $productId, $newProductId, ['seller_id' => $sellerId]);
    }

    public static function cloneStores(int $productId, int $newProductId): bool
    {
        return self::cloneRecords(StoreProduct::class, 'product_id', $productId, $newProductId);
    }

    public static function getLowestBaseCostVariant($selectedVariant, $location, $variants)
    {
        try {
            $selectedOptions = explode('-', $selectedVariant);
            $size = end($selectedOptions);
            $filteredVariants = array_filter($variants, function ($variant) use ($size, $location) {
                return str_contains($variant['variant_key'], $size) && $variant['location_code'] === $location && $variant['base_cost'] > 0;
            });

            if (empty($filteredVariants)) {
                return null;
            }
            $minBaseCost = min(array_column($filteredVariants, 'base_cost'));
            $minBaseCostVariants = array_filter($filteredVariants, function ($variant) use ($minBaseCost) {
                return $variant['base_cost'] === $minBaseCost;
            });
            if (count($minBaseCostVariants) === 0) {
                return null;
            }
            return reset($minBaseCostVariants);
        } catch (\Exception $e) {
            logException($e);
            return null;
        }
    }

    /**
     * @param $fileUrl
     * @param float $percentageThreshold
     * @return bool
     */
    public static function isDesignTransparent($fileUrl, float $percentageThreshold = 0.999): bool
    {
        try {
            $url = s3Url($fileUrl);
            $maxRetried = 1;
            while (true) {
                $ch = curl_init($url);
                curl_setopt_array($ch, [
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_TIMEOUT => 30,
                ]);
                $imageData = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                if (($imageData && $httpCode === 200) || $maxRetried > 3) {
                    break;
                }
                $maxRetried++;
            }
            if ($imageData === false) {
                return false;
            }

            $image = new Imagick();
            $image->readImageBlob($imageData);
            $width = $image->getImageWidth();
            $height = $image->getImageHeight();
            $totalPixels = $width * $height;
            $transparentPixels = 0;

            $iterator = $image->getPixelIterator();
            foreach ($iterator as $row) {
                foreach ($row as $pixel) {
                    $alpha = $pixel->getColorValue(Imagick::COLOR_ALPHA);
                    if ($alpha < 0.1) {
                        $transparentPixels++;
                    }
                }
                $iterator->syncIterator();
            }
            $image->clear();
            $transparentRatio = round($transparentPixels / $totalPixels, 3);
            return $transparentRatio >= $percentageThreshold;
        } catch (\Exception $e) {
            logException($e);
            return false;
        }
    }

    /**
     * @param $product
     * @return void
     * @throws \Throwable
     */
    public static function refactorProductAdditionalPrintSpaces(&$product): void
    {
        if (!isset($product->print_spaces)) {
            return;
        }

        $printSpaces = $product->print_spaces;
        if (Str::isJson($printSpaces)) {
            $printSpaces = json_decode($product->print_spaces, true, 512, JSON_THROW_ON_ERROR);
        }
        if (!empty($printSpaces)) {
            $printSpaces = collect($printSpaces)->filter(function ($printSpace) {
                return $printSpace && !in_array(data_get($printSpace, 'name'), PrintSpaceEnum::additionalPrintSpaces(), true);
            })->all();
            unset($product->print_spaces);

            $printSpaces = array_values($printSpaces);
            $product->print_spaces = json_encode($printSpaces, JSON_THROW_ON_ERROR);
        }

    }

    /**
     * @param $products
     * @param $applyTestPrice
     * @param $sellerId
     * @return void
     */
    public static function mappingCorrectPricing(&$products, $applyTestPrice, $sellerId): void
    {
        // Filter out products belonging to combo campaigns
        $products = $products->reject(function ($product) {
            return $product->campaign && $product->campaign->system_type === ProductSystemTypeEnum::COMBO;
        });

        if ($products->isEmpty()) {
            return;
        }

        $sellerIds = $products->pluck('seller_id')->unique()->toArray();
        $pricing = UserService::getPricingListBySellerIds($sellerIds);
        if ($pricing->isEmpty()) {
            return;
        }
        $products->map(function ($product) use ($pricing, $applyTestPrice, $sellerId) {
            $productPricing = $pricing->filter(function ($savedPrice) use ($product) {
                $generalConditions = $savedPrice->seller_id == $product->seller_id && $savedPrice->product_id == $product->template_id;
                if (!$generalConditions) {
                    return false;
                }

                if ($product->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                    $option = json_decode($product->options, true) ?? [];
                    $colorPosition = getPositionOfKeyInOptions('color', $option);
                    $variantKey = $product->variant_key;
                    $explode = explode('-', $variantKey);

                    if ($explode[$colorPosition] && $explode[$colorPosition] !== 'white') {
                        $explode[$colorPosition] = 'white';
                    }

                    return $savedPrice->variant_key === implode('-', $explode);
                }

                return true;
            })->sortBy('price')->first();
            if (!empty($productPricing)) {
                $productPricing = $productPricing->toArray();
                $rate = SystemConfigController::findOrDefaultCurrency($productPricing['currency_code'])->rate;

                if ($product->pricing_mode === PricingModeEnum::ADJUST_PRICE) {
                    $productPricing['price'] += ceil($product->adjust_price);
                }

                if ($productPricing['currency_code'] !== CurrencyEnum::USD) {
                    $productPricing['price'] /= $rate;
                    $productPricing['test_price'] /= $rate;
                }

                $product->price = $productPricing['price'] + $product->extra_custom_fee + data_get($product, 'extra_print_cost', 0);
                if ($sellerId == $productPricing['seller_id'] && $productPricing['test_price'] != 0) {
                    $product->adjust_test_price = 0;
                    if ($applyTestPrice == 'true') {
                        $product->price += $productPricing['test_price'];
                        $product->adjust_test_price = $productPricing['test_price'];
                    }
                }
            }
            return $product;
        });
    }

    /**
     * @param $campaign
     * @param $store
     * @return void
     */
    public static function overrideCountdownStatus(&$campaign, $store): void
    {
        $storeCountdownStatus = $store->show_countdown;
        if (isset($storeCountdownStatus) && $storeCountdownStatus > 0) {
            $campaign->show_countdown = $storeCountdownStatus;
            $campaign->end_time = $store->countdown_end_time;
        }
    }

    public function getListImportCampaign(array $data)
    {
        $user = currentUser();
        $status = data_get($data, 'status');
        $type = data_get($data, 'type');
        $dateRange = data_get($data, 'date_range_type');
        $startDate = data_get($data, 'start_date');
        $endDate = data_get($data, 'end_date');
        $storeIds = data_get($data, 'store_ids', '');
        $selected = data_get($data, 'selected', []);
        if (!empty($selected)) {
            $selected = explode(',', $selected);
            $selected = array_filter($selected);
        }
        $collection = data_get($data, 'collections', []);
        if (!empty($collection)) {
            $collection = explode(',', $collection);
            $collection = array_filter($collection);
        }
        $q = data_get($data, 'q');
        $sortDirection = data_get($data, 'direction', 'desc');
        $sortBy = data_get($data, 'sort_by', 'updated_at');
        if (currentUser()->isAdmin()) {
            $sellerId = data_get($data, 'seller_id');
        } else {
            $sellerId = $user->getUserId();
        }
        $storeIds = explode(',', $storeIds);
        $storeIds = array_filter($storeIds);
        $actualStoreIds = $storeIds;
        if ($user->isSeller()) {
            $store_ids = get_team_seller_stores($user->getUserId(), $user->getAuthorizedAccountId());
            if (!empty($store_ids)) {
                $actualStoreIds = $store_ids;
                if (!empty($storeIds)) {
                    $actualStoreIds = array_intersect($storeIds, $store_ids);
                }
            }
        }
        $actualStoreIds = array_filter($actualStoreIds);
        $query = ImportCampaignsData::query()
            ->select([
                'id',
                'campaign_id',
                'campaign_name',
                'template_campaign_id',
                'campaign_slug',
                'seller_id',
                'status',
                'type',
                'ip_address',
                'device_id',
                'session_id',
                'user_agent',
                'collection',
                'created_at',
                'updated_at',
                'logs',
                'store_ids',
            ])
            ->when(!empty($selected), function ($q) use ($selected) {
                $q->whereIn('id', $selected);
            })
            ->when(!empty($collection), function ($q) use ($collection) {
                $q->whereIn('collection', $collection);
            })
            ->when(!empty($sellerId), function ($q) use ($sellerId) {
                $q->where('seller_id', $sellerId);
            })
            ->when($user->isAdmin(), function ($q) {
                $q->with('seller');
            })
            ->when(!empty($actualStoreIds), function ($q) use ($actualStoreIds) {
                $q->where(function ($q) use ($actualStoreIds) {
                    $q->whereJsonContains('store_ids', DB::raw($actualStoreIds[0]));
                    unset($actualStoreIds[0]);
                    if (!empty($actualStoreIds)) {
                        foreach ($actualStoreIds as $actualStoreId) {
                            $q->orWhereJsonContains('store_ids', DB::raw($actualStoreId));
                        }
                    }
                });
            });
        if (!empty($q)) {
            $q = trim($q);
            if (filter_var($q, FILTER_VALIDATE_INT)) {
                $query->whereKey($q);
            } else if (preg_match('/^[a-z0-9]+(-[a-z0-9]+)+$/', $q) === 1) {
                $query->where('campaign_slug', $q);
            } else {
                $query->where('campaign_name', 'like', "%$q%");
            }
        } else {
            if (!empty($dateRange)) {
                $query->filterDateRange($dateRange, $startDate, $endDate);
            }
            if (!empty($status)) {
                $query->where('status', $status);
            }
            if (!empty($type)) {
                $query->where('type', $type);
            }
        }
        return $query->orderBy($sortBy, $sortDirection);
    }

    public function mapCampaignAndTemplateForImportCampaign($seller, $importCampaign, &$campaigns, &$campaignTemplates): mixed
    {
        if (isset($campaigns[$importCampaign->campaign_id])) {
            $importCampaign->campaign = $campaigns[$importCampaign->campaign_id];
        } else {
            $importCampaign->campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->select('id', 'status', 'slug', 'thumb_url')
                ->find($importCampaign->campaign_id);
            $campaigns[$importCampaign->campaign_id] = $importCampaign->campaign;
        }
        if (isset($campaignTemplates[$importCampaign->template_campaign_id])) {
            $importCampaign->template = $campaignTemplates[$importCampaign->template_campaign_id];
        } else {
            $importCampaign->template = Campaign::query()
                ->onSellerConnection($seller)
                ->select('id', 'name')
                ->find($importCampaign->template_campaign_id);
            $campaignTemplates[$importCampaign->template_campaign_id] = $importCampaign->template;
        }
        return $importCampaign;
    }

    public static function isCampaignBelongSeller($campaignId, User $seller): bool
    {
        return Campaign::query()
            ->onSellerConnection($seller)
            ->where('id', $campaignId)
            ->where('seller_id', $seller->id)
            ->exists();
    }

    /**
     * This function check if the design is full print or print on a specific area
     *
     * @param $fileUrl
     * @param float $threshold
     * @return bool
     * @throws \Throwable
     */
    public static function isDesignPrintFull($fileUrl, float $threshold = 0.999): bool
    {
        try {
            if (empty($fileUrl)) {
                throw new \RuntimeException('File URL is empty');
            }
            $url = s3Url($fileUrl);
            $maxRetried = 1;
            while (true) {
                $ch = curl_init($url);
                curl_setopt_array($ch, [
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_TIMEOUT => 30,
                ]);
                $imageData = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                if (($imageData && $httpCode === 200) || $maxRetried > 3) {
                    break;
                }
                $maxRetried++;
            }
            if ($imageData === false) {
                throw new \RuntimeException('Image data is empty');
            }
            $image = new Imagick();
            $image->readImageBlob($imageData);
            $width = $image->getImageWidth();
            $height = $image->getImageHeight();
            $halfWidth = (int)($width / 2);
            $halfHeight = (int)($height / 2);
            $corners = [
                [0, 0, $halfWidth, $halfHeight], // topLeft
                [$halfWidth, 0, $halfWidth, $halfHeight], // topRight
            ];
            $result = [];
            foreach ($corners as [$x, $y, $w, $h]) {
                $region = clone $image;
                $region->cropImage($w, $h, $x, $y);
                $transparentPixels = 0;
                $iterator = $region->getPixelIterator();
                foreach ($iterator as $row) {
                    foreach ($row as $pixel) {
                        $alpha = $pixel->getColorValue(Imagick::COLOR_ALPHA);
                        if ($alpha < 0.1) {
                            $transparentPixels++;
                        }
                    }
                    $iterator->syncIterator();
                }
                $transparentRatio = round($transparentPixels / ($w * $h), 3);
                $result[] = $transparentRatio < $threshold;
                $region->clear();
            }
            $image->clear();
            return array_reduce($result, fn($carry, $value) => $carry && $value, true);
        } catch (\Throwable $e) {
            logException($e);
            throw $e;
        }
    }

    /**
     * @param array $data
     * @param Campaign $campaign
     * @param string $slug
     * @return void
     */
    public static function storeReportedCampaign(array $data, Campaign $campaign, string $slug = '') {
        DB::beginTransaction();
        $result = ReportedCampaign::query()->create($data);

        if (!$result->wasRecentlyCreated) {
            DB::rollBack();
            return;
        }

        // change campaign tm_status
        $campaign->tm_status = CampaignTrademarkStatusEnum::FLAGGED;
        $campaign->sync_status = 0;
        $updated = $campaign->save();
        DB::commit();

        if (!$updated) {
            return;
        }
        $seller = User::query()->find($campaign->seller_id);
        $previewDomain = SystemConfig::getConfig('preview_domain', 'senstores.com');
        $message = 'Seller ID: ' . $campaign->seller_id . "\n";
        $message .= 'Store Type: ' . ($seller && $seller->custom_payment ? 'Custom store' : 'SenPrints store') . "\n";
        $message .= 'Campaign ID: ' . $campaign->id . "\n";
        $message .= 'URL: https://'. $previewDomain .'/' . $slug . "\n";
        $message .= 'Reason: ' . $data['reason'];

        logToDiscord($message, 'reported');
    }

    /**
     * @param $products
     * @return void
     */
    public static function getProductsCampaignExtraPrintCost(&$products) {
        if (empty($products)) {
            return;
        }
        $templateIds = array_column(is_array($products) ? $products : $products->toArray(), 'template_id');
        $templates = Template::query()->whereIn('id', $templateIds)->get();
        $sellerProductsDesign = collect();
        $collectionProducts = is_array($products) ? collect($products) : $products;
        $collectionProducts->groupBy('seller_id')->each(function (\Illuminate\Support\Collection $collectProducts, $sellerId) use (&$sellerProductsDesign) {
            $seller = User::query()->selectRaw('id,status,custom_payment,tags,sharding_status,db_connection')->find($sellerId);
            $productIds = $collectProducts->pluck('id')->unique()->toArray();
            $sellerProductsDesign->put($sellerId, File::query()
                ->onSellerConnection($seller)
                ->select(['id', 'type', 'file_url', 'file_url_2', 'type_detail', 'option', 'print_space', 'seller_id', 'store_id', 'campaign_id', 'order_id', 'order_product_id', 'product_id', 'mockup_id', 'design_id'])
                ->where('type', FileTypeEnum::DESIGN)
                ->whereIn('option', [
                    FileRenderType::CUSTOM,
                    FileRenderType::PRINT,
                    FileRenderType::PB
                ])
                ->whereIn('product_id', $productIds)
                ->get());
        });
        foreach ($products as $key => &$product) {
            $sellerId = data_get($product, 'seller_id');
            $designs = [];
            if ($sellerProductsDesign->has($sellerId)) {
                $designs = $sellerProductsDesign->get($sellerId, collect())->filter(fn($item) => (int)$item->product_id === (int)data_get($product, 'id'))->values();
            }
            $designs = Product::correctDesigns($designs);
            $templateProduct = $templates->where('id', data_get($product, 'template_id'))->first();
            if (empty($templateProduct)) {
                return;
            }
            /** @var Template $templateProduct */
            if (is_array($product)) {
                $productKey = $products[$key];
                $productKey['extra_print_cost'] = $templateProduct->calcExtraPrintCost($designs);
                $products[$key] = $productKey;
            } else {
                $product->extra_print_cost = $templateProduct->calcExtraPrintCost($designs);
            }
        }
    }
}

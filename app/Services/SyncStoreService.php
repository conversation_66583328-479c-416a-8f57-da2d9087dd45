<?php

namespace App\Services;

use App\Data\Customer\CustomerThirdPartyData;
use App\Data\Order\OrderThirdPartyData;
use App\Data\OrderItem\OrderItemThirdPartyData;
use App\Enums\CurrencyEnum;
use App\Enums\DiscordChannel;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\ShippingMethodEnum;
use App\Enums\StorageDisksEnum;
use App\Jobs\ValidateSellerFulfillOrder;
use App\Models\File;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\TiktokShop\Services\TiktokShopService;

class SyncStoreService
{
    public static function syncOrder(OrderThirdPartyData $orderData, CustomerThirdPartyData $customerData, $prefix): ?Order
    {
        try {
            DB::beginTransaction();
            $order = self::createOrder($orderData, $customerData, $prefix);
            $externalIds = [];
            $logs = [];
            foreach ($orderData->items as $item) {
                logToDiscord(json_encode($item->toArray()), DiscordChannel::THIENNT_LOG);
                $orderProduct = self::createOrderProduct($order, $item, $logs);
                logToDiscord(json_encode($orderProduct->toArray()), DiscordChannel::THIENNT_LOG);
                $lastMapped = self::getLastMappedOrderProduct($item->external_product_id, $order->seller_id);
                if ($lastMapped) {
                    $template = Product::query()->find($lastMapped->template_id);
                    if ($template) {
                        $hasDesign = self::copyDesigns($lastMapped->id, $orderProduct);
                        if (!$hasDesign) {
                            $logs['Product ' . $template->name . ' has no design'] = 'Product ' . $template->name . ' has no design';
                        }
                    }
                }
                $externalIds[] = $item->external_id;
            }

            if (!empty($externalIds)) {
                [$addressVerified, $addressVerifyLog] = self::verifyAddress($order);
                if ($addressVerified !== OrderAddressVerifiedEnum::VERIFIED) {
                    $logs['Address'] = empty($addressVerifyLog) ? 'Address invalid' : $addressVerifyLog;
                }
                Order::query()->whereKey($order->id)->update([
                    'order_number' => $prefix . '-' . $order->id,
                    'status' => empty($logs) ? OrderStatus::PENDING : OrderStatus::DRAFT,
                    'fulfill_status' => $addressVerified === OrderAddressVerifiedEnum::VERIFIED && empty($logs) ? OrderFulfillStatus::UNFULFILLED : OrderFulfillStatus::INVALID,
                    'currency_code' => CurrencyEnum::USD,
                    'currency_rate' => 1,
                    'fulfill_log' => implode(';', array_values($logs)),
                ]);
                if (empty($logs)) {
                    ValidateSellerFulfillOrder::dispatch($order);
                }
            } else {
                $order->delete();
            }
            DB::commit();
            return Order::query()->find($order->id);
        } catch (\Throwable $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private static function verifyAddress(Order $order): array
    {
        $addressVerifyLog = '';
        $addressVerified = OrderAddressVerifiedEnum::INVALID;
        if (AddressService::verify($order, $addressVerifyLog)) {
            $addressVerified = OrderAddressVerifiedEnum::VERIFIED;
        }

        return [$addressVerified,$addressVerifyLog];
    }

    public static function copyDesigns(int $lastOrderProductId, OrderProduct $newOrderProduct): bool
    {
        $files = File::query()->where([
            'order_product_id' => $lastOrderProductId,
            'status' => FileStatusEnum::ACTIVE,
        ])->get();

        $file_ids = $files?->pluck('id')->toArray();

        if(!empty($file_ids)) {
            TiktokShopService::logToDiscord('Copy design from order ' . $lastOrderProductId . ' to order ' . $newOrderProduct->order_id);
            return self::applyDesigns($newOrderProduct, $file_ids, $lastOrderProductId === $newOrderProduct->id);
        }

        return false;
    }

    private static function applyDesigns(OrderProduct $orderProduct, $file_ids, $isCopyMockup): bool
    {
        $order = $orderProduct->order;
        $order_id = $orderProduct->order_id;
        $order_product_id = $orderProduct->id;
        $seller = User::query()->find($orderProduct->seller_id);
        $files = File::query()
            ->onSellerConnection($seller)
            ->whereIn('id', $file_ids)->get();
        try {
            File::query()
                ->onSellerConnection($seller)
                ->where('order_id', $order_id)->where('order_product_id', $order_product_id)->delete();
            $movedFiles = collect([]);
            $s3Storage = Storage::disk(StorageDisksEnum::DEFAULT);
            $update_product_thumb = false;
            foreach ($files as $file) {
                if (!$isCopyMockup && $file->type === FileTypeEnum::IMAGE) { // Khong copy mockup
                    continue;
                }
                $newFile = File::query()->create($file->replicate()->fill([
                    'order_id' => $order_id,
                    'order_product_id' => $order_product_id,
                ])->toArray());
                $file_url = $newFile->file_url_2 ?? $newFile->file_url;
                if(!Str::startsWith($file_url, 'http')) {
                    $oldFilePath = $newFile->file_url_2;
                    $newFilePath = preg_replace(
                        '#o/(\d+)/(\d+)/#',
                        'o/'.$order_id.'/'.$order_product_id.'/',
                        $oldFilePath
                    );
                    if ($newFilePath && $movedFiles->contains($newFilePath) === false) {
                        if(!$s3Storage->exists($newFilePath)) {
                            $isSuccess = $s3Storage->copy($oldFilePath, $newFilePath);
                            if (!$isSuccess) {
                                TiktokShopService::logToDiscord("[Clone Product Files] Copy from {$oldFilePath} to {$newFilePath} failed.");
                                continue;
                            }
                        }
                        if(empty($newFile->file_url_2) || $newFile->file_url_2 === $newFile->file_url) {
                            $newFile->file_url = $newFilePath;
                        }
                        $newFile->file_url_2 = $newFilePath;
                        $movedFiles->push($newFilePath);
                        if (!$update_product_thumb && !empty($newFilePath)) {
                            $orderProduct->update(['thumb_url' => $newFilePath]);
                            $update_product_thumb = true;
                        }
                    }
                }
                $success = $newFile->save();
                TiktokShopService::logToDiscord("Clone success - file_id - file_url : {$success} - {$newFile->id} - {$newFile->file_url_2}");
            }
            if ($order && $order->status === OrderStatus::PENDING) {
                $order->status = OrderStatus::DRAFT;
                $order->save();
            }
            ValidateSellerFulfillOrder::dispatch($order);
            return true;
        } catch (\Throwable $e) {
            TiktokShopService::logToDiscord('CopyProductFilesEvent: ' . $e->getMessage());
            return false;
        }
    }

    public static function createOrder(OrderThirdPartyData $orderData, CustomerThirdPartyData $customerData, $prefix): Order
    {
        $sellerId = $orderData->seller_id;
        $externalOrderId = $orderData->external_order_id;
        $address = $customerData->toArray();
        $order = $orderData->toArray();
        $fixed = [
            'shipping_method' => ShippingMethodEnum::STANDARD,
            'fulfill_status' => OrderFulfillStatus::UNFULFILLED,
            'order_number_2' => $prefix . '-' . $order['external_order_id'],
            'shard_id' => config('senprints.shard_id'),
            'type' => OrderTypeEnum::FULFILLMENT,
            'status' => OrderStatus::DRAFT,
        ];
        $stored = array_merge($order, $address, $fixed);

        return self::findOrderByExternalId($externalOrderId, $sellerId) ?? Order::query()->create($stored);
    }

    public static function createOrderProduct(Order $order, OrderItemThirdPartyData $item, &$logs): OrderProduct
    {
        $lastMapped = self::getLastMappedOrderProduct($item->external_product_id, $order->seller_id);
        $external_product_id = $item->external_product_id;
        if (!$lastMapped) {
            $logs['Product' . $external_product_id] = 'Product ' . $external_product_id . ' not valid';
            $logs['Options' . $external_product_id] = 'Product ' . $external_product_id . ' options invalid';
        }
        $stored = array_merge($item->toArray(), [
            'campaign_title' => $item->title,
            'product_name' => $item->title,
            'template_id' => $lastMapped?->template_id,
            'order_id' => $order->id,
            'seller_id' => $order->seller_id,
            'external_order_id' => $order->external_order_id,
            'shard_id' => config('senprints.shard_id'),
            'sku' => $lastMapped?->sku ?? $item->sku,
        ]);

        $old = OrderProduct::query()->where([
            'external_id' => $item->external_id,
            'seller_id' => $order->seller_id,
            'external_product_id' => $item->external_product_id,
            'order_id' => $order->id,
        ])->first();

        if ($old) {
            $old->update($stored);
            $old->refresh();
            return $old;
        }

        return OrderProduct::query()->create($stored);
    }

    public static function findOrderByExternalId($externalOrderId, $sellerId): ?Order
    {
        return Order::query()->where('external_order_id', $externalOrderId)
            ->where('seller_id', $sellerId)
            ->first();
    }

    public static function getLastMappedOrderProduct($externalProductId, $sellerId): ?OrderProduct
    {
        return OrderProduct::query()
            ->select([
                'id',
                'template_id',
                'sku',
            ])
            ->whereHas('designs')
            ->where('seller_id', $sellerId)
            ->whereNotNull('template_id')
            ->where(function (Builder $query) {
                $query->WhereNotNull('color')
                    ->orWhereNotNull('size');
            })
            ->where('external_product_id', $externalProductId)
            ->orderByDesc('updated_at')
            ->first();
    }
}

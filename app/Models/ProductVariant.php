<?php

namespace App\Models;

use App\Enums\CacheKeys;
use App\Traits\HasCompositePrimaryKey;
use App\Traits\HasRelationShipsCustom;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Collection as CollectionAlias;

/**
 * App\Models\ProductVariant
 *
 * @property int $product_id
 * @property int|null $campaign_id
 * @property string $variant_key
 * @property float $base_cost
 * @property string $location_code
 * @property int|null $out_of_stock
 * @property float|null $adjust_price
 * @property float|null $price
 * @property float $old_price
 * @property float $weight pound:lbs
 * @property int|null $quantity
 * @property int|null $check_quantity
 * @property string|null $sku
 * @property-read \App\Models\Product $product
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant selectForListing()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereAdjustPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereBaseCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereCheckQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereLocationCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereOldPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereOutOfStock($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereVariantKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductVariant whereWeight($value)
 * @mixin \Illuminate\Database\Eloquent\Builder
 * @mixin \Illuminate\Database\Query\Builder
 */
class ProductVariant extends Model
{
    use HasFactory;
    use HasCompositePrimaryKey;
    use HasRelationShipsCustom;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'product_variant';

    /**
     * @var string[]
     */
    protected $fillable = [
        'product_id',
        'campaign_id',
        'variant_key',
        'out_of_stock',
        'adjust_price',
        'price',
        'base_cost',
        'location_code',
        'quantity',
        'check_quantity',
        'sku',
    ];
    protected $primaryKey = [
        'product_id',
        'variant_key',
        'location_code',
    ];
    public const ARR_LISTING = [
        'product_id',
        'campaign_id',
        'variant_key',
        'out_of_stock',
        'price',
        'location_code',
        'quantity',
        'check_quantity',
        'sku',
    ];
    protected $keyType = 'array';

    /**
     * @var bool
     */
    public $timestamps = false;

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function scopeSelectForListing($query){
        return $query->select(self::ARR_LISTING);
    }

    static private ?array $cache = null;
    public static function findAndCacheByTemplate($templateId): CollectionAlias
    {
        if (isset(self::$cache[$templateId])) {
            return self::$cache[$templateId];
        }
        $tag1 = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;
        $tag2 = CacheKeys::LIST_VARIANT_BY_TEMPLATE;
        $data = cacheGet(
            CacheKeys::getVariantsByTemplate($templateId),
            CacheKeys::CACHE_24H,
            static function () use ($templateId) {
                return self::query()
                    ->where('product_id', $templateId)
                    ->get()
                    ->toJson();
            },[$tag1, $tag2], CacheKeys::CACHE_TYPE_ALTERNATIVE
        );

        if (is_string($data)) {
            $arr = json_decode($data, true);
            $data = collect($arr)->map(function ($each){
                return self::make($each);
            });
        }
        self::$cache[$templateId] = $data;

        return $data;
    }

    public static function findAndCacheByProductSharding($productId, $seller = null): CollectionAlias
    {
        if (isset(self::$cache[$productId])) {
            return self::$cache[$productId];
        }
        $tag1 = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;
        $tag2 = CacheKeys::LIST_VARIANT_BY_TEMPLATE;
        $data = cacheGet(
            CacheKeys::getVariantsByProductCustomPrice($productId),
            CacheKeys::CACHE_24H,
            static function () use ($productId, $seller) {
                $productVariants = self::query()
                    ->when(!empty($seller), function ($q) use ($seller) {
                        $q->onSellerConnection($seller);
                    })
                    ->where('product_id', $productId)
                    ->get();
                $productVariants = self::addGlobalLocationVariants($productVariants);
                return $productVariants->toJson();
            },[$tag1, $tag2], CacheKeys::CACHE_TYPE_ALTERNATIVE
        );
        if (is_string($data)) {
            $arr = json_decode($data, true);
            $data = collect($arr)->map(function ($each){
                return self::make($each);
            });
        }
        self::$cache[$productId] = $data;

        return $data;
    }

    /**
     * Kiểm tra xem variant có trong kho hay không
     *
     * @param                                    $templateId
     * @param     string                         $variantKey
     * @param     \App\Models\SystemLocation     $location
     *
     * @return bool
     */
    public static function isTemplateVariantInStock($templateId, string $variantKey, SystemLocation $location): bool
    {
        $variants = self::findAndCacheByTemplate($templateId)
            ->filter(static fn ($e) => $e->variant_key === $variantKey)
            ->keyBy('location_code');

        // Region code được sắp xếp lại theo thứ tự ưu tiên từ
        // ww -> region -> immediate region -> sub region -> country
        // khi đó ta chỉ cần kiểm tra xem region code đầu tiên có trong variants và có out_of_stock != 0 thì
        // sẽ trả về false (hết hàng)
        foreach (array_reverse($location->getRegionCodes()) as $regionCode) {
            if ($variants->has($regionCode) && $variants->get($regionCode)->out_of_stock) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param     int     $id
     *
     * @return bool|mixed|null
     */
    public static function deleteByCampaignId(int $id): mixed
    {
        return self::query()
            ->where('campaign_id', $id)
            ->delete();
    }

    /**
     *
     * @param \Illuminate\Database\Eloquent\Collection $variants
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function addGlobalLocationVariants($variants)
    {
        try {
            $globalVariantKeys = $variants->where('location_code', '*')
                ->pluck('variant_key')
                ->toArray();
            $newVariants = collect();
            foreach ($variants as $variant) {
                if ($variant->location_code !== '*' && !in_array($variant->variant_key, $globalVariantKeys)) {
                    $newVariant = clone $variant;
                    $newVariant->location_code = '*';
                    $newVariants->push($newVariant);
                    $globalVariantKeys[] = $variant->variant_key;
                }
            }
            return $variants->concat($newVariants);
        } catch (\Exception $e) {
            logToDiscord('Error in ProductVariant::addGlobalLocationVariants : ' . $e->getMessage());
            return $variants;
        }
    }
}

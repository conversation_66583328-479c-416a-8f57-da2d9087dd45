<?php

namespace App\Models;

use App\Enums\AbandonedLogStatusEnum;
use App\Enums\AdsLogsActionEnum;
use App\Enums\CacheTime;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\CurrencyEnum;
use App\Enums\DateRangeEnum;
use App\Enums\FirstOrderTypeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerHistoryActionEnum;
use App\Enums\SystemRole;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Traits\ScopeFilterDateRangeTrait;
use Carbon\Carbon;
use Hashids\Hashids;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Modules\Campaign\Models\ImportCampaignsData;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;
use Modules\SellerAccount\Models\UserBalance;
use Modules\SellerTier\Models\SellerTier;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;

/**
 * App\Models\User
 *
 * @property int $id
 * @property int|null $tier_id
 * @property int|null $company_id
 * @property string|null $name
 * @property string|null $nickname
 * @property string|null $username
 * @property string $email
 * @property string|null $email_verified_at
 * @property string $password
 * @property string|null $avatar User profile picture
 * @property string|null $crawled_email_avatar_at
 * @property string|null $address
 * @property string|null $city
 * @property string|null $state
 * @property string|null $postcode
 * @property string|null $country The user current country
 * @property string|null $register_country The user register country
 * @property string|null $birthday
 * @property string|null $phone
 * @property string|null $facebook
 * @property float|null $balance The user current balance
 * @property float|null $hold_amount
 * @property \Illuminate\Support\Carbon|null $hold_to When the hold will expire
 * @property int $campaign_limit Campaign can create per day
 * @property int $total_orders
 * @property float $total_purchases
 * @property string|null $timezone The user timezone
 * @property float|null $utc_offset The user timezone utc offset
 * @property string|null $language The user current language
 * @property string $currency The user current currency
 * @property string|null $market_location
 * @property string|null $stripe_id
 * @property int $sofort_payment
 * @property string $theme light/dark
 * @property string $pricing_mode
 * @property int $is_ref
 * @property int|null $ref_id
 * @property int|null $support_staff_id
 * @property int|null $sale_staff_id
 * @property int $is_marketplace
 * @property int $shard_id
 * @property int $bonus_times
 * @property string $role
 * @property string|null $first_order_type
 * @property string $status This is status of user
 * @property string|null $note
 * @property float|null $sen_points The SEN Point of user
 * @property float $sms_credit
 * @property string|null $sale_volume
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon $deleted_at
 * @property \Illuminate\Support\Carbon $hold_launch_campaign_at
 * @property \Illuminate\Support\Carbon|null $first_order_at
 * @property \Illuminate\Support\Carbon|null $last_order_at
 * @property int $advanced 0: not advanced user, 1: advanced user
 * @property string|null $tags
 * @property int $custom_payment
 * @property int $email_subscribed
 * @property int $sms_subscribed
 * @property string|null $flag_log
 * @property int $auto_fulfill
 * @property int $smart_remarketing
 * @property int $confirm_join_contest
 * @property int $is_deleted
 * @property \Illuminate\Support\Carbon|null $sale_expired_at
 * @property UserShardingStatusEnum $sharding_status
 * @property string $db_connection
 * @property-read EloquentCollection $import_campaigns_data
 * @property-read AdsCampaignLog|null $register_ads
 * @property-read EloquentCollection|AdsCampaignLog[] $ads_logs
 * @property-read EloquentCollection|SocialFeedImages[] $social_feed_images
 * @property-read EloquentCollection|CustomerAddress[] $addresses
 * @property-read int|null $addresses_count
 * @property-read EloquentCollection|SellerBilling[] $billings
 * @property-read int|null $billings_count
 * @property EloquentCollection|Campaign[] $campaigns
 * @property-read int|null $campaigns_count
 * @property-read EloquentCollection|Collection[] $collections
 * @property-read int|null $collections_count
 * @property-read EloquentCollection|Order[] $customer_orders
 * @property-read int|null $customer_orders_count
 * @property-read int $number_campaign_can_create
 * @property-read int $pending_sms_jobs
 * @property-read EloquentCollection|Permission[] $permissions
 * @property-read int|null $permissions_count
 * @property-read EloquentCollection|ProductReview[] $productReviews
 * @property-read int|null $product_reviews_count
 * @property-read EloquentCollection|Product[] $products
 * @property-read int|null $products_count
 * @property-read EloquentCollection|PromotionRule[] $promotion_rules
 * @property-read int|null $promotion_rules_count
 * @property-read Store|null $recent_store
 * @property-read EloquentCollection|Role[] $roles
 * @property-read int|null $roles_count
 * @property-read Staff|null $sale_staff
 * @property-read EloquentCollection|OrderProduct[] $seller_order_products
 * @property-read int|null $seller_order_products_count
 * @property-read EloquentCollection|Order[] $seller_orders
 * @property-read int|null $seller_orders_count
 * @property-read EloquentCollection|PaymentAccount[] $seller_transactions
 * @property-read int|null $seller_transactions_count
 * @property-read EloquentCollection|Store[] $stores
 * @property-read int|null $stores_count
 * @property-read Staff|null $support_staff
 * @property-read EloquentCollection|UserLog[] $userLogs
 * @property-read EloquentCollection|CampaignDMCANotification[] $seller_dmca_notifications
 * @property-read int|null $user_logs_count
 * @property-read EloquentCollection|UserBalance[] $user_balances
 * @property-read int|null $user_balances_count
 * @property string|null $klaviyo_private_key
 * @property string|null $klaviyo_list_id
 * @method static Builder|User bulkCampaignEnabled()
 * @method static \Database\Factories\UserFactory factory(...$parameters)
 * @method static Builder|User filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static Builder|User isGoodAccount()
 * @method static Builder|User hasEngageAds()
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static Builder|User permission($permissions)
 * @method static Builder|User query()
 * @method static Builder|User role($roles, $guard = null)
 * @method static Builder|User whereAddress($value)
 * @method static Builder|User whereAdvanced($value)
 * @method static Builder|User whereAutoFulfill($value)
 * @method static Builder|User whereAvatar($value)
 * @method static Builder|User whereBalance($value)
 * @method static Builder|User whereBirthday($value)
 * @method static Builder|User whereCampaignLimit($value)
 * @method static Builder|User whereCity($value)
 * @method static Builder|User whereCompanyId($value)
 * @method static Builder|User whereCountry($value)
 * @method static Builder|User whereCrawledEmailAvatarAt($value)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereCurrency($value)
 * @method static Builder|User whereCustomPayment($value)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailSubscribed($value)
 * @method static Builder|User whereEmailVerifiedAt($value)
 * @method static Builder|User whereFacebook($value)
 * @method static Builder|User whereFlagLog($value)
 * @method static Builder|User whereHoldAmount($value)
 * @method static Builder|User whereHoldTo($value)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereIsMarketplace($value)
 * @method static Builder|User whereIsRef($value)
 * @method static Builder|User whereLanguage($value)
 * @method static Builder|User whereMarketLocation($value)
 * @method static Builder|User whereName($value)
 * @method static Builder|User whereNickname($value)
 * @method static Builder|User whereNote($value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User wherePhone($value)
 * @method static Builder|User wherePostcode($value)
 * @method static Builder|User wherePricingMode($value)
 * @method static Builder|User whereRefId($value)
 * @method static Builder|User whereRole($value)
 * @method static Builder|User whereSaleExpiredAt($value)
 * @method static Builder|User whereSaleStaffId($value)
 * @method static Builder|User whereSaleVolume($value)
 * @method static Builder|User whereSenPoints($value)
 * @method static Builder|User whereShardId($value)
 * @method static Builder|User whereSmartRemarketing($value)
 * @method static Builder|User whereSmsCredit($value)
 * @method static Builder|User whereSmsSubscribed($value)
 * @method static Builder|User whereSofortPayment($value)
 * @method static Builder|User whereState($value)
 * @method static Builder|User whereStatus($value)
 * @method static Builder|User whereStripeId($value)
 * @method static Builder|User whereSupportStaffId($value)
 * @method static Builder|User whereTags($value)
 * @method static Builder|User whereTheme($value)
 * @method static Builder|User whereTierId($value)
 * @method static Builder|User whereTimezone($value)
 * @method static Builder|User whereTotalOrders($value)
 * @method static Builder|User whereTotalPurchases($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereUsername($value)
 * @method static Builder|User whereUtcOffset($value)
 * @mixin Builder
 */
class User extends Authenticatable implements JWTSubject
{
    use HasFactory;
    use SoftDeletes;
    use HasRoles;
    use ScopeFilterDateRangeTrait;

    public const SENPRINTS_SELLER_ID = 1;
    public const FILTER_COLUMN_DATE = 'user.created_at';

    protected $table = 'user';
    protected $connection = 'mysql';

    protected string $guard_name = 'user';

    protected $hidden = [
        'password',
        'laravel_through_key'
    ];

    protected $fillable = [
        'id',
        'name',
        'email',
        'email_verified_at',
        'password',
        'avatar',
        'crawled_email_avatar_at',
        'city',
        'state',
        'country',
        'register_country',
        'birthday',
        'phone',
        'timezone',
        'utc_offset',
        'language',
        'currency',
        'theme',
        'pricing_mode',
        'status',
        'ref_id',
        'shard_id',
        'role',
        'advanced',
        'custom_payment',
        'email_subscribed',
        'sms_subscribed',
        'sms_credit',
        'flag_log',
        'tier_id',
        'campaign_limit',
        'is_ref',
        'support_staff_id',
        'sale_staff_id',
        'tags',
        'facebook',
        'note',
        'smart_remarketing',
        'hold_amount',
        'hold_to',
        'nickname',
        'contest_name',
        'auto_fulfill',
        'auto_render_mockup',
        'dmca_violation_time',
        'tfa_enable',
        'tfa_secret',
        'register_type',
        'confirm_join_contest',
        'sharding_status',
        'sale_volume',
        'postcode',
        'address',
        'market_location',
        'db_connection',
        'marketing_bonus_times',
        'sync_to_customer',
        'klaviyo_public_key',
        'klaviyo_private_key',
        'klaviyo_list_id',
    ];
    public $incrementing = false;
    protected $casts = [
        'sale_expired_at' => 'datetime',
        'first_order_at' => 'datetime',
        'last_order_at' => 'datetime',
        'hold_to' => 'date',
    ];

    protected static function booted(): void
    {
        static::creating(static function ($model) {
            $model->id = generateShardId($model);
            $model->shard_id = config('senprints.shard_id');
        });
    }

    /**
     * Get users by role name
     * @param array $roles
     * @param array $options
     * @param int $pageSize
     * @param bool $hasAnalytics
     * @return mixed
     * @throws \Throwable
     */
    public static function getUserByRoleName(
        array $roles,
        array $options = [],
        int   $pageSize = 0,
        bool  $hasAnalytics = true
    ) {
        $model = self::query()->role($roles);

        $emailVerifiedFilter = !empty($options['verify_email']);

        if (!empty($options['select']) && is_array($options['select'])) {
            foreach ($options['select'] as $select) {
                $model->addSelect('user.' . $select);
            }
        } else {
            $model->select('user.*');
        }

        $model->with([
            'recent_store' => function ($query) {
                return $query->select(['id', 'name']);
            }
        ]);

        if (!empty($options['q'])) {
            $searchType = Arr::get($options, 'search_type', 'default');
            if ($searchType === 'ads_campaign') {
                $model->join('ads_campaign_logs', 'ads_campaign_logs.seller_id', '=', 'user.id')
                    ->whereIn('ads_campaign_logs.action' , AdsLogsActionEnum::engageActions())
                    ->join('ads_campaign', 'ads_campaign.id', '=', 'ads_campaign_logs.ads_campaign_id')
                    ->where(function ($q) use ($options) {
                        $q->where('ads_campaign.utm_campaign', 'like', '%' . $options['q'] . '%')
                            ->orWhere('ads_campaign.utm_source', 'like', '%' . $options['q'] . '%')
                            ->orWhere('ads_campaign.utm_medium', 'like', '%' . $options['q'] . '%');
                    });
            } else if ($searchType === 'country') {
                $keywords = strtoupper($options['q']);
                $model->where('user.register_country', 'like', '%' . $keywords . '%');
            } else {
                if (is_numeric($options['q'])) {
                    $model->where('user.id', $options['q']);
                    $model->limit(1);
                    $pageSize = 1;
                } else {
                    $model->where(function ($q) use ($options) {
                        $q->orWhere('user.email', 'like', $options['q'] . '%');
                        $q->orWhere('user.name', 'like', '%' . $options['q'] . '%');
                    });
                }
            }
        }

        if (isset($options['ids'])) {
            $model->whereIn('user.id', $options['ids']);
        }

        if (isset($options['fulfillment_sellers'])) {
            $dateRanges = Arr::get($options, 'date_ranges');
            $sellerIds = IndexOrder::query()
                ->select('id', 'seller_id')
                ->addFilterAnalytic([], $dateRanges)
                ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                ->whereIn('status', [OrderStatus::PROCESSING, OrderStatus::COMPLETED])
                ->whereNull('deleted_at')
                ->pluck('seller_id')
                ->unique();

            $fulfillSellerIds = User::query()
                ->select('id')
                ->whereIn('id', $sellerIds)
                ->when($emailVerifiedFilter, function ($q) {
                    $q->whereNotNull('email_verified_at');
                })
                ->filterFulfillSeller()
                ->pluck('id');

            if ($fulfillSellerIds->isNotEmpty()) {
                $model->whereIn('user.id', $fulfillSellerIds);
            } else {
                return collect();
            }
        }

        if (!empty($options['status'])) {
            $model->where('user.status', $options['status']);
        }

        if (isset($options['sale_staff_not_null'])) {
            $model->where('user.sale_staff_id', '!=', null);
        }

        if (!empty($options['support_staff_id'])) {
            $model->where('user.support_staff_id', $options['support_staff_id']);
        }

        if (!empty($options['sale_staff_id'])) {
            $model->where('user.sale_staff_id', $options['sale_staff_id']);
        }

        if (!empty($options['role'])) {
            $model->where('user.role', $options['role']);
        }

        if (!empty($options['tier_id'])) {
            $model->where('user.tier_id', $options['tier_id']);
        }

        if (!empty($options['country'])) {
            $model->where('user.country', $options['country']);
        }

        if (isset($options['confirm_join_contest'])) {
            $model->where('user.confirm_join_contest', $options['confirm_join_contest']);
        }

        if (isset($options['min_order'])) {
            $model->where('user.total_orders', '>', $options['min_order']);
        }

        if ($emailVerifiedFilter) {
            $model->whereNotNull('user.email_verified_at');
        }

        if (!empty($options['store_id'])) {
            $model->join('seller_customer', 'seller_customer.customer_id', '=', 'user.id', 'right')
                ->where('seller_customer.store_id', $options['store_id']);
        }

        if (!empty($options['date_range']) && !isset($options['new_sale']) && !isset($options['support_staff_id'])) {
            $model->filterDateRange(
                $options['date_range'],
                $options['start_date'],
                $options['end_date'],
            );
        }

        $sortBy = Arr::get($options, 'sort.0');
        if (!empty($options['order_by_users_id'])) {
            // order by analytic
            $model->orderByRaw("FIELD(user.id,'" . implode("','", $options['order_by_users_id']) . "') desc");
        } elseif (!empty($sortBy) && $sortBy !== 'campaigns') {

            if ($sortBy === 'paid_at') {
                //exception: inactive sellers case
                //return sortBy total_items here for get sellers
                //then we'll sort by paid_at at the end (when have inactive sellers)
                $sortBy = CampaignSortByAllowEnum::TOTAL_ITEMS;
            }

            // order by else
            $prefix = 'user.';

            // left join for order by
            switch ($sortBy) {
                case CampaignSortByAllowEnum::TOTAL_ITEMS:
                case CampaignSortByAllowEnum::TOTAL_ORDERS:
                case CampaignSortByAllowEnum::TOTAL_PROFITS:
                case CampaignSortByAllowEnum::TOTAL_SALES:
                    $prefix = '';
                    $dateRanges['date_type'] = request('date_range', DateRangeEnum::CUSTOM);
                    $dateRanges['range'] = [
                        request('start_date'),
                        request('end_date'),
                    ];

                    $order = Order::query()
                        ->analytic($sortBy)
                        ->addSelect('order.seller_id')
                        ->addFilterAnalytic([], $dateRanges)
                        ->when(isset($options['fulfillment_sellers']), function ($q) {
                            $q->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA]);
                        })
                        ->whereIn('payment_status', [
                            OrderPaymentStatus::PAID,
                            OrderPaymentStatus::PARTIALLY_REFUNDED,
                        ])
                        ->groupBy('order.seller_id');
                    $model
                        ->addSelect('order.*')
                        ->leftJoinSub($order, 'order', 'order.seller_id', '=', 'user.id')
                        ->groupBy('user.id');
                    break;
                case CampaignSortByAllowEnum::ORDERS_SEN_POINTS:
                    $prefix = '';
                    $dateRanges['date_type'] = request('date_range', DateRangeEnum::CUSTOM);
                    $dateRanges['range'] = [
                        request('start_date', '2023-03-12 17:00:00'),
                        request('end_date', '2023-06-18 06:59:59'),
                    ];

                    $order = Order::query()
                        ->select('id')
                        ->addFilterAnalytic([], $dateRanges)
                        ->isValidPaidOrder();

                    $orderProduct = OrderProduct::query()
                        ->select([
                            'sen_points',
                            'seller_id',
                        ])
                        ->where('order_product.fulfill_status', '!=', OrderProductFulfillStatus::CANCELLED)
                        ->joinSub($order, 'order', function ($join) {
                            $join->on('order.id', '=', 'order_product.order_id');
                        });

                    $model
                        ->selectRaw("sum(order_product.sen_points) as $sortBy")
                        ->leftJoinSub($orderProduct, 'order_product', 'order_product.seller_id', '=', 'user.id')
                        ->groupBy('user.id');
                    break;
                case 'stores':
                    $prefix = '';
                    $model
                        ->selectRaw('count(s.id) as stores')
                        ->leftJoin('store as s', 's.seller_id', 'user.id')
                        ->groupBy('user.id');
                    break;
                case 'promotions':
                    $prefix = '';
                    $sub = PromotionRule::query()
                        ->select('seller_id')
                        ->where('status', 1);

                    $model
                        ->selectRaw('count(p.seller_id) as promotions')
                        ->leftJoinSub($sub, 'p', 'p.seller_id', 'user.id')
                        ->groupBy('user.id');
                    break;
            }
            $model->orderByRaw($prefix . $sortBy . ' ' . $options['sort'][1]);
        }

        if (in_array(SystemRole::SELLER, $roles)) {
            // order by campaigns
            if ($sortBy === 'campaigns') {
                $options['is_sort_by_campaigns'] = true;

                $dataCampaigns = (new Elastic())->getCountCampaignBySeller($options, $pageSize);

                $sellerIds = array_column($dataCampaigns, 'seller_id');

                if (!empty($sellerIds)) {
                    if ($options['sort'][1] === 'desc') {
                        $sellerIds = array_reverse($sellerIds);
                    }
                    $model->orderByRaw('FIELD(user.id,' . implode(',', $sellerIds) . ') ' . $options['sort'][1]);
                }
            }

            $model->with(['support_staff:id,name', 'sale_staff:id,name', 'tier']);
            $model->with(['register_ads' => function ($query) {
                $query->select('seller_id', 'ads_campaign_id', 'action')->with('ads_campaign:id,utm_campaign,extra');
            }]);

            if ($hasAnalytics) {
                // if not sort by, select sub query
                if ($sortBy !== 'stores') {
                    $model->withCount('stores as stores');
                }
                if ($sortBy !== 'promotions') {
                    $model->withCount([
                        'promotion_rules as promotions' => function ($query) {
                            return $query->where('status', 1);
                        }
                    ]);
                }
            }
            if (!empty($options['inactive_date_range'])) {
                $sort = Arr::get($options, 'sort.0');
                $sortPaidAt = [];

                if ($sort === 'paid_at') {
                    $sortPaidAt = $options['sort'];
                }
                $result = self::getInactiveUsers($options['inactive_date_range'], $model, $pageSize, $sortPaidAt);
            } else {
                if ($pageSize === 0) {
                    $result = $model->get();
                } else {
                    $result = $model->paginate($pageSize);
                }
            }

            if ($hasAnalytics) {
                if (!isset($dataCampaigns)) {
                    // where in array ids
                    $options['seller_ids'] = $result->modelKeys();

                    $dataCampaigns = (new Elastic())->getCountCampaignBySeller($options, $pageSize);
                }

                foreach ($result as &$seller) {
                    // set campaigns
                    $dataCampaign = Arr::first($dataCampaigns, static function ($arr) use ($seller) {
                        return $arr['seller_id'] === $seller->id;
                    });
                    $seller->campaigns = Arr::get($dataCampaign, 'campaigns', 0);
                }
            }
            return $result;
        }

        if ($pageSize > 0) {
            return $model->paginate($pageSize);
        }

        return $model->get();
    }


    public static function getInactiveUsers($inactiveDateRange, $model, $pageSize, $sortPaidAt = [])
    {
        $startTime = Carbon::now()->subDays(3);
        $endTime = Carbon::now()->subDays(7);
        switch ($inactiveDateRange) {
            case DateRangeEnum::LAST_3_DAYS:
                $startTime = Carbon::now()->subDays(3);
                $endTime = Carbon::now()->subDays(7);
                break;
            case DateRangeEnum::LAST_7_DAYS:
                $startTime = Carbon::now()->subWeek();
                $endTime = Carbon::now()->subMonth();
                break;
            case DateRangeEnum::LAST_30_DAYS:
                $startTime = Carbon::now()->subMonth();
                $endTime = Carbon::now()->subDays(60);
                break;
        }

        // get inactive sellers
        try {
            $orders = Order::query()
                ->getLastPaidOrder($startTime, $endTime)
                ->limit(1000);

            $cacheTime = ($inactiveDateRange === DateRangeEnum::LAST_3_DAYS) ? CacheTime::CACHE_10m : CacheTime::CACHE_24H;

            $inactiveSellers = Cache::remember('inactive_users_' . $inactiveDateRange, $cacheTime, function () use ($orders) {
                return User::query()
                    ->select('user.id', 'user.name', 'user.email', 'order.paid_at')
                    ->role(UserRoleEnum::SELLER)
                    ->joinSub($orders, 'order', function ($join) {
                        $join->on('user.id', '=', 'order.seller_id');
                    })
                    ->orderBy('order.paid_at', 'desc')
                    ->get();
            });
        } catch (\Exception $e) {
            $inactiveSellers = [];
        }
        if (!empty($inactiveSellers)) {
            // take every inactive sellers in result
            // use foreach to make sure sort by $model
            $inactiveSellersIds = $inactiveSellers->pluck('id')->toArray();
            $users = $model->get();

            if ($users->isEmpty()) {
                return $users;
            }

            $result = collect();
            foreach ($users as $user) {
                if (in_array($user->id, $inactiveSellersIds)) {
                    $result->push($user);
                }
            }

            // add paid_at field into sellers
            $result = $result->map(function ($seller) use ($inactiveSellers) {
                $seller->paid_at = $inactiveSellers->where('id', $seller->id)->first()->paid_at;
                return $seller;
            });

            //sort by paid_at
            if (!empty($sortPaidAt)) {
                if (Arr::get($sortPaidAt, 1) === 'desc') {
                    $result = $result->sortByDesc('paid_at');
                } else {
                    $result = $result->sortBy('paid_at');
                }
            }

            if ($pageSize > 0) {
                $currentPage = LengthAwarePaginator::resolveCurrentPage();
                //skip and take records
                $currentPageItems = $result->skip(($currentPage - 1) * $pageSize)->take($pageSize);

                //convert from Support\Collection to DB\Eloquent\Collection
                $eloquentCollection = new EloquentCollection($currentPageItems->values());

                //create our paginator
                $paginatedResult = new LengthAwarePaginator($eloquentCollection, count($result), $pageSize);
                $result = $paginatedResult->setPath(request()->url());
            }
        } else if ($pageSize === 0) {
            $result = $model->get();
        } else {
            $result = $model->paginate($pageSize);
        }

        return $result;
    }

    public function seller_transactions(): HasMany
    {
        return $this->hasMany(PaymentAccount::class, 'seller_id', 'id');
    }

    public function infos(): HasMany
    {
        return $this->hasMany(UserInfo::class, 'user_id', 'id');
    }

    public function seller_dmca_notifications(): HasMany
    {
        return $this->hasMany(CampaignDMCANotification::class, 'seller_id', 'id');
    }

    public static function getSellers(array $options = [], $pageSize = 20, $hasAnalytics = true)
    {
        return self::getUserByRoleName(
            [SystemRole::SELLER, SystemRole::BLOCKED],
            $options,
            $pageSize,
            $hasAnalytics
        );
    }

    public static function getCustomers(array $options = [], $pageSize = 20)
    {
        return self::getUserByRoleName([SystemRole::CUSTOMER], $options, $pageSize);
    }

    public static function getById($role, $id)
    {
        return self::role($role)->where('id', $id);
    }

    public static function getSellerById($id)
    {
        return self::getById([SystemRole::SELLER, SystemRole::BLOCKED], $id);
    }

    public function billings(): HasMany
    {
        return $this->hasMany(SellerBilling::class, 'seller_id', 'id')->limit(15)->orderByDesc('id');
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    public function collections(): HasManyThrough
    {
        return $this->hasManyThrough(Collection::class, SellerCollection::class, 'seller_id', 'id','id', 'collection_id');
    }

    public function pivotCollections()
    {
        return $this->belongsToMany(Collection::class, 'seller_collection', 'seller_id', 'collection_id');
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class, 'user_id', 'id');
    }

    public function stores(): HasMany
    {
        return $this->hasMany(Store::class, 'seller_id', 'id');
    }

    public function promotion_rules(): HasMany
    {
        return $this->hasMany(PromotionRule::class, 'seller_id');
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'seller_id', 'id');
    }

    public function campaigns(): HasMany
    {
        return $this->hasMany(Campaign::class, 'seller_id');
    }

    public function indexCampaigns(): HasMany
    {
        return $this->hasMany(IndexCampaign::class, 'seller_id');
    }

    public function user_balances(): HasMany
    {
        return $this->hasMany(UserBalance::class, 'seller_id');
    }

    public function customer_orders(): HasMany
    {
        return $this->hasMany(Order::class, 'customer_id', 'id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'seller_id');
    }

    public function productReviews(): HasMany
    {
        return $this->hasMany(ProductReview::class, 'customer_id');
    }

    public function userLogs(): HasMany
    {
        return $this->hasMany(UserLog::class, 'user_id', 'id');
    }

    public function support_staff(): HasOne
    {
        return $this->hasOne(Staff::class, 'id', 'support_staff_id');
    }

    public function sale_staff(): HasOne
    {
        return $this->hasOne(Staff::class, 'id', 'sale_staff_id');
    }

    public function seller_orders(): HasMany
    {
        return $this->hasMany(Order::class, 'seller_id');
    }

    public function indexOrders()
    {
        return $this->hasMany(IndexOrder::class, 'seller_id');
    }

    public function seller_order_products(): HasMany
    {
        return $this->hasMany(OrderProduct::class, 'seller_id');
    }

    public function files(): HasMany
    {
        return $this->hasMany(File::class, 'seller_id');
    }

    /**
     * @param $id
     * @return array|null
     */
    public static function get_utc_offset($id): ?array
    {
        $res = self::getById('seller', $id)
            ->select(['timezone', 'utc_offset'])
            ->first();

        if (is_null($res) || is_null($res->utc_offset)) {
            return null;
        }

        return is_null($res->timezone) ? [false, $res->utc_offset] : [$res->timezone, $res->utc_offset];
    }

    public function updateHoldAmount(float $amount): void
    {
        if (empty($this->hold_amount) || (!is_null($this->hold_to) && $this->hold_to->gt(now()))) {
            return;
        }

        // check max value we can update
        if ($amount > $this->hold_amount) {
            $amount = (float) $this->hold_amount;
        }

        $this->hold_amount -= $amount;
        $this->save();
    }

    /**
     * @param float $amount
     * @param string $type
     * @param string $detail
     * @param $orderId
     * @param string $status
     * @param $paymentAccountId
     * @param $transactionKey
     * @param $last_order_at
     * @return SellerBilling|Builder|\Illuminate\Database\Eloquent\Model|null
     * @throws \JsonException|\Throwable
     */
    public function updateBalance(float $amount, string $type, string $detail, $orderId = null, string $status = SellerBillingStatus::COMPLETED, $paymentAccountId = null, $transactionKey = null, $last_order_at = null)
    {
        if ($amount == 0) {
            return null;
        }
        $currentUser = currentUser();
        $userInfo = $currentUser->getInfo();
        $isAdmin = $currentUser->isAdmin();
        $log = null;
        if ($userInfo !== null) {
            $log = 'Updated by ' . $userInfo->email;
        }

        $data = [
            'seller_id' => $this->id,
            'amount' => $amount,
            'detail' => $detail,
            'type' => $type,
            'status' => $status,
            'order_id' => $orderId,
            'payment_account_id' => $paymentAccountId,
            'log' => $log,
            'transaction_key' => $transactionKey
        ];

        if ($isAdmin) {
            $data['staff_id'] = $currentUser->getUserId();
        }
        DB::beginTransaction();
        try {
            $balance = self::query()->where('id', $this->id)->lockForUpdate()->value('balance');
            $balance += $amount;
            if ($balance < 0 && $amount < 0 && !$isAdmin) {
                throw new \RuntimeException('Not enough balance.');
            }
            $data['balance'] = $balance;
            $data['signature'] = SellerBilling::signature($data);
            $transaction = SellerBilling::query()->create($data);
            $update = [
                'balance' => $balance
            ];
            if (!empty($last_order_at)) {
                $update['last_order_at'] = $last_order_at;
            }
            self::query()->whereKey($this->id)->update($update);
            DB::commit();
            $this->balance = $balance;
            return $transaction;
        } catch (\Throwable $e) {
            DB::rollBack();
            logException($e, 'Update balance failed, data: ' . json_encode($data, JSON_THROW_ON_ERROR));
            throw $e;
        }
    }

    /**
     * @param $where
     * @return bool
     */
    public function isExistsActionUpdateBalance($where): bool
    {
        try {
            if (!isset($where['seller_id'])) {
                $where['seller_id'] = $this->id;
            }
            if (!isset($where['balance_type'])) {
                $where['balance_type'] = SellerBalanceTypeEnum::DEFAULT;
            }
            return SellerBilling::query()->where($where)->exists();
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * @param $seller_id
     * @return bool
     */
    public function hasBalance2CanUse($seller_id = null): bool
    {
        try {
            if (is_null($seller_id)) {
                $seller_id = $this->id;
            }
            return UserBalance::query()->where('seller_id', $seller_id)->where('type', SellerBalanceTypeEnum::BALANCE_2)->canUse()->exists();
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * @param float $amount
     * @param string $type
     * @param string $detail
     * @param int $order_id
     * @param int $reward_days
     * @param string $status
     * @param bool $needVerify
     * @return float|int
     */
    public function updateBalance2(float $amount, string $type, string $detail, int $order_id = 0, int $reward_days = 0, string $status = SellerBillingStatus::COMPLETED, bool $needVerify = true)
    {
        $user = currentUser();
        $seller_balance = UserBalance::query()->where('seller_id', $this->id)->where('type', SellerBalanceTypeEnum::BALANCE_2)->first();
        if (empty($seller_balance)) {
            $seller_balance = new UserBalance();
            $seller_balance->seller_id = $this->id;
            $seller_balance->type = SellerBalanceTypeEnum::BALANCE_2;
        }
        $today = Carbon::now();
        $expired_at = !empty($seller_balance->expired_at) ? Carbon::parse($seller_balance->expired_at) : $today->copy()->addHours(24);
        if (!empty($amount)) {
            $balance = !empty($seller_balance->balance) ? (float) $seller_balance->balance : 0;
            //Not enough balance to use
            if ($needVerify && !$user->isAdmin() && ($balance + $amount < 0 || $expired_at->lt($today))) {
                return 0;
            }
            $seller_balance->balance = $balance + $amount;
        }
        if ($reward_days > 0) {
            if ($expired_at->lte($today)) {
                $expired_at = $today;
            }
            $seller_balance->expired_at = $expired_at->addDays($reward_days)->toDateTimeString();
        }
        $seller_balance->save();
        $userInfo = $user->getInfo();
        $log = null;
        if ($userInfo !== null) {
            $log = 'Updated by ' . $userInfo->email;
        }
        $data = [
            'seller_id' => $this->id,
            'amount' => $amount,
            'detail' => $detail,
            'type' => $type,
            'status' => $status,
            'log' => $log,
            'balance_type' => SellerBalanceTypeEnum::BALANCE_2
        ];

        if (!empty($order_id)) {
            $data['order_id'] = $order_id;
        }

        if ($user->isAdmin()) {
            $data['staff_id'] = $user->getUserId();
        }

        $data['balance'] = $seller_balance->balance;
        $data['signature'] = SellerBilling::signature($data);
        SellerBilling::query()->create($data);
        return $amount;
    }

    private function validate($transactions)
    {
        return true;
    }

    // todo @long: create table tier rate and convert to relationship
    public function getSellerCommissionRate(): float
    {
        return 80 / 100;
    }

    public function getArtistCommissionRate(): float
    {
        return 20 / 100;
    }

    /**
     * @return int
     * @throws \Throwable
     */
    public function getNumberCampaignCanCreateAttribute(): int
    {
        try {
            if ($this->guard_name === 'admin') {
                return 100;
            }
            return $this->campaign_limit - (new Elastic())->getNumberCampaignCanCreate($this->id);
        } catch (\Throwable $e) {
            return 100;
        }
    }

    public function getCurrencyAttribute(): string
    {
        return $this->attributes['currency'] ?? CurrencyEnum::USD;
    }

    public function getRefCommission($items, $fulfillItems): float
    {
        $items = $items ?? 0;
        $fulfillItems = $fulfillItems ?? 0;
        $levels = [
            ['from' => 0, 'to' => 15000, 'commissionPerItem' => 0.3],
            ['from' => 15001, 'to' => 30000, 'commissionPerItem' => 0.45],
            ['from' => 30001, 'commissionPerItem' => 0.6],
        ];

        $fulfillCommissionRate = 0.1;

        $level = array_values(array_filter($levels, function ($level) use ($items) {
            $conditions = [];

            if (isset($level['from'])) {
                $conditions['gte'] = $level['from'] <= $items;
            }

            if (isset($level['to'])) {
                $conditions['lte'] = $level['to'] >= $items;
            }

            if (empty($conditions)) {
                return false;
            }

            foreach ($conditions as $condition) {
                if ($condition === false) {
                    return false;
                }
            }

            return true;
        }));

        $commissionRate = !empty($level) && !empty($level[0]) && !empty($level[0]['commissionPerItem']) ? $level[0]['commissionPerItem'] : 0;
        return $items * $commissionRate + $fulfillItems * $fulfillCommissionRate;
    }

    public function getRefFulfillCommission(): float
    {
        return 0.3;
    }

    public function updateSenPoints($points, $orderId, $detail = ''): void
    {
        if (empty($points)) {
            return;
        }
        $startDate = SystemConfig::getConfig('contest_start_date');
        if (
            !empty($startDate)
            && now()->addHours(7)->lt(Carbon::createFromFormat('Y-m-d', $startDate)->startOfDay())
        ) {
            return;
        }
        $endDate = SystemConfig::getConfig('contest_end_date');
        if (
            !empty($endDate)
            && now()->addHours(7)->gt(Carbon::createFromFormat('Y-m-d', $endDate)->endOfDay())
        ) {
            return;
        }
        $data = [
            'seller_id' => $this->id,
            'points' => $points,
            'order_id' => $orderId,
            'detail' => $detail,
        ];
        $this->sen_points += $points;
        $data['point_balance'] = $this->sen_points;
        SenPoint::create($data);
        $this->save();
    }

    public static function isSellerBlocked($sellerId): bool
    {
        return self::query()
            ->where('id', $sellerId)
            ->whereIn('status', [
                UserStatusEnum::SOFT_BLOCKED,
                UserStatusEnum::HARD_BLOCKED
            ])
            ->exists();
    }

    public function getTags(): array
    {
        $tags = !empty($this->tags) ? explode(',', $this->tags) : [];

        if (empty($tags)) {
            return [];
        }

        return array_map('trim', $tags);
    }

    public function isSellName(): bool
    {
        return in_array('sell name', $this->getTags());
    }

    public function isExpressPayout(): bool
    {
        return in_array('express payout', $this->getTags());
    }

    public function isFulfillment(): bool
    {
        return in_array('fulfill', $this->getTags());
    }

    public function addTag($tag): void
    {
        if (!empty($tag) && !in_array($tag, $this->getTags())) {
            $this->tags .= ',' . $tag;
            self::query()
                ->where('id', $this->id)
                ->update(['tags' => $this->tags]);
        }
    }

    public function removeTag($tag): void
    {
        $tags = $this->getTags();
        if (!empty($tag) && !empty($tags) && ($key = array_search($tag, $tags)) !== false) {
            unset($tags[$key]);
            $tags = !empty($tags) ? implode(',', $tags) : null;
            self::query()
                ->where('id', $this->id)
                ->update(['tags' => $tags]);
        }
    }

    /**
     * Check if user is not flagged, blocked or deleted
     *
     * @param $query
     * @return mixed
     */
    public function scopeIsGoodAccount($query)
    {
        return $query->whereIn('status', [
            UserStatusEnum::NEW,
            UserStatusEnum::VERIFIED,
            UserStatusEnum::TRUSTED
        ]);
    }

    public function scopeFilterFulfillSeller($query)
    {
        return $query->where('role', UserRoleEnum::SELLER)
            ->whereNotIn('status', UserStatusEnum::getLimitedStatuses(false))
            ->where('is_deleted', 0);
    }

    public function scopeIsSeller($query)
    {
        return $query->where('role', UserRoleEnum::SELLER);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeHasEngageAds(Builder $query): Builder
    {
        return $query->whereHas('ads_logs', function ($q) {
            $q->whereIn('action', AdsLogsActionEnum::engageActions());
        });
    }

    public function ads_logs(): HasMany
    {
        return $this->hasMany(AdsCampaignLog::class, 'seller_id')->orderByDesc('created_at');
    }

    public function register_ads(): HasOne
    {
        return $this->hasOne(AdsCampaignLog::class, 'seller_id')->whereIn('action', AdsLogsActionEnum::engageActions())->orderByDesc('created_at');
    }

    public function referrer(): BelongsTo
    {
        return $this->belongsTo(__CLASS__, 'ref_id');
    }

    /**
     * Return name if seller don't have a nickname
     *
     * @param $value
     * @return string|null
     */
    public function getNicknameAttribute($value): ?string
    {
        return $value ?: $this->name;
    }

    public function getContestNameAttribute($value): string
    {
        if (!empty($value)) {
            return $value;
        }

        if (!empty($this->nickname)) {
            return $this->nickname;
        }

        if (!empty($this->name)) {
            return $this->name;
        }

        return 'Unknown';
    }

    /**
     * Get total pending sms jobs for minus credits.
     * @return int
     */
    public function getPendingSmsJobsAttribute(): int
    {
        return AbandonedLog::whereStatus(AbandonedLogStatusEnum::PENDING)->whereSellerId($this->id)->count();
    }

    public function isEmailSubscribed(): bool
    {
        return $this->email_subscribed === 1;
    }

    public function unsubscribeEmail(): void
    {
        $this->email_subscribed = 0;
        $this->save();
    }

    public function isSmsSubscribed(): bool
    {
        return $this->sms_subscribed === 1;
    }

    public function unsubscribeSms(): void
    {
        $this->sms_subscribed = 0;
        $this->save();
    }

    public function getCurrentBalance(): float
    {
        return round($this->balance, 2);
    }

    public function recent_store()
    {
        return $this->hasOneThrough(
            Store::class,
            SellerCustomer::class,
            'customer_id',
            'id',
            'id',
            'store_id'
        )->orderBy('seller_customer.created_at', 'desc');
    }

    public function notifyTopUpBalance(): void
    {
        // Notify to telegram
        $sellerTelegram = TelegramNotification::whereSellerId($this->id)->first();
        if ($sellerTelegram) {
            $chatId = $sellerTelegram->chat_id;
            $message = "Your balance is low, please top up your balance to continue using our service.";
            try {
                logToTelegram($chatId, $message);
            } catch (\Exception $ex) {
                Log::error('FulfillNotifyTopUpBalanceCommand - Send telegram failed', [
                    'chat_id' => $chatId,
                    'message' => $message,
                    'error' => $ex->getMessage(),
                ]);
            }
        }
        // Notify to email
        $email = $this->email;
        if ($email) {
            $config = [
                'to' => $email,
                'template' => 'emails.balance_need_top_up_notification',
                'data' => [
                    'seller_name' => $this->name,
                    'subject' => 'We can\'t fulfill your orders due to low balance',
                ],
            ];
            $status = sendEmail($config);
            if (!$status) {
                Log::error('FulfillNotifyTopUpBalanceCommand - Send email failed', $config);
            }
        }
    }

    /**
     * @param null $orderType
     * @param null $order_paid_at
     * @return bool
     */
    public function updateSaleExpiredAt($orderType = null, $order_paid_at = null): bool
    {
        try {
            $sale_expired_at = null;
            if (!empty($this->sale_expired_at)) {
                if ($this->sale_expired_at instanceof Carbon) {
                    $sale_expired_at = $this->sale_expired_at;
                } else {
                    $sale_expired_at = Carbon::parse($this->sale_expired_at);
                }
            }
            $last_order_at = null;
            if (!empty($this->last_order_at)) {
                if ($this->last_order_at instanceof Carbon) {
                    $last_order_at = $this->last_order_at;
                } else {
                    $last_order_at = Carbon::parse($this->last_order_at);
                }
            }
            $paid_at = null;
            if (!empty($order_paid_at)) {
                if ($order_paid_at instanceof Carbon) {
                    $paid_at = $order_paid_at;
                } else {
                    $paid_at = Carbon::parse($order_paid_at);
                }
            }
            graylogInfo('Start update sale expired at for seller id: ' . $this->id, [
                'category' => 'update_sale_expired_at',
                'seller_id' => $this->id,
                'sale_expired_at' => $sale_expired_at?->toDateTimeString(),
                'last_order_at' => $last_order_at?->toDateTimeString(),
                'sale_staff_id' => $this->sale_staff_id,
                'order_type' => $orderType,
                'paid_at' => $paid_at?->toDateTimeString(),
                'user_original' => $this->getOriginal()
            ]);
            $isFirstOrder = (is_null($sale_expired_at) || now()->lte($sale_expired_at)) && is_null($last_order_at);
            $hasSaleStaff = !empty($this->sale_staff_id);
            $isLastOrderOver90Days = $last_order_at && $last_order_at->lte(now()->subDays(90));
            $nowOverSaleExpiredAt = $sale_expired_at && now()->gte($sale_expired_at);
            if ($isFirstOrder || (!$hasSaleStaff && $nowOverSaleExpiredAt && (!$last_order_at || $isLastOrderOver90Days))) {
                if ($paid_at) {
                    $sale_expired_at = $paid_at->clone()->addDays(180);
                } else {
                    $sale_expired_at = now()->addDays(180);
                }
                SellerHistory::query()->insert(array(
                    'seller_id' => $this->id,
                    'action' => SellerHistoryActionEnum::UPDATE_ACCOUNT,
                    'details' => '[System] Auto changed sale expired at to ' . $sale_expired_at->toDateTimeString(),
                    'seller_status' => $this->status,
                    'staff_id' => 0
                ));
                $update_data = [
                    'sale_expired_at' => $sale_expired_at,
                    'first_order_at' => $paid_at ?: now()
                ];
                if ($orderType && $orderType !== OrderTypeEnum::SERVICE) {
                    if ($orderType === OrderTypeEnum::FULFILLMENT || $orderType === OrderTypeEnum::FBA) {
                        $orderType = FirstOrderTypeEnum::FULFILL;
                    } else {
                        $orderType = FirstOrderTypeEnum::PLATFORM;
                    }
                    $update_data['first_order_type'] = $orderType;
                }
                $message = 'Sale expired at: ' . $sale_expired_at?->toDateTimeString() . PHP_EOL;
                $message .= 'Last order at: ' . $last_order_at?->toDateTimeString() . PHP_EOL;
                $message .= 'Sale staff id: ' . $this->sale_staff_id . PHP_EOL;
                $message .= 'Order type: ' . $orderType . PHP_EOL;
                $message .= 'Paid at: ' . $paid_at?->toDateTimeString();
                $embedDesc = [
                    [
                        'description' => $message,
                        'color' => 5763719
                    ]
                ];
                logToDiscord('Updated sale expired at for seller id: ' . $this->id, 'seller_tier', embeds: $embedDesc);
                return self::query()->whereKey($this->id)->update($update_data);
            }
            return false;
        } catch (\Throwable $e) {
            logException($e);
            return false;
        }
    }

    public function getSlugNameWithHash(): string
    {
        $hash = new Hashids('user', 4, 'abcdefghijklmnopqrstuvwxyz');
        $name = $this->nickname ?? $this->name;
        $name = preg_replace('/[^a-zA-Zàáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ0-9 ]/iu', '', $name);
        $name = trim($name);
        $name = ucwords(strtolower($name));
        $slug = Str::slug($name);
        return $slug . '-' . $hash->encode($this->id);
    }

    public static function getUserBySlug($slug): ?User
    {
        $hash = new Hashids('user', 4, 'abcdefghijklmnopqrstuvwxyz');
        $pattern = '/-([^-]+)$/';
        preg_match($pattern, $slug, $matches);
        $hashId = $matches[1] ?? null;
        if (empty($hashId)) {
            return null;
        }
        $id = $hash->decode($hashId)[0] ?? null;
        if (empty($id)) {
            return null;
        }

        return self::query()->where('id', $id)->first();
    }

    /**
     * @return HasMany
     */
    public function import_campaigns_data(): HasMany
    {
        return $this->hasMany(ImportCampaignsData::class, 'seller_id', 'id');
    }

    /**
     * @param Builder $q
     *
     * @return Builder
     */
    public function scopeBulkCampaignEnabled(Builder $q): Builder
    {
        return $q->whereDoesntHave('infos', fn($q) => $q->where('key', UserInfo::KEY_BULK_CAMPAIGN_STATUS));
    }

    /**
     * @return HasMany
     */
    public function social_feed_images(): HasMany
    {
        return $this->hasMany(SocialFeedImages::class, 'seller_id', 'id');
    }

    public function hasPrivateConnection(): bool
    {
        return UserShardingStatusEnum::hasPrivateConnection($this->sharding_status);
    }

    public function shardingCompleted(): bool
    {
        return UserShardingStatusEnum::shardingCompleted($this->sharding_status);
    }

    public function getPrivateConnection($shardingCompleted = true): ?string
    {
        if ($shardingCompleted) {
            return $this->shardingCompleted() ? 'mysql_'.$this->id : ($this->db_connection ?? 'mysql');
        }
        return $this->hasPrivateConnection() ? 'mysql_'.$this->id : ($this->db_connection ?? 'mysql');
    }

    public function getElasticSearchIndex(): string
    {
        return $this->shardingCompleted() ? "products_$this->id" : str_replace('mysql', 'products', $this->db_connection);
    }

    public function tierHistory(): HasMany
    {
        return $this->hasMany(SellerHistory::class, 'seller_id', 'id')->whereNotNull('tier_id');
    }

    public function tier(): BelongsTo
    {
        return $this->belongsTo(SellerTier::class, 'tier_id', 'id');
    }

    public function customers(): BelongsToMany
    {
        return $this->belongsToMany(
            Customer::class,
            'seller_customer',
            'seller_id',
            'customer_id'
        );
    }

    public function fulfillOrders(): HasMany
    {
        return $this->hasMany(Order::class, 'seller_id', 'id')->whereIn('type', OrderTypeEnum::fulFill());
    }

    public function country(): BelongsTo
    {
        return $this->belongsTo(SystemLocation::class, 'country', 'code');
    }

    /**
     * @note Must use this relationship on singlestore connection
     * @return HasOne
     */
    public function userCleanSetting(): HasOne
    {
        return $this->hasOne(IndexUserCleanSettings::class, 'seller_id');
    }
}

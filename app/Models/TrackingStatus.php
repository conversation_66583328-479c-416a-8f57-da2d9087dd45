<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;

/**
 * App\Models\TrackingStatus
 *
 * @property int $id
 * @property int $order_id
 * @property string $tracking_code
 * @property string $order_number
 * @property string $status
 * @property string|null $shipping_carrier
 * @property string $tracking_service
 * @property string|null $exported_at
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property-read \App\Models\Order $order
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\OrderProduct[] $orderProducts
 * @property-read int|null $order_products_count
 * @method static \Database\Factories\TrackingStatusFactory factory(...$parameters)
 * @method static Builder|TrackingStatus newModelQuery()
 * @method static Builder|TrackingStatus newQuery()
 * @method static Builder|TrackingStatus query()
 * @method static Builder|TrackingStatus whereCreatedAt($value)
 * @method static Builder|TrackingStatus whereExportedAt($value)
 * @method static Builder|TrackingStatus whereId($value)
 * @method static Builder|TrackingStatus whereOrderId($value)
 * @method static Builder|TrackingStatus whereOrderNumber($value)
 * @method static Builder|TrackingStatus whereShippingCarrier($value)
 * @method static Builder|TrackingStatus whereStatus($value)
 * @method static Builder|TrackingStatus whereTrackingCode($value)
 * @method static Builder|TrackingStatus whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class TrackingStatus extends Model
{
    use HasFactory;

    protected $table = 'tracking_status';
    protected $fillable = [
        'order_id',
        'tracking_code',
        'order_number',
        'status',
        'shipping_carrier',
        'tracking_service',
        'exported_at'
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function orderProducts(): HasMany
    {
        return $this->hasMany(OrderProduct::class, 'tracking_code', 'tracking_code');
    }
}

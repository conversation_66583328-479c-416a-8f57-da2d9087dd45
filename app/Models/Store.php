<?php

namespace App\Models;

use App\Enums\StoreTypeEnum;
use App\Traits\SPModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\ShopifyApp\Models\ShopifySession;
use Modules\TiktokShop\Models\TiktokShopInfo;

/**
 * App\Models\Store
 *
 * @property int $id
 * @property string $name
 * @property string|null $description
 * @property string $sub_domain
 * @property string|null $domain
 * @property string|null $domain_status
 * @property int $seller_id
 * @property int|null $company_id
 * @property string $theme default/modern
 * @property string|null $theme_options JSON/theme options
 * @property string|null $logo_url
 * @property string|null $banner_url
 * @property string|null $banner_link
 * @property string|null $favicon
 * @property string|null $head_line
 * @property string|null $foot_line
 * @property string $email
 * @property string|null $mail_support
 * @property string|null $cc_email
 * @property string|null $phone
 * @property string|null $address
 * @property string|null $order_prefix
 * @property int $private_store 1: private store
 * @property int $list_all_my_campaigns
 * @property int $random_popular
 * @property int $market_place_listing
 * @property int $market_place_upsell
 * @property string|null $tracking_code JSON
 * @property string|null $timezone The user timezone
 * @property float|null $utc_offset The user timezone utc offset
 * @property string $status active,inactive,deleted
 * @property string|null $social_accounts {"facebook":"","instagram":"","twitter":"","google":""}
 * @property string $default_language
 * @property string|null $default_currency
 * @property int $feature_collection_id
 * @property string|null $feature_text
 * @property string|null $discount_code
 * @property string|null $auto_apply_coupon
 * @property string|null $promotion_title
 * @property string $default_color
 * @property int $checkout_phone
 * @property int $total_campaigns
 * @property int $total_collections
 * @property int $total_orders
 * @property float $total_sales
 * @property float $total_profit
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string $store_type 'normal','google_ads','proxy'
 * @property int $show_payment_button For turn off show payment button at add to cart page (Google Shopping)
 * @property int $show_tipping
 * @property int $default_tipping
 * @property string $product_select_type
 * @property int $show_checkout_shipping_info 0:hide, 1:show
 * @property string|null $sitewide_banner
 * @property int $sitewide_banner_enable
 * @property int $option_label_enable
 * @property int $enable_product_name_export_feed
 * @property int $enable_search
 * @property int $enable_payment_ssl_norton
 * @property int $enable_deliver_to
 * @property int $enable_insurance_fee
 * @property int $show_product_stats
 * @property int $is_proxy
 * @property string $product_review_display
 * @property string|null $woocommerce_webhook_url
 * @property string|null $product_review_coupon
 * @property string|null $product_review_thank_you_message
 * @property int|null $stripe_gateway_id
 * @property int|null $paypal_gateway_id
 * @property string|null $tags
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\File[] $banners
 * @property-read int|null $banners_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Campaign[] $campaigns
 * @property-read int|null $campaigns_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\StoreCollection[] $collections
 * @property-read int|null $collections_count
 * @property-read string $base_url
 * @property-read mixed $sms_configs
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\StoreNavigation[] $headerNavigations
 * @property-read int|null $header_navigations_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\NotificationSetting[] $notification_settings
 * @property-read int|null $notification_settings_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Order[] $orders
 * @property-read int|null $orders_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Product[] $products
 * @property-read int|null $products_count
 * @property-read \App\Models\User $seller
 * @property-read \App\Models\SmsConfig|null $smsConfig
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\StoreDomain[] $storeDomain
 * @property-read int|null $store_domain_count
 * @property-read string $support_mail
 * @property-read int $show_countdown
 * @property-read \Illuminate\Support\Carbon|null $countdown_end_time
 * @method static \Illuminate\Database\Eloquent\Builder|Store addFieldCache()
 * @method static \Database\Factories\StoreFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Store isSenStore()
 * @method static \Illuminate\Database\Eloquent\Builder|Store makeHiddenAll()
 * @method static \Illuminate\Database\Eloquent\Builder|Store newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Store newQuery()
 * @method static \Illuminate\Database\Query\Builder|Store onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Store query()
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereAutoApplyCoupon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereBannerLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereBannerUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereCcEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereCheckoutPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDefaultColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDefaultCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDefaultLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDiscountCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereDomainStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereFavicon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereFeatureCollectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereFeatureText($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereFootLine($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereHeadLine($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereIsProxy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereListAllMyCampaigns($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereLogoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereMarketPlaceListing($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereMarketPlaceUpsell($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereOptionLabelEnable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereOrderPrefix($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store wherePrivateStore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereProductReviewCoupon($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereProductReviewDisplay($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereProductReviewThankYouMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereProductSelectType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store wherePromotionTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereRandomPopular($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereShowCheckoutShippingInfo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereShowPaymentButton($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereShowTipping($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereSitewideBanner($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereSitewideBannerEnable($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereSocialAccounts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereStoreType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereSubDomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTheme($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereThemeOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTimezone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTotalCampaigns($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTotalCollections($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTotalOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTotalProfit($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTotalSales($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereTrackingCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Store whereUtcOffset($value)
 * @method static \Illuminate\Database\Query\Builder|Store withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Store withoutTrashed()
 * @mixin \Eloquent
 */
class Store extends Model
{
    use HasFactory;
    use SoftDeletes;
    use SPModel;

    public const SENPRINTS_STORE_ID = 1;

    protected $table = 'store';
    protected $connection = 'mysql';
    protected $guarded = [];
    protected $appends = ['base_url'];

    protected $casts = [
        'deleted_at' => 'datetime',
    ];

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }

    public function collections(): HasMany
    {
        return $this->hasMany(StoreCollection::class, 'store_id', 'id');
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'store_id', 'id');
    }

    public function campaigns(): BelongsToMany
    {
        return $this->belongsToMany(
            Campaign::class,
            StoreProduct::class,
            'store_id',
            'campaign_id'
        );
    }

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(
            Product::class,
            StoreProduct::class,
            'store_id',
            'product_id'
        );
    }

    public function banners(): HasMany
    {
        return $this->hasMany(File::class, 'store_id', 'id')
            ->where([
                'type_detail' => 'store_banner'
            ]);
    }

    public function collection_banners(): HasMany
    {
        return $this->hasMany(File::class, 'store_id', 'id')
            ->where([
                'type_detail' => 'collection_banner'
            ]);
    }

    /**
     * @param $storeId
     * @return array|null
     */
    public static function get_utc_offset($storeId): ?array
    {
        $res = self::query()->find($storeId, ['timezone', 'utc_offset']);

        if (is_null($res) || is_null($res->utc_offset)) {
            return null;
        }

        return is_null($res->timezone) ? [false, $res->utc_offset] : [$res->timezone, $res->utc_offset];
    }

    public static function getDomainById($storeId)
    {
        $store = self::query()
            ->select('domain')
            ->firstWhere('id', $storeId);

        return !is_null($store) ? $store->domain : null;
    }

    public static function updateDomainStatus($storeId, $status): int
    {
        return self::query()
            ->where('id', $storeId)
            ->update(['domain_status' => $status]);
    }

    public function getBaseUrlAttribute(): string
    {
        if ($this->id == Store::SENPRINTS_STORE_ID) {
            return getMarketPlaceDomain();
        }

        if (!empty($this->domain)) {
            return $this->domain;
        }

        return $this->sub_domain . '.' . getStoreBaseDomain();
    }

    public function headerNavigations(): HasMany
    {
        return $this->hasMany(StoreNavigation::class, 'store_id', 'id')
            ->with(['childrens']);
    }

    /**
     * Get SenPrints main store with id = 1
     *
     * @param $query
     * @return mixed
     */
    public function scopeIsSenStore($query)
    {
        return $query->where('id', self::SENPRINTS_STORE_ID);
    }

    public function scopeAddFieldCache($query)
    {
        return $query->addselect(['id', 'domain', 'sub_domain', 'domain_status']);
    }

    public function smsConfig()
    {
        return $this->hasOne(SmsConfig::class, 'store_id', 'id');
    }

    public static function belongsToCurrentSeller($storeId): bool
    {
        return self::query()
            ->where([
                'id' => $storeId,
                'seller_id' => currentUser()->getUserId()
            ])
            ->exists();
    }

    public function notification_settings()
    {
        return $this->hasMany(NotificationSetting::class, 'store_id');
    }

    public function getSmsConfigsAttribute()
    {
        $configs = $this->notification_settings;

        if ($configs instanceof \Illuminate\Database\Eloquent\Collection) {
            $hasItem = $configs->count() > 0;
        } else {
            $hasItem = !empty($configs);
        }

        if ($hasItem) {
            return $configs->filter(fn($notification) => $notification->channel === 'sms');
        }

        return null;
    }

    public function storeDomain(): HasMany
    {
        return $this->hasMany(StoreDomain::class, 'store_id', 'id');
    }

    public function connection(): HasOne
    {
        return $this->hasOne(StoreConnection::class)->where('status', 'active');
    }

    public function headTags(): HasMany
    {
        return $this->hasMany(StoreHeadTag::class, 'store_id', 'id')
            ->where('enabled', 1);
    }

    public function isExpressListing(): bool
    {
        return $this->store_type === StoreTypeEnum::EXPRESS_LISTING;
    }

    public function shopify_session(): HasOne
    {
        return $this->hasOne(ShopifySession::class, 'store_id', 'id');
    }

    public function tiktok_shop(): HasOne
    {
        return $this->hasOne(TiktokShopInfo::class, 'store_id', 'id');
    }

    public function checkoutStore(): HasOne
    {
        return $this->hasOne(Store::class, 'id', 'checkout_store_id');
    }

    public function getSupportMailAttribute(): string
    {
        if ($this->id != Store::SENPRINTS_STORE_ID) {
            $value = $this->mail_support;
            if (!$value) {
                $value = $this->domain ? strtok($this->domain, '.') : $this->sub_domain;
            }
            return preg_replace("/[^a-zA-Z0-9]+/", "", $value) . '@storehelp.net';
        }

        return $this->email;
    }
}

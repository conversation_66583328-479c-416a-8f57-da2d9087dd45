<?php

namespace App\Models;

use App\Enums\CacheTime;
use App\Enums\CampaignSortByAllowEnum;
use App\Enums\CampaignStatusEnum;
use App\Enums\ElasticProductStatusEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Services\StoreService;
use App\Services\UserService;
use App\Traits\ElasticClient;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Throwable;

class Elastic
{
    use ElasticClient;

    private const ARR_LISTING_FEED = [
        'id',
        'name',
        'seller_id',
        'campaign_id',
        'seller_id',
        'campaign_name',
        'slug',
        'description',
        'thumb_url',
        'google_category_id',
        'options',
        'default_option',
        'category_slugs',
        'collection_slugs',
        'price',
        'template_id',
        'pricing_mode',
        'product_type',
        'express_product_id',
        'system_product_type',
        'market_location',
        'system_type',
    ];

    public const ARR_FIELD_LISTING_PRODUCT = [
        "id",
        "campaign_id",
        "supplier_id",
        "seller_id",
        "auth_id",
        "company_id",
        "template_id",
        "name",
        "thumb_url",
        "options",
        "mockup_type",
        "default_option",
        "print_spaces",
        "currency_code",
        "market_location",
        "base_cost",
        "base_costs",
        "price",
        "old_price",
        "shipping_cost",
        "status",
        "product_type",
        "slug",
        "extra_print_cost",
        "default_product_id",
        "tm_status",
        "sku",
        "pricing_mode",
        "public_status",
        "google_category_id",
        "created_at",
        "updated_at",
        "render_mode",
        "personalized",
        "full_printed",
    ];

    /**
     * @param array $options
     * @param int $limit
     * @return array
     * @throws Throwable
     */
    public function getCountCampaignBySeller(array $options, int $limit = 0): array
    {
        $filters                             = [];
        $filters['bool']['filter'][]['term'] = [
            'product_type' => ProductType::CAMPAIGN
        ];
        if (!empty($options['is_sort_by_campaigns'])) {
            $options['direction'] = $options['sort'][1];
        } else {
            $filters['bool']['filter'][]['terms'] = [
                'seller_id' => $options['seller_ids']
            ];
        }

        // get all
        if ($limit === 0) {
            $limit = 10000;
        }

        return $this->elasticGetCountCampaignBySeller($filters, $options, $limit);
    }

    /**
     * @param array $arrListing
     * @param array $filters
     * @param int $limit
     * @param int $currentPage
     * @param bool $orderByFieldInElastic
     * @param array $arrIdSortBy
     * @param bool $searchAllIndex
     * @return array
     * @throws Throwable
     */
    public function getCampaign(
        array $arrListing,
        array $filters = [],
        int $limit = 15,
        int $currentPage = 1,
        bool $orderByFieldInElastic = true,
        array $arrIdSortBy = [],
        bool $searchAllIndex = false
    ): array {
        $query = [];
        $sort  = [];
        $index = null;

        $arrIdSortBy = array_filter($arrIdSortBy);
        // order by array id from analytic
        if (!empty($arrIdSortBy)) {
            try {
                $arrId = array_flip($arrIdSortBy);

                $sort += [
                    '_script' => [
                        'type'   => 'number',
                        'script' => [
                            // get by id script
                            'id'     => "calculate-score",
                            'params' => $arrId,
                        ],
                        'order'  => 'asc',
                    ]
                ];
            } catch (Throwable $e) {
                logToDiscord('Campaign Index: ' . $e->getMessage() . 'Arr:' . json_encode($arrIdSortBy));
            }
        }

        // filtering
        $query['bool']['filter'][]['terms'] = [
            'product_type' => $filters['product_types'] ?? [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
                ProductType::CAMPAIGN_TEMPLATE,
            ]
        ];
        if (!empty($filters['search'])) {
            $this->searchKeyword($filters['search'], $query, $sort);
        }
        if (!empty($filters['id'])) {
            $query['bool']['filter'][]['term'] = [
                'id' => $filters['id']
            ];
        }
        if (data_get($filters, 'ids')) {
            $query['bool']['filter'][]['terms'] = [
                'id' => $filters['ids']
            ];
        }
        if (!empty($filters['collection_id'])) {
            $query['bool']['filter'][]['term'] = [
                'collection_ids' => $filters['collection_id']
            ];
        }
        if (!empty($filters['seller_id'])) {
            $query['bool']['filter'][]['term'] = [
                'seller_id' => $filters['seller_id']
            ];
            $index = $this->getIndexProduct($filters['seller_id']);
        }
        if (!empty($filters['seller_ids'])) {
            $query['bool']['filter'][]['terms'] = [
                'seller_id' => $filters['seller_ids']
            ];
            $searchAllIndex = true;
        }
        if (!empty($filters['status'])) {
            $query['bool']['filter'][]['term'] = [
                'status' => $filters['status']
            ];
        }
        if (!empty($filters['not_status'])) {
            $query['bool']['must_not'][]['term'] = [
                'status' => $filters['not_status']
            ];
        }
        if (!empty($filters['not_in_ids'])) {
            $query['bool']['must_not'][]['terms'] = [
                'id' => $filters['not_in_ids']
            ];
        }
        if (!empty($filters['not_in_seller_ids'])) {
            $query['bool']['must_not'][]['terms'] = [
                'seller_id' => $filters['not_in_seller_ids']
            ];
        }
        if (!empty($filters['tm_statuses'])) {
            $query['bool']['filter'][]['terms'] = [
                'tm_status' => $filters['tm_statuses']
            ];
        }
        if (!empty($filters['public_status_detail'])) {
            $query['bool']['filter'][]['term'] = [
                'public_status_detail' => $filters['public_status_detail']
            ];
        }
        if (!empty($filters['product_type'])) {
            $query['bool']['filter'][]['term'] = [
                'product_type' => $filters['product_type']
            ];
        }
        if (!empty($filters['system_product_type'])) {
            $query['bool']['filter'][]['term'] = [
                'system_product_type' => $filters['system_product_type']
            ];
        }
        if (!empty($filters['created_at_from_date_to_now'])) {
            $query['bool']['filter'][]['range'] = [
                'created_at' => [
                    'gte' => $filters['created_at_from_date_to_now'],
                    'lte' => Carbon::now(),
                ]
            ];
        }

        if (! empty($filters['date_time_range'])) {
            $query['bool']['filter'][]['range'] = collect($filters['date_time_range'])
                ->map(static fn ($date, $field) => ['gte' => $date[0], 'lte' => $date[1]])
                ->toArray();
        }

        if (!empty($filters['system_types'])) {
            $query['bool']['filter'][]['terms'] = [
                'system_type' => $filters['system_types']
            ];
        }
        if (!empty($filters['full_printed'])) {
            $query['bool']['filter'][]['terms'] = [
                'full_printed' => $filters['full_printed']
            ];
        }
        if (!empty($filters['store_id'])) {
            if (!empty($filters['collection_ids'])) {
                $queryCondition = &$query['bool']['filter'][]['bool'];

                $queryCondition['should'][]['terms'] = [
                    'collection_ids' => $filters['collection_ids'],
                ];
                $queryCondition['should'][]['term']  = [
                    'store_ids' => $filters['store_id'],
                ];

                $queryCondition['minimum_should_match'] = 1;
            } elseif (is_array($filters['store_id'])) {
                $query['bool']['filter'][]['terms'] = [
                    'store_ids' => $filters['store_id']
                ];
            } else {
                $query['bool']['filter'][]['term'] = [
                    'store_ids' => $filters['store_id']
                ];
            }
        }

        $body = [
            'query'   => $query,
            'size'    => $limit,
            'from'    => $limit * ($currentPage - 1),
            '_source' => [
                'includes' => $arrListing
            ],
        ];
        if ($orderByFieldInElastic) {
            $sort += [
                CampaignSortByAllowEnum::getDefault() => 'desc'
            ];
        }

        if (!empty($sort)) {
            $body['sort'] = $sort;
        }
        return $this->elasticGetProductAndTotal($query, $body, true, $searchAllIndex, index: $index);
    }

    /**
     * @param array|null $filters
     * @param bool $justCount
     * @param array $justColumns
     * @param $afterId
     * @return array|int
     * @throws Throwable
     */
    public function getProductFeed(
        ?array $filters = [],
        bool $justCount = false,
        array $justColumns = [],
        $afterId = null
    ) {
        $query         = [];
        $query['bool'] = [];

        $maxLengthTerms = 50000;
        $limitSize      = 5000;

        if (isset($filters['ids'])) {
            $arChunks       = array_chunk($filters['ids'], $maxLengthTerms);
            $queryCondition = &$query['bool']['filter'][]['bool'];

            foreach ($arChunks as $arr) {
                $queryCondition['should'][]['terms'] = [
                    'id' => $arr
                ];
            }
            $queryCondition['minimum_should_match'] = 1;
        }

        if (!empty($filters['default_product_only'])) {
            $query['bool']['must'][]['script'] = [
                'script' => [
                    "source" => "doc['id'].value == doc['default_product_id'].value"
                ]
            ];
        }

        if (!empty($filters['template_product_ids'])) {
            $query['bool']['filter'][]['terms'] = [
                'template_id' => $filters['template_product_ids']
            ];
        }

        if (!empty($filters['colors'])) {
            if (!empty($filters['default_color_only'])) {
                $query['bool']['filter'][]['terms'] = [
                    'default_option' => $filters['colors']
                ];
            }

            $query['bool']['filter'][]['terms'] = [
                'options.color' => $filters['colors']
            ];
        }

        if (!empty($filters['sizes'])) {
            $query['bool']['filter'][]['terms'] = [
                'options.size' => $filters['sizes']
            ];
        }

        if (!empty($filters['include_keywords'])) {
            $query['bool']['filter'][]['terms'] = [
                'search_text' => $filters['include_keywords']
            ];
        }

        if (!empty($filters['start_date'])) {
            $query['bool']['filter'][]['range']['created_at'] = [
                'gte' => date_create($filters['start_date'])->format('Y-m-d\TH:i:s')
            ];
        }

        if (!empty($filters['end_date'])) {
            $query['bool']['filter'][]['range']['created_at'] = [
                'lte' => date_create($filters['end_date'])->format('Y-m-d\TH:i:s')
            ];
        }

        if (!empty($filters['keywords'])) {
            $query['bool']['must_not'][]['terms'] = [
                'search_text' => $filters['keywords']
            ];
        }

        $query['bool']['must_not'][]['prefix'] = [
            'express_product_id' => "express_"
        ];

        $query['bool']['filter'][]['term'] = [
            'product_type' => ProductType::PRODUCT
        ];

        $query['bool']['filter'][]['term'] = [
            'status' => CampaignStatusEnum::ACTIVE
        ];

        $query['bool']['must_not'][]['match'] = [
            'google_category_id' => -1
        ];

        if (!empty($filters['collection_ids'])) {
            $query['bool']['filter'][]['terms'] = [
                'collection_ids' => $filters['collection_ids']
            ];
        }

        if (!empty($filters['exclude_social_feed_ids'])) {
            $ids = [];
            foreach ($filters['exclude_social_feed_ids'] as $socialFeedId) {
                $filtersFeed = SocialFeed::query()
                    ->where('id', $socialFeedId)
                    ->value('filters');
                if (!empty($filtersFeed['default_product_only'])) {
                    $filtersFeed['ids'] = $this->getAllDefaultProductIds($filtersFeed);
                }
                $ids = $this->getAllDefaultProductIds($filtersFeed);
            }
            if (!empty($ids)) {
                $arChunks = array_chunk($ids, $maxLengthTerms);

                foreach ($arChunks as $arr) {
                    $query['bool']['must_not'][]['terms'] = [
                        'id' => $arr
                    ];
                }
                unset($ids);
            }
        }

        if (isset($filters['store_id']) && $filters['store_id'] !== 1) {
            $query['bool']['filter'][]['term'] = [
                'seller_id' => $filters['seller_id']
            ];

            $collectionIds = StoreCollection::query()
                ->where('store_id', $filters['store_id'])
                ->pluck('collection_id')
                ->toArray();

            // where store id or store collection
            $queryCondition                         = &$query['bool'];
            $queryCondition['should'][]['term']     = [
                'store_ids' => $filters['store_id']
            ];
            $queryCondition['should'][]['terms']    = [
                'collection_ids' => $collectionIds
            ];
            $queryCondition['minimum_should_match'] = 1;
        } else {
            $query['bool']['filter'][]['term'] = [
                'public_status' => 1
            ];
        }

        $seller = User::query()
            ->where('id', $filters['seller_id'])
            ->first();

        if ($justCount) {
            return $this->elasticGetTotal($query, index: StoreService::getElasticSearchIndexes($seller));
        }

        if (empty($justColumns)) {
            $source = self::ARR_LISTING_FEED;
        } else {
            $source = $justColumns;
        }

        $body = [
            'query'            => $query,
            'size'             => $limitSize,
            'from'             => 0,
            'track_total_hits' => false, // true: get correct match value | false: get faster result
            '_source'          => [
                'includes' => $source
            ],
            'sort'             => [
                'id' => 'asc', // for paginate
            ],
        ];
        if (!empty($afterId)) {
            $body['search_after'] = [$afterId];
        }

        return $this->elasticGetProductFeed($body, index: StoreService::getElasticSearchIndexes($seller));
    }

    /**
     * @param $userId
     * @param $size
     * @return array
     * @throws Throwable
     */
    public function indexOptionsSizesAndColors($userId, $size): array
    {
        $body = [];
        $body['query']['bool']['filter'][]['term'] = [
            'seller_id' => $userId
        ];
        $body['aggs'] = [
            'color_group' => [
                'terms' => [
                    'field' => 'options.color.keyword',
                    'size'  => $size
                ]
            ],
            'size_group'  => [
                'terms' => [
                    'field' => 'options.size.keyword',
                    'size'  => $size
                ]
            ]
        ];
        $body['size'] = 0;
        return $this->elasticGetOptionsSizesAndColors($body);
    }

    /**
     * @param array|null $filters
     * @param bool $justCount
     * @param $afterId
     * @return array|int
     * @throws Throwable
     */
    public function getProductWithDefaultFeed(?array $filters, bool $justCount = false, $afterId = null)
    {
        return (new self())->getProductFeed($filters, $justCount, [], $afterId);
    }

    /**
     * @param $id
     * @return int
     * @throws Throwable
     */
    public function getNumberCampaignCanCreate($id): int
    {
        return cache()->remember('number_campaign_of_' . $id, CacheTime::CACHE_5m, function () use ($id) {
            $query = [];
            $query['bool']['filter'][]['bool'] = ['should' => [
                ['term' => [
                    'product_type' => ProductType::CAMPAIGN
                ]],
                ['term' => [
                    'system_product_type' => ProductType::CAMPAIGN_EXPRESS
                ]],
            ]];
            $query['bool']['filter'][]['term'] = [
                'seller_id' => $id
            ];
            $query['bool']['filter'][]['term'] = [
                'status' => 'active'
            ];
            $query['bool']['filter'][]['range'] = [
                'created_at' => [
                    'gte' => now()->subHours(24)
                ]
            ];
            return $this->elasticGetTotal($query);
        });
    }

    /**
     * @param $keyword
     * @param $query
     * @param $sort
     * @param $justName
     * @return void
     */
    private function searchKeyword($keyword, &$query, &$sort, $justName = false): void
    {
        $search = trim($keyword);
        /** @noinspection HttpUrlsUsage */
        if (str_starts_with($search, 'http://') || str_starts_with($search, 'https://')) {
            $search = Str::before(Str::afterLast($search, '/'), '?');
        }
        $search = escapeSpecialCharacter($search);
        if ($justName) {
            $querySearch = 'name:*' . $search . '*';
            $query['bool']['must']['query_string'] = [
                'query' => $querySearch
            ];
        } else {
            // priority search exact slug, exact name, similar slug, similar name
            $queryCondition = &$query['bool'];
            $queryCondition['should'][]['term'] = [
                'slug.keyword' => [
                    'value' => $search,
                    'boost' => 6,
                ]
            ];
            $queryCondition['should'][]['term'] = [
                'name.keyword' => [
                    'value' => $search,
                    'boost' => 5,
                ]
            ];
            $queryCondition['should'][]['prefix'] = [
                'slug.keyword' => [
                    'value' => $search,
                    'boost' => 4,
                ]
            ];
            $queryCondition['should'][]['prefix'] = [
                'name.keyword' => [
                    'value' => $search,
                    'boost' => 3,
                ]
            ];
            $queryCondition['should'][]['match'] = [
                'slug' => [
                    'query' => $search,
                    'boost' => 2,
                ]
            ];
            $queryCondition['should'][]['match'] = [
                'name' => [
                    'query' => $search,
                    'boost' => 1,
                ]
            ];
        }
        // order by score searching
        $sort += [
            '_score' => 'desc'
        ];
    }

    /**
     * @param array $querySearch
     * @param bool $includeTotal
     * @return array
     * @throws Throwable
     */
    public function getProduct(array $querySearch, bool $includeTotal = true): array
    {
        $query = [];
        $sort  = [];

        if (!empty($querySearch['type'])) {
            $query['bool']['filter'][]['term'] = [
                'product_type' => $querySearch['type']
            ];
        }
        $searchAllIndex = false;
        if (!empty($querySearch['search'])) {
            $this->searchKeyword($querySearch['search'], $query, $sort, true);
            $searchAllIndex = true;
        }

        if (!empty($querySearch['search_all_index']) && $querySearch['search_all_index'] == 1) {
            $searchAllIndex = true;
        }

        if (!empty($querySearch['status'])) {
            $query['bool']['filter'][]['term'] = [
                'status' => $querySearch['status']
            ];
        }

        if (!empty($querySearch['system_type'])) {
            $query['bool']['filter'][]['term'] = [
                'system_type' => $querySearch['system_type']
            ];
        }

        if (!empty($querySearch['ids'])) {
            $query['bool']['filter'][]['terms'] = [
                'id' => $querySearch['ids']
            ];
        }

        if (!empty($querySearch['id']) || !empty($querySearch['template_id'])) {
            $queryCondition = &$query['bool']['filter'][]['bool'];
            if (!empty($querySearch['id'])) {
                $queryCondition['should'][]['term'] = [
                    'id' => $querySearch['id']
                ];
            }
            if (!empty($querySearch['template_id'])) {
                $queryCondition['should'][]['term'] = [
                    'template_id' => $querySearch['template_id']
                ];
            }
            $queryCondition['minimum_should_match'] = 1;
        }

        if (!empty($querySearch['campaign_id'])) {
            $query['bool']['filter'][]['term'] = [
                'campaign_id' => $querySearch['campaign_id']
            ];
        }

        if (!empty($querySearch['exists'])) {
            foreach ($querySearch['exists'] as $field) {
                $query['bool']['filter'][]['exists'] = [
                    'field' => $field
                ];
            }
        }

        if (!empty($querySearch['sort'])) {
            foreach ($querySearch['sort'] as $field => $direction) {
                $sort += [
                    $field => $direction
                ];
            }
        }

        $arrListing = $querySearch['listing'] ?? [
            "id",
            "campaign_id",
            "supplier_id",
            "seller_id",
            "auth_id",
            "company_id",
            "template_id",
            "name",
            "thumb_url",
            "options",
            "mockup_type",
            "default_option",
            "print_spaces",
            "currency_code",
            "market_location",
            "base_cost",
            "base_costs",
            "price",
            "old_price",
            "shipping_cost",
            "status",
            "product_type",
            "slug",
            "extra_print_cost",
            "default_product_id",
            "tm_status",
            "sku",
            "pricing_mode",
            "public_status",
            "google_category_id",
            "created_at",
            "updated_at",
            "render_mode",
            "personalized",
            "full_printed",
        ];

        $body = [
            'query'   => $query,
            'size'    => $querySearch['limit'],
            'from'    => $querySearch['from'] ?? 0,
            'sort'    => $sort,
            '_source' => [
                'includes' => $arrListing
            ],
        ];

        return $this->elasticGetProductAndTotal($query, $body, $includeTotal, $searchAllIndex);
    }

    /**
     * @param $filters
     * @return array|null
     * @throws Throwable
     */
    public function getSearchKeywords($filters): ?array
    {
        $query = [];
        $dateRange = filterQueryByDateRange($filters['date_range']);
        if (!empty($dateRange)) {
            if (is_object($dateRange)) {
                $query['bool']['filter'][]['range']['created_at']['gte'] = $dateRange;
            } else {
                $query['bool']['filter'][]['range']['created_at'] = [
                    'gte' => $dateRange[0],
                    'lte' => $dateRange[1]
                ];
            }
        }

        if (!empty($filters['seller_id'])) {
            $query['bool']['filter'][]['term'] = [
                'seller_id' => $filters['seller_id']
            ];
        }

        if (!empty($filters['store_id'])) {
            if (is_array($filters['store_id'])) {
                $query['bool']['filter'][]['terms'] = [
                    'store_ids' => $filters['store_id']
                ];
            } else {
                $query['bool']['filter'][]['term'] = [
                    'store_id' => $filters['store_id']
                ];
            }
        }

        $body = [
            'aggs' => [
                'keyword_group' => [
                    'terms' => [
                        'field' => 'keyword',
                        'size'  => 100
                    ]
                ],
            ],
            'size' => 0,
        ];
        if (!empty($query)) {
            $body['query'] = $query;
        }

        $querySearch = [
            'index' => 'search_keyword',
            'body'  => $body
        ];

        $response = $this->elastic('search', $querySearch, 'elasticlog');

        return Arr::get(
            $response,
            'aggregations.keyword_group.buckets'
        );
    }

    /**
     * @param $filters
     * @return array
     * @throws Throwable
     */
    public function getVisitLog($filters): array
    {
        $query                             = [];
        $query['bool']['filter'][]['term'] = [
            'sessionId.keyword' => $filters['session_id']
        ];

        if (empty($filters['is_full'])) {
            $query['bool']['must_not'][]['terms'] = [
                'event' => [
                    'scroll',
                    'mouse_move',
                ]
            ];
        }
        $body = [
            'size'    => 1000,
            'sort'    => [
                'time' => 'asc',
            ],
            '_source' => [
                'includes' => [
                    'event',
                    'elementName',
                    'url',
                    'campaignId',
                    'campaignSlug',
                    'country',
                    'time',
                    'value',
                    'point',
                ]
            ],
        ];

        if (!empty($query)) {
            $body['query'] = $query;
        }

        $querySearch = [
            'index' => 'user-event',
            'body'  => $body
        ];

        $this->response = $this->elastic('search', $querySearch, 'elasticlog');

        return $this->elasticResponse();
    }

    /**
     * @param $filters
     * @return array|null
     * @throws Throwable
     */
    public function getNewestCampaigns($filters): ?array
    {
        $query                             = [];
        $seller = User::query()->find($filters['store_id']);
        $elasticSearchIndex = $seller?->getElasticSearchIndex() ?? get_env('ELATICSEARCH_INDEX', 'products');
        $query['bool']['filter'][]['term'] = [
            'store_ids' => $filters['store_id'],
        ];
        $query['bool']['filter'][]['term'] = [
            'product_type' => ProductType::CAMPAIGN,
        ];

        $body = [
            'track_total_hits' => false,
            'query'            => $query,
            'size'             => 1000,
            'sort'             => [
                'created_at' => 'desc',
            ],
            '_source'          => [
                'includes' => [
                    'slug',
                ]
            ],
        ];

        $querySearch = [
            'index' => $elasticSearchIndex,
            'body'  => $body
        ];

        $this->response = $this->elastic('search', $querySearch);

        return $this->elasticResponse();
    }

    /**
     * @param $filters
     * @param $store
     * @return array
     * @throws Throwable
     */
    public function getSimilarCampaigns($filters, $store): array
    {
        $query = [];
        $query['bool']['filter'][]['range']['id'] = [
            'gte' => $filters['id'],
        ];
        $query['bool']['filter'][]['term']        = [
            'status' => ProductStatus::ACTIVE,
        ];
        $query['bool']['filter'][]['term']        = [
            'product_type' => ProductType::CAMPAIGN,
        ];

        if ($store->id === Store::SENPRINTS_STORE_ID) {
            $query['bool']['filter'][]['term'] = [
                'public_status' => ElasticProductStatusEnum::PUBLIC,
            ];
        } else {
            $query['bool']['filter'][]['term'] = [
                'seller_id' => $store->seller_id,
            ];
        }

        $body = [
            'track_total_hits' => false,
            'query'            => $query,
            'size'             => 10,
            'sort'             => [
                'created_at' => 'desc',
            ],
            '_source'          => [
                'includes' => [
                    'id',
                    'name',
                    'slug',
                ]
            ],
        ];
        $indexes = StoreService::getElasticSearchIndexes($store->seller, $store);
        $querySearch = [
            'index' => $indexes,
            'body'  => $body
        ];

        $this->response = $this->elastic('search', $querySearch);

        return $this->elasticResponse();
    }

    public function getIds($query): array
    {
        $body = [
            'track_total_hits' => false,
            'query'            => $query,
            'size'             => 5000,
            '_source'          => [
                'includes' => [
                    'id',
                ]
            ],
        ];

        $querySearch = [
            'index' => get_env('ELATICSEARCH_INDEX', 'products'),
            'body'  => $body
        ];

        $this->response = $this->elastic('search', $querySearch);
        $ids = $this->elasticResponse();

        return array_column($ids ?? [], 'id');
    }

    public function countCampaigns(
        array $filters = []
    ) {
        $query = [];
        $index = null;
        // filtering
        $query['bool']['filter'][]['terms'] = [
            'product_type' => [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
                ProductType::CAMPAIGN_TEMPLATE,
            ]
        ];
        $searchAllIndex = false;
        if (!empty($filters['search'])) {
            $this->searchKeyword($filters['search'], $query, $sort);
            $searchAllIndex = true;
        }
        if (!empty($filters['id'])) {
            $query['bool']['filter'][]['term'] = [
                'id' => $filters['id']
            ];
        }
        if (!empty($filters['collection_id'])) {
            $query['bool']['filter'][]['term'] = [
                'collection_ids' => $filters['collection_id']
            ];
        }
        if (!empty($filters['seller_id'])) {
            $query['bool']['filter'][]['term'] = [
                'seller_id' => $filters['seller_id']
            ];
            $index = $this->getIndexProduct($filters['seller_id']) ;
        }
        if (!empty($filters['seller_ids'])) {
            $query['bool']['filter'][]['terms'] = [
                'seller_id' => $filters['seller_ids']
            ];
            $searchAllIndex = true;
        }
        if (!empty($filters['status'])) {
            $query['bool']['filter'][]['term'] = [
                'status' => $filters['status']
            ];
        }
        if (!empty($filters['not_status'])) {
            $query['bool']['must_not'][]['term'] = [
                'status' => $filters['not_status']
            ];
        }
        if (!empty($filters['tm_statuses'])) {
            $query['bool']['filter'][]['terms'] = [
                'tm_status' => $filters['tm_statuses']
            ];
        }
        if (!empty($filters['public_status_detail'])) {
            $query['bool']['filter'][]['term'] = [
                'public_status_detail' => $filters['public_status_detail']
            ];
        }
        if (!empty($filters['product_type'])) {
            $query['bool']['filter'][]['term'] = [
                'product_type' => $filters['product_type']
            ];
        }
        if (!empty($filters['system_product_type'])) {
            $query['bool']['filter'][]['term'] = [
                'system_product_type' => $filters['system_product_type']
            ];
        }

        if (!empty($filters['created_at_from_date_to_now'])) {
            $query['bool']['filter'][]['range'] = [
                'created_at' => [
                    'gte' => $filters['created_at_from_date_to_now'],
                    'lte' => Carbon::now(),
                ]
            ];
        }

        if (!empty($filters['store_id'])) {
            if (!empty($filters['collection_ids'])) {
                $queryCondition = &$query['bool']['filter'][]['bool'];

                $queryCondition['should'][]['terms'] = [
                    'collection_ids' => $filters['collection_ids'],
                ];
                $queryCondition['should'][]['term']  = [
                    'store_ids' => $filters['store_id'],
                ];

                $queryCondition['minimum_should_match'] = 1;
            } elseif (is_array($filters['store_id'])) {
                $query['bool']['filter'][]['terms'] = [
                    'store_ids' => $filters['store_id']
                ];
            } else {
                $query['bool']['filter'][]['term'] = [
                    'store_ids' => $filters['store_id']
                ];
            }
        }

        return $this->elasticGetTotal($query, $searchAllIndex, index: $index);
    }

    /**
     * @param array $arrListing
     * @param array $filters
     * @return array
     * @throws Throwable
     */
    public function getCampaignDetail(array $arrListing, array $filters): array
    {
        $query = [];

        $query['bool']['filter'][]['terms'] = [
            'product_type' => [
                ProductType::CAMPAIGN,
                ProductType::CAMPAIGN_EXPRESS,
                ProductType::CAMPAIGN_TEMPLATE,
            ]
        ];
        if(data_get($filters, 'id')){
            $query['bool']['filter'][]['term'] = [
                'id' => $filters['id']
            ];
        }
        if(data_get($filters, 'slug')){
            $query['bool']['should']['function_score']['query']['bool']['should'][]['match_phrase'] = [
                'slug' => [
                    'query' => $filters['slug'],
                    'boost' => 3,
                ]
            ];
            $query['bool']['should']['function_score']['query']['bool']['should'][]['match'] = [
                'slug' => [
                    'query' => $filters['slug'],
                ]
            ];
        }
        $body = [
            'query' => $query,
            'size' => 1,
            '_source' => [
                'includes' => $arrListing
            ],
        ];

        return $this->elasticGetCampaignById($body, searchAllIndex: true);
    }

    /**
     * @param array $arrListing
     * @param array $filterCondition
     * @param int $limit
     * @return array
     * @throws Throwable
     */
    public function getProductsByCustomConditions(
        array $arrListing,
        array $filterCondition = [],
        int $limit = 10
    ): array {
        $query = [];

        // filtering
        $query['bool']['filter'][]['terms'] = $filterCondition;
        $body = [
            'query'   => $query,
            '_source' => [
                'includes' => $arrListing
            ],
            'size'    => $limit,
        ];
        return $this->elasticGetProductsByCustomConditions($body);
    }

    /**
     * @param array $arrListing
     * @param int $campId
     * @param $debug
     * @return array
     * @throws Throwable
     */
    public function getProductById(
        array $arrListing,
        int $campId,
        $debug = false
    ): array {
        $query = [];

        // filtering
        $query['bool']['filter'][]['terms'] = [
            'product_type' => [
                ProductType::PRODUCT,
                ProductType::FULFILL_PRODUCT,
            ]
        ];
        $query['bool']['filter'][]['term'] = [
            'id' => $campId
        ];
        $body = [
            'query'   => $query,
            'size'    => 1,
            '_source' => [
                'includes' => $arrListing
            ],
        ];
        if ($debug) {
            dd($body);
        }

        return $this->elasticGetCampaignById($body);
    }

    /**
     * @param array $filters
     * @param int $limit
     * @param int $currentPage
     * @return array
     * @throws Throwable
     */
    public function getListStockStatus(
        array $filters = [],
        int   $limit = 15,
        int   $currentPage = 1,
    ): array
    {
        $query = [];
        if (data_get($filters, 'product_id')) {
            $query['bool']['filter'][]['term'] = [
                'product_id' => $filters['product_id']
            ];
            if (data_get($filters, 'color')) {
                $query['bool']['filter'][]['term'] = [
                    'color.keyword' => $filters['color']
                ];
            }
            if (data_get($filters, 'size')) {
                $query['bool']['filter'][]['term'] = [
                    'size.keyword' => $filters['size']
                ];
            }
            if (data_get($filters, 'box')) {
                $query['bool']['filter'][]['term'] = [
                    'box.keyword' => $filters['box']
                ];
            }
            if (data_get($filters, 'pack')) {
                $query['bool']['filter'][]['term'] = [
                    'pack.keyword' => $filters['pack']
                ];
            }
            if (data_get($filters, 'print_size')) {
                $query['bool']['filter'][]['term'] = [
                    'print_size.keyword' => $filters['print_size']
                ];
            }
            if (data_get($filters, 'ring_size')) {
                $query['bool']['filter'][]['term'] = [
                    'ring_size.keyword' => $filters['ring_size']
                ];
            }
        }

        if (data_get($filters, 'in_stock', 1)) {
            $query['bool']['should'][]['match'] = [
                'ww' => false
            ];
            $query['bool']['should'][]['match'] = [
                'eu' => false
            ];
            $query['bool']['should'][]['match'] = [
                'us' => false
            ];
            $query['bool']['should'][]['match'] = [
                'ca' => false
            ];
            $query['bool']['minimum_should_match'] = 1;
        } else {
            $query['bool']['filter'][]['term'] = [
                'ww' => true
            ];
            $query['bool']['filter'][]['term'] = [
                'eu' => true
            ];
            $query['bool']['filter'][]['term'] = [
                'us' => true
            ];
            $query['bool']['filter'][]['term'] = [
                'ca' => true
            ];
        }

        $body = [
            'query' => $query,
            'size' => $limit,
            'from' => $limit * ($currentPage - 1),
            'sort' => [
                'product_id' => 'desc',
            ]
        ];
        return $this->elasticGetProductAndTotal($query, $body, true, index: 'stock_status');
    }

    /**
     * @param $filtersFeed
     * @return array
     */
    private function getAllDefaultProductIds($filtersFeed)
    {
        try {
            $ids = collect();
            $lastProductId = null;
            while (true) {
                $products = $this->getProductFeed($filtersFeed, false, ['id', 'default_product_id', 'express_product_id', 'system_product_type', 'system_type', 'campaign_id'], $lastProductId);
                if (empty($products)) {
                    break;
                }
                foreach ($products as $product) {
                    $isExpressProduct = data_get($product, 'system_product_type') === ProductType::PRODUCT_TEMPLATE;
                    $lastProductId = $isExpressProduct ? $product['express_product_id'] : $product['id'];
                    if (!empty($product['system_type']) && $product['system_type'] === ProductSystemTypeEnum::MOCKUP) {
                        $campaign = Campaign::query()->whereKey($product['campaign_id'])->first();
                        $lastProductId = $product['campaign_id'];
                        if ($campaign) {
                            $lastProductId = $product['campaign_id'] . '-' . $campaign->default_product_id;
                        }
                    }
                }
                $ids = $ids->merge(array_column($products, 'default_product_id'));
            }
            return $ids->unique()->toArray();
        } catch (Throwable $e) {
            logException($e);
            return [];
        }
    }

    private function getIndexProduct($sellerId){
        $seller = UserService::getSellerSharding($sellerId);
        return $seller?->getElasticSearchIndex() ?? get_env('ELATICSEARCH_INDEX', 'products');
    }
}

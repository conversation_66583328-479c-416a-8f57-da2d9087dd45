<?php

namespace App\Models;

use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\ProductIndexConfigurator;
use App\Services\UserService;
use App\Traits\ElasticClient;
use App\Traits\HasRelationShipsCustom;
use App\Traits\InactiveTrait;
use App\Traits\SoftDeletes;
use App\Traits\SPModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use App\Helpers\CustomQueryBuilder;
/**
 * App\Models\Product
 *
 * @property int $id
 * @property string|null $slug
 * @property int|null $campaign_id
 * @property int|null $template_campaign_id
 * @property int|null $supplier_id
 * @property int|null $seller_id
 * @property int|null $auth_id
 * @property int|null $company_id
 * @property int|null $template_id
 * @property string|null $name
 * @property string|null $name_ts
 * @property string|null $thumb_url
 * @property string|null $options
 * @property string $mockup_type mockup type
 * @property string|null $default_option
 * @property string|null $print_spaces
 * @property string $currency_code
 * @property string|null $market_location
 * @property float $base_cost
 * @property array|null $base_costs
 * @property float $price
 * @property float $old_price
 * @property float|null $combo_price Giá combo của campaign (chỉ áp dụng cho product_type=campaign)
 * @property float $shipping_cost
 * @property string $status
 * @property string $product_type
 * @property string|null $description
 * @property string|null $description_ts
 * @property float $extra_print_cost Extra print cost
 * @property \Illuminate\Support\Carbon|null $start_time
 * @property \Illuminate\Support\Carbon|null $end_time
 * @property int $show_countdown
 * @property int|null $default_product_id
 * @property int|null $default_mockup_id
 * @property string|null $tracking_code
 * @property string $tm_status
 * @property int|null $sync_status Sync campaign info to elastic-search
 * @property string|null $elastic_document_id
 * @property string|null $sku
 * @property float $score
 * @property float $time_score
 * @property int $sales_score
 * @property int|null $priority
 * @property string $pricing_mode
 * @property string $public_status
 * @property string|null $google_category_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $is_deleted
 * @property string|null $attributes
 * @property float $pre_discounted_price
 * @property string $render_mode Campaign render mode
 * @property int $personalized
 * @property int $full_printed
 * @property int $allow_bulk 1:allow_bulk
 * @property int $temp_status
 * @property string|null $visited_at
 * @property int $google_crawled_status
 * @property string $system_type
 * @property int $archived
 * @property int|null $quantity
 * @property-read \App\Models\Campaign|null $campaign
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Category> $categories
 * @property-read int|null $categories_count
 * @property-read \App\Models\ProductCategory|null $category
 * @property-read \App\Models\Category[]|null $product_categories
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Collection[] $collections
 * @property-read int|null $collections_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\File[] $designs
 * @property-read int|null $designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $files
 * @property-read int|null $files_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $images
 * @property-read int|null $images_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $mockups
 * @property-read int|null $mockups_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Order> $order
 * @property-read int|null $order_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductCategory> $product_category
 * @property-read int|null $product_category_count
 * @property-read \App\Models\ProductPoint|null $product_points
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Product> $products
 * @property-read int|null $products_count
 * @property-read \App\Models\User|null $sellers
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ShippingRule> $shipping_rules
 * @property-read int|null $shipping_rules_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\StoreProduct> $store_campaign
 * @property-read int|null $store_campaign_count
 * @property-read \App\Models\Supplier|null $supplier
 * @property-read \App\Models\Template|null $template
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductVariant> $templateVariants
 * @property-read int|null $template_variants_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $template_mockups
 * @property-read int|null $template_mockups_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductVariant> $variants
 * @property-read int|null $variants_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $videos
 * @property-read int|null $videos_count
 * @method static \Database\Factories\ProductFactory factory($count = null, $state = [])
 * @method static Builder|Product filterActiveProduct()
 * @method static Builder|Product filterByProductOrCampaignIds($arrIds)
 * @method static Builder|Product inactiveCampaignByCondition1($sellerId, $sixMonthAgoDateTime)
 * @method static Builder|Product isTemplate()
 * @method static Builder|Product makeHiddenAll()
 * @method static Builder|Product newModelQuery()
 * @method static Builder|Product newQuery()
 * @method static Builder|Product onlyTrashed()
 * @method static Builder|Product productBySku($sku)
 * @method static Builder|Product query()
 * @method static Builder|Product selectForListing()
 * @method static Builder|Product whereAllowBulk($value)
 * @method static Builder|Product whereArchived($value)
 * @method static Builder|Product whereAttributes($value)
 * @method static Builder|Product whereAuthId($value)
 * @method static Builder|Product whereBaseCost($value)
 * @method static Builder|Product whereBaseCosts($value)
 * @method static Builder|Product whereCampaignId($value)
 * @method static Builder|Product whereComboPrice($value)
 * @method static Builder|Product whereCompanyId($value)
 * @method static Builder|Product whereCreatedAt($value)
 * @method static Builder|Product whereCurrencyCode($value)
 * @method static Builder|Product whereDefaultMockupId($value)
 * @method static Builder|Product whereDefaultOption($value)
 * @method static Builder|Product whereDefaultProductId($value)
 * @method static Builder|Product whereDeletedAt($value)
 * @method static Builder|Product whereDescription($value)
 * @method static Builder|Product whereDescriptionTs($value)
 * @method static Builder|Product whereElasticDocumentId($value)
 * @method static Builder|Product whereEndTime($value)
 * @method static Builder|Product whereExtraPrintCost($value)
 * @method static Builder|Product whereFullPrinted($value)
 * @method static Builder|Product whereGoogleCategoryId($value)
 * @method static Builder|Product whereGoogleCrawledStatus($value)
 * @method static Builder|Product whereId($value)
 * @method static Builder|Product whereIsDeleted($value)
 * @method static Builder|Product whereMarketLocation($value)
 * @method static Builder|Product whereMockupType($value)
 * @method static Builder|Product whereName($value)
 * @method static Builder|Product whereNameTs($value)
 * @method static Builder|Product whereOldPrice($value)
 * @method static Builder|Product whereOptions($value)
 * @method static Builder|Product wherePersonalized($value)
 * @method static Builder|Product wherePreDiscountedPrice($value)
 * @method static Builder|Product wherePrice($value)
 * @method static Builder|Product wherePricingMode($value)
 * @method static Builder|Product wherePrintSpaces($value)
 * @method static Builder|Product wherePriority($value)
 * @method static Builder|Product whereProductType($value)
 * @method static Builder|Product wherePublicStatus($value)
 * @method static Builder|Product whereQuantity($value)
 * @method static Builder|Product whereRenderMode($value)
 * @method static Builder|Product whereSalesScore($value)
 * @method static Builder|Product whereScore($value)
 * @method static Builder|Product whereSellerId($value)
 * @method static Builder|Product whereShippingCost($value)
 * @method static Builder|Product whereShowCountdown($value)
 * @method static Builder|Product whereSku($value)
 * @method static Builder|Product whereSlug($value)
 * @method static Builder|Product whereStartTime($value)
 * @method static Builder|Product whereStatus($value)
 * @method static Builder|Product whereSupplierId($value)
 * @method static Builder|Product whereSyncStatus($value)
 * @method static Builder|Product whereSystemType($value)
 * @method static Builder|Product whereTempStatus($value)
 * @method static Builder|Product whereTemplateCampaignId($value)
 * @method static Builder|Product whereTemplateId($value)
 * @method static Builder|Product whereThumbUrl($value)
 * @method static Builder|Product whereTimeScore($value)
 * @method static Builder|Product whereTmStatus($value)
 * @method static Builder|Product whereTrackingCode($value)
 * @method static Builder|Product whereUpdatedAt($value)
 * @method static Builder|Product whereVisitedAt($value)
 * @method static Builder|Product withTrashed()
 * @method static Builder|Model withWhereHas(string $relation, \Closure $condition)
 * @method static Builder|Product withoutTrashed()
 * @mixin \Eloquent
 */
class Product extends BaseProduct
{
    use HasFactory;
    use ElasticClient;
    use SPModel;
    use SoftDeletes;
    use InactiveTrait;
    use HasRelationShipsCustom;

    public const SYNC_DATA_STATS_ENABLED = 0;
    public const SYNC_DATA_STATS_DISABLED = 1;
    public const SYNC_DATA_TO_SINGLE_STORE = 2;
    public const ARR_LISTING = [
        'id',
        'campaign_id',
        'campaign_name',
        'supplier_id',
        'seller_id',
        'template_id',
        'name',
        'name_ts',
        'thumb_url',
        'options',
        'default_option',
        'price',
        'old_price',
        'combo_price',
        'product_type',
        'system_type',
        'slug',
        'description',
        'description_ts',
        'start_time',
        'end_time',
        'show_countdown',
        'default_product_id',
        'tracking_code',
        'sku',
        'currency_code',
        'personalized',
        'full_printed',
        'market_location',
        'google_category_id',
        'quantity',
        'tm_status',
        'express_product_id',
        'attributes',
        'pricing_mode',
        'collection_ids'
    ];
    public const ARR_LISTING_ELASTIC = [
        'system_product_type',
    ];

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'product';
    protected $fillable = [
        'name',
        'description',
        'sku',
        'options',
        'slug',
        'template_id',
        'campaign_id',
        'template_campaign_id',
        'seller_id',
        'supplier_id',
        'product_type',
        'system_type',
        'thumb_url',
        'print_spaces',
        'price',
        'old_price',
        'combo_price',
        'status',
        'sync_status',
        'elastic_document_id',
        'extra_print_cost',
        'status',
        'render_type',
        'color_fillable',
        'currency_code',
        'default_option',
        'attributes',
        'mockup_type',
        'personalized',
        'full_printed',
        'pricing_mode',
        'priority',
        'market_location',
        'default_mockup_id',
        'quantity',
        'barcode',
        'fulfill_fba_by',
        'is_deleted',
        'deleted_at',
        'temp_status',
    ];


    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = ['laravel_through_key'];

    protected $indexConfigurator = ProductIndexConfigurator::class;

    protected $casts = [
        'base_costs' => 'array',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];
    protected $batch = false;

    /**
     * @param array $attributes
     * @param bool $batch
     */
    public function __construct(array $attributes = [], $batch = false)
    {
        $this->batch = $batch;
        parent::__construct($attributes);
    }

    /**
     * Lấy danh sách sản phẩm campaign
     *
     * @param     array     $productIDs
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function typeProducts(array $productIDs): \Illuminate\Support\Collection
    {
        $user = currentUser();
        $seller = User::query()->find($user->getUserId());
        return static::query()
            ->onSellerConnection($seller)
            ->whereIn('id', $productIDs)
            ->where('product_type', ProductType::PRODUCT)
            ->get();
    }

    /**
     * Lấy danh sách sản phẩm template
     *
     * @param     array     $IDs
     *
     * @return \Illuminate\Support\Collection
     */
    public static function typeTemplates(array $IDs): \Illuminate\Support\Collection
    {
        return static::query()
            ->whereIn('id', $IDs)
            ->where('product_type', ProductType::TEMPLATE)
            ->get();
    }

    /**
     * Vùng in đắt nhất sẽ được tính vào giá sản phẩm, các vùng in còn lại sẽ được tính vào giá in thêm
     *
     * @param Collection $designs
     * @param bool $mapPriority
     * @return float
     * @throws \JsonException
     */
    public function calcExtraPrintCost(\Illuminate\Support\Collection $designs, bool $mapPriority = true): float
    {
        $designs = self::correctDesigns($designs, $mapPriority);

        // Chỉ tính phí in thêm từ vùng in thứ 2 trở đi
        if ($designs->count() <= 1) {
            return .0;
        }

        // Nếu có giá extra được điền trước đó thì vẫn sư dụng giá đó
        if ($this->extra_print_cost) {
            return $this->extra_print_cost;
        }

        $printSpaces = collect(
            json_decode($this->print_spaces, true, 512, JSON_THROW_ON_ERROR) ?: []
        )->keyBy('name');

        return $designs->reduce(static fn($res, $design, $key): \Illuminate\Support\Collection => $res->push([
                'print_space' => $key,
                'price' => data_get($printSpaces, "$key.price")
            ]), collect())
            ->sortByDesc('price')
            ->splice(1)
            ->reduce(fn($cost, $design) => $cost + $design['price'], .0);
    }

    /**
     * Confirmed by @thangnm on 2024-04-17
     * Mỗi vùng in chỉ có duy nhất 1 design, trong trường hợp nhiều design cho 1 vùng in thì:
     * - Nếu có design option là CUSTOM thì lấy design đó
     * - Nếu có design option là PB thì lấy design đó
     * - Nếu không có design option là CUSTOM, PB thì lấy design mới nhất
     *
     * @param     \Illuminate\Support\Collection|array     $designs
     *
     * @return \Illuminate\Support\Collection
     */
    public static function correctDesigns(\Illuminate\Support\Collection|array $designs, $mapPriority = true): \Illuminate\Support\Collection
    {
        return collect($designs)
            ->sortByDesc('id')
            ->groupBy('print_space')
            ->when($mapPriority, function ($designs) {
                return $designs->map(function (\Illuminate\Support\Collection $designs) {
                    $_designs = $designs->keyBy('option');
                    return $_designs[FileRenderType::CUSTOM] ?? $_designs[FileRenderType::PB] ?? $designs->first();
                });
            });
    }

    /**
     * @return string
     */
    public function getTable()
    {
        if ($this->batch) {
            return $this->table;
        }
        return parent::getTable();
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'campaign_id', 'id');
    }

    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'campaign_id');
    }

    public function collections(): BelongsToMany
    {
        return $this->belongsToMany(
            Collection::class,
            'product_collection',
            'product_id',
            'collection_id'
        );
    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'id');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class, 'template_id', 'id');
    }

    public function templateVariants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'template_id');
    }

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'supplier_id', 'id');
    }

    public function sellers(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function mockups(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'id')
            ->where('type', FileTypeEnum::MOCKUP)
            ->orderBy('position');
    }

    public function videos(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'id')
            ->where('type', FileTypeEnum::VIDEO)
            ->whereNotNull('option')
            ->orderBy('position');
    }

    public function cloud_mockups(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'id')
            ->where([
                'type' => FileTypeEnum::MOCKUP,
                'render_type' => FileRenderType::RENDER_2D,
            ])
            ->orderBy('position');
    }

    public function designs(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'id')
            ->where([
                'type' => FileTypeEnum::DESIGN
            ])->whereIn('option', [
                FileRenderType::CUSTOM,
                FileRenderType::PRINT,
                FileRenderType::PB
            ]);
    }

    public function files(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'id');
    }

    public function shipping_rules(): HasMany
    {
        return $this->hasMany(ShippingRule::class, 'product_id', 'id')
            ->select([
                'product_id',
                'shipping_method',
                'location_code',
                'extra_cost',
                'shipping_cost'
            ]);
    }

    public function category(): HasOne
    {
        return $this->hasOne(ProductCategory::class, 'product_id', 'id')
            ->leftJoin('category', 'category.id', '=', 'product_category.category_id')
            ->orderBy('product_category.category_id', 'desc')
            ->limit(1);
    }

    public function product_categories()
    {
        return $this->hasManyThrough(Category::class, ProductCategory::class, 'product_id', 'id', 'id', 'category_id')
            ->select([
                'category.id',
                'product_category.product_id',
                'product_category.category_id',
                'category.show_dashboard',
                'category.parent_id',
                'category.popularity',
                'category.full_name',
                'category.slug',
                'category.name'
            ])
            ->orderBy('product_category.category_id', 'desc');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function category_record(): HasOne
    {
        return $this->hasOne(ProductCategory::class, 'product_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(
            Category::class,
            'product_category',
            'product_id',
            'category_id'
        );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasManyThrough
     */
    public function category_names()
    {
        return $this->hasManyThrough(
            Category::class,
            ProductCategory::class,
            'product_id',
            'id',
            'template_id',
            'category_id'
        )->select(['name', 'id']);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function product_category(): HasMany
    {
        return $this->hasMany(ProductCategory::class, 'product_id');
    }

    /**
     * Get list product or campaign for sync to elasticsearch
     * @param array $relations
     * @return \Illuminate\Database\Query\Builder|Product
     */
    public static function getProductForSyncElastic(array $relations = [])
    {
        $query = self::query()->withTrashed()->select('*');
        if (!empty($relations)) {
            $query->with($relations);
        }

        return $query;
    }

    public static function updateSyncStatusByIds(array $allIds = [], int $syncStatus = 0, array $otherField = []): bool
    {
        $otherField['sync_status'] = $syncStatus;
        try {
            foreach($allIds as $sellerId => $productIds) {
                $seller = UserService::getSellerSharding($sellerId);
                $productIds = array_unique($productIds);
                $sellerOnSyncing = $seller && $seller->hasPrivateConnection() && !$seller->shardingCompleted();
                foreach (array_chunk($productIds, 1000) as $ids) {
                    self::query()
                        ->withTrashed()
                        ->onSellerConnection($seller, shardingCompleted: false)
                        ->whereIn('id', $ids)
                        ->update($otherField);
                    if ($sellerOnSyncing) {
                        self::query()
                            ->withTrashed()
                            ->onSellerConnection($seller) // mysql or mysql_sub only
                            ->whereIn('id', $ids)
                            ->update(Arr::except($otherField, ['temp_status']));
                    }
                }
            }
            return true;
        } catch (\Throwable $e) {
            logException($e);
            return false;
        }
    }

    public function store_campaign(): HasMany
    {
        // return parent campaign stores if the product belong to a campaign
        if ($this->campaign_id) {
            return $this->hasMany(StoreProduct::class, 'product_id', 'campaign_id');
        }

        return $this->hasMany(StoreProduct::class, 'product_id', 'id');
    }

    public function images()
    {
        return $this->hasMany(File::class, 'product_id', 'id');
    }

    /**
     * @param null $id
     * @param int $limit
     * @param bool $wait
     * @return int
     * @throws \Throwable
     */
    public function syncProductToElastic($id = null, int $limit = 1000, bool $wait = false): int
    {
        return (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($id, $limit, $wait);
    }

    public static function getArrListingElastic(): array
    {
        return array_merge(self::ARR_LISTING, self::ARR_LISTING_ELASTIC);
    }

    public function scopeFilterActiveProduct($query)
    {
        return $query->where('status', ProductStatus::ACTIVE)
            ->whereIn('product_type', [ProductType::PRODUCT, ProductType::PRODUCT_TEMPLATE]);
    }

    public function scopeFilterByProductOrCampaignIds($query, $arrIds)
    {
        if (!is_array($arrIds)) {
            $arrIds = [$arrIds];
        }
        return $query->where(function ($q) use ($arrIds) {
            return $q->whereIn('id', $arrIds)->orWhereIn('campaign_id', $arrIds);
        });
    }

    public function scopeSelectForListing($query)
    {
        return $query->select(self::ARR_LISTING)
            ->with('campaign:id,name,slug');
    }

    public function scopeIsTemplate($query)
    {
        return $query->where('product_type', ProductType::TEMPLATE);
    }

    public static function setPublicStatus($campaignIds, $status, $seller = null): int
    {
        return self::query()
            ->onSellerConnection($seller)
            ->whereIn('id', $campaignIds) // campaign
            ->orWhereIn('campaign_id', $campaignIds) // all products of campaign
            ->update(['public_status' => $status]);
    }

    public function product_points(): HasOne
    {
        return $this->hasOne(ProductPoint::class, 'product_id', 'id');
    }

    /**
     * @param $query
     * @param $sku
     * @return mixed
     */
    public function scopeProductBySku($query, $sku)
    {
        return $query->where('sku', $sku)->where('status', ProductStatus::ACTIVE);
    }

    /**
     * Product template mockups
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function template_mockups(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'template_id')
            ->where('type', FileTypeEnum::MOCKUP)
            ->where('render_type', FileRenderType::RENDER_3D)
            ->where('status', FileStatusEnum::ACTIVE)
            ->orderBy('position');
    }

    /**
     * @param     int     $id
     *
     * @return bool|mixed|null
     */
    public static function deleteByCampaignId(int $id): mixed
    {
        return self::query()
            ->where('campaign_id', $id)
            ->delete();
    }

    /**
     * @throws \Throwable
     */
    public function printSpacesKeyByName(): \Illuminate\Support\Collection
    {
        $printSpaces = json_decode(
            $this->print_spaces, false, 512, JSON_THROW_ON_ERROR
        );

        return collect($printSpaces)->keyBy('name');
    }

    /**
     * @param int $id
     * @param User $seller
     * @param array $score
     *
     * @return bool
     */
    public static function updateCampaignScore(int $id, User $seller, array $score): bool
    {
        return self::query()
            ->onSellerConnection($seller)
            ->where([
                'seller_id' => $seller->id,
                'status' => ProductStatus::DRAFT,
            ])
            ->where(fn ($query) => $query->where('id', $id)->orWhere('campaign_id', $id))
            ->update([
                'status' => ProductStatus::ACTIVE,
                'time_score' => $score['timestamp'],
                'score' => $score['score'],
            ]);
    }

    /**
     * @param int $id
     * @param User $seller
     * @param $score
     *
     * @return bool
     */
    public static function updateScore(int $id, User $seller, $score): bool
    {
        return self::query()
            ->onSellerConnection($seller, shardingCompleted: false)
            ->where('id', $id)
            ->update(['score' => $score + 1]);
    }

    /**
     * @return BelongsToMany
     */
    public function order(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'order_product', 'template_id', 'order_id', 'id', 'id');
    }

    /**
     * Get designs specific to this product in combo
     * @return HasMany
     */
    public function comboDesigns(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'id')
            ->where('campaign_id', $this->campaign_id);
    }
}

<?php

namespace App\Models;

use App\Enums\PhoneNumberStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\PhoneNumbers
 *
 * @property string $phone_number
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|PhoneNumbers newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PhoneNumbers newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PhoneNumbers query()
 * @method static \Illuminate\Database\Eloquent\Builder|PhoneNumbers whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PhoneNumbers wherePhoneNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PhoneNumbers whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PhoneNumbers whereUpdatedAt($value)
 */
class PhoneNumbers extends Model
{
    use HasFactory;
    protected $table = 'phone_numbers';
    protected $fillable = ['phone_number', 'status', 'created_at', 'updated_at'];
    protected $primaryKey = 'phone_number';
    public $incrementing = false;
    public $keyType = 'string';

    /**
     * @param $phoneNumber
     * @return bool
     */
    public static function isExists($phoneNumber): bool
    {
        return self::query()->where('phone_number', $phoneNumber)->exists();
    }

    /**
     * @param $phoneNumber
     * @return bool
     */
    public static function isValidPhoneNumber($phoneNumber): bool
    {
        return self::query()->where('phone_number', $phoneNumber)->whereIn('status', [PhoneNumberStatusEnum::VALID, PhoneNumberStatusEnum::PENDING])->exists();
    }

    /**
     * @param $phoneNumber
     * @return bool
     */
    public static function canSend($phoneNumber): bool
    {
        $phone = self::query()->where('phone_number', $phoneNumber)->first();
        return !$phone || in_array($phone->status, [PhoneNumberStatusEnum::VALID, PhoneNumberStatusEnum::PENDING], true);
    }
}

<?php

namespace App\Models;

use App\Enums\CacheKeys;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Helpers\CustomQueryBuilder;
use App\Traits\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

/**
 * App\Models\Template
 *
 * @property int $id
 * @property string|null $slug
 * @property int|null $campaign_id
 * @property int|null $supplier_id
 * @property int|null $seller_id
 * @property int|null $auth_id
 * @property int|null $company_id
 * @property int|null $template_id
 * @property string|null $name
 * @property string|null $name_ts
 * @property string|null $thumb_url
 * @property string|null $options
 * @property string $mockup_type mockup type
 * @property string|null $default_option
 * @property string|null $print_spaces
 * @property string $currency_code
 * @property string|null $market_location
 * @property float $base_cost
 * @property string|null $base_costs
 * @property float $price
 * @property float $old_price
 * @property float $shipping_cost
 * @property string $status
 * @property string $product_type
 * @property string|null $description
 * @property string|null $description_ts
 * @property float $extra_print_cost Extra print cost
 * @property string|null $start_time
 * @property string|null $end_time
 * @property int $show_countdown
 * @property int|null $default_product_id
 * @property string|null $tracking_code
 * @property string $tm_status
 * @property int|null $sync_status Sync campaign info to elastic-search
 * @property string|null $elastic_document_id
 * @property string|null $sku
 * @property float $score
 * @property float $time_score
 * @property int $sales_score
 * @property int|null $priority
 * @property string $pricing_mode
 * @property string $public_status
 * @property string|null $google_category_id
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int $is_deleted
 * @property string|null $attributes
 * @property float $pre_discounted_price
 * @property string $render_mode Campaign render mode
 * @property int $personalized
 * @property int $full_printed
 * @property int $allow_bulk 1:allow_bulk
 * @property int $temp_status
 * @property string|null $visited_at
 * @property int $google_crawled_status
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Pricing[] $pricings
 * @property-read int|null $pricings_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Product[] $products
 * @property-read int|null $products_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\ProductVariant[] $variants
 * @property-read int|null $variants_count
 * @method static \Illuminate\Database\Eloquent\Builder|Template newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Template newQuery()
 * @method static \Illuminate\Database\Query\Builder|Template onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Template query()
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereAllowBulk($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereAuthId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereBaseCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereBaseCosts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereCampaignId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereCompanyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereCurrencyCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereDefaultOption($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereDefaultProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereDescriptionTs($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereElasticDocumentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereExtraPrintCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereFullPrinted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereGoogleCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereGoogleCrawledStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereIsDeleted($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereMarketLocation($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereMockupType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereNameTs($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereOldPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template wherePersonalized($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template wherePreDiscountedPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template wherePricingMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template wherePrintSpaces($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereProductType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template wherePublicStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereRenderMode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereSalesScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereShippingCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereShowCountdown($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereSupplierId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereSyncStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereTempStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereTemplateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereThumbUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereTimeScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereTmStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereTrackingCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Template whereVisitedAt($value)
 * @method static \Illuminate\Database\Query\Builder|Template withTrashed()
 * @method static \Illuminate\Database\Query\Builder|Template withoutTrashed()
 * @mixin \Eloquent
 */
class Template extends BaseProduct
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'product';
    protected $connection = 'mysql';
    protected $guarded = [];

    /**
     * @param array $attributes
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    protected static function booted()
    {
        static::addGlobalScope('campaign_type', static function ($query) {
            $query->where('product_type', ProductType::TEMPLATE);
        });
    }

    public function newEloquentBuilder($query)
    {
        return new CustomQueryBuilder($query);
    }

    public static function findAndCacheByKey($templateId, $mustActive = true): ?object
    {
        return Arr::first(self::getAndCache(),
            static function ($each) use ($mustActive, $templateId) {
                if (
                    $mustActive
                    && $each->status !== ProductStatus::ACTIVE
                ) {
                    return false;
                }

                /** @noinspection TypeUnsafeComparisonInspection */
                return $each->id == $templateId;
            });
    }

    public static function findAndCacheBySku($sku): ?object
    {
        return self::getAndCache()->first(
            static function ($each) use ($sku) {
                return $each->status === ProductStatus::ACTIVE
                    && $each->sku === $sku;
            });
    }

    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'template_id');
    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'product_id');
    }

    public function pricings(): HasMany
    {
        return $this->hasMany(Pricing::class, 'product_id');
    }

    public function mockups(): HasMany
    {
        return $this->hasMany(File::class, 'product_id', 'id')
            ->where('type', FileTypeEnum::MOCKUP)
            ->orderBy('position');
    }

    public function shipping_rules(): HasMany
    {
        return $this->hasMany(ShippingRule::class, 'product_id', 'id');
    }

    static private ?Collection $cache = null;
    public static function getAndCache(): Collection
    {
        if (isset(self::$cache)) {
            return self::$cache;
        }

        $tag = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;
        $data = cacheGet(
            CacheKeys::LIST_PRODUCT_TEMPLATES,
            CacheKeys::CACHE_30D,
            static function () {
                return self::query()
                    ->whereIn('status', [
                        ProductStatus::ACTIVE,
                        ProductStatus::ARCHIVED,
                    ])
                    ->get()
                    ->toJson();
            }, [$tag], CacheKeys::CACHE_TYPE_ALTERNATIVE
        );

        if (is_string($data)) {
            $arr = json_decode($data, true);
            $data = collect($arr)->map(function ($each){
                return self::make($each);
            });
        }
        self::$cache = $data;

        return $data;
    }

    static private $cacheByCategory = null;
    /**
     * @param $category_id
     * @return array
     */
    public static function getAndCacheProductIdByCategory($category_id)
    {
        if (empty($category_id)) {
            return [];
        }
        if (isset(self::$cacheByCategory)) {
            return self::$cacheByCategory;
        }
        $data = cacheGet(
            CacheKeys::PRODUCT_ID_BY_CATEGORY . $category_id,
            CacheKeys::CACHE_30D,
            static function () use ($category_id) {
                return self::query()
                    ->select('product.id')
                    ->leftJoin('product_category', 'product_category.product_id', '=', 'product.id')
                    ->where('product.product_type', ProductType::TEMPLATE)
                    ->whereIn('product.status', [
                        ProductStatus::ACTIVE,
                        ProductStatus::ARCHIVED,
                    ])
                    ->where('product_category.category_id', $category_id)
                    ->where('product.base_cost', '>', 0)
                    ->where('product.is_deleted', '=', 0)
                    ->get()
                    ->pluck('id')
                    ->toJson();
            }
        );

        if (is_string($data)) {
            $data = json_decode($data, true);
        }
        self::$cacheByCategory = $data;
        return $data;
    }

    /**
     * Vùng in đắt nhất sẽ được tính vào giá sản phẩm, các vùng in còn lại sẽ được tính vào giá in thêm
     *
     * @param     \Illuminate\Support\Collection     $designs
     *
     * @return float
     * @throws Throwable
     */
    public function calcExtraPrintCost(\Illuminate\Support\Collection $designs): float
    {
        $designs = self::correctDesigns($designs);

        // Chỉ tính phí in thêm từ vùng in thứ 2 trở đi
        if ($designs->count() <= 1) {
            return .0;
        }

        // Nếu có giá extra được điền trước đó thì vẫn sư dụng giá đó
        if ($this->extra_print_cost) {
            return $this->extra_print_cost;
        }

        $printSpaces = collect(
            json_decode($this->print_spaces, true, 512, JSON_THROW_ON_ERROR) ?: []
        )->keyBy('name');

        return $designs->reduce(static fn($res, $design, $key): \Illuminate\Support\Collection => $res->push([
            'print_space' => $key,
            'price' => data_get($printSpaces, "$key.price")
        ]), collect())?->sortByDesc('price')->splice(1)->reduce(fn($cost, $design) => $cost + $design['price'], .0);
    }

    /**
     * Confirmed by @thangnm on 2024-04-17
     * Mỗi vùng in chỉ có duy nhất 1 design, trong trường hợp nhiều design cho 1 vùng in thì:
     * - Nếu có design option là CUSTOM thì lấy design đó
     * - Nếu có design option là PB thì lấy design đó
     * - Nếu không có design option là CUSTOM, PB thì lấy design mới nhất
     *
     * @param     \Illuminate\Support\Collection|array     $designs
     *
     * @return \Illuminate\Support\Collection
     */
    public static function correctDesigns(\Illuminate\Support\Collection|array $designs): \Illuminate\Support\Collection
    {
        return collect($designs)
            ->sortByDesc('id')
            ->groupBy('print_space')
            ->map(function (\Illuminate\Support\Collection $designs) {
                $_designs = $designs->keyBy('option');

                return $_designs[FileRenderType::CUSTOM] ?? $_designs[FileRenderType::PB] ?? $designs->first();
            });
    }
}

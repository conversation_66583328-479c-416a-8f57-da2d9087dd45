<?php

namespace App\Models;

use App\Enums\EnvironmentEnum;
use App\Enums\PaymentAccountStatus;
use App\Enums\PaymentAccountTypeEnum;
use App\Enums\SellerBillingStatus;
use App\Library\Paypal\PaypalClient;
use App\Library\PingPongX\PingPongX;
use App\Traits\ScopeFilterDateRangeTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Hash;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;

/**
 * App\Models\SellerBilling
 *
 * @property int $id
 * @property int $seller_id
 * @property float $amount
 * @property int|null $payment_account_id
 * @property int|null $order_id
 * @property string $detail
 * @property string|null $log
 * @property string|null $comment
 * @property string|null $reason
 * @property float $balance
 * @property string $type 'sale','commission','ref_commission','fulfill','refund','payout','fee','admin','other','topup','ads_topup'
 * @property string|null $transaction_key
 * @property string $signature
 * @property string $status pending, processing, completed, on hold, cancelled
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property int $is_valid
 * @property string $balance_type
 * @property int|null $staff_id
 * @property \App\Models\PaymentAccount $payment_account
 * @property-read \App\Models\User|null $seller
 * @property-read \Illuminate\Database\Eloquent\Collection|SellerBilling[] $transactions
 * @property-read int|null $transactions_count
 * @method static \Database\Factories\SellerBillingFactory factory(...$parameters)
 * @method static Builder|SellerBilling filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static Builder|SellerBilling newModelQuery()
 * @method static Builder|SellerBilling newQuery()
 * @method static Builder|SellerBilling query()
 * @method static Builder|SellerBilling whereAmount($value)
 * @method static Builder|SellerBilling whereBalance($value)
 * @method static Builder|SellerBilling whereBalanceType($value)
 * @method static Builder|SellerBilling whereComment($value)
 * @method static Builder|SellerBilling whereCreatedAt($value)
 * @method static Builder|SellerBilling whereDetail($value)
 * @method static Builder|SellerBilling whereId($value)
 * @method static Builder|SellerBilling whereIsValid($value)
 * @method static Builder|SellerBilling whereLog($value)
 * @method static Builder|SellerBilling whereOrderId($value)
 * @method static Builder|SellerBilling wherePaymentAccountId($value)
 * @method static Builder|SellerBilling whereReason($value)
 * @method static Builder|SellerBilling whereSellerId($value)
 * @method static Builder|SellerBilling whereSignature($value)
 * @method static Builder|SellerBilling whereStaffId($value)
 * @method static Builder|SellerBilling whereStatus($value)
 * @method static Builder|SellerBilling whereTransactionKey($value)
 * @method static Builder|SellerBilling whereType($value)
 * @method static Builder|SellerBilling whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SellerBilling extends Model
{
    use HasFactory;
    use ScopeFilterDateRangeTrait;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'seller_billing';

    protected $fillable = [
        'seller_id',
        'amount',
        'detail',
        'balance',
        'type',
        'transaction_key',
        'order_id',
        'payment_account_id',
        'status',
        'signature',
        'log',
        'comment',
        'balance_type',
        'staff_id'
    ];

    public const FILTER_COLUMN_DATE = 'seller_billing.created_at';

    public function seller(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'seller_id')
            ->select(['id', 'email', 'name', 'status']);
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function payment_account(): HasOne
    {
        return $this->hasOne(PaymentAccount::class, 'id', 'payment_account_id')
            ->withDefault([
                'payment_type' => '',
                'account_name' => '',
                'account_id' => '',
                'additional_info' => ''
            ]);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(__CLASS__, 'seller_id', 'seller_id');
    }

    /**
     * Get billing in processing or cancel
     * @return Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public static function getPOProcessBilling()
    {
        $query = self::query()
            ->where([
                'type' => 'payout',
                'status' => SellerBillingStatus::PROCESSING
            ])
            ->whereHas('payment_account', function ($query) {
                $query->where([
                    'payment_type' => PaymentAccountTypeEnum::PAYONEER,
                    'status' => PaymentAccountStatus::VERIFIED
                ]);
                $query->whereNotNull('additional_info->payoneer_id');
            });

        if (!POMassPayOutQuickTestingEnabled()) {
            $query->where('updated_at', '<=', Carbon::now()->subHour());
        }

        return $query->get();
    }

    public static function updateStatusToCompleted(array $billIds, array $transactionInfo = []): bool
    {
        try {
            foreach ($billIds as $billId) {
                self::query()
                    ->where([
                        'id' => $billId,
                        'status' => SellerBillingStatus::PROCESSING
                    ])
                    ->update([
                        'status' => SellerBillingStatus::COMPLETED,
                        'transaction_key' => $transactionInfo[$billId]['payout_id'] ?? null,
                    ]);
            }
        } catch (\Exception $e) {
            return false;
        }

        return true;
    }

    public static function updateLog($billId, $paymentId): int
    {
        return self::query()
            ->where('id', $billId)
            ->update([
                'log' => "Transaction ID: {$paymentId}"
            ]);
    }

    public static function signature($data): string
    {
        return Hash::make(self::getStringNeedHash($data));
    }

    private static function getStringNeedHash($data): string
    {
        $dataToHash = $data['seller_id'] . $data['balance'] . $data['amount'] . $data['type'];
        if (!empty($data['balance_type']) && $data['balance_type'] !== SellerBalanceTypeEnum::DEFAULT) {
            $dataToHash .= $data['balance_type'];
        }
        return $dataToHash;
    }

    public function validate()
    {
        $hashData = self::getStringNeedHash($this->toArray());
        $this->is_valid = Hash::check($hashData, $this->signature);
        return $this->is_valid;
    }

    public static function getPingPongXProcessBilling()
    {
        $query = self::query()
            ->where([
                'type' => 'payout',
                'status' => SellerBillingStatus::PROCESSING
            ])->with('seller', function ($query) {
                $query->select('id', 'tags');
            })
            ->whereHas('payment_account', function ($query) {
                $query->where([
                    'payment_type' => PaymentAccountTypeEnum::PINGPONG,
                    'status' => PaymentAccountStatus::VERIFIED
                ]);
                $query->whereNotNull('additional_info->biz_id');
            });
        $quickTestEnabled = PingPongX::quickTestEnabled();
        if (!$quickTestEnabled) {
            $query->where('updated_at', '<=', Carbon::now()->subHour());
        }

        return $query->get();
    }

    public static function updateTransactionKeyOfPingPongPayout($billId, string $transactionKey, $updatedAt = null): int
    {
        $updateParam = [
            'transaction_key' => $transactionKey
        ];
        if (!is_null($updatedAt)) {
            $updateParam['updated_at'] = $updatedAt;
        }
        return self::query()
            ->where('id', $billId)
            ->update($updateParam);
    }

    /**
     * Get list bill for payout with method Paypal
     * @return Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public static function getPaypalProcessBilling()
    {
        $query = self::query()
            ->where([
                'type' => 'payout',
                'status' => SellerBillingStatus::PROCESSING
            ])
            ->whereHas('payment_account', function ($query) {
                $query->where([
                    'payment_type' => PaymentAccountTypeEnum::PAYPAL,
                    'status' => PaymentAccountStatus::VERIFIED
                ]);
            });
        $quickTestEnabled = PaypalClient::quickTestEnabled();
        if (!$quickTestEnabled) {
            $query->where('updated_at', '<=', Carbon::now()->subHour());
        }
        return $query->get();
    }

    public static function updatePayoutTransactionKeyPaypal($billId, string $transactionKey, $updatedAt = null): int
    {
        return self::updateTransactionKeyOfPingPongPayout($billId, $transactionKey, $updatedAt);
    }

    public static function updateStatusByManyIdAndStatus(array $ids = [], string $whereStatus = null, string $status = ''): int
    {
        if (empty($status)) {
            return 0;
        }
        $query = self::query()
            ->whereIn('id', $ids);
        if (!is_null($whereStatus)) {
            $query->where('status', $whereStatus);
        }
        return $query->update(['status' => $status]);
    }

    public static function getLianLianProcessBilling(array $fields = [])
    {
        $query = self::query()
            ->where([
                'type' => 'payout',
                'status' => SellerBillingStatus::PROCESSING
            ])
            ->whereHas('payment_account', function ($query) {
                $query->where([
                    'payment_type' => PaymentAccountTypeEnum::LIANLIAN,
                    'status' => PaymentAccountStatus::VERIFIED
                ]);
            });

        if (app()->environment(EnvironmentEnum::PRODUCTION)) {
            $query->where('updated_at', '<=', Carbon::now()->subHour());
        }

        if (empty($fields)) {
            return $query->get();
        }

        return $query->get($fields);
    }
}

<?php

namespace App\Models;

use App\Enums\CacheKeys;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

/**
 * App\Models\UserInfo
 *
 * @property int $id
 * @property string $key
 * @property string|null $value
 * @property int|null $user_id
 * @property User $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo whereKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo whereValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo sellerBulkCampaignActive()
 * @method static \Illuminate\Database\Eloquent\Builder|UserInfo sellerBulkCampaignInActive()
 * @mixin \Illuminate\Database\Eloquent\Builder
 */
class UserInfo extends Model
{
    public const KEY_BULK_CAMPAIGN_STATUS = 'bulk_campaign_status';
    public const VAL_BULK_CAMPAIGN_ACTIVE = 'active';
    public const VAL_BULK_CAMPAIGN_INACTIVE = 'inactive';
    /**
     * table name
     *
     * @var string
     */
    protected $table = 'user_info';
    protected $guarded = [];
    protected $fillable = [
        'key',
        'value',
        'user_id',
    ];
    public $timestamps = false;

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }


    /**
     * @param     int        $sellerId
     * @param     string     $key
     * @param     string     $value
     *
     * @return void
     */
    public function toggle(int $sellerId, string $key, string $value): void
    {
        $exists = self::query()
            ->where('user_id', $sellerId)
            ->where('key', $key)
            ->first();

        if ($exists) {
            $exists->update(['value' => $value]);
        } else {
            self::query()->create([
                'user_id' => $sellerId,
                'key' => $key,
                'value' => $value
            ]);
        }
    }

    /**
     * @param     int     $sellerId
     *
     * @return void
     */
    public function activeBulkCampaign(int $sellerId): void
    {
        self::query()
            ->where('user_id', $sellerId)
            ->where('key', self::KEY_BULK_CAMPAIGN_STATUS)
            ->delete();
    }

    /**
     * @param     int     $sellerId
     *
     * @return void
     */
    public function inactiveBulkCampaign(int $sellerId): void
    {
        $this->toggle($sellerId, self::KEY_BULK_CAMPAIGN_STATUS, self::VAL_BULK_CAMPAIGN_INACTIVE);
    }

    /**
     * @param $sellerId
     * @param $key
     * @param $defaultValue
     * @return string|null
     */
    public static function getSetting($sellerId, $key, $defaultValue = null): ?string
    {
        $setting = cacheAlt()->remember(
            'user_info_' . $sellerId . '_' . $key,
            CacheKeys::CACHE_1H,
            static function () use ($sellerId, $key) {
                return UserInfo::query()->select('value')->where('user_id', $sellerId)->where('key', $key)->first();
            }
        );
        if (!empty($setting)) {
            return Str::isJson($setting->value) ? json_decode($setting->value, true, 512, JSON_THROW_ON_ERROR) : $setting->value;
        }
        return $defaultValue;
    }
}

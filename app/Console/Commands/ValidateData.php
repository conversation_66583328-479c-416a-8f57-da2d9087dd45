<?php

namespace App\Console\Commands;

use App\Enums\CacheKeys;
use App\Enums\CampaignStatusEnum;
use App\Enums\DiscordChannel;
use App\Enums\DiscordUserIdEnum;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\ProductStatus;
use App\Enums\SellerBillingType;
use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Jobs\ProcessOrderCompleted;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\Elastic;
use App\Models\IndexCampaign;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Models\PgWebhookLogs;
use App\Models\Product;
use App\Models\SellerBilling;
use App\Models\Slug;
use App\Models\User;
use App\Services\StripeEmbeddedService;
use App\Services\UserService;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;
use Throwable;

class ValidateData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system-data:validate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate Data';

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        $cache = Cache::store('database');
        $CACHE_LAST_VALIDATED_TIME = 'cache_last_validated_time';
        $validateTime = now();

        try {
            $lastValidatedTime = $cache->get($CACHE_LAST_VALIDATED_TIME) ?? $validateTime->copy()->subMinutes(5);
        } catch (Throwable $e) {
            $lastValidatedTime = $validateTime->copy()->subMinutes(5);
        }

        // validate seller balance
        $CACHE_VALIDATED_SELLER_IDS = 'validated_seller_ids';

        try {
            $validatedIds = $cache->get($CACHE_VALIDATED_SELLER_IDS) ?? [];
        } catch (Throwable $e) {
            $validatedIds = [];
        }

        $recentUpdatedSellers = User::query()->select(['user.id', 'user.balance', 'seller_billing.id as transaction_id', 'seller_billing.balance as last_balance'])
            ->leftJoin('seller_billing', function($join) {
                $join->on('user.id', '=', 'seller_billing.seller_id');
                $join->on('seller_billing.id', '=', DB::raw("(select max(seller_billing.id) from seller_billing where seller_billing.seller_id=user.id and seller_billing.balance_type='default')"));
            })
            ->where('user.balance', '>', 0)
            ->where('user.updated_at', '>=', $lastValidatedTime)
            ->whereNotIn('user.id', $validatedIds)
            ->groupBy('user.id')
            ->get();

        $validatedIds = [];

        foreach ($recentUpdatedSellers as $seller) {
            if ($seller->balance !== $seller->last_balance) {
                User::query()->where('id', $seller->id)->update([
                    'status' => UserStatusEnum::FLAGGED,
                    'flag_log' => 'Balance updated unusual. Balance: ' . $seller->balance . '. Last balance: ' . $seller->last_balance . ' - transaction #' . $seller->transaction_id
                ]);
                $validatedIds[] = $seller->id;
                logToDiscord('Balance invalid! Seller #' . $seller->id . '. Balance: ' . $seller->balance . '. Last balance: ' . $seller->last_balance . ' - transaction #' . $seller->transaction_id, 'admin_warning');
            }
        }
        $cache->put($CACHE_VALIDATED_SELLER_IDS, $validatedIds, CacheKeys::CACHE_5m);

        // validate transaction
        $transactions = SellerBilling::query()
            ->where('updated_at', '>=', $lastValidatedTime)
            ->where('balance_type', SellerBalanceTypeEnum::DEFAULT)
            ->where('is_valid', true)
            ->get();

        if ($transactions->isNotEmpty()) {
            foreach ($transactions as $transaction) {
                if (!$transaction->validate()) {
                    $transaction->save();
                    logToDiscord('Transaction invalid! Seller #' . $transaction->seller_id . '. Transaction #' . $transaction->id, 'admin_warning');
                }
            }
        }

        // validate order amount after 10 min
        $delayTime = 10;
        $orders = Order::query()->where('payment_status', OrderPaymentStatus::PAID)
            ->with(['products', 'seller:id,email'])
            ->whereDoesntHave('order_history', function ($q) {
                $q->where('action', OrderHistoryActionEnum::PAID_BY_ADMIN);
            })
            ->whereBetween('paid_at', [$lastValidatedTime->copy()->subMinutes($delayTime * 3), $validateTime->copy()->subMinutes($delayTime)])
            ->get();
        foreach ($orders as $order) {
            $cacheKey = md5('order_invalid_amount_' . $order->id);
            if ($order->isCustomServiceOrder() || $order->isServiceOrder() || $cache->has($cacheKey)) {
                continue;
            }
            $cache->put($cacheKey, $order->id, CacheKeys::CACHE_1H);
            $order->calculateOrder();
            if ($order->type === OrderTypeEnum::REGULAR || $order->type === OrderTypeEnum::CUSTOM) {
                if (abs($order->total_product_amount + $order->total_shipping_amount + $order->insurance_fee - $order->total_discount + $order->tip_amount - $order->total_amount) > 0.1) {
                    logToDiscord("https://admin.senprints.com/order/detail/{$order->id}\r\nOrder invalid #$order->id . Total amount $order->total_paid is invalid.", 'admin_warning');
                }
            }
            if (abs($order->total_paid - $order->total_amount) > 0.1) {
                logToDiscord("https://admin.senprints.com/order/detail/{$order->id}\r\nOrder invalid #$order->id . Total paid $order->total_paid is different from total amount $order->total_amount.", 'admin_warning');
            }
            $region_order_id = $order->getIdFromOrderNumber();
            $paymentLogs = PgWebhookLogs::query()
                ->whereNotNull('order_id')
                ->where('order_id', $region_order_id)
                ->whereIn('event_type', ['charge.succeeded', 'payment_intent.succeeded', 'PAYMENT.CAPTURE.COMPLETED', 'WEBHOOK.PAYMENT.CAPTURE.COMPLETED', 'VERIFY_PURCHASE.CAPTURE.COMPLETED'])
                ->get();
            if (!$paymentLogs || $paymentLogs->isEmpty()) {
                continue;
            }
            $transactions = [];
            foreach ($paymentLogs as $paymentLog) {
                $payment = Str::isJson($paymentLog->payload) ? json_decode($paymentLog->payload, false, 512, JSON_THROW_ON_ERROR) : null;
                if (!$payment) {
                    continue;
                }
                if ($paymentLog->gateway === PaymentMethodEnum::PAYPAL) {
                    $transaction_id = data_get($payment, 'resource.id', data_get($payment, 'capture.purchase_units.0.payments.captures.0.id'));
                    $total_paid = (float)data_get($payment, 'resource.amount.value', data_get($payment, 'capture.purchase_units.0.payments.captures.0.amount.value'));
                } else if ($paymentLog->gateway === PaymentMethodEnum::STRIPE) {
                    $transaction_id = data_get($payment, 'data.object.payment_intent', data_get($payment, 'data.object.id'));
                    if ($transaction_id && !str_starts_with($transaction_id, 'pi_')) {
                        $transaction_id = null;
                    }
                    $total_paid = data_get($payment, 'data.object.amount', 0);
                    $total_paid = (new StripeEmbeddedService($order))->reverseChargeAmount($total_paid);
                }
                if (empty($transaction_id) || empty($total_paid)) {
                    continue;
                }
                $transactions[$region_order_id][$transaction_id] = UserService::formatPrice($total_paid, $order->getCurrencyCode(), $order->getCurrencyRate());
            }

            if (!isset($transactions[$region_order_id])) {
                continue;
            }
            $transactions = $transactions[$region_order_id];
            if (count($transactions) > 1) {
                $color = match ($order->type) {
                    OrderTypeEnum::CUSTOM => 16759808,
                    default => 7503093,
                };
                $actionMessage = match ($order->type) {
                    OrderTypeEnum::CUSTOM => ' Cần thông báo cho seller để refund lại cho K!',
                    default => ' Cần xử lý refund lại cho K trên cổng của mình!' . "\r\n" . "cc: " . mentionDiscord(DiscordUserIdEnum::FIN),
                };
                $message = 'Order ID: #' . $order->id . "\r\n";
                $message .= 'Order number: ' . $order->order_number . "\r\n";
                $message .= 'Seller email: ' . $order->seller->email . "\r\n";
                $message .= 'Total amount: ' . UserService::formatCurrency($order->total_amount) . "\r\n";
                $message .= str_repeat('-', 40) . "\r\n";
                $message .= "Transactions: \r\n";
                foreach ($transactions as $transactionId => $transactionAmount) {
                    $message .= "- ID: {$transactionId}, Amount: {$transactionAmount}" . "\r\n";
                }
                $embedDesc = [
                    [
                        'description' => $message,
                        'color' => $color
                    ]
                ];
                if (app()->isProduction()) {
                    logToDiscord(mentionDiscord(DiscordUserIdEnum::MIMICS) . " https://admin.senprints.com/order/detail/{$order->id}\r\nOrder #$order->id có nhiều giao dịch đã thanh toán thành công." . $actionMessage, DiscordChannel::ADMIN_WARNING, embeds: $embedDesc);
                } else {
                    logToDiscord("https://admin.senprints.com/order/detail/{$order->id}\r\nOrder #$order->id có nhiều giao dịch đã thanh toán thành công." . $actionMessage, DiscordChannel::ADMIN_WARNING, embeds: $embedDesc);
                }
            }
        }

        // validate order pay profit after 10 min
        $orders = Order::query()
            ->with(['products'])
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->whereBetween('paid_at', [$lastValidatedTime->copy()->subHours(3), $validateTime->copy()->subMinutes($delayTime)])
            ->where(function ($q) {
                $q->whereHas('products', function ($q) {
                    $q->where('seller_profit', 0)->orWhereNull('seller_profit');
                });
                $q->orWhere(function ($q) {
                    $q->where('total_seller_profit', 0)->orWhereNull('total_seller_profit');
                });
            })
            ->get();
        foreach ($orders as $order) {
            if ($order->isCustomServiceOrder()) {
                continue;
            }
            if (empty($order->total_seller_profit)) {
                ProcessOrderCompleted::dispatch($order->id)->onQueue('order');
                continue;
            }
            $order->calculateDesignCost();
            if (empty($order->total_fulfill_fee)) {
                $order->calculateFulfillProcessingFee();
            }
            $order->calculateSellerProfit();
            $order->push();
        }

        $orders = Order::query()
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->where('type', OrderTypeEnum::REGULAR)
            ->whereBetween('paid_at', [$lastValidatedTime->copy()->subMinutes($delayTime), $validateTime->copy()->subMinutes($delayTime)])
            ->whereDoesntHave('sellerBilling', function ($q) {
                $q->where('type', SellerBillingType::COMMISSION);
            })
            ->get();
        foreach ($orders as $order) {
            $paymentGateway = PaymentGateway::query()->select(['id', 'seller_id'])->find($order->payment_gateway_id);
            $cacheKey = md5('order_invalid_commission_' . $order->id);
            if (!$paymentGateway || ($paymentGateway->seller_id && $paymentGateway->seller_id !== User::SENPRINTS_SELLER_ID) || $cache->has($cacheKey)) {
                continue;
            }
            $cache->put($cacheKey, $order->id, CacheKeys::CACHE_1H);
            logToDiscord("https://admin.senprints.com/order/detail/{$order->id}\r\nOrder #$order->id Order paid, but seller doesn't have commission transaction. Paid at: {$order->paid_at}, Now: " . now()->toDateTimeString(), 'admin_warning');
        }

        $cache->put($CACHE_LAST_VALIDATED_TIME, $validateTime, CacheKeys::CACHE_5m);

        // validate campaigns sync status
        $connections = User::query()->select('db_connection')->where('is_deleted', 0)->where('role', '!=', UserRoleEnum::CUSTOMER)->groupBy('db_connection')->get()->pluck('db_connection')->toArray();
        foreach ($connections as $connection) {
            $activeProductsInOver30Mins = Campaign::query()
                ->on($connection)
                ->select(['id', 'status', 'seller_id', 'slug'])
                ->where('updated_at', '<=', now()->subMinutes(5))
                ->where('updated_at', '>=', now()->subMinutes(35))
                ->where('created_at', '>=', now()->subHour())
                ->where('status', ProductStatus::ACTIVE)
                ->get();
            $activeProductsInOver30Mins->groupBy('seller_id')->each(function ($collection, $sellerId) use ($connection) {
                // Check and sync slugs
                $slugs = $collection->pluck('slug')->toArray();
                if (count($slugs) > 0) {
                    $createdSlug = collect($slugs)
                        ->chunk(200)
                        ->flatMap(fn($chunk) => Slug::query()->whereIn('slug', $chunk)->pluck('slug'))
                        ->all();
                    $notCreatedSlug = array_values(array_diff($slugs, $createdSlug));
                    if (count($notCreatedSlug) > 0) {
                        Slug::query()->insertOrIgnore($collection->filter(fn ($item) => in_array($item->slug, $notCreatedSlug, true))->map(fn ($item) => ['seller_id' => $sellerId, 'campaign_id' => $item->id, 'slug' => $item->slug])->toArray());
                    }
                }
                $campaignIds = $collection->pluck('id')->toArray();
                if ($connection === config('database.default')) {
                    $syncedCampaignIds = IndexCampaign::query()
                        ->withTrashed()
                        ->whereIn('id', $campaignIds)
                        ->where('status', ProductStatus::ACTIVE)
                        ->pluck('id')
                        ->toArray();
                    $notSyncedCampaignIds = array_values(array_diff($campaignIds, $syncedCampaignIds));
                    if (count($notSyncedCampaignIds) > 0) {
                        Product::query()->on($connection)->filterByProductOrCampaignIds($notSyncedCampaignIds)->update(['sync_status' => Product::SYNC_DATA_TO_SINGLE_STORE]);
                    }
                }

                // Sync to elastic
                $arrFilterElastic = [];
                $arrFilterElastic['ids'] = $campaignIds;
                $arrFilterElastic['status'] = CampaignStatusEnum::DRAFT;
                $arrFilterElastic['seller_id'] = (int)$sellerId;
                [$campaigns, $total] = (new Elastic())->getCampaign(['id'], $arrFilterElastic, $collection->count());
                if (empty($campaigns) || $total <= 0) {
                    return;
                }
                $campaignIds = Arr::pluck($campaigns, 'id');
                if (count($campaignIds) > 0) {
                    try {
                        (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($campaignIds, sellerId: $sellerId);
                    } catch (\Throwable $e) {
                        Product::query()->on($connection)->filterByProductOrCampaignIds($campaignIds)->update(['sync_status' => Product::SYNC_DATA_STATS_ENABLED]);
                    }
                }
                unset($campaigns, $total, $arrFilterElastic, $campaignIds);
            });
            unset($activeProductsInOver10Mins);
        }
    }
}

<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;

class CorrectOrderFee extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:correct-order-fee {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Correct order fee by recalculating fulfill processing fee and seller profit';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $orderId = $this->argument('id');
        $order = Order::query()->with(['products'])->find($orderId);
        if (!$order) {
            $this->error('Order not found');
            return 1;
        }
        $order->calculateDesignCost();
        $order->calculateFulfillProcessingFee();
        $order->calculateSellerProfit();
        $order->push();
        $this->info('Order fee corrected successfully');
        return self::SUCCESS;
    }
}

<?php

namespace App\Console\Commands;

use App\Enums\OrderFulfillStatus;
use App\Enums\TrackingStatusEnum;
use App\Models\OrderProduct;
use App\Models\TrackingStatus;
use App\Services\TrackingService;
use Illuminate\Console\Command;

class RegisterTrackingCode extends Command
{
    protected int $times = 25;
    protected int $limit = 40;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delivery-tracking:register-tracking-code';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Register tracking code on the 17 Track';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $trackingCodes = OrderProduct::query()->select([
            'id',
            'order_id',
            'tracking_code',
            'shipping_carrier',
            'tracking_service',
            'tracking_url',
            'fulfilled_at',
            'received_at',
            'delivered_at',
            'tracking_status as status',
            'supplier_name',
            'supplier_id',
            'fulfill_status',
        ])
        ->with('order:id,order_number,country')
        ->where('fulfill_status', '!=', OrderFulfillStatus::FULFILLED)
        ->whereNull('order_product.deleted_at')
        ->where('delivered_at', '>=', today()->subDays(30))
        ->where(function ($query) {
            $query->where('tracking_code', '!=', '')->orWhereNull('tracking_status');
        })
        ->whereNotExists(function ($query) {
            $query->selectRaw('1')->from('tracking_status')->whereColumn('tracking_code', 'order_product.tracking_code');
        })
        ->where(function ($query) {
            $query->whereIn('tracking_status', [TrackingStatusEnum::NOTFOUND, TrackingStatusEnum::NEW, TrackingStatusEnum::UNTRACKED]);
        })
        ->groupBy('tracking_code')
        ->limit($this->limit * $this->times)
        ->orderByDesc('id')
        ->get();
        $trackingService = TrackingService::instance();
        if ($trackingCodes->isNotEmpty()) {
            $insertCodes = [];
            $trackingCodes = $trackingCodes->map(function ($item) use (&$insertCodes, $trackingService) {
                $item->tracking_code = str_replace("'", '', trim($item->tracking_code));
                $item->tracking_service = $trackingService->getService();
                if ($item->isDirty()) {
                    $item->update();
                }
                if (!isset($insertCodes[$item->tracking_code])) {
                    $insertCodes[$item->tracking_code] = [
                        [
                            'tracking_code' => $item->tracking_code,
                            'order_number' => $item->order->order_number ?? $item->order_id,
                        ],
                        [
                            'order_id' => $item->order_id ?? $item->order->order_number,
                            'status' => TrackingStatusEnum::NEW,
                            'tracking_service' => $trackingService->getService()
                        ]
                    ];
                }
                return $item;
            });
            if (!empty($insertCodes)) {
                foreach ($insertCodes as $insertCode) {
                    TrackingStatus::query()->updateOrCreate($insertCode[0], $insertCode[1]);
                }
            }
            $listTrackingCodes = $trackingCodes->pluck('tracking_code')->unique();
            graylogInfo('Start register tracking code on the 17track.', [
                'category' => 'tracking_status_logs',
                'tracking_codes' => $listTrackingCodes->toJson(),
            ]);
            OrderProduct::query()->whereIn('tracking_code', $listTrackingCodes->toArray())->update([
                'tracking_status' => TrackingStatusEnum::NEW,
                'tracking_service' => $trackingService->getService()
            ]);
            $trackingCodes->chunk($this->limit)->each(function ($trackingCodes) use ($trackingService) {
                $trackingService->registerOrderTracking($trackingCodes);
            });
        }
        return 0;
    }
}

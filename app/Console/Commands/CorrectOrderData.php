<?php

namespace App\Console\Commands;

use App\Enums\CacheKeys;
use App\Enums\DiscordChannel;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Jobs\ProcessOrderCompleted;
use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Modules\OrderService\Jobs\DatabaseSync\SyncOrderJob;
use Modules\OrderService\Models\RegionOrders;

class CorrectOrderData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:correct-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Correct order data';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $orders = Order::query()
            ->select('id', 'type')
            ->where([
                'payment_status' => OrderPaymentStatus::PAID,
                'total_seller_profit' => 0
            ])
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->where('paid_at', '<', now()->subMinutes(5))
            ->whereNotIn('status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::DELETED, OrderStatus::COMPLETED])
            ->limit(100)
            ->get();

        if ($orders->count() > 0) {
            foreach ($orders as $order) {
                if ($order->isCustomServiceOrder()) {
                    continue;
                }
                ProcessOrderCompleted::dispatch($order->id)->onQueue('order');
            }
        }

        $orders = Order::query()
            ->select('id', 'type')
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->where('paid_at', '<=', now()->subMinutes(5))
            ->where('paid_at', '>=', now()->subMinutes(15))
            ->whereNotIn('status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::DELETED, OrderStatus::COMPLETED])
            ->whereDoesntHave('products')
            ->get();
        $cache = Cache::store('database');
        if ($orders->count() > 0) {
            foreach ($orders as $order) {
                $cacheKey = md5('order_invalid_has_no_products_' . $order->id);
                if ($order->isCustomServiceOrder() || $order->isServiceOrder() || $cache->has($cacheKey)) {
                    continue;
                }
                $cache->put($cacheKey, $order->id, CacheKeys::CACHE_1H);
                logToDiscord("https://admin.senprints.com/order/detail/" . $order->id . "\r\n[URGENT] Order invalid #$order->id . Order has no products.", DiscordChannel::ADMIN_WARNING);
            }
        }

        $regions = collect(config('region.regions'))->keys()->filter(function ($region) {
            return $region !== config('app.region_master');
        })->values()->toArray();

        foreach ($regions as $region) {
            $regionOrders = RegionOrders::onRegion($region)
                ->select(['id', 'order_number', 'region'])
                ->where('payment_status', OrderPaymentStatus::PAID)
                ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                ->where('paid_at', '<=', now()->subMinutes(6))
                ->where('paid_at', '>=', now()->subMinutes(25))
                ->whereNotIn('status', [OrderStatus::CANCELLED, OrderStatus::REFUNDED, OrderStatus::DELETED, OrderStatus::COMPLETED])
                ->get();

            if ($regionOrders->count() > 0) {
                $regionOrderNumbers = $regionOrders->pluck('order_number')->toArray();
                $masterOrders = Order::query()
                    ->where('payment_status', OrderPaymentStatus::PAID)
                    ->whereIn('order_number', $regionOrderNumbers)
                    ->pluck('order_number')
                    ->toArray();

                $needToProcessOrderIds = array_values(array_diff($regionOrderNumbers, $masterOrders));
                if (count($needToProcessOrderIds) > 0) {
                    foreach ($needToProcessOrderIds as $orderNumber) {
                        $cacheKey = md5('order_invalid_synced_to_master_' . $orderNumber);
                        $order = Order::query()
                            ->where('payment_status', '!=', OrderPaymentStatus::PAID)
                            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
                            ->firstWhere('order_number', $orderNumber);
                        if (!$order || $cache->has($cacheKey)) {
                            continue;
                        }
                        SyncOrderJob::dispatch($order->getIdFromOrderNumber(), $order->getRegion())->onQueue('sync_order_region');
                        logToDiscord("https://admin.senprints.com/order/detail/{$order->getId()}\r\nOrder #{$order->getId()} ({$order->getOrderNumber()}) is paid but not synced to master. Re-synced to master.", DiscordChannel::ADMIN_WARNING);
                        $cache->put($cacheKey, $order->getId(), CacheKeys::CACHE_30m);
                    }
                }
            }
        }

        $orders = Order::query()
            ->where('type', OrderTypeEnum::CUSTOM)
            ->where('sen_fulfill_status', OrderSenFulfillStatus::YES)
            ->where('processing_fee_paid', 0)
            ->where('paid_at', '<=', now()->clone()->subMinutes(1441)) // 24 hours 1 minute
            ->where('paid_at', '>=', now()->clone()->subDays(7)) // 7 days
            ->whereHas('products', function ($q) {
                $q->where('campaign_id', '>', 0);
            })->get();
        if ($orders->count() > 0) {
            foreach ($orders as $order) {
                $cacheKey = md5('order_invalid_seller_charge_fee_' . $order->id);
                if ($cache->has($cacheKey)) {
                    continue;
                }
                $order->sen_fulfill_status = OrderSenFulfillStatus::PENDING;
                $order->calculateDesignCost();
                $order->calculateFulfillProcessingFee();
                if (empty($order->total_seller_profit)) {
                    $order->calculateSellerProfit();
                }
                if ($order->isDirty()) {
                    $order->push();
                }
                $cache->put($cacheKey, $order->id, CacheKeys::CACHE_24H);
                logToDiscord("https://admin.senprints.com/order/detail/{$order->getId()}\r\nOrder invalid #{$order->getId()} ({$order->getOrderNumber()})the seller has not been charged for the processing fee, but the order has sen fulfill status is YES. Re-calculated the fee.", DiscordChannel::ADMIN_WARNING);
            }
        }
    }
}

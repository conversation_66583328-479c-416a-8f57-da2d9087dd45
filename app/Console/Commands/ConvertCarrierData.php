<?php

namespace App\Console\Commands;

use App\Data\DataCarrierItemData;
use App\Enums\TrackingServiceEnum;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ConvertCarrierData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-carrier-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     * @throws \Throwable
     */
    public function handle()
    {
        $finalPath = resource_path('/json/carriers');
        $services = TrackingServiceEnum::asArray();
        foreach ($services as $service) {
            $carriersFilePath = $finalPath . '/base/' . $service . '.json';
            if (!File::exists($carriersFilePath)) {
                continue;
            }
            $carriers = File::get($carriersFilePath);
            $carriers = json_decode($carriers, true, 512, JSON_THROW_ON_ERROR);
            $_carriers = [];
            foreach ($carriers as $carrier) {
                $_carriers[] = DataCarrierItemData::withMapping([$carrier])->first();
            }
            $finalFile = $finalPath . '/' . $service . '.json';
            if (File::exists($finalFile)) {
                File::delete($finalFile);
            }
            File::put($finalFile, json_encode($_carriers));
            $this->info('Convert ' . $service . ' done');
        }
    }
}

<?php

namespace App\Console\Commands;

use App\Enums\OrderProductFulfillStatus;
use App\Enums\QueueName;
use App\Enums\TrackingStatusEnum;
use App\Jobs\ProcessUpdateOrderTrackingStatus;
use App\Models\OrderProduct;
use App\Models\TrackingStatus;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class UpdateTrackingStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delivery-tracking:update-tracking-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update delivery tracking status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        dispatch(static function () {
            try {
                $limit = 500;
                $orderProductFulfillStatuses = [
                    OrderProductFulfillStatus::PENDING,
                    OrderProductFulfillStatus::PROCESSING,
                    OrderProductFulfillStatus::REJECTED,
                    OrderProductFulfillStatus::EXCEPTION,
                    OrderProductFulfillStatus::FULFILLED,
                    OrderProductFulfillStatus::ON_DELIVERY
                ];
                $orderProducts = OrderProduct::query()->select([
                    'id',
                    'order_id',
                    'tracking_code',
                    'shipping_carrier',
                    'tracking_status',
                ])
                ->with('order:id,order_number')
                ->whereRaw('NOT EXISTS (SELECT 1 FROM tracking_status WHERE order_product.order_id = tracking_status.order_id)')
                ->where('tracking_code', '!=', '')
                ->whereNotNull('tracking_code')
                ->whereIn('tracking_status', [TrackingStatusEnum::NOTFOUND, TrackingStatusEnum::NEW])
                ->whereIn('fulfill_status', $orderProductFulfillStatuses)
                ->where('updated_at', '>=', now()->subMinutes(30))
                ->groupBy('tracking_code')
                ->orderByDesc('updated_at')
                ->get();
                if ($orderProducts->isNotEmpty()) {
                    $insertCodes = [];
                    $orderProducts->each(function ($tracking) use (&$insertCodes) {
                        $arrTrackingCode = Str::of($tracking->tracking_code)->explode(',')->filter()->toArray();
                        $arrShippingCarrier = explode(',', $tracking->shipping_carrier);
                        $insertCodes[] = [
                            'tracking_code' => data_get($arrTrackingCode, 0),
                            'order_number' => $tracking->order->order_number,
                            'order_id' => $tracking->order_id,
                            'status' => $tracking->tracking_status,
                            'shipping_carrier' => data_get($arrShippingCarrier, 0),
                        ];
                    });
                    $deleteTrackingCodes = collect($insertCodes)->pluck('tracking_code')->toArray();
                    foreach (array_chunk($deleteTrackingCodes, 100) as $chunkDeleteTrackingCodes) {
                        TrackingStatus::query()->whereIn('tracking_code', $chunkDeleteTrackingCodes)->delete();
                    }
                    foreach (array_chunk($insertCodes, 500) as $chunkInsertCodes) {
                        TrackingStatus::query()->insert($chunkInsertCodes);
                    }
                    unset($orderProducts, $insertCodes, $deleteTrackingCodes, $arrTrackingCode);
                }
                $query = TrackingStatus::query()->select(['id', 'order_id', 'tracking_code'])->groupBy('tracking_code');
                $query1 = $query->clone()
                    ->whereNotIn('status', TrackingStatusEnum::endStatuses())
                    ->where('created_at', '>=', now()->subDays(3))
                    ->where('updated_at', '>=', now()->subHours(4))
                    ->limit($limit);
                $query2 = $query->clone()
                    ->whereNotIn('status', TrackingStatusEnum::endStatuses())
                    ->where('created_at', '<', now()->subDays(3))
                    ->where('created_at', '>=', now()->subDays(60))
                    ->where('updated_at', '>=', now()->subDay())
                    ->limit($limit);
                $trackingCodes = $query1->unionAll($query2)->get();
                $orderIds = $trackingCodes->pluck('order_id')->unique()->toArray();
                unset($trackingCodes);
                if (!empty($orderIds)) {
                    self::triggerUpdateOrderTrackingStatus($orderIds, $orderProductFulfillStatuses);
                }
                $orderIds = $query->clone()
                    ->whereIn('status', [TrackingStatusEnum::NOTFOUND, TrackingStatusEnum::NEW, TrackingStatusEnum::UNTRACKED])
                    ->where('created_at', '>=', now()->subDays(7))
                    ->limit($limit)
                    ->get()
                    ->pluck('order_id')
                    ->unique()
                    ->toArray();
                if (!empty($orderIds)) {
                    self::triggerUpdateOrderTrackingStatus($orderIds, $orderProductFulfillStatuses);
                }
                unset($query, $query1, $query2, $orderIds);
            } catch (\Exception $e) {
                logException($e, '[17Track] Get tracking info failed - Message: ' . $e->getMessage(), 'tracking_status_logs', true);
            }
        })->onQueue(QueueName::CRAWL_TRACKING_STATUS);
    }

    /**
     * @param $orderIds
     * @param $orderProductFulfillStatuses
     * @return void
     */
    public static function triggerUpdateOrderTrackingStatus($orderIds, $orderProductFulfillStatuses)
    {
        $orderProducts = OrderProduct::query()
            ->select([
                'id',
                'order_id',
                'tracking_code',
                'shipping_carrier',
                'tracking_service',
                'tracking_url',
                'fulfilled_at',
                'received_at',
                'delivered_at',
                'tracking_status as status',
                'supplier_name',
                'supplier_id',
                'fulfill_status',
            ])
            ->with('order:id,order_number,country')
            ->whereIn('order_id', $orderIds)
            ->where('tracking_code', '!=', '')
            ->where('tracking_code', '!=', '-')
            ->whereNotNull('tracking_code')
            ->whereIn('fulfill_status', $orderProductFulfillStatuses)
            ->groupBy('tracking_code')
            ->get();
        if ($orderProducts->isNotEmpty()) {
            $orderProducts->chunk(40)->each(function ($chunkTrackingCodes, $idx) {
                ProcessUpdateOrderTrackingStatus::dispatch($chunkTrackingCodes->toArray())->onQueue(QueueName::ORDER_EVENTS)->delay(now()->addSeconds($idx * 2));
            });
            unset($orderProducts);
        }
    }
}

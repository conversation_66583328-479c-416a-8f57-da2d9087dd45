<?php
namespace App\Console\Commands;

use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\SellerBillingType;
use App\Enums\ShippingMethodEnum;
use App\Events\OrderCancelledEvent;
use App\Facades\ProcessLock;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\PaymentGatewayRefund;
use App\Models\User;
use App\Services\StoreService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Throwable;

class RefundOrderCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:refund';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refund order request';

    public function handle()
    {
        ProcessLock::handle($this->signature, callback: function () {
            $limit = 10;
            $refunds = OrderCancelRequest::query()
                ->where(function ($query) {
                    $query->whereRaw("(status = '" . OrderCancelRequestStatus::PROCESSING . "' AND updated_at <= '" . now()->subHour() . "')")
                        ->orWhereRaw("(status = '" . OrderCancelRequestStatus::CONFIRMED . "' AND updated_at <= '" . now()->subHours(24) . "')");
                })
                ->limit($limit)
                ->get();
            if ($refunds->count() === 0) {
                return;
            }
            DB::beginTransaction();
            try {
                $artistRefund = [];
                $orderIds = [];
                foreach ($refunds as $refund) {
                    if (empty($refund->order_id)) {
                        continue;
                    }
                    try {
                        $order = Order::query()
                            ->where('id', $refund->order_id)
                            ->with([
                                'products' => function ($query) {
                                    return $query->whereNotIn('order_product.fulfill_status', [
                                        OrderProductFulfillStatus::CANCELLED,
                                    ]);
                                }
                            ])
                            ->whereIn('order.payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::REFUNDED])
                            ->where('order.shipping_method', ShippingMethodEnum::STANDARD)
                            ->whereIn('order.type', [
                                OrderTypeEnum::CUSTOM,
                                OrderTypeEnum::REGULAR
                            ])
                            ->first();
                        if (!$order) {
                            $refund->status = OrderCancelRequestStatus::ERROR;
                            $refund->error_log = 'Order status invalid';
                            continue;
                        }
                        if ($order->payment_status === OrderPaymentStatus::REFUNDED) {
                            $refund->status = OrderCancelRequestStatus::COMPLETED;
                            $refund->updated_at = now();
                            $refund->save();
                            continue;
                        }
                        foreach ($order->products as $product) {
                            if ($product->artist_refund === 0) {
                                continue;
                            }
                            if (!isset($artistRefund[$product->seller_id])) {
                                $artistRefund[$product->seller_id] = 0;
                            }
                            $artistRefund[$product->seller_id] += $product->artist_refund;
                        }
                        foreach ($artistRefund as $sellerId => $refundAmount) {
                            $refundAmount = number_format($refundAmount, 2);
                            $artist = User::query()->firstWhere('id', $sellerId);
                            if ($artist && $refundAmount > 0) {
                                $artist->updateBalance(-$refundAmount, SellerBillingType::REFUND, 'Refund order 12h #' . $order->order_number, $order->id);
                            }
                        }

                        $seller = User::query()->firstWhere('id', $order->seller_id);
                        if ($seller) {
                            $totalRefundToSeller = -$order->total_seller_profit;
                            if ($order->type === OrderTypeEnum::CUSTOM) {
                                $totalRefundToSeller = $order->processing_fee_paid + $order->fulfill_fee_paid;
                            }
                            $seller->updateBalance($totalRefundToSeller, SellerBillingType::REFUND, 'Refund order 12h #' . $order->order_number, $order->id);
                        }
                        $totalRefund = $order->total_paid;
                        $order->total_refund = $totalRefund;
                        $order->total_seller_profit = 0;
                        $order->total_artist_profit = 0;
                        $order->payment_status = OrderPaymentStatus::REFUNDED;
                        $order->status = OrderPaymentStatus::REFUNDED;
                        $order->fulfill_status = OrderFulfillStatus::CANCELLED;
                        $order->products->map(function (OrderProduct $product) {
                            $product->fulfill_status = OrderProductFulfillStatus::CANCELLED;
                        });
                        OrderHistory::insertLog(
                            $order,
                            OrderHistoryActionEnum::REFUNDED,
                            'Refund order 12h #' . $order->order_number,
                            OrderHistoryDisplayLevelEnum::CUSTOMER,
                        );
                        $refund->status = OrderCancelRequestStatus::COMPLETED;
                        if ($totalRefund > 0) {
                            PaymentGatewayRefund::query()->create([
                                'payment_gateway_id' => $order->payment_gateway_id,
                                'order_id' => $order->id,
                                'seller_id' => $order->seller_id,
                                'store_id' => $order->store_id,
                                'staff_id' => 0, // System
                                'refund_amount' => $totalRefund,
                                'is_full_refund' => 1,
                                'reason' => 'Refund order 12h #' . $order->order_number
                            ]);
                        }
                        $orderIds[] = $order->id;
                        $order->push();
                        $storeInfo = StoreService::getStoreInfo($order->store_id);
                        $sellerEmail = User::query()
                            ->where('id', $order->seller_id)
                            ->value('email');
                        $dataSendMailLog = [
                            'sellerId' => $order->seller_id,
                            'storeId' => $order->store_id,
                            'orderId' => $order->id,
                        ];
                        $config = [
                            'to' => $order->customer_email,
                            'template' => 'buyer.order_cancelled',
                            'data' => [
                                'subject' => "Your order #{$order->order_number} was cancelled",
                                'name' => $order->customer_name,
                                'email' => $order->customer_email,
                                'store_info' => $storeInfo,
                                'seller_email' => $sellerEmail,
                                'order' => $order,
                            ],
                            'sendMailLog' => $dataSendMailLog
                        ];
                        OrderCancelledEvent::dispatch($order);
                        if (!sendEmail($config)) {
                            logToDiscord([
                                'email' => 'order_cancelled',
                                'order_id' => $order->id
                            ], 'email');
                        }
                    } catch (Throwable $exception) {
                        $refund->status = OrderCancelRequestStatus::ERROR;
                        $refund->error_log = $exception->getMessage();
                    }
                    $refund->updated_at = now();
                    $refund->save();
                }
                DB::commit();
                if (!empty($orderIds)) {
                    $countSuccess = count($orderIds);
                    logToDiscord(date('Y-m-d H:i:s') . " - Refunded for total $countSuccess request(s). Order Id: " . implode(', ', $orderIds), 'order_refund');
                }
                return;
            } catch (Throwable $e) {
                DB::rollBack();
                logToDiscord($e->getCode() . 'RefundOrderCommand -> Message: ' . $e->getMessage() . ', File: ' . $e->getFile() . ', Line: ' . $e->getLine(), 'order_refund');
                return;
            }
        });
    }
}

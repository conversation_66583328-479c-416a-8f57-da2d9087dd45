<?php

namespace App\Console\Commands;

use App\Enums\OrderPaymentStatus;
use App\Enums\QueueName;
use App\Jobs\FindAndFillOrderTransactionId;
use App\Models\Order;
use Illuminate\Console\Command;

class FillOrderLostTransactionId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fill-orders-lost-transaction-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fill orders lost transaction id';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $start = now()->toDateTimeString();
        $end = now()->yesterday()->toDateTimeString();

        $orders = Order::query()
            ->where(function ($q) {
                $q->whereNull('transaction_id')
                    ->orWhere('transaction_id', '');
            })
            ->where('payment_status', OrderPaymentStatus::PAID)
            ->where(function ($q) use ($start, $end) {
                $q->where('paid_at', '>=', $start)->where('paid_at', '<=', $end);
            })
            ->where('payment_method', 'LIKE', 'stripe%')
            ->get();

        if ($orders->isNotEmpty()) {
            foreach ($orders as $idx => $order) {
                FindAndFillOrderTransactionId::dispatch($order, true)->onQueue(QueueName::ORDER_EVENTS)->delay(now()->addSeconds($idx));
            }
        }
        return self::SUCCESS;
    }
}

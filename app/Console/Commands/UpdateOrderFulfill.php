<?php

namespace App\Console\Commands;

use App\Enums\AccessExternalApiType;
use App\Enums\ApiLogTypeEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\QueueName;
use App\Http\Controllers\Admin\FulfillController;
use App\Models\ApiLog;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Supplier;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Throwable;

class UpdateOrderFulfill extends Command
{
    protected $signature = 'order-fulfill:update';

    protected $description = 'Update order fulfill status';

    public function handle(): int
    {
        try {
            $now = now();
            $supplierIdsAllow = Supplier::query()
            ->select('id')
            ->where(function ($q) use ($now) {
                $q->where('api_holds_until', '<' ,$now->toDateTimeString())
                ->orWhere('api_holds_until', null);
            })
            ->get()
            ->pluck('id')
            ->toArray();
            // where not in primary key > join
            $collectOrderIds = ApiLog::query()
                ->select('order_id')
                ->where('created_at', '>=', now()->subHours())
                ->where('reference_type', AccessExternalApiType::SUPPLIER)
                ->where('type', ApiLogTypeEnum::API)
                ->whereIn('reference_id', $supplierIdsAllow)
                ->pluck('order_id')
                ->unique()
                ->values();
            $arr1 = collect();
            foreach ($collectOrderIds->chunk(500) as $orderIds) {
                DB::enableQueryLog();
                $result_ids = Order::query()
                    ->select('id')
                    ->whereNotIn('id', $orderIds)
                    ->where('updated_at', '<=', now()->subHours(24))
                    ->where('fulfill_status', OrderFulfillStatus::PROCESSING)
                    ->whereIn('status', [
                        OrderStatus::PROCESSING,
                        OrderStatus::REFUNDED,
                    ])
                    ->get()
                    ->pluck('id')
                    ->toArray();
                $arr1 = $arr1->merge($result_ids);
                $queryLog = DB::getQueryLog();
                graylogInfo('Debug Update Order Fulfill', [
                    'category' => 'debug_update_order_fulfill',
                    'order_ids' => count($result_ids),
                    'run_at' => now()->toDateTimeString(),
                    'order_ids_list' => implode(",", $result_ids),
                    'query_log' => $queryLog,
                    'step' => 1
                ]);
            }
            graylogInfo('Debug Update Order Fulfill', [
                'category' => 'debug_update_order_fulfill',
                'order_ids' => $collectOrderIds->count(),
                'order_ids_lost_24h' => $arr1->count(),
                'run_at' => now()->toDateTimeString(),
            ]);
            DB::enableQueryLog();
            $arr2 = OrderProduct::query()
                ->where('fulfilled_at', '<=', now()->subHours(6))
                ->whereIn('fulfill_status', [
                    OrderProductFulfillStatus::PENDING,
                    OrderProductFulfillStatus::EXCEPTION,
                ])
                ->whereNotNull('fulfill_order_id')
                ->whereIn('supplier_id', $supplierIdsAllow)
                ->pluck('order_id')
                ->unique()
                ->values()
                ->toArray();
            $queryLog = DB::getQueryLog();
            graylogInfo('Debug Update Order Fulfill', [
                'category' => 'debug_update_order_fulfill',
                'order_ids_lost_6h' => count($arr2),
                'run_at' => now()->toDateTimeString(),
                'order_ids_list' => implode(",", $arr2),
                'query_log' => $queryLog,
                'step' => 2
            ]);
            $order_ids = $arr1->merge($arr2)->unique()->values()->toArray();
            if (count($order_ids) > 100) {
                foreach (array_chunk($order_ids, 100) as $key => $order_ids_chunk) {
                    $this->updateOrders($order_ids_chunk, $key * 5);
                }
                return self::SUCCESS;
            }
            $this->updateOrders($order_ids);
            return self::SUCCESS;
        } catch (Throwable $e) {
            logException($e);
        }
        return self::FAILURE;
    }

    /**
     * @param $order_ids
     * @param int $delay
     * @return void
     * @throws Throwable
     */
    private function updateOrders($order_ids, int $delay = 0) {
        try {
            dispatch(function () use ($order_ids) {
                $controller = new FulfillController();
                $arr = [];
                $arr['order_ids'] = $order_ids;
                if (!empty($arr['order_ids'])) {
                    $arr['order_product_fulfill_statuses'] = [
                        OrderProductFulfillStatus::PENDING,
                        OrderProductFulfillStatus::PROCESSING,
                    ];
                    $arr['is_manually'] = false;
                    $request = new Request($arr);
                    $controller->updateOrders($request);
                }
            })->onQueue(QueueName::CRAWL_FULFILL)->delay(now()->addSeconds($delay));
        } catch (Throwable $e) {
            throw $e;
        }
    }
}

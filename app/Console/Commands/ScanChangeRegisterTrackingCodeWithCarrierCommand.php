<?php

namespace App\Console\Commands;

use App\Services\SeventeenTrack;
use Illuminate\Console\Command;

/**
 * @deprecated This command is deprecated and will be removed in the future.
 */
class ScanChangeRegisterTrackingCodeWithCarrierCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan:re-register-tracking-code';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan re-register tracking code with custom shipping carriers';

    /**
     * Execute the console command.
     * @return void
     * @throws \Throwable
     */
    public function handle()
    {
        SeventeenTrack::scanRegisterTrackingCodeWithDiffCarrier();
    }
}

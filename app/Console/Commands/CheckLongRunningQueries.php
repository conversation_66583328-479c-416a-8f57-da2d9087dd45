<?php

namespace App\Console\Commands;

use App\Enums\DiscordChannel;
use App\Traits\QueryTrait;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CheckLongRunningQueries extends Command
{
    use QueryTrait;
    const QUERY_DURATION_THRESHOLD = 99;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:longqueries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for long queries running';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $lockProcess = $this->getLockProcessByConnection('mysql', 610); // 10 minutes 10 seconds
            if (!empty($lockProcess)) {
                foreach ($lockProcess as $process) {
                    if ($process->trx_rows_modified > 0 && Str::lower($process->command) === 'sleep' && Str::lower($process->trx_state) === 'running') {
                        DB::connection('mysql')->statement("KILL {$process->id};");
                        $message = "- User: {$process->user}\r\n";
                        $message .= "- Host: {$process->host}\r\n";
                        $message .= "- DB: {$process->db}\r\n";
                        if ($process->query) {
                            $message .= "- Query: ```{$process->query}```\r\n";
                        }
                        $message .= "- Time: " . round($process->time_running / 60, 2) . " minutes" . "\r\n";
                        $message .= "- Rows modified: {$process->trx_rows_modified}\r\n";
                        $message .= "- Trx id: {$process->trx_id}\r\n";
                        $message .= "- Trx state: {$process->trx_state}\r\n";
                        $message .= "- Trx started: " . Carbon::parse($process->trx_started)->timezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s') . "\r\n";
                        $embedDesc = [
                            [
                                'description' => $message,
                                'color' => 15548997
                            ]
                        ];
                        logToDiscord("Đã kill process {$process->id}, do thời gian chạy quá 10 phút.", DiscordChannel::DEV_CHAT, false, true, 7, $embedDesc, threadId: 1303544454933909546);
                    }
                }
            }
            for ($i = 0; $i < 3; $i++) {
                $this->checkLongQueries('mysql');
                $this->checkLongQueries('mysql_seller');
                $this->checkLongQueries('singlestore');
                if ($i < 2) {
                    // Sleep for 20 seconds before the next iteration,
                    sleep(20);
                }
            }
            return self::SUCCESS;
        } catch (\Throwable $e) {
            return self::FAILURE;
        }
    }
}

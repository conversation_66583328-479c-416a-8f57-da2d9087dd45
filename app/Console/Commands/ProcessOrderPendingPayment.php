<?php

namespace App\Console\Commands;

use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\DiscordChannel;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Jobs\ScanCompleteOrderPaymentJob;
use App\Models\Order;
use App\Models\PaymentGateway;
use App\Models\PgWebhookLogs;
use App\Services\StripeEmbeddedService;
use App\Traits\Encrypter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Psr\SimpleCache\InvalidArgumentException;

class ProcessOrderPendingPayment extends Command
{
    use Encrypter;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:complete-pending-payment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This is a command handle complete pending payment for order';

    /**
     * Execute the console command.
     * @throws \Throwable
     */
    public function handle(): void
    {
        $cache = Cache::store('database');
        $orders = Order::query()
            ->select(['id', 'order_number', 'access_token', 'region', 'payment_gateway_id', 'payment_method', 'transaction_id', 'status', 'region_synced_at'])
            ->whereNull('paid_at')
            ->whereIn('status', [
                OrderStatus::DRAFT,
                OrderStatus::PENDING,
                OrderStatus::PENDING_PAYMENT,
            ])
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->whereNotNull('region_synced_at')
            ->excludeTest()
            ->where('created_at', '>=', now()->clone()->subWeek()->toDateTimeString())
            ->whereBetween('updated_at', [now()->clone()->subHours(2)->toDateTimeString(), now()->clone()->subMinutes(5)->toDateTimeString()])
            ->orderBy('id')
            ->get();
        if ($orders->isNotEmpty()) {
            foreach ($orders as $order) {
                $order_number = $order->getOrderNumber();
                if (!preg_match('/\d-\w+$/', $order_number)) {
                    continue;
                }
                $cacheKey = md5('process_order_draft_pending_payment_' . $order->id);
                if ($cache->has($cacheKey)) {
                    continue;
                }
                $cache->put($cacheKey, $order->id, CacheKeys::CACHE_1H);
                $order_id = $order->getIdFromOrderNumber();
                $paymentLog = PgWebhookLogs::query()
                    ->whereNotNull('order_id')
                    ->where('order_id', $order_id)
                    ->whereIn('event_type', ['charge.succeeded', 'payment_intent.succeeded', 'PAYMENT.CAPTURE.COMPLETED', 'WEBHOOK.PAYMENT.CAPTURE.COMPLETED', 'VERIFY_PURCHASE.CAPTURE.COMPLETED'])
                    ->orderByDesc('created_at')
                    ->groupBy('gateway')
                    ->first();
                if (!$paymentLog) {
                    continue;
                }
                $payment = Str::isJson($paymentLog->payload) ? json_decode($paymentLog->payload, false) : null;
                if ($paymentLog->gateway === PaymentMethodEnum::STRIPE) {
                    $payment_gateway_id = (int)$paymentLog->payment_gateway_id;
                } else if ($paymentLog->gateway === PaymentMethodEnum::PAYPAL) {
                    $payment_gateway_id = (int)$paymentLog->payment_gateway_id;
                    $payee_email = data_get($payment, 'resource.payee.email_address', data_get($payment, 'orderDetail.purchase_units.0.payee.email_address'));
                    if ($payment_gateway_id === 0 && !empty($payee_email)) {
                        $payment_gateway_id = cacheAlt()->remember(md5($payee_email . '-' . PaymentMethodEnum::PAYPAL), CacheTime::CACHE_24H, function () use ($payee_email) {
                            return (int)PaymentGateway::query()->select('id')->where([
                                'gateway' => PaymentMethodEnum::PAYPAL,
                                'account_id' => $payee_email
                            ])->value('id');
                        });
                    }
                }
                if (empty($payment_gateway_id)) {
                    continue;
                }
                if ($order->status === OrderStatus::DRAFT) {
                    $order->status = OrderStatus::PENDING;
                }
                $order->payment_gateway_id = $payment_gateway_id;
                $order->payment_method = $paymentLog->gateway;
                if ($order->isDirty()) {
                    $order->saveQuietly();
                }
            }
        }
        $this->processOrderPendingStripePayment();
        $this->processOrderPendingPayPalPayment();
    }

    /**
     * @return int
     * @throws \Throwable
     */
    private function processOrderPendingStripePayment()
    {
        $cache = Cache::store('database');
        $orders = Order::query()
            ->whereNull('paid_at')
            ->whereNotNull('payment_gateway_id')
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::PENDING_PAYMENT,
            ])
            ->whereNotNull('region_synced_at')
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->excludeTest()
            ->where(function ($q) {
                $q->whereNull('transaction_id')->orWhere('transaction_id', '');
            })
            ->where('created_at', '>=', now()->clone()->subWeeks(2)->toDateTimeString())
            ->whereBetween('updated_at', [now()->clone()->subHours(2)->toDateTimeString(), now()->clone()->subMinutes(5)->toDateTimeString()])
            ->orderBy('id')
            ->get();
        $excludeOrderIds = [];
        if ($orders->isNotEmpty()) {
            foreach ($orders as $idx => $order) {
                $cacheKey = md5('process_order_pending_stripe_payment_1_' . $order->id);
                if ($cache->has($cacheKey)) {
                    continue;
                }
                $cache->put($cacheKey, $order->id, CacheKeys::CACHE_1H);
                $excludeOrderIds[] = $order->id;
                ScanCompleteOrderPaymentJob::dispatch($order)->delay(now()->addSeconds($idx));
            }
        }

        // Order has webhook data
        $query = Order::query()
            ->whereNull('paid_at')
            ->whereNotNull('payment_gateway_id')
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::PENDING_PAYMENT,
            ])
            ->whereNotNull('region_synced_at')
            ->excludeTest()
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->when(!empty($excludeOrderIds), function ($q) use ($excludeOrderIds) {
                $q->whereNotIn('id', $excludeOrderIds);
            })
            ->where('created_at', '>=', now()->clone()->subWeeks(2)->toDateTimeString())
            ->whereBetween('updated_at', [now()->clone()->subHours(2)->toDateTimeString(), now()->clone()->subMinutes(5)->toDateTimeString()])
            ->where('payment_method', 'like', PaymentMethodEnum::STRIPE . '%');
        $full_sql = $query->toRawSql();
        $pendingPaymentOrders = $query->get();
        if ($pendingPaymentOrders->isEmpty()) {
            return 0;
        }
        graylogInfo('[STRIPE] Start scan and complete orders.', [
            'category' => 'pending_payment_order',
            'gateway' => PaymentMethodEnum::STRIPE,
            'raw_query' => $full_sql,
            'total_orders' => $pendingPaymentOrders->count(),
        ]);
        foreach ($pendingPaymentOrders as $order) {
            $order_number = $order->order_number;
            if (!preg_match('/\d-\w+$/', $order_number)) {
                return 0;
            }
            $cacheKey = md5('process_order_pending_stripe_payment_2_' . $order->id);
            if ($cache->has($cacheKey)) {
                continue;
            }
            $cache->put($cacheKey, $order->id, CacheKeys::CACHE_1H);
            $order_id = $order->getIdFromOrderNumber();
            $paymentLog = PgWebhookLogs::query()
                ->whereNotNull('order_id')
                ->where('order_id', $order_id)
                ->whereIn('event_type', ['charge.succeeded', 'payment_intent.succeeded'])
                ->orderByDesc('created_at')
                ->first();
            if (!$paymentLog) {
                continue;
            }
            $payment_gateway_id = (int)$order->payment_gateway_id;
            $actual_payment_gateway_id = (int)$paymentLog->payment_gateway_id;
            $payment = json_decode($paymentLog->payload, false);
            $transaction_id = data_get($payment, 'data.object.payment_intent', data_get($payment, 'data.object.id'));
            if ($transaction_id && !str_starts_with($transaction_id, 'pi_')) {
                $transaction_id = null;
            }
            $total_amount = (float)$order->total_amount;
            $total_paid = data_get($payment, 'data.object.amount', 0);
            if (empty($transaction_id) || empty($total_paid)) {
                continue;
            }
            $currency_rate = (float)$order->currency_rate;
            $total_amount *= $currency_rate;
            $total_amount = round($total_amount, 2);
            $description = data_get($payment, 'data.object.description');
            $total_paid = (new StripeEmbeddedService($order))->reverseChargeAmount($total_paid);
            $message = "Order #{$order->id} has not been paid, but a successful payment transaction has been recorded on the gateway." . PHP_EOL;
            $message .= 'Payment method: ' . PaymentMethodEnum::STRIPE . PHP_EOL;
            $message .= 'Total amount: ' . $total_amount . PHP_EOL;
            $message .= 'Total received amount: ' . $total_paid . PHP_EOL;
            if (!empty($payment_gateway_id)) {
                $message .= 'Current PGW ID: ' . $payment_gateway_id . PHP_EOL;
            }
            if ($payment_gateway_id !== $actual_payment_gateway_id) {
                $message .= 'Actual PGW ID: ' . $actual_payment_gateway_id . PHP_EOL;
                $order->payment_gateway_id = $actual_payment_gateway_id;
            }
            if (!empty($order->transaction_id)) {
                $message .= 'Current Transaction ID: ' . $order->transaction_id . PHP_EOL;
            }
            if ($order->transaction_id !== $transaction_id) {
                $message .= 'Actual Transaction ID: ' . $transaction_id . PHP_EOL;
                $order->transaction_id = $transaction_id;
            }
            if ($description) {
                $message .= 'Summary: ' . $description . PHP_EOL;
            }
            $color = '15548997';
            if ($total_paid === $total_amount) {
                if ($order->isDirty()) {
                    $order->save();
                }
                $order->paymentCompleted($order->total_amount, $order->transaction_id, false, null, null, $order->payment_gateway_id);
                $message .= 'Order has been completed.' . PHP_EOL;
                $color = '5763719';
            }
            $this->logToDiscord($order->id, $message, $color);
        }
        return 1;
    }

    /**
     * @param $order_id
     * @param $log
     * @param string $color
     * @return void
     * @throws \Throwable
     */
    private function logToDiscord($order_id, $log, string $color = '15548997'): void
    {
        $cache = Cache::store('database');
        $cacheKey = md5('process_order_pending_payment_' . $order_id);
        if ($cache->has($cacheKey)) {
            return;
        }
        $cache->put($cacheKey, $order_id, CacheKeys::CACHE_30m);
        $embedDesc = [
            [
                'description' => $log,
                'color' => $color
            ]
        ];
        logToDiscord("https://admin.senprints.com/order/detail/" . $order_id, DiscordChannel::ADMIN_WARNING, false, true, 7, $embedDesc);
    }

    /**
     * @return int
     * @throws \Throwable
     */
    private function processOrderPendingPayPalPayment()
    {
        $cache = Cache::store('database');
        $query = Order::query()
            ->whereNull('paid_at')
            ->whereNotNull('payment_gateway_id')
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::PENDING_PAYMENT,
            ])
            ->whereNotNull('region_synced_at')
            ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])
            ->excludeTest()
            ->where('created_at', '>=', now()->clone()->subWeeks(2)->toDateTimeString())
            ->whereBetween('updated_at', [now()->clone()->subHours(2)->toDateTimeString(), now()->clone()->subMinutes(5)->toDateTimeString()])
            ->where('payment_method', PaymentMethodEnum::PAYPAL)
            ->orderBy('id');
        $full_sql = $query->toRawSql();
        $pendingPaymentOrders = $query->get();
        if ($pendingPaymentOrders->isEmpty()) {
            return 0;
        }
        graylogInfo('[PAYPAL] Start scan and complete orders.', [
            'category' => 'pending_payment_order',
            'gateway' => PaymentMethodEnum::PAYPAL,
            'raw_query' => $full_sql,
            'total_orders' => $pendingPaymentOrders->count(),
        ]);
        foreach ($pendingPaymentOrders as $order) {
            $order_number = $order->order_number;
            if (!preg_match('/\d-\w+$/', $order_number)) {
               continue;
            }
            $cacheKey = md5('process_order_pending_paypal_payment_' . $order->id);
            if ($cache->has($cacheKey)) {
                continue;
            }
            $cache->put($cacheKey, $order->id, CacheKeys::CACHE_1H);
            $order_id = $order->getIdFromOrderNumber();
            $paymentLog = PgWebhookLogs::query()
                ->whereNotNull('order_id')
                ->where('order_id', $order_id)
                ->whereIn('event_type', ['PAYMENT.CAPTURE.COMPLETED', 'WEBHOOK.PAYMENT.CAPTURE.COMPLETED', 'VERIFY_PURCHASE.CAPTURE.COMPLETED'])
                ->orderByDesc('created_at')
                ->first();
            if (!$paymentLog) {
                continue;
            }

            $payment_gateway_id = (int)$order->payment_gateway_id;
            $actual_payment_gateway_id = (int)$paymentLog->payment_gateway_id;
            $payment = json_decode($paymentLog->payload, false);
            $transaction_id = data_get($payment, 'resource.id', data_get($payment, 'capture.purchase_units.0.payments.captures.0.id'));
            $total_amount = (float)$order->total_amount;
            $total_paid = (float)data_get($payment, 'resource.amount.value', data_get($payment, 'capture.purchase_units.0.payments.captures.0.amount.value'));
            if (empty($transaction_id) || empty($total_paid)) {
                continue;
            }
            if (empty($actual_payment_gateway_id)) {
                $payee_email = data_get($payment, 'resource.payee.email_address', data_get($payment, 'orderDetail.purchase_units.0.payee.email_address'));
                if (!empty($payee_email)) {
                    $actual_payment_gateway_id = cacheAlt()->remember(md5($payee_email . '-' . PaymentMethodEnum::PAYPAL), CacheTime::CACHE_24H, function () use ($payee_email) {
                        return (int)PaymentGateway::query()->select('id')->where([
                            'gateway' => PaymentMethodEnum::PAYPAL,
                            'account_id' => $payee_email
                        ])->value('id');
                    });
                }
            }
            $message = "Order #{$order->id} has not been paid, but a successful payment transaction has been recorded on the gateway." . PHP_EOL;
            $message .= 'Payment method: ' . PaymentMethodEnum::PAYPAL . PHP_EOL;
            $message .= 'Total amount: ' . $total_amount . PHP_EOL;
            $message .= 'Total received amount: ' . $total_paid . PHP_EOL;
            if (!empty($payment_gateway_id)) {
                $message .= 'Current PGW ID: ' . $payment_gateway_id . PHP_EOL;
            }
            if ($payment_gateway_id !== $actual_payment_gateway_id) {
                $message .= 'Actual PGW ID: ' . $actual_payment_gateway_id . PHP_EOL;
                $order->payment_gateway_id = $actual_payment_gateway_id;
            }
            if (!empty($order->transaction_id)) {
                $message .= 'Current Transaction ID: ' . $order->transaction_id . PHP_EOL;
            }
            if ($order->transaction_id !== $transaction_id) {
                $message .= 'Actual Transaction ID: ' . $transaction_id . PHP_EOL;
                $order->transaction_id = $transaction_id;
            }
            $color = '15548997';
            if ($total_paid === $total_amount) {
                if ($order->isDirty()) {
                    $order->save();
                }
                $order->paymentCompleted($order->total_amount, $order->transaction_id, false, null, null, $order->payment_gateway_id);
                $message .= 'Order has been completed.' . PHP_EOL;
                $color = '5763719';
            }
            $this->logToDiscord($order->id, $message, $color);
        }
        return 1;
    }
}

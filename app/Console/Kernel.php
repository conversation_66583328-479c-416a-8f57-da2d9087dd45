<?php

namespace App\Console;

use App\Enums\EnvironmentEnum;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    private const DEFAULT_TIMEZONE = 'Asia/Ho_Chi_Minh';

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');
        require base_path('routes/console.php');
    }

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        if (config('senprints.schedule_enabled')) {
            $schedule->command('schedule-health')->everyMinute()->logAfter();
            // Notify top up balance for seller who has not enough balance to fulfill order
            $schedule->command('fulfill:notify-top-up-balance')->dailyAt('09:00')->timezone(self::DEFAULT_TIMEZONE)->withoutOverlapping(10)->logAfter();
            $schedule->command('sync:stats-order')->everyMinute()->withoutOverlapping(5)->logAfter();
            $schedule->command('product:update-score')->hourly()->runInBackground()->withoutOverlapping(10)->logAfter();
            $schedule->command('campaigns:archived')->dailyAt('00:30')->withoutOverlapping(10)->logAfter();
            $schedule->command('sync:products-to-elasticsearch')->everyMinute()->withoutOverlapping(10)->logAfter();
            $schedule->command('sync:products-sharding-to-elasticsearch')->everyMinute()->withoutOverlapping(10)->logAfter();
            $schedule->command('sync:products-mysql-singlestore')->everyMinute()->withoutOverlapping(5)->logAfter();
            $schedule->command('sync:orders-to-singlestore')->everyMinute()->withoutOverlapping(5)->logAfter();
            // new abandoned cart find
            $schedule->command('abandoned:run')->everyMinute()->withoutOverlapping(10)->logAfter();
            $schedule->command('process-fulfill-orders')->everyFiveMinutes()->withoutOverlapping(10)->logAfter();
            // validate fulfill orders after import 2 mins but not get validated
            $schedule->command('revalidate-fulfill-order')->everyMinute()->withoutOverlapping(5)->logAfter();
            $schedule->command('process-custom-orders')->everyMinute()->withoutOverlapping(5)->logAfter();
            // correct order data
            $schedule->command('order:correct-data')->everyMinute()->withoutOverlapping(5)->logAfter();
            // create send mail review request job
            $schedule->command('product-review:create-review-request-job')->runInBackground()->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            // Register tracking code on the 17 Track
            $schedule->command('delivery-tracking:register-tracking-code')->everyMinute()->withoutOverlapping(5)->logAfter();
            // Update delivery tracking status`
            $schedule->command('delivery-tracking:update-tracking-status')->everyMinute()->withoutOverlapping(5)->logAfter();
            // Update delivery tracking status has exceeded sixty days
            $schedule->command('delivery-tracking:update-tracking-status-exceeded-sixty-days')->everyFifteenMinutes()->runInBackground()->withoutOverlapping(5)->logAfter();

            if (POMassPayOutIsEnabled() && POMassPayOutScheduleEnabled()) {
                // Process payout with payoneer
                $schedule->command('po-payout:process')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            }

            $schedule->command('create-crawl-email-avatar-job')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            // Check the transaction time of top-up has more than 24h
            $schedule->command('top-up:check24h')->hourly()->withoutOverlapping(5)->logAfter();
            // crawl currency rate
            $schedule->command('currency:crawl')->hourly()->withoutOverlapping(5)->logAfter();
            $schedule->command('system-data:clean')->hourly()->withoutOverlapping(5)->logAfter();
            $schedule->command('system-data:validate')->everyMinute()->runInBackground()->withoutOverlapping(5)->logAfter();
            $schedule->command('system-clean-products')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();

            if (app()->environment(EnvironmentEnum::PRODUCTION)) {
                // update order fulfill status
                $schedule->command('order-fulfill:post')->everyMinute()->withoutOverlapping(10)->logAfter();
                $schedule->command('order-fulfill:update')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
                $schedule->command('order-fulfill:update-via-webhook')->everyThreeHours()->logAfter();
                $schedule->command('system-data:update')->everyMinute()->withoutOverlapping(5)->logAfter();
                // weekly report
                $schedule->command('dev-report')->timezone(self::DEFAULT_TIMEZONE)->weeklyOn(5, '21:00')->logAfter();
                $schedule->command('dev-report --monthly')->timezone(self::DEFAULT_TIMEZONE)->lastDayOfMonth('21:00')->logAfter();

                $schedule->command('paypal-verify-pending-order')->everyTenMinutes()->withoutOverlapping(5)->logAfter();
                $schedule->command('check-paypal-gateway')->everyTenMinutes()->withoutOverlapping(5)->logAfter();
                $schedule->command('update-store-domain-expiration-date')->dailyAt('13:00')->timezone(self::DEFAULT_TIMEZONE)->withoutOverlapping(5)->logAfter();
                $schedule->command('update-seller-domain-expiration-date')->dailyAt('13:00')->timezone(self::DEFAULT_TIMEZONE)->withoutOverlapping(5)->logAfter();
                $schedule->command('check-today-sales')->dailyAt('00:00')->timezone(self::DEFAULT_TIMEZONE)->withoutOverlapping(5)->logAfter();
            }

            $schedule->command('payment:refund-to-gateway')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            $schedule->command('pingpongx:process-payout')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            $schedule->command('paypal:process-payout')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            $schedule->command('lianlian-payout')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();

            $schedule->command('renew-seller-domains')->dailyAt('00:00')->timezone(self::DEFAULT_TIMEZONE)->withoutOverlapping(5)->logAfter();
            $schedule->command('reconfirm-invalid-address')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            $schedule->command('order:send-email-cancel-order')->everyMinute()->withoutOverlapping(10)->logAfter();
            $schedule->command('order:refund')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            $schedule->command('app:send-custom-email-template')->everyMinute()->withoutOverlapping(10)->logAfter();
            $schedule->command('disposable:update')->weekly()->logAfter();
            $schedule->command('copyright:scan:start')->everyMinute()->runInBackground()->withoutOverlapping(10)->logAfter();
            $schedule->command('sms:resend')->everyMinute()->withoutOverlapping(10)->logAfter();
            $schedule->command('resend-contact-form-to-crisp')->everyFiveMinutes()->runInBackground()->withoutOverlapping(10)->logAfter();

            //inactive campaign & inactive user last login
            $schedule->command('scan:users-inactive-campaigns')->everyFifteenMinutes()->withoutOverlapping(10)->logAfter();
            $schedule->command('scan:users-inactive-last-login')->everyFifteenMinutes()->withoutOverlapping(10)->logAfter();
            $schedule->command('scan:delete-inactive-campaigns')->everyThirtyMinutes()->withoutOverlapping(10)->logAfter();
            $schedule->command('scan:delete-inactive-last-login')->everyThirtyMinutes()->withoutOverlapping(10)->logAfter();
            $schedule->command('scan:delete-user-clean-settings')->everyThirtyMinutes()->withoutOverlapping(10)->logAfter();
            $schedule->command('scan:expiring-domains')->dailyAt('00:30')->withoutOverlapping(10)->logAfter();

            $schedule->command('resend-failed-or-pending-mail')->everyFiveMinutes()->withoutOverlapping(10)->logAfter();
            $schedule->command('dmca_violation:check')->everyMinute()->withoutOverlapping(10)->logAfter();
            $schedule->command('check:longqueries')->everyMinute()->withoutOverlapping(1)->runInBackground()->logAfter();
            $schedule->command('abandoned:orders')->everyMinute()->withoutOverlapping(2)->logAfter();
            $schedule->command('fulfill-item:notify')->everyThirtyMinutes()->withoutOverlapping(10)->logAfter();

            $schedule->command('sync:supplier-oos')->hourly()->withoutOverlapping()->logAfter();
            $schedule->command('notify:order-cross-shipping')->everyMinute()->withoutOverlapping()->logAfter();
            $schedule->command('scan:create-next-targets')->monthlyOn(1, '00:15')->timezone(self::DEFAULT_TIMEZONE)->withoutOverlapping(10)->logAfter();
            $schedule->command('fill-orders-lost-transaction-id')->dailyAt('00:30')->timezone(self::DEFAULT_TIMEZONE)->withoutOverlapping()->logAfter();

            // https://laravel.com/docs/10.x/upgrade#redis-cache-tags
            $schedule->command('cache:prune-stale-tags')->hourly()->logAfter();
            $schedule->command('scan:shipping-late:register-tracking-code')->everyFifteenMinutes()->runInBackground()->logAfter();
            $schedule->command('scan:on-delivery-fulfilled-over-90-days')->everyTenMinutes()->withoutOverlapping(5)->logAfter();
            $schedule->command('scan:order-to-notify-supplier')->everyFourHours()->withoutOverlapping(30)->logAfter();
            $schedule->command('download-social-feed-images')->everyMinute()->withoutOverlapping(1)->logAfter();
            $schedule->command('orders:render-design')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            $schedule->command('active-social-feed')->everyMinute()->withoutOverlapping(5)->logAfter();
            $schedule->command('scan:estimate-delivery-date')->everyFiveMinutes()->withoutOverlapping(5)->logAfter();
            $schedule->command('order:complete-pending-payment')->everyMinute()->withoutOverlapping(5)->logAfter();
            $schedule->command('resync:campaigns-to-elastic')->everyTenMinutes()->runInBackground()->withoutOverlapping(5)->logAfter();
            $schedule->command('resend-order-confirmation-mail')->everyMinute()->withoutOverlapping(5)->logAfter();
            $schedule->command('app:migrate-user-to-customer')->everyMinute()->withoutOverlapping(1)->logAfter();
            // Check SES identity status
            $schedule->command('marketing:sender:status')->everyFiveMinutes()->runInBackground()->withoutOverlapping(5)->logAfter();
            $schedule->command('clean-index-event-logs')->dailyAt('00:00')->timezone(self::DEFAULT_TIMEZONE)->logAfter();

            $schedule->command('update-social-feed-command')->dailyAt('00:00')->timezone(self::DEFAULT_TIMEZONE)->logAfter();
            $schedule->command('scan-send-order-late-shipping-for-supplier')->dailyAt('02:00')->logAfter();
            $schedule->command('scan-order-no-ship-by-lawyer-address')->everyMinute()->withoutOverlapping(1)->logAfter();
        }
    }
}

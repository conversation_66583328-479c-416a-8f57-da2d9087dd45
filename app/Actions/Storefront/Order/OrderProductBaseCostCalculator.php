<?php

namespace App\Actions\Storefront\Order;

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\SystemLocation;
use App\Models\Template;
use Illuminate\Support\Arr;
use Throwable;

class OrderProductBaseCostCalculator
{
    /**
     * @throws \Throwable
     */
    public function handle(Product|Template $tplProduct, string $variantKey, ?SystemLocation $orderLocation = null): ?string
    {
        $variant = $this->getTemplateVariant(
            $tplProduct->id, $variantKey, $this->toRegionCodes($orderLocation)
        );

        return $variant->base_cost ?? $this->getBaseCostFromTemplate(
            $tplProduct->base_cost, $tplProduct->base_costs, $orderLocation
        );
    }

    /**
     * @param     \App\Models\SystemLocation|null     $location
     *
     * @return string[]
     */
    private function toRegionCodes(?SystemLocation $location = null): array
    {
        return $location ? $location->getRegionCodes() : ['*'];
    }

    /**
     * @param     string|null                         $configString
     * @param     \App\Models\SystemLocation|null     $location
     * @param                                         $default
     *
     * @return array|\ArrayAccess|mixed
     * @throws Throwable
     */
    private function getBaseCostFromTemplate($default, ?string $configString, ?SystemLocation $location = null)
    {
        $config = json_decode(
            $configString ?: '', true, 512, JSON_THROW_ON_ERROR
        ) ?? [];

        if ($config && $location) {
            if ( ! is_null($result = Arr::get($config, $location->code))) {
                return $result;
            }

            if ( ! is_null($result = Arr::get($config, $location->sub_region_code))) {
                return $result;
            }

            if ( ! is_null($result = Arr::get($config, $location->intermediate_region_code))) {
                return $result;
            }

            if ( ! is_null($result = Arr::get($config, $location->region_code))) {
                return $result;
            }
        }

        return $default;
    }

    /**
     * @param                $tplProductId
     * @param     string     $variantKey
     * @param     array      $regionCodes
     *
     * @return mixed
     */
    private function getTemplateVariant($tplProductId, string $variantKey, array $regionCodes)
    {
        return ProductVariant::findAndCacheByTemplate($tplProductId)

            // Lấy ra các variant có variant_key và location_code trùng
            // với variant_key và region_code của sản phẩm trên đơn hàng
            ->filter(fn($row) => $row->variant_key === $variantKey
                && in_array($row->location_code, $regionCodes, true))

            // Sắp xếp theo thứ tự của region_code trên đơn hàng
            ->sortBy(fn ($row) => array_search($row['location_code'], $regionCodes, true))

            // Lấy ra variant đầu tiên
            ->first();
    }
}

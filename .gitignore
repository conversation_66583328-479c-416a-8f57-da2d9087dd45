/node_modules
/public/hot
/public/storage
/storage/*.key
/vendor
/.idea
.env
.env.backup
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
composer.lock
supervisord*.log
supervisord*.pid
yarn.lock
_ide_helper_models.php
.phpstorm.meta.php
_ide_helper.php
.php_cs.cache
/.phpintel
package-lock.json

### VisualStudioCode ###
.vs/*
.vscode/*
!.vscode/tasks.json
.vscode/launch.json
*.code-workspace

### VisualStudioCode Patch ###
# Ignore all local history of files
.history
.ionide

# End of https://www.toptal.com/developers/gitignore/api/visualstudiocode
public/vendor/telescope/app-dark.css
public/vendor/telescope/app.css
public/vendor/telescope/app.js
public/vendor/telescope/mix-manifest.json
*.tmp

# Custom
/public/customer.csv
/nbproject/private/
config/ray.php
/bitbucket-pipelines.yml

**/.DS_Store
/nbproject
.devcontainer/devcontainer.json
/docker/db/resource
/storage/framework/laravel-excel
rename_css.py
.lando.yml

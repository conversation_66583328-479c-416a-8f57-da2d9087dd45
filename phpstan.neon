includes:
    - ./vendor/nunomaduro/larastan/extension.neon

parameters:

    paths:
        - app

    # The level 8 is the highest level
    level: 6

    ignoreErrors:
        - '#Unsafe usage of new static#'
        - '#PHPDoc tag @param has invalid value#'
        - '#Access to an undefined property#'
        - '#typehint specified#'
        - '#Call to an undefined static method#'
        - '#Call to private method#'
        - '#Call to an undefined method .*addFilterAnalytic#'
        - '#Property .+ does not accept default value of type#'
        - '#Call to an undefined method Illuminate\\Database\\Eloquent#'

    excludePaths:
        - ./app/Traits/HasCompositePrimaryKey.php
        - ./app/Traits/ElasticClient.php
        - ./app/Repositories/*

    checkMissingIterableValueType: false

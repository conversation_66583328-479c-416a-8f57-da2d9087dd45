{"name": "babe<PERSON><PERSON>n/scout-elasticsearch-driver", "type": "library", "description": "The Elasticsearch Driver for Laravel Scout", "version": "4.3.0", "keywords": ["elastic", "elasticsearch", "driver", "engine", "laravel", "scout", "search"], "homepage": "https://babenkoivan.github.io/scout-elasticsearch-driver/", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^7.4|^8.0", "elasticsearch/elasticsearch": ">=7.0 <=7.17.16", "laravel/scout": "^7.0|^8.0|^9.0|^10.0"}, "require-dev": {"phpunit/phpunit": "^7.0|^8.0|^9.0|^10.0", "mockery/mockery": "^1.0"}, "autoload": {"psr-4": {"ScoutElastic\\Tests\\": "tests/", "ScoutElastic\\": "src/"}}, "extra": {"laravel": {"providers": ["ScoutElastic\\ScoutElasticServiceProvider"]}}}
name: Tests

on: [ push, pull_request ]

jobs:
  test:
    runs-on: ubuntu-18.04
    strategy:
      matrix:
        php: [ 7.1, 7.2, 7.3, 7.4, 8.0 ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Install php and composer
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          coverage: none
          tools: composer:v2

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Restore composer cache
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: composer install --no-interaction --prefer-dist

      - name: Run tests
        run: vendor/bin/phpunit


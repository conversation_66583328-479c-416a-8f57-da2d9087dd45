{"name": "swayok/alternative-laravel-cache", "type": "library", "description": "Replacements for Laravel's redis and file cache stores that properly implement tagging idea. Powered by cache pool implementations provided by http://www.php-cache.com/", "keywords": ["php", "laravel", "cache", "redis cache", "tagged cache", "redis tagged cache"], "authors": [{"name": "<PERSON>"}], "license": "MIT", "support": {"issues": "https://github.com/swayok/alternative-laravel-cache/issues"}, "autoload": {"psr-4": {"AlternativeLaravelCache\\": "AlternativeLaravelCache/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "require": {"php": ">=8.0", "laravel/framework": ">=6.0", "cache/adapter-common": "*", "cache/hierarchical-cache": "*"}, "require-dev": {"cache/redis-adapter": "*", "cache/predis-adapter": "*", "swayok/cache-filesystem-adapter": "*", "cache/memcached-adapter": "*", "phpunit/phpunit": "^10.5"}, "suggest": {"cache/redis-adapter": "Required to use Redis-based cache through php-redis extension (recommended - faster and more stable then predis)", "cache/predis-adapter": "Required to use Redis-based cache through predis/predis package", "swayok/cache-filesystem-adapter": "Required to use file-based cache", "cache/memcached-adapter": "Required to use Memcache-based cache"}, "extra": {"laravel": {"providers": ["AlternativeLaravelCache\\Provider\\AlternativeCacheStoresServiceProvider"]}}}
FROM php:8.1-fpm-alpine as build

ENV COMPOSER_MEMORY_LIMIT=-1

RUN apk update && apk add --no-cache --virtual build-dependencies build-base curl git openssh-client openssl-dev autoconf postgresql-dev freetype-dev libzip-dev libpng-dev libjpeg-turbo-dev libmemcached-dev libwebp-dev icu-dev \
    shadow supervisor \
    && pecl install mongodb \
    && pecl install redis \
    php8.1-common \
    php8.1-pdo \
    php8.1-pdo_mysql \
    php8.1-mysqli \
    php8.1-pgsql \
    php8.1-mcrypt \
    php8.1-mbstring \
    php8.1-xml \
    php8.1-openssl \
    php8.1-json \
    php8.1-phar \
    php8.1-zip \
    php8.1-gd \
    php8.1-imagick \
    php8.1-dom \
    php8.1-session \
    php8.1-curl \
    php8.1-zlib \
    memcached \
    && docker-php-ext-enable memcached \
    && docker-php-ext-configure pgsql -with-pgsql=/usr/local/pgsql \
    && docker-php-ext-install pdo pdo_mysql pdo_pgsql \
    && docker-php-ext-install sockets \
    && docker-php-ext-configure zip \
    && docker-php-ext-install zip \
    && docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp \
    && docker-php-ext-install -j "$(nproc)" gd \
    && docker-php-ext-enable pdo \
    && docker-php-ext-enable pdo_mysql \
    && docker-php-ext-enable pdo_pgsql \
    && docker-php-ext-enable mongodb \
    && docker-php-ext-enable redis \
    && docker-php-ext-configure intl \
    && docker-php-ext-install intl \
    && docker-php-ext-enable intl \
    && docker-php-ext-install opcache \
    && set -ex \
    && apk add --no-cache --virtual .phpize-deps $PHPIZE_DEPS imagemagick-dev libtool \
    && export CFLAGS="$PHP_CFLAGS" CPPFLAGS="$PHP_CPPFLAGS" LDFLAGS="$PHP_LDFLAGS" \
    && pecl install imagick \
    && docker-php-ext-enable imagick \
    && apk add --no-cache --virtual .imagick-runtime-deps imagemagick \
    && apk del .phpize-deps \
    && rm -rf /var/cache/apk/* \
    && curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

ADD ./docker/supervisord.conf /etc/supervisord.conf
ADD ./docker/supervisor.d /etc/supervisor.d

RUN mkdir /var/run/supervisord \
    && chown www-data:root /var/run/supervisord

FROM build as base

COPY --from=composer:2 /usr/bin/composer /usr/bin/composer

ADD ./docker/php/conf.d /usr/local/etc/php/conf.d
ADD ./docker/php/php.ini /usr/local/etc/php/php.ini
ADD ./docker/php-fpm.d/www.conf /usr/local/etc/php-fpm.d/www.conf

ADD ./docker/entrypoint.sh /usr/local/bin/docker-entrypoint.sh
ADD ./docker/crontab/root /etc/crontabs/root
ADD ./docker/script/startsupervisor.sh /usr/local/bin/startsupervisor
ADD ./docker/script/rmlaravelworker.sh /usr/local/bin/rmlaravelworker

RUN chmod 777 /usr/local/bin/docker-entrypoint.sh \
    && ln -s /usr/local/bin/docker-entrypoint.sh \
    && chmod 777 /usr/local/bin/startsupervisor \
    && chmod 777 /usr/local/bin/rmlaravelworker

WORKDIR /var/www/app

COPY ./modules ./modules
COPY ./packages ./packages

COPY ./composer.* ./

RUN composer install \
        --no-ansi \
        --no-autoloader \
        --no-interaction \
        --no-scripts
COPY . .

RUN  composer dump-autoload --optimize --classmap-authoritative \
    && composer clear-cache

RUN chmod 777 -R /var/www/app/storage \
    && chmod 777 -R /var/www/app/bootstrap/cache

ENTRYPOINT ["sh"]

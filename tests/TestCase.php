<?php

namespace Tests;

use App\Models\Campaign;
use App\Models\File;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShippingRule;
use App\Models\Store;
use App\Models\Template;
use App\Models\User;
use App\Services\SessionService;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\DB;
use Tymon\JWTAuth\Facades\JWTFactory;
use Tymon\JWTAuth\Payload;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected float $startTime;

    protected function getToken(User $user, $service_name = 'admin', $guard_name = 'user'): string
    {
        app(SessionService::class)->flushSessions($user);

        $payload = JWTFactory::sub($user->id)
            ->email($user->email)
            ->service_name($service_name)
            ->guard_name($guard_name)
            ->customClaims([
                'tfa_confirm' => false,
                'session_token' => app(SessionService::class)->createSession($user),
            ])
            ->make();

        return jwtauth_encode($payload);
    }

    protected function actingAsSeller(User $seller): self
    {
        $token = $this->getToken($seller, 'seller', 'seller');

        $this->withToken($token);

        return $this;
    }

    protected function actingAsSellerExternal(User $seller): self
    {
        $token = $this->getToken($seller, 'seller');

        $response = $this
            ->withToken($token)
            ->postJson(route('api.apikey.generate', [
                'type' => 'seller',
            ]));

        $apiKey = $response->json('data.apikey');

        $this->withToken($apiKey);

        return $this;
    }

    protected function addXDomainHeader(?Store $store = null): self
    {
        $store ??= Store::find(Store::SENPRINTS_STORE_ID);
        $this->withHeaders([
            'X-Domain' => $store->domain,
        ]);

        return $this;
    }

    public function listenQuery($checkTrace = false): void
    {
        $this->startTime = microtime(true);
        $count = 0;

        DB::listen(function ($query) use ($checkTrace, &$count) {
            $arr = [];
            $sql = $query->sql;

            foreach ($query->bindings as $binding) {
                $value = is_numeric($binding) ? $binding : "'" . $binding . "'";
                $sql = preg_replace('/\?/', $value, $sql, 1);
            }

            $arr['sql'] = $sql;
            $arr['time_ms'] = $query->time; // Thời gian thực thi truy vấn (ms)
            $arr['elapsed_time_s'] = round(
                microtime(true) - $this->startTime,
                4
            ); // Thời gian từ lúc bắt đầu tiến trình

            if ($checkTrace) {
                // Lấy stack trace
                $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 50);
                $relevantTrace = [];

                foreach ($trace as $traceLine) {
                    if (isset($traceLine['file']) && !str_contains($traceLine['file'], 'vendor')) {
                        $relevantTrace[] = [
                            'file' => $traceLine['file'],
                            'line' => $traceLine['line'],
                            'function' => $traceLine['function'],
                        ];
                    }
                }

                $arr['trace'] = $relevantTrace;
            }

            dump($arr);
            dump(++$count);
        });
    }

    public function createTemplateProduct(): Template
    {
        return Template::factory()->create();
    }

    public function createVariant(?Template $template = null, ?Product $product = null): ProductVariant
    {
        $factory = ProductVariant::factory();
        if ($template) {
            $factory = $factory->forTemplate($template);
        }

        if ($product) {
            $factory = $factory->forProduct($product);
        }

        return $factory->create();
    }

    public function createCampaign(User $seller): Campaign
    {
        return Campaign::factory()->forSeller($seller)->create();
    }

    public function createProduct(?User $seller = null, ?Campaign $campaign = null): Product
    {
        $factory = Product::factory();
        if ($seller) {
            $factory = $factory->forSeller($seller);
        }

        if ($campaign) {
            $factory = $factory->forCampaign($campaign);
        }

        return $factory->create();
    }

    public function createFile(?Product $product = null, ?User $seller = null): File
    {
        $factory = File::factory();
        if ($seller) {
            $factory = $factory->forSeller($seller);
        }

        if ($product) {
            $factory = $factory->forProduct($product);
        }

        return $factory->create();
    }

    public function createShippingRule(Product $product): ShippingRule
    {
        return ShippingRule::factory()->forProduct($product)->create();
    }
}

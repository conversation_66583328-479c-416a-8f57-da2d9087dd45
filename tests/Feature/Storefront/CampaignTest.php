<?php

namespace Tests\Feature\Storefront;

use App\Enums\CampaignStatusEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\UserStatusEnum;
use App\Models\Campaign;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Store;
use App\Models\User;
use Tests\TestCase;

class CampaignTest extends TestCase
{
    public function test_get_campaign(): void
    {
        $orderProduct = OrderProduct::query()
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->where('order.type', OrderTypeEnum::REGULAR)
            ->where('order.store_id', '!=', Store::SENPRINTS_STORE_ID)
            ->where('order.seller_id', '!=', User::SENPRINTS_SELLER_ID)
            ->where('order.payment_status', OrderPaymentStatus::PAID)
            ->where('order_product.fulfill_status', OrderProductFulfillStatus::UNFULFILLED)
            ->whereNotNull('order_product.campaign_id')
            ->latest('order_product.updated_at')
            ->limit(1)
            ->first();

        if (!$orderProduct->campaign_id) {
            $this->fail('No campaign id found');
        }
        $seller = Order::find($orderProduct->order_id)->seller;

        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->where([
                'id' => $orderProduct->campaign_id,
                'status' => CampaignStatusEnum::ACTIVE
            ])
            ->with('seller:id,status')
            ->first(['id', 'slug']);

        if (!$campaign) {
            $this->fail('Campaign not found');
        }

        // ref: app/Http/Controllers/Storefront/CampaignController.php:324
        if ($campaign->seller->status !== UserStatusEnum::TRUSTED) {
            $this->markTestSkipped('Seller is not trusted');
        }

        $url = route('public.campaign.show', [
            'slug' => $campaign->slug
        ]);

        $response = $this->get($url);

        echo 'Campaign URL: ' . $url . PHP_EOL;

        $response
            ->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => ['id', 'name', 'slug', 'description', 'images', 'products'],
                'message'
            ])
            ->assertJson([
                'success' => true,
                'data' => ['slug' => $campaign->slug]
            ]);
    }
}

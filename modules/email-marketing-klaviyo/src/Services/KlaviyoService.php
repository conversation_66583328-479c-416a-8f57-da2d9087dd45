<?php

namespace Modules\EmailMarketingKlaviyo\Services;

use App\Models\Store;
use GuzzleHttp\Exception\GuzzleException;
use InvalidArgumentException;
use KlaviyoAPI\ApiException;
use KlaviyoAPI\KlaviyoAPI;
use Modules\EmailMarketingKlaviyo\Traits\ParseResponseTrait;
use Modules\EmailMarketingKlaviyo\Services\StoreService;
use Illuminate\Support\Collection;
use Modules\EmailMarketingKlaviyo\Enums\SpawnBulkProfileStatus;
use App\Models\Order;
use Modules\OrderService\Models\RegionOrders;
use App\Enums\OrderTypeEnum;
use Exception;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoActiveOnSiteJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoViewedProductJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoViewedCollectionJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoAddedToCartJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoDeliveredShipmentJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoMarkedOutForDeliveryJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoOrderCanceledJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoStartedCheckoutJob;
use Modules\EmailMarketingKlaviyo\Listeners\KlaviyoOrderPlacedListener;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoOrderPlacedJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoOrderRefundedJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoSubmittedSearchJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoAbandonedCartJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoFulfilledOrderJob;
use Modules\EmailMarketingKlaviyo\Jobs\Demo\KlaviyoDemoFulfilledPartialOrderJob;
use PHPUnit\Event\Code\Throwable;

class KlaviyoService
{
    private KlaviyoAPI $klaviyo;
    private StoreService $storeService;
    private Store $store;
    private string $privateKey;
    private ?int $storeId;
    private int $page;
    private int $limit;

    public function __construct(array $options = [])
    {
        $this->limit = $options['limit'] ?? 1000;
        $this->privateKey = $options['private_key'] ?? null;
        $this->klaviyo = new KlaviyoAPI(
            $this->privateKey,
            guzzle_options: [
                'verify' => false,
            ],
        );
        $this->storeService = new StoreService();
    }

    /**
     * Initialize metrics
     * @return void
     */
    public function initMetrics(?int $storeId = null, $sellerId = null): void
    {
        $this->storeId = $storeId;
        // Active on Site
        KlaviyoDemoActiveOnSiteJob::dispatch($this->storeId, $sellerId);
        // Viewed Product
        KlaviyoDemoViewedProductJob::dispatch($this->storeId, $sellerId);
        // Viewed Collection
        KlaviyoDemoViewedCollectionJob::dispatch($this->storeId, $sellerId);
        // Added to Cart
        KlaviyoDemoAddedToCartJob::dispatch($this->storeId, $sellerId);
        // Started Checkout
        KlaviyoDemoStartedCheckoutJob::dispatch($this->storeId, $sellerId);
        // Placed Order
        KlaviyoDemoOrderPlacedJob::dispatch($this->storeId, $sellerId);
        // Cancelled Order
        KlaviyoDemoOrderCanceledJob::dispatch($this->storeId, $sellerId);
        // Refunded Order
        KlaviyoDemoOrderRefundedJob::dispatch($this->storeId, $sellerId);
        // Submitted Search
        KlaviyoDemoSubmittedSearchJob::dispatch($this->storeId, $sellerId);
        // Delivered Shipment
        KlaviyoDemoDeliveredShipmentJob::dispatch($this->storeId, $sellerId);
        // Marked Out For Delivery
        KlaviyoDemoMarkedOutForDeliveryJob::dispatch($this->storeId, $sellerId);
        // Abandoned Cart
        KlaviyoDemoAbandonedCartJob::dispatch($this->storeId, $sellerId);
        // Fulfilled Order
        KlaviyoDemoFulfilledOrderJob::dispatch($this->storeId, $sellerId);
        // Fulfilled Partial Order
        KlaviyoDemoFulfilledPartialOrderJob::dispatch($this->storeId, $sellerId);
    }

    /**
     * Set store
     *
     * @param Store $store
     * @return $this
     */
    public function setStore(Store $store): static
    {
        $this->store = $store;
        return $this;
    }

    /**
     * Set store id
     *
     * @param int $storeId
     * @return $this
     */
    public function fetchStore(int $storeId): static
    {
        $this->storeService = $this->storeService->setStoreId($storeId);
        $this->store = $this->storeService->getStore();
        return $this;
    }

    public function withPage(int $page): static
    {
        $this->page = $page;
        return $this;
    }

    /**
     * Import profiles to Klaviyo
     *
     * @return array
     */
    public function buildJsonSpawnBulkProfileImport(): array
    {
        $customers = $this->storeService->getCustomers($this->page, $this->limit);
        if ($customers->isEmpty()) {
            return [];
        }
        $profiles = $customers->map(function ($customer) {
            return [
                "type" => "profile",
                "attributes" => $customer->toArray(),
            ];
        });
        return [
            'data' => [
                'type' => 'profile-bulk-import-job',
                'attributes' => [
                    'profiles' => [
                        'data' => array_values($profiles->toArray()),
                    ],
                ],
            ]
        ];
    }

    /**
     * Spawn bulk profile import
     *
     * @return string
     * @throws InvalidArgumentException
     */
    public function sendSpawnBulkProfileImportRequest(): string
    {
        $jsonData = $this->buildJsonSpawnBulkProfileImport();
        if (empty($jsonData)) {
            return SpawnBulkProfileStatus::FINISHED;
        }
        $this->klaviyo->Profiles->spawnBulkProfileImportJob($jsonData);
        return SpawnBulkProfileStatus::SUCCESS;
    }

    /**
     * Check if order type is allowed
     *
     * @param Order|RegionOrders $order
     * @return bool
     */
    public static function isOrderTypeAllowed(Order|RegionOrders $order): bool
    {
        return in_array($order->type, [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM], true) && !$order->isCustomServiceOrder();
    }

    /**
     * Check if key is valid
     * @param ?string $privateKey
     * @return bool
     */
    public static function isKeyValid(?string $privateKey): bool
    {
        try {
            $klaviyo = new KlaviyoAPI($privateKey, guzzle_options: [
                'timeout' => 10,
                'verify' => false,
                'http_errors' => false
            ]);
            $klaviyo->Accounts->getAccounts();
            return true;
        } catch (Exception $e) {
            graylogError('KlaviyoService isKeyValid failed', [
                'category' => 'email-marketing-klaviyo',
                'data' => $privateKey,
            ]);
            return false;
        }
    }

    /**
     * Create a new list
     * @param string $name
     * @param string $privateKey
     * @return string
     * @throws Exception
     */
    public function createList(string $name, string $privateKey = ''): string
    {
        if (!empty($privateKey)) {
            $apiKey = $privateKey;
        } else {
            $apiKey = $this->privateKey;
        }

        // Create a new list
        try {
            $klaviyo = new KlaviyoAPI($apiKey, guzzle_options: [
                'verify' => false,
            ]);

            $response = $klaviyo->Lists->createList([
                'data' => [
                    'type' => 'list',
                    'attributes' => [
                        'name' => $name,
                    ],
                ]
            ]);

            if (!isset($response['data']['id'])) {
                throw new Exception('Failed to create Klaviyo list');
            }
            return $response['data']['id'];
        } catch (ApiException $e) {
            graylogError('Failed to create Klaviyo list', [
                'category' => 'email-marketing-klaviyo',
                'name' => $name,
                'error' => $e->getResponseBody()
            ]);
        } catch (Exception $e) {
            graylogError('Failed to create Klaviyo list', [
                'category' => 'email-marketing-klaviyo',
                'name' => $name,
                'message' => $e->getMessage()
            ]);

            throw new Exception('Failed to create Klaviyo list: ' . $e->getMessage());
        }
    }

    // Get lists: https://a.klaviyo.com/api/lists?fields[flow]=&fields[list]=name,created&filter=equals%28name%2C%5B%27SenPrints%20Newsletter%27%5D%29&include=

    /**
     * Get Klaviyo lists by filter
     * @param string $filterName
     * @param string $filterValue
     * @param string $privateKey
     * @return ?array
     * @throws Exception
     */
    public function getLists(string $filterName = 'name', string $filterValue = '', string $privateKey = ''): ?array
    {
        try {
            if (!empty($privateKey)) {
                $apiKey = $privateKey;
            } else {
                $apiKey = $this->privateKey;
            }
            if (empty($apiKey)) {
                throw new InvalidArgumentException('Private key is required');
            }

            $klaviyo = new KlaviyoAPI($apiKey, guzzle_options: [
                'verify' => false,
            ]);

            $response = $klaviyo->Lists->getLists(
                filter: "equals($filterName,['" . $filterValue . "'])",
            );

            if (!isset($response['data'])) {
                return [];
            }
            $lists = $response['data'];
            if (empty($lists)) {
                return [];
            }


            return json_decode(json_encode($response['data']), true);
        } catch (ApiException $e) {
            graylogError('Failed to get Klaviyo lists', [
                'category' => 'email-marketing-klaviyo',
                'error' => $e->getResponseBody(),
            ]);
        } catch (Exception $e) {
            graylogError('Failed to get Klaviyo lists', [
                'category' => 'email-marketing-klaviyo',
                'filter_name' => $filterName,
                'filter_values' => $filterValue,
                'message' => $e->getMessage()
            ]);

            throw new Exception('Failed to get Klaviyo lists: ' . $e->getMessage());
        }
    }

    /**
     * Add a profile to a Klaviyo list/segment
     *
     * @param string $profileId
     * @param string $listId
     * @param string $privateKey
     * @return bool
     * @throws Exception
     */
    public function addProfileToList(string $profileId, string $listId, string $privateKey = ''): bool
    {
        try {
            if (!empty($privateKey)) {
                $apiKey = $privateKey;
            } else {
                $apiKey = $this->privateKey;
            }
            if (empty($apiKey)) {
                throw new InvalidArgumentException('Private key is required');
            }

            $klaviyo = new KlaviyoAPI($apiKey, guzzle_options: [
                'verify' => false,
            ]);

            $klaviyo->Lists->createListRelationshipsProfile($listId, [
                'data' => [
                    [
                        'type' => 'profile',
                        'id' => $profileId
                    ]
                ]
            ]);
            return true;
        } catch (ApiException $e) {
            graylogError('Failed to add profile to Klaviyo list', [
                'category' => 'email-marketing-klaviyo',
                'error' => $e->getResponseBody(),
            ]);
        } catch (Exception $e) {
            graylogError('Failed to add profile to Klaviyo list', [
                'category' => 'email-marketing-klaviyo',
                'profile_id' => $profileId,
                'list_id' => $listId,
                'message' => $e->getMessage()
            ]);

            throw new Exception('Failed to add profile to Klaviyo list: ' . $e->getMessage());
        }
    }

    /**
     * Get Klaviyo profiles
     * @param string $email
     * @param string $privateKey
     * @return mixed
     * @throws Exception
     */
    public function getProfiles(string $email, string $privateKey = '')
    {
        try {
            if (!empty($privateKey)) {
                $apiKey = $privateKey;
            } else {
                $apiKey = $this->privateKey;
            }
            if (empty($apiKey)) {
                throw new InvalidArgumentException('Private key is required');
            }
            $klaviyo = new KlaviyoAPI($apiKey, guzzle_options: [
                'verify' => false,
            ]);

            // $klaviyo->Profiles->getProfiles(additional_fields_profile: $additional_fields_profile, fields_profile: $fields_profile, filter: $filter, page_cursor: $page_cursor, page_size: $page_size, sort: $sort);
            $response = $klaviyo->Profiles->getProfiles(
                filter: "equals(email,'".$email."')",
            );
            if (!isset($response['data'])) {
                return [];
            }
            $profiles = $response['data'];
            if (empty($profiles)) {
                return [];
            }
            return $profiles;
        } catch (ApiException $e) {
            graylogError('Failed to get Klaviyo profile', [
                'category' => 'email-marketing-klaviyo',
                'error' => $e->getResponseBody(),
            ]);
        } catch (Exception $e) {
            graylogError('Failed to get Klaviyo profile', [
                'category' => 'email-marketing-klaviyo',
                'email' => $email,
                'message' => $e->getMessage()
            ]);

            throw new Exception('Failed to get Klaviyo profile: ' . $e->getMessage());
        }
    }

    /**
     * Create a profile in Klaviyo
     *
     * @param string $email
     * @param string $name
     * @param string $privateKey
     * @return string
     * @throws Exception
     */
    public function createProfile(string $email, string $name = '', string $privateKey = '')
    {
        try {
            if (!empty($privateKey)) {
                $apiKey = $privateKey;
            } else {
                $apiKey = $this->privateKey;
            }
            if (empty($apiKey)) {
                throw new InvalidArgumentException('Private key is required');
            }

            $klaviyo = new KlaviyoAPI($apiKey, guzzle_options: [
                'verify' => false,
            ]);

            // Create or update profile
            // {"data":{"type":"profile","attributes":{"properties":{"newKey":"New Value"},"email":"<EMAIL>"}}}
            $data = (object) [
                'type' => 'profile',
                'attributes' => (object) [
                    'email' => $email,
                    'properties' => (object) [
                        'name' => $name,
                    ]
                ]
            ];

            $response = $klaviyo->Profiles->createProfile([
                'data' => $data
            ]);

            if (!isset($response['data']['id'])) {
                throw new Exception('Failed to create or update profile in Klaviyo');
            }

            $profileId = $response['data']['id'];

            return $profileId;
        } catch (ApiException $e) {
            graylogError('Failed to create/subscribe Klaviyo profile', [
                'category' => 'email-marketing-klaviyo',
                'error' => $e->getResponseBody(),
            ]);
        } catch (Exception $e) {
            graylogError('Failed to create/subscribe Klaviyo profile', [
                'category' => 'email-marketing-klaviyo',
                'email' => $email,
                'message' => $e->getMessage()
            ]);

            throw new Exception('Failed to create/subscribe Klaviyo profile: ' . $e->getMessage());
        }
    }

    /**
     * Subscribe email to Klaviyo list
     * @param string $email
     * @param string $listId
     * @return bool
     */
    public function subscribeEmailToKlaviyoList(string $email, string $listId): bool
    {
        $this->klaviyo->Profiles->subscribeProfiles([
            'data' => [
                'type' => 'profile-subscription-bulk-create-job',
                'attributes' => [
                    'profiles' => [
                        'data' => [
                            [
                                'type' => 'profile',
                                'attributes' => [
                                    'email' => $email,
                                    'subscriptions' => [
                                        'email' => [
                                            'marketing' => [
                                                'consent' => 'SUBSCRIBED',
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'historical_import' => false
                ],
                'relationships' => [
                    'list' => [
                        'data' => [
                            'type' => 'list',
                            'id' => $listId
                        ]
                    ]
                ]
            ]
        ]);
        return true;
    }

    /**
     * Process Klaviyo API exception
     * @param ApiException|\Throwable $e
     * @param string $name
     * @param array $traceData
     * @return void
     */
    public static function processException(ApiException|\Throwable $e, $name = 'Klaviyo', $traceData = [])
    {
        if ($e instanceof ApiException) {
            $message = $e->getResponseBody();
        } else {
            $message = $e->getMessage();
        }
        if ($e->getCode() === 304) {
            graylogError($name . ' failed (private key is invalid)', [
                'category' => 'email-marketing-klaviyo',
                'message' => $message,
                'trace_data' => $traceData,
            ]);
        } else {
            graylogError($name . ' failed', [
                'category' => 'email-marketing-klaviyo',
                'message' => $message,
                'trace_data' => $traceData,
            ]);
        }
    }
}

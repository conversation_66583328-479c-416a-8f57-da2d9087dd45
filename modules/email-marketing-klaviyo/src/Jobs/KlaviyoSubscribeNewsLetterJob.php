<?php

namespace Modules\EmailMarketingKlaviyo\Jobs;

use App\Enums\CacheKeys;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Throwable;
use InvalidArgumentException;
use Psr\Container\NotFoundExceptionInterface;
use Psr\Container\ContainerExceptionInterface;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Data\ProfileData;
use Spatie\RateLimitedMiddleware\RateLimited;
use KlaviyoAPI\KlaviyoAPI;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use App\Models\Store;
use Illuminate\Support\Facades\Cache;
use Illuminate\Queue\Middleware\WithoutOverlapping;

class KlaviyoSubscribeNewsLetterJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public $storeId;
    public $email;
    public $privateKey;

    public function __construct($email, $storeId)
    {
        $this->onQueue(KlaviyoQueueName::ImportProfiles);
        $this->email = $email;
        $this->storeId = $storeId;
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    public function middleware(): array
    {
        $steadyRateLimit = (new RateLimited())
            ->allow(700)
            ->everyMinute()
            ->releaseAfterBackoff($this->attempts(), 3);
        $burstRateLimit = (new RateLimited())
            ->allow(75)
            ->everySecond()
            ->releaseAfterBackoff($this->attempts(), 3);
        $overLappingKey = KlaviyoQueueName::ImportProfiles . '|' . $this->email . '|' . $this->storeId;
        return [
            $steadyRateLimit,
            $burstRateLimit,
            (new WithoutOverlapping($overLappingKey))->dontRelease(),
        ];
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        if ($this->privateKey === "") {
            graylogError('KlaviyoSubscribeNewsLetterJob - Klaviyo private key is empty', [
                'category' => 'email-marketing-klaviyo',
            ]);
            return;
        }

        // validate email
        if (!filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
            graylogError('KlaviyoSubscribeNewsLetterJob - Invalid email', [
                'category' => 'email-marketing-klaviyo',
                'data' => [
                    'email' => $this->email,
                    'store_id' => $this->storeId,
                ],
            ]);
            $this->delete();
            return;
        }

        $store = Store::whereId($this->storeId)->first();
        if (!$store) {
            graylogError('KlaviyoSubscribeNewsLetterJob - Store not found', [
                'category' => 'email-marketing-klaviyo',
                'data' => [
                    'email' => $this->email,
                    'store_id' => $this->storeId,
                ],
            ]);
            $this->delete();
            return;
        }
        $this->privateKey = $store->klaviyo_private_key ?? $store->seller->klaviyo_private_key;
        if (empty($this->privateKey)) {
            graylogError('KlaviyoSubscribeNewsLetterJob - Klaviyo private key is empty', [
                'category' => 'email-marketing-klaviyo',
                'data' => [
                    'email' => $this->email,
                    'store_id' => $this->storeId,
                ],
            ]);
            return;
        }
        $service = new KlaviyoService([
            'private_key' => $this->privateKey,
        ]);
        $listId = $store->klaviyo_list_id ?? $store->seller->klaviyo_list_id;
        if (empty($listId)) {
            graylogError('KlaviyoSubscribeNewsLetterJob - Klaviyo list id is empty', [
                'category' => 'email-marketing-klaviyo',
                'data' => [
                    'email' => $this->email,
                    'store_id' => $this->storeId,
                ],
            ]);
            return;
        }
        try {
            $service->subscribeEmailToKlaviyoList($this->email, $listId);
        } catch (\Throwable $e) {
            $this->failed($e);
        }
    }

    /**
     * Handle a job failure.
     *
     * @param mixed $exception
     * @return void
     */
    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoSubscribeNewsLetterJob', [
            'email' => $this->email,
            'store_id' => $this->storeId,
        ]);
    }
}

<?php

namespace Modules\EmailMarketingKlaviyo\Jobs;

use App\Enums\CacheKeys;
use App\Models\Store;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Modules\EmailMarketingKlaviyo\Data\ImportProfilesData;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Enums\SpawnBulkProfileStatus;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use Spatie\RateLimitedMiddleware\RateLimited;
use Throwable;

class KlaviyoImportProfilesJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public ImportProfilesData $data;

    public function __construct(ImportProfilesData $data)
    {
        $this->onQueue(KlaviyoQueueName::ImportProfiles);
        $this->data = $data;
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    public function middleware(): array
    {
        $steadyRateLimit = (new RateLimited())
            ->allow(3500)
            ->everyMinute()
            ->releaseAfterBackoff($this->attempts(), 3);
        $burstRateLimit = (new RateLimited())
            ->allow(10)
            ->everySeconds(10)
            ->releaseAfterBackoff($this->attempts(), 3);
        $overLappingKey = KlaviyoQueueName::ImportProfiles . '|' . $this->data->store_id . '|' . $this->data->page;
        return [
            $steadyRateLimit,
            $burstRateLimit,
            (new WithoutOverlapping($overLappingKey))->dontRelease(),
        ];
    }

    /**
     * Get the private key
     *
     * @return string
     */
    public function getPrivateKey(): string
    {
        return $this->data->private_key;
    }

    /**
     * Get the store id
     *
     * @return int
     */
    public function getStoreId(): int
    {
        return $this->data->store_id;
    }

    /**
     * Get the current page
     *
     * @return int
     */
    public function getCurrentPage(): int
    {
        return $this->data->page;
    }

    /**
     * Get the limit
     *
     * @return int
     */
    public function getLimit(): int
    {
        return $this->data->limit;
    }

    public function handle()
    {
        try {
            $storeId = $this->getStoreId();
            $privateKey = $this->getPrivateKey();
            if (empty($privateKey)) {
                $store = Store::whereId($storeId)->first();
                if (!$store) {
                    return;
                }
                $privateKey = $store->klaviyo_private_key ?? $store->seller->klaviyo_private_key;
            }
            if (empty($privateKey)) {
                return;
            }
            $this->data->private_key = $privateKey;
            $page = $this->getCurrentPage();
            $service = new KlaviyoService(
                options: [
                    'limit' => $this->getLimit(),
                    'private_key' => $privateKey,
                ]
            );
            $status = $service
                ->fetchStore($storeId)
                ->withPage($page)
                ->sendSpawnBulkProfileImportRequest();
            if ($status === SpawnBulkProfileStatus::FINISHED) {
                $lockKey = CacheKeys::KLAVIYO_SYNC_LOCK . $storeId;
                Cache::forget($lockKey);
                return;
            }
            graylogInfo('KlaviyoImportProfilesJob', [
                'data' => [
                    'storeId' => $this->data->store_id,
                    'page' => $this->data->page,
                    'limit' => $this->data->limit,
                ],
                'category' => 'email-marketing-klaviyo',
            ]);
            ++$this->data->page;
            self::dispatch($this->data);
        } catch (Throwable $e) {
            $this->failed($e);
        }
    }

    /**
     * Handle a job failure.
     *
     * @param $exception
     * @return void
     */
    public function failed($exception): void
    {
        KlaviyoService::processException($exception, 'KlaviyoImportProfilesJob', [
            'storeId' => $this->data->store_id,
            'page' => $this->data->page,
            'limit' => $this->data->limit,
        ]);
    }

    /**
     * The number of times the job may be attempted.
     *
     * @return Carbon
     */
    public function retryUntil(): Carbon
    {
        return now()->addMinutes(5);
    }
}

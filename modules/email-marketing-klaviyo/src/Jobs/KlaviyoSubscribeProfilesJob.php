<?php

namespace Modules\EmailMarketingKlaviyo\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use <PERSON><PERSON>iyoAPI\KlaviyoAPI;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Spatie\RateLimitedMiddleware\RateLimited;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;

class KlaviyoSubscribeProfilesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public array $customerInfo;
    public string $privateKey;

    public function __construct(string $privateKey, array $customerInfo)
    {
        $this->onQueue(KlaviyoQueueName::ImportProfiles);
        $this->customerInfo = $customerInfo;
        $this->privateKey = $privateKey;
    }

    /**
     * Get the middleware the job should pass through.
     * @return array
     */
    public function middleware(): array
    {
        $burstRateLimit = (new RateLimited())
            ->allow(350)
            ->everySeconds(1)
            ->releaseAfterBackoff($this->attempts(), 3);
        $steadyRateLimit = (new RateLimited())
            ->allow(3500)
            ->everyMinute()
            ->releaseAfterBackoff($this->attempts(), 3);
        return [
            $burstRateLimit,
            $steadyRateLimit,
        ];
    }

    public function handle()
    {
        try {
            $email = $this->customerInfo['email'] ?? null;
            $phoneNumber = $this->customerInfo['phone_number'] ?? null;
            $phoneNumber = phone($phoneNumber)->isValid() ? phone($phoneNumber)->formatE164() : null;
            if (empty($email) && empty($phoneNumber)) {
                return;
            }
            $klaviyoClient = new KlaviyoAPI($this->privateKey, guzzle_options: [
                'timeout' => 10,
                'verify' => false
            ]);

            $payload = [
                'data' => [
                    'type' => 'profile-subscription-bulk-create-job',
                    'attributes' => [
                        'profiles' => [
                            'data' => [
                                [
                                    'type' => 'profile',
                                    'attributes' => []
                                ]
                            ]
                        ]
                    ]
                ]
            ];
            $attributes = $payload['data']['attributes']['profiles']['data'][0]['attributes'];
            if (!empty($email)) {
                $attributes['email'] = $email;
                $attributes['subscriptions']['email'] = [
                    'marketing' => [
                        'consent' => 'SUBSCRIBED'
                    ]
                ];
            }
            if (!empty($phoneNumber)) {
                $attributes['phone_number'] = $phoneNumber;
                $attributes['subscriptions']['sms'] = [
                    'marketing' => [
                        'consent' => 'SUBSCRIBED'
                    ]
                ];
            }
            $payload['data']['attributes']['profiles']['data'][0]['attributes'] = $attributes;
            $klaviyoClient->Profiles->subscribeProfiles($payload);
            graylogInfo('KlaviyoSubscribeProfilesJob success', [
                'category' => 'email-marketing-klaviyo',
                'data' => [
                    'customer_info' => $this->customerInfo,
                ]
            ]);
        } catch (\Throwable $exception) {
            $this->failed($exception);
        }
    }

    /**
     * Handle a job failure.
     *
     * @param mixed $exception
     * @return void
     */
    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoSubscribeProfilesJob', [
            'customerInfo' => $this->customerInfo,
            'privateKey' => $this->privateKey,
        ]);
        if ($exception->getCode() === 304) {
            $this->delete();
        }
    }
}

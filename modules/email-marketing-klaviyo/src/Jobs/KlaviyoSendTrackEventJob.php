<?php

namespace Modules\EmailMarketingKlaviyo\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use K<PERSON>iyoAPI\KlaviyoAPI;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Spatie\RateLimitedMiddleware\RateLimited;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;

class KlaviyoSendTrackEventJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public array $payload;
    public string $privateKey;

    public function __construct(string $privateKey, array $payload)
    {
        $this->onQueue(KlaviyoQueueName::TrackEvents);
        $this->payload = $payload;
        $this->privateKey = $privateKey;
    }

    /**
     * Get the middleware the job should pass through.
     * @return array
     */
    public function middleware(): array
    {
        $burstRateLimit = (new RateLimited())
            ->allow(350)
            ->everySeconds(1)
            ->releaseAfterBackoff($this->attempts(), 3);
        $steadyRateLimit = (new RateLimited())
            ->allow(3500)
            ->everyMinute()
            ->releaseAfterBackoff($this->attempts(), 3);
        return [
            $burstRateLimit,
            $steadyRateLimit,
        ];
    }

    public function handle()
    {
        try {
            $klaviyoClient = new KlaviyoAPI($this->privateKey, guzzle_options: [
                'timeout' => 10,
                'verify' => false
            ]);
            $klaviyoClient->Events->createEvent($this->payload);
        } catch (\Throwable $e) {
            $this->failed($e);
        }
    }

    /**
     * Handle a job failure.
     *
     * @param mixed $exception
     * @return void
     */
    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoSendTrackEventJob', [
            'payload' => $this->payload,
            'privateKey' => $this->privateKey,
        ]);
        if ($exception->getCode() === 304) {
            $this->delete();
        }
    }
}

<?php

namespace Modules\EmailMarketingKlaviyo\Jobs\Demo;

use App\Models\Store;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;

class KlaviyoDemoFulfilledOrderJob implements ShouldQueue
{
    use InteractsWithQueue;
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?User $seller;
    public $storeId;
    public ?Store $store;

    public function __construct($storeId, $sellerId = null)
    {
        $this->onQueue(KlaviyoQueueName::TrackEvents);
        if ($storeId) {
            $this->storeId = $storeId;
            $this->store = Store::whereId($storeId)->first();
        }
        else {
            $this->seller = User::query()->find($sellerId);
        }
    }

    public function handle(): void
    {
        if (empty($this->store) && empty($this->seller)) {
            return;
        }
        $privateKey = $this->store?->klaviyo_private_key ?? $this->seller?->klaviyo_private_key;;
        // $publicKey = $this->store->klaviyo_public_key ?? null;
        if (empty($privateKey)) {
            $this->release();
            return;
        }
        $baseUrl = env('APP_URL', 'https://senprints.com');
        $domain = parse_url($baseUrl, PHP_URL_HOST);
        $storeName = env('APP_NAME', 'SenPrints');
        $properties = [
            '$value' => 100,
            'Items' => ["Item 1", "Item 2", "Item 3"],
            'Collections' => ["Collection 1", "Collection 2", "Collection 3"],
            'Item Count' => 3,
            'Discount Codes' => 'DISCOUNT123',
            'Total Discounts' => 10,
            'Customer Locale' => 'en_US',
            'OptedInToSmsOrderUpdates' => 'False',
        ];
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => $properties,
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Fulfilled Order"
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => "<EMAIL>",
                                "phone_number" => "+12345678901",
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $storeName,
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($privateKey, $payload));
    }

    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoDemoFulfilledOrderJob', [
            'storeId' => $this?->storeId,
            'sellerId' => $this->seller?->id,
        ]);
    }
}

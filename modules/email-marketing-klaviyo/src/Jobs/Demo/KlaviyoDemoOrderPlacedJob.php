<?php

namespace Modules\EmailMarketingKlaviyo\Jobs\Demo;

use App\Models\Store;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;

class KlaviyoDemoOrderPlacedJob implements ShouldQueue
{
    use InteractsWithQueue;
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private ?User $seller;
    public $storeId;
    public ?Store $store;

    public function __construct($storeId, $sellerId = null)
    {
        $this->onQueue(KlaviyoQueueName::TrackEvents);
        if ($storeId) {
            $this->storeId = $storeId;
            $this->store = Store::whereId($storeId)->first();
        }
        else {
            $this->seller = User::query()->find($sellerId);
        }
    }

    public function handle(): void
    {
        if (empty($this->store) && empty($this->seller)) {
            return;
        }
        $privateKey = $this->store?->klaviyo_private_key ?? $this->seller?->klaviyo_private_key;;
        // $publicKey = $this->store->klaviyo_public_key ?? null;
        if (empty($privateKey)) {
            $this->release();
            return;
        }
        $baseUrl = env('APP_URL', 'https://senprints.com');
        $domain = parse_url($baseUrl, PHP_URL_HOST);
        $storeName = env('APP_NAME', 'SenPrints');
        $properties = [
            'OrderId' => '12345',
            'Categories' => ['T-Shirt'],
            'ItemNames' => ['T-Shirt'],
            'DiscountCode' => 'DISCOUNT',
            'DiscountValue' => 0,
            'Brands' => [],
            'OrderStatusUrl' => $baseUrl . '/order-status/12345',
            'BillingAddress' => [
                'FirstName' => 'John',
                'LastName' => 'Doe',
                'Address1' => '123 Main St',
                'Address2' => '',
                'City' => 'San Francisco',
                'State' => 'CA',
                'Zip' => '94103',
                'Country' => 'US',
                'Phone' => '************',
            ],
            'ShippingAddress' => [
                'FirstName' => 'John',
                'LastName' => 'Doe',
                'Address1' => '123 Main St',
                'Address2' => '',
                'City' => 'San Francisco',
                'State' => 'CA',
                'Zip' => '94103',
                'Country' => 'US',
                'Phone' => '************',
            ],
            'Items' => [
                [
                    'ProductID' => '12345',
                    'SKU' => 'SKU123',
                    'ProductName' => 'T-Shirt',
                    'Quantity' => 1,
                    'ItemPrice' => 10,
                    'RowTotal' => 10,
                    'ProductURL' => $baseUrl . '/t-shirt',
                    'ImageURL' => $baseUrl . '/t-shirt.jpg',
                    'Categories' => ['T-Shirt'],
                    'Brand' => '',
                    'VariantName' => 'T-Shirt',
                ]
            ],
        ];
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => $properties,
                    "time" => Carbon::now()->toIso8601ZuluString(),
                    "value" => 10,
                    "value_currency" => "USD",
                    "unique_id" => '12345',
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Placed Order"
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => 'noreply@' . $domain,
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $storeName,
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($privateKey, $payload));
    }

    public function failed($exception)
    {
        KlaviyoService::processException($exception, 'KlaviyoDemoOrderPlacedJob', [
            'storeId' => $this?->storeId,
            'sellerId' => $this->seller?->id,
        ]);
    }
}

<?php

namespace Modules\OrderService\Models;

use App\Enums\FulfillmentStatusEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Models\Currency;
use App\Models\Model;
use App\Models\Order;
use App\Models\OrderCancelRequest;
use App\Models\OrderHistory;
use App\Models\OrderIssue;
use App\Models\PaymentGateway;
use App\Models\PromotionRule;
use App\Models\Store;
use App\Models\User;
use App\Traits\ScopeFilterDateRangeTrait;
use Awobaz\Compoships\Compoships;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\OrderService\Helpers\RegionDbConnectionManager;
use Modules\OrderService\Models\Data\RegionOrderData;
use Modules\OrderService\Traits\HasRegionConnection;
use Modules\OrderService\Traits\UpdatingOrderAttributesTrait;
use Spatie\LaravelData\WithData;

/**
 * App\Models\RegionOrders
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\AbandonedLog> $abandoned_logs
 * @property-read int|null $abandoned_logs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ApiLog> $api_logs
 * @property-read int|null $api_logs_count
 * @property-read \App\Models\CustomerAddress|null $billingAddress
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Campaign> $campaigns
 * @property-read int|null $campaigns_count
 * @property-read \App\Models\SystemLocation|null $country_info
 * @property-read \App\Models\Currency|null $currency
 * @property-read \App\Models\User|null $customer
 * @property-read \App\Models\CustomerAddress|null $customer_address
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderAssignSupplierHistory> $assign_supplier_histories
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $designs
 * @property-read int|null $designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $files
 * @property-read int|null $files_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderProduct> $fulfilled_products
 * @property-read int|null $fulfilled_products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Fulfillment> $fulfillments
 * @property-read int|null $fulfillments_count
 * @property-read array|string|null $formatted_phone_number
 * @property-read mixed $line_items_pb
 * @property-read $order_info_pb
 * @property-read string $status_url
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderIssue> $issues
 * @property-read int|null $issues_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\File> $mockups
 * @property-read int|null $mockups_count
 * @property-read \App\Models\OrderExport|null $order_export
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderHistory> $order_history
 * @property-read int|null $order_history_count
 * @property-read int|null $order_products_count
 * @property-read \App\Models\PaymentGateway|null $payment_gateway
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductReview> $productReviews
 * @property-read int|null $product_reviews_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\ProductPoint> $product_points
 * @property-read int|null $product_points_count
 * @property-read int|null $products_count
 * @property-read \App\Models\PromotionRule|null $promotion
 * @property-read \App\Models\OrderCancelRequest|null $request_cancel
 * @property-read \App\Models\User|null $seller
 * @property-read \App\Models\SellerCustomer|null $seller_customer
 * @property-read \App\Models\CustomerAddress|null $shippingAddress
 * @property-read \App\Models\Store|null $store
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TrackingStatus> $tracking_statuses
 * @property-read int|null $tracking_statuses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderProduct> $unfulfilled_products
 * @property-read int|null $unfulfilled_products_count
 * @property string|null $payment_summary
 * @method static Builder|RegionOrders newModelQuery()
 * @method static Builder|RegionOrders newQuery()
 * @method static Builder|RegionOrders onlyTrashed()
 * @method Builder|RegionOrders paymentCompleted(string $totalPaid, string $paymentObjectId = '', bool $isPaypalEcheck = false, ?string $currencyCode = null, ?float $currencyRate = null, string $gatewayId = null)
 * @method Builder|RegionOrders paymentFailed(?string $failedLog, ?string $paymentObjectId = '')
 * @method static Builder|RegionOrders query()
 * @method static Builder|RegionOrders selectForHistory($addSelect = '')
 * @method static Builder|RegionOrders selectedFieldToFulfill()
 * @method static Builder|RegionOrders withTrashed()
 * @method static Builder|RegionOrders withoutTrashed()
 * @method static Builder|RegionOrders whereAccessToken($value)
 * @method Builder|RegionOrders updateOrderCondition(array $whereCondition = [])
 * @method Builder|RegionOrders paymentPendingReview(?string $paymentObjectId = '')
 * @method Builder|RegionOrders findByAccessToken(string $accessToken, string $withTrashed = false)
 * @mixin Builder
 * /
 */
class RegionOrders extends Model
{
    use HasFactory,
        Compoships,
        SoftDeletes,
        HasRegionConnection,
        UpdatingOrderAttributesTrait,
        WithData,
        ScopeFilterDateRangeTrait;

    public const PLATFORM_ORDER_TYPES = [
        OrderTypeEnum::SERVICE,
        OrderTypeEnum::REGULAR,
        OrderTypeEnum::CUSTOM
    ];

    private const FBA_COST_PER_UNIT = 0.5;
    private const FBA_FEE_PER_ORDER = 0.03;
    protected $dataClass = RegionOrderData::class;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'order';

    protected $fillable = [
        'access_token',
        'customer_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'address',
        'address_2',
        'city',
        'state',
        'postcode',
        'country',
        'store_id',
        'store_name',
        'store_domain',
        'seller_id',
        'ref_id',
        'tip_amount',
        'discount_code',
        'shipping_method',
        'payment_method',
        'payment_status',
        'payment_log',
        'payment_summary',
        'ip_address',
        'ad_source',
        'ad_campaign',
        'ad_medium',
        'ad_id',
        'device',
        'device_detail',
        'currency_code',
        'currency_rate',
        'fulfilled_at',
        'device_id',
        'session_id',
        'order_number',
        'region',
        'order_number_2',
        'type',
        'status',
        'fulfill_status',
        'fulfill_log',
        'delivered_at',
        'support_status',
        'review_request_status',
        'order_note',
        'received_at',
        'fraud_status',
        'flag_log',
        'ip_location',
        'tm_status',
        'insurance_fee',
        'external_order_id',
        'external_fulfillment_id',
        'skip_validate_address',
        'shipping_label',
        'temp_status',
        'cross_shipping',
        'total_fulfill_base_cost',
        'total_fulfill_shipping_cost',
        'total_fulfill_profit',
        'last4_card_number',
        'visit_info',
        'ioss_number',
    ];

    protected $casts = [
        'paid_at' => 'datetime',
        'region_synced_at' => 'datetime',
        'fulfilled_at' => 'datetime',
        'exported_at' => 'datetime',
    ];
    public $incrementing = true;
    protected $batch = false;

    /**
     * @param array $attributes
     * @param $batch
     */
    public function __construct(array $attributes = [], $batch = false)
    {
        $this->connection = RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region'));
        $this->batch = $batch;
        parent::__construct($attributes);
    }

    public function products(): HasMany
    {
        return $this->hasMany(RegionOrderProducts::class, 'order_id', 'id');
    }

    public function order_products(): HasMany
    {
        return $this->hasMany(RegionOrderProducts::class, 'order_id', 'id');
    }

    public function request_cancel(): HasOne
    {
        return $this->hasOne(OrderCancelRequest::class, 'order_id', 'id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'currency_code', 'code');
    }

    public function issues(): HasMany
    {
        return $this->hasMany(OrderIssue::class, 'order_id', 'id')->limit(10)->orderByDesc('created_at');
    }

    public function promotion(): BelongsTo
    {
        return $this->belongsTo(PromotionRule::class, 'promotion_rule_id');
    }

    /**
     * @param $q
     * @param $addSelect
     * @return mixed
     */
    public function scopeSelectForHistory($q, $addSelect = '')
    {
        $q->select(OrderHistory::ARR_SELECT_ORDER);
        if (!empty($addSelect)) {
            $q->addSelect($addSelect);
        }
        return $q;
    }

    public function syncAble()
    {
        return (!$this->synced_at || $this->updated_at->gt($this->synced_at));
    }

    /**
     * @return bool
     */
    public function isOrderCompleted(): bool
    {
        $isCompleted = true;
        $fulfillments = $this->fulfillments->toArray();
        $orderProducts = $this->products->toArray();

        array_map(static function ($fulfillment) use (&$isCompleted) {
            if (
                $fulfillment['status'] !== FulfillmentStatusEnum::FULFILLED
                && $fulfillment['status'] !== FulfillmentStatusEnum::CANCELLED
            ) {
                $isCompleted = false;
            }
        }, $fulfillments);

        array_map(static function ($product) use (&$isCompleted) {
            if (
                $product['fulfill_status'] !== OrderProductFulfillStatus::FULFILLED
                && $product['fulfill_status'] !== OrderProductFulfillStatus::CANCELLED
            ) {
                $isCompleted = false;
            }
        }, $orderProducts);

        return $isCompleted;
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    public function resolveRouteBinding($value, $field = null): self
    {
        // Attempt to find the model in the region-specific context
        $result = self::onRegion(config('app.region'))->where($field, $value)->first();
        if (!isset($result)) {
            return new RegionOrders;
        }
        return $result;
    }

    public function checkDataExistsInModel($value, $field = null) {
        $result = self::onRegion(config('app.region'))->where($field, $value)->exists();
        return $result;
    }

    public function allowCustomerUpdate()
    {
        return $this->syncable();
    }

    public function order_history () {
        return $this->hasMany(RegionOrderHistory::class, 'order_id')
        ->orderBy('created_at', 'desc');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function payment_gateway(): HasOne
    {
        return $this->hasOne(PaymentGateway::class, 'id', 'payment_gateway_id');
    }
}

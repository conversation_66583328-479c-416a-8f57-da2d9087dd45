<?php
namespace Modules\OrderService\Models;

use App\Enums\CountryEnum;
use App\Enums\CustomOptionType;
use App\Enums\OrderProductFulfillStatus;
use App\Models\BaseProduct;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\ShippingRule;
use App\Models\User;
use App\Traits\ScopeFilterDateRangeTrait;
use App\Traits\SPModel;
use Awo<PERSON>z\Compoships\Compoships;
use Awo<PERSON>z\Compoships\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
use Modules\OrderService\Helpers\RegionDbConnectionManager;
use Modules\OrderService\Traits\HasRegionConnection;
use Modules\OrderService\Traits\UpdatingRegionOrderProductsAttributesTrait;

/**
 * App\Models\RegionOrderProducts
 *
 * @property int $id
 * @property int $order_id
 * @property int|null $product_id
 * @property int|null $campaign_id
 * @property int|null $seller_id
 * @property int|null $auth_id
 * @property int|null $ref_id
 * @property int|null $template_id
 * @property int|null $shipping_rule_id
 * @property string|null $campaign_title copy from campaign
 * @property string|null $product_name copy from product type
 * @property string|null $product_url
 * @property string|null $barcode
 * @property string $thumb_url
 * @property string|null $options
 * @property string|null $custom_options
 * @property string|null $custom_designs
 * @property string|null $custom_print_space custom print space
 * @property string|null $color
 * @property string|null $size
 * @property float $fulfill_base_cost
 * @property float $base_cost
 * @property float $price
 * @property int | null $total_reprint
 * @property float|null $adjust_test_price
 * @property float $extra_custom_fee
 * @property float $weight pound:lbs
 * @property int $quantity
 * @property float $tax
 * @property float $total_amount
 * @property float $fulfill_shipping_cost
 * @property float $shipping_cost
 * @property float $discount_amount discount of product if applied
 * @property float $fulfill_profit
 * @property float $seller_profit
 * @property float $artist_profit
 * @property float $sen_points
 * @property int $upsell_status 1:yes,0:no
 * @property int $fulfilled_quantity set if create fulfillment
 * @property string $fulfill_status 'unfulfilled','fulfilled','partial_fulfilled','cancelled','processing','on_hold','rejected','exception','invalid','out_of_stock','no_ship','pending','reviewing','designing','on_delivery'
 * @property string $sen_fulfill_status YES,NO,REVIEW,PENDING
 * @property int $refund_quantity
 * @property int $full_printed
 * @property string|null $sku
 * @property int|null $supplier_id
 * @property string|null $supplier_name
 * @property string|null $fulfill_sku
 * @property int|null $fulfill_product_id
 * @property float $fulfill_cost
 * @property float $extra_print_cost
 * @property string $fulfill_order_id
 * @property string|null $fulfill_exception_log
 * @property string|null $shipping_carrier
 * @property string|null $tracking_code
 * @property string|null $tracking_url
 * @property string|null $tracking_status
 * @property string $updated_at
 * @property string|null $supplier_exported_at
 * @property string|null $fulfilled_at
 * @property string|null $delivered_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $assigned_supplier_at
 * @property string|null $received_at
 * @property float $processing_day
 * @property float|null $shipping_day
 * @property string|null $billed_at
 * @property int $personalized 1:yes,0:no (yes if customer customize text/image)
 * @property string|null $exported_at
 * @property int $shard_id
 * @property int|null $collection_id
 * @property string $tm_status
 * @property string|null $external_product_id
 * @property string|null $external_fulfillment_id
 * @property string|null $external_id
 * @property string|null $sync_at
 * @property string|null $location
 * @property int|null $cross_shipping
 * @property int|null $promotion_rule_id
 * @property int|null $combo_id
 * @property int|null $related_campaign_id
 * @property int|null $related_product_id
 * @property-read \App\Models\User|null $author
 * @property-read \App\Models\Campaign|null $campaign
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Design[] $customOptionDesigns
 * @property-read int|null $custom_option_designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\File[] $designs
 * @property-read int|null $designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\File[] $fulfillOrderDesigns
 * @property-read int|null $fulfill_order_designs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\File[] $fulfillOrderMockups
 * @property-read int|null $fulfill_order_mockups_count
 * @property-read array $collections
 * @property-read mixed $custom_options_pb
 * @property-read mixed $custom_options_regular
 * @property-read array|null $design_pb
 * @property-read mixed $print_url
 * @property-read array $stores
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\File[] $mockups
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\File[] $files
 * @property-read int|null $mockups_count
 * @property-read \App\Models\Order $order
 * @property-read \App\Models\Pricing|null $pricing
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\File[] $printDesigns
 * @property-read int|null $print_designs_count
 * @property-read \App\Models\Product|null $product
 * @property-read \App\Models\ProductReview|null $productReview
 * @property-read \App\Models\ProductPoint|null $product_point
 * @property-read \App\Models\User|null $seller
 * @property-read \App\Models\ShippingRule|null $shipping_rule
 * @property-read \App\Models\Supplier|null $supplier
 * @property-read \App\Models\Product|null $template
 * @property-read \App\Models\TrackingStatus|null $trackingStatus
 * @method static \Database\Factories\OrderProductFactory factory(...$parameters)
 * @method Builder|RegionOrderProducts makeHiddenAll()
 * @method static Builder|RegionOrderProducts newModelQuery()
 * @method static Builder|RegionOrderProducts newQuery()
 * @method static \Illuminate\Database\Query\Builder|RegionOrderProducts onlyTrashed()
 * @method static Builder|RegionOrderProducts query()
 * @method static Builder|RegionOrderProducts shippingLate()
 * @method static Builder|RegionOrderProducts whereAdjustTestPrice($value)
 * @method static Builder|RegionOrderProducts whereArtistProfit($value)
 * @method static Builder|RegionOrderProducts whereAuthId($value)
 * @method static Builder|RegionOrderProducts whereBaseCost($value)
 * @method static Builder|RegionOrderProducts whereBaseShippingCost($value)
 * @method static Builder|RegionOrderProducts whereBilledAt($value)
 * @method static Builder|RegionOrderProducts whereCampaignId($value)
 * @method static Builder|RegionOrderProducts whereCampaignTitle($value)
 * @method static Builder|RegionOrderProducts whereCollectionId($value)
 * @method static Builder|RegionOrderProducts whereColor($value)
 * @method static Builder|RegionOrderProducts whereCost($value)
 * @method static Builder|RegionOrderProducts whereCustomOptions($value)
 * @method static Builder|RegionOrderProducts whereCustomPrintSpace($value)
 * @method static Builder|RegionOrderProducts whereDeletedAt($value)
 * @method static Builder|RegionOrderProducts whereDeliveredAt($value)
 * @method static Builder|RegionOrderProducts whereDiscountAmount($value)
 * @method static Builder|RegionOrderProducts whereExportedAt($value)
 * @method static Builder|RegionOrderProducts whereExternalFulfillmentId($value)
 * @method static Builder|RegionOrderProducts whereExternalId($value)
 * @method static Builder|RegionOrderProducts whereExternalProductId($value)
 * @method static Builder|RegionOrderProducts whereExtraCustomFee($value)
 * @method static Builder|RegionOrderProducts whereFulfillCost($value)
 * @method static Builder|RegionOrderProducts whereFulfillExceptionLog($value)
 * @method static Builder|RegionOrderProducts whereFulfillOrderId($value)
 * @method static Builder|RegionOrderProducts whereFulfillProductId($value)
 * @method static Builder|RegionOrderProducts whereFulfillSku($value)
 * @method static Builder|RegionOrderProducts whereFulfillStatus($value)
 * @method static Builder|RegionOrderProducts whereFulfilledAt($value)
 * @method static Builder|RegionOrderProducts whereFulfilledQuantity($value)
 * @method static Builder|RegionOrderProducts whereFullPrinted($value)
 * @method static Builder|RegionOrderProducts whereId($value)
 * @method static Builder|RegionOrderProducts whereOptions($value)
 * @method static Builder|RegionOrderProducts whereOrderId($value)
 * @method static Builder|RegionOrderProducts wherePersonalized($value)
 * @method static Builder|RegionOrderProducts wherePrice($value)
 * @method static Builder|RegionOrderProducts whereProcessingDay($value)
 * @method static Builder|RegionOrderProducts whereProductId($value)
 * @method static Builder|RegionOrderProducts whereProductName($value)
 * @method static Builder|RegionOrderProducts whereProductUrl($value)
 * @method static Builder|RegionOrderProducts whereProfit($value)
 * @method static Builder|RegionOrderProducts whereQuantity($value)
 * @method static Builder|RegionOrderProducts whereReceivedAt($value)
 * @method static Builder|RegionOrderProducts whereRefId($value)
 * @method static Builder|RegionOrderProducts whereRefundQuantity($value)
 * @method static Builder|RegionOrderProducts whereSellerId($value)
 * @method static Builder|RegionOrderProducts whereSellerProfit($value)
 * @method static Builder|RegionOrderProducts whereSenFulfillStatus($value)
 * @method static Builder|RegionOrderProducts whereSenPoints($value)
 * @method static Builder|RegionOrderProducts whereShardId($value)
 * @method static Builder|RegionOrderProducts whereShippingCarrier($value)
 * @method static Builder|RegionOrderProducts whereShippingCost($value)
 * @method static Builder|RegionOrderProducts whereShippingDay($value)
 * @method static Builder|RegionOrderProducts whereShippingRuleId($value)
 * @method static Builder|RegionOrderProducts whereSize($value)
 * @method static Builder|RegionOrderProducts whereSku($value)
 * @method static Builder|RegionOrderProducts whereSupplierId($value)
 * @method static Builder|RegionOrderProducts whereSupplierName($value)
 * @method static Builder|RegionOrderProducts whereTax($value)
 * @method static Builder|RegionOrderProducts whereTemplateId($value)
 * @method static Builder|RegionOrderProducts whereThumbUrl($value)
 * @method static Builder|RegionOrderProducts whereTmStatus($value)
 * @method static Builder|RegionOrderProducts whereTotalAmount($value)
 * @method static Builder|RegionOrderProducts whereTrackingCode($value)
 * @method static Builder|RegionOrderProducts whereTrackingStatus($value)
 * @method static Builder|RegionOrderProducts whereTrackingUrl($value)
 * @method static Builder|RegionOrderProducts whereUpdatedAt($value)
 * @method static Builder|RegionOrderProducts whereUpsellStatus($value)
 * @method static Builder|RegionOrderProducts whereWeight($value)
 * @method static \Illuminate\Database\Query\Builder|RegionOrderProducts withTrashed()
 * @method static \Illuminate\Database\Query\Builder|RegionOrderProducts withoutTrashed()
 * @mixin Builder
 * @mixin \Illuminate\Database\Query\Builder
 */
class RegionOrderProducts extends BaseProduct
{
    use HasFactory;
    use SPModel;
    use SoftDeletes;
    use ScopeFilterDateRangeTrait;
    use Compoships;
    use HasRegionConnection;
    use UpdatingRegionOrderProductsAttributesTrait;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'order_product';
    protected $fillable = [
        'order_id',
        'product_id',
        'campaign_id',
        'campaign_title',
        'template_id',
        'product_name',
        'product_url',
        'thumb_url',
        'options',
        'custom_options',
        'custom_designs',
        'fulfill_base_cost',
        'base_cost',
        'price',
        'quantity',
        'tax',
        'total_amount',
        'fulfill_shipping_cost',
        'base_shipping_cost',
        'shipping_cost',
        'discount_amount',
        'fulfill_profit',
        'seller_profit',
        'upsell_status',
        'seller_id',
        'auth_id',
        'ref_id',
        'supplier_id',
        'supplier_name',
        'fulfill_sku',
        'fulfill_cost',
        'fulfill_order_id',
        'tracking_code',
        'shipping_carrier',
        'tracking_url',
        'fulfill_status',
        'fulfill_exception_log',
        'fulfilled_at',
        'billed_at',
        'personalized',
        'full_printed',
        'sku',
        'exported_at',
        'weight',
        'delivered_at',
        'processing_day',
        'received_at',
        'deleted_at',
        'adjust_test_price',
        'fulfill_product_id',
        'extra_custom_fee',
        'shipping_rule_id',
        'artist_profit',
        'tm_status',
        'sen_points',
        'shipping_day',
        'external_product_id',
        'external_fulfillment_id',
        'external_id',
        'color',
        'custom_print_space',
        'extra_print_cost',
        'barcode',
        'fulfill_fba_by',
        'cross_shipping',
        'supplier_exported_at',
        'promotion_rule_id',
        'related_campaign_id',
        'related_product_id',
        'dynamic_base_cost_index',
        'dynamic_base_cost_index_rounded',
        'combo_id',
        'campaign_type',
        'design_by',
    ];

    public $timestamps = false;
    protected $batch = false;
    public $incrementing = true;
    protected $connection = '';

    /**
     * @param array $attributes
     * @param $batch
     */
    public function __construct(array $attributes = [], $batch = false)
    {
        $this->connection = RegionDbConnectionManager::getConnectionNameFromRegion(config('app.region'));
        $this->batch = $batch;
        parent::__construct($attributes);
    }

    /**
     * @return string
     */
    public function getTable(): string
    {
        if ($this->batch) {
            return $this->table;
        }
        return parent::getTable();
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(RegionOrders::class, 'order_id');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id');
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'template_id', 'id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'auth_id');
    }

    /**
     * @param null|int $quantity
     * @return float|int
     */
    public function calculateTotal($quantity = null)
    {
        if ($quantity) {
            $this->total_amount = (int) $quantity * (float) $this->price;
        } else {
            $this->total_amount = (int) $this->quantity * (float) $this->price;
        }
        return $this->total_amount;
    }

    public function calculateTotalBaseCost()
    {
        return $this->quantity * $this->base_cost;
    }

    public function calculateTotalRefund()
    {
        return $this->refund_quantity * $this->price;
    }

    public function calculateTax()
    {
        return $this->tax;
    }

    public function calculateShipping()
    {
        return $this->shipping_cost;
    }

    /**
     * @param array $pbCustomInfo
     * @return void
     * @throws \JsonException
     */
    public function putCustomOptionsPB(array $pbCustomInfo): void
    {
        $pbOptions = json_encode($pbCustomInfo['options'], JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES);
        $hash = md5($pbOptions);
        $pbCustomInfo['print_url'] = replacePBArtworkURL($pbCustomInfo['print_url'], $hash);
        $this->custom_options = json_encode([
            CustomOptionType::REGULAR => $this->custom_options,
            CustomOptionType::PB => json_encode($pbCustomInfo, JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES),
        ], JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES);
    }

    /**
     * @return bool
     */
    public function justCreated(): bool
    {
        return $this->fulfill_status === OrderProductFulfillStatus::PENDING;
    }

    public function skipAssignSupplier(): bool
    {
        return in_array($this->fulfill_status, [
            OrderProductFulfillStatus::FULFILLED,
            OrderProductFulfillStatus::PROCESSING,
            OrderProductFulfillStatus::EXCEPTION,
        ], true);
    }

    public function shipping_rule(): HasOne {
        return $this->hasOne(ShippingRule::class, 'id', 'shipping_rule_id');
    }

    /**
     * @return $this
     */
    public function setFulfillLogNotFoundVariant(): RegionOrderProducts
    {
        $this->fulfill_exception_log =
            'Assign Supplier failed by OrderOption . Order Id: '
            . $this->order_id
            . '. Order Product Id: ' . $this->id
            . '. Template Id: ' . $this->template_id
            . '. VariantKey: ' . $this->options;

        return $this;
    }

     /**
     * Khi đơn hàng ship đến US hoặc EU thì đánh dấu là cross shipping
     * nếu khớp sản phẩm fulfill là * (world wide). Hoặc US thì khớp
     * với EU và ngược lại v iệc này hỗ trợ đội vận hành lọc những đơn
     * này để xử lí thủ công, tối ưu chi phí vận chuyển
     *
     * @param     string          $rawLocSup
     * @param     string|null     $oLocCode
     *
     * @return $this
     */
    public function determineCrossShipping(string $rawLocSup, ?string $oLocCode): RegionOrderProducts
    {
        // Chuyển order location về EU nếu là EU, không thì giữ nguyên
        $oLocCode = CountryEnum::isEu($oLocCode) ? 'EU' : $oLocCode;

        // Chuyển danh sách supplier location về EU nếu có EU, không thì giữ nguyên
        $supLocCodes = Str::of($rawLocSup)
            ->explode(',')
            ->map(fn($code) => CountryEnum::isEu($code) ? 'EU' : $code);

        $this->markCrossShipping(
            $supLocCodes->contains($oLocCode) ? 0 : 1
        );

        return $this;
    }

    /**
     * @return $this
     */
    public function markCrossShipping(?int $value = null): RegionOrderProducts
    {
        $this->cross_shipping = $value ?? 0;

        return $this;
    }

        /**
     * Find variant by its key
     * @param string $variantKey
     * @param string $country
     * @return ProductVariant|null
     */
    public function findVariantByKey(string $variantKey, string $country): ?ProductVariant
    {
        $variants = ProductVariant::query()
            ->where('product_id', $this->template_id)
            ->where('variant_key', $variantKey)
            ->get();
        $location = getLocationByCode(strtoupper($country));
        $regionCodes = $location?->getRegionCodes() ?? ['*'];

        // Lọc các variant có location_code nằm trong regionCodes
        // Sau đó sắp xếp theo thứ tự xuất hiện trong regionCodes và lấy variant đầu tiên
        return $variants->whereIn('location_code', $regionCodes)
            ->sortBy(function ($variant) use ($regionCodes) {
                return array_search($variant->location_code, $regionCodes);
            })
            ->first();
    }
}

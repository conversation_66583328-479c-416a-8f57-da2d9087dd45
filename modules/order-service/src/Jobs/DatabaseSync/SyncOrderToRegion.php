<?php

namespace Modules\OrderService\Jobs\DatabaseSync;

use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\OrderService\Data\CreateRegionOrderProductData;
use Modules\OrderService\Models\RegionOrderProducts;
use Modules\OrderService\Models\RegionOrders;

/**
 * Class SyncOrderToRegion
 *
 * This job will sync region order and their relationship to master database
 */
class SyncOrderToRegion implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public Order $orderMaster, public string $region, public ?RegionOrders $regionOrder = null, public bool $syncOrder = true, public bool $syncOrderProducts = true) {}

    public function handle()
    {
        try {
            $now = now();
            $startTime = round(microtime(true) * 1000);
            $orderMaster = $this->orderMaster;
            $accessToken = $orderMaster->access_token;
            if (!$orderMaster || $this->region === 'sg') {
                return null;
            }
            $regionOrder = $this->regionOrder;
            $isCreated = false;
            if (!$regionOrder) {
                $regionOrder = RegionOrders::onRegion($this->region)->where('access_token', $orderMaster->access_token)->first();
                if (!$regionOrder) {
                    $regionOrder = RegionOrders::onRegion($this->region)->create([
                        'access_token' => $accessToken,
                        'order_number' => $orderMaster->getOrderNumber(),
                        'region' => $this->region,
                    ]);
                    $isCreated = true;
                }
            }
            /**
             * 1. Sync order info
             */
            if ($this->syncOrder) {
                if ($isCreated) {
                    $regionOrder->setAdCampaign($orderMaster->getAdCampaign());
                    $regionOrder->setAdId($orderMaster->getAdId());
                    $regionOrder->setAdMedium($orderMaster->getAdMedium());
                    $regionOrder->setAdSource($orderMaster->getAdSource());
                    $regionOrder->setCountry($orderMaster->getCountry());
                    $regionOrder->setCurrencyCode($orderMaster->getCurrencyCode());
                    $regionOrder->setDevice($orderMaster->getDevice());
                    $regionOrder->setDeviceDetail($orderMaster->getDeviceDetail());
                    $regionOrder->setDeviceId($orderMaster->getDeviceId());
                    $regionOrder->setStoreDomain($orderMaster->getStoreDomain());
                    $regionOrder->setStoreId($orderMaster->getStoreId());
                    $regionOrder->setSellerId($orderMaster->getSellerId());
                    $regionOrder->setStoreName($orderMaster->getStoreName());
                    $regionOrder->setTransactionId($orderMaster->getTransactionId());
                    $regionOrder->setPaymentGatewayId($orderMaster->getPaymentGatewayId());
                    $regionOrder->setPaymentMethod($orderMaster->getPaymentMethod());
                    $regionOrder->setStatusUrl($orderMaster->getStatusUrl());
                    $regionOrder->setCustomerEmail($orderMaster->getCustomerEmail());
                    $regionOrder->setCustomerName($orderMaster->getCustomerName());
                    $regionOrder->setCustomerPhone($orderMaster->getCustomerPhone());
                    $regionOrder->setIossNumber($orderMaster->getIossNumber());
                    $regionOrder->setIsCornerPlacement($orderMaster->getIsCornerPlacement());
                }
                $regionOrder->setCompanyId($orderMaster->getCompanyId());
                $regionOrder->setFulfillStatus($orderMaster->getFulfillStatus());
                $regionOrder->setIpAddress($orderMaster->getIpAddress());
                $regionOrder->setIpLocation($orderMaster->getIpLocation());
                $regionOrder->setPromotionRule($orderMaster->getPromotionRule());
                $regionOrder->setPromotionRuleId($orderMaster->getPromotionRuleId());
                $regionOrder->setRefId($orderMaster->getRefId());
                $regionOrder->setShippingMethod($orderMaster->getShippingMethod());
                $regionOrder->setTotalAmount($orderMaster->getTotalAmount());
                $regionOrder->setTotalDiscount($orderMaster->getTotalDiscount());
                $regionOrder->setDiscountCode($orderMaster->getDiscountCode());
                $regionOrder->setTotalPaid($orderMaster->getTotalPaid());
                $regionOrder->setRegion($this->region);
                $regionOrder->setOrderNumber($orderMaster->getOrderNumber());
                $regionOrder->setPaymentStatus($orderMaster->getPaymentStatus());
                $regionOrder->setPaymentFee($orderMaster->getPaymentFee());
                $regionOrder->setProcessingFee($orderMaster->getProcessingFee());
                $regionOrder->setInsuranceFee($orderMaster->getRawInsuranceFee());
                $regionOrder->setTotalFulfillFee($orderMaster->getTotalFulfillFee());
                $regionOrder->setTotalQuantity($orderMaster->getTotalQuantity());
                $regionOrder->setTotalProductAmount($orderMaster->getTotalProductAmount());
                $regionOrder->setTotalShippingAmount($orderMaster->getTotalShippingAmount());
                $regionOrder->setTipAmount($orderMaster->getTipAmount());
                $regionOrder->setTotalTaxAmount($orderMaster->getTotalTaxAmount());
                $regionOrder->setTotalRefund($orderMaster->getTotalRefund());
                $regionOrder->setTotalProductCost($orderMaster->getTotalProductCost());
                $regionOrder->setTotalProductBaseCost($orderMaster->getTotalProductBaseCost());
                $regionOrder->setTotalShippingCost($orderMaster->getTotalShippingCost());
                $regionOrder->setTotalSellerProfit($orderMaster->getTotalSellerProfit());
                $regionOrder->setTotalProfit($orderMaster->getTotalProfit());
                $regionOrder->setProcessingFeePaid($orderMaster->getProcessingFeePaid());
                $regionOrder->setFulfillFeePaid($orderMaster->getFulfillFeePaid());
                $regionOrder->setRemarketingStatus($orderMaster->getRemarketingStatus());
                $regionOrder->setStatus($orderMaster->getStatus());
                $regionOrder->setPaymentLog($orderMaster->getPaymentLog());
                $regionOrder->setFraudStatus($orderMaster->getFraudStatus());
                $regionOrder->setStatsStatus($orderMaster->getStatsStatus());
                $regionOrder->setType($orderMaster->getType());
                $regionOrder->setAddressVerified($orderMaster->getAddressVerified());
                $regionOrder->setOrderNote($orderMaster->getOrderNote());
                $regionOrder->setCreatedAt($orderMaster->getCreatedAt());
                $regionOrder->setFulfilledAt($orderMaster->getFulfilledAt());
                $regionOrder->setPaidAt($orderMaster->getPaidAt());
                $regionOrder->setDeliveredAt($orderMaster->getDeliveredAt());
                $regionOrder->setReceivedAt($orderMaster->getReceivedAt());
                $regionOrder->setCurrencyRate($orderMaster->getCurrencyRate());
                $regionOrder->setFulfillLog($orderMaster->getFulfillLog());
                $regionOrder->setFlagLog($orderMaster->getFlagLog());
                $regionOrder->setSenFulfillStatus($orderMaster->getSenFulfillStatus());
                $regionOrder->setDatestamp($orderMaster->getDatestamp());
                $regionOrder->setOrderNumber($orderMaster->getOrderNumber());
                $regionOrder->setOrderNumber2($orderMaster->getOrderNumber2());
                $regionOrder->setPersonalized($orderMaster->getPersonalized());
                $regionOrder->setLast4CardNumber($orderMaster->getLast4CardNumber());
                $regionOrder->setAddress($orderMaster->getAddress());
                $regionOrder->setAddress2($orderMaster->getAddress2());
                $regionOrder->setCity($orderMaster->getCity());
                $regionOrder->setState($orderMaster->getState());
                $regionOrder->setPostcode($orderMaster->getPostcode());
                $regionOrder->setHouseNumber($orderMaster->getHouseNumber());
                $regionOrder->setMailboxNumber($orderMaster->getMailboxNumber());
                $regionOrder->setEstimateDeliveryDate($orderMaster->getEstimateDeliveryDate());
                $regionOrder->setApprovedAt($orderMaster->getApprovedAt());
                $regionOrder->setSyncAt($now);
                $regionOrder->setUpdatedAt($now);
                $regionOrder->setRegionSyncedAt($now);
                $regionOrder->saveQuietly();
            }

            /**
             * 2. Sync order products
             *
             * But we only sync order products if order is not paid
             */
            if ($this->syncOrderProducts) {
                foreach ($orderMaster->products as $orderProductMaster) {
                    $orderProductMaster->order_id = $regionOrder->getId();
                    $data = CreateRegionOrderProductData::from($orderProductMaster->toArray());
                    $newOrderProductData = $data->toArray();
                    unset($newOrderProductData['id'], $newOrderProductData['additional_attributes']);
                    RegionOrderProducts::onRegion($this->region)->where('order_id', $regionOrder->getId())->delete();
                    RegionOrderProducts::onRegion($this->region)->create($newOrderProductData);
                }
                /**
                 * 3. We mark order as region order
                 */
                $orderMaster->setRegion($this->region);
                if ($orderMaster->isDirty()) {
                    $orderMaster->save();
                }
            }
            $endTime = round(microtime(true) * 1000);
            $time = $endTime - $startTime;
            graylogInfo('Synced order to region done: ' . $regionOrder->getAccessToken() . ' in ' . $time . ' ms', [
                'category' => 'sync_order_to_region_job',
                'region_order' => $regionOrder,
                'access_token' => $accessToken,
            ]);
            return $regionOrder;
        } catch (\Exception $e) {
            logToDiscord('Sync Order To Region Job Error: ' . $e->getMessage(), 'error', true);
            graylogError('Sync Order To Region Job Error', [
                'category' => 'sync_order_to_region_job_fatal_error',
                'data' => $e
            ]);
            return null;
        }
    }
}

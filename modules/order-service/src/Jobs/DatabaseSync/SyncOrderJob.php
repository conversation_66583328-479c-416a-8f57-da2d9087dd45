<?php

namespace Modules\OrderService\Jobs\DatabaseSync;

use App\Enums\DiscordChannel;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderPaymentStatus as OrderPaymentStatusEnum;
use App\Enums\OrderStatus;
use App\Events\OrderPaymentCompleted;
use App\Models\Customer;
use App\Models\CustomerAddress;
use App\Models\Design;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProductLog;
use App\Models\SellerCustomer;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Modules\OrderService\Models\RegionOrderHistory;
use Modules\OrderService\Models\RegionOrderProducts;
use Modules\OrderService\Models\RegionOrders;

/**
 * Class SyncOrderJob
 *
 * This job will sync region order and their relationship to master database
 */
class SyncOrderJob implements ShouldQueue, ShouldBeUniqueUntilProcessing
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $reSync = 0;

    public function __construct(public $regionOrderId, public string $region) {}

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return 'sync-region-order:' . $this->regionOrderId . '-' . $this->region;
    }


    private function shouldSync(Order $order): bool
    {
        /**
         * We only sync order if:
         */
        return (
            !$order->getId() // New order
            || !$order->isPaid() // Order is not paid
            || $order->isPaidUnder(24) // Order is paid but under 24 hours
        ) && in_array($order->getFulfillStatus(), OrderFulfillStatus::canSync(), true);
    }

    public function handle()
    {
        try {
            /**
             * ----------------------------------
             * PREPARE DATA
             * ----------------------------------
             */
            $startTime = round(microtime(true) * 1000);
            if ($this->region === config('app.region_master')) {
                return;
            }
            $regionOrder = RegionOrders::onRegion($this->region)->withTrashed()->find($this->regionOrderId);
            if (!$regionOrder) {
                graylogError('Sync Order Job Error Not Found Region Order', [
                    'category' => 'sync_order_job_fatal_error',
                    'region' => $this->region,
                    'region_order_id' => $this->regionOrderId,
                    'trace' => debug_backtrace(),

                ]);
                return;
            }
            /** @var RegionOrders $regionOrder */
            /** @var RegionOrderProducts[] $regionOrderProducts */
            $regionOrderProducts = RegionOrderProducts::onRegion($this->region)->where('order_id', $regionOrder->getId())->get();
            $regionOrder->setRelation('products', $regionOrderProducts);
            $now = now();
            $order = Order::query()->findByAccessToken($regionOrder->getAccessToken(), true)->first();
            if (!$order) {
                $insertData = [
                    'access_token' => $regionOrder->getAccessToken(),
                    'order_number' => $regionOrder->getOrderNumber(),
                    'region' => $regionOrder->getRegion(),
                ];
                $order = Order::setRegionCheckout($this->region ?? $regionOrder->region)->firstOrCreate(['access_token' => $insertData['access_token']], $insertData);
            }

            if (!$order || !$this->shouldSync($order)) {
                return;
            }
            $cache = Cache::store('database');
            $syncedKey = md5('total_region_master_synced_' . $regionOrder->getRegion() . '_' . $order->getId());
            try {
                $totalSynced = (int)$cache->get($syncedKey, 0);
            } catch (\Throwable $e) {
                $totalSynced = 0;
            }
            if ($totalSynced >= 3) {
                logToDiscord("https://admin.senprints.com/order/detail/" . $order->getId() . "\r\n Đơn này đang bị đồng bộ từ {$this->region} về master liên tục với số lượng lớn hơn 3 lần/phút.", DiscordChannel::DEV_DEBUG_LOGS, threadId: 1382576439324442634);
                $cache->forget($syncedKey);
                $totalSynced = 0;
            }
            $cache->put($syncedKey, $totalSynced + 1, $now->clone()->addMinute());
            $paymentStatusBefore = $order->getPaymentStatus();
            $orderStatusBefore = $order->getStatus();
            $order->setPaymentStatus($regionOrder->getPaymentStatus());
            $paymentStatusAfter = $order->getPaymentStatus();
            $changePaymentStatusToPaid = false;
            if ($paymentStatusBefore !== $paymentStatusAfter && $paymentStatusAfter === OrderPaymentStatusEnum::PAID) {
                $changePaymentStatusToPaid = true;
            }
            /**
             * ----------------------------------
             * NOW WE SYNC ORDER DATA
             * ----------------------------------
             */
            /**
             * 1. Sync order info
             */
            if ($changePaymentStatusToPaid || !$order->isPaid()) {
                $order->setAccessToken($regionOrder->getAccessToken());
                $order->setAdCampaign($regionOrder->getAdCampaign());
                $order->setAdId($regionOrder->getAdId());
                $order->setAdMedium($regionOrder->getAdMedium());
                $order->setAdSource($regionOrder->getAdSource());
                $order->setCompanyId($regionOrder->getCompanyId());
                $order->setCurrencyCode($regionOrder->getCurrencyCode());
                $order->setDevice($regionOrder->getDevice());
                $order->setDeviceDetail($regionOrder->getDeviceDetail());
                $order->setDeviceId($regionOrder->getDeviceId());
                $order->setFulfillStatus($regionOrder->getFulfillStatus());
                $order->setIpAddress($regionOrder->getIpAddress());
                $order->setIpLocation($regionOrder->getIpLocation());
                $order->setPromotionRule($regionOrder->getPromotionRule());
                $order->setPromotionRuleId($regionOrder->getPromotionRuleId());
                $order->setRefId($regionOrder->getRefId());
                $order->setSessionId($regionOrder->getSessionId());
                $order->setShippingMethod($regionOrder->getShippingMethod());
                $order->setStoreDomain($regionOrder->getStoreDomain());
                $order->setStoreId($regionOrder->getStoreId());
                $order->setSellerId($regionOrder->getSellerId());
                $order->setStoreName($regionOrder->getStoreName());
                $order->setTotalAmount($regionOrder->getTotalAmount());
                $order->setTotalDiscount($regionOrder->getTotalDiscount());
                $order->setDiscountCode($regionOrder->getDiscountCode());
                $order->setTotalPaid($regionOrder->getTotalPaid());
                $order->setRegion($regionOrder->getRegion());
                $order->setTransactionId($regionOrder->getTransactionId());
                $order->setPaymentGatewayId($regionOrder->getPaymentGatewayId());
                $order->setPaymentMethod($regionOrder->getPaymentMethod());
                $order->setPaymentStatus($regionOrder->getPaymentStatus());
                $order->setPaymentFee($regionOrder->getPaymentFee());
                $order->setProcessingFee($regionOrder->getProcessingFee());
                $order->setInsuranceFee($regionOrder->getRawInsuranceFee());
                $order->setTotalFulfillFee($regionOrder->getTotalFulfillFee());
                $order->setTotalQuantity($regionOrder->getTotalQuantity());
                $order->setTotalProductAmount($regionOrder->getTotalProductAmount());
                $order->setTotalShippingAmount($regionOrder->getTotalShippingAmount());
                $order->setTipAmount($regionOrder->getTipAmount());
                $order->setTotalTaxAmount($regionOrder->getTotalTaxAmount());
                $order->setTotalRefund($regionOrder->getTotalRefund());
                $order->setTotalProductCost($regionOrder->getTotalProductCost());
                $order->setTotalProductBaseCost($regionOrder->getTotalProductBaseCost());
                $order->setTotalShippingCost($regionOrder->getTotalShippingCost());
                $order->setTotalSellerProfit($regionOrder->getTotalSellerProfit());
                $order->setTotalProfit($regionOrder->getTotalProfit());
                $order->setBillingAddress($regionOrder->getBillingAddress());
                $order->setShippingAddress($regionOrder->getShippingAddress());
                $order->setProcessingFeePaid($regionOrder->getProcessingFeePaid());
                $order->setFulfillFeePaid($regionOrder->getFulfillFeePaid());
                $order->setRemarketingStatus($regionOrder->getRemarketingStatus());
                $order->setStatus($regionOrder->getStatus());
                $order->setStatusUrl($regionOrder->getStatusUrl());
                $order->setPaymentLog($regionOrder->getPaymentLog());
                $order->setFraudStatus($regionOrder->getFraudStatus());
                $order->setStatsStatus($regionOrder->getStatsStatus());
                $order->setType($regionOrder->getType());
                $order->setAddressVerified($regionOrder->getAddressVerified());
                $order->setOrderNote($regionOrder->getOrderNote());
                $order->setCreatedAt($regionOrder->getCreatedAt());
                $order->setUpdatedAt($regionOrder->getUpdatedAt());
                $order->setFulfilledAt($regionOrder->getFulfilledAt());
                $order->setDeliveredAt($regionOrder->getDeliveredAt());
                $order->setReceivedAt($regionOrder->getReceivedAt());
                $order->setCurrencyRate($regionOrder->getCurrencyRate());
                $order->setFulfillLog($regionOrder->getFulfillLog());
                $order->setFlagLog($regionOrder->getFlagLog());
                $order->setSenFulfillStatus($regionOrder->getSenFulfillStatus());
                $order->setDatestamp($regionOrder->getDatestamp());
                $order->setOrderNumber($regionOrder->getOrderNumber());
                $order->setOrderNumber2($regionOrder->getOrderNumber2());
                $order->setPersonalized($regionOrder->getPersonalized());
                $order->setLast4CardNumber($regionOrder->getLast4CardNumber());
                $order->setVisitInfo($regionOrder->getVisitInfo());
                $order->setDeletedAt(null);
            } else if($order->isPaidUnder(1)) {
                $order->setTransactionId($regionOrder->getTransactionId());
                $order->setShippingMethod($regionOrder->getShippingMethod());
                $order->setStatus($regionOrder->getStatus());
                $order->setPaymentGatewayId($regionOrder->getPaymentGatewayId());
                $order->setPaymentMethod($regionOrder->getPaymentMethod());
                $order->setPaymentLog($regionOrder->getPaymentLog());
                $order->setPaymentStatus($regionOrder->getPaymentStatus());
                $order->setPromotionRule($regionOrder->getPromotionRule());
                $order->setPromotionRuleId($regionOrder->getPromotionRuleId());
                $order->setTotalAmount($regionOrder->getTotalAmount());
                $order->setTotalDiscount($regionOrder->getTotalDiscount());
                $order->setDiscountCode($regionOrder->getDiscountCode());
                $order->setTotalPaid($regionOrder->getTotalPaid());
                $order->setInsuranceFee($regionOrder->getRawInsuranceFee());
                $order->setTotalShippingAmount($regionOrder->getTotalShippingAmount());
                $order->setTipAmount($regionOrder->getTipAmount());
                $order->setTotalTaxAmount($regionOrder->getTotalTaxAmount());
                $order->setTotalShippingCost($regionOrder->getTotalShippingCost());
                $order->setOrderNote($regionOrder->getOrderNote());
                $order->setCurrencyCode($regionOrder->getCurrencyCode());
                $order->setCurrencyRate($regionOrder->getCurrencyRate());
            }
            /**
             * Some information we only sync if order is not paid or paid under 24 hours
             */
            if (!$order->isPaid() || $order->isPaidUnder(24)) {
                $order->setCountry($regionOrder->getCountry());
                $order->setHouseNumber($regionOrder->getHouseNumber());
                $order->setMailboxNumber($regionOrder->getMailboxNumber());
                $order->setAddress($regionOrder->getAddress());
                $order->setAddress2($regionOrder->getAddress2());
                $order->setCity($regionOrder->getCity());
                $order->setState($regionOrder->getState());
                $order->setPostcode($regionOrder->getPostcode());
                $order->setCustomerEmail($regionOrder->getCustomerEmail());
                $order->setCustomerName($regionOrder->getCustomerName());
                $order->setCustomerPhone($regionOrder->getCustomerPhone());
            }
            /**
             * 2. Sync order products
             *
             * But we only sync order products if order is not paid
             */
            $orderId = (int)data_get($order, 'id');
            if ($changePaymentStatusToPaid || !$order->isPaid()) {
                $order->products()->delete();
                foreach ($regionOrder->products as $orderProduct) {
                    $orderProduct->setOrderId($orderId);
                    $additional_attributes = $orderProduct->additional_attributes;
                    $newOrderProduct = $order->products()->create($orderProduct->makeHidden(['id', 'additional_attributes'])->toArray());
                    $orderProductId = (int)data_get($newOrderProduct, 'id');
                    if (!empty($additional_attributes) && $orderProductId) {
                        $additional_attributes = Str::isJson($additional_attributes) ? json_decode($additional_attributes, true, 512, JSON_THROW_ON_ERROR) : [];
                        foreach ($additional_attributes as $key => $values) {
                            $values = array_filter(array_map(static function ($value) use ($orderId, $orderProductId, $key) {
                                if (empty($value)) {
                                    return null;
                                }
                                if ($key === 'design_files') {
                                    $value = array_merge($value, ['id' => generateUUID(), 'order_id' => $orderId]);
                                }
                                return array_merge($value, ['order_product_id' => $orderProductId]);
                            }, $values));
                            if ($key === 'design_files') {
                                Design::query()->insert($values);
                            }
                            if ($key === 'no_ship_logs') {
                                OrderProductLog::query()->insert($values);
                            }
                        }
                    }
                }
            }
            $order->setRegionSyncedAt($now);
            $order->setUpdatedAt($now);
            // make sure paid at set after check to sync products
            $order->setPaidAt($regionOrder->getPaidAt());
            /**
             * 3. Sync order customer
             */
            if ($order->getCustomerEmail()) {
                $customer = Customer::query()->where('email', $order->getCustomerEmail())->first();

                if (!$customer) {
                    $customer = Customer::query()->make();
                    $customer->setEmail($order->getCustomerEmail());
                }

                $customer->setName($order->getCustomerName());
                $customer->save();

                $address = CustomerAddress::query()->make();
                $address->setUserId($customer->getId());
                $address->setName($order->getCustomerName());
                $address->setPhone($order->getCustomerPhone());
                $address->setAddress($order->getAddress());
                $address->setAddress2($order->getAddress2());
                $address->setCity($order->getCity());
                $address->setState($order->getState());
                $address->setPostcode($order->getPostcode());
                $address->setCountry($order->getCountry());

                if (!$address->checkAddressExisted()) {
                    $address->setId(generateUUID());
                    $address->save();
                }

                // We sync customer id to order
                $order->setCustomerId($customer->getId());

                // We sync customer to seller
                if ($order->getSellerId()) {
                    $sellerCustomer = SellerCustomer::query()->where('customer_id', $customer->getId())->where('seller_id', $order->getSellerId())->exists();
                    if (!$sellerCustomer) {
                        SellerCustomer::query()->insertOrIgnore([
                            'seller_id' => $order->getSellerId(),
                            'store_id' => $order->getStoreId(),
                            'customer_id' => $customer->getId(),
                        ]);
                    }
                }
            }
            // process save
            $order->save();

            /**
             * 4. We mark region order as synced
             */
            $regionOrder->setSyncAt($now);
            $regionOrder->setUpdatedAt($now);
            $regionOrder->setRegionSyncedAt($now);
            $regionOrder->saveQuietly();
            /**
             * 5. Sync order history
             */
            $orderHistoriesRegion = RegionOrderHistory::onRegion($this->region)->where('order_id', $regionOrder->getId())->get();
            if ($this->reSync > 0 || in_array($orderStatusBefore, [OrderStatus::DRAFT, OrderStatus::PENDING], true)) {
                OrderHistory::query()->where('order_id', $order->getId())->delete();
            }
            foreach ($orderHistoriesRegion as $orderHistory) {
                if ($orderHistory->getAction() === OrderHistoryActionEnum::PAID_BY_CUSTOMER && $regionOrder->getPaymentSummary()) {
                    OrderHistory::query()->create([
                        'id' => generateUUID(),
                        'order_id' => $order->getId(),
                        'action' => OrderHistoryActionEnum::PAYMENT_SUMMARY,
                        'display_level' => OrderHistoryDisplayLevelEnum::ADMIN,
                        'order_status' => $orderHistory->getOrderStatus(),
                        'fulfill_status' => $orderHistory->getFulfillStatus(),
                        'support_status' => $orderHistory->getSupportStatus(),
                        'admin_detail' => $orderHistory->getAdminDetail(),
                        'assignee' => $orderHistory->getAssignee(),
                        'detail' => $regionOrder->getPaymentSummary(),
                        'created_at' => $orderHistory->getCreatedAt(),
                    ]);
                }
                OrderHistory::query()->create([
                    'id' => generateUUID(),
                    'order_id' => $order->getId(),
                    'action' => $orderHistory->getAction(),
                    'display_level' => $orderHistory->getDisplayLevel(),
                    'order_status' => $orderHistory->getOrderStatus(),
                    'fulfill_status' => $orderHistory->getFulfillStatus(),
                    'support_status' => $orderHistory->getSupportStatus(),
                    'admin_detail' => $orderHistory->getAdminDetail(),
                    'assignee' => $orderHistory->getAssignee(),
                    'detail' => $orderHistory->getDetail(),
                    'created_at' => $orderHistory->getCreatedAt(),
                ]);
            }
            /**
             * We only dispatch OrderPaidEvent on the first time order is paid
             */
            if ($changePaymentStatusToPaid) {
                OrderPaymentCompleted::dispatch($order);
            }
            $endTime = round(microtime(true) * 1000);
            $time = $endTime - $startTime;
            graylogInfo('Synced region order token: ' . $regionOrder->getAccessToken() . ' in ' . $time . ' ms', [
                'category' => 'sync_order_job',
                'region_order' => $regionOrder
            ]);
            return;
        } catch (\Exception $e) {
            logToDiscord('Sync Order Job Error: ' . $e->getMessage() . ' - File: ' . $e->getFile() . ' - Line: ' . $e->getLine(), 'error', true);
            graylogError('Sync Order Job Error', [
                'category' => 'sync_order_job_fatal_error',
                'data' => $e,
                'sync_unique_id' => $this->uniqueId()
            ]);
            $isDuplicateId = $this->validateDuplicateFieldData($e->getMessage());
            if ($isDuplicateId && $this->reSync < 3) {
                $this->reSync++;
                $this->handle();
            }
            return;
        }
    }

    /**
     * @param $message
     * @param string $fieldName
     * @return bool
     */
    private function validateDuplicateFieldData($message, string $fieldName = 'PRIMARY'): bool
    {
        $pattern = "/Duplicate entry '([^']+)' for key '$fieldName'/";
        if (preg_match($pattern, $message, $matches)) {
            return true;
        }
        return false;
    }
}

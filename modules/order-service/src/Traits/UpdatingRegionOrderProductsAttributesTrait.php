<?php

namespace Modules\OrderService\Traits;

use App\Enums\DesignByEnum;
use App\Enums\OrderProductFulfillStatus;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

trait UpdatingRegionOrderProductsAttributesTrait
{

    /**
     * Is product in store, campaign or collection
     * @param $promotionRule
     * @return bool
     */
    private function matchRule($promotionRule): bool
    {
        $promotionRules = json_decode($promotionRule?->rules);

        if (
            $this->quantity === 0 || $this->fulfill_status === OrderProductFulfillStatus::OUT_OF_STOCK
            || $this->fulfill_status === OrderProductFulfillStatus::CANCELLED
        ) {
            return false;
        }
        if (isset($promotionRules?->get?->same_campaign) && $promotionRules?->get?->same_campaign) {
            if ($this->campaign_id == $this->related_campaign_id && $this->product_id == $this->related_product_id) {
                return false;
            }
        } else {
            if ($this->campaign_id == $this->related_campaign_id) {
                return false;
            }
        }

        if (!empty($promotionRule->campaign_id) && $this->related_campaign_id !== $promotionRule->campaign_id) {
            return false;
        }

        if (
            $promotionRule->collections_id
            && $this->relationLoaded('collections')
            && !$this->collections->contains('collections_id', $promotionRule->collections_id)
        ) {
            return false;
        }

        if (!empty($promotionRule->template_id)) {
            return $this->template_id === $promotionRule->template_id;
        }

        return true;
    }

    /**
     * @param $promotionRule
     * @return bool
     */
    public function findX($promotionRule, &$totalQuantity, &$totalAmount): bool
    {
        try {
            $minimumAmount = 0;
            $minimumQuantity = 0;

            $jsonData = json_decode($promotionRule->rules);

            if (isset($jsonData->buy)) {
                $buyRules = $jsonData->buy;
                $minimumAmount = $buyRules->minimum_amount ?? 0;
                $minimumQuantity = $buyRules->minimum_quantity ?? 0;
            }
            $totalQuantity += $this->quantity;
            $totalAmount += $this->price;

            if ($this->matchRule($promotionRule) && $this->quantity > 0) {
                //Log::channel('stack')->info('found X: ' . $product->campaign_id);
                if ($this->quantity === 0) {
                    return false;
                }

                if ($totalQuantity >= $minimumQuantity && $totalAmount >= $minimumAmount) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @param $promotionRule
     * @return bool
     */
    public function findY($promotionRule, $totalQuantity): bool
    {
        $jsonData = json_decode($promotionRule->rules);
        $getYRules = $jsonData->get;
        $getQuantity = $getYRules->quantity ?? 0;
        $discountPercentage = $getYRules->discount_percentage ?? 0;
        $discountPercentage /= 100;

        if ($this->matchGetYRule($promotionRule) && $this->quantity > 0) {
            if ($this->quantity === 0) {
                return false;
            }

            $discountAmount = $this->price * $discountPercentage;
            $this->discount_amount += $discountAmount * ($this->quantity ?? 0);

            if ($totalQuantity >= $getQuantity) {
                return true;
            }
        }

        return $totalQuantity > 0;
    }

    /**
     * @param $promotionRule
     * @return bool
     */
    public function matchGetYRule( $promotionRule): bool
    {
        $jsonData = json_decode($promotionRule->rules);
        $getYRule = $jsonData->get;

        if (!empty($getYRule->campaigns_id)) {
            return $this->campaign_id !== $getYRule->campaigns_id;
        }

        if (
            !empty($getYRule->collections_id)
            && $this->relationLoaded('collections')
            && !$this->collections->contains('collections_id', $getYRule->collections_id)
        ) {
            return false;
        }

        return true;
    }

    /**
     * ------------------------------
     * Getters
     * ------------------------------
     */

    public function getOrderId()
    {
        return $this->order_id;
    }


    /**
     * ------------------------------
     * Setters
     * ------------------------------
     */

    public function setOrderId($value)
    {
        $this->order_id = $value;
    }

    /**
     * @return bool
     */
    public function isDesignBySenPrints(): bool
    {
        return $this->isAiMockupOrder() || $this->design_by === DesignByEnum::SENPRINTS;
    }

    /**
     * @return bool
     */
    public function isAiMockupOrder(): bool
    {
        return $this->campaign_type === ProductSystemTypeEnum::AI_MOCKUP;
    }

    public function rememberOldData(): void
    {
        $this->old_seller_profit = $this->seller_profit;
        $this->old_quantity = $this->quantity;
        $this->old_artist_profit = $this->artist_profit;
        $this->old_shipping_cost = $this->shipping_cost;
        $this->old_discount_amount = $this->discount_amount;
        $this->old_total_amount = $this->total_amount;
        $this->old_related_campaign_id = $this->related_campaign_id;
        $this->old_related_product_id = $this->related_product_id;
        $this->old_promotion_rule_id = $this->promotion_rule_id;
    }

    public function removeOldData(): void
    {
        unset(
            $this->old_seller_profit,
            $this->old_artist_profit,
            $this->old_shipping_cost,
            $this->old_discount_amount,
            $this->old_quantity,
            $this->old_total_amount,
            $this->old_related_campaign_id,
            $this->old_related_product_id,
            $this->old_promotion_rule_id
        );
    }
}

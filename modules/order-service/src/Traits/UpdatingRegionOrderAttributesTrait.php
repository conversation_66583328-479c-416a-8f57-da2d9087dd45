<?php
namespace Modules\OrderService\Traits;

use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PromotionTypeEnum;
use App\Models\Order;
use App\Models\PromotionRule;
use App\Services\OrderService;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\OrderService\Models\RegionOrders;

trait UpdatingRegionOrderAttributesTrait
{
    /**
     * @param Order|RegionOrders $order
     * @param $discountCode
     * @param bool $inOrderCreating
     * @return bool
     */
    private function applyDiscount(Order|RegionOrders $order, $discountCode, bool $inOrderCreating = false): bool
    {
        if ($order->isServiceOrder() || $order->isCustomServiceOrder()) {
            return false;
        }
        // check if we have this code
        $promotion = PromotionRule::query()
            ->where([
                'discount_code' => $discountCode,
                'status' => 1
            ])
            ->where(function ($query) use ($order) {
                $query->where('seller_id', $order->seller_id)
                    ->orWhere('store_id', $order->store_id);
            })
            ->where(function ($query) {
                $query->where('start_time', '<', now())
                    ->orWhereNull('start_time');
            })
            ->where(function ($query) {
                $query->where('end_time', '>', now())
                    ->orWhereNull('end_time');
            })
            ->first();
        if (is_null($promotion)) {
            if (empty($discountCode)) {
                $order->discount_code = null;
                $order->promotion_rule_id = null;
                return true;
            }
            return false;
        }
        $allowOrderDiscountCode = true;
        if (!$inOrderCreating && $promotion->type === PromotionTypeEnum::BUNDLE_DISCOUNT) {
            $allowOrderDiscountCode = false;
            $orderProductCollection = $order->products->filter(fn($item) => !$item->combo_id || $item->campaign_type !== ProductSystemTypeEnum::COMBO)->values();
            $orderProductArr = $orderProductCollection->toArray();
            usort($orderProductArr, function ($a, $b) {
                return $b['price'] - $a['price'];
            });
            $response = (new OrderService())->mapBundleDiscountInCheckout($promotion, $orderProductArr, 0);
            if (!empty($response)) {
                $productsInBundle = $response['products'];
                usort($productsInBundle, function ($a, $b) {
                    return $b['price'] - $a['price'];
                });

                if (!empty($productsInBundle)) {
                    $limitBundleProducts = app(OrderService::class)->orderPromotionGetLimitBdProduct($promotion);
                    $productIdsInBD = array_column($productsInBundle, 'id');
                    $relatedCampaignId = $response['related_campaign_id'];
                    $relatedProductId = $response['related_product_id'];
                    $numProductOnBundle = 0;
                    if ($this->checkSatifyMinNumberProductInBd($promotion, $orderProductCollection, $productIdsInBD, $relatedCampaignId, $relatedProductId)) {
                        $numProductsFollowBundle = 0;
                        foreach ($orderProductCollection as $key => $op) {
                            $productInBundle = false;
                            $allowSetPromotionOrderProductInfo = false;
                            if ($op->campaign_id == $relatedCampaignId) {
                                $productInBundle = true;
                            }

                            if (in_array($op->product_id, $productIdsInBD)) {
                                $productInBundle = true;
                                if ($op->campaign_id != $relatedCampaignId) {
                                    $numProductsFollowBundle += 1;
                                }
                            }

                            if (($productInBundle && (isset($limitBundleProducts) && $numProductsFollowBundle <= $limitBundleProducts)) || !isset($limitBundleProducts)) {
                                $allowSetPromotionOrderProductInfo = true;
                            }
                            if ($allowSetPromotionOrderProductInfo) {
                                $op->related_campaign_id = $relatedCampaignId;
                                $op->promotion_rule_id = $promotion?->id;
                                $op->related_product_id = $relatedProductId;
                                $numProductOnBundle += 1;
                            } else {
                                $op->related_campaign_id = null;
                                $op->related_product_id = null;
                                $op->promotion_rule_id = null;
                            }
                        }

                        if ($numProductOnBundle > 1) {
                            $order->discount_code = $discountCode;
                            $order->promotion_rule_id = $promotion->id;
                        }
                    } else {
                        foreach ($orderProductCollection as $op) {
                            $op->related_campaign_id = null;
                            $op->promotion_rule_id = null;
                            $op->related_product_id = null;
                        }
                        $allowOrderDiscountCode = false;
                    }
                }
            }
        }

        if ($allowOrderDiscountCode) {
            $order->discount_code = $discountCode;
            $order->promotion_rule_id = $promotion->id;
        }

        $order->setRelation('promotion', $promotion);
        return true;
    }


    private function getType(): string
    {
        $default = OrderTypeEnum::REGULAR;

        foreach ($this->data->lineItems as $lineItem) {
            // sản phẩm của seller
            if ($lineItem->senFulfillStatus === OrderSenFulfillStatus::NO) {
                return OrderTypeEnum::CUSTOM;
            }
        }

        return $default;
    }

    /**
     * @param $orderProduct
     * @return bool
     */
    public function checkDiscountAmountBundleDiscount ($orderProduct): bool {
        $opPromotionRule = $orderProduct->promotionRule;
        if (isset($opPromotionRule) && ($opPromotionRule->type == PromotionTypeEnum::BUNDLE_DISCOUNT)) {
            return false;
        }
        return true;
    }
}

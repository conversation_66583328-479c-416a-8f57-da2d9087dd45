<?php

namespace Modules\OrderService\Actions\RegionOrder;

use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderStatus;
use App\Models\Campaign;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Services\OrderService;
use App\Services\StoreService;
use App\Traits\StripePaymentDescriptors;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use LogicException;
use Modules\OrderService\Jobs\DatabaseSync\SyncOrderToRegion;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Services\RegionOrderService;
use UnexpectedValueException;

class GetRegionOrder
{
    use StripePaymentDescriptors;

    private $accessToken;
    private $regionName;

    public function __construct(
        string $accessToken
    )
    {
        $this->accessToken = $accessToken;
    }

    /**
     * @throws \Throwable
     */
    public function handle(Request $request)
    {
        $this->regionName = config('app.region');

        $storeInfo = StoreService::getCurrentStoreInfo();
        if (!$storeInfo) {
            throw new LogicException('Store is invalid');
        }

        $ref = $request->get('ref');
        $order = RegionOrders::onRegion($this->regionName)->select(['id', 'region'])->where('access_token', $this->accessToken)->first();
        if (!$order) {
            $order = Order::query()->select(['id', 'region'])->where('access_token', $this->accessToken)->first();
        }
        if (!$order) {
            throw new UnexpectedValueException('Not found order in region and master');
        }
        if (!$order->region || $order->isPaid()) {
            $query = Order::query();
        } else {
            $query = RegionOrders::onRegion($order->region);
        }

        $query = $query
            ->where('access_token', $this->accessToken)
            ->where(function ($q) use ($storeInfo) {
                $q->where('store_id', $storeInfo->id);
                $q->orWhere('seller_id', $storeInfo->seller_id);
            })
            ->with(['request_cancel' => function ($query) {
                $query->select([
                    'order_id',
                    'sent_email',
                    'status'
                ])->whereNotIn('status', [
                    OrderCancelRequestStatus::CANCELLED,
                    OrderCancelRequestStatus::COMPLETED,
                ]);
            }])
            ->with(['products' => static function ($query) {
                $query->with([
                    'shipping_rule' => function ($q) {
                        $q->select([
                            'id',
                            'shipping_method',
                            'shipping_cost',
                            'extra_cost',
                            'min_days',
                            'max_days',
                        ]);
                    }
                ]);
            }])
            ->with('store:id,name,logo_url,domain,sub_domain');

        if ($ref === 'thank_you') {
            $query->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::PROCESSING,
                OrderStatus::PENDING_PAYMENT,
            ]);
        } else {
            $query->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT
            ]);
        }
        $order = $query->first();

        if (!$order) {
            graylogError('Not found order', [
                'category' => 'get_region_order',
                'access_token' => $this->accessToken,
                'region' => $this->regionName,
                'store_data' => $storeInfo,
            ]);
            throw new UnexpectedValueException('Order not found');
        }

        if ($order->region) {
            $order->withProducts($order->region, ['shipping_rule' => function ($q) {
                $q->select([
                    'id',
                    'shipping_method',
                    'shipping_cost',
                    'extra_cost',
                    'min_days',
                    'max_days',
                ]);
            }]);
        }

        $campaigns = [];
        $sellers = User::query()
            ->selectRaw('id,name,status,custom_payment,tags,sharding_status,db_connection')
            ->whereIn('id', $order->products->pluck('seller_id')->unique()->values()->toArray())
            ->get();
        $order->products->map(function ($product) use (&$campaigns, $sellers) {
            $seller = $sellers->firstWhere('id', $product->seller_id);
            $product->setRelation('product', Product::query()->onSellerConnection($seller)->select(['id', 'campaign_id', 'options'])->find($product->product_id));
            $seller = $sellers->firstWhere('id', $product->seller_id);
            if ($product->combo_id && $product->campaign_id) {
                $campaignId = $product->campaign_id;
                if (!isset($campaigns[$campaignId])) {
                    $campaign = Campaign::query()->onSellerConnection($seller)->find($campaignId);
                    if ($campaign) {
                        $campaigns[$campaignId] = $campaign;
                    }
                }
                if (isset($campaigns[$campaignId])) {
                    $product->campaign = $campaigns[$campaignId];
                }
            }
            if ($product->product && Str::isJson($product->product->options) && Str::isJson($product->options)) {
                $productOptions = json_decode($product->product->options, true, 512, JSON_THROW_ON_ERROR);
                $options = json_decode($product->options, true, 512, JSON_THROW_ON_ERROR);
                if (isset($options['color'], $productOptions['color']) && count($productOptions['color']) === 1 && data_get($productOptions, 'color.0') === 'white') {
                    unset($options['color']);
                }
                if (isset($options['size'], $productOptions['size']) && count($productOptions['size']) === 1) {
                    unset($options['size']);
                }
                $product->options = json_encode($options, JSON_THROW_ON_ERROR);
            }
            unset($product->product);
            return $product;
        });

        //Re-calculate order after 5 minute
        $oldUpdatedAt = $order->updated_at;
        $currentTimestamp = Carbon::now()->timestamp;
        $orderUpdatedTimestamp = Carbon::parse($oldUpdatedAt)->timestamp;
        $fiveMin = 5 * 60;

        if ($currentTimestamp - $orderUpdatedTimestamp > $fiveMin && in_array($order->status, [OrderStatus::DRAFT, OrderStatus::PENDING], true)) {
            $order->assignSupplier(source: Order::CHECKOUT_ASSIGN_SUPPLIER, isRegion: true, inCreatingOrder: true);
            $order->calculateOrder();
            $order->updated_at = $oldUpdatedAt;
        }

        // load payment by order store
        if (!empty($order->store) && $order->store->id !== $storeInfo->id) {
            $storeInfo = StoreService::getCurrentStoreInfo($order->store->domain ?? $order->store->sub_domain);
        }
        $paymentGateways = OrderService::singleton()->getPaymentGateways($storeInfo, $order);

        //return insurance fee for display
        $order->insurance_fee_2 = 0;
        if ($storeInfo && $storeInfo->enable_insurance_fee) {
            $order->insurance_fee_2 = $order->getInsuranceFee();
        }

        $response = [
            'payment_gateways' => $paymentGateways['gateways'],
            'payment_domain' => $paymentGateways['paypalCheckoutDomain'],
            'shipping_methods' => $order->availableShippingMethods(),
            'separate_name' => $order->isSeparateName(),
        ];
        if (self::isStripePaymentMethod($order->payment_method)) {
            try {
                $statementDescriptor = self::getFullStatementDescriptor(
                    $order->payment_gateway_id,
                    $order->order_number
                );

                if ($statementDescriptor) {
                    $response['statement_descriptor'] = $statementDescriptor;
                }
            } catch (\Throwable $e) {}
        }
        try {
            if (RegionOrderService::needSyncToRegion($order)) {
                SyncOrderToRegion::dispatch($order, $this->regionName)->onQueue('sync_order_region');
            }
            $masterOrder = Order::query()->where('access_token', $order->getAccessToken())->first();
            if ($masterOrder && $masterOrder->isPaid() && $masterOrder->isDateAfter('updated_at', $order->getRegionSyncedAt())) {
                SyncOrderToRegion::dispatch($masterOrder, $order->getRegion(), syncOrderProducts: false)->onQueue('sync_order_region');
            }
        } catch (\Exception $e) {}
        $order->products = $order->products->map(function ($product) {
            return $product->only([
                'id',
                'campaign_id',
                'campaign_title',
                'product_name',
                'product_url',
                'options',
                'custom_options',
                'order_id',
                'price',
                'product_id',
                'template_id',
                'base_cost',
                'base_shipping_cost',
                'fulfill_shipping_cost',
                'shipping_cost',
                'quantity',
                'thumb_url',
                'total_amount',
                'discount_amount',
                'personalized',
                'full_printed',
                'fulfill_status',
                'shipping_rule_id',
                'supplier_id',
                'shipping_rule',
                'collections',
                'promotionRule',
                'combo_id',
                'campaign',
            ]);
        });
        $order = $order->only([
            'id',
            'access_token',
            'discount_code',
            'order_number',
            'type',
            'payment_method',
            'payment_gateway_id',
            'promotion_rule_id',
            'shipping_method',
            'total_amount',
            'total_discount',
            'payment_discount',
            'tip_amount',
            'total_product_amount',
            'total_quantity',
            'total_shipping_amount',
            'total_tax_amount',
            'insurance_fee',
            'insurance_fee_2',
            'country',
            'store_id', // for checking discount rule
            'seller_id',
            'updated_at',
            'customer_name',
            'customer_email',
            'customer_phone',
            'mailbox_number',
            'house_number',
            'address',
            'address_2',
            'city',
            'state',
            'postcode',
            'currency_code',
            'currency_rate',
            'address_verified',
            'status',
            'fulfill_status',
            'fraud_status',
            'session_id',
            'paid_at',
            'device',
            'products',
            'request_cancel',
            'store',
        ]);
        $response['order'] = $order;
        return $response;
    }
}

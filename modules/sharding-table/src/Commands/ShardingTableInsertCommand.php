<?php

namespace Modules\ShardingTable\Commands;

use App\Enums\CacheKeys;
use App\Enums\DiscordChannel;
use App\Enums\FileTypeEnum;
use App\Enums\UserRoleEnum;
use App\Models\Campaign;
use App\Models\File;
use App\Models\IndexCampaign;
use App\Models\User;
use App\Traits\ElasticClient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Modules\ShardingTable\Enums\TableShardingEnum;
use Modules\ShardingTable\Enums\TempStatusEnum;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;
use Modules\ShardingTable\Jobs\InsertCampaignJob;
use Modules\ShardingTable\Jobs\InsertFileJob;
use Modules\ShardingTable\Jobs\SyncRemainingCampaignJob;

class ShardingTableInsertCommand extends Command
{
    use ElasticClient;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sharding-table:insert';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sharding table for seller by id';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        try {
            $this->createTable();
            User::query()
                ->select('id', 'sharding_status', 'db_connection')
                ->where('role', '!=', UserRoleEnum::CUSTOMER)
                ->where('sharding_status', UserShardingStatusEnum::SYNCING)
                ->get()
                ->each(function (User $user) {
                    InsertFileJob::dispatch($user);
                    $query = IndexCampaign::query();
                    if ($user->db_connection != config('database.default')) {
                        $query = Campaign::query()->on($user->db_connection);
                    }
                    $query->withTrashed()
                        ->select('id')
                        ->where('seller_id', $user->id)
                        ->where('temp_status', '>', TempStatusEnum::SYNCHRONIZED)
                        ->orderBy('id')
                        ->take(1000)
                        ->get()
                        ->each(function (IndexCampaign|Campaign $campaign) use ($user) {
                            InsertCampaignJob::dispatch((int)$campaign->id, $user);
                        });
                    $files = File::query()
                        ->onSellerConnection($user)
                        ->select('id')
                        ->where('seller_id', $user->id)
                        ->where('type', FileTypeEnum::FONT)
                        ->where('temp_status', '>', TempStatusEnum::SYNCHRONIZED)
                        ->exists();
                    $campaignModel = Campaign::query()->onSellerConnection($user);
                    if ($user->db_connection === config('database.default')) {
                        $campaignModel = IndexCampaign::query();
                    }
                    $campaigns = $campaignModel
                        ->withTrashed()
                        ->select('id')
                        ->where('seller_id', $user->id)
                        ->where('temp_status', '>', TempStatusEnum::SYNCHRONIZED)
                        ->exists();
                    if (!$campaigns && !$files) {
                        SyncRemainingCampaignJob::dispatch($user, $user->db_connection)->delay(now()->addMinutes(30));
                        $user->update(['sharding_status' => UserShardingStatusEnum::COMPLETED, 'db_connection' => 'mysql_' . $user->id]);
                        logToDiscord("Seller: {$user->id} change sharding status to 3", DiscordChannel::SHARDING_DATABASE);
                    }
                });

        } catch (\Throwable $e) {
            logToDiscord("Sharding table: ERROR: " . "{$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}", DiscordChannel::SHARDING_DATABASE);
        }
    }

    /**
     * @return void
     */
    private function createTable(): void
    {
        $processingSellers = User::query()
            ->select('id', 'role', 'sharding_status')
            ->where('role', '!=', UserRoleEnum::CUSTOMER)
            ->where('sharding_status', UserShardingStatusEnum::PROCESSING)->get();
        $processingSellers->each(function (User $user) {
            config()->set([
                "database.connections.mysql_$user->id" => config("database.connections.mysql_seller"),
            ]);
            $database = config("database.connections.mysql.database") . "_$user->id";
            DB::connection("mysql_$user->id")->statement("CREATE DATABASE IF NOT EXISTS `$database`");
            config()->set([
                "database.connections.mysql_$user->id" => config("database.connections.mysql_seller"),
                "database.connections.mysql_$user->id.database" => $database,
            ]);
            foreach (TableShardingEnum::asArray() as $table) {
                $query = DB::select("SHOW CREATE TABLE $table")[0]->{'Create Table'};
                DB::connection("mysql_$user->id")->statement(str_replace('CREATE TABLE ', "CREATE TABLE IF NOT EXISTS `$database`.", $query));
            }
            logToDiscord("Database $database created.", DiscordChannel::SHARDING_DATABASE);

            $status = $this->createIndex("products_$user->id");
            if (!$status) {
                throw new \RuntimeException("Cannot create index products_$user->id");
            }
            logToDiscord("Elasticsearch index products_$user->id created.", DiscordChannel::SHARDING_DATABASE);

            $user->sharding_status = UserShardingStatusEnum::SYNCING;
            $status = $user->save();
            if (!$status) {
                throw new \RuntimeException("Seller: $user->id can't change sharding status to 2");
            }
            syncClearCache(['tags' => [CacheKeys::SELLER_CONNECTION]], CacheKeys::CACHE_TYPE_ALTERNATIVE);
            logToDiscord("Seller: $user->id change sharding status to 2", DiscordChannel::SHARDING_DATABASE);
        });
    }
}

<?php
namespace Modules\ShardingTable\Providers;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;

class ShardingTableServiceProvider extends ServiceProvider
{
    const DEFAULT_TIMEZONE = 'Asia/Ho_Chi_Minh';
    /**
     * Bootstrap ShardingTable service provider service.
     *
     * @return void
     */
    public function boot()
    {
        if(!defined('SHARDING_TABLE_MODULE_PATH')) {
            define('SHARDING_TABLE_MODULE_PATH', dirname(__DIR__, 2));
        }
        $this->autoload(SHARDING_TABLE_MODULE_PATH . '/helpers');
        $this->loadMigrationsFrom(SHARDING_TABLE_MODULE_PATH . '/database/migrations');
        $this->app->booted(function () {
            $this->app->register(CommandServiceProvider::class);
            $this->app->register(InitConnectionServiceProvider::class);
            $this->loadViewsFrom(SHARDING_TABLE_MODULE_PATH . '/resources/views', 'sharding_table');
            $this->registerConfigs(['sharding-table']);
            $this->registerRoutes();
            if (config('senprints.schedule_enabled')) {
                /** @var \Illuminate\Console\Scheduling\Schedule $schedule */
                $schedule = $this->app->make(Schedule::class);
                $schedule->command('sharding-table:insert')->everyMinute()->withoutOverlapping(5)->logAfter();
                $schedule->command('sharding-table:delete')->everyMinute()->withoutOverlapping(5)->logAfter();
                $schedule->command('sharding-table:update-sharding-status-by-total-campaign')->dailyAt('09:00')->runInBackground()->timezone(self::DEFAULT_TIMEZONE)->logAfter();
            }
        });
    }

    /**
     * Register the sharding_table's configs.
     *
     * @return void
     */
    protected function registerConfigs($fileNames)
    {
        if (!is_array($fileNames)) {
            $fileNames = [$fileNames];
        }
        $config_path = SHARDING_TABLE_MODULE_PATH . '/config';
        foreach ($fileNames as $fileName) {
            $full_path = $config_path . '/' . $fileName . '.php';
            $this->mergeConfigFrom($full_path, 'sharding.table.config.' . $fileName);
        }
    }

    /**
     * Register the sharding_table's routes.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }
        $route_path = SHARDING_TABLE_MODULE_PATH . '/routes';
        $routes = $this->scanFolder($route_path);
        foreach ($routes as $route) {
            $this->loadRoutesFrom($route_path . '/' . $route);
        }
    }

    /**
     * @param string $path
     * @param array $ignoreFiles
     * @return array
     */
    public function scanFolder($path, array $ignoreFiles = [])
    {
        try {
            if (File::isDirectory($path)) {
                $data = array_diff(scandir($path), array_merge(['.', '..', '.DS_Store'], $ignoreFiles));
                natsort($data);
                return array_values($data);
            }
            return [];
        } catch (\Exception $exception) {
            return [];
        }
    }

    /**
     * Load helpers from a directory
     * @param string $directory
     */
    public function autoload(string $directory): void
    {
        $helpers = File::glob($directory . '/*.php');
        if(empty($helpers)) {
            return;
        }
        foreach ($helpers as $helper) {
            File::requireOnce($helper);
        }
    }
}

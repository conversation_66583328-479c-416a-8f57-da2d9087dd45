<?php

namespace Modules\ShardingTable\Jobs;

use App\Enums\DiscordChannel;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\File;
use App\Models\IndexProduct;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class DeleteCampaignJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, Queueable;

    public function __construct(readonly private array $campaignIds, readonly private int $sellerId, readonly private string $oldConnection)
    {
        $this->onQueue('sharding-table-delete');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $startTime = round(microtime(true) * 1000);
            $seller = User::query()->find($this->sellerId);
            if (!$seller || $this->oldConnection === $seller->db_connection || !$seller->shardingCompleted()) {
                return;
            }
            $limit = 500;
            if ($this->oldConnection === config('database.default')) {
                (new SyncProductsToElasticSearchJob())->elasticDeleteProductsByProductIds($this->campaignIds, 'products_archived');
                foreach (array_chunk($this->campaignIds, $limit) as $chunkIds) {
                    IndexProduct::query()
                        ->filterByProductOrCampaignIds($chunkIds)
                        ->withTrashed()
                        ->where('seller_id', $this->sellerId)
                        ->forceDelete();
                }
            }
            (new SyncProductsToElasticSearchJob())->elasticDeleteProductsByProductIds($this->campaignIds, str_replace('mysql', 'products', $this->oldConnection));
            Product::query()
                ->withTrashed()
                ->on($this->oldConnection)
                ->select('id')
                ->where('seller_id', $this->sellerId)
                ->whereIn('campaign_id', $this->campaignIds)
                ->get()
                ->chunk($limit)
                ->each(function ($products) {
                    ProductVariant::query()->on($this->oldConnection)->whereIn('product_id', $products->pluck('id')->toArray())->delete();
                    File::query()->on($this->oldConnection)->whereIn('product_id', $products->pluck('id')->toArray())->delete();
                });

            foreach (array_chunk($this->campaignIds, $limit) as $chunkIds) {
                File::query()
                    ->on($this->oldConnection)
                    ->where('seller_id', $this->sellerId)
                    ->whereIn('campaign_id', $chunkIds)
                    ->delete();
                Product::query()
                    ->filterByProductOrCampaignIds($chunkIds)
                    ->withTrashed()
                    ->on($this->oldConnection)
                    ->where('seller_id', $this->sellerId)
                    ->forceDelete();
            }
            $endTime = round(microtime(true) * 1000);
            $time = $endTime - $startTime;
            graylogInfo("Delete: Seller: {$this->sellerId} campaign successfully. Time: {$time} ms", ['category' => 'sharding', 'seller_id' => $this->sellerId, 'action' => 'delete', 'campaign_ids' => $this->campaignIds]);
        } catch (\Throwable $e) {
            logException($e, 'DeleteCampaignJob@handle', DiscordChannel::SHARDING_DATABASE);
        }
    }
}

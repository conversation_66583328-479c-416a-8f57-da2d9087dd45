<?php
namespace Modules\Campaign\Commands;

use App\Enums\FileTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\StorageDisksEnum;
use App\Library\UrlUploadedFile;
use App\Models\File;
use App\Models\IndexProduct;
use App\Models\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

class ProcessDownloadProductThumb extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'campaigns:process-download-product-thumb';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process download product thumb for campaign';

    /**
     * @var int
     */
    protected $seller_ids = [104859, 1056758, 217, 101432, 123215, 1057615, 1164576, 1007, 104759, 124019, 104663, 104111];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $products = IndexProduct::query()->select([ 'id', 'thumb_url', 'seller_id', 'campaign_id', 'default_product_id'])
                ->whereIn('seller_id', $this->seller_ids)
                ->whereIn('system_type', [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP])
                ->where('status', ProductStatus::ACTIVE)
                ->withoutTrashed()
                ->where('thumb_url', 'LIKE', 'https://drive.google.com%')
                ->limit(10)
                ->get();

            if ($products->isEmpty()) {
                return false;
            }

            foreach ($products as $product) {
                $defaultProductId = $product->default_product_id ?? $product->id;
                $files = File::query()
                    ->select(['id', 'file_url_2'])
                    ->where('product_id', $defaultProductId)
                    ->where('type', '!=' ,FileTypeEnum::DESIGN)
                    ->whereNotNull('file_url_2')
                    ->get()
                    ->toArray();

                if (!empty($files)) {
                    $filteredFiles = array_filter($files, static function($file) {
                        return !empty($file['file_url_2']) && Str::startsWith($file['file_url_2'], 'p/');
                    });

                    if (!empty($filteredFiles) && count($filteredFiles) > 0) {
                        //update to product->thumb_url
                        $this->updateProductThumb($product->id, $filteredFiles[0]['file_url_2']);
                        graylogInfo('updated thumb url for product', [
                            'category' => 'process_update_thumb',
                            'product_id' => $product->id,
                            'thumb_url' => $filteredFiles[0]['file_url_2']
                        ]);
                        continue;
                    }
                }

                // DOWNLOAD
                $file_url_download = $product->thumb_url;
                $hash_file_url = md5(trim($file_url_download));
                $campaign_id = !empty($product->campaign_id) ? $product->campaign_id : $product->id;
                if(empty($url_downloaded[$hash_file_url])) {
                    $tmpFile = UrlUploadedFile::createFileFromUrl($file_url_download);
                    if ($tmpFile === null) {
                        $tmpFile = UrlUploadedFile::createFileFromUrl($file_url_download);
                        if($tmpFile === null) {
                            if(empty($product->campaign_id)) {
                                $this->logToDiscord('[' . currentTime() . '] - ProcessDownloadProductThumb -> Product ID: #' . $product->id . ', Can not create file from product mockup url. Url: ' . $file_url_download);
                            } else {
                                $this->logToDiscord('[' . currentTime() . '] - ProcessDownloadProductThumb -> Campaign ID: #' . $campaign_id . ', Can not create file from campaign mockup url. Url: ' . $file_url_download);
                            }
                            continue;
                        }
                    }
                    $name = $tmpFile->getClientOriginalName();
                    $file_url = $tmpFile->storePubliclyAs('p/' . $campaign_id, $name, StorageDisksEnum::DEFAULT);
                    if(empty($file_url)) {
                        if(empty($product->campaign_id)) {
                            $this->logToDiscord('[' . currentTime() . '] - ProcessDownloadProductThumb -> Product ID: #' . $product->id . ', Can not upload file to S3. Product thumb url is empty. Product ID: #' . $product->id);
                        } else {
                            $this->logToDiscord('[' . currentTime() . '] - ProcessDownloadProductThumb -> Campaign ID: #' . $campaign_id . ', Can not upload file to S3. Campaign thumb url is empty. Campaign ID: #' . $product->id);
                        }
                        continue;
                    }
                    $url_downloaded[$hash_file_url] = $file_url;
                }
                if (!empty($url_downloaded[$hash_file_url])) {
                    $this->updateProductThumb($product->id, $url_downloaded[$hash_file_url]);
                } else if(empty($product->campaign_id)) {
                    $this->logToDiscord('[' . currentTime() . '] - ProcessDownloadProductThumb -> Product ID: #' . $product->id . ', Can not get file url after upload to S3. Product thumb url is empty. Product ID: #' . $product->id);
                } else {
                    $this->logToDiscord('[' . currentTime() . '] - ProcessDownloadProductThumb -> Campaign ID: #' . $campaign_id . ', Can not get file url after upload to S3. Campaign thumb url is empty. Campaign ID: #' . $product->id);
                }
            }
            return true;
        } catch (\Throwable $exception) {
            logException($exception, 'ProcessDownloadProductThumb::handle');
        }
    }

    /**
     * @param $product_id
     * @param $thumb_url
     * @return void
     */
    private function updateProductThumb($product_id, $thumb_url) {
        IndexProduct::query()->whereKey($product_id)->update([
            'thumb_url' => $thumb_url
        ]);
        Product::query()->whereKey($product_id)->update([
            'thumb_url' => $thumb_url,
            'sync_status' => Product::SYNC_DATA_STATS_ENABLED
        ]);
    }

    /**
     * @param $message
     * @param bool $mention
     * @return void
     */
    private function logToDiscord($message, $mention = false) {
        // logToDiscord($message, 'custom_campaign');
        graylogInfo($message, [
            'category' => 'custom_campaign',
        ]);
    }
}

<?php
namespace Modules\Campaign\Commands;

use App\Services\CampaignService;
use Illuminate\Console\Command;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignTypeEnum;
use Modules\Campaign\Jobs\CreateImportedCampaignJob;
use Modules\Campaign\Models\ImportCampaignsData;

class CreateImportedCampaignCommand extends Command
{
    protected $signature = 'campaign:create-imported';

    protected $description = 'Create imported campaign';

    public function handle(): void
    {
        try {
            $this->info('Starting process...');
            $limitUsers = CampaignService::listUserLimitCreateCampaign();
            $importedCampaigns = ImportCampaignsData::query()
                ->select('id', 'seller_id')
                ->where('status', ImportCampaignStatusEnum::PENDING)
                ->whereNotIn('type', [ImportCampaignTypeEnum::REGULAR, ImportCampaignTypeEnum::REGULAR_MULTI_SPACE, ImportCampaignTypeEnum::EXPRESS_MULTI_SPACE])
                ->whereNotIn('seller_id', $limitUsers)
                ->limit(1000)
                ->get();

            if ($importedCampaigns->isEmpty()) {
                $this->info('Done.');
                return;
            }
            foreach ($importedCampaigns as $importedCampaign) {
                CreateImportedCampaignJob::dispatch($importedCampaign->id);
            }
            $this->info('Done.');
        } catch (\Throwable $exception) {
            logException($exception, 'CreateImportedCampaignCommand::handle');
        }
    }
}

<?php
namespace Modules\Campaign\Models;

use App\Models\Campaign;
use App\Models\Model;
use App\Models\Store;
use App\Models\User;
use App\Models\UserInfo;
use App\Traits\Filterable;
use App\Traits\HasRelationShipsCustom;
use App\Traits\ScopeFilterDateRangeTrait;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Carbon;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignSystemStatusEnum;
use Modules\Campaign\Enums\ImportCampaignTypeEnum;
use Modules\Campaign\Filters\ImportCampaignsDataFilter;

/**
 * Modules\Campaign\Models\ImportCampaignsData
 * @property string $id
 * @property int|null $seller_id
 * @property int|null $campaign_id
 * @property int|null $template_campaign_id
 * @property string|null $campaign_slug
 * @property string|null $campaign_name
 * @property string|null $campaign_description
 * @property string|null $collection
 * @property string|null $store_ids
 * @property string|null $market_location
 * @property string|null $pricing_mode
 * @property string|null $prefix
 * @property string|null $suffix
 * @property string|null $name_prefix
 * @property string|null $name_suffix
 * @property boolean $indefinite_article
 * @property string|null $currency
 * @property string|null $print_space
 * @property int $short_url
 * @property string|null $products
 * @property string|null $designs
 * @property string|null $mockups
 * @property string $status
 * @property string|null $device_id
 * @property string|null $session_id
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property string|null $system_status
 * @property string $logs
 * @property string $type
 * @property boolean $disable_validate_design
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon|null $limit_at
 * @property Campaign $template_campaign
 * @property Campaign $campaign
 *
 * @method static Builder|ImportCampaignsData query()
 * @method Builder|ImportCampaignsData filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static Builder|ImportCampaignsData isRegularOrExpress()
 * @method static Builder|ImportCampaignsData isRegular()
 * @method static Builder|ImportCampaignsData isExpress()
 * @method static Builder|ImportCampaignsData isFailed()
 * @method static Builder|ImportCampaignsData isProcessing()
 * @method static Builder|ImportCampaignsData isPending()
 * @method static Builder|ImportCampaignsData hasMockups()
 * @method static Builder|ImportCampaignsData hasDesigns()
 * @method static Builder|ImportCampaignsData isCompleted()
 * @method static Builder|ImportCampaignsData isStopped()
 * @method static Builder|ImportCampaignsData regularOrExpressFailed($sellerId = null)
 * @method static Builder|ImportCampaignsData isPendingOrProcessing()
 * @method static Builder|ImportCampaignsData enqueueable()
 * @method static Builder|ImportCampaignsData isRegularOrExpressMultiSpace()
 * @method static Builder|ImportCampaignsData isRegularMultiSpace()
 * @method static Builder|ImportCampaignsData isExpressMultiSpace()
 * @method static Builder|ImportCampaignsData multiSpaceEnqueueable()
 * @method static Builder|ImportCampaignsData unImportDesignUrl()
 * @method static Builder|ImportCampaignsData importDesignUrl()
 * @method static Builder|ImportCampaignsData multiSpaceUnImportDesignUrl()
 * @method static Builder|ImportCampaignsData multiSpaceShouldPrepareDesign()
 * @method static Builder|ImportCampaignsData multiSpaceStartable($sellerId = null)
 * @method static Builder|ImportCampaignsData multiSpaceStopable($sellerId = null)
 * @method static Builder|ImportCampaignsData multiSpaceRemovable($sellerId = null)
 * @method static Builder|ImportCampaignsData multiSpaceRetryable($sellerId = null, $force = false)
 * @method static Builder|ImportCampaignsData start($sellerId = null)
 * @method static Builder|ImportCampaignsData stop($sellerId = null)
 *
 * @mixin Builder
 */
class ImportCampaignsData extends Model
{
    use HasFactory;
    use ScopeFilterDateRangeTrait;
    use Filterable;
    use HasRelationShipsCustom;

    const FILTER_COLUMN_DATE = 'import_campaigns_data.created_at';
    /**
     * table name
     *
     * @var string
     */
    protected $table = 'import_campaigns_data';

    protected $appends = ['stores'];

    protected $fillable = [
        'id',
        'seller_id',
        'campaign_id',
        'campaign_slug',
        'campaign_name',
        'campaign_description',
        'collection',
        'store_ids',
        'market_location',
        'pricing_mode',
        'prefix',
        'suffix',
        'currency',
        'short_url',
        'products',
        'designs',
        'mockups',
        'status',
        'system_status',
        'temp_status',
        'logs',
        'type',
        'device_id',
        'session_id',
        'ip_address',
        'limit_at',
        'disable_validate_design',
        'retry_count',
    ];

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id')
            ->select('id', 'name', 'email')
            ->withDefault([
                'id' => '',
                'email' => '<EMAIL>',
                'name' => 'Unknown',
                'nickname' => null
            ]);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function seller_record(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function seller_info(): HasMany
    {
        return $this->hasMany(UserInfo::class, 'user_id', 'seller_id');
    }

    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'campaign_id', 'id');
    }

    /**
     * @return BelongsTo<Campaign, self>
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'template_campaign_id', 'id');
    }

    /**
    * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
    */
    public function template_campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'template_campaign_id');
    }

    /**
     * Lấy ra các bản ghi đang ở trạng thái pending và chưa được xử lý trong 10 giây
     * theo type và số lượng bản ghi
     *
     * @param     string     $type
     * @param     int        $sellerId
     * @param     int        $numRecord
     *
     * @return \Illuminate\Support\Collection
     */
    public static function takePendingRecord(string $type, int $sellerId, int $numRecord = 1): \Illuminate\Support\Collection
    {
        $params = [
            'type' => $type,
            'seller_id' => $sellerId,
            'status' => ImportCampaignStatusEnum::PENDING,
            'system_status' => ImportCampaignSystemStatusEnum::IMPORTED_DESIGN,
            'seller_bulk_campaign_status' => UserInfo::VAL_BULK_CAMPAIGN_ACTIVE
        ];

        return self::filter($params, ImportCampaignsDataFilter::class)
            ->take($numRecord)
            ->get();
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsRegularOrExpress($query): Builder
    {
        return $query->where('type', ImportCampaignTypeEnum::REGULAR)
            ->orWhere('type', ImportCampaignTypeEnum::EXPRESS);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsRegular($query): Builder
    {
        return $query->where('type', ImportCampaignTypeEnum::REGULAR);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsExpress($query): Builder
    {
        return $query->where('type', ImportCampaignTypeEnum::EXPRESS);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsRegularOrExpressMultiSpace($query): Builder
    {
        return $query->where('type', ImportCampaignTypeEnum::REGULAR_MULTI_SPACE)
            ->orWhere('type', ImportCampaignTypeEnum::EXPRESS_MULTI_SPACE);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsRegularMultiSpace($query): Builder
    {
        return $query->where('type', ImportCampaignTypeEnum::REGULAR_MULTI_SPACE);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsExpressMultiSpace($query): Builder
    {
        return $query->where('type', ImportCampaignTypeEnum::EXPRESS_MULTI_SPACE);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsFailed($query): Builder
    {
        return $query->where('status', ImportCampaignStatusEnum::FAILED);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsProcessing($query): Builder
    {
        return $query->where('status', ImportCampaignStatusEnum::PROCESSING);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsCompleted($query): Builder
    {
        return $query->where('status', ImportCampaignStatusEnum::COMPLETED);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsStopped($query): Builder
    {
        return $query->where('status', ImportCampaignStatusEnum::STOPPED);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsPending($query): Builder
    {
        return $query->where('status', ImportCampaignStatusEnum::PENDING);
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHasMockups($query): Builder
    {
        return $query->whereNotNull('mockups');
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHasDesigns($query): Builder
    {
        return $query->whereNotNull('designs');
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnImportDesignUrl($query): Builder
    {
        return $query->where(
            fn($q) => $q->isPending()->whereNull('system_status')
        );
    }

    /**
     * @param $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeImportDesignUrl($query): Builder
    {
        return $query->where(
            fn($q) => $q->isPending()->where('system_status', ImportCampaignSystemStatusEnum::IMPORTED_DESIGN)
        );
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $query
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeIsPendingOrProcessing($query): Builder
    {
        return $query->whereIn('status', [
            ImportCampaignStatusEnum::PENDING, ImportCampaignStatusEnum::PROCESSING
        ]);
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMultiSpaceEnqueueable(Builder $q): Builder
    {
        return $q->where(function($q) {
            $q->where(function($q) {
                $q->where(function ($q) {
                    $q->isRegularMultiSpace();
                    $q->isProcessing();
                });

                $q->orWhere(function ($q) {
                    $q->isExpressMultiSpace();
                    $q->isPendingOrProcessing();
                });
            });

            $q->where(function($q) {
                $q->whereNull('limit_at');
                $q->orWhere('limit_at', '<', now());
            });
        });
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMultiSpaceUnImportDesignUrl(Builder $q): Builder
    {
        return $q->where(function($q) {
            $q->isRegularMultiSpace();
            $q->unImportDesignUrl();
        });
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMultiSpaceShouldPrepareDesign(Builder $q): Builder
    {
        return $q->where(static function($q) {
            $q->isRegularMultiSpace();
            $q->isPending();
            $q->importDesignUrl();
        });
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param     int|null                                  $sellerId
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMultiSpaceStartable(Builder $q, int $sellerId = null): Builder
    {
        /** @var \Modules\Campaign\Models\ImportCampaignsData $q */
        return $q
            ->isRegularOrExpressMultiSpace()
            ->isStopped()
            ->when($sellerId, static fn($q) => $q->where('seller_id', $sellerId));
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param     int|null                                  $sellerId
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMultiSpaceStopable(Builder $q, int $sellerId = null): Builder
    {
        /** @var \Modules\Campaign\Models\ImportCampaignsData $q */
        return $q
            ->isRegularOrExpressMultiSpace()
            ->where(static function($q) {
                $q->orWhere(fn($q) => $q->isProcessing());
                $q->orWhere(fn($q) => $q->isPending());
            })
            ->when($sellerId, static fn($q) => $q->where('seller_id', $sellerId));
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param     int|null                                  $sellerId
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMultiSpaceRemovable(Builder $q, int $sellerId = null): Builder
    {
        /** @var \Modules\Campaign\Models\ImportCampaignsData $q */
        return $q
            ->isRegularOrExpressMultiSpace()
            ->where(static function($q) {
                $q->orWhere(fn($q) => $q->isFailed());
                $q->orWhere(fn($q) => $q->isPending());
            })
            ->when($sellerId, static fn($q) => $q->where('seller_id', $sellerId));
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param     int|null                                  $sellerId
     * @param     bool                                      $force
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMultiSpaceRetryable(Builder $q, int $sellerId = null, bool $force = false): Builder
    {
        /** @var \Modules\Campaign\Models\ImportCampaignsData $q */
        return $q
            ->isRegularOrExpressMultiSpace()
            ->unless($force, fn($q) => $q->isFailed())
            ->when($sellerId, static fn($q) => $q->where('seller_id', $sellerId));
    }

    /**
     * @return void
     */
    public function start(): void
    {
        $this->campaign_id = null;
        $this->system_status = null;
        $this->status = ImportCampaignStatusEnum::PROCESSING;

        // Nếu không có mockups và là regular multi space thì chuyển về trạng thái pending
        // để chờ xử lí mockups
        if (! $this->mockups && static::isRegularMultiSpace()) {
            $this->status = ImportCampaignStatusEnum::PENDING;
        }

        $this->save();
    }

    /**
     * @return void
     */
    public function stop(): void
    {
        $this->status = ImportCampaignStatusEnum::STOPPED;
        $this->save();
    }

    /**
     * @return Store[]|array|Builder[]|\Illuminate\Database\Eloquent\Collection
     * @throws \Throwable
     */
    public function getStoresAttribute()
    {
        $stores = [];
        if (!empty($this->store_ids)) {
            $store_ids = json_decode($this->store_ids, true, 512, JSON_THROW_ON_ERROR);
            if (empty($store_ids)) {
                return $stores;
            }
            $stores = Store::query()->select('id', 'name')->whereIn('id', $store_ids)->get();
        }
        return $stores;
    }
}

<?php

namespace Modules\Campaign\Services;

use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Http\Controllers\CampaignController;
use App\Models\File;
use App\Models\Product;
use App\Models\User;
use App\Services\CampaignService;
use Illuminate\Support\Collection;
use RuntimeException;

class ApplyDesigns3DToProductService extends ApplyDesignsToProductService
{
    /**
     * @param Product $campProduct
     * @param array|Collection $designs
     * @param User $seller
     * @return bool
     */
    public function applyDesign(Product $campProduct, array|Collection $designs, User $seller, bool $isValidateDesign = true): bool
    {
        try {
            foreach ($designs as $design) {
                if (empty($design['file_url'])) {
                    continue;
                }

                $newPath = CampaignController::saveTempDesignFile(
                    $design['file_url'], $campProduct->campaign_id
                );

                $isDesignTooSmall = $isValidateDesign &&
                    (file_size_on_storage($newPath) < to_byte(50, 'KB') &&
                    CampaignService::isDesignTransparent($newPath));

                if ($isDesignTooSmall) {
                    delete_file_on_storage($design['file_url']);
                    delete_file_on_storage($newPath);
                    throw new RuntimeException('File size is too small');
                }

                $design['file_url'] = $newPath;
                $design['option'] = FileRenderType::RENDER_3D;

                $created = $this->createDesign(
                    $design, $campProduct, $seller
                );

                $this->createDesignSuccess($created);
            }
            return true;
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * @param Product $instance
     * @param User $seller
     * @return bool|null
     */
    public function deleteDesign(Product $instance, User $seller): ?bool
    {
        return File::query()
            ->onSellerConnection($seller)
            ->where('product_id', $instance->id)
            ->where('type', FileTypeEnum::DESIGN)
            ->where('option', FileRenderType::RENDER_3D)
            ->delete();
    }

    /**
     * @param     array|\Illuminate\Support\Collection     $design
     * @param     \App\Models\Product                      $campProduct
     * @param     int                                      $sellerId
     *
     * @return array
     */
    public function makeDesignRecord(array|Collection $design, Product $campProduct, int $sellerId, bool $isValidateDesign = true): array
    {
        return [...parent::makeDesignRecord($design, $campProduct, $sellerId, $isValidateDesign), 'type' => FileTypeEnum::DESIGN];
    }
}

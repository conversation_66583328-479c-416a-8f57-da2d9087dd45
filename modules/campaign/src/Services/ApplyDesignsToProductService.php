<?php

namespace Modules\Campaign\Services;

use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Jobs\ScanCampaignCopyright;
use App\Models\File;
use App\Models\Product;
use App\Models\User;
use App\Services\CampaignService;
use Illuminate\Support\Collection;
use RuntimeException;
use Throwable;

class ApplyDesignsToProductService
{
    /**
     * ApplyDesignsToProductService constructor.
     */
    public function __construct() {}

    /**
     * @throws \Throwable
     */
    public function handle(Product $product, Collection $designs, User $seller, bool $isValidateDesign = true): bool
    {
        $this->deleteDesign($product, $seller);

        return $this->applyDesign($product, $designs, $seller, $isValidateDesign);
    }

    /**
     * @param Product $campProduct
     * @param array|Collection $designs
     * @param User $seller
     * @return bool
     */
    public function applyDesign(Product $campProduct, array|Collection $designs, User $seller, bool $isValidateDesign = true): bool
    {
        try {
            foreach ($designs as $design) {
                $created = $this->createDesign(
                    $design, $campProduct, $seller, $isValidateDesign
                );

                $this->createDesignSuccess($created);
            }
            return true;
        } catch (Throwable $e) {
            throw $e;
        }
    }

    /**
     * @param     \App\Models\File     $design
     *
     * @return void
     */
    public function createDesignSuccess(File $design): void
    {
        ScanCampaignCopyright::dispatch($design->campaign_id, sellerId: $design->seller_id);

        graylogInfo(
            'saveOriginDesignBulkV2 - bulk upload',
            [
                'category' => 'create_camp_design',
                'campaign_id' => $design->campaign_id ?? null,
                'product_id' => $design->product_id ?? null,
                'data' => $design
            ]
        );
    }

    /**
     * @param array|Collection $design
     * @param Product $campProduct
     * @param User $seller
     * @return File
     */
    public function createDesign(array|Collection $design, Product $campProduct, User $seller, bool $isValidateDesign = true): File
    {
        $newDesign = $this->makeDesignRecord($design, $campProduct, $seller->id, $isValidateDesign);
        return $this->saveDesign($newDesign, $seller);
    }

    /**
     * @param     array|\Illuminate\Support\Collection     $design
     * @param     \App\Models\Product                      $campProduct
     * @param     int                                      $sellerId
     *
     * @return array
     */
    public function makeDesignRecord(array|Collection $design, Product $campProduct, int $sellerId, bool $isValidateDesign = true): array
    {
        [$url, $designJson] = $this->prepareDesignUrl($design['file_url'] ?? null, $design['design_json'] ?? null, $campProduct->campaign_id, $isValidateDesign);

        return [
            'type' => FileTypeEnum::DESIGN,
            'product_id' => $campProduct->id,
            'campaign_id' => $campProduct->campaign_id,
            'seller_id' => $sellerId,
            'mockup_id' => $design['mockup_id'] ?? null,
            'option' => data_get($design, 'option', FileRenderType::PRINT),
            'print_space' => $design['print_space'],
            'file_url' => $url,
            'design_json' => $designJson,
        ];
    }

    /**
     * @param array $design
     * @param User $seller
     * @return File
     */
    public function saveDesign(array $design, User $seller): File
    {
        return File::query()
            ->onSellerConnection($seller)
            ->create($design);
    }

    /**
     * @param Product $instance
     * @param User $seller
     * @return bool|null
     */
    public function deleteDesign(Product $instance, User $seller): ?bool
    {
        return File::query()
            ->onSellerConnection($seller)
            ->where('product_id', $instance->id)
            ->delete();
    }

    /**
     * @param     string|null     $url
     * @param     string|null     $designJson
     * @param     int             $campaignId
     *
     * @return null[]|string[]
     */
    private function prepareDesignUrl(?string $url, ?string $designJson, int $campaignId, bool $isValidateDesign = true): array
    {
        if (str_starts_with($url, 'p/' . $campaignId)) {
            return [$url, $designJson];
        }
        if (! is_tmp_url($url)) {
            return [$url, $designJson];
        }

        if (! preg_match('/\/tmp\/(.+)$/', $url, $matches)) {
            return [$url, $designJson];
        }

        [, $fileName] = $matches;
        $newPath = 'p/' . $campaignId . '/' . $fileName;
        $oldPath = 'tmp/' . $fileName;

        if (!copy_file_on_storage($oldPath, $newPath)) {
            throw new RuntimeException('Failed to copy file from ' . $oldPath . ' to ' . $newPath);
        }

        $isDesignTooSmall = $isValidateDesign &&
            (file_size_on_storage($newPath) < to_byte(50, 'KB') &&
            CampaignService::isDesignTransparent($newPath));

        if ($isDesignTooSmall) {
            delete_file_on_storage($oldPath);
            delete_file_on_storage($newPath);
            throw new RuntimeException('File size is too small');
        }

        $designJson = preg_replace(
            "#\"[^\"]*/tmp/$fileName\"#", '"' . imgUrl($newPath) . '"',
            $designJson
        );

        return [$newPath, $designJson];
    }
}

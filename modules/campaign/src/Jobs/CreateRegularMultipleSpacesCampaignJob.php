<?php
namespace Modules\Campaign\Jobs;

use App\Enums\QueueName;
use App\Models\Campaign;
use App\Models\Slug;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignSystemStatusEnum;
use Modules\Campaign\Enums\ImportCampaignTypeEnum;
use Modules\Campaign\Models\ImportCampaignsData;
use Modules\Campaign\Services\BulkService;
use Modules\ShardingTable\Traits\InitSellerConnection;
use RuntimeException;
use Throwable;

class CreateRegularMultipleSpacesCampaignJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, InitSellerConnection;

    /**
     * Create a new job instance.
     *
     * @param ImportCampaignsData $rawCampaign
     * @param User $seller
     */
    public function __construct(private ImportCampaignsData $rawCampaign, private readonly User $seller)
    {
        $this->rawCampaign = ImportCampaignsData::query()->whereKey($rawCampaign->id)->first();
        $this->onQueue(QueueName::BULK_CAMPAIGN);
    }

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return $this->rawCampaign->id;
    }

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        try {
            $this->initConnection();
            if ($this->rawCampaign->type !== ImportCampaignTypeEnum::REGULAR_MULTI_SPACE) {
                return;
            }

            if ($this->rawCampaign->status !== ImportCampaignStatusEnum::PROCESSING) {
                return;
            }

            if (
                // sau khi xu ly design
                $this->rawCampaign->system_status !== ImportCampaignSystemStatusEnum::SAVE_DESIGN

                // null sau khi retry
                && ! is_null($this->rawCampaign->system_status)
            ) {
                throw new RuntimeException('Invalid campaign system status');
            }

            $designs = json_decode($this->rawCampaign->designs ?? '', true, 512, JSON_THROW_ON_ERROR);
            if ( ! is_array($designs) || empty($designs)) {
                throw new RuntimeException('Invalid designs');
            }

            $mockups = json_decode($this->rawCampaign->mockups ?? '', true, 512, JSON_THROW_ON_ERROR);
            if ( ! is_array($mockups) || ! is_array($mockups['designs']) || empty($mockups['designs'])) {
                throw new RuntimeException('Invalid mockups');
            }

            $prepared = self::prepareNameAndSlug($this->rawCampaign);

            /** @var BulkService $service */
            $this->rawCampaign->load([
                'template_campaign' => fn($q) => $q->onSellerConnection($this->seller),
                'campaign' => fn($q) => $q->onSellerConnection($this->seller),
            ]);
            $service = app(BulkService::class, [
                'seller' => $this->seller,
                'fromCampaign' => $this->rawCampaign->template_campaign,
                'toCampaign' => $this->rawCampaign->campaign,
                'name' => $prepared['name'],
                'designs' => collect($mockups['designs']),
                'designs3D' => collect($mockups['designs3D']),
                'slug' => $prepared['slug'],
                'collection' => $prepared['collection'],
                'isValidateDesign' => (bool) !$this->rawCampaign->disable_validate_design
            ]);

            $campaign = app()->call([$service, 'handle']);

            $this->createSuccessCampaign($campaign);
        } catch (Throwable $e) {
            logException($e, 'CreateRegularMultipleSpacesCampaignJob@handle');
            // Khi tạo campaign thất bại do vượt quá giới hạn tạo campaign trong ngày
            // thì sẽ release job để thực hiện lại vào lúc 00:00:00 ngày hôm sau
            if ($e->getCode() === BulkService::ERR_LIMIT_CREATE_CAMPAIGN) {
                $this->rawCampaign->update(['limit_at' => now()->startOfDay()->addDay()]);
            } else {
                $this->createCampaignFailed($e);
            }

            $this->fail($e);
        }
    }



    /**
     * @param     \Modules\Campaign\Models\ImportCampaignsData     $record
     *
     * @return string[]
     * @throws Throwable
     */
    public static function prepareNameAndSlug(ImportCampaignsData $record): array
    {
        $designs = json_decode($record->designs, true, 512, JSON_THROW_ON_ERROR);
        if (! $name = $record->campaign_name) {
            $first = Arr::first($designs, static fn ($d) => data_get($d, 'campaign_name'));
            $name =  data_get($first, 'campaign_name') ?? '';
        }

        if (! $collection = $record->collection) {
            $first = Arr::first($designs, static fn ($d) => data_get($d, 'collection'));
            $collection =  data_get($first, 'collection') ?? '';
        }

        if (! $slug = $record->campaign_slug) {
            $slug = Str::slug($record->campaign_name);
        }

        if ($record->short_url) {
            $slug = Str::of($slug)
                ->explode('-')
                ->slice(0, 5)
                ->join('-');
        }

        if ($record->indefinite_article) {
            $firstChar = Str::lower($name[0]);
            $prefix = in_array($firstChar, ['a', 'e', 'i', 'o', 'u']) ? 'an' : 'a';
            $name = $prefix . ' ' . $name;
        }

        $slug = collect([$record->prefix, $slug, $record->suffix])
            ->filter()
            ->join('-');
        $slug = Slug::genSlugIfNecessary($slug);

        $name = collect([$record->name_prefix, $name, $record->name_suffix])
            ->filter()
            ->join(' ');

        return [
            'name' => $name,
            'slug' => $slug,
            'collection' => $collection
        ];
    }

    /**
     * @param     Throwable     $e
     *
     * @return void
     * @throws Throwable
     */
    private function createCampaignFailed(Throwable $e): void
    {
        $logs = [
            'message' => match($e->getCode()) {
                BulkService::ERR_LUNCH_CAMPAIGN_FAILED => 'Launch campaign failed',
                BulkService::ERR_EXISTS_CAMPAIGN => 'Exists campaign',
                default => $e->getMessage()
            },
            'traces' => $e->getTraceAsString()
        ];

        if ($this->rawCampaign->retry_count > 3) {
            $this->rawCampaign->update([
                'status' => ImportCampaignStatusEnum::FAILED,
                'logs' => $logs['message']
            ]);
        } else {
            $this->rawCampaign->update([
                'status' => ImportCampaignStatusEnum::PENDING,
                'logs' => $logs['message'],
                'retry_count' => $this->rawCampaign->retry_count + 1
            ]);
        }

        graylogError('Create campaign failed', [
            'category' => 'bulk_multi_design_campaign',
            'import_campaign_id' => $this->rawCampaign->id,
            'logs' => $logs
        ]);
    }

    /**
     * @param     \App\Models\Campaign     $camp
     *
     * @return void
     */
    private function createSuccessCampaign(Campaign $camp): void
    {
        $this->rawCampaign->status = ImportCampaignStatusEnum::COMPLETED;
        $this->rawCampaign->logs = null;
        $this->rawCampaign->campaign_id = $camp->id;
        $this->rawCampaign->save();
    }
}

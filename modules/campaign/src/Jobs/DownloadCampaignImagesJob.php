<?php

namespace Modules\Campaign\Jobs;

use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductStatus;
use App\Enums\StorageDisksEnum;
use App\Jobs\ScanCampaignCopyright;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Library\UrlUploadedFile;
use App\Models\Campaign;
use App\Models\File;
use App\Models\Product;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\Campaign\Services\CustomCampaignService;
use Modules\ShardingTable\Traits\InitSellerConnection;

class DownloadCampaignImagesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, InitSellerConnection;

    /**
     * @var int
     */
    protected $campaign_id;

    /**
     * @var int
     */
    protected $seller_id;

    /**
     * @var int
     */
    protected $download_design;

    public function __construct($campaign_id, $seller_id, $download_design = false)
    {
        $this->campaign_id = $campaign_id;
        $this->seller_id = $seller_id;
        $this->download_design = $download_design;
    }

    /**
     * @throws \Throwable
     */
    public function handle()
    {
        try {
            $this->initConnection();
            if (empty($this->campaign_id) || empty($this->seller_id)) {
                $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Not found campaign or seller ID.');
                return;
            }
            $seller = User::query()->find($this->seller_id);
            $campaign = Campaign::query()
                ->onSellerConnection($seller)
                ->selectRaw('id, thumb_url, product_type, status, system_type')
                ->where([
                    'id' => $this->campaign_id,
                    'seller_id' => $this->seller_id
                ])
                ->first();
            if (empty($campaign)) {
                $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Not found campaign ID: #' . $this->campaign_id . ' / seller ID: ' . $this->seller_id);
                return;
            }
            $system_type = $campaign->system_type;

            $query = File::query()
                ->onSellerConnection($seller)
                ->where([
                    'seller_id' => $this->seller_id,
                    'campaign_id' => $this->campaign_id,
                    'status' => FileStatusEnum::ACTIVE,
                ]);

            if ($this->download_design && $system_type === ProductSystemTypeEnum::AOP) {
                $query->whereNotNull('print_space')
                    ->where([
                        'type' => FileTypeEnum::DESIGN,
                        'option' => 'print',
                        'print_space' => PrintSpaceEnum::DEFAULT,
                    ]);
            }
            $product_images = $query->orderBy('id')->get();

            if (empty($product_images) && !$this->download_design) {
                $this->updateCampaignAndProductsStatus(ProductStatus::ERROR);
                $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Not found files in campaign ID: #' . $this->campaign_id);
                return;
            }
            //Process download images of products
            $is_downloaded = true;
            $url_downloaded = [];
            $updated_product_thumb = [];
            foreach ($product_images as $idx => $product_image) {
                $file_url_download = $product_image->file_url;
                if (empty($file_url_download)) {
                    $file_url_download = $product_image->file_url_2;
                }
                if (empty($file_url_download)) {
                    if (!empty($product_image->product_id)) {
                        $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Product ID: #' . $product_image->product_id . ', File url is empty.');
                    } else {
                        $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Campaign ID: #' . $product_image->campaign_id . ', File url is empty.');
                    }
                    $is_downloaded = false;
                    File::query()
                        ->onSellerConnection($seller)
                        ->where([
                            'id' => $product_image->id,
                        ])
                        ->update([
                            'status' => FileStatusEnum::INACTIVE
                        ]);
                    continue;
                }
                $hash_file_url = md5(trim($file_url_download));
                if (Str::startsWith($file_url_download, 'http')) {
                    if (empty($url_downloaded[$hash_file_url])) {
                        $tmpFile = UrlUploadedFile::createFileFromUrl($file_url_download, $product_image->type);
                        if ($tmpFile === null) {
                            //Try to create file from url one more time
                            $tmpFile = UrlUploadedFile::createFileFromUrl($file_url_download, $product_image->type);
                            if ($tmpFile === null) {
                                if (!empty($product_image->product_id)) {
                                    $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Product ID: #' . $product_image->product_id . ', Can not create file from product mockup url. Url: ' . $file_url_download);
                                } else {
                                    $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Campaign ID: #' . $product_image->campaign_id . ', Can not create file from campaign mockup url. Url: ' . $file_url_download);
                                }
                                $is_downloaded = false;
                                File::query()
                                    ->onSellerConnection($seller)
                                    ->where([
                                        'id' => $product_image->id,
                                    ])
                                    ->update([
                                        'status' => FileStatusEnum::INACTIVE
                                    ]);
                                continue;
                            }
                        }
                        $name = $tmpFile->getClientOriginalName();
                        $file_url = $tmpFile->storePubliclyAs('p/' . $campaign->id, $name, StorageDisksEnum::DEFAULT);
                        if (empty($file_url)) {
                            if (!empty($product_image->product_id)) {
                                $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Product ID: #' . $product_image->product_id . ', Can not upload file to S3. Product thumb url is empty. File ID: #' . $product_image->id);
                            } else {
                                $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Campaign ID: #' . $product_image->campaign_id . ', Can not upload file to S3. Campaign thumb url is empty. File ID: #' . $product_image->id);
                            }
                            $is_downloaded = false;
                            File::query()
                                ->onSellerConnection($seller)
                                ->where([
                                    'id' => $product_image->id,
                                ])->update([
                                    'status' => FileStatusEnum::INACTIVE
                                ]);
                            continue;
                        }
                        $url_downloaded[$hash_file_url] = $file_url;
                    }
                } else if (Str::startsWith($file_url_download, 'p/')) {
                    $url_downloaded[$hash_file_url] = $file_url_download;
                }
                // Make sure data is valid
                if (empty($url_downloaded[$hash_file_url])) {
                    if (!empty($product_image->product_id)) {
                        $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Product ID: #' . $product_image->product_id . ', Can not download & upload product thumb to S3. File ID: #' . $product_image->id);
                    } else {
                        $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Campaign ID: #' . $product_image->campaign_id . ', Can not download & upload campaign thumb to S3. File ID: #' . $product_image->id);
                    }
                    $is_downloaded = false;
                    continue;
                }
                if ($system_type !== ProductSystemTypeEnum::AOP) {
                    // Update campaign thumb
                    if ($idx === 0) {
                        $this->updateCampaignThumb($url_downloaded[$hash_file_url]);
                    }
                    // Update product thumb
                    if (!empty($product_image->product_id) && empty($updated_product_thumb[$product_image->product_id . $this->campaign_id])) {
                        $this->updateProductThumb($product_image->product_id, $url_downloaded[$hash_file_url]);
                        $updated_product_thumb[$product_image->product_id . $this->campaign_id] = $url_downloaded[$hash_file_url];
                    }
                    File::query()
                        ->onSellerConnection($seller)
                        ->whereKey($product_image->id)
                        ->update([
                            'file_url' => $url_downloaded[$hash_file_url],
                            'file_url_2' => $file_url_download
                        ]);
                } else {
                    File::query()
                        ->onSellerConnection($seller)
                        ->whereKey($product_image->id)
                        ->withoutGlobalScope('getActive')
                        ->update([
                            'file_url' => $url_downloaded[$hash_file_url],
                            'file_url_2' => $file_url_download
                        ]);
                }
            }
            if ($system_type === ProductSystemTypeEnum::AOP && !empty($url_downloaded)) {
                $createdMockup = CustomCampaignService::createMockupForAopCampaign($this->campaign_id, seller: $seller);
                if (!$createdMockup['status']) {
                    $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Campaign ID: #' . $this->campaign_id . ', Can not create mockup for AOP, error' . $createdMockup['message']);
                    $this->updateCampaignAndProductsStatus(ProductStatus::ERROR);
                }
            } else if (!$this->download_design) {
                if ($is_downloaded) {
                    $this->updateCampaignAndProductsStatus(ProductStatus::ACTIVE);
                } else {
                    $this->updateCampaignAndProductsStatus(ProductStatus::ERROR);
                }
            } else if ($is_downloaded) {
                $this->updateCampaignAndProductsStatus(ProductStatus::ACTIVE);
            } else {
                $this->updateCampaignAndProductsStatus(ProductStatus::ERROR);
            }
            (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($this->campaign_id, sellerId:$this->seller_id);
            ScanCampaignCopyright::dispatch($this->campaign_id, sellerId: $this->seller_id);
        } catch (\Exception $e) {
            $this->logToDiscord('[' . currentTime() . '] - DownloadCampaignImagesJob -> Error -> Campaign ID: #' . $this->campaign_id . '. ' . $e->getMessage());
        }
    }

    /**
     * @param $status
     * @return void
     */
    private function updateCampaignAndProductsStatus($status)
    {
        $seller = User::find($this->seller_id);
        Product::query()
            ->onSellerConnection($seller)
            ->where(function ($q) {
                return $q->where('id', $this->campaign_id)->orWhere('campaign_id', $this->campaign_id);
            })
            ->update([
                'status' => $status,
                'sync_status' => Product::SYNC_DATA_STATS_ENABLED
            ]);
    }

    /**
     * @param $thumb_url
     * @return void
     */
    private function updateCampaignThumb($thumb_url)
    {
        $seller = User::find($this->seller_id);
        Campaign::query()
            ->onSellerConnection($seller)
            ->whereKey($this->campaign_id)
            ->update([
                'thumb_url' => $thumb_url
            ]);
    }

    /**
     * @param $product_id
     * @param $thumb_url
     * @return void
     */
    private function updateProductThumb($product_id, $thumb_url)
    {
        $seller = User::find($this->seller_id);
        Product::query()
            ->onSellerConnection($seller)
            ->whereKey($product_id)->update([
                'thumb_url' => $thumb_url
            ]);
    }

    /**
     * @param $message
     * @return void
     */
    private function logToDiscord($message): void
    {
        logToDiscord($message, 'custom_campaign');
    }
}

<?php

namespace Modules\Campaign\Jobs;

use App\Actions\Commons\ImgDirectLinkAction;
use App\Enums\CacheKeys;
use App\Enums\CampaignStatusEnum;
use App\Enums\ProductStatus;
use App\Enums\QueueName;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\CollectionSellerController;
use App\Http\Controllers\UploadController;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\BulkUploadLog;
use App\Models\Campaign;
use App\Models\Product;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\CampaignService;
use App\Services\Link;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignTypeEnum;
use Modules\Campaign\Models\ImportCampaignsData;
use Modules\Campaign\Services\ImportCampaignDataService;

class CreateRegularDraftCampaignsJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const USER_CHUNK = 5;
    public const ROW_LIMIT = 50;
    public const TOTAL_LIMIT = 250;

    protected $sellerId;

    public function __construct($sellerId = null)
    {
        $this->sellerId = $sellerId;
        $this->onQueue(QueueName::BULK_CAMPAIGN);
    }

    /**
     * @return void
     */
    public function handle()
    {
        try {
            if ($this->sellerId) {
                $this->handle_create_draft_campaigns($this->sellerId);
                graylogInfo('Create draft campaign for seller: ' . $this->sellerId, [
                    'category' => 'bulk_campaigns',
                    'type' => 'create_draft',
                ]);
                return;
            }
            SystemConfig::setConfig(CacheKeys::CREATE_BULK_DRAFT_CAMPAIGNS, array(
                'value' => now()->clone()->addMinutes(5)->toDateTimeString()
            ));
            $total = $this->handle_create_draft_campaigns();
            if ($total < self::TOTAL_LIMIT) {
                SystemConfig::setConfig(CacheKeys::CREATE_BULK_DRAFT_CAMPAIGNS, array(
                    'value' => null,
                ));
                graylogInfo('Create draft campaign STOP: ' . $total . ' / ' . self::TOTAL_LIMIT, [
                    'category' => 'bulk_campaigns',
                    'type' => 'create_draft',
                ]);
                return;
            }
            dispatch(new self());
        } catch (\Throwable $exception) {
            logException($exception, 'CreateRegularDraftCampaignsJob::handle', 'bulk_campaign', true);
        }
    }

    /**
     * @return int
     */
    public function handle_create_draft_campaigns($sellerId = null)
    {
        try {
            $limitUsers = CampaignService::listUserLimitCreateCampaign();
            $sellers = User::query()
                ->select('id', 'sharding_status')
                ->bulkCampaignEnabled()
                ->when(!empty($limitUsers), function ($query) use ($limitUsers) {
                    $query->whereNotIn('id', $limitUsers);
                })
                ->when($sellerId, function ($query) use ($sellerId) {
                    $query->where('id', $sellerId);
                })
                ->whereHas('import_campaigns_data', function ($query) {
                    $query->where('type', ImportCampaignTypeEnum::REGULAR)->where('status', ImportCampaignStatusEnum::PENDING)->whereNull('campaign_id')->whereNotNull('template_campaign_id');
                })
                ->inRandomOrder()
                ->get();
            if ($sellers->isEmpty()) {
                return 0;
            }
            $total_pending = 0;
            $sellers
                ->chunk(self::USER_CHUNK)
                ->map(function ($users) use ($limitUsers, &$total_pending) {
                    foreach ($users as $user) {
                        $user->load(['import_campaigns_data' => function ($q) {
                            $q->select(['id', 'campaign_id', 'seller_id'])->where('type', ImportCampaignTypeEnum::REGULAR)->where('status', ImportCampaignStatusEnum::PENDING)->whereNull('campaign_id')->whereNotNull('template_campaign_id')->limit(self::ROW_LIMIT)->orderBy('created_at');
                        }]);
                        /** @var User $user */
                        $importedCampaigns = $user->import_campaigns_data;
                        if ($importedCampaigns->isEmpty()) {
                            continue;
                        }
                        $seller_total_pending = ImportCampaignsData::query()->select('id')->where('seller_id', $user->id)->where('type', ImportCampaignTypeEnum::REGULAR)->where('status', ImportCampaignStatusEnum::PENDING)->whereNull('campaign_id')->whereNotNull('template_campaign_id')->count();
                        $total_pending += ($seller_total_pending - $importedCampaigns->count());
                        foreach ($importedCampaigns as $importedRecord) {
                            $importedCampaign = ImportCampaignsData::query()->find($importedRecord->id);
                            if (!$importedCampaign) {
                                continue;
                            }
                            $seller_id = $importedCampaign->seller_id;
                            if (in_array($seller_id, $limitUsers)) {
                                continue;
                            }
                            graylogInfo('[1]. Create draft campaign.', [
                                'category' => 'bulk_campaigns',
                                'bulk_id' => $importedCampaign->id,
                                'type' => 'create_draft',
                            ]);
                            $user = currentUser($seller_id, [
                                'service_name' => 'seller',
                                'guard_name' => 'user',
                            ]);
                            $isSellName = $user->isSeller() && $user->getInfo()?->isSellName();
                            if ($user->getNumberCampaignCanCreate() <= 0) {
                                User::query()->whereKey($seller_id)->update(['hold_launch_campaign_at' => now()]);
                                $limitUsers[] = $seller_id;
                                continue;
                            }
                            $campaignName = trim($importedCampaign->campaign_name);
                            $design = json_decode($importedCampaign->designs, true, 512, JSON_THROW_ON_ERROR);
                            if (empty($design['original_url'])) {
                                $importedCampaign->logs = 'Missing design url';
                                $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                                $importedCampaign->save();
                                continue;
                            }
                            $fileUrl = $design['original_url'];
                            $collectionName = trim($importedCampaign->collection);
                            if (empty($design['new_url'])) {
                                try {
                                    if (str_contains($fileUrl, 'drive.google.com')) {
                                        $client = new \Google_Client();
                                        $client->setAuthConfig(storage_path('google-service-account-credentials.json'));
                                        $client->addScope(\Google_Service_Drive::DRIVE);
                                        $service = new \Google_Service_Drive($client);
                                        $fileId = $this->extractFileIdFromGoogleDriveUrl($fileUrl);
                                        $file = $service->files->get($fileId, array(
                                            'fields' => 'name'
                                        ));
                                        [$collectionName, $campaignName] = $this->extractNameAndCollection($file->getName());
                                        if (empty($importedCampaign->campaign_name)) {
                                            if (empty($campaignName)) {
                                                $importedCampaign->logs = 'Invalid file name';
                                                $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                                                $importedCampaign->save();
                                                continue;
                                            }
                                            $importedCampaign->campaign_name = $campaignName;
                                        }
                                        $action = new ImgDirectLinkAction();
                                        $directLink = $action->handle($fileUrl);
                                        $loadImage = UploadController::uploadS3FromDirectLink($directLink, 'tmp/');
                                        if (empty($loadImage)) {
                                            $importedCampaign->logs = 'File invalid ' . $fileUrl;
                                            $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                                            $importedCampaign->save();
                                            continue;
                                        }
                                        $design['new_url'] = $loadImage['path'];
                                        graylogInfo('[2]. Create draft campaign.', [
                                            'category' => 'bulk_campaigns',
                                            'bulk_id' => $importedCampaign->id,
                                            'type' => 'create_draft',
                                        ]);
                                    } else if (str_contains($fileUrl, 'dropbox.com')) {
                                        $directLink = new Link($fileUrl);
                                        $design['new_url'] = $directLink->toDirectLink();
                                        graylogInfo('[2]. Create draft campaign.', [
                                            'category' => 'bulk_campaigns',
                                            'bulk_id' => $importedCampaign->id,
                                            'type' => 'create_draft',
                                        ]);
                                    } else {
                                        $design['new_url'] = $fileUrl;
                                    }
                                    $artworkUrl = $design['new_url'];
                                } catch (\Exception $e) {
                                    $importedCampaign->logs = 'Handle file ' . $fileUrl . ' error: ' . $e->getMessage();
                                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                                    $importedCampaign->save();
                                    continue;
                                }
                            } else {
                                $artworkUrl = $design['new_url'];
                            }
                            if (str_starts_with($artworkUrl, 'http') && filter_var($artworkUrl, FILTER_VALIDATE_URL)) {
                                $action = new ImgDirectLinkAction();
                                $directLink = $action->handle($artworkUrl);
                                $loadImage = UploadController::uploadS3FromDirectLink($directLink, 'tmp/');
                                if (empty($loadImage)) {
                                    $importedCampaign->logs = 'File artwork invalid ' . $artworkUrl;
                                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                                    $importedCampaign->save();
                                    logToDiscord('Can not download file from url: ' . $artworkUrl, 'bulk_campaign');
                                    continue;
                                }
                                graylogInfo('[3]. Create draft campaign.', [
                                    'category' => 'bulk_campaigns',
                                    'bulk_id' => $importedCampaign->id,
                                    'type' => 'create_draft',
                                ]);
                                $artworkUrl = $loadImage['path'];
                                $design['new_url'] = $artworkUrl;
                            }
                            $importedCampaign->designs = json_encode($design);
                            $importedCampaign->save();
                            $slug = CampaignService::generateCampaignSlugAndClearDraftCampaign($campaignName, $importedCampaign->prefix ?? '', $importedCampaign->suffix ?? '', $seller_id);
                            $existCampaign = Campaign::query()
                                ->onSellerConnection($user)
                                ->select(['id', 'name', 'slug', 'thumb_url', 'status'])
                                ->where([
                                    'slug' => $slug,
                                    'seller_id' => $seller_id
                                ])
                                ->first();
                            // delete draft camp to avoid duplicate slug
                            if ($existCampaign) {
                                if ($existCampaign->status === CampaignStatusEnum::DRAFT && !empty($existCampaign->id)) {
                                    $query = Product::query()
                                        ->onSellerConnection($user)
                                        ->where(function ($query) use ($existCampaign) {
                                            $query->where('id', $existCampaign->id)
                                                ->orWhere('campaign_id', $existCampaign->id);
                                        })
                                        ->where([
                                            'seller_id' => $seller_id,
                                            'status' => ProductStatus::DRAFT
                                        ]);
                                    $productIds = $query->pluck('id')->toArray();
                                    $query->update(['slug' => null]);
                                    $query->delete();
                                    (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($productIds, sellerId: $seller_id);
                                    SyncSlugJob::dispatchSync(ids: [$existCampaign->id], seller: $user, isUpsert: false);
                                } else if ($isSellName) {
                                    // don't create duplicate camp if seller sell name
                                    $importedCampaign->update([
                                        'status' => ImportCampaignStatusEnum::COMPLETED,
                                        'logs' => 'Campaign already exist',
                                        'campaign_id' => $existCampaign->id,
                                        'campaign_slug' => $existCampaign->slug,
                                        'campaign_description' => $existCampaign->description
                                    ]);
                                    continue;
                                }
                            }
                            if ($importedCampaign->indefinite_article) {
                                $firstChar = strtolower($campaignName[0]);
                                $isVowel = in_array($firstChar, ['a', 'e', 'i', 'o', 'u']);
                                $campaignName = ($isVowel ? 'an ' : 'a ') . $campaignName;
                            }

                            if (!empty($importedCampaign->name_prefix)) {
                                $campaignName = $importedCampaign->name_prefix . ' ' . $campaignName;
                            }

                            if (!empty($importedCampaign->name_suffix)) {
                                $campaignName .= ' ' . $importedCampaign->name_suffix;
                            }

                            if ($isSellName && empty($collectionName)) {
                                $collectionName = $campaignName;
                            }

                            try {
                                $campaignId = (int)$importedCampaign->template_campaign_id;
                                $hasArchived = ImportCampaignDataService::checkArchivedProducts($campaignId, $user);
                                if ($hasArchived) {
                                    $importedCampaign->update([
                                        'status' => ImportCampaignStatusEnum::FAILED,
                                        'logs' => ImportCampaignDataService::ARCHIVED_PRODUCTS_MESSAGE
                                    ]);
                                    continue;
                                }
                                $newCampaign = CampaignService::duplicateCampaignWithFile($campaignId, $artworkUrl, $campaignName, $slug, $user);
                                if ($newCampaign === false) {
                                    logToDiscord('Duplicate campaign failed: ' . $seller_id . ' / ' . $slug, 'bulk_campaign');
                                    $importedCampaign->update([
                                        'status' => ImportCampaignStatusEnum::FAILED,
                                        'logs' => 'Duplicate campaign failed'
                                    ]);
                                    continue;
                                }
                                if (!empty($collectionName)) {
                                    // Now collectionName can be multiple separated by |
                                    $collectionNames = explode('|', $collectionName);
                                    foreach ($collectionNames as $collectionName) {
                                        $collectionName = trim($collectionName);
                                        $collection = CollectionSellerController::createCollection($collectionName, $seller_id);
                                        CampaignController::addCampaignToCollection($newCampaign['id'], $seller_id, $collection['collection_id']);
                                    }
                                }
                                try {
                                    BulkUploadLog::query()->create([
                                        'seller_id' => $seller_id,
                                        'parent_campaign_id' => $campaignId,
                                        'campaign_id' => $newCampaign['id'],
                                        'file_name' => $campaignName,
                                        'ip_address' => '127.0.0.1',
                                        'uploaded_at' => now()
                                    ]);
                                } catch (\Throwable $exception) {
                                    logToDiscord('Save bulk upload log failed: ' . $seller_id . ' - ' . $exception->getMessage(), 'bulk_campaign');
                                }
                                $designs = json_decode($importedCampaign->designs, true);
                                $designs['artwork_url'] = $newCampaign['artwork_url'];
                                $importedCampaign->update([
                                    'campaign_id' => $newCampaign['id'],
                                    'campaign_slug' => $newCampaign['slug'],
                                    'campaign_description' => $newCampaign['description'] ?? null,
                                    'designs' => json_encode($designs),
                                ]);
                                graylogInfo('Create Draft Campaign Command', [
                                    'category' => 'bulk_campaigns',
                                    'time' => now(),
                                    'campaign_id' => $newCampaign['id'],
                                    'bulk_id' => $importedCampaign->id,
                                ]);
                            } catch (\Throwable $exception) {
                                logToDiscord('Create campaign failed: ' . $seller_id . ' / ' . $slug . ' - ' . $exception->getMessage(), 'bulk_campaign');
                                $importedCampaign->update([
                                    'status' => ImportCampaignStatusEnum::FAILED,
                                    'logs' => 'Create campaign failed'
                                ]);
                            }
                        }
                    }
                });
            return $total_pending;
        } catch (\Throwable $exception) {
            logException($exception, 'CreateRegularDraftCampaignsJob::handle', 'bulk_campaign', true);
            return 0;
        }
    }

    /**
     * @throws \Exception
     */
    public function extractFileIdFromGoogleDriveUrl($url): string
    {
        if (preg_match('/\?id=([^&]+)/', $url, $matches)) {
            return $matches[1];
        }

        if (preg_match('%drive\.google\.com/file/[a-z]/([^/]+)/(edit|view|preview)%', $url, $matches)) {
            return $matches[1];
        }

        throw new \RuntimeException('Invalid Google Drive file URL');
    }

    /**
     * @param string $fileName
     * @return array|null[]
     */
    private function extractNameAndCollection(string $fileName): array
    {
        $pattern = '/^(?:\[(.*?)]\s*)?(.+?)\.([a-z0-9]+)$/i';
        if (preg_match($pattern, $fileName, $matches)) {
            $collectionName = $matches[1] ?? null;
            if (!empty($collectionName)) {
                $collectionName = trim($collectionName);
            }
            return [$collectionName, trim($matches[2])];
        }
        return [null, null];
    }
}

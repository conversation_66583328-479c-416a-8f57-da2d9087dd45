<?php
namespace Modules\Campaign\Jobs;

use App\Enums\CacheKeys;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\ProductStatus;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Elastic;
use App\Models\File;
use App\Models\Product;
use App\Models\User;
use App\Services\CampaignService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Models\ImportCampaignsData;

class VerifyCampaignDesignAfterRenderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $bulkId;
    protected $seller;
    protected $status;

    public function __construct($bulkId, $seller = null, $status = null)
    {
        $this->bulkId = $bulkId;
        $this->seller = $seller;
        $this->status = $status;
    }

    /**
     * @throws \Throwable
     */
    public function handle(): void
    {
        try {
            $bulk = ImportCampaignsData::query()->whereKey($this->bulkId)->first();
            if (!$bulk) {
                return;
            }
            $seller = $this->seller;
            if (!$seller) {
                $seller = User::query()->whereKey($bulk->seller_id)->first();
            }
            if (!$seller) {
                return;
            }
            $campaign = Product::query()->onSellerConnection($seller)->whereKey($bulk->campaign_id)->first();
            if (empty($campaign)) {
                $bulk->update([
                    'status' => ImportCampaignStatusEnum::FAILED,
                    'logs' => 'Campaign maybe deleted.'
                ]);
                return;
            }
            $bulk->update(['temp_status' => $this->status]);
            $image = File::query()->onSellerConnection($seller)->where([
                'campaign_id' => $bulk->campaign_id,
                'type' => FileTypeEnum::IMAGE,
            ])->whereNull('type_detail')->exists();
            $design_3d = File::query()->onSellerConnection($seller)->select('file_url')->where([
                'campaign_id' => $bulk->campaign_id,
                'type' => FileTypeEnum::DESIGN,
                'option' => FileRenderType::RENDER_3D
            ])->whereNull('type_detail')->first();
            if (empty($image) || empty($design_3d)) {
                $logs = 'Invalid design rendered data';
                if (empty($image)) {
                    $logs = 'Invalid image data';
                }
                if (empty($design_3d)) {
                    $logs = 'Invalid design 3d data';
                }
                $bulk->update([
                    'status' => ImportCampaignStatusEnum::PENDING,
                    'system_status' => null,
                    'temp_status' => 0,
                    'logs' => $logs
                ]);
                Product::query()->onSellerConnection($seller)->filterByProductOrCampaignIds([$bulk->campaign_id])->update([
                    'status' => ProductStatus::DRAFT,
                    'sync_status' => 0,
                ]);
                return;
            }
            if (empty(file_exists_on_storage($design_3d->file_url))) {
                $bulk->update([
                    'status' => ImportCampaignStatusEnum::PENDING,
                    'system_status' => null,
                    'temp_status' => 0,
                    'mockups' => null,
                    'logs' => 'Design 3D file not found'
                ]);
                cache()->put($bulk->session_id, 1, CacheKeys::CACHE_1H);
                Product::query()->onSellerConnection($seller)->filterByProductOrCampaignIds([$bulk->campaign_id])->update([
                    'status' => ProductStatus::DRAFT,
                    'sync_status' => 0,
                ]);
                graylogInfo('Design 3D file not found', [
                    'category' => 'bulk_campaigns',
                    'campaign_id' => $bulk->campaign_id,
                    'bulk_id' => $bulk->id,
                    'seller_id' => $bulk->seller_id,
                    'file_url' => $design_3d->file_url,
                ]);
                return;
            }

            $isDesignTooSmall = !$bulk->disable_validate_design && file_size_on_storage($design_3d->file_url) < to_byte(50, 'KB') && CampaignService::isDesignTransparent($design_3d->file_url);

            if ($isDesignTooSmall) {
                if ($bulk->retry_count > 3) {
                    $bulk->update([
                        'status' => ImportCampaignStatusEnum::FAILED,
                        'logs' => 'Design 3D file is too small'
                    ]);
                } else {
                    $bulk->update([
                        'status' => ImportCampaignStatusEnum::PENDING,
                        'logs' => 'Design 3D file is too small',
                        'retry_count' => $bulk->retry_count + 1
                    ]);
                }
                Product::query()->onSellerConenvnection($seller)->filterByProductOrCampaignIds([$bulk->campaign_id])->update([
                    'status' => ProductStatus::DRAFT,
                    'sync_status' => 0,
                ]);
                graylogInfo('Design 3D file is too small', [
                    'category' => 'bulk_campaigns',
                    'campaign_id' => $bulk->campaign_id,
                    'bulk_id' => $bulk->id,
                    'seller_id' => $bulk->seller_id,
                    'file_url' => $design_3d->file_url,
                ]);

                return;
            }

            $campaignOnElastic = (new Elastic())->getCampaignDetail(['status'], ['id' => $bulk->campaign_id]);
            if (!empty($campaignOnElastic) && data_get($campaignOnElastic, 'status') !== $campaign->status) {
                try {
                    (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch([$bulk->campaign_id], sellerId: $seller->id);
                } catch (\Throwable $e) {
                    Product::query()->onSellerConnection($seller)->filterByProductOrCampaignIds([$bulk->campaign_id])->update(['sync_status' => 0]);
                }
            }
        } catch (\Throwable $exception) {
            logException($exception, 'VerifyCampaignDesignAfterRenderJob::handle', 'bulk_campaign');
        }
    }
}

<?php
namespace Modules\TiktokShop\Providers;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;

class TiktokShopServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap TiktokShop service provider service.
     *
     * @return void
     */
    public function boot()
    {
        if(!defined('TIKTOK_SHOP_MODULE_PATH')) {
            define('TIKTOK_SHOP_MODULE_PATH', dirname(__DIR__, 2));
        }
        $this->loadMigrationsFrom(TIKTOK_SHOP_MODULE_PATH . '/database/migrations');
        $this->app->booted(function () {
            $this->app->register(CommandServiceProvider::class);
            $this->registerConfigs(['general']);
            $this->registerRoutes();
            if (config('senprints.schedule_enabled')) {
                $schedule = $this->app->make(Schedule::class);
                $schedule->command('tts:refresh-token')->everyFourHours()->runInBackground()->logAfter();
                $schedule->command('tts:sync-order')->everyFiveMinutes()->runInBackground()->withoutOverlapping(10)->logAfter();
                $schedule->command('tts:sync-tracking-code')->everyFiveMinutes()->runInBackground()->withoutOverlapping(10)->logAfter();
            }
        });
    }

    /**
     * Register the tiktok_shop's configs.
     *
     * @return void
     */
    protected function registerConfigs($fileNames)
    {
        if (!is_array($fileNames)) {
            $fileNames = [$fileNames];
        }
        $config_path = TIKTOK_SHOP_MODULE_PATH . '/config';
        foreach ($fileNames as $fileName) {
            $full_path = $config_path . '/' . $fileName . '.php';
            $this->mergeConfigFrom($full_path, 'tiktok.shop.config.' . $fileName);
        }
    }

    /**
     * Register the tiktok_shop's routes.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }
        $route_path = TIKTOK_SHOP_MODULE_PATH . '/routes';
        $routes = $this->scanFolder($route_path);
        foreach ($routes as $route) {
            $this->loadRoutesFrom($route_path . '/' . $route);
        }
    }

    /**
     * @param string $path
     * @param array $ignoreFiles
     * @return array
     */
    public function scanFolder($path, array $ignoreFiles = [])
    {
        try {
            if (File::isDirectory($path)) {
                $data = array_diff(scandir($path), array_merge(['.', '..', '.DS_Store'], $ignoreFiles));
                natsort($data);
                return array_values($data);
            }
            return [];
        } catch (\Exception $exception) {
            return [];
        }
    }

    /**
     * Load helpers from a directory
     * @param string $directory
     */
    public function autoload(string $directory): void
    {
        $helpers = File::glob($directory . '/*.php');
        if(empty($helpers)) {
            return;
        }
        foreach ($helpers as $helper) {
            File::requireOnce($helper);
        }
    }
}

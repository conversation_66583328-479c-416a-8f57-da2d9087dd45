<?php
namespace Modules\SellerTier\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Modules\SellerTier\Jobs\SellerTierDownJob;
use Modules\SellerTier\Jobs\SellerTierUpdateJob;
use Modules\SellerTier\Models\SellerTier;

class SellerTierUpdateCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'module:seller-tier:update-tier';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update seller tier';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $previousMonth = now()->subMonth();
        $tiers = SellerTier::query()->orderByDesc('required_sen_points')->get();

        if ($tiers->isNotEmpty()) {
            $sellerIdsHavePointsInThePreviousMonth = [];
            logToDiscord("Start update sellers tier for " . $previousMonth->format('F') . " " . $previousMonth->format('Y'), 'seller_tier');
            // Seller có phát sinh point trong tháng trước where('order.total_sen_points', '>', 0)
            User::query()
                ->leftJoin('order', 'user.id', '=', 'order.seller_id')
                ->groupBy('order.seller_id')
                ->selectRaw('sum(order.total_sen_points) as point, user.id, user.tier_id')
                ->whereBetween('order.created_at', [
                    $previousMonth->firstOfMonth()->format('Y-m-d H:i:s'),
                    $previousMonth->endOfMonth()->format('Y-m-d H:i:s')
                ])->where('order.total_sen_points', '>', 0)
                ->whereNull('order.deleted_at')
                ->chunk(1000, function ($sellers) use ($tiers, &$sellerIdsHavePointsInThePreviousMonth) {
                    if ($sellers->isNotEmpty()) {
                        $sellers->each(function ($seller) use ($tiers) {
                            SellerTierUpdateJob::dispatch($tiers, $seller);
                        });
                        $sellerIdsHavePointsInThePreviousMonth = array_merge($sellerIdsHavePointsInThePreviousMonth, $sellers->pluck('id')->toArray());
                    }
                });

            // Seller đã có tier nhưng không phát sinh point whereNotIn('id', $sellerIdsHavePointsInThePreviousMonth)
            User::query()
                ->with('tier')
                ->has('tier')
                ->whereNotNull('tier_id')
                ->whereNotIn('id', $sellerIdsHavePointsInThePreviousMonth)
                ->chunk(1000, function ($sellers) use ($tiers) {
                    if ($sellers->isNotEmpty()) {
                        $sellers->each(function ($seller) use ($tiers) {
                            SellerTierDownJob::dispatchSync($tiers, $seller->tier, $seller);
                        });
                    }
                });
        }
    }
}

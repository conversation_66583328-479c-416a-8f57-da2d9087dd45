<?php

namespace Modules\SellerAPI\Actions;

use App\Enums\DesignByEnum;
use App\Enums\FbaFulfillBy;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductStatus;
use App\Imports\SellerFulfillOrders\ImportCSVMultiPlatform;
use App\Models\FulfillMapping;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Template;
use App\Services\OrderService;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\SellerAPI\Data\SaveOrderProductData;
use Modules\SellerAPI\Data\SaveOrderProductFileData;
use Modules\SellerAPI\Events\OrderProductProcessingDesignsEvent;
use Modules\SellerAPI\Events\OrderProductProcessingMockupsEvent;
use Modules\SellerAPI\Events\OrderProductUpdatedEvent;
use RuntimeException;
use Spatie\LaravelData\DataCollection;
use Throwable;

class SaveOrderProductsForOrder
{
    public function __construct(
        private readonly Order $order,
        /** @var DataCollection<SaveOrderProductData> $data */
        public DataCollection $data,
    ) {

    }

    /**
     * @throws Throwable
     */
    public function execute(): void
    {
        $errors = [];
        $orderProductIds = [];
        $order = $this->order;
        $fulfillByAmazonProducts = $this->data->toCollection()->filter(function ($product) use ($order) {
            return !empty($product->fulfill_fba_by) && $product->fulfill_fba_by === FbaFulfillBy::BY_AMAZON && $order->type === OrderTypeEnum::FBA;
        });
        if ($fulfillByAmazonProducts->count() > 1) {
            throw new RuntimeException('Please only add one product with fulfill by Amazon', 400);
        }

        foreach ($this->data as $orderProductData) {
            $errorLog = '';

            $productTemplate = Template::query()
                ->whereKey($orderProductData->template_id)
                ->where('status', ProductStatus::ACTIVE)
                ->first();

            if (!$productTemplate) {
                $errors[] = 'Product ID ' . $orderProductData->template_id . ' not found';
                continue;
            }

            if (
                $this->order->isFBA()
                && $productTemplate->isNotFulfillFBA()
            ) {
                $errors[] = 'Product ' . $productTemplate->name . 'is invalid when create order platform';
                continue;
            }

            $location = getLocationByCode($this->order->country);
            if (!$location) {
                $errors[] = 'Country not valid';
                continue;
            }

            $isExcludeShipping = FulfillMapping::checkExcludeShipping(
                $location,
                $productTemplate->id,
            );
            if ($isExcludeShipping) {
                $errors[] = 'Product ' . $productTemplate->name . ' not shipping to ' . $location->name;
                continue;
            }
            $isSenPrintsDesign = data_get($orderProductData, 'design_by_sen', false);
            $validMockups = array_filter($orderProductData->mockups->toArray(), function ($mockup) {
                return !empty($mockup['file_url']);
            });
            if ($isSenPrintsDesign && count($validMockups) === 0) {
                $errors[] = 'Product ' . $productTemplate->name . ' require the mockups if need SenPrints clone the design';
            }

            if ($orderProductData->id) {
                $orderProduct = OrderProduct::query()
                    ->where('id', $orderProductData->id)
                    ->where('seller_id', $this->order->seller_id)
                    ->where('order_id', $this->order->id)
                    ->first();

                if (!$orderProduct) {
                    $errors[] = 'Order product not found';
                    continue;
                }
            } else {
                $orderProduct = new OrderProduct();
                $orderProduct->fill([
                    'seller_id'    => $this->order->seller_id,
                    'order_id'     => $this->order->id,
                    'sku'          => $productTemplate->sku,
                    'template_id'  => $productTemplate->id,
                    'ref_id'       => $this->order->ref_id,
                    'product_name' => $productTemplate->name,
                ]);
            }

            $orderProduct->setRelation('template', $productTemplate);

            $printSpaceNames = $orderProduct->getPrintSpacesName();
            $optionSize = $orderProduct->getOptionSize();
            $defaultPrintSpace = $orderProduct->getDefaultPrintSpace();

            if (!$isSenPrintsDesign && $productTemplate->isNotHandMade()) {
                $hasDesigns = $orderProductData->designs->toCollection()->filter(function (SaveOrderProductFileData $design) {
                    return $design->file_url;
                })->count();
                if (!$hasDesigns) {
                    $this->order->fulfill_status = OrderFulfillStatus::INVALID;
                    $this->order->fulfill_log .= 'No artworks found';
                }

                foreach ($orderProductData->designs as $design) {
                    $printSpace = $design->print_space;
                    if ($printSpace === PrintSpaceEnum::DEFAULT) {
                        $printSpace = $defaultPrintSpace;
                    }

                    if (!in_array($printSpace, $printSpaceNames, true)) {
                        $errors[] = 'Artwork print space ' . $printSpace . ' invalid';
                        continue;
                    }

                    if (
                        $productTemplate->isFullPrintedType()
                        && $optionSize
                        && $printSpace !== PrintSpaceEnum::DEFAULT
                        && !str_contains($optionSize, $printSpace)
                    ) {
                        $errors[] = 'Artwork print space ' . $printSpace . " doesn't match product size " . $optionSize;
                    }
                }

                foreach ($orderProductData->mockups as $mockup) {
                    $printSpace = $mockup->print_space;
                    if ($printSpace === PrintSpaceEnum::DEFAULT) {
                        $printSpace = $defaultPrintSpace;
                    }

                    if (!in_array($printSpace, $printSpaceNames, true)) {
                        $errors[] = 'Mockup print space ' . $printSpace . ' invalid';
                        continue;
                    }

                    if (
                        $productTemplate->isFullPrintedType()
                        && $optionSize
                        && $printSpace !== PrintSpaceEnum::DEFAULT
                        && !str_contains($optionSize, $printSpace)
                    ) {
                        $errors[] = 'Mockup print space ' . $printSpace . " doesn't match product size " . $optionSize;
                    }
                }
            }

            if (!$isSenPrintsDesign && $productTemplate->isEmbroidery()) {
                $hasMockups = $orderProductData->mockups->toCollection()->filter(function (SaveOrderProductFileData $mockup) {
                    return $mockup->file_url;
                })->count();
                if (!$hasMockups) {
                    $this->order->fulfill_status = OrderFulfillStatus::INVALID;
                    $this->order->fulfill_log .= 'No mockups found';
                }

                foreach ($orderProductData->designs as $design) {
                    $hasMockup = $orderProductData->mockups->toCollection()->first(function (
                        SaveOrderProductFileData $mockup
                    ) use ($design) {
                        return $mockup->print_space === $design->print_space;
                    });

                    if (!$hasMockup) {
                        $this->order->fulfill_status = OrderFulfillStatus::INVALID;
                        $this->order->fulfill_log .= 'No mockups found for ' . $design->print_space;
                    }
                }
            }

            $productOptions = OrderService::correctOptions(
                orderOptions: $orderProductData->options,
                product: $productTemplate,
                logs: $errorLog,
            );
            if ($productOptions === false) {
                $errors[] = $errorLog;
                continue;
            }

            $productCustomOptions = $orderProductData->custom_options ?? [];
            if ($productTemplate->isHandMadeType() || $productTemplate->personalized === PersonalizedType::CUSTOM_OPTION) {
                $productCustomOptions = OrderService::correctCustomOptions(
                    orderCustomOptions: $productCustomOptions,
                    product: $productTemplate,
                    logs: $errorLog,
                );
                if ($productCustomOptions === false) {
                    $errors[] = $errorLog;
                    continue;
                }
            }

            $productCustomOptions = ImportCSVMultiPlatform::handleCustomOptions(
                order: $this->order,
                customOptions: $productCustomOptions,
            );

            $fulfillFbaBy = $orderProductData->fulfill_fba_by ?? FbaFulfillBy::BY_POSHMARK;
            if ($orderProductData->barcode && $this->order->isFBA()) {
                $fulfillFbaBy = FbaFulfillBy::BY_AMAZON;
            }
            $designBy = $orderProductData->mockups->toCollection()->isNotEmpty() && $isSenPrintsDesign ? DesignByEnum::SENPRINTS : DesignByEnum::SELLER;
            $updateData = [
                'campaign_title' => $orderProductData->campaign_title,
                'options'        => json_encode($productOptions, JSON_THROW_ON_ERROR),
                'quantity'       => $orderProductData->quantity,
                'full_printed'   => $productTemplate->full_printed,
                'fulfill_fba_by' => $fulfillFbaBy,
                'custom_options' => $productCustomOptions ? json_encode($productCustomOptions, JSON_THROW_ON_ERROR) : null,
                'campaign_type' => ProductSystemTypeEnum::FULFILL,
                'design_by' => $designBy,
            ];

            $orderProduct->fill($updateData);
            $orderProduct->save();

            event(new OrderProductProcessingMockupsEvent($orderProduct->id, $orderProductData->mockups));
            event(new OrderProductProcessingDesignsEvent($orderProduct->id, $orderProductData->designs));
            event(new OrderProductUpdatedEvent($orderProduct->id));

            $orderProductIds[] = $orderProduct->id;
        }

        if ($errors) {
            throw new RuntimeException(implode(';', $errors), 400);
        }

        $this->order->products()->whereKeyNot($orderProductIds)->delete();
        $this->order->save();
    }

    public static function make(Order $order, DataCollection $data): self
    {
        return new static($order, $data);
    }
}
